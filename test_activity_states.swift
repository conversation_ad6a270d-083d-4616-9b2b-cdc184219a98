import Foundation
import SwiftUI
import Combine

// 模拟API响应
let jsonString = """
{
  "code": 0,
  "data": [
    {
      "state": 0,
      "title": "Low",
      "time": 1743141600000
    },
    {
      "state": 1,
      "title": "Moderate",
      "time": 1743164400000
    },
    {
      "state": 2,
      "title": "Vigorous",
      "time": 1743184800000
    },
    {
      "state": 0,
      "title": "Inactive",
      "time": 1743199200000
    }
  ],
  "msg": ""
}
"""

// 简化版的ActivityStateData模型
struct TestActivityStateData: Codable, Identifiable {
    var id: String { String(time) } // 使用时间戳作为唯一标识符
    
    let state: Int
    let title: String
    let time: Int64 // 毫秒时间戳
    
    // 从毫秒时间戳获取Date对象
    var date: Date {
        return Date(timeIntervalSince1970: Double(time) / 1000.0)
    }
    
    // 计算半小时索引
    var halfHourIndex: Int {
        let date = self.date
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        return hour * 2 + (minute >= 30 ? 1 : 0)
    }
    
    // 获取格式化的时间字符串
    func formattedTime() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

// 简化版的响应模型
struct TestActivityStateResponse: Codable {
    let code: Int
    let data: [TestActivityStateData]?
    let msg: String
}

print("开始测试活动状态API接入...")
print("===========================\n")

print("模拟API响应:")
print(jsonString)
print("\n")

// 解析JSON
do {
    let data = jsonString.data(using: .utf8)!
    let decoder = JSONDecoder()
    let response = try decoder.decode(TestActivityStateResponse.self, from: data)
    
    // 检查解析结果
    print("解析结果:")
    print("- code =", response.code)
    print("- 数据项数量 =", response.data?.count ?? 0)
    
    if let stateData = response.data {
        print("\n活动状态数据:")
        for (index, state) in stateData.enumerated() {
            print("[\(index+1)] 级别: \(state.title), 状态值: \(state.state), 时间戳: \(state.time)")
            print("    转换为日期时间: \(state.date)")
            print("    格式化时间: \(state.formattedTime())")
            print("    计算的半小时索引: \(state.halfHourIndex)")
            
            // 计算正确的时间范围
            let hour = state.halfHourIndex / 2
            let minute = (state.halfHourIndex % 2) * 30
            let timeRangeStart = String(format: "%02d:%02d", hour, minute)
            let nextHourIndex = state.halfHourIndex + 1
            let nextHour = nextHourIndex / 2
            let nextMinute = (nextHourIndex % 2) * 30
            let timeRangeEnd = String(format: "%02d:%02d", nextHour, nextMinute)
            
            print("    时间范围: \(timeRangeStart)-\(timeRangeEnd)")
        }
        
        // 转换为UI需要的格式
        let activityLevels = stateData.map { (level: $0.title, halfHourIndex: $0.halfHourIndex) }
        print("\n转换为UI格式后:")
        for (index, level) in activityLevels.enumerated() {
            print("[\(index+1)] 级别: \(level.level), 半小时索引: \(level.halfHourIndex)")
        }
        
        // 计算平均活动级别
        var levelCounts: [String: Int] = [:]
        for state in stateData {
            levelCounts[state.title, default: 0] += 1
        }
        if let (mostCommonLevel, count) = levelCounts.max(by: { $0.value < $1.value }) {
            print("\n平均活动级别: \(mostCommonLevel) (出现\(count)次)")
        }
    }
    
    print("\n✅ 测试通过: 成功解析活动状态数据")
} catch {
    print("❌ 测试失败: JSON解析错误:", error)
}

print("\n===========================")
print("测试完成") 