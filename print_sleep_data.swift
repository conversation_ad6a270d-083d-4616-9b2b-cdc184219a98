#!/usr/bin/env swift

import Foundation
import CoreData

// 定义数据模型URL
let modelURL = URL(fileURLWithPath: "WindRing/WindRingDataModel.momd")

// 创建持久化存储协调器
let persistentStoreCoordinator = NSPersistentStoreCoordinator(managedObjectModel: NSManagedObjectModel(contentsOf: modelURL)!)

// 添加持久化存储
let storeURL = URL(fileURLWithPath: "WindRing.sqlite")
try persistentStoreCoordinator.addPersistentStore(ofType: NSSQLiteStoreType, configurationName: nil, at: storeURL, options: nil)

// 创建上下文
let context = NSManagedObjectContext(concurrencyType: .mainQueueConcurrencyType)
context.persistentStoreCoordinator = persistentStoreCoordinator

// 获取所有Sleep实体
let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: "Sleep")
fetchRequest.returnsObjectsAsFaults = false

do {
    let results = try context.fetch(fetchRequest)
    print("获取到 \(results.count) 条睡眠数据:")
    
    for (index, case let sleep as NSManagedObject) in results.enumerated() {
        print("\n--- 睡眠记录 #\(index+1) ---")
        print("ID: \(sleep.value(forKey: "id") ?? "unknown")")
        print("用户ID: \(sleep.value(forKey: "userId") ?? "unknown")")
        
        if let startTime = sleep.value(forKey: "startTime") as? Date {
            print("开始时间: \(formatDate(startTime))")
        }
        
        if let endTime = sleep.value(forKey: "endTime") as? Date {
            print("结束时间: \(formatDate(endTime))")
        }
        
        print("总睡眠时间: \(sleep.value(forKey: "totalMinutes") ?? 0) 分钟")
        print("深睡眠时间: \(sleep.value(forKey: "deepMinutes") ?? 0) 分钟")
        print("浅睡眠时间: \(sleep.value(forKey: "lightMinutes") ?? 0) 分钟")
        print("REM睡眠时间: \(sleep.value(forKey: "remMinutes") ?? 0) 分钟")
        print("清醒时间: \(sleep.value(forKey: "awakeMinutes") ?? 0) 分钟")
        print("睡眠评分: \(sleep.value(forKey: "score") ?? 0)")
        print("睡眠效率: \(sleep.value(forKey: "efficiency") ?? 0)%")
    }
} catch {
    print("获取睡眠数据失败: \(error)")
}

// 格式化日期为可读字符串
func formatDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
    return formatter.string(from: date)
} 