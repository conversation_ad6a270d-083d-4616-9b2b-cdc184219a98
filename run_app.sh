#!/bin/bash

# 切换到项目目录
cd /Users/<USER>/Desktop/WindRing

# 获取可用的模拟器列表
SIMULATOR_ID=$(xcrun simctl list devices | grep "iPhone" | grep "Booted" | head -1 | sed -E 's/.*\(([A-Z0-9-]+)\).*/\1/')

if [ -z "$SIMULATOR_ID" ]; then
    echo "没有找到已启动的模拟器，尝试启动一个..."
    # 尝试启动iPhone 15 Pro模拟器（如果存在）
    SIMULATOR_ID=$(xcrun simctl list devices | grep "iPhone 15 Pro" | head -1 | sed -E 's/.*\(([A-Z0-9-]+)\).*/\1/')
    
    if [ -z "$SIMULATOR_ID" ]; then
        echo "未找到iPhone 15 Pro模拟器，尝试启动任何可用的iPhone模拟器..."
        SIMULATOR_ID=$(xcrun simctl list devices | grep "iPhone" | head -1 | sed -E 's/.*\(([A-Z0-9-]+)\).*/\1/')
    fi
    
    if [ -z "$SIMULATOR_ID" ]; then
        echo "未找到可用的iPhone模拟器。请在Xcode中创建一个模拟器。"
        exit 1
    fi
    
    echo "启动模拟器 $SIMULATOR_ID..."
    xcrun simctl boot $SIMULATOR_ID
    open -a Simulator
    sleep 5  # 给模拟器一些启动时间
fi

# 构建并运行应用
echo "正在构建并运行WindRing应用..."
echo "您可以在Xcode中查看更详细的构建过程和日志"
echo "或者直接在打开的模拟器中查看应用运行效果"

xcodebuild clean build -scheme WindRing -destination "platform=iOS Simulator,id=$SIMULATOR_ID" -configuration Debug 

# 尝试安装应用到模拟器
APP_PATH=$(find ~/Library/Developer/Xcode/DerivedData -name "WindRing.app" -print -quit)
if [ -n "$APP_PATH" ]; then
    echo "安装应用到模拟器..."
    xcrun simctl install $SIMULATOR_ID "$APP_PATH"
    xcrun simctl launch $SIMULATOR_ID "com.yourcompany.WindRing"
else
    echo "未找到构建好的应用。请在Xcode中直接运行项目。"
fi 