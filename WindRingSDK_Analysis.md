# WindRing SDK 分析文档

## 文档概述

本文档是对WindRing智能戒指SDK开发指南的分析总结，旨在提供一个清晰的SDK功能框架和接口说明，帮助开发者快速掌握和使用该SDK。

## 1. SDK整体架构

WindRing SDK的整体架构分为五个主要模块：

1. **数据模型** - 定义了与设备交互所需的各种数据结构
2. **代理方法** - 提供回调接口，用于接收设备数据和状态变化
3. **戒指扫描和连接** - 管理设备发现和连接过程
4. **戒指交互** - 提供控制设备和获取数据的方法
5. **版本更新日志** - 记录SDK的版本变更历史

## 2. 数据模型详解

SDK定义了大量以"CRP"开头的数据模型类，用于存储和传输各类数据。

### 2.0 初始设置

使用SDK前需要完成的工程配置：

- 导入框架包 `CRPSmartRing.framework` 到项目嵌入式二进制文件
- 配置必要权限：
  - 在Info.plist中添加CoreBluetooth下的Required background modes (蓝牙权限)
  - 添加App Transport Security Settings并设置Allow Arbitrary Loads为YES (网络访问)
  - 添加蓝牙权限描述 Bluetooth Always Usage Description

### 2.1 设备发现与连接相关

#### CRPDiscovery
设备发现相关数据模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| String | LocalName | 设备名称 |
| Int | RSSI | 信号强度 |
| String | Mac | MAC地址 |

#### CRPStepModel
步数数据模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | steps | 步数 |
| Int | distance | 距离 |
| Int | calory | 卡路里 |
| Int | time | 时间（当值大于-1时为运动统计时间，值为-1表示设备不支持运动时间统计） |

### 2.2 睡眠监测相关

#### CRPSleepModel
睡眠数据主模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | deep | 深度睡眠 |
| Int | light | 浅度睡眠 |
| [Dictionary<String,String>] | detail | 睡眠数据详情 |

睡眠数据详情包含：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| String | type | 状态: 0-清醒, 1-浅睡, 2-深睡, 3-快速眼动 |
| String | total | 睡眠长度(分钟) |
| String | start | 开始时间 |
| String | end | 结束时间 |

#### CRPSleepRecordModel
睡眠记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | day | 0: 今天; 1: 昨天; 2: 前天，以此类推 |
| Int | deep | 深度睡眠 |
| Int | light | 浅度睡眠 |
| Int | rem | 快速眼动睡眠(REM) |
| [Dictionary<String,String>] | detail | 睡眠数据详情 |

### 2.3 用户信息与训练数据

#### CRPUserInfoModel
用户信息模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | height | 身高 |
| Int | weight | 体重 |
| Int | age | 年龄 |
| GenderOption | gender | 性别 |
| Int | stepLength | 步长 |

#### CRPNewTrainingModel
新训练模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | id | 序列号 |
| Int | starttime | 开始时间(时间戳) |
| Int | endtime | 结束时间(时间戳) |
| Int | availdTime | 有效时间(秒) |
| Int | hrAvg | 平均心率 |
| CRPTrainingType | type | 运动类型 |
| Int | Step | 步数 |
| Int | Distance | 距离 |
| Int | kcal | 卡路里 |
| [Int] | heartRate | 心率数据 |
| CRPTrainingGoalType | goalType | 运动目标类型 |
| Int | goal | 目标值 |
| Int | hrAddress | 心率地址 |
| Int | hrLength | 心率长度 |

#### CRPSportRecord
运动记录，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | id | 序列号 |
| Int | startTime | 开始时间(时间戳) |
| Int | type | 类型 |

#### CRPTrainingGoalsModel
训练目标模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | steps | 步数目标 |
| Int | distance | 里程目标(米) |
| Int | kcal | 卡路里目标 |
| Int | exerciseTime | 运动时间目标(分钟) |

#### CRPTrainingGoalStateModel
训练目标状态模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Bool | open | 开关状态 |
| [Int] | weekday | 运动日数组(参考CRPWeekday)，例如：[CRPWeekDay.sun.rawValue, CRPWeekDay.thu.rawValue, CRPWeekDay.mon.rawValue] |

#### CRPTrainingRecordModel
训练记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | day | 0: 今天; 1: 昨天; 2: 前天，以此类推 |
| Int | step | 步数 |
| Int | kcal | 卡路里(小卡) |
| Int | distance | 距离(厘米) |
| Int | exerciseTime | 运动时间(分钟) |

### 2.4 生理指标相关

#### CRPHeartRecordModel
心率记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | value | 测量结果 |
| Int | time | 时间戳 |

#### CRPO2RecordModel
血氧记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | value | 测量结果 |
| Int | time | 时间戳 |

#### CRPHRVRecordModel
心率变异性记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | hrv | 心率变异性测量值 |
| Int | time | 时间戳 |

#### CRPTimingHRRecordModel
定时心率记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | day | 0: 今天; 1: 昨天; 2: 前天，以此类推 |
| [Int] | hearts | 心率数据(288个，每5分钟一个) |

#### CRPTimingHRVRecordModel
定时心率变异性记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | day | 0: 今天; 1: 昨天; 2: 前天，以此类推 |
| [Int] | hrvs | 心率变异性数据(288个，每5分钟一个) |

#### CRPTimingO2RecordModel
定时血氧记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | day | 0: 今天; 1: 昨天; 2: 前天，以此类推 |
| [Int] | o2s | 血氧数据(288个，每5分钟一个) |

#### CRPTimingStepsRecordModel
定时步数记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | day | 0: 今天; 1: 昨天; 2: 前天，以此类推 |
| [Int] | steps | 半小时步数统计数据(48个，每半小时一个) |

### 2.5 活动监测相关

#### CRPContinueActivitysRecordModel
持续活动记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | day | 0: 今天; 1: 昨天; 2: 前天，以此类推 |
| [Int] | activities | 活动统计数据(1440个，每分钟一个) |

#### CRPActivityReminderModel
活动提醒模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Bool | open | 开关 |
| Int | period | 久坐提醒周期(分钟) |
| Int | steps | 最大步数 |
| Int | startHour | 开始时间(24小时制) |
| Int | endHour | 结束时间(24小时制) |

#### CRPHR Remind
心率提醒，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Bool | isRemind | 开关(提醒与否) |
| Int | max | 心率提醒阈值 |

### 2.6 其他健康数据

#### CRPGsensorInfoModel
传感器信息模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | x | x轴偏移量 |
| Int | y | y轴偏移量 |
| Int | z | z轴偏移量 |

#### CRPBatteryInfoModel
电池信息模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | value | 电量值 |
| Int | voltage | 电压(毫伏) |

#### CRPActivityReminderStateModel
活动提醒状态模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Bool | wearState | 佩戴状态(00: 未佩戴, 01: 已佩戴) |
| Int | time | 时间(分钟) |
| Int | steps | 周期步数 |

#### CRPSleepDetailModel
睡眠详情模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| CRPSleepType | type | 睡眠状态 |
| Int | hour | 小时 |
| Int | minute | 分钟 |

#### CRPSleepTemperatureDataModel
睡眠温度数据模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | day | 0: 今天; 1: 昨天; 2: 前天，以此类推 |
| [Int] | temperatures | 体温数据(288个，每5分钟一个) |

#### CRPRingInfoModel
戒指信息模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | color | 颜色 |
| Int | size | 尺寸 |
| Int | type | 类型 |

#### CRPPressRecordModel
压力记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | stress | 压力值 |
| Int | time | 时间戳 |

#### CRPTouchModel
触摸模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Bool | open | 开关状态 |
| CRPTouchType | type | 触摸类型 |

#### CRPMeditationModel
冥想模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | id | 序列号 |
| Int | starttime | 开始时间(时间戳) |
| Int | endtime | 结束时间(时间戳) |
| Int | validTime | 有效时间(秒) |
| Int | hrAvg | 平均心率 |
| Int | hrAddress | 心率地址 |
| Int | hrLength | 心率长度 |
| [Int] | heartRate | 心率数据 |

### 2.7 高级睡眠分析

#### CRPGoMoreSleepRecord
GoMore睡眠记录，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | id | 序列号 |
| Int | startTime | 开始时间(时间戳) |

#### CRPGoMoreSleepDataModel
GoMore睡眠数据模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | type | 类型(1: 长睡眠; 2: 短睡眠) |
| Int | startTime | 开始时间(时间戳) |
| Int | endTime | 结束时间(时间戳) |
| Float | totalTime | 总睡眠时间(分钟) |
| Float | sleepEfficiency | 睡眠效率 |
| Float | sleepScore | 睡眠评分 |

#### CRPGoMoreSleepRecordModel
GoMore睡眠记录模型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | id | 序列号 |
| Int | startTime | 开始时间(时间戳) |
| Int | endTime | 结束时间(时间戳) |
| Int | validTime | 有效时间(秒) |
| Int | hrAvg | 平均心率 |
| Int | hrAddress | 心率地址 |
| Int | hrLength | 心率长度 |
| [Int] | heartRate | 心率数据 |

#### CRPGoMoreSleepType
GoMore睡眠类型，包含以下字段：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | id | 序列号 |
| Int | deep | 深度睡眠 |
| Int | light | 浅度睡眠 |
| Int | rem | 快速眼动睡眠 |
| [Dictionary<String,String>] | detail | 睡眠数据详情 |

睡眠状态说明：
| 数据类型 | 属性名 | 说明 |
|---------|-------|------|
| Int | state | 0: 长睡眠数据收集进行中<br>1: 已收集一天长睡眠数据，有基本时间类型指标<br>2: 在一天内收集的长期睡眠数据，但有睡眠片段，有基本时间类型指标<br>3: 连续7天收集4天或更多天数的长睡眠数据，具有较高置信度<br>4: 连续7天收集长睡眠数据，具有较高置信度<br>5: 连续7天收集长睡眠数据，但置信度较低<br>71: 睡眠时间超过15小时<br>72: 连续7天内未收集长期睡眠数据 |
| Int | type | 1: 蜂鸟型; 2: 猫头鹰型; 3: 云雀型 |
| Int | reliability | 过去七天的睡眠时间型置信度: 0-低; 1-高 |
| Int | bedtime | 就寝时间(小时) |

## 3. 代理方法

代理方法提供回调机制，使应用能接收来自设备的数据和状态变化。以下是各个代理方法的详细说明：

### 3.1 连接与状态

#### 3.1.1 返回当前连接状态
```swift
didState(_ state: CRPState)
```
当设备连接状态发生变化时，此方法会被调用。

#### 3.1.2 返回蓝牙状态
```swift
didBluetoothState(_ state: CRPBluetoothState)
```
返回当前蓝牙的工作状态。

#### 3.1.3 接收固件升级进度和状态
```swift
receiveOTA(_ state: CRPOTAState, _ progress: Int)
```
在固件升级过程中，此方法会被调用以报告升级进度和状态。

#### 3.1.4 接收配对状态
```swift
receivePairState(_ state: CRPPairState)
```
当设备配对状态发生变化时，此方法会被调用。

### 3.2 健康数据接收

#### 3.2.1 接收实时运动步数数据
```swift
receiveSteps(_ model: StepModel)
```
使用getSteps或ring触发新步数生成时，此方法会被调用。

#### 3.2.2 接收心率测量(单次)
```swift
receiveHeartRate(_ heartRate: Int)
```
接收单次心率测量结果。

#### 3.2.3 接收实时心率数据
```swift
receiveRealTimeHeartRate(_ heartRate: Int)
```
接收实时心率数据，一般仅用于显示而非存储。

#### 3.2.4 接收心率变异性测量结果
```swift
receiveHRV(_ hrv: Int)
```
接收心率变异性(HRV)测量结果。

#### 3.2.5 接收血氧测量结果
```swift
receiveSpO2(_ o2: Int)
```
接收血氧饱和度(SpO2)测量结果。

#### 3.2.6 接收压力测量结果
```swift
receiveStress(_ stress: Int)
```
接收压力水平测量结果。

#### 3.2.7 接收触摸开关和类型
```swift
receiveTouchType(_ model: CRPTouchModel)
```
接收触摸操作的开关状态和类型。

### 3.3 活动与训练数据

#### 3.3.1 接收运动数据记录列表
```swift
receiveTrainingList(_ list: [CRPSportRecord])
```
此方法在使用getTrainingRecordList或戒指结束运动时被调用，返回运动记录列表。

#### 3.3.2 接收运动模式状态
```swift
receiveTrainingState(_ state: CRPTrainingType)
```
接收当前运动模式的状态。

#### 3.3.3 接收佩戴状态
```swift
receiveWearState(_ state: Int)
```
接收设备的佩戴状态(00: 未佩戴, 01: 已佩戴)。

#### 3.3.4 接收每日目标提醒
```swift
receiveDailyGoal(_ type: CRPTrainingGoalType, state: Int)
```
接收每日目标提醒状态(0: 不提醒, 1: 提醒)。收到目标提醒后，需要使用setReceiveGoalReminder方法通知Ring App用户已被提醒。

#### 3.3.5 接收久坐提醒
```swift
receiveActivityReminder(isReminder: Int, wearState: Int, time: Int, step: Int)
```
接收久坐提醒信息。isReminder表示App是否需要被提醒(00: 不需要, 01: 需要)。wearState、time和step三个数据仅当调用setActivityReminderReach命令时才会返回。

#### 3.3.6 接收锻炼中的目标完成情况
```swift
receiveTrainingGoal(_ type: CRPTrainingGoalType, state: Int)
```
接收锻炼目标完成提醒(0: 不提醒, 1: 提醒)。收到锻炼目标提醒后，需要使用setReceiveTrainingGoalReach方法通知Ring App用户已被提醒。

### 3.4 其他数据回调

#### 3.4.1 SOS触发回调
```swift
receiveSOS()
```
当SOS功能被触发时调用，用于记录SOS触发记录。

#### 3.4.2 接收冥想数据记录列表
```swift
receiveMeditationList(_ list: [CRPMeditationRecord])
```
此方法在使用getMeditationRecordList或戒指结束冥想时被调用，返回冥想记录列表。

#### 3.4.3 接收冥想状态
```swift
receiveMeditationState(_ state: Int)
```
接收当前冥想状态。

#### 3.4.4 接收睡眠数据列表
```swift
receiveSleepList(_ list: [CRPGoMoreSleepRecord])
```
使用getGoMoreSleepDataList方法调用或当戒指睡眠状态变化时，此方法会被调用以返回睡眠数据列表。

#### 3.4.5 接收单次血压测量结果
```swift
receiveBloodPressure(_ sbp: Int, _ dbp: Int)
```
接收单次血压测量结果，其中sbp为收缩压，dbp为舒张压。

#### 3.4.6 接收单次温度测量结果
```swift
receiveTemperature(_ value: Double)
```
接收单次体温测量结果，单位为摄氏度。

#### 3.4.7 接收照片数据
```swift
receviePhoto()
```
当使用拍照功能时，此方法会被调用。

#### 3.4.8 接收Knock开关状态
```swift
receiveKnockSwitch(_ model: CRPTouchModel)
```
接收Knock功能的开关状态和触摸类型。

## 4. 戒指扫描和连接

SDK提供了完整的设备发现和连接管理功能，以下是各个步骤的详细说明：

### 4.1 初始化

CRPSmartRingSDK采用单例模式设计，通过设置代理可以初始化SDK并创建蓝牙环境。初始化是使用SDK的第一步，必须在任何其他操作之前完成。

```swift
CRPSmartRingSDK.sharedInstance.delegate = self
```

注意：蓝牙初始化是一个耗时操作，需要预先设置。

### 4.2 戒指扫描

只有在权限允许且蓝牙已开启的情况下才能开始正常扫描。如果在扫描过程中发现戒指，可以通过scan()方法的progressHandler回调获取发现的设备，而completionHandler会在整个扫描完成后调用，返回扫描到的所有戒指。

由于蓝牙扫描是一个耗时操作，建议的扫描时间为10秒。

```swift
scan(_ duration: TimeInterval = 10, progressHandler: scanProgressHandler?, completionHandler: scanCompletionHandler?)
```

参数说明：
- duration: 扫描持续时间，默认为10秒
- progressHandler: 进度回调，每发现一个设备就会调用一次
- completionHandler: 完成回调，扫描结束后调用，返回所有发现的设备

### 4.3 取消扫描

可以随时中断正在进行的戒指扫描过程。

```swift
interruptScan()
```

### 4.4 连接

通过扫描获得CRPDiscovery对象后，可以根据localName和Mac地址选择对应的戒指进行连接。

```swift
connect(discovery: CRPDiscovery)
```

如需获取系统中已连接的设备（原始CBPeripheral），可以使用connectedPeripheral()方法。如果需要连接此设备，则需要使用CBPeripheral创建CRPDiscovery，然后调用连接方法。

```swift
connectedPeripheral() -> [CBPeripheral]
```

### 4.5 解绑

解除与戒指的绑定关系。

```swift
remove(_ handler: @escaping removeHandler)
```

如需重新连接：

```swift
reconnect()
```

## 5. 戒指交互

SDK提供了丰富的设备控制和数据获取功能，以下是各类交互方法的详细说明：

### 5.1 时间同步

保持戒指与App端时间的一致性，这对于数据的准确性非常重要。

```swift
setTime()
```

### 5.2 固件升级

#### 5.2.1 查询固件版本

获取当前戒指的固件版本。

```swift
getSoftVer(_ handler: @escaping stringHandler)
```

#### 5.2.2 获取戒指MAC地址

查询当前连接戒指的MAC地址。

```swift
getMac(_ handler: @escaping stringHandler)
```

#### 5.2.3 检查新固件

通过传入当前版本和MAC地址，检查是否有可用的新固件版本。

```swift
checkLatest(_ version: String, _ mac: String, handler: @escaping versionHandler)
```

#### 5.2.4 固件升级

SDK提供了多种固件升级方法，适用于不同类型的设备：

- Goodix固件升级：
```swift
startGoodixUpgradeFromFile(zipPath: String)
```

- 中科蓝讯固件升级：
```swift
startUpgradeFromFile(path: String)
```

- 汇顶5331固件升级：
```swift
startBootLoaderUpgrade(zipPath: String)
```

#### 5.2.5 中断升级

可以随时中断正在进行的固件升级过程。

```swift
stopUpgrade()
```

### 5.3 电量查询

查询戒指当前的电池电量。如果电池电量高于100，表示处于充电状态，此时实际电量为最后两位数字。例如，如果电量显示为159，表示设备正在充电，当前电量为59%。

```swift
getBattery(_ handler: @escaping intHandler)
```

### 5.4 用户信息管理

#### 5.4.1 获取用户信息

获取存储在戒指上的用户个人信息。

```swift
getUserInfo(_ handler: @escaping profileHandler)
```

#### 5.4.2 设置用户信息

将用户个人信息保存到戒指中。

```swift
setUserInfo(_ userInfo: CRPUserInfoModel)
```

### 5.5 活动步数

活动步数相关功能提供了获取和分析用户运动数据的能力。

#### 5.5.1 同步当前活动步数

同步戒指当前的活动步数数据，同步结果将通过代理方法`receiveSteps`返回。

```swift
getSteps()
```

#### 5.5.2 获取历史运动统计数据

获取特定日期的运动统计数据，包括步数、距离、卡路里和运动时间。

```swift
getTrainingData(_ day: Int, _ handler: @escaping exerciseRecordHandler)
```

参数说明：
- day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
- handler: 回调函数，返回运动记录数据

#### 5.5.3 获取半小时步数统计数据

获取指定日期的半小时步数统计数据，每天包含48个值，每半小时一个。

```swift
getStepArchiveData(_ day: Int, _ handler: @escaping fullDayStepsRecordHandler)
```

参数说明：
- day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
- handler: 回调函数，返回全天步数记录数据

#### 5.5.4 获取每分钟步数统计数据

获取每分钟的步数数据，一天共1440个值。

```swift
getStepsPerMin(_ handler: @escaping intHandler)
```

### 5.6 睡眠监测

睡眠监测相关功能用于获取和分析用户的睡眠数据。

#### 5.6.1 获取睡眠数据

获取指定日期的睡眠数据，包括睡眠时长、深睡眠、浅睡眠等信息。

```swift
getSleepData(_ day: Int, _ handler: @escaping sleepRecordHandler)
```

参数说明：
- day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
- handler: 回调函数，返回睡眠记录数据

### 5.7 运动目标

运动目标功能允许设置和管理日常运动目标与锻炼日目标。

#### 5.7.1 获取日常运动目标

获取当前设置的日常运动目标，如步数、里程、卡路里和运动时间目标。

```swift
getNormalTrainingGoal(_ handler: @escaping normalExerciseGoalDataHandler)
```

#### 5.7.2 设置日常运动目标

设置日常运动目标，包括步数、里程、卡路里和运动时间目标。

```swift
setNormalTrainingGoal(_ goals: CRPTrainingGoalsModel)
```

#### 5.7.3 获取锻炼日目标

获取特定锻炼日的运动目标设置。

```swift
getTrainingDayGoal(_ handler: @escaping exerciseGoalDataHandler)
```

#### 5.7.4 设置锻炼日目标

设置特定锻炼日的运动目标和相关配置。

```swift
setTrainingDayGoal(_ model: CRPTrainingGoalStateModel, _ goals: CRPTrainingGoalsModel)
```

参数说明：
- model: 锻炼目标状态模型，包含开关状态和训练日设置
- goals: 锻炼目标模型，包含具体的目标值

### 5.8 久坐提醒

久坐提醒功能用于管理久坐行为的监测和提醒。

#### 5.8.1 获取久坐提醒设置

获取当前的久坐提醒设置，包括开关状态、提醒周期、步数阈值和时间范围。

```swift
getActivityReminderInfo(_ handler: @escaping sitRemindHandler)
```

#### 5.8.2 设置久坐提醒

配置久坐提醒功能，设置开关状态、提醒周期、步数阈值和时间范围。

```swift
setActivityReminder(_ activityReminder: CRPActivityReminderModel)
```

### 5.9 心率警告

心率警告功能用于设置和获取心率超过阈值时的提醒功能。

#### 5.9.1 获取心率警告设置

获取当前的心率警告设置，包括开关状态和心率阈值。

```swift
getHeartRateRemind(_ handler: @escaping hrRemindHandler)
```

#### 5.9.2 设置心率警告

配置心率警告功能，设置开关状态和心率阈值。

```swift
setHeartRateRemind(_ remind: CRPHRRemind)
```

### 5.10 心率监测

心率监测功能提供了多种心率数据的获取和管理方法，包括单次测量和持续监测。

#### 5.10.1 开始单次心率测量

启动单次心率测量，测量结果将通过代理方法`receiveHeartRate`返回。

```swift
setStartSingleHR()
```

#### 5.10.2 结束单次心率测量

结束心率测量过程。如果返回结果为255或0，表示心率测量被中断。

```swift
setStopSingleHR()
```

#### 5.10.3 获取心率测量历史

获取历史心率测量记录。

```swift
getHeartRecordData(_ handler: @escaping heartRecordDataHandler)
```

#### 5.10.4 获取定时心率测量状态

查询当前定时心率测量功能的状态和设置。

```swift
getTimingHeartRateInterval(_ handler: @escaping intHandler)
```

#### 5.10.5 设置定时心率测量

配置定时心率测量功能。戒指支持24小时定时心率测量，从0:00开始，测量间隔可设置。

```swift
setTimingHeartRate(_ interval: Int)
```

参数说明：
- interval: 测量间隔(0:关闭，正数:开启，间隔为 interval * 5 分钟)

#### 5.10.6 获取定时心率数据

获取指定日期的定时心率数据，每天包含288个值，每5分钟一个。

```swift
getTimingHeartRate(_ day: Int, _ handler: @escaping fullDayHRRecordHandler)
```

参数说明：
- day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
- handler: 回调函数，返回全天心率记录数据

### 5.11 血氧监测

血氧监测功能提供了血氧饱和度测量和数据获取的方法。

#### 5.11.1 开始单次血氧测量

启动单次血氧测量，测量结果将通过代理方法`receiveSpO2`返回。

```swift
setStartSpO2()
```

#### 5.11.2 结束单次血氧测量

结束血氧测量过程。如果测量时间过短，可能不会有测量结果。如果返回结果为255或0，表示血氧测量被中断。

```swift
setStopSpO2()
```

#### 5.11.3 获取血氧测量历史

获取历史血氧测量记录。

```swift
getO2RecordData(_ handler: @escaping o2RecordDataHandler)
```

#### 5.11.4 获取定时血氧测量状态

查询当前定时血氧测量功能的状态和设置。

```swift
getTimingO2Interval(_ handler: @escaping intHandler)
```

#### 5.11.5 检查睡眠血氧测量功能支持状态

查询设备是否支持睡眠期间的血氧测量功能。返回1表示支持，返回0或无返回表示不支持。

```swift
getSleepO2SupportState(_ handler: @escaping intHandler)
```

#### 5.11.6 设置定时血氧测量

配置定时血氧测量功能。戒指支持24小时定时血氧测量，从0:00开始，测量间隔可设置。

```swift
setTimingO2(_ interval: Int)
```

参数说明：
- interval: 测量间隔(0:关闭，正数:开启，间隔为 interval * 5 分钟)

#### 5.11.7 获取定时血氧数据

获取指定日期的定时血氧数据，每天包含288个值，每5分钟一个。

```swift
getTimingO2(_ day: Int, _ handler: @escaping fullDayO2RecordHandler)
```

参数说明：
- day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
- handler: 回调函数，返回全天血氧记录数据

### 5.12 HRV疲劳度监测

HRV(心率变异性)疲劳度监测功能提供了评估用户身体疲劳程度的方法。

#### 5.12.1 开始单次HRV测量

启动单次HRV测量，测量结果将通过代理方法`receiveHRV`返回。

```swift
setStartHRV()
```

#### 5.12.2 结束单次HRV测量

结束HRV测量过程。如果测量时间过短，可能不会有测量结果。如果返回结果为255或0，表示HRV测量被中断。

```swift
setStopHRV()
```

#### 5.12.3 获取HRV测量历史

获取历史HRV测量记录。

```swift
getHRVRecord(_ handler: @escaping hrvRecordDataHandler)
```

#### 5.12.4 获取定时HRV测量状态

查询当前定时HRV测量功能的状态和设置。

```swift
getTimingHRVInterval(_ handler: @escaping intHandler)
```

#### 5.12.5 设置定时HRV测量

配置定时HRV测量功能。戒指支持24小时定时HRV测量，从0:00开始，测量间隔可设置。

```swift
setTimingHRV(_ interval: Int)
```

参数说明：
- interval: 测量间隔(0:关闭，正数:开启，间隔为 interval * 5 分钟)

#### 5.12.6 获取定时HRV数据

获取指定日期的定时HRV数据，每天包含288个值，每5分钟一个。

```swift
getTimingHRV(_ day: Int, _ handler: @escaping fullDayHRVRecordHandler)
```

参数说明：
- day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
- handler: 回调函数，返回全天HRV记录数据

### 5.13 运动数据管理

运动数据管理功能提供了多种方法来管理和分析用户的运动记录。

#### 5.13.1 获取支持的运动类型

获取戒指支持的运动类型列表。

```swift
getTrainingSupportList(_ handler: @escaping exerciseSupportListHandler)
```

#### 5.13.2 设置运动状态

设置当前运动状态，包括运动类型、目标类型和目标值。

```swift
setTraining(state: CRPTrainingType, type: CRPTrainingGoalType, goal: Int)
```

参数说明：
- state: 运动类型
- type: 运动目标类型
- goal: 目标值

#### 5.13.3 获取当前运动状态

获取当前运动状态，包括运动类型和是否正在进行中。

```swift
getTrainingState(_ handler: @escaping trainingStateHandler)
```

#### 5.13.4 获取历史运动记录列表

获取历史运动记录列表，结果通过代理方法`receiveTrainingList`返回。

```swift
getTrainingRecordList()
```

#### 5.13.5 获取详细运动数据

获取特定运动记录的详细数据，使用`receiveTrainingList`返回的id进行查询。

```swift
getTrainingRecordData(id: Int, _ handler: @escaping sportDetailHandler)
```

参数说明：
- id: 运动记录ID
- handler: 回调函数，返回运动详细数据

### 5.14 活动量监测

活动量监测功能提供了分析用户每分钟活动状态的方法。

#### 5.14.1 获取活动量数据

获取指定日期的每分钟活动量数据，一天共1440个值。

```swift
getActivityArchiveData(_ day: Int, _ handler: @escaping fullDayActivityRecordHandler)
```

参数说明：
- day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
- handler: 回调函数，返回全天活动量记录数据

### 5.15 佩戴状态检测

佩戴状态检测功能用于获取戒指当前的佩戴状态。

#### 5.15.1 获取佩戴状态

获取当前戒指的佩戴状态，结果通过代理方法`receiveWearState`返回。

```swift
getWearState()
```

### 5.16 设备控制

设备控制功能提供了管理戒指基本操作的方法。

#### 5.16.1 关机

使戒指关机。

```swift
shutdown()
```

#### 5.16.2 恢复出厂设置

将戒指恢复到出厂状态，清除所有用户数据和配置。

```swift
reset()
```

#### 5.16.3 重启设备

重启戒指设备。

```swift
reboot()
```

### 5.17 测试命令

测试命令功能提供了一系列用于测试和诊断的方法。

#### 5.17.1 获取设备GitHash信息

获取设备固件的Git哈希值，用于版本验证。

```swift
getGitHashInfo(handler: @escaping stringHandler)
```

#### 5.17.2 获取设备传感器数据

获取设备的G-sensor数据，包含x、y、z三轴信息。

```swift
getGSensorInfo(handler: @escaping gsensorHandler)
```

#### 5.17.3 获取电池详细信息

获取戒指电池的详细信息，包括电量和电压。

```swift
getBatteryInfo(handler: @escaping batteryInfoHandler)
```

### 5.18 体温监测

体温监测功能提供了多种获取用户体温数据的方法。

#### 5.18.1 设置睡眠体温监测状态

设置睡眠期间的体温监测功能的开关状态。

```swift
setSleepTemperatureState(open: Bool)
```

参数说明：
- open: 开关状态(true:开启，false:关闭)

#### 5.18.2 获取睡眠体温监测状态

获取当前睡眠期间体温监测功能的开关状态。

```swift
getSleepTemperatureState(handler: @escaping boolHandler)
```

#### 5.18.3 获取睡眠体温数据

获取指定日期的睡眠体温数据，包含整晚的体温变化记录。

```swift
getSleepTemperatureData(day: Int, handler: @escaping sleepTemperatureDataHandler)
```

参数说明：
- day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
- handler: 回调函数，返回睡眠体温数据

#### 5.18.4 开始单次体温测量

启动单次体温测量，测量结果将通过代理方法`receiveTemperature`返回。

```swift
setStartTemperature()
```

#### 5.18.5 结束单次体温测量

结束体温测量过程。如果返回结果为0，表示体温测量被中断。

```swift
setStopTemperature()
```

### 5.19 测量状态管理

测量状态管理功能用于获取各种测量功能的当前状态。

#### 5.19.1 获取当前体温测量状态

获取当前体温测量功能的状态，以了解是否有测量在进行中。

```swift
getMeasurementState(handler: @escaping measurementStateHandler)
```

### 5.20 压力监测

压力监测功能提供了评估用户心理压力水平的方法。

#### 5.20.1 开始单次压力测量

启动单次压力测量，测量结果将通过代理方法`receiveStress`返回。

```swift
setStartStress()
```

#### 5.20.2 结束单次压力测量

结束压力测量过程。如果测量时间过短，可能不会有测量结果。如果返回结果为255或0，表示压力测量被中断。

```swift
setStopStress()
```

#### 5.20.3 获取压力测量历史

获取历史压力测量记录。

```swift
getStressRecord(_ handler: @escaping stressRecordDataHandler)
```

### 5.21 戒指配置

戒指配置功能提供了获取和设置戒指信息的方法。

#### 5.21.1 获取戒指信息配置

获取戒指的配置信息，包括颜色、尺寸和类型。

```swift
getRingInfo(handler: @escaping ringInfoHandler)
```

#### 5.21.2 设置戒指信息配置

设置戒指的配置信息。

```swift
setRingInfo(info: CRPRingInfoModel)
```

### 5.22 触摸操作

触摸操作功能提供了管理戒指触摸交互的方法。

#### 5.22.1 获取触摸开关和类型

获取当前触摸功能的开关状态和触摸类型，结果通过代理方法`receiveTouchType`返回。

```swift
getTouchType()
```

#### 5.22.2 设置触摸开关

设置触摸功能的开关状态。

```swift
setTouchSwitch(open: Bool)
```

参数说明：
- open: 开关状态(true:开启，false:关闭)

#### 5.22.3 设置触摸类型

设置触摸操作的类型。

```swift
setTouchType(type: CRPTouchType)
```

参数说明：
- type: 触摸类型

#### 5.22.4 设置触摸屏幕尺寸

设置触摸屏幕尺寸，固件需要根据屏幕尺寸确定触摸范围。

```swift
setTouchSize(width: Int, height: Int)
```

参数说明：
- width: 屏幕宽度
- height: 屏幕高度

#### 5.22.5 获取配对状态

获取当前配对状态，结果通过代理方法`receivePairState`返回。

```swift
getPairState()
```

#### 5.22.6 发起配对

如果未配对，可以重新发起配对过程。

```swift
setPair()
```

#### 5.22.7 清除配对信息

清除已有的配对信息。

```swift
clearPairInfo()
```

#### 5.22.8 设置敲击开关

设置敲击功能的开关状态。敲击功能允许用户通过敲击戒指来触发特定操作。

```swift
setKnockSwitch(open: Bool)
```

参数说明：
- open: 开关状态(true:开启，false:关闭)

#### 5.22.9 获取敲击开关

获取当前敲击功能的开关状态，结果通过代理方法`receiveKnockSwitch`返回。

```swift
getKnockSwitch()
```

#### 5.22.10 设置敲击灵敏度

设置敲击功能的灵敏度级别，可以根据用户习惯调整敲击识别灵敏度。

```swift
setKnockSensitivity(level: Int)
```

参数说明：
- level: 灵敏度级别(0-10，值越大灵敏度越高)

#### 5.22.11 获取敲击灵敏度

获取当前敲击功能的灵敏度级别设置。

```swift
getKnockSensitivity(_ handler: @escaping intHandler)
```

### 5.23 消息推送设置

消息推送设置功能提供了管理戒指接收推送通知的方法。

#### 5.23.1 查询支持的消息推送类型

查询戒指支持的消息推送类型，可以与CRPNotificationType比较以确定戒指支持的推送类型。

```swift
getNotificationSupportType(_ handler: @escaping notificationsHandler)
```

#### 5.23.2 查询消息推送状态

查询当前消息推送类型的开关状态。

```swift
getNotificationState(_ handler: @escaping notificationsHandler)
```

#### 5.23.3 设置消息推送状态

设置消息推送类型的开关状态。

```swift
setNotificationState(_ states: [Int])
```

参数说明：
- states: 消息推送类型状态数组

### 5.24 SOS功能

SOS功能提供了紧急情况下的求助功能管理方法。

#### 5.24.1 查询SOS功能开关

获取当前SOS功能的开关状态。

```swift
getSOSState(_ handler: @escaping boolHandler)
```

#### 5.24.2 设置SOS功能开关

设置SOS功能的开关状态。

```swift
setSOSState(open: Bool)
```

参数说明：
- open: 开关状态(true:开启，false:关闭)

### 5.25 小说翻页设置

小说翻页设置功能提供了管理戒指辅助翻页功能的方法。

#### 5.25.1 查询小说翻页设置

查询当前翻页模式设置。

```swift
getPageTurnMode(handler: @escaping intHandler)
```

#### 5.25.2 设置小说翻页设置

设置翻页模式。

```swift
setPageTurnMode(mode: Int)
```

参数说明：
- mode: 翻页模式(0: 上下翻页，1: 其他模式，其他模式由手机音量键控制)

### 5.26 冥想功能

冥想功能提供了管理和记录冥想活动的方法。

#### 5.26.1 查询冥想状态

查询当前冥想状态设置，也可用于查询是否支持开始冥想。结果通过代理方法`receiveMeditationState`返回。

```swift
getMeditationState()
```

#### 5.26.2 设置冥想状态

设置冥想状态。

```swift
setMeditation(state: CRPMeditationSetState)
```

参数说明：
- state: 冥想设置状态

#### 5.26.3 查询冥想历史列表

查询冥想历史记录列表，结果通过代理方法`receiveMeditationList`返回。

```swift
getMeditationRecordList()
```

#### 5.26.4 查询冥想历史详情

查询特定冥想记录的详细数据。

```swift
getMeditationRecordData(id: Int, _ handler: @escaping meditationDetailHandler)
```

参数说明：
- id: 冥想记录ID
- handler: 回调函数，返回冥想详细数据

### 5.27 GoMore算法

GoMore算法功能提供了专门用于睡眠分析的高级数据处理方法。

#### 5.27.1 查询固件是否支持GoMore算法

查询当前固件是否支持GoMore算法功能。

```swift
getGoMoreSupport(_ handler: @escaping boolHandler)
```

#### 5.27.2 查询GoMore算法密钥是否与固件匹配

查询当前算法密钥是否与固件匹配，用于验证算法授权。

```swift
getGoMoreKeyMatch(_ handler: @escaping boolHandler)
```

#### 5.27.3 查询GoMore芯片ID

获取GoMore芯片的唯一识别ID，用于算法授权。

```swift
getGoMoreChipID(_ handler: @escaping stringHandler)
```

#### 5.27.4 设置GoMore密钥

为固件设置GoMore算法密钥，启用相关功能。

```swift
setGoMoreKey(key: String, _ handler: @escaping intHandler)
```

参数说明：
- key: GoMore算法密钥
- handler: 回调函数，返回设置结果

#### 5.27.5 获取GoMore密钥类型

获取当前设备使用的GoMore算法密钥类型。

```swift
getGoMoreKeyType(_ handler: @escaping intHandler)
```

#### 5.27.6 获取GoMore睡眠数据列表

获取GoMore算法处理的睡眠数据列表，结果通过代理方法`receiveSleepList`回调返回。

```swift
getGoMoreSleepDataList()
```

#### 5.27.7 获取GoMore睡眠详情

根据睡眠记录ID获取详细的睡眠数据。

```swift
getGoMoreSleepDataDetail(id: Int, _ handler: @escaping goMoreSleepDataHandler)
```

参数说明：
- id: 睡眠记录ID
- handler: 回调函数，返回详细的睡眠数据

#### 5.27.8 获取GoMore睡眠分段数据

获取根据ID的四段睡眠数据，仅适用于长睡眠类型，不适用于短睡眠类型。

```swift
getGoMoreSleepSegmentationData(id: Int, _ handler: @escaping goMoreSleepRecordHandler)
```

参数说明：
- id: 睡眠记录ID
- handler: 回调函数，返回睡眠分段数据

#### 5.27.9 获取GoMore睡眠类型输出

获取睡眠时间类型输出结果。

```swift
getGoMoreSleepType(_ handler: @escaping goMoreSleepTypeHandler)
```

#### 5.27.10 结束睡眠获取算法输出

APP结束睡眠并获取算法输出结果（00: 失败 01: 成功 FF: 未睡眠）。

```swift
setTerminationSleep(_ handler: @escaping intHandler)
```

### 5.28 血压测量

血压测量功能提供了测量和获取用户血压数据的方法。

#### 5.28.1 开始单次血压测量

启动单次血压测量，测量结果将通过代理方法`receiveBloodPressure`返回。

```swift
setStartBP()
```

#### 5.28.2 结束单次血压测量

结束血压测量过程。测量结果为255或0表示血压测量被中断。

```swift
setStopBP()
```

## 6. 版本更新日志

### 1.0.4
- 增强体温功能
- 增强测量状态获取功能
- 增强运动状态获取功能
- 整合移动相关方法和设置运动目标方法

### 1.0.5
- 增加广播获取戒指配置项信息
- 增加获取和设置戒指配置项信息的方法
- 增强压力单次测量方法

### 1.0.6
1. 增强获取历史单次压力测量方法
2. 修改获取历史单次HRV测量记录方法
3. 修改获取全天HRV测量记录方法

### 1.0.7
- 修改久坐提醒回调数据处理问题

### 1.0.8
- 增加触摸相关方法

### 1.0.9
- 修改触摸相关方法

### 1.1.0
1. 增加谁发送消息设置
2. 增加SOS开关设置
3. 增加一天内每分钟步数统计

### 1.1.1
- 增加小说翻页模式设置

### 1.1.2
- 增加冥想功能

### 1.1.3
- 增加GoMore算法授权与睡眠数据功能

### 1.1.4
- 增加运动识别相关功能
- 修复醒来时第一份睡眠数据被过滤的问题

### 1.1.6
- 修复解析睡眠数据时睡眠时间为0的数据被过滤的问题
- 增加获取睡眠血氧功能是否支持的接口

### 1.1.7
- 适配新版本的运动识别协议

### 1.1.8
- 冥想功能命令修改

### 1.1.9
- 适配DIY升级
- 时间戳数据增加返回原始时间戳属性

### 1.2.0
- 增加日志打印功能
- 适配中科蓝讯平台OTA

### 1.2.1
- 适配中科蓝讯平台指令间隔问题

### 1.2.2
- 修复空电量恢复数据导致的解析崩溃问题

### 1.2.3
- 增加单次血压、体温测量和首次连接指令
- 修改关机指令回调是否传输成功

### 1.2.4
1. 修改5331芯片OTA方法
2. 增加摇一摇拍照功能
3. 增加修改蓝牙传输功率功能

### 1.2.5
- 修改5331 BootLoader模式启动升级适配问题

### 1.2.6
1. 增加敲击控制相关方法
2. 增加获取GoMore算法的密钥类型

### 1.2.9
1. 增加设置屏幕时间、屏幕亮度和屏幕内容的方法

## 7. 最新功能详解

### 7.1 GoMore睡眠算法功能

GoMore睡眠算法功能提供了专门用于睡眠分析的高级数据处理方法。

#### 7.1.1 获取GoMore睡眠数据列表

获取GoMore算法处理的睡眠数据列表，结果通过代理方法`receiveSleepList`回调返回。

```swift
getGoMoreSleepDataList()
```

#### 7.1.2 获取GoMore睡眠详情

根据睡眠记录ID获取详细的睡眠数据。

```swift
getGoMoreSleepDataDetail(id: Int, _ handler: @escaping goMoreSleepDataHandler)
```

参数说明：
- id: 睡眠记录ID
- handler: 回调函数，返回详细的睡眠数据

#### 7.1.3 获取GoMore睡眠分段数据

获取根据ID的四段睡眠数据，仅适用于长睡眠类型，不适用于短睡眠类型。

```swift
getGoMoreSleepSegmentationData(id: Int, _ handler: @escaping goMoreSleepRecordHandler)
```

参数说明：
- id: 睡眠记录ID
- handler: 回调函数，返回睡眠分段数据

#### 7.1.4 获取GoMore睡眠类型输出

获取睡眠时间类型输出结果。

```swift
getGoMoreSleepType(_ handler: @escaping goMoreSleepTypeHandler)
```

#### 7.1.5 结束睡眠获取算法输出

APP结束睡眠并获取算法输出结果（00: 失败 01: 成功 FF: 未睡眠）。

```swift
setTerminationSleep(_ handler: @escaping intHandler)
```

### 7.2 血压测量

血压测量功能提供了测量和获取用户血压数据的方法。

#### 7.2.1 开始单次血压测量

启动单次血压测量，测量结果将通过代理方法`receiveBloodPressure`返回。

```swift
setStartBP()
```

#### 7.2.2 结束单次血压测量

结束血压测量过程。测量结果为255或0表示血压测量被中断。

```swift
setStopBP()
```

### 7.3 首次连接提示

首次连接提示功能提供了首次连接设备时的交互提示设置。

#### 7.3.1 设置首次连接闪烁

设置首次连接时戒指闪烁提示。

```swift
setFirstConnect()
```

### 7.4 拍照功能

拍照功能提供了使用戒指控制手机拍照的相关方法。

#### 7.4.1 设置摇一摇拍照开关

设置摇一摇拍照功能的开关状态。

```swift
setShakeToTakePhoto(open: Bool)
```

参数说明：
- open: 开关状态(true:开启，false:关闭)

#### 7.4.2 获取摇一摇拍照开关状态

获取当前摇一摇拍照功能的开关状态。

```swift
getShakeToTakePhoto(_ handler: @escaping boolHandler)
```

### 7.5 蓝牙传输频率

蓝牙传输频率功能提供了设置和获取蓝牙传输功率的方法。

#### 7.5.1 设置蓝牙传输功率

设置蓝牙传输的功率级别。

```swift
setTransmitPower(level: Int)
```

参数说明：
- level: 功率级别，数值越大功率越高，传输距离越远，但电池消耗也越大

#### 7.5.2 获取蓝牙传输功率

获取当前蓝牙传输的功率级别设置。

```swift
getTransmitPower(_ handler: @escaping intHandler)
```

### 7.6 屏幕时间

部分戒指型号支持屏幕时间设置，提供了设置和获取屏幕显示时间的方法。

#### 7.6.1 设置屏幕时间

设置屏幕显示的时间长度。

```swift
setAutoLockTime(time: Int)
```

参数说明：
- time: 屏幕显示时间，单位为秒

#### 7.6.2 获取屏幕时间

获取当前设置的屏幕显示时间。

```swift
getAutoLockTime(_ handler: @escaping intHandler)
```

### 7.7 屏幕亮度

部分戒指型号支持屏幕亮度设置，提供了设置和获取屏幕亮度的方法。

#### 7.7.1 设置屏幕亮度

设置屏幕的亮度级别。

```swift
setScreenBrightness(level: Int)
```

参数说明：
- level: 亮度级别，数值越大亮度越高

#### 7.7.2 获取屏幕亮度

获取当前设置的屏幕亮度级别。

```swift
getScreenBrightness(_ handler: @escaping intHandler)
```

### 7.8 屏幕内容设置

部分戒指型号支持屏幕内容设置，提供了设置和获取屏幕显示内容的方法。

#### 7.8.1 获取屏幕支持的显示内容

获取屏幕支持显示的内容类型列表。

```swift
getDisplaySupportInfo(_ handler: @escaping displayTypeHandler)
```

#### 7.8.2 设置屏幕内容

设置屏幕显示的内容类型，类型为CRPDisplayType枚举的原始值数组。

```swift
setDisplayContent(types: [Int])
```

参数说明：
- types: 内容类型数组，包含CRPDisplayType枚举的原始值

#### 7.8.3 获取屏幕内容设置

获取当前设置的屏幕显示内容类型。

```swift
getDisplayContent(_ handler: @escaping displayTypeHandler)
```

## 8. 开发建议

1. **初始化流程**：
   - 首先初始化SDK
   - 设置必要的代理方法
   - 开始扫描设备
   - 建立连接

2. **数据获取策略**：
   - 对于实时数据（如心率），使用相应的实时监测方法
   - 对于历史数据，使用相应的记录获取方法

3. **错误处理**：
   - 实现连接状态回调，处理连接中断情况
   - 检查操作返回值，处理可能的错误

4. **功耗优化**：
   - 不需要实时数据时，关闭相应的监测功能
   - 合理设置蓝牙传输频率

## 9. 总结

WindRing SDK提供了全面的智能戒指设备控制和数据获取功能，覆盖了从设备连接到各种健康数据监测的完整流程。通过合理使用SDK提供的接口，开发者可以构建功能丰富的健康和健身应用程序，为用户提供全面的健康管理体验。

此SDK包含了详细的设备发现与连接机制，多种健康数据的采集和分析功能，以及丰富的设备控制能力，使开发者能够轻松实现与WindRing智能戒指的交互，为用户提供全面的健康管理解决方案。 