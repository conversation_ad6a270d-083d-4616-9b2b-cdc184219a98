import Foundation
import Network
import Combine

/// 网络连接类型
public enum ConnectionType {
    case wifi
    case cellular
    case ethernet
    case unknown
}

/// 网络监控器
public class NetworkMonitor: ObservableObject {
    // MARK: - 单例
    public static let shared = NetworkMonitor()
    
    // MARK: - 属性
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    /// 网络连接状态发布者
    @Published public private(set) var isConnected = false
    
    /// 网络连接类型发布者
    @Published public private(set) var connectionType: ConnectionType = .unknown
    
    /// 网络连接状态变化发布者
    public let connectionStatusPublisher = PassthroughSubject<Bool, Never>()
    
    /// 网络连接类型变化发布者
    public let connectionTypePublisher = PassthroughSubject<ConnectionType, Never>()
    
    // MARK: - 初始化方法
    private init() {
        startMonitoring()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - 公共方法
    
    /// 开始监控网络
    public func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            guard let self = self else { return }
            
            // 更新连接状态
            let isConnected = path.status == .satisfied
            DispatchQueue.main.async {
                self.isConnected = isConnected
                self.connectionStatusPublisher.send(isConnected)
            }
            
            // 更新连接类型
            let connectionType = self.checkConnectionType(path)
            DispatchQueue.main.async {
                self.connectionType = connectionType
                self.connectionTypePublisher.send(connectionType)
            }
        }
        
        monitor.start(queue: queue)
    }
    
    /// 停止监控网络
    public func stopMonitoring() {
        monitor.cancel()
    }
    
    // MARK: - 私有方法
    
    /// 检查网络连接类型
    /// - Parameter path: 网络路径
    /// - Returns: 连接类型
    private func checkConnectionType(_ path: NWPath) -> ConnectionType {
        if path.usesInterfaceType(.wifi) {
            return .wifi
        } else if path.usesInterfaceType(.cellular) {
            return .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            return .ethernet
        } else {
            return .unknown
        }
    }
}

// MARK: - 扩展
extension NetworkMonitor {
    /// 检查是否连接到WiFi
    public var isWiFiConnected: Bool {
        return isConnected && connectionType == .wifi
    }
    
    /// 检查是否连接到蜂窝网络
    public var isCellularConnected: Bool {
        return isConnected && connectionType == .cellular
    }
    
    /// 检查是否连接到以太网
    public var isEthernetConnected: Bool {
        return isConnected && connectionType == .ethernet
    }
    
    /// 获取当前网络连接类型的描述
    public var connectionTypeDescription: String {
        switch connectionType {
        case .wifi:
            return "WiFi"
        case .cellular:
            return "蜂窝网络"
        case .ethernet:
            return "以太网"
        case .unknown:
            return "未知"
        }
    }
}

// MARK: - SwiftUI视图扩展
#if canImport(SwiftUI)
import SwiftUI

extension View {
    /// 添加网络连接状态监听
    /// - Parameter action: 网络状态变化时的回调
    /// - Returns: 修改后的视图
    public func onNetworkChange(perform action: @escaping (Bool) -> Void) -> some View {
        self.onReceive(NetworkMonitor.shared.$isConnected) { isConnected in
            action(isConnected)
        }
    }
    
    /// 添加网络连接类型监听
    /// - Parameter action: 网络连接类型变化时的回调
    /// - Returns: 修改后的视图
    public func onNetworkTypeChange(perform action: @escaping (ConnectionType) -> Void) -> some View {
        self.onReceive(NetworkMonitor.shared.$connectionType) { connectionType in
            action(connectionType)
        }
    }
    
    /// 当没有网络连接时显示提示
    /// - Parameters:
    ///   - isPresented: 是否显示提示的绑定
    ///   - content: 提示内容
    /// - Returns: 修改后的视图
    public func noNetworkAlert(isPresented: Binding<Bool>, content: @escaping () -> Alert) -> some View {
        self.onNetworkChange { isConnected in
            isPresented.wrappedValue = !isConnected
        }
        .alert(isPresented: isPresented) {
            content()
        }
    }
}
#endif 