<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="21754" systemVersion="22G90" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Activity" representedClassName="ActivityEntity" syncable="YES" codeGenerationType="class">
        <attribute name="calories" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="distance" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="duration" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="endTime" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="steps" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="type" attributeType="String"/>
        <attribute name="userId" attributeType="String"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="activities" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="BloodOxygen" representedClassName="BloodOxygenEntity" syncable="YES" codeGenerationType="class">
        <attribute name="confidence" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="bloodOxygenReadings" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="BloodPressure" representedClassName="BloodPressureEntity" syncable="YES" codeGenerationType="class">
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="diastolic" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="pulse" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="systolic" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="bloodPressureReadings" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Device" representedClassName="DeviceEntity" syncable="YES" codeGenerationType="class">
        <attribute name="batteryLevel" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="connectionStatus" attributeType="String"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="firmwareVersion" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="lastSyncTime" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="manufacturer" attributeType="String"/>
        <attribute name="model" attributeType="String"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="settings" optional="YES" attributeType="Transformable" valueTransformerName="NSSecureUnarchiveFromDataTransformer" customClassName="[String: String]"/>
        <attribute name="type" attributeType="String"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="devices" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="HealthGoal" representedClassName="HealthGoalEntity" syncable="YES" codeGenerationType="class">
        <attribute name="completed" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="current" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="endDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="startDate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="target" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="type" attributeType="String"/>
        <attribute name="unit" attributeType="String"/>
        <attribute name="userId" attributeType="String"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="healthGoals" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="HeartRate" representedClassName="HeartRateEntity" syncable="YES" codeGenerationType="class">
        <attribute name="confidence" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="heartRateReadings" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Meditation" representedClassName="MeditationEntity" syncable="YES" codeGenerationType="class">
        <attribute name="duration" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="endTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="type" optional="YES" attributeType="String"/>
        <attribute name="userId" attributeType="String"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="meditations" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Sleep" representedClassName="SleepEntity" syncable="YES" codeGenerationType="class">
        <attribute name="awakeMinutes" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="deepMinutes" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="efficiency" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="endTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="lightMinutes" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="remMinutes" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="score" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="totalMinutes" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="userId" attributeType="String"/>
        <relationship name="sleepStages" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="SleepStage" inverseName="sleep" inverseEntity="SleepStage"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="sleepRecords" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="SleepStage" representedClassName="SleepStageEntity" syncable="YES" codeGenerationType="class">
        <attribute name="duration" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="sleepId" attributeType="String"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="type" attributeType="String"/>
        <relationship name="sleep" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Sleep" inverseName="sleepStages" inverseEntity="Sleep"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Steps" representedClassName="StepsEntity" syncable="YES" codeGenerationType="class">
        <attribute name="calories" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="date" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="distance" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="stepsRecords" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Stress" representedClassName="StressEntity" syncable="YES" codeGenerationType="class">
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="stressReadings" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Temperature" representedClassName="TemperatureEntity" syncable="YES" codeGenerationType="class">
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="type" attributeType="String"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="temperatureReadings" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="User" representedClassName="UserEntity" syncable="YES" codeGenerationType="class">
        <attribute name="avatarUrl" optional="YES" attributeType="String"/>
        <attribute name="birthday" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="email" attributeType="String"/>
        <attribute name="gender" optional="YES" attributeType="String"/>
        <attribute name="height" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="language" optional="YES" attributeType="String"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="settings" optional="YES" attributeType="Transformable" valueTransformerName="NSSecureUnarchiveFromDataTransformer" customClassName="[String: Any]"/>
        <attribute name="unitSystem" optional="YES" attributeType="String"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="weight" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <relationship name="activities" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Activity" inverseName="user" inverseEntity="Activity"/>
        <relationship name="bloodOxygenReadings" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="BloodOxygen" inverseName="user" inverseEntity="BloodOxygen"/>
        <relationship name="bloodPressureReadings" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="BloodPressure" inverseName="user" inverseEntity="BloodPressure"/>
        <relationship name="devices" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Device" inverseName="user" inverseEntity="Device"/>
        <relationship name="healthGoals" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="HealthGoal" inverseName="user" inverseEntity="HealthGoal"/>
        <relationship name="heartRateReadings" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="HeartRate" inverseName="user" inverseEntity="HeartRate"/>
        <relationship name="meditations" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Meditation" inverseName="user" inverseEntity="Meditation"/>
        <relationship name="sleepRecords" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Sleep" inverseName="user" inverseEntity="Sleep"/>
        <relationship name="stepsRecords" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Steps" inverseName="user" inverseEntity="Steps"/>
        <relationship name="stressReadings" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Stress" inverseName="user" inverseEntity="Stress"/>
        <relationship name="temperatureReadings" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Temperature" inverseName="user" inverseEntity="Temperature"/>
        <relationship name="waterRecords" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Water" inverseName="user" inverseEntity="Water"/>
        <relationship name="weightRecords" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Weight" inverseName="user" inverseEntity="Weight"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
            <uniquenessConstraint>
                <constraint value="email"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Water" representedClassName="WaterEntity" syncable="YES" codeGenerationType="class">
        <attribute name="date" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="waterRecords" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Weight" representedClassName="WeightEntity" syncable="YES" codeGenerationType="class">
        <attribute name="bodyFat" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="bmi" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="muscleMass" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="weightRecords" inverseEntity="User"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="CachedMQTTMessage" representedClassName="CachedMQTTMessage" syncable="YES">
        <attribute name="id" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="isSynced" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="message" optional="YES" attributeType="Binary"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="topic" optional="YES" attributeType="String"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
</model> 