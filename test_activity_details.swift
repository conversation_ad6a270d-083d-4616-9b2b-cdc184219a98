import Foundation
import SwiftUI
import Combine

// 模拟API响应
let jsonString = """
{
  "code": 0,
  "data": [
    {
      "steps": 120,
      "time": 1743141600000
    },
    {
      "steps": 350,
      "time": 1743143400000
    },
    {
      "steps": 580,
      "time": 1743145200000
    },
    {
      "steps": 280,
      "time": 1743156600000
    },
    {
      "steps": 450,
      "time": 1743166800000
    }
  ],
  "msg": ""
}
"""

// 简化版的数据模型
struct TestActivityDetailData: Codable, Identifiable {
    var id: String { "\(time)" }
    
    let steps: Int
    let time: Int64
    
    // 解析毫秒时间戳
    var date: Date? {
        return Date(timeIntervalSince1970: Double(time) / 1000.0)
    }
    
    // 格式化时间显示
    func formattedTime() -> String {
        guard let date = self.date else {
            return "无效时间"
        }
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    // 计算半小时索引
    var halfHourIndex: Int? {
        guard let date = self.date else {
            return nil
        }
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        return hour * 2 + (minute >= 30 ? 1 : 0)
    }
}

// 响应结构
struct TestActivityDetailResponse: Codable {
    let code: Int
    let data: [TestActivityDetailData]?
    let msg: String
}

print("开始测试活动明细API接入...")
print("===========================\n")

print("模拟API响应:")
print(jsonString)
print("\n")

// 解析JSON
do {
    let data = jsonString.data(using: .utf8)!
    let decoder = JSONDecoder()
    let response = try decoder.decode(TestActivityDetailResponse.self, from: data)
    
    // 检查解析结果
    print("解析结果:")
    print("- code =", response.code)
    print("- 数据项数量 =", response.data?.count ?? 0)
    
    if let detailData = response.data {
        print("\n活动明细数据:")
        for (index, detail) in detailData.enumerated() {
            print("[\(index+1)] 时间戳: \(detail.time), 步数: \(detail.steps)")
            if let date = detail.date {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                print("    日期时间: \(formatter.string(from: date))")
            }
            print("    格式化时间: \(detail.formattedTime())")
            if let halfHourIndex = detail.halfHourIndex {
                print("    半小时索引: \(halfHourIndex)")
                
                // 计算正确的时间范围
                let hour = halfHourIndex / 2
                let minute = (halfHourIndex % 2) * 30
                let timeRangeStart = String(format: "%02d:%02d", hour, minute)
                let nextHourIndex = halfHourIndex + 1
                let nextHour = nextHourIndex / 2
                let nextMinute = (nextHourIndex % 2) * 30
                let timeRangeEnd = String(format: "%02d:%02d", nextHour, nextMinute)
                
                print("    时间范围: \(timeRangeStart)-\(timeRangeEnd)")
            } else {
                print("    半小时索引: 无效")
            }
        }
        
        // 转换为UI所需的半小时步数分布
        var hourlyStepData = Array(repeating: 0, count: 48)
        for detail in detailData {
            if let halfHourIndex = detail.halfHourIndex, halfHourIndex >= 0 && halfHourIndex < 48 {
                hourlyStepData[halfHourIndex] = detail.steps
            }
        }
        
        print("\n转换为半小时数据后:")
        var displayedData = 0
        for i in 0..<48 {
            if hourlyStepData[i] > 0 {
                let hour = i / 2
                let minute = (i % 2) * 30
                let timeStr = String(format: "%02d:%02d", hour, minute)
                print("时间: \(timeStr), 步数: \(hourlyStepData[i])")
                displayedData += 1
                if displayedData >= 10 { // 最多显示10条数据
                    if detailData.count > 10 {
                        print("...")
                    }
                    break
                }
            }
        }
    }
    
    print("\n✅ 测试通过: 成功解析活动明细数据")
} catch {
    print("❌ 测试失败: JSON解析错误:", error)
}

print("\n===========================")
print("测试完成") 