// ... existing code ...
struct FamilyDataSharingView: View {
    // 添加状态变量来跟踪当前选中的选项卡和提示文本
    @State private var selectedTab: Int = 0
    @State private var promptText: String = "Go invite your relatives to share their data"
    // 添加状态变量来控制确认对话框的显示
    @State private var showConfirmationDialog: Bool = false
    // 添加状态变量来控制分享链接弹框的显示
    @State private var showShareSheet: Bool = false
    // 分享链接
    @State private var shareLink: String = "https://ziwang.cn/h5/windring/gon..."
    
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                // 顶部选项卡
                HStack(spacing: 0) {
                    // 左侧选项卡 - Family data
                    tabButton(title: "Family data", isSelected: selectedTab == 0) {
                        selectedTab = 0
                        promptText = "Go invite your relatives to share their data"
                    }
                    
                    // 右侧选项卡 - Family data (灰色)
                    tabButton(title: "Family data", isSelected: selectedTab == 1) {
                        selectedTab = 1
                        promptText = "Let my family know about my health condition"
                    }
                }
                .padding(.top, 20)
                
                Spacer()
                
                // 中央空状态部分
                VStack(spacing: 30) {
                    // 箱子图标
                    ZStack {
                        // 使用系统箱子图标，也可以替换为自定义图标
                        Image(systemName: "cube")
                            .font(.system(size: 100))
                            .foregroundColor(.gray.opacity(0.5))
                    }
                    .frame(width: 150, height: 150)
                    
                    // 提示文本 - 根据选中的选项卡显示不同的文本
                    Text(promptText)
                        .font(.title3)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.gray)
                        .padding(.horizontal, 40)
                        .animation(.easeInOut, value: promptText)
                }
                
                Spacer()
                
                // 底部按钮 - 根据选中的选项卡显示不同的文本
                Button(action: {
                    // 显示确认对话框
                    showConfirmationDialog = true
                }) {
                    Text(selectedTab == 0 ? "Invite others to share" : "与他人共享")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color(hex: "#FA6C2D"))
                        .cornerRadius(25)
                        .padding(.horizontal, 20)
                }
                .padding(.bottom, 40)
            }
            .navigationTitle("Family Sharing")
            .navigationBarTitleDisplayMode(.inline)
            .background(Color(UIColor.systemGroupedBackground).edgesIgnoringSafeArea(.all))
            
            // 确认对话框
            if showConfirmationDialog {
                // 半透明背景
                Color.black.opacity(0.4)
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        // 点击背景关闭对话框
                        showConfirmationDialog = false
                    }
                
                // 对话框内容
                VStack(spacing: 20) {
                    // 对话框文本
                    Text("We are about to invite other users to share their health data with you through the link, which is valid for 30 minutes!")
                        .font(.headline)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    
                    // 按钮区域
                    HStack(spacing: 20) {
                        // 取消按钮
                        Button(action: {
                            showConfirmationDialog = false
                        }) {
                            Text("Cancel")
                                .font(.headline)
                                .foregroundColor(Color(hex: "#FA6C2D"))
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.white)
                                .cornerRadius(10)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 10)
                                        .stroke(Color(hex: "#FA6C2D"), lineWidth: 1)
                                )
                        }
                        
                        // 确认按钮
                        Button(action: {
                            // 关闭确认对话框
                            showConfirmationDialog = false
                            // 显示分享链接弹框
                            showShareSheet = true
                        }) {
                            Text("Confirm")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color(hex: "#FA6C2D"))
                                .cornerRadius(10)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                }
                .frame(width: UIScreen.main.bounds.width - 60)
                .background(Color.white)
                .cornerRadius(20)
                .shadow(radius: 10)
            }
            
            // 分享链接弹框 - 修改为居中显示的弹框
            if showShareSheet {
                // 半透明背景
                Color.black.opacity(0.4)
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        showShareSheet = false
                    }
                
                // 居中显示的分享弹框
                VStack(spacing: 20) {
                    // 关闭按钮
                    HStack {
                        Spacer()
                        Button(action: {
                            showShareSheet = false
                        }) {
                            Image(systemName: "xmark")
                                .font(.title2)
                                .foregroundColor(.black)
                                .padding(.trailing, 10)
                        }
                    }
                    
                    // 链接图标
                    Image(systemName: "link.circle")
                        .font(.system(size: 60))
                        .foregroundColor(.gray)
                    
                    // 链接标题
                    Text("【WindRing】开启亲友关怀之旅:")
                        .font(.headline)
                        .foregroundColor(.black)
                    
                    // 链接URL
                    Text(shareLink)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .lineLimit(1)
                        .truncationMode(.middle)
                        .padding(.bottom, 10)
                    
                    // 分享选项
                    HStack(spacing: 25) {
                        // 微信好友
                        shareOption(icon: "person.circle.fill", name: "微信好友", color: .green)
                        
                        // 微信收藏
                        shareOption(icon: "square.grid.2x2.fill", name: "微信收藏", color: .yellow)
                        
                        // QQ好友
                        shareOption(icon: "bubble.left.fill", name: "QQ好友", color: .blue)
                        
                        // QQ邮箱
                        shareOption(icon: "envelope.fill", name: "QQ邮箱", color: .orange)
                    }
                    .padding(.horizontal)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 25)
                .background(Color.white)
                .cornerRadius(20)
                .shadow(radius: 10)
                .frame(width: UIScreen.main.bounds.width - 60)
                .transition(.opacity)
                .animation(.easeInOut, value: showShareSheet)
            }
        }
    }
    
    // 自定义选项卡按钮
    private func tabButton(title: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(isSelected ? .black : .gray)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
            }
        }
        .frame(maxWidth: .infinity)
        .background(Color.clear)
    }
    
    // 分享选项按钮 - 修改为使用传入的颜色
    private func shareOption(icon: String, name: String, color: Color) -> some View {
        VStack {
            // 图标 - 使用系统图标替代，实际应用中应使用实际的社交媒体图标
            ZStack {
                Circle()
                    .fill(color)
                    .frame(width: 60, height: 60)
                
                Image(systemName: icon)
                    .font(.system(size: 30))
                    .foregroundColor(.white)
            }
            
            // 名称
            Text(name)
                .font(.caption)
                .foregroundColor(.black)
        }
        .onTapGesture {
            // 处理分享操作
            // 这里可以添加实际的分享逻辑
        }
    }
}
// ... existing code ...