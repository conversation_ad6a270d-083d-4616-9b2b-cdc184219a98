# WindRing MQTT集成

本文档介绍了如何在WindRing应用中集成和使用MQTT功能。

## 概述

MQTT（消息队列遥测传输）是一种轻量级的发布/订阅消息传输协议，非常适合用于物联网设备通信。在WindRing应用中，我们使用MQTT来实现以下功能：

1. 实时数据同步
2. 设备状态监控
3. 远程命令下发
4. 离线数据缓存与同步

## 架构

WindRing MQTT集成架构包括以下组件：

- **MQTTService**: MQTT客户端服务，负责与MQTT服务器的连接和消息收发
- **MQTTCacheManager**: 本地缓存管理器，负责在离线状态下缓存数据
- **MQTTSyncService**: 同步服务，负责协调MQTT服务和缓存管理器
- **CachedMQTTMessage**: CoreData实体，用于存储缓存的消息

## 安装

### 前提条件

- iOS 15.0+
- Xcode 13.0+
- CocoaPods

### 安装步骤

1. 克隆项目到本地
2. 运行安装脚本：

```bash
chmod +x install_mqtt.sh
./install_mqtt.sh
```

3. 使用生成的`.xcworkspace`文件打开项目

## 配置

MQTT服务器配置在`MQTTService.swift`文件中：

```swift
// MQTT连接参数
private let host = "*************"
private let port: UInt16 = 12930
```

如需修改MQTT服务器地址，请更新这些参数。

## 使用方法

### 初始化MQTT服务

MQTT服务在应用启动时自动初始化，无需手动操作。

### 发送健康数据

```swift
// 构建健康数据
let healthData: [String: Any] = [
    "device_id": deviceId,
    "type": "heart_rate",
    "value": 75,
    "timestamp": Date().timeIntervalSince1970
]

// 发送到MQTT
MQTTSyncService.shared.cacheHealthData(ringId: deviceId, data: healthData)
```

### 发送设备状态

```swift
// 构建设备状态数据
let statusData: [String: Any] = [
    "device_id": deviceId,
    "battery_level": 85,
    "wearing_state": 1,
    "connection_state": 2,
    "firmware_version": "1.2.3",
    "last_updated": Date().timeIntervalSince1970
]

// 发送到MQTT
MQTTSyncService.shared.cacheDeviceStatus(ringId: deviceId, status: statusData)
```

### 订阅主题

```swift
// 订阅用户通知
MQTTService.shared.subscribeToUserNotifications(userId: userId)

// 订阅戒指命令
MQTTService.shared.subscribeToRingCommands(ringId: deviceId)
```

### 手动同步

```swift
// 手动触发同步
MQTTSyncService.shared.syncNow()
```

## 主题设计

WindRing MQTT使用以下主题结构：

- `ring/{ring_id}/health_data`: 健康数据
- `ring/{ring_id}/status`: 设备状态
- `ring/{ring_id}/commands`: 发送给戒指的命令
- `user/{user_id}/notifications`: 用户通知
- `app/{device_id}/commands`: 发送给APP的命令

## 消息格式

所有MQTT消息使用JSON格式，基本结构如下：

```json
{
  "type": "health_data",
  "timestamp": **********,
  "data": {
    // 具体数据字段
  }
}
```

## 故障排除

### 连接问题

如果无法连接到MQTT服务器，请检查：

1. 网络连接是否正常
2. MQTT服务器地址和端口是否正确
3. 认证信息是否正确

### 同步问题

如果数据同步失败，请尝试：

1. 检查MQTT连接状态
2. 手动触发同步
3. 查看日志中的错误信息

## 开发者工具

### MQTT客户端工具

推荐使用以下工具测试MQTT连接：

- [MQTT Explorer](http://mqtt-explorer.com/)
- [MQTT.fx](https://mqttfx.jensd.de/)
- [MQTT CLI](https://github.com/hivemq/mqtt-cli)

### 测试命令

```bash
# 订阅测试
mqtt subscribe -h ************* -p 12930 -t 'test/topic'

# 发布测试
mqtt publish -h ************* -p 12930 -t 'test/topic' -m 'Hello MQTT'
``` 