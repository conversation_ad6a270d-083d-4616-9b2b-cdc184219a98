# CoreData 设置指南

为了使MQTT缓存功能正常工作，您需要在CoreData模型中手动添加`CachedMQTTMessage`实体。请按照以下步骤操作：

## 添加CachedMQTTMessage实体

1. 打开Xcode项目
2. 在项目导航器中找到`WindRing.xcdatamodeld`文件并打开它
3. 点击"Add Entity"按钮（右下角的+号）
4. 将新实体命名为`CachedMQTTMessage`
5. 在"Class"字段中输入`WindRing.CachedMQTTMessage`

## 添加属性

为`CachedMQTTMessage`实体添加以下属性：

| 属性名 | 类型 | 可选 |
|-------|------|------|
| id | UUID | 否 |
| topic | String | 否 |
| message | Binary Data | 否 |
| timestamp | Date | 否 |
| isSynced | Boolean | 否 |

## 更新iOS部署目标

由于MQTT依赖库的要求，您需要将项目的iOS部署目标设置为12.0或更高：

1. 选择项目文件
2. 选择"WindRing"目标
3. 在"General"标签页中，将"Deployment Info"下的"iOS Deployment Target"设置为12.0或更高

## 完成设置

完成上述步骤后，请重新构建项目。MQTT功能应该能够正常工作了。 