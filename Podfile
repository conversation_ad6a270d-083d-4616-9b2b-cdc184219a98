# Uncomment the next line to define a global platform for your project
platform :ios, '15.0'

# 禁用未使用的master specs repo警告
install! 'cocoapods', :warn_for_unused_master_specs_repo => false

target 'WindRing' do
  # 使用静态库而不是动态框架
  use_frameworks! :linkage => :static

  # Pods for WindRing
  pod 'CocoaMQTT', '~> 2.1.0'
  
  pod 'FLEX', :configurations => ['Debug']
  
  #pod 'CoreStore', '~> 9.2.0'
  #pod 'Alamofire', '~> 5.6.4'
  pod 'Sentry', :git => 'https://github.com/getsentry/sentry-cocoa.git', :tag => '8.52.1'
  
  pod 'IQKeyboardManagerSwift'
  
  # 设置CocoaMQTT和MqttCocoaAsyncSocket的部署目标版本
  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'
        
        # 禁用位置相关的警告
        config.build_settings['CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER'] = 'NO'
        
        # 确保正确的构建设置
        config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'YES'
      end
    end
    
    # 修复项目设置
    installer.aggregate_targets.each do |aggregate_target|
      aggregate_target.xcconfigs.each do |config_name, config_file|
        # 修复LD_RUNPATH_SEARCH_PATHS
        config_file.attributes['LD_RUNPATH_SEARCH_PATHS'] = ['$(inherited)', '@executable_path/Frameworks']
        
        # 将修改后的配置写回文件
        xcconfig_path = aggregate_target.xcconfig_path(config_name)
        config_file.save_as(xcconfig_path)
      end
      
      # 更新项目的构建设置
      aggregate_target.user_project.targets.each do |user_target|
        user_target.build_configurations.each do |user_config|
          # 确保使用$(inherited)
          user_config.build_settings['LD_RUNPATH_SEARCH_PATHS'] = '$(inherited) @executable_path/Frameworks'
        end
      end
      aggregate_target.user_project.save
    end
  end

  target 'WindRingTests' do
    inherit! :search_paths
    # Pods for testing
  end

  target 'WindRingUITests' do
    # Pods for testing
  end

end 
