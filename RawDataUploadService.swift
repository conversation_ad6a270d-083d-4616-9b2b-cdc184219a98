import Foundation

class RawDataUploadService {
    private let deviceService: DeviceService

    init(deviceService: DeviceService) {
        self.deviceService = deviceService
    }

    func uploadSleepDataFor7Day() {
        // 首先检查设备是否已连接，避免在设备未连接时调用API
        guard deviceService.isDeviceConnected() else {
            print("设备未连接，无法上传睡眠数据")
            return
        }
        
        // ... existing code ...
    } else {
        print("设备不支持Golore接入，使用基础睡眠数据获取和上传数据")
        
        // 使用基础API获取并上传睡眠数据
        let dispatchGroup = DispatchGroup()
        
        for day in 0..<7 {
            dispatchGroup.enter()
            
            // 使用try-catch包装getSleepData调用，处理可能的崩溃
            do {
                deviceService.getSleepData(day: day) { [weak self] sleepData, error in
                    // 确保在所有情况下都调用leave
                    defer { 
                        dispatchGroup.leave() 
                        print("完成处理第\(day)天的睡眠数据")
                    }
                    
                    guard let self = self else { return }
                    
                    if let error = error {
                        print("获取第\(day)天的睡眠数据失败: \(error)")
                        return
                    }
                    
                    guard let sleepData = sleepData else {
                        print("第\(day)天没有睡眠数据")
                        return
                    }
                    
                    // 添加额外的数据验证
                    guard self.isValidSleepData(sleepData) else {
                        print("第\(day)天的睡眠数据格式无效")
                        return
                    }
                    
                    // 继续处理有效的睡眠数据
                    // ... existing code ...
                }
            } catch {
                print("调用getSleepData时发生异常: \(error)")
                dispatchGroup.leave() // 确保即使发生异常也会leave
            }
        }
        
        // 添加超时处理
        let result = dispatchGroup.wait(timeout: .now() + 30)
        if result == .timedOut {
            print("获取睡眠数据超时")
        }
    }

    // 添加辅助方法验证睡眠数据
    private func isValidSleepData(_ data: Any) -> Bool {
        // 根据实际数据结构实现验证逻辑
        // 例如检查必要字段是否存在、数据是否符合预期格式等
        return true // 临时返回true，根据实际需求调整
    }
} 