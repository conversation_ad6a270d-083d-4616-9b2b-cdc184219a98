#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始安装MQTT依赖...${NC}"

# 检查是否安装了CocoaPods
if ! command -v pod &> /dev/null; then
    echo -e "${RED}未找到CocoaPods，正在安装...${NC}"
    sudo gem install cocoapods
    if [ $? -ne 0 ]; then
        echo -e "${RED}CocoaPods安装失败，请手动安装后重试。${NC}"
        exit 1
    fi
    echo -e "${GREEN}CocoaPods安装成功！${NC}"
fi

# 安装CocoaMQTT依赖
echo -e "${YELLOW}正在安装CocoaMQTT依赖...${NC}"
pod install

if [ $? -ne 0 ]; then
    echo -e "${RED}CocoaMQTT安装失败，请检查错误信息并重试。${NC}"
    exit 1
fi

echo -e "${GREEN}CocoaMQTT安装成功！${NC}"
echo -e "${YELLOW}请使用.xcworkspace文件打开项目。${NC}"

# 修改MQTTService.swift文件，取消注释CocoaMQTT相关代码
echo -e "${YELLOW}正在配置MQTT服务...${NC}"

# 检查MQTTService.swift文件是否存在
if [ -f "WindRing/Core/Services/MQTTService.swift" ]; then
    # 取消注释CocoaMQTT导入
    sed -i '' 's/\/\/ import CocoaMQTT/import CocoaMQTT/g' WindRing/Core/Services/MQTTService.swift
    
    # 取消注释CocoaMQTT相关代码
    sed -i '' 's/\/\*\(.*CocoaMQTT.*\)\*\//\1/g' WindRing/Core/Services/MQTTService.swift
    
    echo -e "${GREEN}MQTT服务配置完成！${NC}"
else
    echo -e "${RED}未找到MQTTService.swift文件，请检查文件路径。${NC}"
    exit 1
fi

echo -e "${GREEN}MQTT集成完成！${NC}"
echo -e "${YELLOW}现在您可以使用MQTT功能了。${NC}" 