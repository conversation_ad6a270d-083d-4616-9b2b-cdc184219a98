import Foundation
import Combine

// 简单模拟API响应
let jsonString = """
{
  "code": 0,
  "data": {
    "score": 10,
    "steps": 8000,
    "calories": 350,
    "time": 3600,
    "distance": 6000
  },
  "msg": ""
}
"""

print("开始测试ActivityService修复...")
print("===========================\n")

print("模拟API响应:")
print(jsonString)
print("\n")

// 解析JSON
do {
    let data = jsonString.data(using: .utf8)!
    let json = try JSONSerialization.jsonObject(with: data, options: []) as! [String: Any]
    
    // 检查解析结果
    let code = json["code"] as? Int ?? -1
    let hasData = json["data"] != nil
    
    print("解析结果:")
    print("- code =", code)
    print("- hasData =", hasData)
    print("")
    
    // 模拟ActivityService判断逻辑
    if code == 0 && hasData {
        print("✅ 测试通过: code == 0 判断条件正确，数据会被正确处理")
    } else if code == 200 && hasData {
        print("❌ 测试失败: 仍然使用 code == 200 判断，这会导致错误")
    } else {
        print("⚠️ 测试结果不明确: 判断条件既不匹配 code == 0 也不匹配 code == 200")
    }
} catch {
    print("JSON解析错误:", error)
}

print("\n===========================")
print("测试完成") 