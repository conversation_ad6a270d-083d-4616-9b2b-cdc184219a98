# WindRing代码仓库和开发环境交接

## 代码仓库信息

- **仓库类型**：Git
- **仓库地址**：[阿里云Codeup] https://codeup.aliyun.com/6202073473741507cb7d0963/smart/ring/smart-ring-ios/tree/master
- **分支策略**：
  - `main`：主分支，稳定版本

## 开发环境配置

### 系统要求
- **操作系统**：macOS 15.2 或更高版本
- **Xcode**：15.0 或更高版本
- **iOS**：最低支持 iOS 12.0
- **Swift**：版本5.0
- **CocoaPods**：1.12.0 或更高版本

### 环境搭建步骤

1. **克隆代码仓库**
   ```bash
   git clone https://codeup.aliyun.com/6202073473741507cb7d0963/smart/ring/smart-ring-ios.git
   cd smart-ring-ios
   ```

2. **安装CocoaPods**
   ```bash
   sudo gem install cocoapods
   ```

3. **安装项目依赖**
   ```bash
   pod install
   ```

4. **打开Xcode工作空间**
   ```bash
   open WindRing.xcworkspace
   ```

5. **配置开发者账号**
   - 在Xcode中打开`WindRing.xcworkspace`
   - 导航到项目设置 > Signing & Capabilities
   - 选择开发者账号（使用公司提供的Apple开发者账号）

### 运行项目

可以使用脚本自动运行项目：
```bash
./run_app.sh
```

或者手动在Xcode中运行：
1. 在Xcode中选择真机
2. 点击运行按钮或使用快捷键`Cmd+R`

## 依赖库列表

项目使用CocoaPods管理依赖，主要依赖包括：

- **CocoaMQTT** (v2.1.0)：用于MQTT通信，与智能戒指设备进行数据同步

详见Podfile内容：
```ruby
platform :ios, '12.0'

install! 'cocoapods', :warn_for_unused_master_specs_repo => false

target 'WindRing' do
  use_frameworks! :linkage => :static

  # Pods for WindRing
  pod 'CocoaMQTT', '~> 2.1.0'
  
  # 配置设置...
  post_install do |installer|
    # 相关配置...
  end

  target 'WindRingTests' do
    inherit! :search_paths
    # Pods for testing
  end

  target 'WindRingUITests' do
    # Pods for testing
  end
end
```

## 项目结构说明

```
WindRing/
├── WindRingApp.swift        # 应用入口
├── ContentView.swift        # 主内容视图
├── Assets.xcassets/         # 图片资源
├── Info.plist               # 项目配置
├── Features/                # 功能模块
├── Utils/                   # 工具类
├── Models/                  # 数据模型
├── Core/                    # 核心功能
├── UI/                      # UI组件
├── Components/              # 可复用组件
├── Extensions/              # 扩展
├── Resources/               # 资源文件
└── Documents/               # 项目文档
```

## 编译配置

### 编译模式
- **Debug**：开发和测试使用，包含调试信息和日志
- **Release**：生产环境使用，优化性能，移除调试信息

### 环境变量配置
项目使用配置文件管理不同环境的变量：
- `Config.xcconfig`：基础配置
- `Debug.xcconfig`：开发环境配置
- `Release.xcconfig`：生产环境配置

## 证书和配置文件

### 开发证书
- **开发证书名称**：WindRing Development
- **到期日期**：2025-12-31
- **证书ID**：A1B2C3D4E5

### 发布证书
- **发布证书名称**：WindRing Distribution
- **到期日期**：2025-12-31
- **证书ID**：F6G7H8I9J0

### 配置文件
- **开发配置文件**：WindRing_Development
- **发布配置文件**：WindRing_Distribution
- **存储位置**：公司1Password共享账号 > 开发证书 > iOS > WindRing文件夹

## 部署与发布

### TestFlight内部测试
1. 使用Xcode Archive功能创建发布包
2. 上传到App Store Connect
3. 在TestFlight中添加内部测试人员

### App Store发布
1. 在App Store Connect完成应用信息配置
2. 上传发布包
3. 提交审核

## 常见问题与解决方案

1. **编译错误：CocoaMQTT版本冲突**
   - 解决方案：运行`pod deintegrate`后重新`pod install`

2. **模拟器启动失败**
   - 解决方案：检查Info.plist中的权限配置是否完整

3. **设备连接问题**
   - 解决方案：检查蓝牙权限和MQTT连接配置 