import Foundation

// MARK: - 注册请求模型
public struct RegisterRequest: Codable {
    let account: String // 手机号
    let password: String
    let confirmPassword: String
    let code: String // 验证码
    
    public func asDictionary() -> [String: Any] {
        return [
            "account": account,
            "password": password,
            "confirmPassword": confirmPassword,
            "code": code
        ]
    }
}

// MARK: - 注册响应模型
public struct RegisterResponse: Codable {
    public let code: Int
    public let data: TokenModel?
    public let msg: String?
}

// MARK: - 注册数据模型
public struct RegisterData: Codable {
    public let userId: Int
    public let accessToken: String
    public let refreshToken: String
    public let expiresTime: TimeInterval
    public let openid: String?
    
    // 自定义解码器，处理可能的类型不匹配问题
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 解码userId，支持Int或String类型
        if let intValue = try? container.decode(Int.self, forKey: .userId) {
            userId = intValue
        } else if let stringValue = try? container.decode(String.self, forKey: .userId),
                  let intValue = Int(stringValue) {
            userId = intValue
        } else {
            throw DecodingError.dataCorruptedError(
                forKey: .userId,
                in: container,
                debugDescription: "userId must be an integer or a string convertible to integer"
            )
        }
        
        // 解码其他字段
        accessToken = try container.decode(String.self, forKey: .accessToken)
        refreshToken = try container.decode(String.self, forKey: .refreshToken)
        
        // 解码expiresTime，支持TimeInterval或String类型
        if let timeInterval = try? container.decode(TimeInterval.self, forKey: .expiresTime) {
            expiresTime = timeInterval
        } else if let stringValue = try? container.decode(String.self, forKey: .expiresTime),
                  let doubleValue = Double(stringValue) {
            expiresTime = doubleValue
        } else {
            throw DecodingError.dataCorruptedError(
                forKey: .expiresTime,
                in: container,
                debugDescription: "expiresTime must be a number or a string convertible to number"
            )
        }
        
        // 解码可选的openid
        openid = try container.decodeIfPresent(String.self, forKey: .openid)
    }
    
    public enum CodingKeys: String, CodingKey {
        case userId
        case accessToken
        case refreshToken
        case expiresTime
        case openid
    }
}

// MARK: - 验证码请求模型
public struct VerificationCodeRequest: Codable {
    let mobile: String
    
    public func asDictionary() -> [String: Any] {
        return ["mobile": mobile]
    }
}

// MARK: - 验证码响应模型
public struct VerificationCodeResponse: Codable {
    public let code: Int
    public let data: Bool?
    public let msg: String?
} 
