//
//  APIResponse.swift
//  WindRing
//
//  Created by zx on 2025/5/16.
//
import Foundation
//struct APIResponse<T: Decodable>: Decodable {
//    let code: Int
//    let msg: String
//    let data: T?
//
//    enum CodingKeys: String, CodingKey {
//        case code
//        case message
//        case data
//    }
//}
public struct APIResponse<T: Codable>: Codable {
    let code: Int
    let msg: String
    let data: T

    enum CodingKeys: String, CodingKey {
        case code
        case msg
        case data
    }
} 
