import Foundation
import Combine

// Using existing types from the project
// UserProfile is already defined as SyncProfile in SyncService.swift
// HealthDataType is already defined as SyncDataType in SyncService.swift
// Device is already defined as SyncDeviceInfo in SyncService.swift
// HealthGoal is already defined in HealthAnalysisService.swift
public typealias UserAuthResponse = APIUserAuthResponse

// Networking response models
public struct ResetPasswordResponse: Codable {
    public let success: Bool
    public let message: String
}

// Additional response types needed by API Service
public struct VerifyResetPasswordResponse: Codable {
    public let success: Bool
    public let message: String
}

public struct HealthSummary: Codable {
    public let userId: String
    public let date: Date
    public let steps: Int
    public let calories: Int
    public let sleepHours: Double
    public let heartRateAvg: Int
}

public struct HealthSyncResponse: Codable {
    public let success: Bool
    public let message: String
}

public struct DeleteResponse: Codable {
    public let success: Bool
    public let message: String
}

// Health data point for API communication
public struct HealthDataPoint: Codable {
    public let id: String
    public let userId: String
    public let timestamp: Date
    public let value: Double
    public let deviceId: String?
    
    public init(id: String, userId: String, timestamp: Date, value: Double, deviceId: String? = nil) {
        self.id = id
        self.userId = userId
        self.timestamp = timestamp
        self.value = value
        self.deviceId = deviceId
    }
}

// Health data response from API
public struct HealthDataResponse: Codable {
    public let userId: String
    public let type: String // Using string instead of HealthDataType to avoid Codable issues
    public let data: [HealthDataPoint]
    
    public init(userId: String, type: String, data: [HealthDataPoint]) {
        self.userId = userId
        self.type = type
        self.data = data
    }
}

// Health data sync model for API
public struct HealthDataSync: Codable {
    public let userId: String
    public let type: String // Using string instead of HealthDataType to avoid Codable issues
    public let data: [HealthDataPoint]
    
    public init(userId: String, type: String, data: [HealthDataPoint]) {
        self.userId = userId
        self.type = type
        self.data = data
    }
}

// Health report models
public enum HealthReportType: String, Codable {
    case daily
    case weekly
    case monthly
    case custom
}

public enum HealthReportPeriod: String, Codable {
    case day
    case week
    case month
    case year
    case custom
}

public struct HealthReport: Codable {
    public let id: String
    public let userId: String
    public let type: HealthReportType
    public let period: HealthReportPeriod
    public let startDate: Date
    public let endDate: Date
    public let summary: String
    public let details: String
}

// Profile update request
public struct UserProfileUpdate: Codable {
    public let name: String?
    public let avatarUrl: String?
    public let gender: String?
    public let birthday: Date?
    public let height: Double?
    public let weight: Double?
    
    public init(name: String? = nil, avatarUrl: String? = nil, gender: String? = nil, birthday: Date? = nil, height: Double? = nil, weight: Double? = nil) {
        self.name = name
        self.avatarUrl = avatarUrl
        self.gender = gender
        self.birthday = birthday
        self.height = height
        self.weight = weight
    }
}

// Device request models
public struct DeviceAddRequest: Codable {
    public let name: String
    public let type: String
    public let model: String
    public let firmwareVersion: String?
    
    public init(name: String, type: String, model: String, firmwareVersion: String? = nil) {
        self.name = name
        self.type = type
        self.model = model
        self.firmwareVersion = firmwareVersion
    }
}

public struct DeviceUpdateRequest: Codable {
    public let name: String?
    public let firmwareVersion: String?
    public let settings: [String: String]?
    
    public init(name: String? = nil, firmwareVersion: String? = nil, settings: [String: String]? = nil) {
        self.name = name
        self.firmwareVersion = firmwareVersion
        self.settings = settings
    }
}

// Device response models
public struct DeviceListResponse: Codable {
    public let userId: String
    public let devices: [DeviceInfo]
    
    public init(userId: String, devices: [DeviceInfo]) {
        self.userId = userId
        self.devices = devices
    }
}

// Simple device info model for JSON serialization
public struct DeviceInfo: Codable {
    public let id: String
    public let name: String
    public let type: String
    public let model: String
    public let connected: Bool
    public let lastSync: Date?
    
    public init(id: String, name: String, type: String, model: String, connected: Bool, lastSync: Date?) {
        self.id = id
        self.name = name
        self.type = type
        self.model = model
        self.connected = connected
        self.lastSync = lastSync
    }
}

public struct DeviceResponse: Codable {
    public let success: Bool
    public let device: DeviceInfo
    
    public init(success: Bool, device: DeviceInfo) {
        self.success = success
        self.device = device
    }
}

// Content models
public struct ArticleListResponse: Codable {
    public let articles: [ArticleSummary]
    public let totalCount: Int
    public let page: Int
    public let pageSize: Int
}

public struct ArticleSummary: Codable {
    public let id: String
    public let title: String
    public let summary: String
    public let imageUrl: String?
    public let category: String
    public let publishDate: Date
}

public struct ArticleDetail: Codable {
    public let id: String
    public let title: String
    public let content: String
    public let imageUrl: String?
    public let category: String
    public let author: String
    public let publishDate: Date
    public let tags: [String]
}

public struct EventListResponse: Codable {
    public let events: [EventSummary]
    public let totalCount: Int
    public let page: Int
    public let pageSize: Int
}

public struct EventSummary: Codable {
    public let id: String
    public let title: String
    public let description: String
    public let startDate: Date
    public let endDate: Date
    public let location: String?
    public let imageUrl: String?
}

// API Error 
public struct APIError: Codable {
    public let code: Int
    public let message: String
    
    public init(code: Int, message: String) {
        self.code = code
        self.message = message
    }
} 