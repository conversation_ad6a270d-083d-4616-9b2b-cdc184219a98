import Foundation

// MARK: - /app-api/member/share/info/familyData
public struct FamilyMemberData: Codable, Identifiable {
    public let id: Int64
    let userId: Int64
    let nickname: String
    let avatar: String
    let name: String
    let sleepTime: Int?
    let activityScore: Int?
    let stressScore: Int?
    let vitalSings: Int? // Per user request, keeping 'vitalSings' typo
}

// MARK: - /app-api/member/share/info/myData
// Assuming this returns an array of users I share my data with.
public struct MySharedUserData: Codable, Identifiable {
    public let id: Int64
    let userId: Int64
    let nickname: String
    let avatar: String
    let name: String
    var sleep: Int      // 0: open, 1: close
    var activity: Int   // 0: open, 1: close
    var stress: Int     // 0: open, 1: close
    var healthStatus: Int // 0: open, 1: close
} 
