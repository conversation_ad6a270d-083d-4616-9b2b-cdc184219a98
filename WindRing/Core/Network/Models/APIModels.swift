import Foundation

// MARK: - 用户相关
public struct APIUserAuthResponse: Codable {
    public let userId: String
    public let token: String
    public let refreshToken: String
    public let expiresIn: Int
}

// MARK: - 健康数据类型
public typealias APIHealthDataType = SyncDataType

// MARK: - 用户配置文件
public struct APIUserProfile: Codable {
    public let id: String
    public let name: String
    public let email: String
    public let avatarUrl: String?
    public let gender: String?
    public let birthday: Date?
    public let height: Double?
    public let weight: Double?
    public let createdAt: Date
    public let updatedAt: Date
    public let language: String?
    public let unitSystem: String?
}

// MARK: - 健康目标
public struct APIHealthGoal: Codable {
    public let id: String
    public let userId: String
    public let type: String
    public let target: Double
    public let currentValue: Double?
    public let unit: String
    public let progress: Double?
    public let startDate: Date
    public let endDate: Date?
    public let isCompleted: Bool
    public let createdAt: Date
    public let updatedAt: Date
}

// MARK: - 设备
public typealias APIDevice = SyncDeviceInfo

/// 用户设置
public struct APIUserSettings: Codable {
    /// 数据单位（公制/英制）
    public let units: String
    /// 主题
    public let theme: String
    /// 通知设置
    public let notifications: APINotificationSettings
}

/// 通知设置
public struct APINotificationSettings: Codable {
    /// 是否启用通知
    public let enabled: Bool
    /// 每日总结通知
    public let dailySummary: Bool
    /// 目标提醒通知
    public let goalReminders: Bool
    /// 活动提醒通知
    public let activityReminders: Bool
}

/// 用户个人资料更新请求
public struct APIUserProfileUpdate: Codable {
    /// 用户名
    public let name: String?
    /// 头像URL
    public let avatarUrl: String?
    /// 性别
    public let gender: String?
    /// 生日
    public let birthday: Date?
    /// 身高（厘米）
    public let height: Double?
    /// 体重（千克）
    public let weight: Double?
}

/// 手机号登录请求
public struct MobileLoginRequest: Codable {
    /// 手机号
    public let mobile: String
    /// 密码
    public let password: String
    
    public init(mobile: String, password: String) {
        self.mobile = mobile
        self.password = password
    }
}

/// 登录响应
public struct LoginResponse: Codable {
    /// 返回码
    public let code: Int
    /// 返回消息
    public let msg: String?
    /// 返回数据
    public let data: TokenModel?
}

/// 登录数据
public struct LoginData: Codable {
    /// 用户ID
    public let userId: Int
    /// 访问令牌
    public let accessToken: String
    /// 刷新令牌
    public let refreshToken: String?
    /// 过期时间
    public let expiresTime: Double
    /// 第三方OpenID
    public let openid: String?
}



/// 短信验证码请求
public struct SMSCodeRequest: Codable {
    /// 手机号
    public let mobile: String
    /// 场景 (1=注册/登录 )发送场景,1 会员用户 - 手机号登陆 2 会员用户 - 修改手机 3 会员用户 - 修改密码 4 会员用户 - 忘记密码  5 会员用户 - 注册
    public let scene: Int
    ///中国的代号为 CN，美国为 US
    public let countryCode: String
    
    public init(mobile: String, scene: Int, countryCode: String) {
        self.mobile = mobile
        self.scene = scene
        self.countryCode = countryCode
    }
}

/// 短信验证码响应
public struct SMSCodeResponse: Codable {
    /// 返回码
    public let code: Int
    /// 返回消息
    public let msg: String?
    /// 返回数据
    public let data: Bool?
}

/// 短信登录请求
public struct SMSLoginRequest: Codable {
    /// 手机号
    public let mobile: String
    /// 验证码
    public let code: String
    
    public init(mobile: String, code: String) {
        self.mobile = mobile
        self.code = code
    }
}

/// 通用响应
public struct CommonResponse: Codable {
    /// 返回码
    public let code: Int
    /// 返回消息
    public let msg: String?
    /// 返回数据
    public let data: Bool?
}

/// 用户信息响应
public struct UserInfoResponse: Codable {
    /// 返回码
    public let code: Int
    /// 返回消息
    public let msg: String?
    /// 返回数据
    public let data: UserDetailInfo?
    
    // 自定义解码
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        code = try container.decode(Int.self, forKey: .code)
        msg = try container.decodeIfPresent(String.self, forKey: .msg)
        
        // 尝试解码data字段
        if let dataContainer = try? container.nestedContainer(keyedBy: UserDetailInfo.CodingKeys.self, forKey: .data) {
            // 创建UserDetailInfo实例
            let id = try dataContainer.decodeIfPresent(Int.self, forKey: .id)
            let nickname = try dataContainer.decodeIfPresent(String.self, forKey: .nickname)
            let mobile = try dataContainer.decodeIfPresent(String.self, forKey: .mobile)
            let email = try dataContainer.decodeIfPresent(String.self, forKey: .email)
            let avatar = try dataContainer.decodeIfPresent(String.self, forKey: .avatar)
            let sex = try dataContainer.decodeIfPresent(Int.self, forKey: .sex)
            let point = try dataContainer.decodeIfPresent(Int.self, forKey: .point)
            let experience = try dataContainer.decodeIfPresent(Int.self, forKey: .experience)
            let level = try dataContainer.decodeIfPresent(Int.self, forKey: .level)
            let birthday = try dataContainer.decodeIfPresent(Int.self, forKey: .birthday)
            let height = try dataContainer.decodeIfPresent(Double.self, forKey: .height)
            let heightType = try dataContainer.decodeIfPresent(Int.self, forKey: .heightType)
            let weight = try dataContainer.decodeIfPresent(Double.self, forKey: .weight)
            let weightType = try dataContainer.decodeIfPresent(Int.self, forKey: .weightType)
            let brokerageEnabled = try dataContainer.decodeIfPresent(Bool.self, forKey: .brokerageEnabled)
            
            data = UserDetailInfo(
                id: id,
                nickname: nickname,
                mobile: mobile,
                email: email,
                avatar: avatar,
                sex: sex,
                point: point,
                experience: experience,
                level: level,
                birthday: birthday,
                height: height,
                heightType: heightType,
                weight: weight,
                weightType: weightType,
                brokerageEnabled: brokerageEnabled
            )
        } else {
            data = nil
        }
    }
    
    // 编码
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(code, forKey: .code)
        try container.encodeIfPresent(msg, forKey: .msg)
        try container.encodeIfPresent(data, forKey: .data)
    }
    
    // 编码键
    private enum CodingKeys: String, CodingKey {
        case code, msg, data
    }
}

/// 用户详细信息
public struct UserDetailInfo: Codable {
    /// 用户ID
    public let id: Int?
    /// 用户昵称
    public let nickname: String?
    /// 手机号
    public let mobile: String?
    /// 邮箱
    public let email: String?
    /// 头像URL
    public let avatar: String?
    /// 性别（0-未知，1-男，2-女）
    public let sex: Int?
    /// 积分
    public let point: Int?
    /// 经验值
    public let experience: Int?
    /// 等级
    public let level: Int?
    /// 生日
    public let birthday: Int?
    /// 身高（厘米）
    public let height: Double?
    /// 身高类型
    public let heightType: Int?
    /// 体重（千克）
    public let weight: Double?
    /// 体重类型
    public let weightType: Int?
    /// 是否启用分销
    public let brokerageEnabled: Bool?
    
    // 初始化方法
    public init(id: Int?, nickname: String?, mobile: String?, email: String?, avatar: String?, sex: Int?, point: Int?, experience: Int?, level: Int?, birthday: Int?, height: Double?, heightType: Int?, weight: Double?, weightType: Int?, brokerageEnabled: Bool?) {
        self.id = id
        self.nickname = nickname
        self.mobile = mobile
        self.email = email
        self.avatar = avatar
        self.sex = sex
        self.point = point
        self.experience = experience
        self.level = level
        self.birthday = birthday
        self.height = height
        self.heightType = heightType
        self.weight = weight
        self.weightType = weightType
        self.brokerageEnabled = brokerageEnabled
    }
    
    // 编码键
    enum CodingKeys: String, CodingKey {
        case id, nickname, mobile, email, avatar, sex, point, experience, level, birthday, height, heightType, weight, weightType, brokerageEnabled
    }
}

/// 更新用户信息请求
public struct UpdateUserInfoRequest: Codable {
    /// 用户昵称
    public let nickname: String?
    /// 头像URL
    public let avatar: String?
    /// 性别（0-未知，1-男，2-女）
    public let sex: Int?
    /// 生日
    public let birthday: Int?
    /// 身高（厘米）
    public let height: Double?
    /// 身高类型
    public let heightType: Int?
    /// 体重（千克）
    public let weight: Double?
    /// 体重类型
    public let weightType: Int?
    
    public init(nickname: String? = nil, avatar: String? = nil, sex: Int? = nil, birthday: Int? = nil, height: Double? = nil, heightType: Int? = nil, weight: Double? = nil, weightType: Int? = nil) {
        self.nickname = nickname
        self.avatar = avatar
        self.sex = sex
        self.birthday = birthday
        self.height = height
        self.heightType = heightType
        self.weight = weight
        self.weightType = weightType
    }
}

// MARK: - 扩展
extension MobileLoginRequest {
    func asDictionary() -> [String: Any] {
        return [
            "mobile": mobile,
            "password": password
        ]
    }
}

extension SMSCodeRequest {
    func asDictionary() -> [String: Any] {
        return [
            "mobile": mobile,
            "scene": scene
        ]
    }
}

extension SMSLoginRequest {
    func asDictionary() -> [String: Any] {
        return [
            "mobile": mobile,
            "code": code
        ]
    }
}

extension UpdateUserInfoRequest {
    func asDictionary() -> [String: Any] {
        var dict: [String: Any] = [:]
        
        if let nickname = nickname {
            dict["nickname"] = nickname
        }
        
        if let avatar = avatar {
            dict["avatar"] = avatar
        }
        
        if let sex = sex {
            dict["sex"] = sex
        }
        
        if let birthday = birthday {
            dict["birthday"] = birthday
        }
        
        if let height = height {
            dict["height"] = height
        }
        
        if let heightType = heightType {
            dict["heightType"] = heightType
        }
        
        if let weight = weight {
            dict["weight"] = weight
        }
        
        if let weightType = weightType {
            dict["weightType"] = weightType
        }
        
        return dict
    }
} 
