import Foundation
import Combine
#if canImport(UIKit)
import UIKit
#elseif canImport(AppKit)
import AppKit
#endif

/// 分享信息确认请求模型
public struct ShareInfoConfirmRequest: Codable {
    public let uuid: String
    ///类型：0 主动分享 1 邀请分享
    public var type: Int
    ///type =1时，必填; 睡眠：0 开启 1 关闭
    public let sleep: Int?
    ///type =1时，必填; 活动：0 开启 1 关闭
    public let activity: Int?
    ///type =1时，必填; 压力：0 开启 1 关闭
    public let stress: Int?
    ///type =1时，必填; 心率：0 开启 1 关闭
    public let healthStatus: Int?
}

/// API服务协议
public protocol APIServiceProtocol {
    /// 用户相关API
    var user: UserAPIService { get }
    
    /// 健康数据相关API
    var health: HealthAPIService { get }
    
    /// 设备相关API
    var device: DeviceAPIService { get }
    
    /// 社区相关API
    var community: CommunityAPIService { get }
    
    /// 上传用户头像
    func uploadAvatar(image: UIKit.UIImage, completion: @escaping (Result<[String], Error>) -> Void)
}

/// API服务错误
public enum APIServiceError: Error {
    case invalidData
    case noData
    case invalidResponse
    case serverError(String)
}

/// API服务实现
public class APIService: APIServiceProtocol {
    // MARK: - 单例
    public static let shared = APIService()
    
    // MARK: - 服务属性
    public let user: UserAPIService
    public let health: HealthAPIService
    public let device: DeviceAPIService
    public let community: CommunityAPIService
    
    // MARK: - 网络管理器
    private let networkManager: NetworkManager
    
    // MARK: - API基础URL和认证令牌
//    private var baseURL: URL {
//        // 确保与SyncService中的serverURL一致
//        return URL(string: "https://yapi.weaving-park.com")!
//    }
    
    private var authToken: String? {
        return AuthService.shared.currentToken?.accessToken
    }
    
    // MARK: - 初始化方法
    private init(networkManager: NetworkManager = NetworkManager.shared) {
        self.networkManager = networkManager
        
        // 初始化各个服务
        self.user = UserAPIService(networkManager: networkManager)
        self.health = HealthAPIService(networkManager: networkManager)
        self.device = DeviceAPIService(networkManager: networkManager)
        self.community = CommunityAPIService(networkManager: networkManager)
    }
    
    /// 上传用户头像
    /// 上传用户头像
    public func uploadAvatar(image: UIKit.UIImage, completion: @escaping (Result<[String], Error>) -> Void) {
        guard let imageData = image.jpegData(compressionQuality: 0.7) else {
            completion(.failure(APIServiceError.invalidData))
            return
        }
        guard let url = URL(string: AppGlobals.apiBaseURL + "/app-api/infra/file/upload") else {
            completion(.failure(NetworkError.invalidURL))
            return
        }
//        let endpoint =
//        let url = AppGlobals.apiBaseURL.appendingPathComponent(endpoint)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        // 设置请求头
        request.setValue("1", forHTTPHeaderField: "tenant-id")
        if let token = authToken {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        // 生成唯一的文件名
        let fileName = "avatar_\(Date().timeIntervalSince1970).jpg"
        
        // 创建multipart/form-data请求体
//        let boundary = "Boundary-\(UUID().uuidString)"
//        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
//        var body = Data()
//        
//        // 添加文件数据
//        body.append("--\(boundary)\r\n".data(using: .utf8)!)
//        body.append("Content-Disposition: form-data; name=\"file\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
//        body.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
//        body.append(imageData)
//        body.append("\r\n".data(using: .utf8)!)
//        
//        // 结束标记
//        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
//        
//        request.httpBody = body
        
        
        let boundary = UUID().uuidString
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
//        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")

        var body = Data()

        // 1. 添加文本字段 path
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"path\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(fileName)\r\n".data(using: .utf8)!)

        // 2. 添加图片字段 file
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"file\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
        body.append(imageData)
        body.append("\r\n".data(using: .utf8)!)

        // 3. 结束
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)

        request.httpBody = body

        
        
        print("开始上传头像...")
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("头像上传失败: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                print("头像上传失败: 没有返回数据")
                completion(.failure(APIServiceError.noData))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    print("头像上传响应: \(json)")
                    
                    if let code = json["code"] as? Int {
                        if code == 0 {
                            if let data = json["data"] as? String {
                                print("头像上传成功: \(data)")
                                completion(.success([data]))
//                            } else if let data = json["data"] as? [String: Any], let url = data["url"] as? String {
//                                print("头像上传成功: \(url)")
//                                completion(.success(url))
                            } else {
                                print("头像上传失败: 无法解析URL")
                                completion(.failure(APIServiceError.invalidResponse))
                            }
                        } else {
                            let message = json["msg"] as? String ?? "未知错误"
                            print("头像上传失败: \(message)")
                            completion(.failure(APIServiceError.serverError(message)))
                        }
                    } else {
                        print("头像上传失败: 无法解析响应码")
                        completion(.failure(APIServiceError.invalidResponse))
                    }
                } else {
                    print("头像上传失败: 无法解析响应")
                    completion(.failure(APIServiceError.invalidResponse))
                }
            } catch {
                print("头像上传失败: \(error.localizedDescription)")
                completion(.failure(error))
            }
        }.resume()
    }
    
    /// 确认分享信息
    /// - Parameter requestBody: 分享信息确认请求体.
    /// - Returns: 发布者，发布分享确认响应
    public func confirmShareInfo(requestBody: [String:Any]) -> AnyPublisher<ShareInfoConfirmResponse, NetworkError> {
        return networkManager.request(
            endpoint: "/app-api/member/share/info/comfirm",
            method: .post,
            parameters: requestBody,
            responseType: ShareInfoConfirmResponse.self
        )
    }
    
    /// 确认分享信息 (异步)
    /// - Parameter requestBody: 分享信息确认请求体.
    /// - Returns: 分享确认响应
    @available(iOS 15.0, *)
    public func confirmShareInfo(requestBody: [String:Any]) async throws -> ShareInfoConfirmResponse {
        return try await networkManager.request(
            endpoint: "/app-api/member/share/info/comfirm",
            method: .post,
            parameters: requestBody,
            responseType: ShareInfoConfirmResponse.self
        )
    }

    @available(iOS 15.0, *)
    public func inviteOthersToShare() async throws -> String {
        return try await networkManager.request(
            endpoint: "/app-api/member/share/link/inviteOthersToShare",
            method: .post,
            responseType: String.self
        )
    }

    /// 获取家庭共享数据
    @available(iOS 15.0, *)
    public func getFamilyData() async throws -> [FamilyMemberData] {
        return try await networkManager.request(
            endpoint: "/app-api/member/share/info/familyData",
            method: .post,
            responseType: [FamilyMemberData].self
        )
    }

    /// 获取我的共享数据
    @available(iOS 15.0, *)
    public func getMyData() async throws -> [MySharedUserData] {
        return try await networkManager.request(
            endpoint: "/app-api/member/share/info/myData",
            method: .post,
            responseType: [MySharedUserData].self
        )
    }

    @available(iOS 15.0, *)
    public func updateShareInfo(id: Int, sleep: Int, activity: Int, stress: Int, healthStatus: Int) async throws -> Bool {
        let parameters: [String: Any] = [
            "id": id,
            "sleep": sleep,
            "activity": activity,
            "stress": stress,
            "healthStatus": healthStatus
        ]
        return try await networkManager.request(
            endpoint: "/app-api/member/share/info/update",
            method: .post,
            parameters: parameters,
            responseType: Bool.self
        )
    }

    @available(iOS 15.0, *)
    public func shareWithOthers(sleep: Int, activity: Int, stress: Int, healthStatus: Int) async throws -> String {
        let parameters: [String: Any] = [
            "sleep": sleep,
            "activity": activity,
            "stress": stress,
            "healthStatus": healthStatus
        ]
        return try await networkManager.request(
            endpoint: "/app-api/member/share/link/shareWithOthers",
            method: .post,
            parameters: parameters,
            responseType: String.self
        )
    }

//    @available(iOS 15.0, *)
    public func getGlossaryList()  ->  AnyPublisher<[String:[String:String]], NetworkError>  {
        return networkManager.request(
            endpoint: "/app-api/member/glossary/list",
            method: .get,
            parameters: [:],
            responseType: [String:[String:String]].self
        )
    }
}

// MARK: - 健康数据API服务
public class HealthAPIService {
    private let networkManager: NetworkManager
    private let endpoint = "/health"
    
    init(networkManager: NetworkManager) {
        self.networkManager = networkManager
    }
    
    /// 获取健康数据摘要
    /// - Parameter token: 授权令牌
    /// - Returns: 健康数据摘要
    public func getHealthSummary(token: String) -> AnyPublisher<HealthSummary, NetworkError> {
        return networkManager.request(
            endpoint: "\(endpoint)/summary",
            headers: networkManager.authHeaders(token: token),
            responseType: HealthSummary.self
        )
    }
    
    /// 获取特定类型的健康数据
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - type: 健康数据类型
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 健康数据列表
    public func getHealthData(
        token: String,
        type: HealthDataType,
        startDate: Date,
        endDate: Date
    ) -> AnyPublisher<HealthDataResponse, NetworkError> {
        let dateFormatter = ISO8601DateFormatter()
        let parameters: [String: Any] = [
            "type": type.rawValue,
            "startDate": dateFormatter.string(from: startDate),
            "endDate": dateFormatter.string(from: endDate)
        ]
        
        return networkManager.request(
            endpoint: "\(endpoint)/data",
            parameters: parameters,
            headers: networkManager.authHeaders(token: token),
            responseType: HealthDataResponse.self
        )
    }
    
    /// 获取睡眠心率详情数据（异步版本）
    /// - Parameter date: 日期字符串，格式为yyyy-MM-dd
    /// - Returns: 睡眠心率详情响应
    /// - Throws: NetworkError 如果请求失败
    public func getHrToSleepDetails(date: String) async throws -> HrSleepDetailsResponse {
        // 获取认证Token，如果Token不存在，直接抛出异常
//        guard let token = AuthService.shared.currentToken?.accessToken else {
//            throw NetworkError.unauthorized
//        }
        
        // 构建请求头
//        var headers = networkManager.authHeaders(token: token)
        //headers["tenant-id"] = "1" // 添加租户ID
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return try await networkManager.request(
            endpoint: "/app-api/iot/hr/getHrToSleepDetails", // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: HrSleepDetailsResponse.self
        )
    }
    
    /// 获取睡眠心率详情数据（Combine版本）
    /// - Parameter date: 日期字符串，格式为yyyy-MM-dd
    /// - Returns: 睡眠心率详情响应
    public func getHrToSleepDetailsCombine(date: String) -> AnyPublisher<HrSleepDetailsData, NetworkError> {
        // 获取认证Token，如果Token不存在，直接返回错误
//        guard let token = AuthService.shared.currentToken?.accessToken else {
//            return Fail(error: NetworkError.unauthorized).eraseToAnyPublisher()
//        }
        
        // 构建请求头
//        var headers = networkManager.authHeaders(token: token)
//        headers["tenant-id"] = "1" // 添加租户ID
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return networkManager.request(
            endpoint: "/app-api/iot/hr/getHrToSleepDetails", // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: HrSleepDetailsData.self
        )
    }
    
    /// 获取睡眠HRV详情数据（异步版本）
    /// - Parameter date: 日期字符串，格式为yyyy-MM-dd
    /// - Returns: 睡眠HRV详情响应
    /// - Throws: NetworkError 如果请求失败
    public func getHRVToSleepDetails(date: String) async throws -> HRVSleepDetailsResponse {
        // 获取认证Token，如果Token不存在，直接抛出异常
//        guard let token = AuthService.shared.currentToken?.accessToken else {
//            throw NetworkError.unauthorized
//        }
//        
        // 构建请求头
//        var headers = networkManager.authHeaders(token: token)
//        headers["tenant-id"] = "1" // 添加租户ID
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return try await networkManager.request(
            endpoint: "/app-api/iot/hrv/getHRVToSleepDetails", // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: HRVSleepDetailsResponse.self
        )
    }
    
    /// 获取睡眠HRV详情数据（Combine版本）
    /// - Parameter date: 日期字符串，格式为yyyy-MM-dd
    /// - Returns: 睡眠HRV详情响应
    public func getHRVToSleepDetailsCombine(date: String) -> AnyPublisher<HRVSleepDetailsData, NetworkError> {
        // 获取认证Token，如果Token不存在，直接返回错误
        guard let token = AuthService.shared.currentToken?.accessToken else {
            return Fail(error: NetworkError.unauthorized).eraseToAnyPublisher()
        }
        
        // 构建请求头
//        var headers = networkManager.authHeaders(token: token)
//        headers["tenant-id"] = "1" // 添加租户ID
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return networkManager.request(
            endpoint: "/app-api/iot/hrv/getHRVToSleepDetails", // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: HRVSleepDetailsData.self
        )
    }
    
    /// 获取睡眠体温数据（异步版本）
    /// - Parameter date: 日期字符串，格式为yyyy-MM-dd
    /// - Returns: 睡眠体温数据响应
    /// - Throws: NetworkError 如果请求失败
    public func getTemperatureToSleep(date: String) async throws -> TemperatureSleepResponse {
        // 获取认证Token，如果Token不存在，直接抛出异常
        guard let token = AuthService.shared.currentToken?.accessToken else {
            throw NetworkError.unauthorized
        }
        
        // 构建请求头
//        var headers = networkManager.authHeaders(token: token)
//        headers["tenant-id"] = "1" // 添加租户ID
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return try await networkManager.request(
            endpoint: "/app-api/iot/sleep/getTemperature", // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: TemperatureSleepResponse.self
        )
    }
    
    /// 获取睡眠体温数据（Combine版本）
    /// - Parameter date: 日期字符串，格式为yyyy-MM-dd
    /// - Returns: 睡眠体温数据响应
    public func getTemperatureToSleepCombine(date: String) -> AnyPublisher<TemperatureSleepResponse, NetworkError> {
        // 获取认证Token，如果Token不存在，直接返回错误
//        guard let token = AuthService.shared.currentToken?.accessToken else {
//            return Fail(error: NetworkError.unauthorized).eraseToAnyPublisher()
//        }
        
        // 构建请求头
//        var headers = networkManager.authHeaders(token: token)
//        headers["tenant-id"] = "1" // 添加租户ID
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return networkManager.request(
            endpoint: "/app-api/iot/sleep/getTemperature", // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: TemperatureSleepResponse.self
        )
    }
    
    /// 同步健康数据
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - data: 健康数据
    /// - Returns: 同步结果
    public func syncHealthData(token: String, data: [HealthDataSync]) -> AnyPublisher<HealthSyncResponse, NetworkError> {
        let parameters: [String: Any] = [
            "data": data.map { $0.asDictionary() }
        ]
        
        return networkManager.request(
            endpoint: "\(endpoint)/sync",
            method: .post,
            parameters: parameters,
            headers: networkManager.authHeaders(token: token),
            responseType: HealthSyncResponse.self
        )
    }
    
    /// 获取健康分析报告
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - type: 报告类型
    ///   - period: 时间段
    /// - Returns: 健康分析报告
    public func getHealthReport(
        token: String,
        type: HealthReportType,
        period: HealthReportPeriod
    ) -> AnyPublisher<HealthReport, NetworkError> {
        let parameters: [String: Any] = [
            "type": type.rawValue,
            "period": period.rawValue
        ]
        
        return networkManager.request(
            endpoint: "\(endpoint)/report",
            parameters: parameters,
            headers: networkManager.authHeaders(token: token),
            responseType: HealthReport.self
        )
    }
    
    /// 获取压力得分
    /// - Parameters:
    ///   - date: 日期字符串，格式为yyyy-MM-dd
    ///   - token: 授权令牌（可选）
    /// - Returns: 压力得分结果
    public func getStressScore(date: String, token: String? = nil) -> AnyPublisher<StressScoreResponse, NetworkError> {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return networkManager.request(
            endpoint: "/app-api/iot/stress/getScore",  // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: StressScoreResponse.self
        )
    }
    
    /// 获取生命体征状态
    /// - Parameters:
    ///   - date: 日期字符串，格式为yyyy-MM-dd
    ///   - token: 授权令牌（可选）
    /// - Returns: 生命体征状态结果
    @available(iOS 15.0, *)
    public func getVitalsStatus(date: String, token: String? = nil) async throws -> VitalsStatusResponse {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return try await networkManager.request(
            endpoint: "/app-api/iot/vital/getVitalsStatus",  // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: VitalsStatusResponse.self
        )
    }
    
    /// 获取生命体征状态（Combine版本）
    /// - Parameters:
    ///   - date: 日期字符串，格式为yyyy-MM-dd
    ///   - token: 授权令牌（可选）
    /// - Returns: 发布者，发布生命体征状态响应
    public func getVitalsStatus(date: String, token: String? = nil) -> AnyPublisher<VitalsStatusData, NetworkError> {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return networkManager.request(
            endpoint: "/app-api/iot/vital/getVitalsStatus",  // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: VitalsStatusData.self
        )
    }
    
    /// 获取心率详情数据
    /// - Parameters:
    ///   - date: 日期字符串，格式为yyyy-MM-dd
    ///   - token: 授权令牌（可选）
    /// - Returns: 心率详情数据结果
    @available(iOS 15.0, *)
    public func getHrToVitalDetails(date: String, token: String? = nil) async throws -> HrVitalDetailsData {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return try await networkManager.request(
            endpoint: "/app-api/iot/hr/getHrToVitalDetails",  // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: HrVitalDetailsData.self
        )
    }
    
    /// 获取心率详情数据（Combine版本）
    /// - Parameters:
    ///   - date: 日期字符串，格式为yyyy-MM-dd
    ///   - token: 授权令牌（可选）
    /// - Returns: 发布者，发布心率详情数据响应
    public func getHrToVitalDetails(date: String, token: String? = nil) -> AnyPublisher<HrVitalDetailsData, NetworkError> {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return networkManager.request(
            endpoint: "/app-api/iot/hr/getHrToVitalDetails",  // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: HrVitalDetailsData.self
        )
    }
    
    /// 获取血氧详情数据（Combine版本）
    /// - Parameters:
    ///   - date: 日期字符串，格式为yyyy-MM-dd
    ///   - token: 授权令牌（可选）
    /// - Returns: 发布者，发布血氧详情数据响应
    public func getSpO2ToVitalDetails(date: String, token: String? = nil) -> AnyPublisher<SpO2DetailsData, NetworkError> {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return networkManager.request(
            endpoint: "/app-api/iot/hrv/getHRVToVitalDetails",  // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: SpO2DetailsData.self
        )
    }
    
    /// 获取HRV详情数据（Combine版本）
    /// - Parameters:
    ///   - date: 日期字符串，格式为yyyy-MM-dd
    ///   - token: 授权令牌（可选）
    /// - Returns: 发布者，发布HRV详情数据响应
    func getHRVToVitalDetails(date: String, token: String? = nil) -> AnyPublisher<HRVDetailsData, NetworkError> {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return networkManager.request(
            endpoint: "/app-api/iot/hrv/getHRVToVitalDetails",  // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: HRVDetailsData.self
        )
    }
    
    /// 获取体温详情数据（Combine版本）
    /// - Parameters:
    ///   - date: 日期字符串，格式为yyyy-MM-dd
    ///   - token: 授权令牌（可选）
    /// - Returns: 发布者，发布体温详情数据响应
    func getTemperatureToVital(date: String, token: String? = nil) -> AnyPublisher<TemperatureDetailsData, NetworkError> {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return networkManager.request(
            endpoint: "/app-api/iot/sleep/getTemperatureToVital",  // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: TemperatureDetailsData.self
        )
    }
    
    /// 获取7天平均睡眠时间
    /// - Parameter token: 授权令牌（可选），如果不提供则使用默认值
    /// - Returns: 7天平均睡眠时间响应
    public func getHistory7AvgSleepTime(token: String? = nil) async throws -> Int {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        return try await networkManager.request(
            endpoint: "/app-api/iot/sleep/getHistory7AvgSleepTime",
            method: .get,
//            headers: headers,
            responseType: Int.self
        )
    }
    
    /// 获取7天平均压力分数
    /// - Parameter token: 授权令牌（可选），如果不提供则使用默认值
    /// - Returns: 7天平均压力分数响应
    public func getHistory7AvgStressScore(token: String? = nil) async throws -> Int {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        return try await networkManager.request(
            endpoint: "/app-api/iot/stress/getHistory7AvgScore",
            method: .get,
//            headers: headers,
            responseType: Int.self
        )
    }
    
    /// 获取7天平均心率
    /// - Parameter token: 授权令牌（可选），如果不提供则使用默认值
    /// - Returns: 7天平均心率响应
    public func getHistory7AvgHeartRate(token: String? = nil) async throws -> Int {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        return try await networkManager.request(
            endpoint: "/app-api/iot/hr/getHistory7AvgHearts",
            method: .get,
//            headers: headers,
            responseType: Int.self
        )
    }
    
    // MARK: - 异步/等待API
    
    /// 获取健康数据摘要（异步）
    /// - Parameter token: 授权令牌
    /// - Returns: 健康数据摘要
    @available(iOS 15.0, *)
    public func getHealthSummary(token: String) async throws -> HealthSummary {
        return try await networkManager.request(
            endpoint: "\(endpoint)/summary",
            headers: networkManager.authHeaders(token: token),
            responseType: HealthSummary.self
        )
    }
    
    /// 获取特定类型的健康数据（异步）
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - type: 健康数据类型
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 健康数据列表
    @available(iOS 15.0, *)
    public func getHealthData(
        token: String,
        type: HealthDataType,
        startDate: Date,
        endDate: Date
    ) async throws -> HealthDataResponse {
        let dateFormatter = ISO8601DateFormatter()
        let parameters: [String: Any] = [
            "type": type.rawValue,
            "startDate": dateFormatter.string(from: startDate),
            "endDate": dateFormatter.string(from: endDate)
        ]
        
        return try await networkManager.request(
            endpoint: "\(endpoint)/data",
            parameters: parameters,
            headers: networkManager.authHeaders(token: token),
            responseType: HealthDataResponse.self
        )
    }
    
    /// 获取压力得分 (异步版本)
    /// - Parameters:
    ///   - date: 日期字符串，格式为yyyy-MM-dd
    ///   - token: 授权令牌（可选）
    /// - Returns: 压力得分结果
    @available(iOS 15.0, *)
    public func getStressScore(date: String, token: String? = nil) async throws -> StressScoreResponse {
        // 设置请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else {
//            headers["Authorization"] = "Bearer test1"  // 使用默认值
//        }
        
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return try await networkManager.request(
            endpoint: "/app-api/iot/stress/getScore",  // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: StressScoreResponse.self
        )
    }
    
    /// 获取历史压力分数
    /// - Parameters:
    ///   - startDate: 开始日期字符串，格式为yyyy-MM-dd
    ///   - endDate: 结束日期字符串，格式为yyyy-MM-dd
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - token: 授权令牌（可选）
    /// - Returns: 压力历史得分响应
    /// - Throws: NetworkError 如果请求失败
    @available(iOS 15.0, *)
    public func getHistoryStressScore(
        startDate: String,
        endDate: String,
        type: Int,
        token: String? = nil
    ) async throws -> StressHistoryScoreData {
        print("健康服务 - 获取历史压力得分 - 日期范围: \(startDate) 到 \(endDate), 类型: \(type)")
        
        // 构建请求参数
//        let parameters: [String: Any] = [:]
        
        // 目前这是一个模拟实现，将来会替换为实际API调用
        // 模拟数据以测试UI的正确性
//        let mockData = createMockStressHistoryData(type: type, startDate: startDate, endDate: endDate)
        
        let parameters: [String: Any] = [
            "startDate": startDate,
            "endDate": endDate,
            "type": "\(type)"
        ]
        return try await networkManager.request(
            endpoint: "/app-api/iot/stress/getHistoryStress",  // 使用完整的API路径
            method: .get,
            parameters: parameters,
//            headers: headers,
            responseType: StressHistoryScoreData.self
        )
//         return try await networkManager.request(
//             endpoint: "/app-api/iot/stress/getHistoryStressScore",
//             method: .get,
//             queryParameters: [
//                 "startDate": startDate,
//                 "endDate": endDate,
//                 "type": "\(type)"
//             ],
//             headers: headers,
//             responseType: StressHistoryScoreResponse.self
//         )
        
        // 模拟网络延迟
//        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        
//        return mockData
    }
    
    /// 创建模拟压力历史数据（仅用于开发和测试）
    private func createMockStressHistoryData(type: Int, startDate: String, endDate: String) -> StressHistoryScoreResponse {
        // 根据type生成不同数量的记录
        var records: [StressHistoryRecord] = []
        var maxScore = 0
        var minScore = 100
        var totalScore = 0
        
        let count: Int
        switch type {
        case 1: // 周视图
            count = 7
        case 2: // 月视图
            count = 5 // 5周
        case 3: // 年视图
            count = 12 // 12个月
        default:
            count = 7
        }
        
        for i in 0..<count {
            // 生成范围在40-50之间的随机分数
            let score = Int.random(in: 40...50)
            
            // 更新最大值和最小值
            maxScore = max(maxScore, score)
            minScore = min(minScore, score)
            totalScore += score
            
            // 创建时间字符串
            var timeStr: String
            switch type {
            case 1: // 周视图 - 日期格式
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                if let startDateObj = dateFormatter.date(from: startDate) {
                    let day = Calendar.current.date(byAdding: .day, value: i, to: startDateObj)!
                    timeStr = dateFormatter.string(from: day)
                } else {
                    timeStr = "2025-03-\(10 + i)"
                }
            case 2: // 月视图 - 周格式
                timeStr = "W\(i + 1)"
            case 3: // 年视图 - 月份格式
                let months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
                timeStr = months[i]
            default:
                timeStr = "2025-03-\(10 + i)"
            }
            
            records.append(StressHistoryRecord(stress: score, time: timeStr))
        }
        
        // 计算平均分
        let avgScore = totalScore / count
        
        return StressHistoryScoreResponse(
            code: 0,
            data: StressHistoryScoreData(
                maxStress: maxScore,
                avgStress: avgScore,
                minStress: minScore,
                records: records
            ),
            msg: "success"
        )
    }
    
    /// 获取历史低压力时段数据
    /// - Parameters:
    ///   - startDate: 开始日期字符串，格式为yyyy-MM-dd
    ///   - endDate: 结束日期字符串，格式为yyyy-MM-dd
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - token: 授权令牌（可选）
    /// - Returns: 低压力时段历史数据响应
    /// - Throws: NetworkError 如果请求失败
    @available(iOS 15.0, *)
    public func getHistoryLowStressPeriod(
        startDate: String,
        endDate: String,
        type: Int,
        token: String? = nil
    ) async throws -> StressHistoryLowStressPeriodResponse {
        print("健康服务 - 获取历史低压力时段数据 - 日期范围: \(startDate) 到 \(endDate), 类型: \(type)")
        
        // 构建请求头
        var headers = [
            "tenant-id": "1"
        ]
        
        if let token = token {
            headers["Authorization"] = "Bearer \(token)"
        } else if let defaultToken = AuthService.shared.currentToken {
            headers["Authorization"] = "Bearer \(defaultToken)"
        } else {
            headers["Authorization"] = "Bearer test1" // 默认测试令牌
        }
        
        // 目前这是一个模拟实现，将来会替换为实际API调用
        // 模拟数据以测试UI的正确性
        let mockData = createMockLowStressPeriodData(type: type, startDate: startDate, endDate: endDate)
        
        // 当实际API准备好后，使用真实的网络请求替换以下代码
        // return try await networkManager.request(
        //     endpoint: "/app-api/iot/stress/getHistoryLowStressPeriod",
        //     method: .get,
        //     queryParameters: [
        //         "startDate": startDate,
        //         "endDate": endDate,
        //         "type": "\(type)"
        //     ],
        //     headers: headers,
        //     responseType: StressHistoryLowStressPeriodResponse.self
        // )
        
        // 模拟网络延迟
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        
        return mockData
    }
    
    /// 创建模拟低压力时段历史数据（仅用于开发和测试）
    private func createMockLowStressPeriodData(type: Int, startDate: String, endDate: String) -> StressHistoryLowStressPeriodResponse {
        // 根据type生成不同数量的记录
        var records: [LowStressPeriodRecord] = []
        var maxPeriod = 0
        var minPeriod = 1000
        var totalPeriod = 0
        
        let count: Int
        switch type {
        case 1: // 周视图
            count = 7
        case 2: // 月视图
            count = 5 // 5周
        case 3: // 年视图
            count = 12 // 12个月
        default:
            count = 7
        }
        
        for i in 0..<count {
            // 生成范围在180-330分钟之间的随机低压力时段（3-5.5小时）
            let period = Int.random(in: 180...330)
            
            // 更新最大值和最小值
            maxPeriod = max(maxPeriod, period)
            minPeriod = min(minPeriod, period)
            totalPeriod += period
            
            // 创建时间字符串
            var timeStr: String
            switch type {
            case 1: // 周视图 - 日期格式
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                if let startDateObj = dateFormatter.date(from: startDate) {
                    let day = Calendar.current.date(byAdding: .day, value: i, to: startDateObj)!
                    timeStr = dateFormatter.string(from: day)
                } else {
                    timeStr = "2025-03-\(10 + i)"
                }
            case 2: // 月视图 - 周格式
                timeStr = "W\(i + 1)"
            case 3: // 年视图 - 月份格式
                let months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
                timeStr = months[i]
            default:
                timeStr = "2025-03-\(10 + i)"
            }
            
            records.append(LowStressPeriodRecord(lowStressPeriod: period, time: timeStr))
        }
        
        // 计算平均值
        let avgPeriod = totalPeriod / count
        
        return StressHistoryLowStressPeriodResponse(
            code: 0,
            data: StressHistoryLowStressPeriodData(
                maxLowStressPeriod: maxPeriod,
                avgLowStressPeriod: avgPeriod,
                minLowStressPeriod: minPeriod,
                records: records
            ),
            msg: "success"
        )
    }
    
    /// 获取历史过度压力时段数据
    /// - Parameters:
    ///   - startDate: 开始日期字符串，格式为yyyy-MM-dd
    ///   - endDate: 结束日期字符串，格式为yyyy-MM-dd
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - token: 授权令牌（可选）
    /// - Returns: 过度压力时段历史数据响应
    /// - Throws: NetworkError 如果请求失败
    @available(iOS 15.0, *)
    public func getHistoryExcessive(
        startDate: String,
        endDate: String,
        type: Int,
        token: String? = nil
    ) async throws -> StressHistoryExcessiveResponse {
        print("健康服务 - 获取历史过度压力时段数据 - 日期范围: \(startDate) 到 \(endDate), 类型: \(type)")
        
        // 构建请求头
        var headers = [
            "tenant-id": "1"
        ]
        
        if let token = token {
            headers["Authorization"] = "Bearer \(token)"
        } else if let defaultToken = AuthService.shared.currentToken {
            headers["Authorization"] = "Bearer \(defaultToken)"
        } else {
            headers["Authorization"] = "Bearer test1" // 默认测试令牌
        }
        
        // 目前这是一个模拟实现，将来会替换为实际API调用
        // 模拟数据以测试UI的正确性
        let mockData = createMockExcessiveData(type: type, startDate: startDate, endDate: endDate)
        
        // 当实际API准备好后，使用真实的网络请求替换以下代码
        // return try await networkManager.request(
        //     endpoint: "/app-api/iot/stress/getHistoryExcessive",
        //     method: .get,
        //     queryParameters: [
        //         "startDate": startDate,
        //         "endDate": endDate,
        //         "type": "\(type)"
        //     ],
        //     headers: headers,
        //     responseType: StressHistoryExcessiveResponse.self
        // )
        
        // 模拟网络延迟
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        
        return mockData
    }
    
    /// 创建模拟过度压力时段历史数据（仅用于开发和测试）
    private func createMockExcessiveData(type: Int, startDate: String, endDate: String) -> StressHistoryExcessiveResponse {
        // 根据type生成不同数量的记录
        var records: [ExcessiveRecord] = []
        var maxExcessive = 0
        var minExcessive = 1000
        var totalExcessive = 0
        
        let count: Int
        switch type {
        case 1: // 周视图
            count = 7
        case 2: // 月视图
            count = 5 // 5周
        case 3: // 年视图
            count = 12 // 12个月
        default:
            count = 7
        }
        
        for i in 0..<count {
            // 生成范围在180-330分钟之间的随机过度压力时段（3-5.5小时）
            let excessive = Int.random(in: 180...330)
            
            // 更新最大值和最小值
            maxExcessive = max(maxExcessive, excessive)
            minExcessive = min(minExcessive, excessive)
            totalExcessive += excessive
            
            // 创建时间字符串
            var timeStr: String
            switch type {
            case 1: // 周视图 - 日期格式
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                if let startDateObj = dateFormatter.date(from: startDate) {
                    let day = Calendar.current.date(byAdding: .day, value: i, to: startDateObj)!
                    timeStr = dateFormatter.string(from: day)
                } else {
                    timeStr = "2025-03-\(10 + i)"
                }
            case 2: // 月视图 - 周格式
                timeStr = "W\(i + 1)"
            case 3: // 年视图 - 月份格式
                let months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
                timeStr = months[i]
            default:
                timeStr = "2025-03-\(10 + i)"
            }
            
            records.append(ExcessiveRecord(excessive: excessive, time: timeStr))
        }
        
        // 计算平均值
        let avgExcessive = totalExcessive / count
        
        return StressHistoryExcessiveResponse(
            code: 0,
            data: StressHistoryExcessiveData(
                maxExcessive: maxExcessive,
                avgExcessive: avgExcessive,
                minExcessive: minExcessive,
                records: records
            ),
            msg: "success"
        )
    }
    
    /// 获取历史夜间压力分数
    /// - Parameters:
    ///   - startDate: 开始日期 (YYYY-MM-DD)
    ///   - endDate: 结束日期 (YYYY-MM-DD)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - token: 授权令牌（可选）
    /// - Returns: 夜间压力分数历史数据响应
    /// - Throws: NetworkError 如果请求失败
    @available(iOS 15.0, *)
    public func getHistoryEvening(
        startDate: String,
        endDate: String,
        type: Int,
        token: String? = nil
    ) async throws -> StressHistoryScoreData {
        print("健康服务 - 获取历史夜间压力分数数据 - 日期范围: \(startDate) 到 \(endDate), 类型: \(type)")
        

        // 目前这是一个模拟实现，将来会替换为实际API调用
        // 模拟数据以测试UI的正确性
//        let mockData = createMockEveningData(type: type, startDate: startDate, endDate: endDate)
        let parameters: [String: Any] = [
            "startDate": startDate,
            "endDate": endDate,
            "type": "\(type)"
        ]
        // 当实际API准备好后，使用真实的网络请求替换以下代码
         return try await networkManager.request(
             endpoint: "/app-api/iot/stress/getHistoryEvening",
             method: .get,
             parameters: parameters,
//             headers: headers,
             responseType: StressHistoryScoreData.self
         )
        
        // 模拟网络延迟
//        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        
//        return mockData
    }
    
    /// 创建模拟夜间压力分数历史数据（仅用于开发和测试）
    private func createMockEveningData(type: Int, startDate: String, endDate: String) -> StressHistoryEveningResponse {
        // 根据type生成不同数量的记录
        var records: [EveningRecord] = []
        var maxEvening = 0
        var minEvening = 100
        var totalEvening = 0
        
        let count: Int
        switch type {
        case 1: // 周视图
            count = 7
        case 2: // 月视图
            count = 5 // 5周
        case 3: // 年视图
            count = 12 // 12个月
        default:
            count = 7
        }
        
        for i in 0..<count {
            // 生成范围在42-48之间的随机夜间压力分数
            let evening = Int.random(in: 42...48)
            
            // 更新最大值和最小值
            maxEvening = max(maxEvening, evening)
            minEvening = min(minEvening, evening)
            totalEvening += evening
            
            // 创建时间字符串
            var timeStr: String
            switch type {
            case 1: // 周视图 - 日期格式
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                if let startDateObj = dateFormatter.date(from: startDate) {
                    let day = Calendar.current.date(byAdding: .day, value: i, to: startDateObj)!
                    timeStr = dateFormatter.string(from: day)
                } else {
                    timeStr = "2025-03-\(10 + i)"
                }
            case 2: // 月视图 - 周格式
                timeStr = "W\(i + 1)"
            case 3: // 年视图 - 月份格式
                let months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
                timeStr = months[i]
            default:
                timeStr = "2025-03-\(10 + i)"
            }
            
            records.append(EveningRecord(evening: evening, time: timeStr))
        }
        
        // 计算平均值
        let avgEvening = totalEvening / count
        
        return StressHistoryEveningResponse(
            code: 0,
            data: StressHistoryEveningData(
                maxEvening: maxEvening,
                avgEvening: avgEvening,
                minEvening: minEvening,
                records: records
            ),
            msg: "success"
        )
    }
    
    /// 获取历史心率数据
    /// - Parameters:
    ///   - startDate: 开始日期 (YYYY-MM-DD)
    ///   - endDate: 结束日期 (YYYY-MM-DD)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - token: 授权令牌（可选）
    /// - Returns: 心率历史数据响应
    /// - Throws: NetworkError 如果请求失败
    @available(iOS 15.0, *)
    public func getHistoryVitalSignsHeartRate(
        startDate: String,
        endDate: String,
        type: Int,
        token: String? = nil
    ) async throws -> HeartRateHistoryData {
        print("健康服务 - 获取历史心率数据 - 日期范围: \(startDate) 到 \(endDate), 类型: \(type)")
        
        // 构建请求头
        var headers = [
            "tenant-id": "1"
        ]
        
        if let token = token {
            headers["Authorization"] = "Bearer \(token)"
        } else if let defaultToken = AuthService.shared.currentToken {
            headers["Authorization"] = "Bearer \(defaultToken)"
        }
        
        // 为了解决API错误问题，我们使用模拟数据
        // 在实际环境中应该删除注释使用真实API调用
        // 实际API调用
        return try await networkManager.request(
            endpoint: "/app-api/iot/hr/vitalSigns/getHistoryVitalSignsHeartRate",
            method: .get,
            parameters: [
                "startDate": startDate,
                "endDate": endDate,
                "type": "\(type)"
            ],
//            headers: headers,
            responseType: HeartRateHistoryData.self
        )

        
        // 使用模拟数据
//        let mockData = createMockHeartRateHistoryData(type: type, startDate: startDate, endDate: endDate)
//        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
//        return mockData
    }
    
    /// 创建模拟心率历史数据（仅用于开发和测试）
    private func createMockHeartRateHistoryData(type: Int, startDate: String, endDate: String) -> HeartRateHistoryResponse {
        // 根据type生成不同数量的记录
        var records: [HeartRateHistoryRecord] = []
        let maxHeartRate = 82
        let minHeartRate = 65
        let avgHeartRate = 75
        
        let count: Int
        switch type {
        case 1: // 周视图
            count = 7
        case 2: // 月视图
            count = 5 // 5周
        case 3: // 年视图
            count = 12 // 12个月
        default:
            count = 7
        }
        
        for i in 0..<count {
            // 生成范围在65-82之间的随机心率
            let heartRate = Int.random(in: 65...82)
            
            // 创建时间字符串
            var timeStr: String
            switch type {
            case 1: // 周视图 - 日期格式
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                if let startDateObj = dateFormatter.date(from: startDate) {
                    let day = Calendar.current.date(byAdding: .day, value: i, to: startDateObj)!
                    timeStr = dateFormatter.string(from: day)
                } else {
                    timeStr = "2025-03-\(10 + i)"
                }
            case 2: // 月视图 - 周格式
                timeStr = "W\(i + 1)"
            case 3: // 年视图 - 月份格式
                let months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
                timeStr = months[i]
            default:
                timeStr = "2025-03-\(10 + i)"
            }
            
            records.append(HeartRateHistoryRecord(
                heartRate: heartRate,
                time: timeStr,
                maxHeartRate: heartRate + Int.random(in: 0...3),
                minHeartRate: heartRate - Int.random(in: 0...3)
            ))
        }
        
        return HeartRateHistoryResponse(
            code: 0,
            data: HeartRateHistoryData(
                maxHeartRate: maxHeartRate,
                avgHeartRate: avgHeartRate,
                minHeartRate: minHeartRate,
                records: records
            ),
            msg: "success"
        )
    }
    
    /// 获取历史血氧数据
    /// - Parameters:
    ///   - startDate: 开始日期 (YYYY-MM-DD)
    ///   - endDate: 结束日期 (YYYY-MM-DD)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - token: 授权令牌（可选）
    /// - Returns: 血氧历史数据响应
    /// - Throws: NetworkError 如果请求失败
    @available(iOS 15.0, *)
    public func getHistoryVitalSignsSpO2(
        startDate: String,
        endDate: String,
        type: Int,
        token: String? = nil
    ) async throws -> SpO2HistoryData {
        print("健康服务 - 获取历史血氧数据 - 日期范围: \(startDate) 到 \(endDate), 类型: \(type)")
        
        // 构建请求头
        var headers = [
            "tenant-id": "1"
        ]
        
        if let token = token {
            headers["Authorization"] = "Bearer \(token)"
        } else if let defaultToken = AuthService.shared.currentToken {
            headers["Authorization"] = "Bearer \(defaultToken)"
        }
        
        // 为了解决API错误问题，我们使用模拟数据
        // 在实际环境中应该删除注释使用真实API调用

        // 实际API调用
        return try await networkManager.request(
            endpoint: "/app-api/iot/blood/vitalSigns/getHistoryVitalSignsSpO2",
            method: .get,
            parameters: [
                "startDate": startDate,
                "endDate": endDate,
                "type": "\(type)"
            ],
//            headers: headers,
            responseType: SpO2HistoryData.self
        )

        
        // 使用模拟数据
//        let mockData = createMockSpO2HistoryData(type: type, startDate: startDate, endDate: endDate)
//        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
//        return mockData
    }
    
    /// 创建模拟血氧历史数据（仅用于开发和测试）
    private func createMockSpO2HistoryData(type: Int, startDate: String, endDate: String) -> SpO2HistoryResponse {
        // 根据type生成不同数量的记录
        var records: [SpO2HistoryRecord] = []
        let maxSpO2 = 98
        let minSpO2 = 91
        let avgSpO2 = 96
        
        let count: Int
        switch type {
        case 1: // 周视图
            count = 7
        case 2: // 月视图
            count = 5 // 5周
        case 3: // 年视图
            count = 12 // 12个月
        default:
            count = 7
        }
        
        for i in 0..<count {
            // 生成范围在91-98之间的随机血氧值
            let spO2 = Int.random(in: 91...98)
            
            // 创建时间字符串
            var timeStr: String
            switch type {
            case 1: // 周视图 - 日期格式
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                if let startDateObj = dateFormatter.date(from: startDate) {
                    let day = Calendar.current.date(byAdding: .day, value: i, to: startDateObj)!
                    timeStr = dateFormatter.string(from: day)
                } else {
                    timeStr = "2025-03-\(10 + i)"
                }
            case 2: // 月视图 - 周格式
                timeStr = "W\(i + 1)"
            case 3: // 年视图 - 月份格式
                let months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
                timeStr = months[i]
            default:
                timeStr = "2025-03-\(10 + i)"
            }
            
            records.append(SpO2HistoryRecord(
                spO2: spO2,
                time: timeStr,
                maxSpO2: spO2 + Int.random(in: 0...2),
                minSpO2: spO2 - Int.random(in: 0...2)
            ))
        }
        
        return SpO2HistoryResponse(
            code: 0,
            data: SpO2HistoryData(
                maxSpO2: maxSpO2,
                avgSpO2: avgSpO2,
                minSpO2: minSpO2,
                records: records
            ),
            msg: "success"
        )
    }
    
    /// 获取历史HRV数据
    /// - Parameters:
    ///   - startDate: 开始日期 (YYYY-MM-DD)
    ///   - endDate: 结束日期 (YYYY-MM-DD)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - token: 授权令牌（可选）
    /// - Returns: HRV历史数据响应
    /// - Throws: NetworkError 如果请求失败
    @available(iOS 15.0, *)
    public func getHistoryVitalSignsHRV(
        startDate: String,
        endDate: String,
        type: Int,
        token: String? = nil
    ) async throws -> HRVHistoryData {
        print("健康服务 - 获取历史HRV数据 - 日期范围: \(startDate) 到 \(endDate), 类型: \(type)")
        
        // 构建请求头
        var headers = [
            "tenant-id": "1"
        ]
        
//        if let token = token {
//            headers["Authorization"] = "Bearer \(token)"
//        } else if let defaultToken = AuthService.shared.currentToken {
//            headers["Authorization"] = "Bearer \(defaultToken)"
//        }
        
        // 为了解决API错误问题，我们使用模拟数据
        // 在实际环境中应该删除注释使用真实API调用

        // 实际API调用
        return try await networkManager.request(
            endpoint: "/app-api/iot/hrv/vitalSigns/getHistoryVitalSignsHRV",
            method: .get,
            parameters: [
                "startDate": startDate,
                "endDate": endDate,
                "type": "\(type)"
            ],
//            headers: headers,
            responseType: HRVHistoryData.self
        )
   
        
//        // 使用模拟数据
//        let mockData = createMockHRVHistoryData(type: type, startDate: startDate, endDate: endDate)
//        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
//        return mockData
    }
    
    /// 创建模拟HRV历史数据（仅用于开发和测试）
    private func createMockHRVHistoryData(type: Int, startDate: String, endDate: String) -> HRVHistoryResponse {
        // 根据type生成不同数量的记录
        var records: [HRVHistoryRecord] = []
        let maxHRV = 92
        let minHRV = 17
        let avgHRV = 42
        
        let count: Int
        switch type {
        case 1: // 周视图
            count = 7
        case 2: // 月视图
            count = 5 // 5周
        case 3: // 年视图
            count = 12 // 12个月
        default:
            count = 7
        }
        
        for i in 0..<count {
            // 生成范围在17-92之间的随机HRV值
            let hrv = Int.random(in: 17...92)
            
            // 创建时间字符串
            var timeStr: String
            switch type {
            case 1: // 周视图 - 日期格式
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                if let startDateObj = dateFormatter.date(from: startDate) {
                    let day = Calendar.current.date(byAdding: .day, value: i, to: startDateObj)!
                    timeStr = dateFormatter.string(from: day)
                } else {
                    timeStr = "2025-03-\(10 + i)"
                }
            case 2: // 月视图 - 周格式
                timeStr = "W\(i + 1)"
            case 3: // 年视图 - 月份格式
                let months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
                timeStr = months[i]
            default:
                timeStr = "2025-03-\(10 + i)"
            }
            
            records.append(HRVHistoryRecord(
                hrv: hrv,
                time: timeStr,
                maxHRV: hrv + Int.random(in: 0...10),
                minHRV: hrv - Int.random(in: 0...10)
            ))
        }
        
        return HRVHistoryResponse(
            code: 0,
            data: HRVHistoryData(
                maxHRV: maxHRV,
                avgHRV: avgHRV,
                minHRV: minHRV,
                records: records
            ),
            msg: "success"
        )
    }
    
    /// 获取睡眠血氧详情数据（Combine版本）
    /// - Parameter date: 日期字符串，格式为yyyy-MM-dd
    /// - Returns: 睡眠心率详情响应
    public func getSpO2ToSleepDetailsCombine(date: String) -> AnyPublisher<SpO2SleepDetailsData, NetworkError> {
        // 构建查询参数
        let parameters: [String: Any] = ["date": date]
        
        return networkManager.request(
            endpoint: "/app-api/iot/blood/getSpO2ToSleepDetails", // 使用完整的API路径
            method: .get,
            parameters: parameters,
            responseType: SpO2SleepDetailsData.self
        )
    }
}

// MARK: - 设备API服务
public class DeviceAPIService {
    private let networkManager: NetworkManager
    private let endpoint = "/devices"
    
    init(networkManager: NetworkManager) {
        self.networkManager = networkManager
    }
    
    /// 获取用户设备列表
    /// - Parameter token: 授权令牌
    /// - Returns: 设备列表
    public func getUserDevices(token: String) -> AnyPublisher<DeviceListResponse, NetworkError> {
        return networkManager.request(
            endpoint: endpoint,
            headers: networkManager.authHeaders(token: token),
            responseType: DeviceListResponse.self
        )
    }
    
    /// 添加设备
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - device: 设备信息
    /// - Returns: 添加结果
    public func addDevice(token: String, device: DeviceAddRequest) -> AnyPublisher<DeviceResponse, NetworkError> {
        return networkManager.request(
            endpoint: endpoint,
            method: .post,
            parameters: device.asDictionary(),
            headers: networkManager.authHeaders(token: token),
            responseType: DeviceResponse.self
        )
    }
    
    /// 更新设备信息
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - deviceId: 设备ID
    ///   - device: 设备信息
    /// - Returns: 更新结果
    public func updateDevice(
        token: String,
        deviceId: String,
        device: DeviceUpdateRequest
    ) -> AnyPublisher<DeviceResponse, NetworkError> {
        return networkManager.request(
            endpoint: "\(endpoint)/\(deviceId)",
            method: .put,
            parameters: device.asDictionary(),
            headers: networkManager.authHeaders(token: token),
            responseType: DeviceResponse.self
        )
    }
    
    /// 删除设备
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - deviceId: 设备ID
    /// - Returns: 删除结果
    public func deleteDevice(token: String, deviceId: String) -> AnyPublisher<DeleteResponse, NetworkError> {
        return networkManager.request(
            endpoint: "\(endpoint)/\(deviceId)",
            method: .delete,
            headers: networkManager.authHeaders(token: token),
            responseType: DeleteResponse.self
        )
    }
    
    // MARK: - 异步/等待API
    
    /// 获取用户设备列表（异步）
    /// - Parameter token: 授权令牌
    /// - Returns: 设备列表
    @available(iOS 15.0, *)
    public func getUserDevices(token: String) async throws -> DeviceListResponse {
        return try await networkManager.request(
            endpoint: endpoint,
            headers: networkManager.authHeaders(token: token),
            responseType: DeviceListResponse.self
        )
    }
}

// MARK: - 社区API服务
public class CommunityAPIService {
    private let networkManager: NetworkManager
    private let endpoint = "/community"
    
    init(networkManager: NetworkManager) {
        self.networkManager = networkManager
    }
    
    /// 获取社区文章列表
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - page: 页码
    ///   - limit: 每页数量
    /// - Returns: 文章列表
    public func getArticles(
        token: String,
        page: Int = 1,
        limit: Int = 20
    ) -> AnyPublisher<ArticleListResponse, NetworkError> {
        let parameters: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        return networkManager.request(
            endpoint: "\(endpoint)/articles",
            parameters: parameters,
            headers: networkManager.authHeaders(token: token),
            responseType: ArticleListResponse.self
        )
    }
    
    /// 获取文章详情
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - articleId: 文章ID
    /// - Returns: 文章详情
    public func getArticleDetail(token: String, articleId: String) -> AnyPublisher<ArticleDetail, NetworkError> {
        return networkManager.request(
            endpoint: "\(endpoint)/articles/\(articleId)",
            headers: networkManager.authHeaders(token: token),
            responseType: ArticleDetail.self
        )
    }
    
    /// 获取社区活动列表
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - page: 页码
    ///   - limit: 每页数量
    /// - Returns: 活动列表
    public func getEvents(
        token: String,
        page: Int = 1,
        limit: Int = 20
    ) -> AnyPublisher<EventListResponse, NetworkError> {
        let parameters: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        return networkManager.request(
            endpoint: "\(endpoint)/events",
            parameters: parameters,
            headers: networkManager.authHeaders(token: token),
            responseType: EventListResponse.self
        )
    }
    
    // MARK: - 异步/等待API
    
    /// 获取社区文章列表（异步）
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - page: 页码
    ///   - limit: 每页数量
    /// - Returns: 文章列表
    @available(iOS 15.0, *)
    public func getArticles(
        token: String,
        page: Int = 1,
        limit: Int = 20
    ) async throws -> ArticleListResponse {
        let parameters: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        return try await networkManager.request(
            endpoint: "\(endpoint)/articles",
            parameters: parameters,
            headers: networkManager.authHeaders(token: token),
            responseType: ArticleListResponse.self
        )
    }
}

// MARK: - 模型扩展
extension Encodable {
    /// 将Encodable对象转换为字典
    func asDictionary() -> [String: Any] {
        guard let data = try? JSONEncoder().encode(self) else { return [:] }
        guard let dictionary = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return [:]
        }
        return dictionary
    }
}

// MARK: - 响应模型
/// 令牌刷新响应
public struct TokenResponse: Codable {
    /// 访问令牌
    public let token: String
    
    /// 刷新令牌
    public let refreshToken: String
    
    /// 令牌有效期（秒）
    public let expiresIn: Int
    
    private enum CodingKeys: String, CodingKey {
        case token = "accessToken"
        case refreshToken
        case expiresIn
    }
} 

/// 分享信息确认响应
public struct ShareInfoConfirmResponse: Codable {
    public let code: Int
    public let msg: String
    public let data: Bool?
}
