import Foundation
import Combine

/// HTTP请求方法
public enum HTTPMethod: String {
    case get = "GET"
    case post = "POST"
    case put = "PUT"
    case delete = "DELETE"
    case patch = "PATCH"
}

/// 网络管理器
public class NetworkManager {
    // MARK: - 单例
    public static let shared = NetworkManager()
    
    // MARK: - 属性
    private let session: URLSession
    private let baseURL: String
    private let defaultTimeout: TimeInterval
    
    // MARK: - 初始化方法
    private init(
        baseURL: String = AppGlobals.apiBaseURL,
        timeout: TimeInterval = 30.0
    ) {
        self.baseURL = baseURL
        self.defaultTimeout = timeout
        
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = timeout
        configuration.timeoutIntervalForResource = timeout
        self.session = URLSession(configuration: configuration)
    }
    
    // MARK: - 公共方法
    
    /// 发送网络请求
    /// - Parameters:
    ///   - endpoint: 端点
    ///   - method: HTTP方法
    ///   - parameters: 请求参数
    ///   - headers: 请求头
    ///   - responseType: 响应类型
    /// - Returns: 响应数据
    public func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .get,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        responseType: T.Type
    ) -> AnyPublisher<T, NetworkError> {
        guard let url = URL(string: baseURL + endpoint) else {
            return Fail(error: NetworkError.invalidURL).eraseToAnyPublisher()
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        
        // 添加默认请求头
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("application/json", forHTTPHeaderField: "Accept")
        let selectedLanguage = LanguageManager.shared.selectedLanguage
        if selectedLanguage == "zh-Hans" {
            request.addValue("zh-CN", forHTTPHeaderField: "accept-language")
        }else{
            request.addValue("en-US", forHTTPHeaderField: "accept-language")
        }
        
        // 添加认证令牌（如果存在）
        if let token = AuthService.shared.currentToken?.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        // 添加自定义请求头
        headers?.forEach { request.addValue($1, forHTTPHeaderField: $0) }
        if let headers = request.allHTTPHeaderFields {
            debugPrint("🔸 URLRequest Headers:")
            for (key, value) in headers {
                debugPrint("\(key): \(value)")
            }
        } else {
            debugPrint("⚠️ URLRequest has no headers.")
        }
//        request.cachePolicy = .reloadIgnoringLocalCacheData
        
        // 处理请求参数
//        if let parameters = parameters {
//            do {
//                request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
//            } catch {
//                return Fail(error: NetworkError.encodingFailed).eraseToAnyPublisher()
//            }
//        }
        if let parameters = parameters {
            if method == .get {
                // 拼接 URL 查询参数
                guard var components = URLComponents(string: baseURL) else {
                    return Fail(error: NetworkError.invalidURL).eraseToAnyPublisher()
                }
                components.path = endpoint // endpoint 应该以 "/" 开头
                components.queryItems = parameters.map { URLQueryItem(name: $0.key, value: "\($0.value)") }

                guard let newURL = components.url else {
                    return Fail(error: NetworkError.invalidURL).eraseToAnyPublisher()
                }
                request.url = newURL
            } else {
                // 非 GET 请求使用 httpBody
                do {
                    request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
                } catch {
                    return Fail(error: NetworkError.encodingFailed).eraseToAnyPublisher()
                }
            }
        }
        // 放在这里！
        debugPrint("🟦 URLRequest Info")
        debugPrint("🔹 Method: \(request.httpMethod ?? "N/A")")
        debugPrint("🔹 URL: \(request.url?.absoluteString ?? "Invalid URL")")

        if let headers = request.allHTTPHeaderFields {
            debugPrint("🔹 Headers:")
            for (key, value) in headers {
                debugPrint("   • \(key): \(value)")
            }
        }

        if let body = request.httpBody,
           let bodyString = String(data: body, encoding: .utf8) {
            debugPrint("🔹 Body: \(bodyString)")
        }
        
        return session.dataTaskPublisher(for: request)
            .tryMap { data, response in
                try self.handleResponse(data: data, response: response)
            }
            .mapError { error in
                if let networkError = error as? NetworkError {
                    return networkError
                }
                print("NetworkManager - 网络错误: \(error.localizedDescription)")
                return NetworkError.networkError
            }
            .receive(on: RunLoop.main)
            .eraseToAnyPublisher()
    }
    func decodeResponse<T: Decodable>(data: Data, type: T.Type) throws -> APIResponse<T> {
        let decoder = CleanJSONDecoder()
        return try decoder.decode(APIResponse<T>.self, from: data)
    }
    /// 发送请求并返回原始数据
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - method: HTTP方法
    ///   - parameters: 请求参数
    ///   - headers: 请求头
    /// - Returns: 包含原始数据的Publisher
    public func requestData(
        endpoint: String,
        method: HTTPMethod = .get,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) -> AnyPublisher<Data, NetworkError> {
        guard let url = URL(string: baseURL + endpoint) else {
            return Fail(error: NetworkError.invalidURL).eraseToAnyPublisher()
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        
        // 添加默认请求头
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("application/json", forHTTPHeaderField: "Accept")
        let selectedLanguage = LanguageManager.shared.selectedLanguage
        if selectedLanguage == "zh-Hans" {
            request.addValue("zh_CN", forHTTPHeaderField: "accept-language")
        }else{
            request.addValue("en_US", forHTTPHeaderField: "accept-language")
        }
        
        // 添加认证令牌（如果存在）
        if let token = AuthService.shared.currentToken?.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        // 添加自定义请求头
        headers?.forEach { request.addValue($1, forHTTPHeaderField: $0) }
        if let headers = request.allHTTPHeaderFields {
            debugPrint("🔸 URLRequest Headers:")
            for (key, value) in headers {
                debugPrint("\(key): \(value)")
            }
        } else {
            debugPrint("⚠️ URLRequest has no headers.")
        }
        
        // 添加请求参数
        if let parameters = parameters {
            switch method {
            case .get:
                // 对于GET请求，将参数添加到URL中
                var components = URLComponents(url: url, resolvingAgainstBaseURL: true)!
                components.queryItems = parameters.map { key, value in
                    URLQueryItem(name: key, value: "\(value)")
                }
                request.url = components.url
            default:
                // 对于其他请求，将参数添加到请求体中
                do {
                    request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
                } catch {
                    return Fail(error: NetworkError.encodingFailed).eraseToAnyPublisher()
                }
            }
        }
        
        // 放在这里！
        debugPrint("🟦 URLRequest Info")
        debugPrint("🔹 Method: \(request.httpMethod ?? "N/A")")
        debugPrint("🔹 URL: \(request.url?.absoluteString ?? "Invalid URL")")

        if let headers = request.allHTTPHeaderFields {
            debugPrint("🔹 Headers:")
            for (key, value) in headers {
                debugPrint("   • \(key): \(value)")
            }
        }

        if let body = request.httpBody,
           let bodyString = String(data: body, encoding: .utf8) {
            debugPrint("🔹 Body: \(bodyString)")
        }
        
        return session.dataTaskPublisher(for: request)
            .tryMap { data, response in
                try self.handleDataResponse(data: data, response: response)
            }
            .mapError { error -> NetworkError in
                if let networkError = error as? NetworkError {
                    return networkError
                }
                
                let nsError = error as NSError
                if nsError.domain == NSURLErrorDomain {
                    switch nsError.code {
                    case NSURLErrorNotConnectedToInternet:
                        return NetworkError.networkError
                    case NSURLErrorTimedOut:
                        return NetworkError.networkError
                    default:
                        return NetworkError.networkError
                    }
                }
                
                return NetworkError.unknownError
            }
            .receive(on: RunLoop.main)
            .eraseToAnyPublisher()
    }
    
    // MARK: - 异步/等待API
    
    /// 使用async/await发送请求并返回解码后的数据
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - method: HTTP方法
    ///   - parameters: 请求参数
    ///   - headers: 请求头
    ///   - responseType: 响应数据类型
    /// - Returns: 解码后的数据
    @available(iOS 15.0, *)
    public func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .get,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        responseType: T.Type
    ) async throws -> T {
        guard let url = URL(string: baseURL + endpoint) else {
            throw NetworkError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        
        // 添加默认请求头
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("application/json", forHTTPHeaderField: "Accept")
        let selectedLanguage = LanguageManager.shared.selectedLanguage
        if selectedLanguage == "zh-Hans" {
            request.addValue("zh_CN", forHTTPHeaderField: "accept-language")
        }else{
            request.addValue("en_US", forHTTPHeaderField: "accept-language")
        }
        
        // 添加认证令牌（如果存在）
        if let token = AuthService.shared.currentToken?.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        // 添加自定义请求头
        headers?.forEach { request.addValue($1, forHTTPHeaderField: $0) }
        if let headers = request.allHTTPHeaderFields {
            debugPrint("🔸 URLRequest Headers:")
            for (key, value) in headers {
                debugPrint("\(key): \(value)")
            }
        } else {
            debugPrint("⚠️ URLRequest has no headers.")
        }
        
        // 添加请求参数
        if let parameters = parameters {
            switch method {
            case .get:
                // 对于GET请求，将参数添加到URL中
                var components = URLComponents(url: url, resolvingAgainstBaseURL: true)!
                components.queryItems = parameters.map { key, value in
                    URLQueryItem(name: key, value: "\(value)")
                }
                request.url = components.url
            default:
                // 对于其他请求，将参数添加到请求体中
                do {
                    request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
                } catch {
                    throw NetworkError.encodingFailed
                }
            }
        }
        if let dict = parameters {
            for (key, value) in dict {
                debugPrint("\(key): \(value)")
            }
        } else {
            print("❗️dict 是 nil")
        }
        
        do {
            let (data, response) = try await session.data(for: request)
            return try self.handleResponse(data: data, response: response)
        } catch {
            if let networkError = error as? NetworkError {
                throw networkError
            }
            
            let nsError = error as NSError
            if nsError.domain == NSURLErrorDomain {
                switch nsError.code {
                case NSURLErrorNotConnectedToInternet:
                    throw NetworkError.networkError
                case NSURLErrorTimedOut:
                    throw NetworkError.networkError
                default:
                    throw NetworkError.networkError
                }
            }
            
            throw NetworkError.unknownError
        }
    }
    
    /// 使用async/await发送请求并返回原始数据
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - method: HTTP方法
    ///   - parameters: 请求参数
    ///   - headers: 请求头
    /// - Returns: 原始数据
    @available(iOS 15.0, *)
    public func requestData(
        endpoint: String,
        method: HTTPMethod = .get,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> Data {
        guard let url = URL(string: baseURL + endpoint) else {
            throw NetworkError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        
        // 添加默认请求头
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("application/json", forHTTPHeaderField: "Accept")
        let selectedLanguage = LanguageManager.shared.selectedLanguage
        if selectedLanguage == "zh-Hans" {
            request.addValue("zh_CN", forHTTPHeaderField: "accept-language")
        }else{
            request.addValue("en_US", forHTTPHeaderField: "accept-language")
        }
        
        // 添加认证令牌（如果存在）
        if let token = AuthService.shared.currentToken?.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        // 添加自定义请求头
        headers?.forEach { request.addValue($1, forHTTPHeaderField: $0) }
        if let headers = request.allHTTPHeaderFields {
            debugPrint("🔸 URLRequest Headers:")
            for (key, value) in headers {
                debugPrint("\(key): \(value)")
            }
        } else {
            debugPrint("⚠️ URLRequest has no headers.")
        }
        
        // 添加请求参数
        if let parameters = parameters {
            switch method {
            case .get:
                // 对于GET请求，将参数添加到URL中
                var components = URLComponents(url: url, resolvingAgainstBaseURL: true)!
                components.queryItems = parameters.map { key, value in
                    URLQueryItem(name: key, value: "\(value)")
                }
                request.url = components.url
            default:
                // 对于其他请求，将参数添加到请求体中
                do {
                    request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
                } catch {
                    throw NetworkError.encodingFailed
                }
            }
        }
        
        do {
            let (data, response) = try await session.data(for: request)
            return try self.handleDataResponse(data: data, response: response)
        } catch {
            if let networkError = error as? NetworkError {
                throw networkError
            }
            
            let nsError = error as NSError
            if nsError.domain == NSURLErrorDomain {
                switch nsError.code {
                case NSURLErrorNotConnectedToInternet:
                    throw NetworkError.networkError
                case NSURLErrorTimedOut:
                    throw NetworkError.networkError
                default:
                    throw NetworkError.networkError
                }
            }
            
            throw NetworkError.unknownError
        }
    }

    // MARK: - 私有辅助方法
    
    /// 处理标准API响应并解码
    private func 		handleResponse<T: Codable>(data: Data, response: URLResponse) throws -> T {
        guard let httpResponse = response as? HTTPURLResponse else {
            print("NetworkManager - 无效的HTTP响应")
            throw NetworkError.invalidResponse
        }
        
        print("NetworkManager - 收到响应: \(httpResponse.statusCode)")
        
        if httpResponse.statusCode == 401 {
            print("NetworkManager - 未授权: \(httpResponse.statusCode)")
            AuthService.shared.logout { _ in }
            throw NetworkError.httpError(statusCode: httpResponse.statusCode)
        }
        
        if !(200...299).contains(httpResponse.statusCode) {
            print("NetworkManager - HTTP错误: \(httpResponse.statusCode)")
            throw NetworkError.httpError(statusCode: httpResponse.statusCode)
        }
        
        if let jsonString = String(data: data, encoding: .utf8) {
            debugPrint("NetworkManager - 响应数据: \(jsonString)")
        }
        
        do {
            let decoder = CleanJSONDecoder()
            let apiResponse = try decoder.decode(APIResponse<T>.self, from: data)
            
            if apiResponse.code == 401 {
                print("NetworkManager - 未授权: \(httpResponse.statusCode)")
                AuthService.shared.logout { _ in }
                throw NetworkError.httpError(statusCode: httpResponse.statusCode)
            }
            
            if apiResponse.code != 0 {
                print("NetworkManager - API错误: \(apiResponse.code), 消息: \(apiResponse.msg)")
                throw NetworkError.apiError(code: apiResponse.code, message: apiResponse.msg)
            }
            
            return apiResponse.data
        } catch let decodingError as DecodingError {
            print("NetworkManager - 解码失败: \(decodingError)")
            throw NetworkError.decodingError
        } catch {
            throw error // 重新抛出其他错误，例如 serverError
        }
    }
    
    /// 处理原始数据响应
    private func handleDataResponse(data: Data, response: URLResponse) throws -> Data {
        guard let httpResponse = response as? HTTPURLResponse else {
            print("NetworkManager - 无效的HTTP响应")
            throw NetworkError.invalidResponse
        }
        
        print("NetworkManager - 收到响应: \(httpResponse.statusCode)")
        
        if httpResponse.statusCode == 401 {
            print("NetworkManager - 未授权: \(httpResponse.statusCode)")
            AuthService.shared.logout { _ in }
            throw NetworkError.httpError(statusCode: httpResponse.statusCode)
        }
        
        if !(200...299).contains(httpResponse.statusCode) {
            print("NetworkManager - HTTP错误: \(httpResponse.statusCode)")
            throw NetworkError.httpError(statusCode: httpResponse.statusCode)
        }
        if let jsonString = String(data: data, encoding: .utf8) {
            debugPrint("NetworkManager - 响应数据: \(jsonString)")
        }
        do {
            let jsonObject = try JSONSerialization.jsonObject(with: data, options: [])
            if let dictionary = jsonObject as? [String: Any] {
                print("✅ 转换成功：\(dictionary)")
                let code = dictionary["code"] as? Int
                let msg = dictionary["msg"] as? String
                if code == 401 {
                    print("🔒 未授权或 Token 过期")
                    AuthService.shared.logout { _ in }
                    throw NetworkError.httpError(statusCode: httpResponse.statusCode)
                }
                if code != 0 {
                    print("NetworkManager - API错误: \(String(describing: code)), 消息: \(String(describing: msg))")
                    throw NetworkError.apiError(code: code ?? 0, message: msg ?? "")
                }
            } else {
                print("⚠️ JSON 不是一个字典格式")
            }
        } catch {
            print("❌ 转换失败：\(error)")
            throw NetworkError.httpError(statusCode: httpResponse.statusCode)
            
        }
        
        // 对于原始数据请求，我们只检查HTTP状态码，然后直接返回数据
        return data
    }
}

// MARK: - 辅助扩展
extension NetworkManager {
    /// 添加授权令牌到请求头
    /// - Parameter token: 授权令牌
    /// - Returns: 包含授权令牌的请求头
    public func authHeaders(token: String) -> [String: String] {
        return ["Authorization": "Bearer \(token)"]
    }
}

// MARK: - 模拟API响应（用于开发和测试）
extension NetworkManager {
    /// 模拟API响应
    /// - Parameters:
    ///   - data: 模拟数据
    ///   - delay: 延迟时间（秒）
    /// - Returns: 包含模拟数据的Publisher
    public static func mockResponse<T: Decodable>(
        _ data: T,
        delay: TimeInterval = 0.5
    ) -> AnyPublisher<T, NetworkError> {
        return Just(data)
            .delay(for: .seconds(delay), scheduler: RunLoop.main)
            .setFailureType(to: NetworkError.self)
            .eraseToAnyPublisher()
    }
    
    /// 模拟API错误
    /// - Parameters:
    ///   - error: 模拟错误
    ///   - delay: 延迟时间（秒）
    /// - Returns: 包含模拟错误的Publisher
    public static func mockError(
        _ error: NetworkError = .unknownError,
        delay: TimeInterval = 0.5
    ) -> AnyPublisher<Never, NetworkError> {
        return Fail(error: error)
            .delay(for: .seconds(delay), scheduler: RunLoop.main)
            .eraseToAnyPublisher()
    }
} 
