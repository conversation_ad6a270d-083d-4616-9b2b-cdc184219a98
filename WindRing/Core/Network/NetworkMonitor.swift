//
//  NetworkMonitor.swift
//  WindRing
//
//  Created by 1234 on 2025/3/25.
//

import Foundation
import Network

/// 网络状态监控类
class NetworkMonitor {
    // 单例模式
    static let shared = NetworkMonitor()
    
    // 当前网络是否连接
    private(set) var isConnected: Bool = false
    
    // 当前连接类型
    private(set) var connectionType: ConnectionType = .unknown
    
    // 网络路径监控器
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    // 私有初始化方法
    private init() {
        startMonitoring()
    }
    
    /// 开始监控网络状态
    func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            guard let self = self else { return }
            
            // 更新连接状态
            let newConnectionState = path.status == .satisfied
            let previousState = self.isConnected
            self.isConnected = newConnectionState
            
            // 检测连接类型
            self.connectionType = self.checkConnectionType(path)
            
            // 如果状态发生变化，发送通知
            if previousState != newConnectionState {
                DispatchQueue.main.async {
                    NotificationCenter.default.post(
                        name: .networkStatusChanged,
                        object: nil,
                        userInfo: [
                            "connected": newConnectionState,
                            "connectionType": self.connectionType.rawValue
                        ]
                    )
                    
                    print("网络状态变化: \(newConnectionState ? "connected".localized : "已断开"), 类型: \(self.connectionType.rawValue)")
                }
            }
        }
        
        // 启动监控
        monitor.start(queue: queue)
    }
    
    /// 停止监控网络状态
    func stopMonitoring() {
        monitor.cancel()
    }
    
    /// 检查连接类型
    private func checkConnectionType(_ path: NWPath) -> ConnectionType {
        if path.usesInterfaceType(.wifi) {
            return .wifi
        } else if path.usesInterfaceType(.cellular) {
            return .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            return .ethernet
        } else {
            return .unknown
        }
    }
    
    /// 网络连接类型枚举
    enum ConnectionType: String {
        case wifi = "WiFi"
        case cellular = "蜂窝数据"
        case ethernet = "有线网络"
        case unknown = "未知"
    }
} 
