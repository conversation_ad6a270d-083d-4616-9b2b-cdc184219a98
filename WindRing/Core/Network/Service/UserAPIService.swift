//
//  UserAPIService.swift
//  WindRing
//
//  Created by zx on 2025/5/16.
//
import Foundation
import Combine
#if canImport(UIKit)
import UIKit
#elseif canImport(AppKit)
import AppKit
#endif
// MARK: - Goal Models
public struct MemberGoal: Codable {
    let sleepTime: Int
    let steps: Int
    let calories: Int
    let activityDuration: Int
}


// MARK: - Request Models
public struct RegisterWithProfileRequest {
    let account: String
    let password: String
    let confirmPassword: String
    let code: String
    let nickname: String
    let avatar: String
    let sex: Int
    let birthday: String
    let height: Int
    let heightType: Int
    let weight: Int
    let weightType: Int

    func asDictionary() -> [String: Any] {
        return [
            "account": account,
            "password": password,
            "confirmPassword": confirmPassword,
            "code": code,
            "nickname": nickname,
            "avatar": avatar,
            "sex": sex,
            "birthday": birthday,
            "height": height,
            "heightType": heightType,
            "weight": weight,
            "weightType": weightType
        ]
    }
}

public struct EmailCodeRequest {
    var email: String
    var scene: EmailSceneEnum
    var code: String = ""

    func asDictionary() -> [String: Any] {
        var dict = ["email": email, "scene": scene.rawValue] as [String : Any]
        if code.count != 0{
            dict["code"] = code
        }
        return dict
    }
}

public enum EmailSceneEnum: Int, CustomStringConvertible {
    /// 会员用户 - 邮箱登录
    case login = 1
    /// 会员用户 - 邮箱注册
    case register = 2
    /// 会员用户 - 修改邮箱
    case updateEmail = 3
    /// 会员用户 - 修改密码
    case updatePassword = 4
    /// 会员用户 - 忘记账号密码
    case forgotCredentials = 5
    /// 会员用户 - 注销
    case deleteAccount = 6

    public var description: String {
        switch self {
        case .login:
            return "会员用户 - 邮箱登录"
        case .register:
            return "会员用户 - 邮箱注册"
        case .updateEmail:
            return "会员用户 - 修改邮箱"
        case .updatePassword:
            return "会员用户 - 修改密码"
        case .forgotCredentials:
            return "会员用户 - 忘记账号密码"
        case .deleteAccount:
            return "会员用户 - 注销"
        }
    }
}

public enum SmsSceneEnum: Int, CustomStringConvertible {
    /// 会员用户 - 手机号登陆
    case login = 1
    /// 会员用户 - 修改手机
    case updateMobile = 2
    /// 会员用户 - 修改密码
    case updatePassword = 3
    /// 会员用户 - 忘记密码
    case forgotPassword = 4
    /// 会员用户 - 手机号注册
    case register = 5
    /// 会员用户 - 注销
    case deleteAccount = 6

    public var description: String {
        switch self {
        case .login: return "会员用户 - 手机号登陆"
        case .updateMobile: return "会员用户 - 修改手机"
        case .updatePassword: return "会员用户 - 修改密码"
        case .forgotPassword: return "会员用户 - 忘记密码"
        case .register: return "会员用户 - 手机号注册"
        case .deleteAccount: return "会员用户 - 注销"
        }
    }
}

public struct ValidateSmsCodeRequest {
    let mobile: String
    let scene: SmsSceneEnum
    let code: String

    func asDictionary() -> [String: Any] {
        return ["mobile": mobile, "scene": scene.rawValue, "code": code]
    }
}

// MARK: - 用户API服务
public class UserAPIService {
    
    private let networkManager: NetworkManager
    private let endpoint = "/users"
    
    init(networkManager: NetworkManager) {
        self.networkManager = networkManager
    }
    
    /// 用户登录
    /// - Parameters:
    ///   - email: 邮箱
    ///   - password: 密码
    /// - Returns: 登录结果
    public func login(email: String, password: String) -> AnyPublisher<TokenModel, NetworkError> {
        let parameters: [String: Any] = [
            "email": email,
            "password": password
        ]
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/email-login",
            method: .post,
            parameters: parameters,
//            headers: ["tenant-id": "1"],
            responseType: TokenModel.self
        )
    }
    /// 用户登录 验证码登录
    /// - Parameters:
    ///   - email: 邮箱
    ///   - password: 密码
    /// - Returns: 登录结果
    public func smslogin(mobile: String, code: String) -> AnyPublisher<TokenModel , NetworkError> {
        let parameters: [String: Any] = [
            "mobile": mobile,
            "code": code
        ]
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/sms-login",//"\(endpoint)/sms-login",//
            method: .post,
            parameters: parameters,
//            headers: ["tenant-id": "1"],
            responseType: TokenModel.self
        )

        .mapError { error in
            if let networkError = error as? NetworkError {
                print("APIService - 处理网络错误 - \(networkError.localizedDescription)")
                return networkError
            }
            print("APIService - 未知错误 - \(error.localizedDescription)")
            return NetworkError.unknownError
        }
        .eraseToAnyPublisher()
    
    }
    /// 用户注册
    /// - Parameters:
    ///   - email: 邮箱
    ///   - password: 密码
    ///   - name: 用户名
    /// - Returns: 注册结果
    public func register(email: String, password: String, name: String) -> AnyPublisher<TokenModel, NetworkError> {
        let parameters: [String: Any] = [
            "email": email,
            "password": password,
            "name": name
        ]
        
        return networkManager.request(
            endpoint: "\(endpoint)/register",
            method: .post,
            parameters: parameters,
            responseType: TokenModel.self
        )
    }
    
    /// 获取用户信息
    /// - Parameter token: 授权令牌
    /// - Returns: 用户信息
    public func getUserProfile(token: String) -> AnyPublisher<UserProfile, NetworkError> {
        return networkManager.request(
            endpoint: "\(endpoint)/profile",
            headers: networkManager.authHeaders(token: token),
            responseType: UserProfile.self
        )
    }
    
    /// 手机号登录
    /// - Parameters:
    ///   - mobile: 手机号
    ///   - password: 密码
    /// - Returns: 登录结果
    public func loginWithMobile(mobile: String, password: String) -> AnyPublisher<TokenModel, NetworkError> {
        print("APIService - 构建手机登录请求 - 端点: /app-api/member/auth/login")
        let request = MobileLoginRequest(mobile: mobile, password: password)
        
        // 打印请求参数（安全考虑不打印密码）
        let requestParams = request.asDictionary()
        var safeParams = [String: Any]()
        for (key, value) in requestParams {
            if key == "password" {
                safeParams[key] = "******"
            } else {
                safeParams[key] = value
            }
        }
        print("APIService - 请求参数: \(safeParams)")
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/login",
            method: .post,
            parameters: request.asDictionary(),
            //headers: ["tenant-id": "1"],
            responseType: TokenModel.self
        )
        .tryMap { response in
//            print("APIService - 收到登录响应 - 状态码: \(response.code)")
//            
//            guard response.code == 0 else {
//                print("APIService - 登录失败 - 消息: \(response.message ?? "无错误信息")")
//                throw NetworkError.serverError(statusCode: response.code, message: response.message ?? "未知错误")
//            }
            
//            guard let data = response.data else {
//                print("APIService - 响应数据为空")
//                throw NetworkError.serverError(statusCode: 500, message: "无效的响应数据")
//            }
            
            print("APIService - 登录成功 - 用户ID: \(response.userId)")
            return response
        }
        .mapError { error in
            if let networkError = error as? NetworkError {
                print("APIService - 处理网络错误 - \(networkError.localizedDescription)")
                return networkError
            }
            print("APIService - 未知错误 - \(error.localizedDescription)")
            return NetworkError.unknownError
        }
        .eraseToAnyPublisher()
    }
    
    /// 手机号登录
    /// - Parameters:
    ///   - mobile: 手机号
    ///   - password: 密码
    /// - Returns: 登录结果
    public func loginWithMobileSMS(mobile: String, password: String) -> AnyPublisher<TokenModel, NetworkError> {
        print("APIService - 构建手机登录请求 - 端点: /app-api/member/auth/login")
        let request = MobileLoginRequest(mobile: mobile, password: password)
        
        // 打印请求参数（安全考虑不打印密码）
        let requestParams = request.asDictionary()
        var safeParams = [String: Any]()
        for (key, value) in requestParams {
            if key == "password" {
                safeParams[key] = "******"
            } else {
                safeParams[key] = value
            }
        }
        print("APIService - 请求参数: \(safeParams)")
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/login",
            method: .post,
            parameters: request.asDictionary(),
//            headers: ["tenant-id": "1"],
            responseType: LoginResponse.self
        )
        .tryMap { response in
            print("APIService - 收到登录响应 - 状态码: \(response.code)")
            
            guard response.code == 0 else {
                print("APIService - 登录失败 - 消息: \(response.msg ?? "无错误信息")")
                throw NetworkError.serverError(statusCode: response.code, message: response.msg ?? "未知错误")
            }
            
            guard let data = response.data else {
                print("APIService - 响应数据为空")
                throw NetworkError.serverError(statusCode: 500, message: "无效的响应数据")
            }
            
            print("APIService - 登录成功 - 用户ID: \(data.userId)")
            return data
        }
        .mapError { error in
            if let networkError = error as? NetworkError {
                print("APIService - 处理网络错误 - \(networkError.localizedDescription)")
                return networkError
            }
            print("APIService - 未知错误 - \(error.localizedDescription)")
            return NetworkError.unknownError
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新用户信息
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - profile: 用户信息
    /// - Returns: 更新结果
    public func updateUserProfile(token: String, profile: UserProfileUpdate) -> AnyPublisher<UserProfile, NetworkError> {
        return networkManager.request(
            endpoint: "\(endpoint)/profile",
            method: .put,
            parameters: profile.asDictionary(),
            headers: networkManager.authHeaders(token: token),
            responseType: UserProfile.self
        )
    }
    
    /// 重置密码
    /// - Parameter email: 邮箱
    /// - Returns: 重置结果
    public func resetPassword(email: String) -> AnyPublisher<ResetPasswordResponse, NetworkError> {
        let parameters: [String: Any] = ["email": email]
        
        return networkManager.request(
            endpoint: "\(endpoint)/reset-password",
            method: .post,
            parameters: parameters,
            responseType: ResetPasswordResponse.self
        )
    }
    
    /// 验证重置密码令牌
    /// - Parameters:
    ///   - token: 重置密码令牌
    ///   - newPassword: 新密码
    /// - Returns: 验证结果
    public func verifyResetPassword(token: String, newPassword: String) -> AnyPublisher<VerifyResetPasswordResponse, NetworkError> {
        let parameters: [String: Any] = [
            "token": token,
            "newPassword": newPassword
        ]
        
        return networkManager.request(
            endpoint: "\(endpoint)/verify-reset-password",
            method: .post,
            parameters: parameters,
            responseType: VerifyResetPasswordResponse.self
        )
    }
    
    /// APP用户注册
    /// - Parameters:
    ///   - account: 手机号
    ///   - password: 密码
    ///   - confirmPassword: 确认密码
    ///   - code: 验证码
    /// - Returns: 注册结果
    public func registerApp(account: String, password: String, confirmPassword: String, code: String) -> AnyPublisher<RegisterResponse, NetworkError> {
        print("APIService - 构建APP注册请求 - 端点: /app-api/member/auth/register")
        let request = RegisterRequest(
            account: account,
            password: password,
            confirmPassword: confirmPassword,
            code: code
        )
        
        // 打印请求参数（安全考虑不打印密码）
        let requestParams = request.asDictionary()
        var safeParams = [String: Any]()
        for (key, value) in requestParams {
            if key == "password" || key == "confirmPassword" {
                safeParams[key] = "******"
            } else {
                safeParams[key] = value
            }
        }
        print("APIService - 注册请求参数: \(safeParams)")
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/register",
            method: .post,
            parameters: requestParams,
//            headers: ["tenant-id": "1"],
            responseType: RegisterResponse.self
        )
    }
    
    /// APP用户注册 (带个人信息)
    public func registerWithProfile(request: RegisterWithProfileRequest) -> AnyPublisher<RegisterResponse, NetworkError> {
        print("APIService - 构建带个人信息的APP注册请求 - 端点: /app-api/member/auth/register")
        
        let requestParams = request.asDictionary()
        
        // Log params safely
        var safeParams = requestParams
        safeParams["password"] = "******"
        safeParams["confirmPassword"] = "******"
        print("APIService - 注册请求参数: \(safeParams)")
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/register",
            method: .post,
            parameters: requestParams,
            responseType: RegisterResponse.self
        )
    }
    
    /// 获取手机验证码
    /// - Parameter mobile: 手机号
    /// - Returns: 验证码发送结果
    public func getVerificationCode(mobile: String) -> AnyPublisher<VerificationCodeResponse, NetworkError> {
        print("APIService - 获取验证码请求 - 端点: /app-api/member/auth/sms")
        let request = VerificationCodeRequest(mobile: mobile)
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/sms",
            method: .post,
            parameters: request.asDictionary(),
//            headers: ["tenant-id": "1"],
            responseType: VerificationCodeResponse.self
        )
    }
    
    public func getVerificationEmailCode(mobile: String) -> AnyPublisher<VerificationCodeResponse, NetworkError> {
        print("APIService - 获取邮箱验证码请求 - 端点: /app-api/member/auth/send-email-code")
        let request = VerificationCodeRequest(mobile: mobile)
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/send-email-code",
            method: .post,
            parameters: request.asDictionary(),
//            headers: ["tenant-id": "1"],
            responseType: VerificationCodeResponse.self
        )
    }
    
    public func sendEmailCode(email: String, scene: EmailSceneEnum) -> AnyPublisher<SMSCodeResponse, NetworkError> {
        print("APIService - 发送邮件验证码请求 - 端点: /app-api/member/auth/send-email-code")
        let request = EmailCodeRequest(email: email, scene: scene)
        
        print("APIService - 请求参数: \(request.asDictionary())")
        
        return NetworkManager.shared.request(
            endpoint: "/app-api/member/auth/send-email-code",
            method: .post,
            parameters: request.asDictionary(),
            responseType: SMSCodeResponse.self
        )
        .tryMap { response in
            print("APIService - 收到邮件验证码响应 - 状态码: \(response.code)")
            
            guard response.code == 0 else {
                print("APIService - 发送验证码失败 - 消息: \(response.msg ?? "无错误信息")")
                throw NetworkError.serverError(statusCode: response.code, message: response.msg ?? "未知错误")
            }
            
            print("APIService - 验证码发送成功")
            return response
        }
        .mapError { error in
            if let networkError = error as? NetworkError {
                print("APIService - 处理网络错误 - \(networkError.localizedDescription)")
                return networkError
            }
            print("APIService - 未知错误 - \(error.localizedDescription)")
            return NetworkError.unknownError
        }
        .eraseToAnyPublisher()
    }
    
    
    
    /// 发送短信验证码
    /// - Parameter mobile: 手机号
    /// - Returns: 验证码发送结果
    /// 发送场景,1 会员用户 - 手机号登陆 2 会员用户 - 修改手机 3 会员用户 - 修改密码 4 会员用户 - 忘记密码  5 会员用户 - 注册
    public func sendSMSCode(mobile: String,scene:Int,countryCode:String) -> AnyPublisher<SMSCodeResponse, NetworkError> {
        var country = "CN"
        if countryCode == "+1"{
            country = "US"
        }
        print("APIService - 发送短信验证码请求 - 端点: /app-api/member/auth/send-sms-code")
//        let request = SMSCodeRequest(mobile: mobile, scene: scene, countryCode: country)
        let dict = ["mobile": mobile,
                    "scene": scene,
                    "countryCode":country] as [String : Any]
        // 打印请求参数
        print("APIService - 请求参数: \(dict)")
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/send-sms-code",
            method: .post,
            parameters: dict,
//            headers: ["tenant-id": "1"],
            responseType: SMSCodeResponse.self
        )
        .tryMap { response in
            print("APIService - 收到短信验证码响应 - 状态码: \(response.code)")
            
            guard response.code == 0 else {
                print("APIService - 发送验证码失败 - 消息: \(response.msg ?? "无错误信息")")
                throw NetworkError.serverError(statusCode: response.code, message: response.msg ?? "未知错误")
            }
            
//            print("APIService - 验证码发送成功")
            "login_code_sent".localized.showToast()
            return response
        }
        .mapError { error in
            if let networkError = error as? NetworkError {
                print("APIService - 处理网络错误 - \(networkError.localizedDescription)")
                return networkError
            }
            print("APIService - 未知错误 - \(error.localizedDescription)")
            return NetworkError.unknownError
        }
        .eraseToAnyPublisher()
    }
    
    /// 短信验证码登录
    /// - Parameters:
    ///   - mobile: 手机号
    ///   - code: 验证码
    /// - Returns: 登录结果
    public func loginWithSMSCode(mobile: String, code: String) -> AnyPublisher<TokenModel, NetworkError> {
        print("APIService - 构建短信登录请求 - 端点: /app-api/member/auth/login-by-sms")
        let request = SMSLoginRequest(mobile: mobile, code: code)
        
        // 打印请求参数
        var safeParams = request.asDictionary()
        print("APIService - 请求参数: \(safeParams)")
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/login-by-sms",
            method: .post,
            parameters: safeParams,
//            headers: ["tenant-id": "1"],
            responseType: LoginResponse.self
        )
        .tryMap { response in
            print("APIService - 收到登录响应 - 状态码: \(response.code)")
            
            guard response.code == 0 else {
                print("APIService - 登录失败 - 消息: \(response.msg ?? "无错误信息")")
                throw NetworkError.serverError(statusCode: response.code, message: response.msg ?? "未知错误")
            }
            
            guard let data = response.data else {
                print("APIService - 响应数据为空")
                throw NetworkError.serverError(statusCode: 500, message: "无效的响应数据")
            }
            
            print("APIService - 登录成功 - 用户ID: \(data.userId)")
            return data
        }
        .mapError { error in
            if let networkError = error as? NetworkError {
                print("APIService - 处理网络错误 - \(networkError.localizedDescription)")
                return networkError
            }
            print("APIService - 未知错误 - \(error.localizedDescription)")
            return NetworkError.unknownError
        }
        .eraseToAnyPublisher()
    }
    
    /// 退出登录
    /// - Parameter token: 访问令牌
    /// - Returns: 退出登录结果
    public func logout(token: String) -> AnyPublisher<CommonResponse, NetworkError> {
        print("APIService - 构建退出登录请求 - 端点: /app-api/member/auth/logout")
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/logout",
            method: .post,
            headers: [
                "tenant-id": "1",
                "Authorization": "Bearer \(token)"
            ],
            responseType: CommonResponse.self
        )
        .tryMap { response in
            print("APIService - 收到退出登录响应 - 状态码: \(response.code)")
            
            guard response.code == 0 else {
                print("APIService - 退出登录失败 - 消息: \(response.msg ?? "无错误信息")")
                throw NetworkError.serverError(statusCode: response.code, message: response.msg ?? "未知错误")
            }
            
            print("APIService - 退出登录成功")
            return response
        }
        .mapError { error in
            if let networkError = error as? NetworkError {
                print("APIService - 处理网络错误 - \(networkError.localizedDescription)")
                return networkError
            }
            print("APIService - 未知错误 - \(error.localizedDescription)")
            return NetworkError.unknownError
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取用户信息
    /// - Parameter token: 访问令牌
    /// - Returns: 用户信息
    public func getUserInfo(token: String) -> AnyPublisher<UserInfo, NetworkError> {
        print("APIService - 构建获取用户信息请求 - 端点: /app-api/member/user/get")
        
        return networkManager.request(
            endpoint: "/app-api/member/user/get",
            method: .get,
            responseType: UserInfo.self
        )
        .mapError { error in
            if let networkError = error as? NetworkError {
                print("APIService - 处理网络错误 - \(networkError.localizedDescription)")
                return networkError
            }
            print("APIService - 未知错误 - \(error.localizedDescription)")
            return NetworkError.unknownError
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新用户信息
    /// - Parameters:
    ///   - token: 访问令牌
    ///   - userInfo: 用户信息
    /// - Returns: 更新结果
    public func updateUserInfo(token: String, userInfo: UpdateUserInfoRequest) -> AnyPublisher<CommonResponse, NetworkError> {
        print("APIService - 构建更新用户信息请求 - 端点: /app-api/member/user/update")
        
        return networkManager.request(
            endpoint: "/app-api/member/user/update",
            method: .put,
            parameters: userInfo.asDictionary(),
//            headers: [
//                "tenant-id": "1",
//                "Authorization": "Bearer \(token)"
//            ],
            responseType: CommonResponse.self
        )
        .tryMap { response in
            print("APIService - 收到更新用户信息响应 - 状态码: \(response.code)")
            
            guard response.code == 0 else {
                print("APIService - 更新用户信息失败 - 消息: \(response.msg ?? "无错误信息")")
                throw NetworkError.serverError(statusCode: response.code, message: response.msg ?? "未知错误")
            }
            
            print("APIService - 更新用户信息成功")
            return response
        }
        .mapError { error in
            if let networkError = error as? NetworkError {
                print("APIService - 处理网络错误 - \(networkError.localizedDescription)")
                return networkError
            }
            print("APIService - 未知错误 - \(error.localizedDescription)")
            return NetworkError.unknownError
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - 异步/等待API
    
    /// 用户登录（异步）
    /// - Parameters:
    ///   - email: 邮箱
    ///   - password: 密码
    /// - Returns: 登录结果
    @available(iOS 15.0, *)
    public func login(email: String, password: String) async throws -> UserAuthResponse {
        let parameters: [String: Any] = [
            "email": email,
            "password": password
        ]
        
        return try await networkManager.request(
            endpoint: "\(endpoint)/login",
            method: .post,
            parameters: parameters,
            responseType: UserAuthResponse.self
        )
    }
    
    /// 用户注册（异步）
    /// - Parameters:
    ///   - email: 邮箱
    ///   - password: 密码
    ///   - name: 用户名
    /// - Returns: 注册结果
    @available(iOS 15.0, *)
    public func register(email: String, password: String, name: String) async throws -> UserAuthResponse {
        let parameters: [String: Any] = [
            "email": email,
            "password": password,
            "name": name
        ]
        
        return try await networkManager.request(
            endpoint: "\(endpoint)/register",
            method: .post,
            parameters: parameters,
            responseType: UserAuthResponse.self
        )
    }
    
    /// 获取用户信息（异步）
    /// - Parameter token: 授权令牌
    /// - Returns: 用户信息
    @available(iOS 15.0, *)
    public func getUserProfile(token: String) async throws -> UserProfile {
        return try await networkManager.request(
            endpoint: "\(endpoint)/profile",
            headers: networkManager.authHeaders(token: token),
            responseType: UserProfile.self
        )
    }
    
    /// 更新用户信息（异步）
    /// - Parameters:
    ///   - token: 授权令牌
    ///   - profile: 用户信息
    /// - Returns: 更新结果
    @available(iOS 15.0, *)
    public func updateUserProfile(token: String, profile: UserProfileUpdate) async throws -> UserProfile {
        return try await networkManager.request(
            endpoint: "\(endpoint)/profile",
            method: .put,
            parameters: profile.asDictionary(),
            headers: networkManager.authHeaders(token: token),
            responseType: UserProfile.self
        )
    }
    
    /// 刷新令牌
    /// - Parameter refreshToken: 刷新令牌
    /// - Returns: 刷新结果
    public func refreshToken(refreshToken: String) -> AnyPublisher<TokenResponse, NetworkError> {
        let parameters: [String: Any] = [
            "refreshToken": refreshToken
        ]
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/refresh-token",
            method: .post,
            parameters: parameters,
//            headers: ["tenant-id": "1"],
            responseType: TokenResponse.self
        )
    }
    
    /// 获得文案（政策，条款，个人信息）
    /// - Parameters:
    ///   - tag: terms_of_use 使用条款  privacy_policy 隐私政策  information_list 收集个人信息 app_usage_guide 使用指南app 使用指南_Ring 使用指南ring password_usage_guide 使用指南passward
    /// - Returns: 更新结果
    @available(iOS 15.0, *)
    public func getCopywriting(tag: String) -> AnyPublisher<CopywritingResponse, NetworkError> {
        var parameters: [String: String] = [:]
        let selectedLanguage = LanguageManager.shared.selectedLanguage
        if selectedLanguage == "zh-Hans" {
            parameters["lang"] = "ZH"
        }else{
            parameters["lang"] = "EN"
        }
        return networkManager.request(
            endpoint: "/app-api/member/copywriting/get/\(tag)",
            method: .get,
//            parameters: parameters,
            
            headers: parameters,
            responseType: CopywritingResponse.self
        )
//        .tryMap { response in
//            print("APIService - 收到信息响应 - 状态码: \(response.code)")
//            
//            guard response.code == 0 else {
//                print("APIService - 获取信息失败 - 消息: \(response.msg ?? "无错误信息")")
//                throw NetworkError.serverError(statusCode: response.code, message: response.msg ?? "未知错误")
//            }
//            
//            guard let data = response.data else {
//                print("APIService - 用户信息数据为空")
//                throw NetworkError.serverError(statusCode: 500, message: "无效的响应数据")
//            }
//            return response.data!
//        }
//        .mapError { error in
//            if let networkError = error as? NetworkError {
//                print("APIService - 处理网络错误 - \(networkError.localizedDescription)")
//                return networkError
//            }
//            print("APIService - 未知错误 - \(error.localizedDescription)")
//            return NetworkError.unknownError
//        }
//        .eraseToAnyPublisher()
    }
    
    public func validateSmsCode(mobile: String, scene: SmsSceneEnum, code: String) -> AnyPublisher<CommonResponse, NetworkError> {
        print("APIService - 校验短信验证码请求 - 端点: /app-api/member/auth/validate-sms-code")
        let request = ValidateSmsCodeRequest(mobile: mobile, scene: scene, code: code)
        
        return networkManager.request(
            endpoint: "/app-api/member/auth/validate-sms-code",
            method: .post,
            parameters: request.asDictionary(),
            responseType: CommonResponse.self
        )
    }
    
    public func validateEmailCode(email: String, scene: EmailSceneEnum, code: String) -> AnyPublisher<CommonResponse, NetworkError> {
        print("APIService - 校验邮箱验证码请求 - 端点: /app-api/member/auth/validate-email-code")
        var request = EmailCodeRequest(email: email, scene: scene)
        request.code = code
        return networkManager.request(
            endpoint: "/app-api/member/auth/validate-email-code",
            method: .post,
            parameters: request.asDictionary(),
            responseType: CommonResponse.self
        )
    }
    
    /// 重置密码（使用验证码）
    public func resetPasswordWithCode(account: String, code: String, password: String,confirmPassword:String) -> AnyPublisher<String, NetworkError> {
        var parameters: [String: Any] = ["code": code, "password": password,"confirmPassword":confirmPassword]
        if account.contains("@") {
            parameters["email"] = account
        } else {
            parameters["mobile"] = account
        }
        
        return networkManager.request(
            endpoint: "/app-api/member/user/reset-password",
            method: .put,
            parameters: parameters,
            responseType: String.self
        )
    }
    
    /// 获取用户目标
    public func getMemberGoal() -> AnyPublisher<MemberGoal, NetworkError> {
        return networkManager.request(
            endpoint: "/app-api/member/goal/getMemberGoal",
            method: .get,
            responseType: MemberGoal.self
        )
    }

    /// 更新用户目标
    public func updateMemberGoal(goal: [String:Any]) -> AnyPublisher<CommonResponse, NetworkError> {
        return networkManager.request(
            endpoint: "/app-api/member/goal/update",
            method: .post,
            parameters: goal,
            responseType: CommonResponse.self
        )
    }
    
    /// 创建反馈
    public func createFeedback(parameters: [String: Any]) -> AnyPublisher<CommonResponse, NetworkError> {
        return networkManager.request(
            endpoint: "/app-api/member/feedback/create",
            method: .post,
            parameters: parameters,
            responseType: CommonResponse.self
        )
    }
    
    /// 获取意见反馈列表
    public func getFeedbackHistory(page: Int, pageSize: Int) -> AnyPublisher<FeedbackHistoryPage, NetworkError> {
        let parameters: [String: Any] = [
            "pageNo": page,
            "pageSize": pageSize
        ]
        
        return networkManager.request(
            endpoint: "/app-api/member/feedback/page",
            method: .get,
            parameters: parameters,
            responseType: FeedbackHistoryPage.self
        )
    }

    /// 绑定设备
    public func bindDevice(mac: String,deviceSn: String,deviceFirmwareVersion: String, name: String) -> AnyPublisher<CommonResponse, NetworkError> {
        let parameters: [String: Any] = [
            "deviceMac": mac,
            "deviceSn": deviceSn,
            "deviceFirmwareVersion": deviceFirmwareVersion,
            "deviceName": name
            
        ]
        return networkManager.request(
            endpoint: "/app-api/member/user/bind",
            method: .put,
            parameters: parameters,
            responseType: CommonResponse.self
        )
    }

    /// 解绑设备
    public func unbindDevice() -> AnyPublisher<CommonResponse, NetworkError> {
//        let parameters: [String: Any] = [
//            "deviceMac": mac
//        ]
//        
        return networkManager.request(
            endpoint: "/app-api/member/user/unbind",
            method: .put,
            parameters: [:],
            responseType: CommonResponse.self
        )
    }
}
