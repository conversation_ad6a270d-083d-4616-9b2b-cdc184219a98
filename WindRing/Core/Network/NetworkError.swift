/// 网络错误
import Foundation

public enum NetworkError: Error {
    /// 无效的URL
    case invalidURL
    /// 无效的响应
    case invalidResponse
    /// 解码错误
    case decodingError
    /// 编码错误
    case encodingError
    /// 网络错误
    case networkError
    /// HTTP错误
    case httpError(statusCode: Int)
    /// 客户端错误
    case clientError(statusCode: Int)
    /// 服务器错误
    case serverError(statusCode: Int, message: String)
    /// 未授权错误
    case unauthorized
    /// 未知错误
    case unknownError
    /// API错误
    case apiError(code: Int, message: String)
    /// 请求参数编码失败
    case encodingFailed
    /// 响应数据解码失败
    case decodingFailed
    /// 没有数据
    case noData
    
    /// 错误描述
    public var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .invalidResponse:
            return "无效的响应"
        case .decodingError:
            return "解码错误"
        case .encodingError:
            return "编码错误"
        case .networkError:
            return "网络错误"
        case .httpError(let statusCode):
            return "HTTP错误: \(statusCode)"
        case .clientError(let statusCode):
            return "客户端错误: \(statusCode)"
        case .serverError(let statusCode, let message):
            return "\(message)"
        case .unauthorized:
            return "未授权，请先登录"
        case .unknownError:
            return "未知错误"
        case .apiError(_, let message):
            return "\(message)"
        case .encodingFailed:
            return "请求参数编码失败"
        case .decodingFailed:
            return "响应数据解码失败"
        case .noData:
            return "没有数据"
        }
    }
} 
