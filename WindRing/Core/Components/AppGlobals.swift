//
//  AppGlobals.swift
//  WindRing
//
//  Created by zx on 2025/5/21.
//
struct AppGlobals {
    
    // MARK: - API 配置
//    static let apiBaseURL = "https://ring-api.weaving-park.com"
    static let apiBaseURL = "https://ring-api-dev.weaving-park.com"
    
    static let refreshTokenEndpoint = "/app-api/member/auth/refresh-token"
    static let loginEndpoint = "/app-api/member/auth/login"

    static let sentryKey = "https://<EMAIL>/****************"
    
    static let passwordRegex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,20}$"
    
    // MARK: - Keychain 配置
//    static let keychainService = "com.example.app"
//    static let keychainAccountSession = "sessionInfo"

    // MARK: - Token 配置
//    static let tokenExpirationBuffer: TimeInterval = 60 // 提前 60 秒刷新

    // MARK: - 用户默认存储Key
    static let userDefaults_isFirstLaunch = "isFirstLaunch"
    ///默认步数8000步，卡路里300千卡，中高强度总时长45分钟
    static let defaultSteps = 8000.0
    ///卡路里300千卡
    static let defaultKcal = 300.0
    ///中高强度总时长45分钟
    static let defaultActivityStepTime = 45.0

}
