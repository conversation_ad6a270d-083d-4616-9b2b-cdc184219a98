import SwiftUI

/// MQTT状态视图
struct MQTTStatusView: View {
    @EnvironmentObject var mqttService: MQTTService
    @EnvironmentObject var mqttSyncService: MQTTSyncService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Text("MQTT状态")
                    .font(.headline)
                
                Spacer()
                
                // 连接状态指示器
                Circle()
                    .fill(mqttService.connectionStatus ? Color.green : Color.red)
                    .frame(width: 10, height: 10)
                
                Text(mqttService.connectionStatus ? "connected".localized : "disconnected".localized)
                    .font(.subheadline)
                    .foregroundColor(mqttService.connectionStatus ? .green : .red)
            }
            
            // 同步状态
            HStack {
                Text("同步状态:")
                    .font(.subheadline)
                
                Spacer()
                
                if mqttSyncService.isSyncing {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(0.7)
                    
                    Text("正在同步...")
                        .font(.caption)
                        .foregroundColor(.gray)
                } else {
                    Text(mqttSyncService.lastSyncTime != nil ? "上次同步: \(formatDate(mqttSyncService.lastSyncTime!))" : "未同步")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            
            // 自动连接状态
            HStack {
                Text("自动连接:")
                    .font(.subheadline)
                
                Text(mqttService.autoConnect ? "已启用" : "已禁用")
                    .font(.subheadline)
                    .foregroundColor(mqttService.autoConnect ? .green : .gray)
                
                Spacer()
                
                if mqttService.userDisconnected {
                    Text("用户已主动断开")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
            }
            
            // 连接/断开连接按钮行
            HStack {
                // 手动同步按钮
                Button(action: {
                    mqttSyncService.syncNow()
                }) {
                    Text("手动同步")
                        .font(.subheadline)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
                .disabled(mqttSyncService.isSyncing || !mqttService.connectionStatus)
                
                Spacer()
                
                // 连接/断开连接按钮
                if mqttService.connectionStatus {
                    Button(action: {
                        mqttService.disconnect()
                    }) {
                        Text("断开连接")
                            .font(.subheadline)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.red)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                } else {
                    Button(action: {
                        mqttService.connect()
                    }) {
                        Text("连接")
                            .font(.subheadline)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                }
            }
            
            // 错误信息
            if let error = mqttService.lastError {
                Text("错误: \(error.localizedDescription)")
                    .font(.caption)
                    .foregroundColor(.red)
                    .lineLimit(2)
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    /// 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }
}

// 使用明确的PreviewProvider结构体而不是#Preview宏
struct MQTTStatusView_Previews: PreviewProvider {
    static var previews: some View {
        MQTTStatusView()
            .environmentObject(MQTTService.shared)
            .environmentObject(MQTTSyncService.shared)
            .padding()
            .background(Color.appBackground)
    }
} 
