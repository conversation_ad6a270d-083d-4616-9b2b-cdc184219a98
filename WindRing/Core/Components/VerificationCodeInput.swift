import SwiftUI

struct VerificationCodeInput: View {
    @Binding var code: String
    let codeLength: Int
    let completion: () -> Void
    
    @FocusState private var isCodeFieldFocused: Bool
    
    init(code: Binding<String>, codeLength: Int = 6, completion: @escaping () -> Void = {}) {
        self._code = code
        self.codeLength = codeLength
        self.completion = completion
    }
    
    var body: some View {
        VStack(spacing: 16) {
            HStack(spacing: 12) {
                ForEach(0..<codeLength, id: \.self) { index in
                    DigitBox(digit: getDigitAtIndex(index))
                }
            }
            .padding(.horizontal, 8)
            
            // 隐藏的文本框用于处理输入
            TextField("", text: $code)
                .keyboardType(.numberPad)
                .frame(width: 1, height: 1)
                .opacity(0.01)
                .focused($isCodeFieldFocused)
                .onChange(of: code) {newValue in
                    // 限制只能输入数字
                    if !newValue.isEmpty && !CharacterSet.decimalDigits.isSuperset(of: CharacterSet(charactersIn: String(newValue.last!))) {
                        code = String(newValue.dropLast())
                    }
                    
                    // 限制最大长度
                    if newValue.count > codeLength {
                        code = String(newValue.prefix(codeLength))
                    }
                    
                    // 如果达到验证码长度，触发完成回调
                    if newValue.count == codeLength {
                        completion()
                    }
                }
        }
        .onTapGesture {
            isCodeFieldFocused = true
        }
        .onAppear {
            isCodeFieldFocused = true
        }
    }
    
    private func getDigitAtIndex(_ index: Int) -> String {
        guard index < code.count else {
            return ""
        }
        
        let stringIndex = code.index(code.startIndex, offsetBy: index)
        return String(code[stringIndex])
    }
}

// 显示单个验证码数字的盒子
struct DigitBox: View {
    let digit: String
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 8)
                .stroke(digit.isEmpty ? Color.gray.opacity(0.3) : Color.blue, lineWidth: 1.5)
                .frame(width: 44, height: 56)
                .background(digit.isEmpty ? Color.gray.opacity(0.05) : Color.blue.opacity(0.05))
                .animation(.easeInOut(duration: 0.2), value: digit.isEmpty)
            
            if digit.isEmpty {
                Circle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 8, height: 8)
            } else {
                Text(digit)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
        }
    }
}

// 倒计时按钮组件
struct CountdownButton: View {
    let title: String
    let action: () -> Void
    @Binding var isCountingDown: Bool
    @Binding var countdown: Int
    
    var body: some View {
        Button(action: {
            if !isCountingDown {
                action()
            }
        }) {
            Text(isCountingDown ? "\(countdown)秒后重试" : title)
                .font(.system(size: 14))
                .foregroundColor(isCountingDown ? .gray : .blue)
                .frame(minWidth: 100)
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .background(isCountingDown ? Color.gray.opacity(0.1) : Color.blue.opacity(0.1))
                .cornerRadius(8)
        }
        .disabled(isCountingDown)
    }
}

// 预览
struct VerificationCodeInput_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 24) {
            VerificationCodeInput(code: .constant("123"))
            
            CountdownButton(
                title: "获取验证码",
                action: {},
                isCountingDown: .constant(true),
                countdown: .constant(45)
            )
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
} 
