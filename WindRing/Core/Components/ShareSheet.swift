import SwiftUI
#if os(iOS)
import UIKit

/// 分享表单组件
struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    let applicationActivities: [UIActivity]? = nil
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: applicationActivities
        )
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // 无需更新
    }
}
#elseif os(macOS)
import AppKit

/// 分享表单组件 (macOS版本)
struct ShareSheet: NSViewControllerRepresentable {
    let activityItems: [Any]
    
    func makeNSViewController(context: Context) -> NSViewController {
        let controller = NSViewController()
        
        // 在macOS上实现分享功能
        let sharingServicePicker = NSSharingServicePicker(items: activityItems)
        
        // 延迟展示picker以确保视图已加载
        DispatchQueue.main.async {
            if let view = controller.view {
                sharingServicePicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        }
        
        return controller
    }
    
    func updateNSViewController(_ nsViewController: NSViewController, context: Context) {
        // 无需更新
    }
}
#endif

#Preview {
    Button("分享") {
        // 预览中无法触发实际的分享操作
    }
    .previewDisplayName("分享按钮")
} 