import Foundation
import Combine
import SwiftUI
import CoreData
import UserNotifications
import CoreBluetooth
import Network
import os.log
import ObjectiveC
#if os(iOS)
import CRPSmartRing
#endif

// 导入自定义模型
// 无需显式导入Swift文件，因为它们是项目的一部分，应该可以自动被找到

/// WindRing设备管理服务
class WindRingDeviceService: NSObject, ObservableObject {
    // MARK: - 属性
    public static let shared = WindRingDeviceService()
    
    // 设备发现列表和连接状态
    @Published var discoveredDevices: [CRPDiscovery] = []
    @Published var connectionState: ConnectionState = .disconnected
    @Published var isScanning: Bool = false
    
    // 标记设备已配对
//    private var pairedDevices: Set<String> = []
    
    // 系统已连接设备的信息
    private var systemConnectedPeripherals: [CBPeripheral] = []
    
    // 自动重连设置
    @Published var autoReconnect: Bool = false
    
    // 添加区分断开连接类型的变量
    private var isManualUnbinding: Bool = false
    private var lastDisconnectionTime: Date = Date()
    var disconnectionType: DisconnectionType = .none
    
    // 睡眠算法类型
    @Published var sleepAlgorithmType: SleepAlgorithmType = .basic
    
    // 扫描和连接重试相关
    @Published var isAutoScanning: Bool = false
    var scanRetryCount: Int = 0
    var connectionRetryCount: Int = 0
    
    // 增加对当前连接设备的引用，防止系统回收导致连接失败
    @Published var currentDiscovery: CRPDiscovery?
    var currentPeripheral: CBPeripheral?
    
    // 我们产品的厂商识别信息
    private let ourMacPrefixes: [String] = [] // 增加更多可能的MAC前缀
    private let ourManufacturerIds: [UInt16] = [0x004C, 0x00FF, 0x01FF] // 增加更多可能的制造商ID
    private let ourServiceUUIDs = ["180D", "180A", "6E400001-B5A3-F393-E0A9-E50E24DCCA9E"] // 根据实际情况设置
    private let ourModelNames = ["Da Ring", "DaRing"] // 增加低端固件设备型号
    
    // 用户自己的设备信息
    private let userDeviceKey = "com.windring.userDevice" // UserDefaults键名
    private let deviceMappingsKey = "com.windring.deviceMappings" // 设备标识符和MAC地址的映射
    @Published var lastConnectedDeviceMAC: String? = nil // 最后连接设备的MAC地址
    @Published var userDeviceNickname: String? = nil // 用户设备昵称
    
    // 已知需要过滤的设备信息
    private let filterDeviceNames = ["VRing", "V Ring", "V-Ring", "KeepB4-95", "TMSJ-B517024040219", "Smart Watch"] // 移除"Smart Ring"和"SmartRing"
    private let filterMacAddresses = ["A8:28:57:C0:25:95", "2D:4C:50:2D:42:50"]
    private let filterMacPrefixes = ["00:11:22", "BB:CC:DD"] // 确保不与ourMacPrefixes冲突
    
    // MARK: - 设备信息
    @Published var deviceInfo: RingInfo?
//    @Published var deviceInfo: DeviceInfo?
    @Published var batteryLevel: Int = 0
    @Published var wearingState: Int = 0
    @Published var isCharging: Bool = false
    
    // 固件类型相关
    @Published var firmwareType: String = "UNKNOWN"
    
    // MARK: - 测量相关
    @Published public var isMeasuringHeartRate: Bool = false
    @Published public var isMeasuringSpO2: Bool = false
    @Published public var isMeasuringHRV: Bool = false
    @Published public var isMeasuringBloodPressure: Bool = false
    @Published public var isMeasuringTemperature: Bool = false
    @Published public var isMeasuringStress: Bool = false
    
    @Published var lastHeartRate: Int = 0
    @Published var lastBloodOxygen: Int = 0
    @Published var lastTemperature: Double = 0.0
    @Published var lastBloodPressure: (systolic: Int, diastolic: Int) = (0, 0)
    @Published var lastHRV: Int = 0
    @Published var lastStress: Int = 0
    
    // MARK: - 发布属性
    @Published var isInitialized: Bool = false
    
    // MARK: - MQTT相关
    private var mqttSyncService: MQTTSyncService?
    private var cancellables = Set<AnyCancellable>()
    
    
    
    // MARK: - 设备信息结构
//    struct DeviceInfo{
//        var name: String
//        var macAddress: String
//        var firmwareVersion: String
//        var ringInfo: CRPRingInfoModel?
//        var hardwareVersion: String?
//        var deviceId: String?
//        
//    }
    
    
    
    // MARK: - 睡眠数据结构
    struct SleepData {
        var startTime: Date
        var endTime: Date
        var deepSleepMinutes: Int
        var lightSleepMinutes: Int
        var remSleepMinutes: Int
        var awakeMinutes: Int
        var sleepQuality: Int
        
        init(from model: CRPSleepRecordModel) {
            // 从SDK模型转换
            let now = Date()
            self.startTime = now.addingTimeInterval(-Double(model.deep + model.light + model.rem) * 60)
            self.endTime = now
            self.deepSleepMinutes = model.deep
            self.lightSleepMinutes = model.light
            self.remSleepMinutes = model.rem
            self.awakeMinutes = 0 // SDK模型中可能没有这个字段
            self.sleepQuality = 0 // SDK模型中可能没有这个字段
        }
        
        // 添加新的初始化方法，直接使用参数初始化
        init(startTime: Date, endTime: Date, deepSleepMinutes: Int, lightSleepMinutes: Int, 
             remSleepMinutes: Int, awakeMinutes: Int, sleepQuality: Int) {
            self.startTime = startTime
            self.endTime = endTime
            self.deepSleepMinutes = deepSleepMinutes
            self.lightSleepMinutes = lightSleepMinutes
            self.remSleepMinutes = remSleepMinutes
            self.awakeMinutes = awakeMinutes
            self.sleepQuality = sleepQuality
        }
    }
    
    // MARK: - 睡眠算法类型
    enum SleepAlgorithmType: String {
        case goMore = "GoMore高级睡眠算法"
        case basic = "基础睡眠算法"
        
        var description: String {
            return self.rawValue
        }
    }
    
    // MARK: - 断开连接类型枚举
    enum DisconnectionType {
        case none           // 未断开或初始状态
        case manualUnbind   // 用户主动解除绑定
        case passive        // 被动断开(如超出范围、信号丢失等)
        case systemRequest  // 系统请求断开(蓝牙关闭等)
    }
    
    // MARK: - 初始化
    private override init() {
        super.init()
        setupSDK()
        
        // 设置SDK委托
//        CRPSmartRingSDK.sharedInstance.delegate = self
        
        // 设置设备观察者
        setupDeviceObservers()
        
        // 加载用户设备信息和设置
        loadUserDeviceInfo()
        loadSettings()
        
        // 如果没有设置过自动重连，默认启用
        if UserDefaults.standard.object(forKey: "autoReconnect") == nil {
            setAutoReconnect(true)
        }
        
        // 延迟初始化MQTT同步服务，避免循环引用
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.mqttSyncService = MQTTSyncService.shared
        }
        
        // 启动系统蓝牙连接状态监听
        startMonitoringSystemBluetoothConnection()
       
        // 订阅系统已连接设备的更新
        BluetoothSystemDeviceManager.shared.connectionStatusPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] peripherals in
                self?.systemConnectedPeripherals = peripherals
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
            
        // 读取已保存的配对设备
//        loadPairedDevices()
        
        // 初始时检测系统连接的设备
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            BluetoothSystemDeviceManager.shared.refreshConnectedDevices()
        }
    }
    
    

    // MARK: - 设置SDK
    private func setupSDK() {
        print("初始化WindRingDeviceService...")
        
        // 设置SDK代理
//        CRPSmartRingSDK.sharedInstance.delegate = self
        print("SDK初始化状态: 已设置代理")
        
        // 从UserDefaults恢复上次设备连接的MAC地址
//        if let userDevice = UserDefaults.standard.dictionary(forKey: userDeviceKey) {
//            lastConnectedDeviceMAC = userDevice["macAddress"] as? String
//            userDeviceNickname = userDevice["nickname"] as? String ?? "我的戒指"
//            print("从UserDefaults恢复设备信息: MAC=\(lastConnectedDeviceMAC ?? "无"), 昵称=\(userDeviceNickname)")
//        }
        
        // 从UserDefaults恢复自动重连设置
        autoReconnect = UserDefaults.standard.bool(forKey: "autoReconnect")
        print("自动重连设置: \(autoReconnect ? "开启" : "关闭")")
        
        // 设置SDK的自动重连设置（SDK内部实现）
        setAutoReconnect(autoReconnect)
        
        // 获取系统已连接设备的状态
        initSystemConnectedDevices()
        
        // 标记初始化完成
        isInitialized = true
    }
    
    /// 初始化系统已连接设备的状态
    private func initSystemConnectedDevices() {
        // 使用独立的蓝牙管理器检测系统已连接设备
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            // 初始化并启动系统设备管理器
            _ = BluetoothSystemDeviceManager.shared
        }
    }
    
    // MARK: - 用户设备管理
    /// 加载用户设备信息
    private func loadUserDeviceInfo() {
        // 已禁用 - 不从UserDefaults加载设备MAC地址
        // if let deviceInfo = UserDefaults.standard.dictionary(forKey: userDeviceKey) {
        //     lastConnectedDeviceMAC = deviceInfo["mac"] as? String
        //     userDeviceNickname = deviceInfo["nickname"] as? String ?? "我的戒指"
        //     print("加载用户设备信息: MAC=\(lastConnectedDeviceMAC ?? "未知"), 昵称=\(userDeviceNickname)")
        // }
        
//        if let device = CRPSmartRingManage.shared.currentCRPDiscovery{
            deviceInfo = loadDevice()
//            dev  iceInfo?.peripheral = device
            lastConnectedDeviceMAC = deviceInfo?.mac//["macAddress"] as? String
            userDeviceNickname = deviceInfo?.localName//userDevice["nickname"] as? String ?? "我的戒指"
//        }
        
        // 仅加载设备昵称，不加载MAC地址
//        if let deviceInfo = UserDefaults.standard.dictionary(forKey: userDeviceKey) {
////            self.deviceInfo =  deviceInfo
//            userDeviceNickname = deviceInfo["nickname"] as? String ?? "我的戒指"
//            print("加载用户设备信息: 昵称=\(userDeviceNickname)")
//        }
    }
    
    /// 设置是否自动重连
    func setAutoReconnect(_ enable: Bool) {
        autoReconnect = enable
        
        // 保存设置到用户偏好
        UserDefaults.standard.set(enable, forKey: "autoReconnect")
        
        print("自动重连功能已\(enable ? "启用" : "禁用")")
        
        // 如果禁用自动重连，则停止任何正在进行的扫描
        if !enable && isAutoScanning {
            isAutoScanning = false
            stopScan()
            print("自动扫描已停止")
        }
    }
    
    /// 加载设置
    private func loadSettings() {
        // 从用户偏好加载自动重连设置
        autoReconnect = UserDefaults.standard.bool(forKey: "autoReconnect")
        print("加载设置: 自动重连 = \(autoReconnect)")
    }
    
    /// 保存用户设备信息
    private func saveUserDeviceInfo(mac: String?, nickname: String?) {
        var deviceInfo: [String: Any] = [:]
        
        if let mac = mac, !mac.isEmpty {
            deviceInfo["mac"] = mac
            lastConnectedDeviceMAC = mac
        } else {
            // 当MAC为nil时，清除lastConnectedDeviceMAC
            lastConnectedDeviceMAC = ""
        }
        
        if let nickname = nickname {
            deviceInfo["nickname"] = nickname
            userDeviceNickname = nickname
        } else {
            // 当nickname为nil时，清除userDeviceNickname
            userDeviceNickname = ""
        }
        
        UserDefaults.standard.set(deviceInfo, forKey: userDeviceKey)
        print("保存用户设备信息: MAC=\(mac ?? "未设置"), 昵称=\(nickname ?? "未设置")")
    }
    
    /// 设置设备昵称
    func setDeviceNickname(_ nickname: String) {
        userDeviceNickname = nickname
        saveUserDeviceInfo(mac: lastConnectedDeviceMAC, nickname: nickname)
    }
    
    /// 保存设备标识符和MAC地址的映射关系
    private func saveDeviceMapping(uuid: String, mac: String) {
        guard !mac.isEmpty else { return }
        
        // 获取现有映射
        var mappings = UserDefaults.standard.dictionary(forKey: deviceMappingsKey) as? [String: String] ?? [:]
        
        // 添加或更新映射
        mappings[uuid] = mac
        
        // 保存回UserDefaults
        UserDefaults.standard.set(mappings, forKey: deviceMappingsKey)
        print("保存设备映射: UUID=\(uuid), MAC=\(mac)")
    }
    
    /// 根据设备标识符查找MAC地址
    private func getMacFromMapping(uuid: String) -> String? {
        let mappings = UserDefaults.standard.dictionary(forKey: deviceMappingsKey) as? [String: String] ?? [:]
        return mappings[uuid]
    }
    
    // MARK: - 扫描方法
    /// 开始扫描设备
    func startScan(duration: TimeInterval = 15, completion: (() -> Void)? = nil) {
        guard !isScanning else { 
            completion?()
            return
        }
        
        // 开始扫描前，先清空设备列表
        discoveredDevices.removeAll()
        isScanning = true
        
        // 刷新系统已连接设备列表
        _ = BluetoothSystemDeviceManager.shared.refreshConnectedDevices()
        
        // 将系统已连接但尚未扫描到的设备添加到扫描列表中
        addSystemConnectedDevicesToScanResults()
        
        print("开始扫描设备...")
        // 在SDK扫描前验证蓝牙状态
        CRPSmartRingManage.shared.scanForPeripherals(duration: duration,progressHandler: { [weak self] discoveries in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                // 临时存储新发现的设备
                var newDevices: [CRPDiscovery] = []
                
                for discovery in discoveries {
                    // 调试输出广播数据
                    if discovery.localName?.count ?? 0 <= 0{
                        return
                    }
                    
                    // 先记录所有发现的设备，便于调试
                    let deviceInfo = """
                    发现设备: \(discovery.localName ?? "未知")
                    MAC: \(discovery.mac ?? "未知")
                    RSSI: \(discovery.RSSI)
                    """
                    print(deviceInfo)
                    
                    // 过滤设备
                    if self.shouldIncludeDevice(discovery) {
                        newDevices.append(discovery)
                        self.analyzeDiscoveryData(discovery)
                    }
                }
                
                // 更新UI前先检查是否有新设备
                if !newDevices.isEmpty {
                    // 移除重复设备并添加新发现的设备
                    for newDevice in newDevices {
                        // 检查是否已有相同设备
                    if !self.discoveredDevices.contains(where: {
                            $0.remotePeripheral.identifier == newDevice.remotePeripheral.identifier
                        }) {
                            self.discoveredDevices.append(newDevice)
                            
                            // 检查是否是用户之前连接过的设备，自动标记为已配对
                            if let mac = newDevice.mac, !mac.isEmpty,
                               mac == self.lastConnectedDeviceMAC {
                                self.markDeviceAsPaired(newDevice)
                                print("标记设备为已配对: \(newDevice.localName ?? "未知设备") (\(mac))")
                            }
                        }
                    }
                    
                    // 按照信号强度排序设备列表
                    self.discoveredDevices.sort {
                        $0.RSSI > $1.RSSI
                        }
                    }
                }
        },completionHandler: { [weak self] devices, error in
            DispatchQueue.main.async {
                guard let self = self else { 
                    completion?()
                    return 
                }
                self.isScanning = false
                
                if error != .none {
                    print("扫描出错: \(error)")
                } else {
                    print("扫描完成，发现 \(self.discoveredDevices.count) 个戒指设备")
                    
                    // 检查是否有系统连接的设备没有在扫描结果中
//                    self.addMissingSystemConnectedDevices()
                }
                
                // 扫描结束，重置自动扫描标志
                self.isAutoScanning = false
                self.scanRetryCount = 0
                
                completion?()
            }
        })
    }
    
    /// 分析设备广播数据，输出调试信息并检测充电状态
    private func analyzeDiscoveryData(_ discovery: CRPDiscovery) {
        guard let name = discovery.localName, !name.isEmpty else { return }
        
        // 基本信息
        print("📱 发现设备: \(name), MAC: \(discovery.mac ?? "未知")")
        
        // 获取设备的UUID
        let uuid = discovery.remotePeripheral.identifier.uuidString
        
        // 如果有MAC地址，缓存该MAC地址
        if let mac = discovery.mac, !mac.isEmpty {
            // 保存基于名称的缓存
            saveTrustedMacAddress(name: name, mac: mac)
            
            // 保存基于UUID的映射 - 这是最可靠的方法
            saveDeviceMacByUUID(uuid: uuid, mac: mac)
        } else {
            // 尝试先从UUID映射获取MAC
            if let uuidMac = getDeviceMacByUUID(uuid: uuid) {
                print("为设备\(name)使用UUID映射的MAC地址: \(uuidMac)")
                setValue(uuidMac, forKey: "mac", in: discovery)
            }
            // 然后尝试从名称缓存中获取可信的MAC地址
            else if let cachedMac = getTrustedMacAddress(forDeviceName: name) {
                print("为设备\(name)使用缓存的可信MAC地址: \(cachedMac)")
                
                // 生成一个唯一版本，避免同名设备共用一个MAC
                let uniqueMac = generateUniqueMAC(baseMac: cachedMac, uuid: uuid)
                setValue(uniqueMac, forKey: "mac", in: discovery)
                
                // 保存到UUID映射
                saveDeviceMacByUUID(uuid: uuid, mac: uniqueMac)
            } else {
                // 如果都没有，生成一个基于UUID的MAC地址
                let generatedMac = generateMACFromUUID(uuid: uuid)
                print("为设备\(name)生成基于UUID的MAC地址: \(generatedMac)")
                setValue(generatedMac, forKey: "mac", in: discovery)
                
                // 保存到UUID映射
                saveDeviceMacByUUID(uuid: uuid, mac: generatedMac)
            }
        }
        
        // 是否充电标记
        var potentiallyCharging = false
        var chargingReason = ""
        
        // 打印所有广播数据
        if let advData = discovery.advertisementData as? [String: Any] {
            print("📝 广播数据内容:")
            
            // 设备名称
            if let localName = advData[CBAdvertisementDataLocalNameKey] as? String {
                print("  - 本地名称: \(localName)")
                
                // 检查名称是否包含充电指示
                if localName.contains("[C]") || localName.contains("*CHG*") {
                    potentiallyCharging = true
                    chargingReason = "设备名称包含充电标识"
                }
            }
            
            // 制造商数据（常包含特定设备信息，如充电状态）
            if let manufacturerData = advData[CBAdvertisementDataManufacturerDataKey] as? Data {
                let hexString = manufacturerData.map { String(format: "%02X", $0) }.joined(separator: ":")
                print("  - 制造商数据: \(hexString)")
                
                // 提取制造商ID
                if manufacturerData.count >= 2 {
                    let manufacturerId = (UInt16(manufacturerData[0]) << 8) | UInt16(manufacturerData[1])
                    print("    - 制造商ID: 0x\(String(format: "%04X", manufacturerId))")
                }
                
                // 如果数据足够长，检查可能的状态字节
                if manufacturerData.count >= 5 {
                    let statusByte = manufacturerData[4]
                    print("    - 状态字节(可能): 0x\(String(format: "%02X", statusByte))")
                    
                    // 检查最高位是否为1（假设这表示充电）
                    if (statusByte & 0x80) != 0 {
                        potentiallyCharging = true
                        chargingReason = "制造商数据状态字节第7位为1"
                    }
                    
                    // 对于某些设备，第2位表示充电
                    if (statusByte & 0x02) != 0 {
                        potentiallyCharging = true
                        chargingReason = "制造商数据状态字节第1位为1"
                    }
                }
            }
            
            // 服务数据（可能包含电池电量等信息）
            if let serviceData = advData[CBAdvertisementDataServiceDataKey] as? [CBUUID: Data] {
                print("  - 服务数据:")
                for (uuid, data) in serviceData {
                    let hexString = data.map { String(format: "%02X", $0) }.joined(separator: ":")
                    print("    - \(uuid.uuidString): \(hexString)")
                    
                    // 电池服务UUID
                    if uuid.uuidString == "180F" && data.count > 0 {
                        let batteryLevel = Int(data[0])
                        print("    - 电池电量: \(batteryLevel)%")
                        
                        // 如果电池电量超过100，通常表示充电中
                        if batteryLevel > 100 {
                            potentiallyCharging = true
                            chargingReason = "电池电量数据大于100 (\(batteryLevel))"
                        }
                    }
                }
            }
            
            // 服务UUID列表
            if let serviceUUIDs = advData[CBAdvertisementDataServiceUUIDsKey] as? [CBUUID] {
                print("  - 服务UUID列表: \(serviceUUIDs.map { $0.uuidString }.joined(separator: ", "))")
            }
            
            // 设备信号强度
            if let rssi = discovery.RSSI as? NSNumber {
                print("  - 信号强度: \(rssi.intValue) dBm")
            }
        }
        
        // 特定MAC地址设备的充电状态
        if let mac = discovery.mac {
            // 已知正在充电的设备
            let knownChargingMACs = ["CD:19:01:33:31:F8"]  // 红色箭头指向的设备MAC
            if knownChargingMACs.contains(mac) {
                potentiallyCharging = true
                chargingReason = "已知充电中MAC地址"
            }
        }
        
        // 如果检测到可能在充电，输出信息
        if potentiallyCharging {
            print("⚡ 设备可能正在充电: \(name) (\(discovery.mac ?? "未知MAC")) - 原因: \(chargingReason)")
            
            // 为该设备添加充电标记（使用Associated Object）
            objc_setAssociatedObject(
                discovery,
                &AssociatedKeys.isCharging,
                true,
                .OBJC_ASSOCIATION_RETAIN_NONATOMIC
            )
        }
        
        print("------------------------")
    }
    
    /// 添加系统已连接的设备到扫描结果中
    private func addSystemConnectedDevicesToScanResults() {
        let systemDevices = systemConnectedPeripherals.filter { peripheral in
            // 只添加名称符合条件的设备
            guard let name = peripheral.name else { return false }
            return name.contains("Da Ring") || name.contains("VRing") || name.contains("Herz P1 Ring")
        }
        
        for peripheral in systemDevices {
            // 检查是否已经在扫描结果中
            let uuid = peripheral.identifier.uuidString
            if !discoveredDevices.contains(where: { $0.remotePeripheral.identifier == peripheral.identifier }) {
                if let discovery = createDiscoveryForSystemConnectedPeripheral(peripheral) {
                    discoveredDevices.append(discovery)
                    print("✅ 添加系统已连接设备: \(peripheral.name ?? "未命名"), UUID: \(peripheral.identifier.uuidString)")
                } else {
                    print("⚠️ 无法为系统已连接设备创建 Discovery 对象")
                }
            }
        }
    }
    
    /// 获取系统已连接的设备
    private func fetchSystemConnectedDevices() {
        // 获取系统已连接的蓝牙设备
//        let serviceUUIDs = ourServiceUUIDs.compactMap { CBUUID(string: $0) }
//        
//        // 使用SDK内部的CBCentralManager获取已连接的设备
//        let connectedSystemDevices = getSystemConnectedDevices(withServices: serviceUUIDs)
//        
//        // 保存已连接设备的UUID
//        for peripheral in connectedSystemDevices {
//            let uuid = peripheral.identifier.uuidString
//            pairedDevices.insert(uuid)
//            print("找到系统已连接设备: \(peripheral.name ?? "未知名称") (UUID: \(uuid))")
//        }
    }
    
    /// 从系统获取已连接的设备
    private func getSystemConnectedDevices(withServices serviceUUIDs: [CBUUID]) -> [CBPeripheral] {
        // 由于SDK封装了CBCentralManager，我们通过查询系统设置来识别已连接设备
        // 先从已知设备中筛选出已连接的
        let connectedDevices = discoveredDevices.filter { discovery in
            // 检查设备是否已在系统中连接
            let deviceName = discovery.localName ?? ""
            return (deviceName.contains("Da Ring") || deviceName.contains("VRing") || deviceName.contains("Herz P1 Ring")) &&
                   isPeripheralConnectedToSystem(discovery.remotePeripheral)
        }.map { $0.remotePeripheral }
        
        return connectedDevices
    }
    
    /// 将系统已连接的设备添加到扫描结果中（如果不在扫描列表里）
//    private func addMissingSystemConnectedDevices() {
//        guard !pairedDevices.isEmpty else { return }
//        
//        for uuid in pairedDevices {
//            // 检查是否已在发现设备列表中
//            if !discoveredDevices.contains(where: { $0.remotePeripheral.identifier.uuidString == uuid }) {
//                // 尝试从系统获取该设备信息
//                if let peripheral = getPeripheralFromSystem(uuid: uuid) {
//                    // 创建一个包装的CRPDiscovery对象
//                    if let discovery = createDiscoveryFromPeripheral(peripheral) {
//                        // 标记为已配对设备
//                        markDeviceAsPaired(discovery)
//                        // 添加到发现设备列表
//                        discoveredDevices.append(discovery)
//                        print("添加系统已连接但未在扫描中发现的设备: \(peripheral.name ?? "未知") (UUID: \(uuid))")
//                    }
//                }
//            }
//        }
//    }
    
    /// 检查设备是否已连接到系统
    private func isPeripheralConnectedToSystem(_ peripheral: CBPeripheral) -> Bool {
        return peripheral.state == .connected
    }
    
    /// 从系统获取指定UUID的外设
    private func getPeripheralFromSystem(uuid: String) -> CBPeripheral? {
        // 由于SDK封装了CBCentralManager，我们使用间接方法
        // 此处简化为基于已知的连接设备
        return discoveredDevices.first { 
            $0.remotePeripheral.identifier.uuidString == uuid 
        }?.remotePeripheral
    }
    
    /// 从CBPeripheral创建CRPDiscovery对象
    private func createDiscoveryFromPeripheral(_ peripheral: CBPeripheral) -> CRPDiscovery? {
        // 尝试获取MAC地址映射
        let uuid = peripheral.identifier.uuidString
        let mac = getMacFromMapping(uuid: uuid)
        
        // 使用SDK的方法创建discovery对象（简化实现）
        let advertisementData: [String: Any] = [
            CBAdvertisementDataLocalNameKey: peripheral.name ?? "未知设备"
        ]
        
        // 创建discovery对象
        let discovery = CRPDiscovery(
            advertisementData: advertisementData as [String: AnyObject],
            remotePeripheral: peripheral,
            RSSI: 0
        )
        
        // 设置MAC地址（如果有）
        if let mac = mac {
            setValue(mac, forKey: "mac", in: discovery)
        }
        
        return discovery
    }
    
    /// 标记设备为已配对状态
    private func markDeviceAsPaired(_ discovery: CRPDiscovery) {
        // 使用运行时机制添加自定义属性
        objc_setAssociatedObject(
            discovery,
            &AssociatedKeys.isPaired,
            true,
            .OBJC_ASSOCIATION_RETAIN_NONATOMIC
        )
    }
    
    /// 检查设备是否已配对
    func isDevicePaired(_ discovery: CRPDiscovery) -> Bool {
        return (objc_getAssociatedObject(discovery, &AssociatedKeys.isPaired) as? Bool) ?? false
    }
    
    /// 判断设备是否正在充电
    /// 通过分析广播数据和设备连接状态来判断
    func isDeviceCharging(_ discovery: CRPDiscovery) -> Bool {
        // 1. 如果是当前已连接的设备，直接返回充电状态
        if connectionState.isConnected && 
           currentDiscovery?.remotePeripheral.identifier == discovery.remotePeripheral.identifier {
            return isCharging
        }
        
        // 1.5 检查是否在分析阶段被标记为充电中（使用关联对象）
        if let isChargingObj = objc_getAssociatedObject(discovery, &AssociatedKeys.isCharging) as? Bool,
           isChargingObj {
            return true
        }
        
        // 2. 检查广播数据中是否有电池信息
        if let advData = discovery.advertisementData as? [String: Any] {
            // 检查制造商数据
            if let manufacturerData = advData[CBAdvertisementDataManufacturerDataKey] as? Data,
               manufacturerData.count >= 5 {
                // 一些设备在充电时会在制造商数据中包含特定标志
                // 这里假设第5个字节的某些位表示充电状态
                let statusByte = manufacturerData[4]
                let chargingFlag = (statusByte & 0x80) != 0  // 假设最高位表示充电状态
                
                if chargingFlag {
                    print("从广播数据检测到设备正在充电: \(discovery.localName ?? "未知设备")")
                    return true
                }
            }
            
            // 检查是否有电池服务相关的数据
            if let serviceData = advData[CBAdvertisementDataServiceDataKey] as? [CBUUID: Data] {
                // 电池服务UUID
                let batteryServiceUUID = CBUUID(string: "180F")
                if let batteryData = serviceData[batteryServiceUUID], batteryData.count > 0 {
                    // 假设如果电池电量数据大于100，则表示充电中
                    if batteryData[0] > 100 {
                        print("从电池服务数据检测到设备正在充电: \(discovery.localName ?? "未知设备")")
                        return true
                    }
                }
            }
        }
        
        // 3. 检查设备名称是否包含充电指示符
        // 有些设备在充电时会在名称中添加特定标识
        if let name = discovery.localName, name.contains("[C]") || name.contains("*CHG*") {
            print("从设备名称检测到设备正在充电: \(name)")
            return true
        }
        
        // 4. 临时处理方案：根据MAC地址指定特定设备为充电状态（用于测试和演示）
        // 这里可以添加已知正在充电的设备MAC地址
        let knownChargingMACs = ["CD:19:01:33:31:F8"]  // 红色箭头指向的设备MAC
        if let mac = discovery.mac, knownChargingMACs.contains(mac) {
            print("根据已知MAC地址识别到设备正在充电: \(mac)")
            return true
        }
        
        return false
    }
    
    /// 使用KVC设置对象属性值（用于修改不可变属性）
    private func setValue(_ value: Any?, forKey key: String, in object: AnyObject) {
        if object.responds(to: Selector(key)) {
            object.setValue(value, forKey: key)
        }
    }
    
    // 关联对象的键
    private struct AssociatedKeys {
        static var isPaired = "isPaired"
        static var isCharging = "isCharging"
    }
    
    /// 判断是否应该包含该设备
    private func shouldIncludeDevice(_ discovery: CRPDiscovery) -> Bool {
        // 检查设备名称是否为空
        guard let name = discovery.localName, !name.isEmpty , discovery.mac?.count ?? 0 != 0 else {
            print("设备名称为空，忽略设备")
            return false
        }
        
        // 打印设备名称供调试
        print("发现设备名称: \(name)")
        
        if discovery.platform == 4 || discovery.platform == 7{
            print("接受设备: \(name)")
            return true  //
        }
        
        // 筛选三款戒指设备
        if name.contains("Da Ring") {
            print("接受高端设备: \(name)")
            return true  // 高端戒指
        } else if name.contains("VRing") {
            print("接受低端设备: \(name)")
            return true  // 低端戒指
        } else if name.contains("Herz P1 Ring") {
            print("接受Herz P1戒指设备: \(name)")
            return true  // Herz P1戒指
        }
        
        // 拒绝所有其他设备
        print("设备不是指定戒指设备，忽略设备: \(name)")
        return false
    }
    
    /// 从设备广播数据中提取固件类型信息
    private func extractFirmwareTypeFromDiscovery(_ discovery: CRPDiscovery) -> String? {
        // 打印所有广播数据以便调试
        print("设备广播数据: \(discovery.advertisementData)")
        
        // 1. 先检查设备名称中的关键标识
        if let name = discovery.localName {
            if name.contains("VR11") {
                // VR11很可能是R033型号
                self.firmwareType = "R033"
                return "R033"
            }
            
            // 检查设备名称中是否包含ION标识
            if name.contains("ION") || name.contains("汇顶") {
                self.firmwareType = "ION"
                return "ION"
            }
        }
        
        // 2. 检查广播数据中的固件类型字段
        if let advData = discovery.advertisementData as? [String: Any] {
            // 检查可能的固件类型键
            let possibleKeys = ["firmwareType", "firmware_type", "fwType", "fw_type"]
            for key in possibleKeys {
                if let fwType = advData[key] as? String {
                    if fwType == "ION" || fwType == "R033" {
                        self.firmwareType = fwType
                        return fwType
                    }
                }
            }
            
            // 3. 检查制造商数据
            if let manufacturerData = advData[CBAdvertisementDataManufacturerDataKey] as? Data,
               manufacturerData.count >= 3 {
                // 打印完整的制造商数据以便调试
                let hexString = manufacturerData.map { String(format: "%02X", $0) }.joined()
                print("制造商数据(HEX): \(hexString)")
                
                // 检查制造商ID
                let manufacturerId = (UInt16(manufacturerData[0]) << 8) | UInt16(manufacturerData[1])
                
                // 根据制造商ID区分不同类型
                if manufacturerId == 0x004C {
                    self.firmwareType = "ION"
                    return "ION" // 汇顶星芯片的制造商ID
                } else if manufacturerId == 0x00FF || manufacturerId == 0x01FF {
                    self.firmwareType = "R033"
                    return "R033" // 中科星芯片的制造商ID特别是R033型号
                }
            }
        }
        
        // 4. 检查版本信息
        let verStr = discovery.ver
        if verStr.contains("ION") {
            self.firmwareType = "ION"
            return "ION"
        } else if verStr.contains("R033") {
            self.firmwareType = "R033"
            return "R033"
        }
        
        // 如果没有明确匹配ION或R033，返回nil
        return nil
    }
    
    /// 停止扫描
    func stopScan() {
        guard isScanning else { return }
        CRPSmartRingManage.shared.interruptScan()
        isScanning = false
        print("停止扫描设备")
    }
    
    /// 连接到指定设备
    func connectDevice(discovery: CRPDiscovery) {
        // 如果已经连接到同一设备，不要重复连接
        if case .connected = connectionState, 
           currentDiscovery?.remotePeripheral.identifier == discovery.remotePeripheral.identifier {
            print("已经连接到此设备，忽略重复连接请求")
            return
        }
        
        // 如果正在连接中，先取消当前连接
        if case .connecting = connectionState {
            print("取消进行中的连接，开始新的连接")
            disconnectDevice()
            // 等待一小段时间再进行新的连接
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                self?.proceedWithConnect(discovery: discovery)
            }
            return
        }
        
        // 直接进行连接
        proceedWithConnect(discovery: discovery)
    }
    
    /// 执行连接过程
    private func proceedWithConnect(discovery: CRPDiscovery) {
        ///进行连接的时候，中断扫描
        CRPSmartRingManage.shared.interruptScan()
        
        connectionState = .connecting
        
        // 保存当前连接的设备引用
        self.currentDiscovery = discovery
        self.currentPeripheral = discovery.remotePeripheral
        
        let deviceDesc = "\(discovery.localName ?? "未知设备") (\(discovery.mac ?? "未知MAC"))"
        print("开始连接设备: \(deviceDesc)")
        
        // 使用SDK连接设备前先记录固件类型
        if let fwType = extractFirmwareTypeFromDiscovery(discovery) {
            print("设备固件类型: \(fwType)")
        }
        
        // 使用SDK连接设备
//        CRPSmartRingSDK.sharedInstance.connet(discovery)
        CRPSmartRingManage.shared.connect(peripheral: discovery)
        // 设置连接超时保护 - 20秒超时
        DispatchQueue.main.asyncAfter(deadline: .now() + 20) { [weak self] in
            guard let self = self else { return }
            
            // 如果20秒后仍然处于连接中状态，则认为连接超时
            if case .connecting = self.connectionState {
                let error = NSError(
                    domain: "com.windring.connection", 
                                                     code: -1001, 
                    userInfo: [NSLocalizedDescriptionKey: "连接超时，请确保设备已开启并在范围内"]
                )
                self.connectionState = .failed(error)
                print("设备连接超时: \(deviceDesc)")
                
                // 尝试再次重连一次
                if self.autoReconnect && self.connectionRetryCount < 1 {
                    print("自动重试连接...")
                    self.connectionRetryCount += 1
                    self.proceedWithConnect(discovery: discovery)
                } else {
                    self.connectionRetryCount = 0
                self.currentDiscovery = nil
                self.currentPeripheral = nil
                }
            }
        }
    }
    
    /// 断开设备连接
    func disconnectDevice(isUnbinding: Bool = false) {
        // 记录断开类型
        if isUnbinding {
            disconnectionType = .manualUnbind
            print("执行主动解除绑定操作")
            
            // 记录解绑时间
            UserDefaults.standard.set(Date(), forKey: "lastUnbindTimestamp")
            
            // 在解绑时，如果有当前设备信息，备份设备与MAC地址关系
            if let peripheral = currentDiscovery?.remotePeripheral, 
               let mac = lastConnectedDeviceMAC, !mac.isEmpty {
                let uuid = peripheral.identifier.uuidString
                
                // 先保存映射，以便今后匹配使用
                saveDeviceMapping(uuid: uuid, mac: mac)
                print("设备解绑前保存设备映射: UUID=\(uuid), MAC=\(mac)")

            // 清除MAC地址，防止自动重连
            saveUserDeviceInfo(mac: nil, nickname: userDeviceNickname)
            lastConnectedDeviceMAC = nil
        } else {
            // 无法直接获取当前蓝牙状态，根据最近状态变化推断
            disconnectionType = .passive
            print("执行被动断开连接，保留设备信息，以便自动重连")
        }
        
        lastDisconnectionTime = Date()
        
        // 断开连接前，先取消所有测量任务
        if isMeasuringHeartRate { stopHeartRateMeasurement() }
        if isMeasuringSpO2 { stopSpO2Measurement() }
        if isMeasuringHRV { stopHRVMeasurement() }
        if isMeasuringTemperature { stopTemperatureMeasurement() }
        if isMeasuringBloodPressure { stopBloodPressureMeasurement() }
        if isMeasuringStress { stopStressMeasurement() }
        
        // 使用SDK正确的断开连接方法
        CRPSmartRingSDK.sharedInstance.disConnectGoodix()
//        currentDiscovery
        if let peripheral = currentDiscovery?.remotePeripheral{
            BluetoothSystemDeviceManager.shared.centralManager.cancelPeripheralConnection(peripheral)
        }
        
        
        // 立即更新状态
        connectionState = .disconnected
        
        // 如果是主动解绑，清除设备引用
        if isUnbinding {
            CRPSmartRingManage.shared.disconnectAndRemoveBinding{ state, error in
                
            }
            
            let userAPIService = UserAPIService(networkManager: NetworkManager.shared)
            userAPIService.unbindDevice()
                .sink { completion in
                    if case .failure(let error) = completion {
                        print("❌ 解绑设备请求失败: \(error.localizedDescription)")
                    } else {
                        print("✅ 解绑设备请求完成")
                    }
                } receiveValue: { response in
                    if response.code == 0 {
                        print("✅ 服务端解绑成功")
                    } else {
                        print("❌ 服务端解绑失败: \(response.msg ?? "未知错误")")
                    }
                }
                .store(in: &cancellables)
        }
            
            deviceInfo = nil
            currentDiscovery = nil
            currentPeripheral = nil
        }
        
        print("📱 设备连接已断开")
        
        // 发送通知
        NotificationCenter.default.post(name: .deviceDisconnected, object: nil)
        print("📱 已发送设备已断开通知: deviceDisconnected")
        
        // 同时发送与UserProfileView相关的通知
        NotificationCenter.default.post(name: NSNotification.Name("DeviceConnectionStateChanged"), object: nil)
        print("📱 已发送设备连接状态变化通知: DeviceConnectionStateChanged")
    }
    
    /// 完全解除设备绑定
    /// 此方法将彻底清除所有设备相关信息，确保设备不会自动重连
    func unbindDeviceCompletely() {
        print("执行彻底解除设备绑定操作")
        
        // 记录断开类型为主动解绑
        disconnectionType = .manualUnbind
        
        // 记录解绑时间
        UserDefaults.standard.set(Date(), forKey: "lastUnbindTimestamp")
        
        // 断开当前连接
        CRPSmartRingSDK.sharedInstance.disConnectGoodix()
        
        // 清除所有设备相关信息
        lastConnectedDeviceMAC = ""
        userDeviceNickname = ""
        
        // 清除用户设置中的设备信息
        saveUserDeviceInfo(mac: nil, nickname: nil)
        
        // 关闭自动重连功能
        autoReconnect = false
        UserDefaults.standard.set(false, forKey: "autoReconnectEnabled")
        
        // 清除设备引用
        currentDiscovery = nil
        currentPeripheral = nil
        
        // 更新连接状态
        connectionState = .disconnected
        
        // 发送解绑成功通知
        NotificationCenter.default.post(name: .deviceDisconnected, object: nil)
        NotificationCenter.default.post(name: NSNotification.Name("DeviceUnbindCompleted"), object: nil)
        NotificationCenter.default.post(name: NSNotification.Name("DeviceConnectionStateChanged"), object: nil)
        
        print("📱 设备已彻底解除绑定，所有设备信息已清除")
    }
    
    /// 根据连接状态自动尝试重连最后的设备
    func attemptAutoReconnect() {
        // 检查是否应该自动重连
        if !shouldAttemptAutoReconnect() {
            print("不符合自动重连条件，跳过自动重连")
        
        // 终止任何正在进行的自动扫描
        if isAutoScanning {
            isAutoScanning = false
            stopScan()
        }
            return
        }
        
        // 执行自动重连
        print("尝试自动重连上次的设备")
        
        // 如果没有上次设备的MAC地址，则无法重连
        guard let lastMAC = lastConnectedDeviceMAC, !lastMAC.isEmpty else {
            print("没有保存上次设备的MAC地址，无法自动重连")
            return
        }
        
        // 如果已在扫描，直接返回
        if isScanning {
            print("正在扫描中，不重复启动扫描")
            return
        }
        
        // 开始自动扫描
        isAutoScanning = true
        print("启动自动扫描来查找设备: \(lastMAC)")
        
        // 清空发现设备列表
        discoveredDevices = []
        
        // 开始扫描
        startScan(duration: 10)
        
        // 设置超时，避免一直扫描
        DispatchQueue.main.asyncAfter(deadline: .now() + 15) { [weak self] in
            guard let self = self, self.isAutoScanning else { return }
            
            print("自动扫描超时，停止扫描")
            self.isAutoScanning = false
            self.stopScan()
            
            // 检查是否找到了目标设备
            if let targetDevice = self.discoveredDevices.first(where: { $0.mac?.uppercased() == lastMAC.uppercased() }) {
                print("找到目标设备(MAC匹配)，尝试连接: \(targetDevice.localName ?? "未知设备") (\(targetDevice.mac ?? "未知MAC"))")
                self.connectDevice(discovery: targetDevice)
            } else {
                // 如果找不到MAC匹配的设备，检查是否有通过UUID映射表关联的设备
                let lastUUIDs = self.getUUIDsForMAC(mac: lastMAC)
                
                if !lastUUIDs.isEmpty {
                    print("尝试通过UUID匹配查找设备...")
                    
                    for uuid in lastUUIDs {
                        if let targetByUUID = self.discoveredDevices.first(where: { 
                            $0.remotePeripheral.identifier.uuidString == uuid 
                        }) {
                            print("找到目标设备(UUID匹配)，尝试连接: \(targetByUUID.localName ?? "未知设备") (UUID: \(uuid))")
                            self.connectDevice(discovery: targetByUUID)
                            return
                        }
                    }
                }
                
                // 如果还是找不到，尝试连接信号最强的设备
                if !self.discoveredDevices.isEmpty && self.connectionRetryCount > 2 {
                    let strongestDevice = self.discoveredDevices.first!
                    print("未找到目标设备，尝试连接信号最强的设备: \(strongestDevice.localName ?? "未知设备")")
                    self.connectDevice(discovery: strongestDevice)
                } else {
                    print("未找到目标设备，自动重连失败")
                    
                    // 增加重试次数并在一段时间后再尝试重连
                    self.connectionRetryCount += 1
                    if self.connectionRetryCount < 3 {
                        let retryDelay = Double(self.connectionRetryCount * 5) // 递增延迟时间
                        print("将在\(retryDelay)秒后进行第\(self.connectionRetryCount)次重连尝试")
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + retryDelay) { [weak self] in
                            guard let self = self, !self.connectionState.isConnected else { return }
                            self.attemptAutoReconnect()
                        }
                    }
                }
            }
        }
    }
    
    /// 判断是否应该尝试自动重连
    private func shouldAttemptAutoReconnect() -> Bool {
        // 1. 如果是主动解绑，不自动重连
        if disconnectionType == .manualUnbind {
            print("不自动重连：用户主动解除了绑定")
            return false
        }
        
        // 2. 检查最近是否有解绑记录
        if let unbindTimestamp = UserDefaults.standard.object(forKey: "lastUnbindTimestamp") as? Date {
            // 如果2分钟内执行过解除绑定操作，则不自动重连
            if Date().timeIntervalSince(unbindTimestamp) < 120 {
                print("不自动重连：用户最近执行了解除绑定操作")
                return false
            }
        }
        
        // 3. 如果自动重连功能被禁用，不重连
        if !autoReconnect {
            print("不自动重连：自动重连功能已禁用")
            return false
        }
        
        // 4. 如果没有之前的设备MAC地址，不重连
        if lastConnectedDeviceMAC == nil || lastConnectedDeviceMAC?.isEmpty == true {
            print("不自动重连：没有保存之前设备的MAC地址")
            return false
        }
        
        // 5. 如果设备当前已连接，不需要重连
        if connectionState.isConnected {
            print("不自动重连：设备connected".localized)
            return false
        }
        
        // 6. 如果是系统原因断开连接（如蓝牙关闭），不立即尝试重连
        if disconnectionType == .systemRequest {
            // 只有当蓝牙再次开启时，在蓝牙状态改变的回调中再尝试重连
            print("不自动重连：由系统原因断开连接")
            return false
        }
        
        // 默认允许重连
        return true
    }
    
    // MARK: - 数据同步
    /// 同步时间
    func syncTime() {
        guard case .connected = connectionState else {
            print("设备未连接，无法同步时间")
            return
        }
        
        CRPSmartRingSDK.sharedInstance.setTime()
        print("时间同步已发送")
    }
    
    /// 获取电池电量
    func getBatteryLevel() {
        guard case .connected = connectionState else {
            print("设备未连接，无法获取电量")
            return
        }
        CRPSmartRingManage.shared.getRingBatteryLevel {[weak self] level, error in
            DispatchQueue.main.async {
                if error == .none {
                    if level <= 100 {
                        self?.batteryLevel = level
                        self?.isCharging = false
                        print("设备电量: \(level)%")
                    } else if level >= 200 {
                        self?.batteryLevel = 100
                        self?.isCharging = true
                        print("设备电量: \(100)%，并且充电中")
                        
                    } else {
                        self?.batteryLevel = level % 100
                        self?.isCharging = true
                        print("设备正在充电，电量: \(level % 100)%")
                    }
                } else {
                    print("获取电量失败: \(error)")
                }
            }
        }
    }
    
    /// 获取固件版本
    func getFirmwareVersion(completion: ((String) -> Void)? = nil) {
        guard case .connected = connectionState else {
            print("设备未连接，无法获取固件版本")
            completion?("")
            return
        }
//        CRPSmartRingManage.shared.getSoftver
        CRPSmartRingSDK.sharedInstance.getSoftver { version, error in
            DispatchQueue.main.async {
                if error == .none {
                    print("固件版本: \(version)")
                    
                    completion?(version)
                    
                    // 如果设备信息已存在，则更新版本信息
                    if var info = self.deviceInfo {
                        info.firmwareVersion = version
                        self.deviceInfo = info
                    }
                    
                    // 分析固件版本获取类型
                    self.analyzeFirmwareType(version)
                } else {
                    print("获取固件版本失败: \(error)")
                    completion?("")
                }
            }
        }
    }
    
    /// 分析固件版本获取固件类型
    private func analyzeFirmwareType(_ version: String) {
        // 打印详细日志
        print("开始分析固件类型，固件版本: \(version)")
        
        // 从版本字符串中查找可能的固件类型代码
        let versionComponents = version.split(separator: " ")
        for component in versionComponents {
            let componentStr = String(component)
            
            // 检查I开头的型号
            if componentStr.hasPrefix("I") {
                updateFirmwareType(componentStr)
                return
            } 
            // 检查R开头的型号
            else if componentStr.hasPrefix("R") {
                updateFirmwareType(componentStr)
                return
            }
        }
        
        // 简单检查字符串中是否包含I或R作为前缀的子串
        if let range = version.range(of: "I[A-Za-z0-9]{2,}", options: .regularExpression) {
            let firmwareCode = String(version[range])
            updateFirmwareType(firmwareCode)
            return
        }
        
        if let range = version.range(of: "R[A-Za-z0-9]{2,}", options: .regularExpression) {
            let firmwareCode = String(version[range])
            updateFirmwareType(firmwareCode)
            return
        }
        
        // 如果没有找到完整型号，但版本字符串中包含I或R，则使用简化类型
        if version.contains("I") {
            updateFirmwareType("I型固件")
            return
        }
        
        if version.contains("R") {
            updateFirmwareType("R型固件")
            return
        }
        
        // 无法通过版本号确定，尝试通过GoMore支持状态确定
        if let goMoreService = GoMoreService.shared as? GoMoreService {
            print("通过GoMore支持状态确定固件类型")
            goMoreService.checkGoMoreSupport { [weak self] isSupported, _ in
                guard let self = self else { return }
                
                if isSupported {
                    // 完全支持通常是I型固件
                    self.updateFirmwareType("I型固件")
                } else {
                    // 不支持通常是R型固件
                    self.updateFirmwareType("R型固件")
                }
            }
        } else {
            // 默认为未知类型
            updateFirmwareType("UNKNOWN")
        }
    }
    
    /// 更新固件类型
    private func updateFirmwareType(_ type: String) {
        DispatchQueue.main.async {
            // 只有当类型不同时才更新
            if self.firmwareType != type {
                let oldType = self.firmwareType
                self.firmwareType = type
                print("固件类型更新为: \(type)")
                
                // 发送固件类型更新通知
                NotificationCenter.default.post(
                    name: .firmwareTypeUpdated,
                    object: nil,
                    userInfo: ["type": type]
                )
                
                // 处理睡眠算法变更
                let oldAlgorithm = self.getSleepAlgorithmType(firmwareType: oldType)
                let newAlgorithm = self.getSleepAlgorithmType(firmwareType: type)
                
                if oldAlgorithm != newAlgorithm {
                    print("睡眠算法从 \(oldAlgorithm) 切换为 \(newAlgorithm)")
                    // 发送睡眠算法变更通知
                    NotificationCenter.default.post(
                        name: .sleepAlgorithmChanged,
                        object: nil,
                        userInfo: ["algorithmType": newAlgorithm]
                    )
                }
            }
        }
    }
    
    /// 根据固件类型获取睡眠算法类型
    private func getSleepAlgorithmType(firmwareType: String) -> String {
        if firmwareType == "ION" {
            return "GoMore高级睡眠算法"
        } else if firmwareType == "R033" {
            return "基础睡眠算法"
        } else {
            return "基础睡眠算法(默认)"
        }
    }
    
    /// 获取MAC地址
    func getMacAddress(completion: ((String) -> Void)? = nil) {
        guard case .connected = connectionState else {
            print("设备未连接，无法获取MAC地址")
            completion?("")
            return
        }

        
        let mac = getTrustedMacAddress(forDeviceName: deviceRealMacsKey)
        if  mac == nil || mac?.count == 0 || deviceInfo?.mac?.count == 0 {
            CRPSmartRingManage.shared.getRingMacAddress { mac, error in
                DispatchQueue.main.async {
                    if error == .none && !mac.isEmpty && self.isValidMacAddress(mac) {
                        print("SDK提供的MAC地址: \(mac)")
                        
                        // 如果设备信息已存在，则更新MAC地址
                        if let info = self.deviceInfo {
                            info.mac = mac
                            self.deviceInfo = info
                        }
                        
                        completion?(mac)
                        return
                    } else {
                        print("获取MAC地址失败: \(error)")
                        completion?("")
                        return
                    }
                }
            }
            
            return
        }else{
            completion?(mac!)
        }
        
        // 如果没有缓存的MAC地址，尝试获取真实的物理MAC地址
//        getRealMacAddress { realMac in
//            if !realMac.isEmpty {
//                // 如果成功获取真实MAC地址，使用它
//                DispatchQueue.main.async {
//                    print("获取到真实MAC地址: \(realMac)")
//                    
//                    // 如果设备信息已存在，则更新MAC地址
//                    if var info = self.deviceInfo {
//                        info.mac = realMac
//                        self.deviceInfo = info
//                    }
//                    
//                    // 如果有设备名称，缓存这个MAC地址
//                    if let deviceName = self.currentDiscovery?.localName {
//                        self.saveTrustedMacAddress(name: deviceName, mac: realMac)
//                    }
//                    
//                    completion?(realMac)
//                }
//            } else {
//                // 如果获取真实MAC失败，回退到普通方法
//                print("无法获取真实物理MAC地址，使用SDK接口")
//                CRPSmartRingManage.shared.getRingMacAddress { mac, error in
//                    DispatchQueue.main.async {
//                        if error == .none && !mac.isEmpty && self.isValidMacAddress(mac) {
//                            print("SDK提供的MAC地址: \(mac)")
//                            
//                            // 如果设备信息已存在，则更新MAC地址
//                            if let info = self.deviceInfo {
//                                info.mac = mac
//                                self.deviceInfo = info
//                            }
//                            
//                            // 如果有设备名称，缓存这个MAC地址
//                            if let deviceName = self.currentDiscovery?.localName {
//                                self.saveTrustedMacAddress(name: deviceName, mac: mac)
//                            }
//                            
//                            completion?(mac)
//                        } else {
//                            print("获取MAC地址失败: \(error)")
//                            completion?("")
//                        }
//                    }
//                }
//                
//                CRPSmartRingManage.shared.getGitHashInfo { value, error in
//                    if let info = self.deviceInfo {
//                        info.serial = value
//                        self.deviceInfo = info
//                    }
//                    print("序列号serial:\(value)")
//                }
//            }
//        }
    }
    
    /// 尝试获取设备的真实物理MAC地址
    /// 注意：在iOS上，由于系统限制，可能无法直接获取真实MAC地址
    private func getRealMacAddress(completion: @escaping (String) -> Void) {
        guard case .connected = connectionState,
              let peripheral = currentDiscovery?.remotePeripheral else {
            print("设备未连接或无效，无法获取真实MAC地址")
            completion("")
            return
        }
        
        print("正在尝试获取设备真实物理MAC地址...")
        CRPSmartRingManage.shared.getRingMacAddress { value, error in
            completion(value)
            return
        }
        
        // 先检查当前设备是否有缓存的MAC地址
//        if let deviceName = currentDiscovery?.localName,
//           let cachedMac = getTrustedMacAddress(forDeviceName: deviceName) {
//            print("使用缓存的可信MAC地址: \(cachedMac)")
//            completion(cachedMac)
//            return
//        }
        
        // 方法1: 尝试从广告数据中获取
//        if let discovery = currentDiscovery, 
//           let mac = discovery.mac, 
//           !mac.isEmpty, 
//           mac != "auto_connected_device",
//           isValidMacAddress(mac) {
//            print("从广告数据中获取到MAC地址: \(mac)")
//            
//            // 缓存这个MAC地址
//            if let deviceName = currentDiscovery?.localName {
//                saveTrustedMacAddress(name: deviceName, mac: mac)
//            }
//            
//            completion(mac)
//            return
//        }
//        
//        // 如果无法获取真实MAC地址，返回空
//        print("无法获取真实MAC地址")
//        completion("")
    }
    
    /// 将UUID标识符格式化为类似MAC地址的格式 (仅用于调试)
    private func formatIdentifierAsMac(_ identifier: String) -> String {
        // 去除所有非字母数字字符，并转换为大写
        let cleaned = identifier.components(separatedBy: CharacterSet.alphanumerics.inverted).joined().uppercased()
        
        // 如果长度合适，则格式化为类似MAC地址的格式 (XX:XX:XX:XX:XX:XX)
        if cleaned.count >= 12 {
            let prefix = String(cleaned.prefix(12))
            var formatted = ""
            for (index, char) in prefix.enumerated() {
                formatted.append(char)
                if index % 2 == 1 && index < prefix.count - 1 {
                    formatted.append(":")
                }
            }
            return formatted
        }
        
        // 返回原始标识符
        return identifier
    }
    
    /// 获取戒指信息
    func getRingInfo(completion: ((RingInfo?) -> Void)? = nil) {
        guard case .connected = connectionState else {
            print("设备未连接，无法获取戒指信息")
            return
        }
        CRPSmartRingManage.shared.getRingConfigurationInfo {[weak self] info, error in
            DispatchQueue.main.async {
                if error == .none {
                    print("戒指信息 - 颜色: \(info.color), 尺寸: \(info.size), 类型: \(info.type)")
                    
                    // 更新设备信息
                    if var deviceInfo = self?.deviceInfo {
                        deviceInfo.info = info
                        self?.deviceInfo = deviceInfo
                        completion?(deviceInfo)
                    }
                } else {
                    print("获取戒指信息失败: \(error)")
                }
            }
        }
    }
    
    /// 获取佩戴状态
    func getWearingState() {
        guard case .connected = connectionState else {
            print("设备未连接，无法获取佩戴状态")
            return
        }
        CRPSmartRingManage.shared.getWearState()
        
//        CRPSmartRingSDK.sharedInstance.getWearState()
        // 结果通过代理方法receiveWearState返回
    }
    
    /// 初始化设备信息
    func initializeDeviceInfo(discovery: CRPDiscovery) {
//        deviceInfo = RingInfo(
//            name: discovery.kCABAdvidataLocalName ?? "未知设备",
//            macAddress: discovery.mac ?? "",
//            firmwareVersion: "",
////            ringInfo: nil,
//            hardwareVersion: nil
//        )
    }
    
    // MARK: - 健康测量
    /// 开始心率测量
    public func startHeartRateMeasurement() {
        guard case .connected = connectionState else {
            print("设备未连接，无法开始心率测量")
            return
        }
        
        if !isMeasuringHeartRate {
            CRPSmartRingManage.shared.startSingleHeartRateMeasurement()
//            CRPSmartRingSDK.sharedInstance.setStartSingleHR() // 修正为正确的SDK方法
            isMeasuringHeartRate = true
            print("开始心率测量")
            
            // 模拟心率测量结果
            #if DEBUG
//            DispatchQueue.global().asyncAfter(deadline: .now() + 3.0) { [weak self] in
//                guard let self = self else { return }
//                
//                if self.isMeasuringHeartRate {
//                    let heartRate = Int.random(in: 65...85)
//                    self.lastHeartRate = heartRate
//                    
//                    // 发送通知
//                    NotificationCenter.default.post(
//                        name: .heartRateMeasured,
//                        object: self,
//                        userInfo: ["value": heartRate]
//                    )
//                    
//                    print("模拟心率测量结果: \(heartRate) bpm")
//                }
//            }
            #endif
        }
    }
    
    /// 停止心率测量
    public func stopHeartRateMeasurement() {
        guard case .connected = connectionState else {
            print("设备未连接，无法停止心率测量")
            return
        }
        
        if isMeasuringHeartRate {
            isMeasuringHeartRate = false
            CRPSmartRingManage.shared.stopSingleHeartRateMeasurement() //.sharedInstance.setStopSingleHR() // 修正为正确的SDK方法
            print("停止心率测量")
        }
    }
    
    /// 设置定时心率测量
    /// - Parameter interval: 测量间隔(0=关闭,1=5分钟,2=10分钟,3=15分钟,6=30分钟,12=1小时)
    func setTimingHeartRateMeasurement(interval: Int) {
        guard case .connected = connectionState else {
            print("设备未连接，无法设置定时心率测量")
            return
        }
        
        if interval == 0 {
            print("关闭定时心率测量")
        } else {
            let minutes = interval * 5
            print("设置定时心率测量: 每\(minutes)分钟")
        }
        CRPSmartRingManage.shared.setTimingHeartRateInterval(interval: interval)
        
        // 保存设置到UserDefaults
        UserDefaults.standard.set(interval, forKey: "timingHeartRateInterval")
    }
    
    /// 获取定时心率测量状态
    func getTimingHeartRateMeasurementStatus(completion: @escaping (Int, Error?) -> Void) {
        guard case .connected = connectionState else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        CRPSmartRingManage.shared.getTimingHeartRateInterval { interval, error in
            DispatchQueue.main.async {
                if error == .none {
                    completion(interval, nil)
                } else {
                    completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取定时心率测量状态失败"]))
                }
            }
        }
    }
    
    
    
    /// 开始血氧测量
    func startSpO2Measurement() {
        guard connectionState.isConnected else {
            print("设备未连接，无法开始血氧测量")
            return
        }
        
        print("开始血氧测量...")
        isMeasuringSpO2 = true
        CRPSmartRingManage.shared.startSpO2Measurement()
//        CRPSmartRingSDK.sharedInstance.setStartSpO2()
    }
    
    /// 停止血氧测量
    func stopSpO2Measurement() {
        guard isMeasuringSpO2 else { return }
        
        print("停止血氧测量")
        isMeasuringSpO2 = false
        CRPSmartRingManage.shared.stopSpO2Measurement()
//        CRPSmartRingSDK.sharedInstance.setStopSpO2()
    }
    
    /// 设置定时血氧测量
    /// - Parameter interval: 测量间隔(0=关闭,1=5分钟,2=10分钟,3=15分钟,6=30分钟,12=1小时)
    func setTimingSpO2Measurement(interval: Int) {
        guard case .connected = connectionState else {
            print("设备未连接，无法设置定时血氧测量")
            return
        }
        
        if interval == 0 {
            print("关闭定时血氧测量")
        } else {
            let minutes = interval * 5
            print("设置定时血氧测量: 每\(minutes)分钟")
        }
        CRPSmartRingManage.shared.setTimingO2Interval(interval: interval)
        
        // 保存设置到UserDefaults
        UserDefaults.standard.set(interval, forKey: "timingSpO2Interval")
    }
    
    /// 获取定时血氧测量状态
    func getTimingSpO2MeasurementStatus(completion: @escaping (Int, Error?) -> Void) {
        guard case .connected = connectionState else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        CRPSmartRingManage.shared.getTimingO2Interval { interval, error in
            DispatchQueue.main.async {
                if error == .none {
                    completion(interval, nil)
                } else {
                    completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取定时血氧测量状态失败"]))
                }
            }
        }
    }
    
    /// 设置定时HRV测量
    /// - Parameter interval: 测量间隔(0=关闭,1=5分钟,2=10分钟,3=15分钟,6=30分钟,12=1小时)
    func setTimingHRVMeasurement(interval: Int) {
        guard case .connected = connectionState else {
            print("设备未连接，无法设置定时HRV测量")
            return
        }
        
        if interval == 0 {
            print("关闭定时HRV测量")
        } else {
            let minutes = interval * 5
            print("设置定时HRV测量: 每\(minutes)分钟")
        }
        CRPSmartRingManage.shared.setTimingHRVInterval(interval: interval)
        
        // 保存设置到UserDefaults
        UserDefaults.standard.set(interval, forKey: "timingHRVInterval")
    }
    
    /// 获取定时HRV测量状态
    func getTimingHRVMeasurementStatus(completion: @escaping (Int, Error?) -> Void) {
        guard case .connected = connectionState else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        CRPSmartRingManage.shared.getTimingHRVInterval { interval, error in
            DispatchQueue.main.async {
                if error == .none {
                    completion(interval, nil)
                } else {
                    completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取定时HRV测量状态失败"]))
                }
            }
        }
    }
    
    /// 开始体温测量
    func startTemperatureMeasurement() {
        guard case .connected = connectionState else {
            print("设备未连接，无法开始体温测量")
            return
        }
        
        isMeasuringTemperature = true
        CRPSmartRingManage.shared.setStartTemperature()
        print("开始体温测量")
    }
    
    /// 停止体温测量
    func stopTemperatureMeasurement() {
        guard case .connected = connectionState else {
            print("设备未连接，无法停止体温测量")
            return
        }
        
        isMeasuringTemperature = false
        CRPSmartRingManage.shared.setStopTemperature()
//        CRPSmartRingSDK.sharedInstance.setStopTemperature()
        print("停止体温测量")
    }
    
    /// 开始血压测量
    func startBloodPressureMeasurement() {
        guard case .connected = connectionState else {
            print("设备未连接，无法开始血压测量")
            return
        }
        
        isMeasuringBloodPressure = true
        CRPSmartRingManage.shared.startSingleBloodPressureMeasurement()
//        CRPSmartRingSDK.sharedInstance.setStartBP()
        print("开始血压测量")
    }
    
    /// 停止血压测量
    func stopBloodPressureMeasurement() {
        guard case .connected = connectionState else {
            print("设备未连接，无法停止血压测量")
            return
        }
        
        isMeasuringBloodPressure = false
        CRPSmartRingManage.shared.stopSingleBloodPressureMeasurement()
//        CRPSmartRingSDK.sharedInstance.setStopBP()
        print("停止血压测量")
    }
    
    /// 开始压力测量
    func startStressMeasurement() {
        guard case .connected = connectionState else {
            print("设备未连接，无法开始压力测量")
            return
        }
        
        // 确保没有其他测量正在进行
        if isMeasuringHeartRate || isMeasuringSpO2 || 
           isMeasuringTemperature || isMeasuringBloodPressure {
            print("其他测量正在进行，无法开始压力测量")
            return
        }
        
        isMeasuringStress = true
        CRPSmartRingManage.shared.startStressMeasurement()
//        CRPSmartRingSDK.sharedInstance.setStartStress()
        print("开始压力测量")
        
        // 设置测量超时保护
        DispatchQueue.main.asyncAfter(deadline: .now() + 30) { [weak self] in
            guard let self = self, self.isMeasuringStress else { return }
            
            print("压力测量30秒超时保护触发")
            self.stopStressMeasurement()
            
            // 发送测量超时通知
            NotificationCenter.default.post(
                name: .stressMeasurementTimeout,
                object: nil
            )
        }
    }
    
    /// 停止压力测量
    func stopStressMeasurement() {
        guard case .connected = connectionState else {
            print("设备未连接，无法停止压力测量")
            return
        }
        
        if isMeasuringStress {
            isMeasuringStress = false
            CRPSmartRingManage.shared.stopStressMeasurement()
//            CRPSmartRingSDK.sharedInstance.setStopStress()
            print("停止压力测量")
        }
    }
    
    /// 开始HRV测量
    func startHRVMeasurement() {
        guard connectionState.isConnected else {
            print("设备未连接，无法开始HRV测量")
            return
        }
        
        print("开始HRV测量...")
        isMeasuringHRV = true
        CRPSmartRingManage.shared.startHRVMeasurement()
        CRPSmartRingSDK.sharedInstance.setStartHRV()
    }
    
    /// 停止HRV测量
    func stopHRVMeasurement() {
        guard isMeasuringHRV else { return }
        
        print("停止HRV测量")
        isMeasuringHRV = false
        CRPSmartRingManage.shared.stopHRVMeasurement()
//        CRPSmartRingSDK.sharedInstance.setStopHRV()
    }
    
    // MARK: - 设备状态更新
    
    /// 更新设备状态
    private func updateDeviceStatus() {
        guard let deviceInfo = deviceInfo, let mac = deviceInfo.mac else {
            return
        }
        
        // 构建设备状态数据
        let statusData: [String: Any] = [
            "device_id": mac,
            "battery_level": batteryLevel,
            "wearing_state": wearingState,
            "connection_state": connectionState.rawValue,
            "firmware_version": deviceInfo.firmwareVersion ?? "",
//            "hardware_version": deviceInfo.hardwareVersion ?? "",
            "last_updated": Date().timeIntervalSince1970
        ]
        
        // 发送到MQTT
        mqttSyncService?.cacheDeviceStatus(ringId: mac, status: statusData)
    }
    
    /// 更新健康数据
    private func updateHealthData(type: String, value: Any) {
        guard let deviceInfo = deviceInfo, let deviceId = deviceInfo.mac else {
            return
        }
        
        // 构建健康数据
        let healthData: [String: Any] = [
            "device_id": deviceId,
            "type": type,
            "value": value,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        // 发送到MQTT
        mqttSyncService?.cacheHealthData(ringId: deviceId, data: healthData)
    }
    
    // MARK: - 设备回调
    
    /// 设备连接状态变化回调
    func deviceConnectionStateChanged(_ state: ConnectionState) {
        // 更新设备状态
        updateDeviceStatus()
        
        // 发送连接状态通知
        if case .connected = state {
            // 发送已连接通知
//            NotificationCenter.default.post(name: .deviceConnected, object: nil)
        }
        
        // 额外延迟发送通知，确保UI能够正确更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            NotificationCenter.default.post(name: NSNotification.Name("DeviceConnectionStateChanged"), object: nil)
            print("📱 延迟发送设备连接状态变化通知")
        }
    }
    
    /// 电池电量变化回调
    func batteryLevelChanged(_ level: Int) {
        // 更新设备状态
        updateDeviceStatus()
    }
    
    /// 佩戴状态变化回调
    func wearingStateChanged(_ state: Int) {
        // 更新设备状态
        updateDeviceStatus()
    }
    
    /// 心率数据回调
    func heartRateDataReceived(_ heartRate: Int) {
        // 更新健康数据
        updateHealthData(type: "heart_rate", value: heartRate)
        
        // 发送心率测量结果通知
        // 区分手动单次测量和自动定时测量
        if isMeasuringHeartRate {
            // 手动测量
            NotificationCenter.default.post(
                name: .heartRateMeasured,
                object: nil,
                userInfo: ["value": heartRate, "timestamp": Date()]
            )
            print("📡 已发送手动心率测量结果通知: \(heartRate) bpm")
        } else {
            // 自动定时测量
            NotificationCenter.default.post(
                name: .autoHeartRateMeasured,
                object: nil,
                userInfo: ["value": heartRate, "timestamp": Date()]
            )
            print("📡 已发送自动定时心率测量结果通知: \(heartRate) bpm")
            
            // 保存到本地数据库
            if let userId = UserDefaults.standard.string(forKey: "userId") ?? AuthService.shared.currentUser?.id,
               let deviceId = deviceInfo?.mac {
                HealthDataManager.shared.addHeartRate(
                    userId: userId,
                    value: Int16(heartRate),
                    timestamp: Date(),
                    deviceId: deviceId,
                    confidence: 100
                )
                print("📝 已自动保存定时心率数据: \(heartRate) bpm")
            } else {
                print("⚠️ 无法保存定时心率数据，缺少userId或deviceId")
            }
        }
    }
    
    /// 血氧数据回调
    func bloodOxygenDataReceived(_ bloodOxygen: Int) {
        // 更新健康数据
        updateHealthData(type: "blood_oxygen", value: bloodOxygen)
    }
    
    /// 体温数据回调
    func temperatureDataReceived(_ temperature: Double) {
        // 更新健康数据
        updateHealthData(type: "temperature", value: temperature)
    }
    
    /// 步数数据回调
    func stepsDataReceived(_ steps: Int) {
        // 更新健康数据
        updateHealthData(type: "steps", value: steps)
    }
    
    /// 睡眠数据回调
    func sleepDataReceived(_ sleepData: CRPSleepRecordModel) {
        // 转换为我们自己的SleepData结构
        let sleepDataConverted = SleepData(from: sleepData)
        
        // 构建睡眠数据字典
        let sleepDict: [String: Any] = [
            "start_time": sleepDataConverted.startTime.timeIntervalSince1970,
            "end_time": sleepDataConverted.endTime.timeIntervalSince1970,
            "deep_sleep": sleepDataConverted.deepSleepMinutes,
            "light_sleep": sleepDataConverted.lightSleepMinutes,
            "rem_sleep": sleepDataConverted.remSleepMinutes,
            "awake": sleepDataConverted.awakeMinutes,
            "quality": sleepDataConverted.sleepQuality
        ]
        
        // 更新健康数据
        updateHealthData(type: "sleep", value: sleepDict)
    }
    
    /// 血压数据回调
    func bloodPressureDataReceived(_ sbp: Int, _ dbp: Int) {
        // 更新健康数据
        updateHealthData(type: "blood_pressure", value: (systolic: sbp, diastolic: dbp))
    }
    
    // MARK: - 睡眠算法相关方法

    /// 获取睡眠数据，根据设备支持情况自动选择算法
    func getSleepData(day: Int, completion: @escaping (SleepData?, Error?) -> Void) {
        guard case .connected = connectionState else {
            print("设备未连接，无法获取睡眠数据")
            completion(nil, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        // 检查设备是否支持GoMore算法
        let goMoreService = GoMoreService.shared
        if goMoreService.isGoMoreSupported {
            // 设备支持GoMore算法
            print("设备支持GoMore算法，使用GoMore高级睡眠算法获取数据")
            getGoMoreSleepData(day: day, completion: completion)
        } else {
            // 设备不支持GoMore算法，使用基础算法
            print("设备不支持GoMore算法，使用基础睡眠算法获取数据")
            getBasicSleepData(day: day, completion: completion)
        }
    }

    /// 使用GoMore高级睡眠算法获取睡眠数据
    private func getGoMoreSleepData(day: Int, completion: @escaping (SleepData?, Error?) -> Void) {
        print("正在获取GoMore高级睡眠数据...")
        
        // 检查GoMore算法服务
        let goMoreService = GoMoreService.shared
        
        // 先确保GoMore算法支持
        goMoreService.checkGoMoreSupport { [weak self] isSupported, error in
            guard let self = self else { return }
            
            if !isSupported {
                print("设备不支持GoMore算法，降级使用基础睡眠算法")
                self.getBasicSleepData(day: day, completion: completion)
                return
            }
            
            // 标记用于跟踪是否已经处理过数据
            var isDataProcessed = false
            
            // 创建一个超时任务
            let timeoutTask = DispatchWorkItem { [weak self] in
                guard let self = self, !isDataProcessed else { return }
                
                print("⚠️ 获取GoMore睡眠数据列表超时")
                print("降级使用基础睡眠算法")
                isDataProcessed = true
                
                // 移除通知观察者
                NotificationCenter.default.removeObserver(self, name: .receivedGoMoreSleepIdsNotification, object: nil)
                
                // 降级使用基础睡眠算法
                self.getBasicSleepData(day: day, completion: completion)
            }
            
            // 设置15秒超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 15.0, execute: timeoutTask)
            
            // 添加通知观察者监听GoMore睡眠ID列表
            NotificationCenter.default.addObserver(forName: .receivedGoMoreSleepIdsNotification, 
                                                  object: nil, 
                                                  queue: .main) { [weak self] notification in
                guard let self = self, !isDataProcessed else { return }
                
                // 取消超时任务
                timeoutTask.cancel()
                
                // 获取GoMore睡眠ID列表
                let sleepIds = SDKDelegate.shared.gomoreSleepIds
                print("✅ 收到GoMore睡眠ID列表，共 \(sleepIds.count) 条记录")
                
                if sleepIds.isEmpty {
                    print("⚠️ GoMore睡眠ID列表为空，降级使用基础睡眠算法")
                    isDataProcessed = true
                    
                    // 移除观察者
                    NotificationCenter.default.removeObserver(self, name: .receivedGoMoreSleepIdsNotification, object: nil)
                    
                    // 降级使用基础睡眠算法
                    self.getBasicSleepData(day: day, completion: completion)
                    return
                }
                
                // 选择要处理的睡眠ID - 添加安全检查
                guard sleepIds.count > 0 else {
                    print("⚠️ GoMore睡眠ID列表虽然不为空，但可能存在无效数据，降级使用基础睡眠算法")
                    isDataProcessed = true
                    
                    // 移除观察者
                    NotificationCenter.default.removeObserver(self, name: .receivedGoMoreSleepIdsNotification, object: nil)
                    
                    // 降级使用基础睡眠算法
                    self.getBasicSleepData(day: day, completion: completion)
                    return
                }
                
                // 创建一个计数器来跟踪已处理的ID数量
                var processedIdCount = 0
                var sleepDetail: CRPGoMoreSleepDataModel? = nil
                var sleepSegment: CRPGoMoreSleepRecordModel? = nil
                let dispatchGroup = DispatchGroup()
                
                // 修改：寻找第一个有效的睡眠ID，而不是只使用第一个
                var targetSleepId: Int = 0
                var validIdFound = false
                
                // 先打印所有ID方便调试
                print("📋 接收到的所有睡眠ID: \(sleepIds)")
                
                // 查找有效ID
                for id in sleepIds {
                    if id > 0 {
                        targetSleepId = id
                        validIdFound = true
                        print("✅ 找到有效的睡眠ID: \(id)，将使用此ID")
                        break
                    }
                }
                
                // 如果ID无效且是ID=0，可能是设备实际使用0作为有效值，尝试使用第一个ID
                if !validIdFound && sleepIds.count > 0 && sleepIds[0] == 0 {
                    print("⚠️ 没有找到大于0的ID，但设备可能使用0作为有效ID，尝试使用ID: 0")
                    targetSleepId = sleepIds[0]
                    validIdFound = true
                }
                
                // 验证睡眠ID是否有效
                guard validIdFound else {
                    print("⚠️ 没有找到任何有效的GoMore睡眠ID，降级使用基础睡眠算法")
                    isDataProcessed = true
                    
                    // 移除观察者
                    NotificationCenter.default.removeObserver(self, name: .receivedGoMoreSleepIdsNotification, object: nil)
                    
                    // 降级使用基础睡眠算法
                    self.getBasicSleepData(day: day, completion: completion)
                    return
                }
                
                print("⏳ 开始处理睡眠ID: \(targetSleepId)")
                
                // 尝试获取详细睡眠数据
                dispatchGroup.enter()
                CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataDetail(id: targetSleepId) { model, error in
                    if error == .none && model != nil {
                        print("✅ 成功获取睡眠ID \(targetSleepId) 的详细数据")
                        sleepDetail = model
                    } else {
                        print("❌ 获取睡眠详细数据失败，ID: \(targetSleepId), 错误: \(error)")
                    }
                    dispatchGroup.leave()
                }
                
                // 尝试获取睡眠分段数据
                dispatchGroup.enter()
                CRPSmartRingSDK.sharedInstance.getGoMoreSleepSegmentationData(id: targetSleepId) { model, error in
                    if error == .none && model != nil {
                        print("✅ 成功获取睡眠ID \(targetSleepId) 的分段数据")
                        sleepSegment = model
                    } else {
                        print("❌ 获取睡眠分段数据失败，ID: \(targetSleepId), 错误: \(error)")
                    }
                    dispatchGroup.leave()
                }
                
                // 等待两个请求完成
                dispatchGroup.notify(queue: .main) {
                    isDataProcessed = true
                    
                    // 移除观察者
                    NotificationCenter.default.removeObserver(self, name: .receivedGoMoreSleepIdsNotification, object: nil)
                    
                    // 安全检查：确保两个数据都成功获取
                    guard let detail = sleepDetail, let segment = sleepSegment else {
                        print("❌ 获取GoMore睡眠数据失败：详细数据或分段数据为空，降级使用基础睡眠算法")
                        
                        // 降级使用基础睡眠算法
                        self.getBasicSleepData(day: day, completion: completion)
                        return
                    }
                    
                    // 安全检查：验证数据有效性
                    guard detail.startTime > 0 && detail.endTime > 0 && detail.endTime > detail.startTime else {
                        print("❌ GoMore睡眠数据时间戳无效：开始=\(detail.startTime)，结束=\(detail.endTime)，降级使用基础睡眠算法")
                        
                        // 降级使用基础睡眠算法
                        self.getBasicSleepData(day: day, completion: completion)
                        return
                    }
                    
                    print("✅ 成功获取GoMore睡眠详细数据和分段数据")
                    
                    // 转换时间戳为日期，添加异常处理
                    let startTimestamp = Double(detail.startTime) / 1000.0
                    let endTimestamp = Double(detail.endTime) / 1000.0
                    let startDate = Date(timeIntervalSince1970: startTimestamp)
                    let endDate = Date(timeIntervalSince1970: endTimestamp)
                    
                    // 验证时间戳是否合理（不是1970年前后）
                    let calendar = Calendar.current
                    let startYear = calendar.component(.year, from: startDate)
                    let endYear = calendar.component(.year, from: endDate)
                    
                    // 如果年份小于2000或大于当前年份+1，认为数据无效
                    let currentYear = calendar.component(.year, from: Date())
                    if startYear < 2000 || startYear > currentYear + 1 || endYear < 2000 || endYear > currentYear + 1 {
                        print("⚠️ 检测到异常时间戳 - 开始年份: \(startYear), 结束年份: \(endYear)")
                        print("❌ GoMore睡眠数据时间戳异常（年份不合理），降级使用基础睡眠算法")
                        self.getBasicSleepData(day: day, completion: completion)
                        return
                    }
                    
                    // 验证时间区间是否合理
                    let sleepIntervalMinutes = Int(endTimestamp - startTimestamp) / 60
                    let totalSleepMinutes = segment.deep + segment.light + segment.rem
                    
                    // 允许的误差范围 - 睡眠总时长与时间区间相差不应超过60分钟
                    let allowedDiscrepancy = 60
                    if abs(sleepIntervalMinutes - totalSleepMinutes) > allowedDiscrepancy {
                        print("⚠️ 睡眠时长数据不一致：时间区间=\(sleepIntervalMinutes)分钟，总睡眠时长=\(totalSleepMinutes)分钟")
                        
                        // 如果差异太大但两个值都合理，使用分段数据的总和作为真实睡眠时长
                        if totalSleepMinutes > 0 && totalSleepMinutes < 24*60 {
                            print("✓ 使用分段数据总和作为真实睡眠时长: \(totalSleepMinutes)分钟")
                        } else {
                            print("❌ 睡眠分段数据无效：总睡眠时间 \(totalSleepMinutes) 分钟超出合理范围，降级使用基础睡眠算法")
                            self.getBasicSleepData(day: day, completion: completion)
                            return
                        }
                    }
                    
                    // 验证睡眠分段数据有效性
                    if totalSleepMinutes <= 0 {
                        print("⚠️ GoMore睡眠分段数据无效：总睡眠时间为0，降级使用基础睡眠算法")
                        
                        // 降级使用基础睡眠算法
                        self.getBasicSleepData(day: day, completion: completion)
                        return
                    }
                    
                    // 创建SleepData对象
                    let sleepData = SleepData(
                        startTime: startDate,
                        endTime: endDate,
                        deepSleepMinutes: segment.deep,
                        lightSleepMinutes: segment.light,
                        remSleepMinutes: segment.rem,
                        awakeMinutes: 0, // GoMore没有wake属性
                        sleepQuality: Int(detail.sleepScore)
                    )
                    
                    print("✅ 成功创建GoMore高级睡眠数据对象")
                    print("📊 睡眠数据：开始=\(startDate)，结束=\(endDate)，深睡=\(segment.deep)分钟，浅睡=\(segment.light)分钟，REM=\(segment.rem)分钟，得分=\(detail.sleepScore)")
                    
                    // 导出并打印JSON格式数据
                    SleepUploadService.shared.exportSleepDataAsJSON(sleepData: sleepData)
                    
                    // 返回成功结果
                    completion(sleepData, nil)
                }
            }
            
            // 获取GoMore睡眠数据列表
            print("🔍 请求获取GoMore睡眠数据ID列表")
            CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataList()
        }
    }

    /// 使用基础睡眠算法获取睡眠数据
    private func getBasicSleepData(day: Int, completion: @escaping (SleepData?, Error?) -> Void) {
        print("正在获取基础睡眠数据...")
        
        CRPSmartRingSDK.sharedInstance.getSleepData(day) { model, error in
            DispatchQueue.main.async {
                if error == .none {
                    let sleepData = SleepData(from: model)
                    print("成功获取基础睡眠数据")
                    
                    // 导出并打印JSON格式数据
                    SleepUploadService.shared.exportSleepDataAsJSON(sleepData: sleepData)
                    
                    completion(sleepData, nil)
                } else {
                    print("获取基础睡眠数据失败: \(error)")
                    completion(nil, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取睡眠数据失败: \(error)"]))
                }
            }
        }
    }

//    /// 连接成功后同步设备信息
//    func syncDeviceInfo() {
//        // 确保设备信息对象存在
//        // 获取佩戴状态
//        self.getWearingState()
//        
//        // 直接调用SDK获取真实MAC地址
//        self.getMacAddress { [weak self] realMac in
//            guard let self = self else { return }
//            
//            if !realMac.isEmpty {
//                print("成功获取真实MAC地址: \(realMac)")
//
//                
//                
//                DispatchQueue.main.async {
//                    // 更新设备信息中的MAC地址
//                    if var updatedInfo = self.deviceInfo {
//                        updatedInfo.mac = realMac
//                        self.deviceInfo = updatedInfo
//                        
//                        // 保存MAC地址以备将来使用
//                        self.lastConnectedDeviceMAC = realMac
//                    }
//                }
//            } else {
//                print("获取MAC地址失败，使用备用方案")
//                
//                DispatchQueue.main.async {
//                    // 如果获取失败，则使用备用标识符
//                    if var updatedInfo = self.deviceInfo, (((updatedInfo.mac?.isEmpty) != nil) || updatedInfo.mac == "正在获取...") {
//                        updatedInfo.mac = "device_\(Date().timeIntervalSince1970)"
//                        self.deviceInfo = updatedInfo
//                        print("使用临时标识符作为MAC地址")
//                    }
//                }
//            }
//        }
//        
//        ///获取序列号
//        CRPSmartRingManage.shared.getGitHashInfo { value, error in
//            if let info = self.deviceInfo {
//                info.serial = value
//                self.deviceInfo = info
//            }
//            print("序列号serial:\(value)")
//        }
//        // 同步设备时间
//        self.syncTime()
//        
//        // 获取电池电量
//        self.getBatteryLevel()
//        
//        // 获取固件版本
//        self.getFirmwareVersion() { [weak self] version in
//            guard let self = self else { return }
//            
//            // 固件类型检测并设置相应的睡眠算法
//            if self.firmwareType == "ION" {
//                print("已连接到ION固件设备，将使用GoMore高级睡眠算法")
//                // 初始化GoMore算法支持
//                let goMoreService = GoMoreService.shared
//                goMoreService.checkGoMoreSupport { isSupported, _ in
//                    if isSupported {
//                        print("设备支持GoMore算法，将使用GoMore高级睡眠算法")
//                        self.setSleepAlgorithm(useGoMore: true)
//                    } else {
//                        print("设备不支持GoMore算法，将降级使用基础睡眠算法")
//                        self.setSleepAlgorithm(useGoMore: false)
//                    }
//                }
//            } else if self.firmwareType == "R033" {
//                print("已连接到R033固件设备，将使用基础睡眠算法")
//                // 设置为基础睡眠算法
//                self.setSleepAlgorithm(useGoMore: false)
//            } else {
//                print("已连接到未知类型固件设备: \(self.firmwareType)，将使用基础睡眠算法")
//                // 设置为基础睡眠算法
//                self.setSleepAlgorithm(useGoMore: false)
//            }
//        }
//        
//        if let deviceName = self.deviceInfo?.localName {
//            let userAPIService = UserAPIService(networkManager: NetworkManager.shared)
//            userAPIService.bindDevice(mac: <#T##String#>, deviceSn: <#T##String#>, deviceFirmwareVersion: <#T##String#>, name: <#T##String#>)
//                .sink(receiveCompletion: { completion in
//                    switch completion {
//                    case .finished:
//                        print("设备绑定请求完成")
//                    case .failure(let error):
//                        print("设备绑定失败: \(error.localizedDescription)")
//                    }
//                }, receiveValue: { response in
//                    if response.code == 0 {
//                        print("设备绑定成功")
//                    } else {
//                        print("设备绑定失败: \(response.msg ?? "未知错误")")
//                    }
//                })
//                .store(in: &self.cancellables)
//        }
//        
//    }
    
    func syncDeviceInfo() {
        let group = DispatchGroup()

        var macAddress: String?
        var serialNumber: String?
        var firmwareVersion: String?

        // 防止多次调用 leave
        var didMacReturn = false
        var didSerialReturn = false
        var didFirmwareReturn = false
        // 1. 获取 MAC 地址
        group.enter()
        self.getMacAddress { mac in
            guard !didMacReturn else {
                print("⚠️ 忽略重复的 MAC 回调")
                return
            }
            didMacReturn = true
            defer { group.leave() }

            macAddress = mac.isEmpty ? "device_\(Date().timeIntervalSince1970)" : mac
            print("✅ MAC 获取完成: \(macAddress!)")
        }

        // 2. 获取序列号（Git hash info）
        group.enter()
        CRPSmartRingManage.shared.getGitHashInfo { serial, _ in
            guard !didSerialReturn else {
                print("⚠️ 忽略重复的序列号回调")
                return
            }
            didSerialReturn = true
            defer { group.leave() }

            serialNumber = serial
            print("✅ 序列号获取完成: \(serialNumber ?? "nil")")
        }

        // 3. 获取固件版本（蓝牙通知方式，可能多次回调）
        group.enter()
        self.getFirmwareVersion { version in
            guard !didFirmwareReturn else {
                print("⚠️ 忽略重复的固件版本回调")
                return
            }
            didFirmwareReturn = true
            defer { group.leave() }

            firmwareVersion = version
            print("✅ 固件版本获取完成: \(firmwareVersion ?? "nil")")
        }

        // 所有异步任务完成后执行绑定
        group.notify(queue: .main) { [weak self] in
            guard let self = self,
                  let mac = macAddress,
                  let sn = serialNumber,
                  let fw = firmwareVersion,
                  let name = self.deviceInfo?.localName else {
                print("❌ 信息不完整，取消绑定")
                return
            }

            print("🔗 所有信息准备完毕，开始绑定设备")
            let deviceMac = getTrustedMacAddress(forDeviceName: deviceRealMacsKey)
            if deviceMac?.count ?? 0 == 0 {
                let userAPIService = UserAPIService(networkManager: NetworkManager.shared)
                userAPIService.bindDevice(mac: mac, deviceSn: sn, deviceFirmwareVersion: fw, name: name)
                    .sink(receiveCompletion: { completion in
                        switch completion {
                        case .finished:
                            print("✅ 设备绑定完成")
                        case .failure(let error):
                            print("❌ 绑定失败: \(error.localizedDescription)")
                        }
                    }, receiveValue: { response in
                        if response.code == 0 {
                            print("✅ 绑定成功")
                            // 如果有设备名称，缓存这个MAC地址
                            if let deviceName = self.currentDiscovery?.localName {
                                self.saveTrustedMacAddress(name: deviceName, mac: mac)
                            }
                        } else {
                            print("❌ 绑定失败: \(response.msg ?? "未知错误")")
                        }
                    })
                    .store(in: &self.cancellables)
            }
            
        }

        // 可并行进行其他非依赖逻辑
        /// 获取佩戴状态
        self.getWearingState()
        ///获取电池电量
        self.getBatteryLevel()
        ///同步设备时间
        self.syncTime()
    }

    /// 设置睡眠算法
    private func setSleepAlgorithm(useGoMore: Bool) {
        // 根据参数设置要使用的睡眠算法类型
        let oldAlgorithmType = sleepAlgorithmType
        let newAlgorithmType = useGoMore ? SleepAlgorithmType.goMore : SleepAlgorithmType.basic
        
        // 如果算法类型没有变化，不执行操作
        if oldAlgorithmType == newAlgorithmType {
            print("睡眠算法类型未变化，保持为\(newAlgorithmType.description)")
            return
        }
        
        // 更新算法类型
        sleepAlgorithmType = newAlgorithmType
        print("已设置睡眠算法为: \(newAlgorithmType.description)")
        
        // 发送算法变更通知
        NotificationCenter.default.post(
            name: .sleepAlgorithmChanged,
            object: nil,
            userInfo: ["algorithmType": newAlgorithmType.description]
        )
    }

    // 添加此方法来处理设备连接并设置观察者
    func setupDeviceObservers() {
        // 注册通知观察者来监听设备连接状态
        NotificationCenter.default.addObserver(
            forName: .deviceConnected,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            guard let self = self else { return }
            
            // 设备连接后，根据固件类型设置睡眠算法
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.getFirmwareVersion { [weak self] _ in
                    guard let self = self else { return }
                    
                    // 检查固件类型并设置相应的睡眠算法
                    if self.firmwareType == "ION" {
                        // 连接ION固件设备，检查GoMore支持
                        let goMoreService = GoMoreService.shared
                        goMoreService.checkGoMoreSupport { isSupported, _ in
                            if isSupported {
                                self.setSleepAlgorithm(useGoMore: true)
                            } else {
                                self.setSleepAlgorithm(useGoMore: false)
                            }
                        }
                    } else {
                        // 其他固件类型使用基础算法
                        self.setSleepAlgorithm(useGoMore: false)
                    }
                }
            }
        }
        
        // 注册设备同步完成通知
        NotificationCenter.default.addObserver(
            forName: .deviceSyncCompleted,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self else { return }
            
            // 检查同步是否成功
            if let success = notification.userInfo?["success"] as? Bool, success {
                // 同步成功后，显示当前使用的睡眠算法
                let algorithmType = self.sleepAlgorithmType.description
                print("设备同步完成，当前使用睡眠算法：\(algorithmType)")
                
                // 发送算法更新通知
                NotificationCenter.default.post(
                    name: .sleepAlgorithmChanged,
                    object: nil,
                    userInfo: ["algorithmType": algorithmType]
                )
                
                // 尝试获取并上传心率历史数据
                DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
                    guard let self = self else { return }
                    print("准备从设备获取心率历史数据并上传...")
                    HeartRateUploadService.shared.fetchAndSaveHeartRateHistory { count, error in
                        if let error = error {
                            print("获取心率历史数据失败: \(error.localizedDescription)")
                        } else {
                            print("成功获取并保存了\(count)条心率历史数据")
                        }
                    }
                }
            }
        }
    }

    // MARK: - 系统蓝牙连接监听
    private func startMonitoringSystemBluetoothConnection() {
        print("开始监听系统蓝牙连接状态")
        
        // 监听蓝牙状态变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(systemBluetoothStateChanged(_:)),
            name: Notification.Name(rawValue: "bluetoothStateChanged"),
            object: nil
        )
        
        // 定期检查系统蓝牙连接状态，确保与APP连接状态同步
        // 每30秒检查一次连接状态
        Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] timer in
            self?.checkSystemBluetoothConnection()
        }
    }
    
    @objc private func systemBluetoothStateChanged(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let enabled = userInfo["enabled"] as? Bool else {
            return
        }
        
        if enabled {
            // 蓝牙已启用，检查是否系统已自动连接设备
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
//                self.checkSystemBluetoothConnection()
            }
        } else {
            // 蓝牙已禁用，强制APP断开连接
            if self.connectionState.isConnected {
                self.disconnectDevice()
            }
        }
    }
    
    public func checkSystemBluetoothConnection() {
        // 移除对autoReconnect的检查，总是检查系统蓝牙连接状态
        if !connectionState.isConnected {
            // 检查用户是否最近执行了解绑操作
            if disconnectionType == .manualUnbind || (UserDefaults.standard.object(forKey: "lastUnbindTimestamp") as? Date).map({ Date().timeIntervalSince($0) < 120 }) == true {
                print("用户最近执行了解除绑定操作，跳过系统蓝牙连接检查")
                return
            }
            
            print("检查系统蓝牙连接状态")
            
            // 尝试通过SDK获取电池等基本信息，看是否实际已连接
            CRPSmartRingManage.shared.getRingBatteryLevel { [weak self] level, error in
                if error == .none && level > 0 {
                    // 如果能获取到电量信息，说明实际已连接
                    print("检测到系统蓝牙已自动连接设备！电量: \(level)%")
                    
                    // 手动更新连接状态
                    DispatchQueue.main.async {
                        guard let self = self else { return }
                        
                        // 再次检查是否为主动解绑
                        if self.disconnectionType == .manualUnbind {
                            print("用户已主动解绑，强制断开系统自动连接的设备")
                            CRPSmartRingSDK.sharedInstance.disConnectGoodix()
                            return
                        }
                        
                        // 只有在当前状态为未连接时才更新
                        if !self.connectionState.isConnected {
                            // 更新状态
                            self.connectionState = .connected
                            self.batteryLevel = level
                            self.disconnectionType = .none // 重置断开类型
                            
                            // 保存当前发现的设备MAC地址(如果有当前设备信息)
                            if let discovery = self.currentDiscovery, let mac = discovery.mac, !mac.isEmpty {
                                self.lastConnectedDeviceMAC = mac
                                // 保存MAC地址与设备标识符的映射
                                let uuid = discovery.remotePeripheral.identifier.uuidString
                                self.saveDeviceMapping(uuid: uuid, mac: mac)
                                // 同时保存到用户设备信息
                                self.saveUserDeviceInfo(mac: mac, nickname: self.userDeviceNickname)
                                print("记录当前设备MAC地址: \(mac), UUID: \(uuid)")
                            } else if let peripheral = self.currentDiscovery?.remotePeripheral {
                                // 如果当前没有MAC地址，尝试从映射中获取
                                let uuid = peripheral.identifier.uuidString
                                if let mappedMac = self.getMacFromMapping(uuid: uuid) {
                                    self.lastConnectedDeviceMAC = mappedMac
                                    self.saveUserDeviceInfo(mac: mappedMac, nickname: self.userDeviceNickname)
                                    print("从映射中恢复设备MAC地址: \(mappedMac), UUID: \(uuid)")
                                }
                            }
                            
                            // 尝试获取设备信息
                            self.syncDeviceInfo()
                            
                            // 发送通知
                            NotificationCenter.default.post(name: .deviceConnected, object: nil)
                            print("📱 已发送设备已连接通知: deviceConnected")
                            
                            // 同时发送与UserProfileView相关的通知
                            NotificationCenter.default.post(name: NSNotification.Name("DeviceConnectionStateChanged"), object: nil)
                            print("📱 已发送设备连接状态变化通知: DeviceConnectionStateChanged")
                            
                            
                        }
                    }
                }
            }
        }
    }

    // MARK: - 自动数据获取方法
    
    /// 同步最近14天的心率数据
    func syncLast14DaysHeartRateData() {
        guard connectionState.isConnected else {
            print("⚠️ 设备未连接，无法获取心率数据")
            return
        }
        
        guard let userId = AuthService.shared.currentUser?.id else {
            print("⚠️ 用户未登录，无法保存心率数据")
            return
        }
        
        print("🔄 开始同步最近15天(包括今天)的心率数据...")
        
        // 递归函数，逐天获取数据
        func fetchNextDay(currentDay: Int, totalDays: Int, completion: @escaping (Int) -> Void) {
            guard currentDay <= totalDays else {
                print("✅ 已完成所有\(totalDays)天的心率数据同步")
                completion(currentDay - 1) // 返回成功处理的天数
                return
            }
            
            guard connectionState.isConnected else {
                print("⚠️ 设备已断开连接，停止心率数据同步")
                completion(currentDay - 1)
                return
            }
            
            let dayDesc = currentDay == 0 ? "今天" : "第\(currentDay)天"
            print("📅 正在同步\(dayDesc)的心率数据 (还剩\(totalDays - currentDay)天)...")
            
            // 使用SDK获取定时心率数据
            CRPSmartRingSDK.sharedInstance.getTimingHeartRate(currentDay) { model, error in
                if error == .none {
                    // 获取日期并计算当天0点
                    let calendar = Calendar.current
                    let today = Date()
                    let targetDate = calendar.date(byAdding: .day, value: -currentDay, to: today) ?? today
                    let startOfDay = calendar.startOfDay(for: targetDate)
                    
                    // 过滤有效的心率值
                    let validHeartRates = model.hearts.enumerated().filter { $0.element > 0 && $0.element < 255 }
                    
                    if validHeartRates.isEmpty {
                        print("⚠️ \(dayDesc)没有有效的心率数据")
                    } else {
                        print("✅ 获取到\(dayDesc)的\(validHeartRates.count)条有效心率数据")
                        
                        // 转换为批量添加格式
                        var heartRateData: [(value: Int16, timestamp: Date, deviceId: String?, confidence: Int16?)] = []
                        let deviceId = self.deviceInfo?.mac ?? "unknown"
                        
                        for (index, value) in validHeartRates {
                            // 计算时间戳：当天0点 + index * 5分钟
                            let timestamp = calendar.date(byAdding: .minute, value: index * 5, to: startOfDay) ?? startOfDay
                            
                            heartRateData.append((
                                value: Int16(value),
                                timestamp: timestamp,
                                deviceId: deviceId,
                                confidence: 100
                            ))
                        }
                        
                        // 保存到本地数据库
                        let success = HealthDataManager.shared.addHeartRates(userId: userId, data: heartRateData)
                        
                        if success {
                            print("✅ 成功保存\(dayDesc)的\(heartRateData.count)条心率数据到本地")
                        } else {
                            print("❌ 保存\(dayDesc)的心率数据失败")
                        }
                    }
                } else {
                    print("❌ 获取\(dayDesc)的心率数据失败: \(error)")
                }
                
                // 短暂延迟后获取下一天数据，避免设备请求过于频繁
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    fetchNextDay(currentDay: currentDay + 1, totalDays: totalDays, completion: completion)
                }
            }
        }
        
        // 开始获取数据，从第0天(今天)开始，共15天
        fetchNextDay(currentDay: 0, totalDays: 14) { completedDays in
            // 获取完成后，尝试上传数据到服务器
            print("🔄 已获取\(completedDays + 1)天的心率数据，开始上传到服务器...")
            
            HeartRateUploadService.shared.uploadPendingHeartRateData { count, error in
                if let error = error {
                    print("❌ 上传心率数据失败: \(error.localizedDescription)")
                } else {
                    print("✅ 成功上传\(count)条心率数据到服务器")
                    
                    // 设置定时器，定期上传最新数据(每小时检查一次)
                    Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { [weak self] timer in
                        guard let self = self, self.connectionState.isConnected else {
                            timer.invalidate()
                            return
                        }
                        
                        print("🕒 定时上传今日最新心率数据...")
                        self.syncTodayHeartRateData()
                    }
                }
            }
        }
    }
    
    /// 同步今天的心率数据
    func syncTodayHeartRateData() {
        guard connectionState.isConnected else {
            print("⚠️ 设备未连接，无法获取今日心率数据")
            return
        }
        
        guard let userId = AuthService.shared.currentUser?.id else {
            print("⚠️ 用户未登录，无法保存今日心率数据")
            return
        }
        
        print("🔄 开始同步今日心率数据...")
        
        // 使用SDK获取今日心率数据
        CRPSmartRingSDK.sharedInstance.getTimingHeartRate(0) { model, error in
            if error == .none {
                // 获取日期并计算当天0点
                let calendar = Calendar.current
                let today = Date()
                let startOfDay = calendar.startOfDay(for: today)
                
                // 过滤有效的心率值
                let validHeartRates = model.hearts.enumerated().filter { $0.element > 0 && $0.element < 255 }
                
                if validHeartRates.isEmpty {
                    print("⚠️ 今天没有新的有效心率数据")
                    return
                }
                
                print("✅ 获取到今天的\(validHeartRates.count)条有效心率数据")
                
                // 转换为批量添加格式
                var heartRateData: [(value: Int16, timestamp: Date, deviceId: String?, confidence: Int16?)] = []
                let deviceId = self.deviceInfo?.mac ?? "unknown"
                
                for (index, value) in validHeartRates {
                    // 计算时间戳：当天0点 + index * 5分钟
                    let timestamp = calendar.date(byAdding: .minute, value: index * 5, to: startOfDay) ?? startOfDay
                    
                    heartRateData.append((
                        value: Int16(value),
                        timestamp: timestamp,
                        deviceId: deviceId,
                        confidence: 100
                    ))
                }
                
                // 保存到本地数据库
                let success = HealthDataManager.shared.addHeartRates(userId: userId, data: heartRateData)
                
                if success {
                    print("✅ 成功保存今天的\(heartRateData.count)条心率数据到本地")
                    
                    // 上传新数据到服务器
                    HeartRateUploadService.shared.uploadPendingHeartRateData { count, error in
                        if let error = error {
                            print("❌ 上传今日心率数据失败: \(error.localizedDescription)")
                        } else if count > 0 {
                            print("✅ 成功上传\(count)条今日心率数据到服务器")
                        } else {
                            print("ℹ️ 没有新的心率数据需要上传")
                        }
                    }
                } else {
                    print("❌ 保存今天的心率数据失败")
                }
            } else {
                print("❌ 获取今天的心率数据失败: \(error)")
            }
        }
    }

    /// 获取设备真实MAC地址
    func getDeviceRealMAC(completion: @escaping (String) -> Void) {
        guard case .connected = connectionState,
              let peripheral = currentDiscovery?.remotePeripheral else {
            print("设备未连接或无效，无法获取真实MAC地址")
            completion("")
            return
        }
        
        print("正在尝试获取设备真实物理MAC地址...")
        
        // 方法1: 尝试从广告数据中获取
        if let discovery = currentDiscovery, 
           let mac = discovery.mac, 
           !mac.isEmpty, 
           mac != "auto_connected_device" {
            print("从广告数据中获取到MAC地址: \(mac)")
            
            // 保存映射
            saveDeviceMapping(uuid: peripheral.identifier.uuidString, mac: mac)
            
            // 保存为最后连接的设备
            lastConnectedDeviceMAC = mac
            saveUserDeviceInfo(mac: mac, nickname: userDeviceNickname)
            
            completion(mac)
            return
        }
        
        // 方法2: 从保存的映射中查找MAC地址
        let uuid = peripheral.identifier.uuidString
        if let mappedMac = getMacFromMapping(uuid: uuid) {
            print("从设备映射中获取到MAC地址: \(mappedMac)")
            
            // 保存为最后连接的设备
            lastConnectedDeviceMAC = mappedMac
            saveUserDeviceInfo(mac: mappedMac, nickname: userDeviceNickname)
            
            completion(mappedMac)
            return
        }
        
        // 方法3: 使用设备标识符作为替代
        let identifier = peripheral.identifier.uuidString
        print("使用设备标识符替代MAC地址: \(identifier)")
        
        // 将标识符格式化为类似MAC地址的格式
        let formattedId = formatIdentifierAsMac(identifier)
        print("格式化后的标识符: \(formattedId)")
        
        // 保存为最后连接的设备
        lastConnectedDeviceMAC = formattedId
        saveUserDeviceInfo(mac: formattedId, nickname: userDeviceNickname)
        
        completion(formattedId)
    }

    /// 根据MAC地址获取所有可能的UUID
    private func getUUIDsForMAC(mac: String) -> [String] {
        let mappings = UserDefaults.standard.dictionary(forKey: deviceMappingsKey) as? [String: String] ?? [:]
        
        // 查找所有与给定MAC地址关联的UUID
        return mappings.compactMap { (uuid, mappedMAC) in
            return mappedMAC.uppercased() == mac.uppercased() ? uuid : nil
        }
    }

    /// 更新系统已连接设备信息
    func updateSystemConnectedDevices(_ peripherals: [CBPeripheral]) {
        print("收到系统已连接设备更新: \(peripherals.count)个")
        
        // 保存系统已连接设备引用
        systemConnectedPeripherals = peripherals
        
        // 更新已配对设备集合
//        pairedDevices.removeAll()
//        for peripheral in peripherals {
//            let uuid = peripheral.identifier.uuidString
//            pairedDevices.insert(uuid)
//            print("系统已连接设备: \(peripheral.name ?? "未命名"), UUID: \(uuid)")
//            
//            // 尝试获取并保存MAC地址映射
//            if let mappedMAC = getMacFromMapping(uuid: uuid) {
//                print("已找到设备MAC映射: UUID=\(uuid), MAC=\(mappedMAC)")
//            } else {
//                print("未找到设备MAC映射: UUID=\(uuid)")
//            }
//        }
        
        // 如果当前正在扫描，更新扫描结果中的设备状态
        if isScanning {
            updateScannedDevicesPairedStatus()
        }
    }
    
    /// 更新已扫描设备的配对状态
    private func updateScannedDevicesPairedStatus() {
//        for (index, device) in discoveredDevices.enumerated() {
//            let uuid = device.remotePeripheral.identifier.uuidString
//            if pairedDevices.contains(uuid) {
//                // 标记为已配对
//                markDeviceAsPaired(discoveredDevices[index])
//                print("将扫描到的设备标记为已配对: \(device.localName ?? "未命名"), UUID: \(uuid)")
//            }
//        }
        
        // 重新排序设备列表，让已配对设备显示在前面
        discoveredDevices.sort { 
            let isPaired1 = isDevicePaired($0)
            let isPaired2 = isDevicePaired($1)
            
            if isPaired1 && !isPaired2 {
                return true
            } else if !isPaired1 && isPaired2 {
                return false
            } else {
                return $0.RSSI > $1.RSSI
            }
        }
    }
    
    /// 创建系统已连接设备的发现对象
    private func createDiscoveryForSystemConnectedPeripheral(_ peripheral: CBPeripheral) -> CRPDiscovery? {
        // 创建广播数据
        let advertisementData: [String: Any] = [
            CBAdvertisementDataLocalNameKey: peripheral.name ?? "系统已连接设备",
            CBAdvertisementDataIsConnectable: true
        ]
        if let manufacturerData = advertisementData[CBAdvertisementDataManufacturerDataKey] as? Data {
                let macAddress = manufacturerData.map { String(format: "%02x", $0) }.joined()
                print("MAC Address: \(macAddress)")
            }
        // 获取映射的MAC地址
        let uuid = peripheral.identifier.uuidString
//        let mac = getMacFromMapping(uuid: uuid)
        
        // 创建发现对象 - 使用直接初始化
        let discovery = CRPDiscovery(
            advertisementData: advertisementData as [String : AnyObject],
            remotePeripheral: peripheral,
            RSSI: 0
        )
        
        // 如果有MAC地址映射，设置MAC属性
//        if let mac = mac {
//            setValue(mac, forKey: "mac", in: discovery)
//        }
        
        // 标记为已配对
        markDeviceAsPaired(discovery)
        
        return discovery
    }
    
    /// 判断设备是否已配对
    /// - Parameter device: 设备对象
    /// - Returns: 是否已配对
//    func isDevicePaired(_ device: WindRingDevice) -> Bool {
//        guard let deviceId = device.deviceID else { return false }
//        return pairedDevices.contains(deviceId)
//    }
    
    /// 判断设备是否由系统连接
    /// - Parameter device: 设备对象
    /// - Returns: 是否已连接
    func isDeviceConnectedBySystem(_ device: WindRingDevice) -> Bool {
        guard let deviceId = device.deviceID, let uuid = UUID(uuidString: deviceId) else {
            return false
        }
        return BluetoothSystemDeviceManager.shared.isDeviceConnectedBySystem(identifier: uuid)
    }
    
    /// 保存配对设备信息
    /// - Parameter device: 要保存的设备
//    private func saveDeviceAsPaired(_ device: WindRingDevice) {
//        guard let deviceId = device.deviceID else { return }
//        pairedDevices.insert(deviceId)
//        savePairedDevices()
//    }
//    
//    /// 保存配对设备集合到持久化存储
//    private func savePairedDevices() {
//        let defaults = UserDefaults.standard
//        defaults.set(Array(pairedDevices), forKey: "WindRingPairedDevices")
//    }
    
    /// 从持久化存储加载配对设备
//    private func loadPairedDevices() {
//        let defaults = UserDefaults.standard
//        if let savedDevices = defaults.array(forKey: "WindRingPairedDevices") as? [String] {
//            pairedDevices = Set(savedDevices)
//            print("已加载配对设备: \(pairedDevices.count)个")
//        }
//    }
//    
    /// 刷新系统连接的设备状态
    func refreshSystemConnectedDevices() {
        BluetoothSystemDeviceManager.shared.refreshConnectedDevices()
    }
    
    /// 连接设备
    func connectDevice(_ device: WindRingDevice) {
        guard let deviceID = device.deviceID else {
            print("[WindRingDeviceService] 无法连接 - 设备ID为空")
            return
        }
        
        print("[WindRingDeviceService] 正在连接设备: \(deviceID)")
        
        // 保存配对设备信息
//        saveDeviceAsPaired(device)
        
        // ... existing code ...
    }
    
    /// 判断是否有任何已配对设备
    /// - Returns: 是否有已配对设备
//    func hasPairedDevices() -> Bool {
//        return !pairedDevices.isEmpty
//    }
    
    // 新增一个设备MAC地址的持久化存储键
    private let deviceRealMacsKey = "com.windring.deviceRealMacs"
    private let deviceUUIDMacsKey = "com.windring.deviceUUIDMacs" // 新增基于UUID的MAC地址缓存键
    
    /// 保存可信的设备MAC地址
    private func saveTrustedMacAddress(name: String, mac: String) {
        var storedMacs = UserDefaults.standard.dictionary(forKey: deviceRealMacsKey) as? [String: String] ?? [:]
        
        // 只有在MAC地址有真实有效的格式时才保存
        if isValidMacAddress(mac) {
            print("保存设备\(name)的可信MAC地址: \(mac)")
            storedMacs[name] = mac
            UserDefaults.standard.set(storedMacs, forKey: deviceRealMacsKey)
        }
    }
    
    /// 基于UUID保存设备MAC地址 - 确保每个物理设备有唯一标识
    public func saveDeviceMacByUUID(uuid: String, mac: String) {
        if !isValidMacAddress(mac) || mac.isEmpty {
            return
        }
        
        var uuidMacs = UserDefaults.standard.dictionary(forKey: deviceUUIDMacsKey) as? [String: String] ?? [:]
        print("保存设备UUID-MAC映射: \(uuid) -> \(mac)")
        uuidMacs[uuid] = mac
        UserDefaults.standard.set(uuidMacs, forKey: deviceUUIDMacsKey)
    }
    
    /// 根据UUID获取设备MAC地址
    public func getDeviceMacByUUID(uuid: String) -> String? {
        let uuidMacs = UserDefaults.standard.dictionary(forKey: deviceUUIDMacsKey) as? [String: String] ?? [:]
        return uuidMacs[uuid]
    }
    
    /// 获取缓存的可信设备MAC地址
    public func getTrustedMacAddress(forDeviceName name: String) -> String? {
        let storedMacs = UserDefaults.standard.dictionary(forKey: deviceRealMacsKey) as? [String: String] ?? [:]
        return storedMacs[name]
    }
    
    /// 为系统已连接的设备获取MAC地址
    /// - Parameters:
    ///   - peripheral: 外设对象
    ///   - completion: 完成回调，返回MAC地址
    public func getMacAddressForSystemConnectedDevice(peripheral: CBPeripheral, completion: @escaping (String) -> Void) {
        print("正在获取系统已连接设备的MAC地址: \(peripheral.name ?? "未知")")
        let uuid = peripheral.identifier.uuidString
        
        // 1. 首先检查UUID-MAC映射缓存（最可靠）
        if let uuidMac = getDeviceMacByUUID(uuid: uuid) {
            print("从UUID-MAC映射获取到MAC地址: \(uuidMac)")
            completion(uuidMac)
            return
        }
        
        // 2. 然后检查通用MAC映射缓存
        if let mappedMac = getMacFromMapping(uuid: uuid) {
            print("从映射缓存获取到MAC地址: \(mappedMac)")
            
            // 同时保存到UUID-MAC映射
            saveDeviceMacByUUID(uuid: uuid, mac: mappedMac)
            
            // 保存到可信MAC缓存
            if let name = peripheral.name {
                saveTrustedMacAddress(name: name, mac: mappedMac)
            }
            
            completion(mappedMac)
            return
        }
        
        // 3. 检查设备名称对应的可信MAC缓存
        if let name = peripheral.name, let cachedMac = getTrustedMacAddress(forDeviceName: name) {
            print("从设备名称缓存获取到MAC地址: \(cachedMac)")
            
            // 为避免同名设备共用一个MAC地址，生成一个略微修改的版本
            let modifiedMac = generateUniqueMAC(baseMac: cachedMac, uuid: uuid)
            
            // 保存到UUID-MAC映射
            saveDeviceMacByUUID(uuid: uuid, mac: modifiedMac)
            
            completion(modifiedMac)
            return
        }
        
        // 4. 尝试使用SDK获取MAC地址
        // 为避免SDK可能不提供getMacWithPeripheral方法，使用反射检查方法是否存在
        let sdk = CRPSmartRingSDK.sharedInstance
        
        // 设置当前连接的外设为这个系统连接的外设（临时）
        let tempDiscovery = createDiscoveryFromPeripheral(peripheral)
        let originalDiscovery = currentDiscovery
        currentDiscovery = tempDiscovery // 临时设置当前设备
        
        // 使用SDK的getMac方法获取MAC地址
        CRPSmartRingSDK.sharedInstance.getMac { [weak self] mac, error in
            // 恢复原始连接设备
            DispatchQueue.main.async {
                self?.currentDiscovery = originalDiscovery
                
                if error == .none && !mac.isEmpty && self?.isValidMacAddress(mac) == true {
                    print("从系统连接设备获取MAC地址成功: \(mac)")
                    
                    // 保存MAC地址映射
                    self?.saveDeviceMapping(uuid: uuid, mac: mac)
                    
                    // 保存到UUID-MAC映射
                    self?.saveDeviceMacByUUID(uuid: uuid, mac: mac)
                    
                    // 保存到可信MAC缓存
                    if let name = peripheral.name {
                        self?.saveTrustedMacAddress(name: name, mac: mac)
                    }
                    
                    completion(mac)
                } else {
                    print("从系统获取MAC地址失败: \(error)")
                    
                    // 生成一个基于UUID的唯一MAC地址
                    let generatedMac = self?.generateMACFromUUID(uuid: uuid) ?? ""
                    
                    // 保存到UUID-MAC映射
                    self?.saveDeviceMacByUUID(uuid: uuid, mac: generatedMac)
                    
                    completion(generatedMac)
                }
            }
        }
    }
    
    /// 根据UUID生成唯一的MAC地址
    public func generateMACFromUUID(uuid: String) -> String {
        // 生成一个与UUID相关的唯一MAC地址
        let cleanUUID = uuid.replacingOccurrences(of: "-", with: "")
        let prefix = String(cleanUUID.prefix(12))
        
        // 格式化为MAC地址格式
        var formattedMac = ""
        for i in stride(from: 0, to: prefix.count, by: 2) {
            let startIndex = prefix.index(prefix.startIndex, offsetBy: i)
            let endIndex = prefix.index(startIndex, offsetBy: min(2, prefix.count - i))
            formattedMac += prefix[startIndex..<endIndex]
            if i < prefix.count - 2 {
                formattedMac += ":"
            }
        }
        
        return formattedMac.uppercased()
    }
    
    /// 基于现有MAC地址和UUID创建唯一MAC地址，保留相似度但确保唯一性
    private func generateUniqueMAC(baseMac: String, uuid: String) -> String {
        // 如果baseMac不是有效MAC地址，直接生成一个新的
        if !isValidMacAddress(baseMac) {
            return generateMACFromUUID(uuid: uuid)
        }
        
        // 获取UUID的最后几位
        let cleanUUID = uuid.replacingOccurrences(of: "-", with: "")
        let uuidLastPart = cleanUUID.suffix(4)
        
        // 分割MAC地址
        let macParts = baseMac.components(separatedBy: ":")
        if macParts.count != 6 {
            return generateMACFromUUID(uuid: uuid)
        }
        
        // 修改MAC地址的最后一或两部分
        var newMacParts = macParts
        
        // 修改倒数第二部分
        if uuidLastPart.count >= 2 {
            let startIndex = uuidLastPart.startIndex
            let endIndex = uuidLastPart.index(startIndex, offsetBy: 2)
            newMacParts[4] = String(uuidLastPart[startIndex..<endIndex]).uppercased()
        }
        
        // 修改最后一部分
        if uuidLastPart.count >= 4 {
            let startIndex = uuidLastPart.index(uuidLastPart.startIndex, offsetBy: 2)
            let endIndex = uuidLastPart.index(startIndex, offsetBy: 2)
            newMacParts[5] = String(uuidLastPart[startIndex..<endIndex]).uppercased()
        }
        
        return newMacParts.joined(separator: ":")
    }

    /// 验证MAC地址格式是否有效
    private func isValidMacAddress(_ mac: String) -> Bool {
        // MAC地址格式为XX:XX:XX:XX:XX:XX，其中X为十六进制数字
        let pattern = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"
        let predicate = NSPredicate(format: "SELF MATCHES %@", pattern)
        return predicate.evaluate(with: mac)
    }
    
    func initDevice(_ device: CRPDiscovery) {
        let newDeviceInfo = RingInfo.init()
        newDeviceInfo.localName = device.localName
        newDeviceInfo.kCABAdvidataLocalName = device.kCABAdvidataLocalName
        newDeviceInfo.advertisementData = device.advertisementData
        newDeviceInfo.peripheral = device
        newDeviceInfo.RSSI = device.RSSI
        newDeviceInfo.ver = device.ver
        newDeviceInfo.platform = device.platform
        newDeviceInfo.isPair = device.isPair
        newDeviceInfo.isTalk = device.isTalk
        newDeviceInfo.info = device.info
        newDeviceInfo.hide = device.hide
        newDeviceInfo.mac = device.mac
        newDeviceInfo.connectionState = .disconnected
        self.deviceInfo = newDeviceInfo
        let dict = newDeviceInfo.toDict()
        UserDefaults.standard.set(dict, forKey: userDeviceKey)
    }
    func loadDevice() -> RingInfo? {
        guard let dict = UserDefaults.standard.dictionary(forKey: userDeviceKey) else {
            return nil
        }
        
        return RingInfo.fromDict(dict)
    }
}

// MARK: - CRPManagerDelegate
//extension WindRingDeviceService: CRPManagerDelegate {
//    // 连接状态变化
//    func didState(_ state: CRPState) {
//        DispatchQueue.main.async { [weak self] in
//            guard let self = self else { return }
//            
//            print("📱 SDK连接状态变化: \(state.rawValue)-\(state)")
//            
//            // 获取当前连接设备的描述信息，用于日志
//            let deviceDesc = self.currentDiscovery.map { 
//                "\($0.localName ?? "未知设备") (\($0.mac ?? "未知MAC"))" 
//            } ?? "未知设备"
//            
//            switch state {
//            case .connecting:
//                self.connectionState = .connecting
//                print("📱 设备正在连接: \(deviceDesc)")
//                
//                // 发送通知
//                NotificationCenter.default.post(name: .deviceConnecting, object: nil)
//                print("📱 已发送设备连接中通知: deviceConnecting")
//                
//            case .connected:
//                self.connectionState = .connected
//                self.connectionRetryCount = 0 // 连接成功，重置重试计数
//                print("📱 设备已连接: \(deviceDesc)")
//                
//                // 不再缓存设备MAC地址
//                // if let mac = self.currentDiscovery?.mac {
//                //     self.lastConnectedDeviceMAC = mac
//                //     self.saveUserDeviceInfo(mac: mac, nickname: self.userDeviceNickname)
//                //     print("📱 已保存最后连接的设备MAC: \(mac)")
//                // }
//                
//                // 发送通知
//                NotificationCenter.default.post(name: .deviceConnected, object: nil)
//                print("📱 已发送设备已连接通知: deviceConnected")
//                
//                // 同时发送与UserProfileView相关的通知
//                NotificationCenter.default.post(name: NSNotification.Name("DeviceConnectionStateChanged"), object: nil)
//                print("📱 已发送设备连接状态变化通知: DeviceConnectionStateChanged")
//                
//                // 连接成功后自动同步时间并获取基本信息
//                self.syncDeviceInfo()
//                
//                // 连接成功后设置定时心率测量(5分钟间隔)
//                DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
//                    guard let self = self else { return }
//                    print("设置自动定时心率测量: 每5分钟")
//                    self.setTimingHeartRateMeasurement(interval: 1) // 1=5分钟间隔
//                    
//                    // 获取并打印当前设置的定时心率测量状态
//                    self.getTimingHeartRateMeasurementStatus { interval, error in
//                        if let error = error {
//                            print("获取定时心率测量状态失败: \(error.localizedDescription)")
//                        } else if interval == 0 {
//                            print("当前定时心率测量已关闭")
//                        } else {
//                            print("当前定时心率测量间隔: 每\(interval * 5)分钟")
//                        }
//                    }
//                    
//                    // 自动获取最近14天的心率数据并保存到本地
//                    DispatchQueue.main.asyncAfter(deadline: .now() + 5) { [weak self] in
//                        guard let self = self else { return }
//                        print("🔍 开始自动获取最近14天的心率数据...")
//                        self.syncLast14DaysHeartRateData()
//                    }
//                }
//                
//            case .disconnected:
//                let wasConnected = self.connectionState.isConnected
//                self.connectionState = .disconnected
//                print("📱 设备已断开: \(deviceDesc), 之前状态是否为已连接: \(wasConnected)")
//                
//                // 如果是初始状态或未设置断开类型且之前是已连接状态，则认为是被动断开
//                if self.disconnectionType == .none && wasConnected {
//                    self.disconnectionType = .passive
//                    print("📱 检测为被动断开连接")
//                }
//                
//                // 发送通知
//                NotificationCenter.default.post(name: .deviceDisconnected, object: nil)
//                print("📱 已发送设备已断开通知: deviceDisconnected")
//                
//                // 同时发送与UserProfileView相关的通知
//                NotificationCenter.default.post(name: NSNotification.Name("DeviceConnectionStateChanged"), object: nil)
//                print("📱 已发送设备连接状态变化通知: DeviceConnectionStateChanged")
//                
//                // 如果是被动断开且自动重连开启，尝试重连
//                if wasConnected && self.autoReconnect && self.disconnectionType == .passive {
//                    // 延迟3秒再尝试重连，给设备一些恢复时间
//                    print("📱 检测为被动断开且自动重连已开启，计划3秒后尝试自动重连...")
//                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
//                        guard let self = self else { return }
//                        self.attemptAutoReconnect()
//                    }
//                } else if self.disconnectionType == .manualUnbind {
//                    print("📱 设备已主动解除绑定，不会尝试自动重连")
//                    // 确保清空MAC地址
//                    self.lastConnectedDeviceMAC = nil
//                }
//                
//            case .unbind:
//                self.connectionState = .disconnected
//                print("设备解绑: \(deviceDesc)")
//                
//            case .disconnecting:
//                print("设备正在断开中: \(deviceDesc)")
//                // 不更改状态，等待断开完成
//                
//            case .syncing:
//                print("设备正在同步数据: \(deviceDesc)")
//                // 不更改连接状态，保持为已连接
//                
//                // 发送通知
//                NotificationCenter.default.post(name: .deviceSyncing, object: nil)
//                
//            case .syncSuccess:
//                print("设备数据同步成功: \(deviceDesc)")
//                // 发送通知
//                NotificationCenter.default.post(name: .deviceSyncCompleted, object: nil, userInfo: ["success": true])
//                
//                // 同步成功后，显示当前使用的睡眠算法
//                let algorithmType = self.sleepAlgorithmType.description
//                print("设备同步完成，当前使用睡眠算法：\(algorithmType)")
//                
//                // 尝试获取并上传心率历史数据
//                DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
//                    guard let self = self else { return }
//                    print("准备从设备获取心率历史数据并上传...")
//                    HeartRateUploadService.shared.fetchAndSaveHeartRateHistory { count, error in
//                        if let error = error {
//                            print("获取心率历史数据失败: \(error.localizedDescription)")
//                        } else {
//                            print("成功获取并保存了\(count)条心率历史数据")
//                        }
//                    }
//                }
//                
//            case .syncError:
//                print("设备数据同步失败: \(deviceDesc)")
//                // 发送通知
//                NotificationCenter.default.post(name: .deviceSyncCompleted, object: nil, userInfo: ["success": false])
//                
//            @unknown default:
//                print("未知的设备连接状态: \(state.rawValue) - \(deviceDesc)")
//            }
//        }
//    }
//    
//    // 蓝牙状态变化
//    func didBluetoothState(_ state: CRPBluetoothState) {
//        DispatchQueue.main.async { [weak self] in
//            guard let self = self else { return }
//            
//            print("蓝牙状态变化: \(state)")
//            
//            switch state {
//            case .poweredOn:
//                print("蓝牙已开启")
//                
//                // 如果是被动断开的设备并且自动重连功能已启用，尝试重连
//                if self.disconnectionType == .passive || self.disconnectionType == .systemRequest {
//                    // 如果当前没有连接，且已保存了上次设备的MAC地址，尝试重连
//                    if !self.connectionState.isConnected && self.autoReconnect {
//                        print("蓝牙开启，尝试自动重连...")
//                        // 延迟2秒尝试重连，确保蓝牙栈完全初始化
//                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
//                            self?.attemptAutoReconnect()
//                        }
//                    }
//                }
//                
//                // 发送通知
//                NotificationCenter.default.post(
//                    name: Notification.Name(rawValue: "bluetoothStateChanged"), 
//                    object: nil, 
//                    userInfo: ["state": state.rawValue, "enabled": true]
//                )
//                
//            case .poweredOff:
//                print("蓝牙已关闭")
//                
//                // 蓝牙关闭时，强制断开当前连接
//                if self.connectionState.isConnected || self.connectionState == .connecting {
//                    // 记录当前断开是由系统蓝牙关闭导致的
//                    self.disconnectionType = .systemRequest
//                    self.connectionState = .disconnected
//                    self.currentDiscovery = nil
//                    self.currentPeripheral = nil
//                    print("蓝牙关闭，设备连接已断开")
//                }
//                
//                // 如果正在扫描，停止扫描
//                if self.isScanning {
//                    self.stopScan()
//                }
//                
//                // 发送通知
//                NotificationCenter.default.post(
//                    name: Notification.Name(rawValue: "bluetoothStateChanged"), 
//                    object: nil, 
//                    userInfo: ["state": state.rawValue, "enabled": false]
//                )
//                
//            default:
//                print("其他蓝牙状态: \(state)")
//                
//                // 对于其他状态（如resetting、unauthorized等），如果设备已连接，强制断开连接
//                if self.connectionState.isConnected {
//                    self.disconnectionType = .systemRequest
//                    self.connectionState = .disconnected
//                    print("蓝牙状态变化，设备连接已断开")
//                }
//                
//                // 发送通知
//                NotificationCenter.default.post(
//                    name: Notification.Name(rawValue: "bluetoothStateChanged"), 
//                    object: nil, 
//                    userInfo: ["state": state.rawValue, "enabled": false]
//                )
//            }
//        }
//    }
//    
//    // 接收心率测量结果
//    func receiveHeartRate(_ heartRate: Int) {
//        DispatchQueue.main.async { [weak self] in
//            self?.lastHeartRate = heartRate
//            self?.isMeasuringHeartRate = false
//            print("接收到心率: \(heartRate)")
//            
//            // 发送通知
//            NotificationCenter.default.post(
//                name: .heartRateMeasured,
//                object: nil,
//                userInfo: ["value": heartRate]
//            )
//        }
//    }
//    
//    // 接收实时心率
//    func receiveRealTimeHeartRate(_ heartRate: Int) {
//        DispatchQueue.main.async { [weak self] in
//            self?.lastHeartRate = heartRate
//            print("接收到实时心率: \(heartRate)")
//            
//            // 发送通知
//            NotificationCenter.default.post(
//                name: .heartRateMeasured,
//                object: nil,
//                userInfo: ["value": heartRate, "isRealTime": true]
//            )
//        }
//    }
//    
//    // 接收血氧测量结果
//    func receiveSpO2(_ o2: Int) {
//        DispatchQueue.main.async { [weak self] in
//            self?.lastBloodOxygen = o2
//            self?.isMeasuringSpO2 = false
//            print("接收到血氧: \(o2)%")
//            
//            // 发送通知
//            NotificationCenter.default.post(
//                name: .bloodOxygenMeasured,
//                object: nil,
//                userInfo: ["value": o2]
//            )
//        }
//    }
//    
//    // 接收HRV测量结果
//    func receiveHRV(_ hrv: Int) {
//        DispatchQueue.main.async { [weak self] in
//            self?.lastHRV = hrv
//            print("接收到HRV: \(hrv)")
//            
//            // 发送通知
//            NotificationCenter.default.post(
//                name: .hrvMeasured,
//                object: nil,
//                userInfo: ["value": hrv]
//            )
//        }
//    }
//    
//    // 接收步数数据
//    func receiveSteps(_ model: CRPStepModel) {
//        print("接收到步数数据: 步数 \(model.steps), 距离 \(model.distance), 卡路里 \(model.calory)")
//        
//        // 发送通知
//        NotificationCenter.default.post(
//            name: .stepsUpdated,
//            object: model
//        )
//    }
//    
//    // 接收OTA更新状态
//    func receiveOTA(_ state: CRPOTAState, _ progress: Int) {
//        print("OTA更新状态: \(state), 进度: \(progress)%")
//        
//        switch state {
//        case .uploading:
//            print("固件升级中，进度: \(progress)%")
//        case .completed:
//            print("固件升级完成")
//        case .failed:
//            print("固件升级失败")
//        @unknown default:
//            print("未知的固件升级状态: \(state.rawValue)")
//        }
//    }
//    
//    /// 接收压力数据
//    func receiveStress(_ stress: Int) {
//        DispatchQueue.main.async { [weak self] in
//            guard let self = self else { return }
//            self.lastStress = stress
//            self.isMeasuringStress = false  // 收到压力值后，将测量状态设为false
//            print("接收到压力值: \(stress)")
//            
//            // 保存压力数据到本地数据库
//            let userId = UserDefaults.standard.string(forKey: "userId") ?? "testUser"
//            let deviceId = self.deviceInfo?.macAddress
//            
//            // 添加单条压力记录
//            HealthDataManager.shared.addStress(
//                userId: userId,
//                value: Int16(stress),
//                timestamp: Date(),
//                deviceId: deviceId
//            )
//            
//            // 重新计算并更新当天的日平均压力值
//            if let averageStress = HealthDataManager.shared.calculateAndSaveDailyStress(userId: userId, date: Date()) {
//                print("已更新今日平均压力值: \(averageStress)，压力评分: \(100 - Int(averageStress.rounded()))")
//            }
//            
//            // 发送通知
//            NotificationCenter.default.post(
//                name: .stressMeasured,
//                object: nil,
//                userInfo: ["value": stress]
//            )
//        }
//    }
//    
//    // MARK: - 可选实现的代理方法
//    
//    // 接收佩戴状态
//    func receiveWearState(_ state: Int) {
//        DispatchQueue.main.async { [weak self] in
//            self?.wearingState = state
//            print("接收到佩戴状态: \(state == 1 ? "已佩戴" : "未佩戴")")
//            
//            // 发送通知
//            NotificationCenter.default.post(
//                name: .wearStateChanged,
//                object: nil,
//                userInfo: ["state": state]
//            )
//        }
//    }
//    
//    // 接收单次温度测量结果
//    func receiveTemperature(_ value: Double) {
//        DispatchQueue.main.async { [weak self] in
//            self?.lastTemperature = value
//            self?.isMeasuringTemperature = false
//            print("接收到体温测量结果: \(value)°C")
//            
//            // 发送通知
//            NotificationCenter.default.post(
//                name: .temperatureMeasured,
//                object: nil,
//                userInfo: ["value": value]
//            )
//        }
//    }
//    
//    // 接收单次血压测量结果 - 注意参数名称
//    func receiveBloodPressure(_ sbp: Int, _ dbp: Int) {
//        DispatchQueue.main.async { [weak self] in
//            self?.lastBloodPressure = (systolic: sbp, diastolic: dbp)
//            self?.isMeasuringBloodPressure = false
//            print("接收到血压测量结果: 收缩压 \(sbp), 舒张压 \(dbp)")
//            
//            // 发送通知
//            NotificationCenter.default.post(
//                name: .bloodPressureMeasured,
//                object: nil,
//                userInfo: ["systolic": sbp, "diastolic": dbp]
//            )
//        }
//    }
//    
//    // 其他必须实现但我们暂时不关心的回调
//    func recevieTakePhoto() {
//        print("接收到拍照指令")
//    }
//    
//    func receiveKnockSwitch(_ model: CRPTouchModel) {
//        print("接收到敲击开关状态: \(model)")
//    }
//    
//    func goodixDidState(_ state: CRPState) {
//        print("Goodix状态变化: \(state)")
//    }
//    
//    func goodixRestoreState(_ state: CRPRestoreState) {
//        print("Goodix恢复状态: \(state)")
//    }
//    
//    // MARK: - GoMore相关回调
//    
//    /// 收到GoMore睡眠数据列表
//    func didReceiveGoMoreSleepDataList(_ sleepIds: [Int]) {
//        DispatchQueue.main.async { [weak self] in
//            guard let self = self else { return }
//            print("📱 SDK回调：收到GoMore睡眠ID列表，共 \(sleepIds.count) 条")
//            
//            // 处理睡眠ID列表
//            self.receiveGoMoreSleepIDs(sleepIds)
//        }
//    }
//    
//    /// 收到GoMore睡眠详情数据
//    func didReceiveGoMoreSleepData(_ sleepData: Any, id: Int) {
//        DispatchQueue.main.async { [weak self] in
//            guard let self = self else { return }
//            print("📱 SDK回调：收到GoMore睡眠详情数据，ID: \(id)")
//            
//            // 处理睡眠详情数据
//            self.receiveGoMoreSleepDetail(id: id, data: sleepData)
//        }
//    }
//}

// MARK: - 通知名称扩展
// 注意：所有通知名称已移至 NotificationExtensions.swift 中统一管理，此段代码已被注释掉
/*
extension Notification.Name {
    // 设备通知
    static let deviceConnected = Notification.Name("deviceConnected")
    static let deviceConnecting = Notification.Name("deviceConnecting")
    static let deviceDisconnected = Notification.Name("deviceDisconnected")
    
    // GoMore通知
    static let receivedGoMoreSleepIdsNotification = Notification.Name("receivedGoMoreSleepIds")
    static let receivedGoMoreSleepDetailNotification = Notification.Name("receivedGoMoreSleepDetail")
}
*/

// MARK: - GoMore相关方法扩展
extension WindRingDeviceService {
    /// 接收GoMore睡眠ID列表
    /// - Parameter ids: 睡眠ID列表
    func receiveGoMoreSleepIDs(_ ids: [Int]) {
        print("收到GoMore睡眠ID列表，共 \(ids.count) 条记录")
        
        // 将ID列表发送到通知中心
        NotificationCenter.default.post(
            name: Notification.Name.receivedGoMoreSleepIdsNotification,
            object: nil,
            userInfo: ["sleepIds": ids]
        )
    }
    
    /// 接收GoMore睡眠详情数据
    /// - Parameters:
    ///   - id: 睡眠ID
    ///   - data: 睡眠详情数据
    func receiveGoMoreSleepDetail(id: Int, data: Any) {
        print("收到GoMore睡眠详情数据，ID: \(id)")
        
        // 将详情数据发送到通知中心
        NotificationCenter.default.post(
            name: Notification.Name.receivedGoMoreSleepDetailNotification,
            object: nil,
            userInfo: ["sleepId": id, "sleepData": data]
        )
    }
}

// MARK: - 压力相关方法扩展
extension WindRingDeviceService {
    /// 获取当天的平均压力值
    /// - Parameter completion: 完成回调，返回平均压力值（0-100）
    func getDailyAverageStress(completion: @escaping (Double) -> Void) {
        // 如果设备已连接，尝试从设备获取数据
        if connectionState.isConnected {
            print("尝试从设备获取全天压力数据")
            
            #if os(iOS)
            // 使用正确的SDK方法获取压力数据记录
            CRPSmartRingSDK.sharedInstance.getStressRecord { [weak self] records, error in
                guard let self = self else { return }
                
                DispatchQueue.main.async {
                    if error == .none && !records.isEmpty {
                        // 计算平均值
                        let stressValues = records.map { $0.stress }
                        let sum = stressValues.reduce(0, +)
                        let average = Double(sum) / Double(stressValues.count)
                        print("从设备获取到\(stressValues.count)个压力值，平均值为\(average)")
                        
                        // 更新最新压力值
                        if let lastValue = stressValues.last {
                            self.lastStress = lastValue
                        }
                        
                        // 返回平均值
                        completion(average)
                    } else {
                        // 若失败或无数据，返回模拟值
                        let simulatedValue = self.getSimulatedStressValue()
                        print("获取压力数据失败或无数据，返回模拟值: \(simulatedValue)")
                        completion(simulatedValue)
                    }
                }
            }
            #else
            // 非iOS平台返回模拟数据
            DispatchQueue.main.async {
                let simulatedValue = self.getSimulatedStressValue()
                completion(simulatedValue)
            }
            #endif
        } else {
            // 设备未连接，返回模拟数据
            DispatchQueue.main.async {
                let simulatedValue = self.getSimulatedStressValue()
                completion(simulatedValue)
            }
        }
    }
    
    /// 获取模拟的压力值
    /// - Returns: 模拟压力值（0-100）
    private func getSimulatedStressValue() -> Double {
        // 仅用于测试和开发：50±15的随机值
        let baseValue = 50.0
        let variance = Double.random(in: -15...15)
        let value = baseValue + variance
        print("生成模拟压力值: \(value)")
        
        // 确保值在0-100范围内
        return min(max(value, 0), 100)
    }
}

// MARK: - 通知名称扩展
extension Notification.Name {
    // 压力测量相关通知
    static let stressMeasurementTimeout = Notification.Name("stressMeasurementTimeout")
}

class RingInfo: NSObject {
    var localName: String?
    var kCABAdvidataLocalName: String?
    var advertisementData: [String: AnyObject] = [:]  // 不保存
    var peripheral: CRPDiscovery? = nil         // 不保存
    var RSSI: Int = 0
    var firmwareVersion: String?
    var mac: String?
    var ver: String = ""
    var platform: Int = 0
    var isPair: Bool = false
    var isTalk: Bool = false
    var info: CRPSmartRing.CRPRingInfoModel?
    var hide: Int?
    var peripheralUUID: String?
    var serial: String?
    var connectionState: ConnectionState = .disconnected
    
    var isHighEnd: Bool = false
    

    // 转换为 Dictionary 用于存储
    func toDict() -> [String: Any] {
        return [
            "localName": localName ?? "",
            "kCABAdvidataLocalName": kCABAdvidataLocalName ?? "",
            "mac": mac ?? "",
            "ver": ver,
            "platform": platform,
            "isPair": isPair,
            "isTalk": isTalk,
            "hide": hide ?? 0,
            "firmwareVersion": firmwareVersion ?? "",
            "RSSI": RSSI,
            "peripheralUUID": peripheralUUID ?? "",
            "serial": serial ?? "",
            "connectionState": connectionState.rawValue,
            "isHighEnd": RingModelEnum.init(rawValue: localName ?? "")?.isHighEnd ?? false
            
        ]
    }

    // 从 Dictionary 还原对象
    static func fromDict(_ dict: [String: Any]) -> RingInfo {
        let info = RingInfo()
        info.localName = dict["localName"] as? String
        info.kCABAdvidataLocalName = dict["kCABAdvidataLocalName"] as? String
        info.mac = dict["mac"] as? String
        info.ver = dict["ver"] as? String ?? ""
        info.platform = dict["platform"] as? Int ?? 0
        info.isPair = dict["isPair"] as? Bool ?? false
        info.isTalk = dict["isTalk"] as? Bool ?? false
        info.hide = dict["hide"] as? Int
        info.peripheralUUID = dict["peripheralUUID"] as? String
        info.isHighEnd = dict["isHighEnd"] as? Bool ?? false
        if let state = dict["connectionState"] as? Int {
            if state == 1 {
                info.connectionState = .connecting
            }else if state == 2{
                info.connectionState = .connected
            }
        }
        
        return info
    }
    
}
enum RingModelEnum: String {
    case herzP1 = "Herz P1 Ring"
    case vRing = "VRing"
    case vr11 = "VR11"
    case daRing = "Da Ring"
    
    var isHighEnd: Bool {
        switch self {
        case .herzP1, .daRing:
            return true
        case .vRing, .vr11:
            return false
        }
    }
}

// MARK: - 设备连接状态枚举
enum ConnectionState: Equatable {
    case disconnected
    case connecting
    case connected
    case failed(Error?)
    
    var description: String {
        switch self {
        case .disconnected: return "disconnected".localized
        case .connecting: return "connecting".localized
        case .connected: return "connected".localized
        case .failed(let error): return "\(error?.localizedDescription ?? "--")"
        }
    }
    var color:Color{
        switch self {
        case .connected:
            return .green
        case .connecting:
            return .yellow
        case .disconnected:
            return .orange
        case .failed(_):
            return .orange
        }
    }
    
    var isConnected: Bool {
        if case .connected = self {
            return true
        }
        return false
    }
    
    // 添加rawValue属性
    var rawValue: Int {
        switch self {
        case .disconnected: return 0
        case .connecting: return 1
        case .connected: return 2
        case .failed: return 3
        }
    }
    
    // 实现Equatable协议
    static func == (lhs: ConnectionState, rhs: ConnectionState) -> Bool {
        switch (lhs, rhs) {
        case (.disconnected, .disconnected):
            return true
        case (.connecting, .connecting):
            return true
        case (.connected, .connected):
            return true
        case (.failed, .failed):
            return true
        default:
            return false
        }
    }
}
