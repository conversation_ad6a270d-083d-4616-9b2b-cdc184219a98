import Foundation
import Combine
import CocoaMQTT

// 注意：需要添加CocoaMQTT依赖
// 可以通过CocoaPods或Swift Package Manager添加
// pod 'CocoaMQTT'
// 或在Swift Package Manager中添加 https://github.com/emqx/CocoaMQTT.git

/// MQTT消息类型
enum MQTTMessageType: String, Codable {
    case healthData = "health_data"
    case deviceStatus = "status"
    case command = "command"
    case config = "config"
    case notification = "notification"
    case alert = "alert"
}

/// MQTT消息
struct MQTTMessage: Codable {
    let type: MQTTMessageType
    let timestamp: TimeInterval
    let data: [String: Any]
    
    enum CodingKeys: String, CodingKey {
        case type, timestamp, data
    }
    
    init(type: MQTTMessageType, data: [String: Any]) {
        self.type = type
        self.timestamp = Date().timeIntervalSince1970
        self.data = data
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(type.rawValue, forKey: .type)
        try container.encode(timestamp, forKey: .timestamp)
        
        // 将data字典编码为JSON
        let dataData = try JSONSerialization.data(withJSONObject: data)
        let dataString = String(data: dataData, encoding: .utf8)
        try container.encode(dataString, forKey: .data)
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let typeString = try container.decode(String.self, forKey: .type)
        type = MQTTMessageType(rawValue: typeString) ?? .healthData
        timestamp = try container.decode(TimeInterval.self, forKey: .timestamp)
        
        // 将JSON字符串解码为字典
        let dataString = try container.decode(String.self, forKey: .data)
        if let dataData = dataString.data(using: .utf8),
           let jsonObject = try? JSONSerialization.jsonObject(with: dataData),
           let jsonDict = jsonObject as? [String: Any] {
            data = jsonDict
        } else {
            data = [:]
        }
    }
}

/// MQTT服务类
class MQTTService: NSObject, ObservableObject {
    // 单例
    public static let shared = MQTTService()
    
    // MQTT连接参数
    private let host = "*************"
    private let port: UInt16 = 12930
    private var clientID: String
    private var username: String?
    private var password: String?
    
    // MQTT客户端
    private var mqttClient: CocoaMQTT?
    
    // 内部访问器，允许MQTTSyncService访问mqttClient
    internal var client: CocoaMQTT? {
        return mqttClient
    }
    
    // 发布者
    @Published var connectionStatus: Bool = false
    @Published var lastError: Error?
    @Published var receivedMessages: [String: Any] = [:]
    
    // 自动连接相关
    @Published var autoConnect: Bool = true // 是否自动连接的标志
    @Published var userDisconnected: Bool = false // 用户是否主动断开连接的标志
    
    // 主题前缀
    private let ringTopicPrefix = "ring/"
    private let userTopicPrefix = "user/"
    private let appTopicPrefix = "app/"
    
    // 初始化
    private override init() {
        // 生成唯一客户端ID
        self.clientID = "app-\(UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString)"
        super.init()
        
        // 从UserDefaults读取设置
        self.autoConnect = UserDefaults.standard.bool(forKey: "mqtt_autoConnect")
        self.userDisconnected = UserDefaults.standard.bool(forKey: "mqtt_userDisconnected")
        
        // 添加通知中心观察者，监听应用进入前台
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // 应用进入前台时尝试重新连接
    @objc private func appWillEnterForeground() {
        print("MQTT - 应用进入前台")
        if autoConnect && !userDisconnected && !connectionStatus {
            print("MQTT - 尝试自动重新连接")
            connect()
        }
    }
    
    // MARK: - 公共方法
    
    /// 设置认证信息
    func setCredentials(username: String, password: String) {
        self.username = username
        self.password = password
    }
    
    /// 连接到MQTT服务器
    func connect() {
        print("MQTT - 开始连接过程")
        
        // 如果用户曾经主动断开，重置标志
        if userDisconnected {
            userDisconnected = false
            UserDefaults.standard.set(false, forKey: "mqtt_userDisconnected")
        }
        
        // 实际实现时，使用CocoaMQTT连接
        let mqtt = CocoaMQTT(clientID: clientID, host: host, port: port)
        mqtt.username = username
        mqtt.password = password
        mqtt.keepAlive = 60
        mqtt.delegate = self
        mqtt.autoReconnect = true // 启用自动重连功能
        mqtt.autoReconnectTimeInterval = 5 // 自动重连间隔5秒
        let _ = mqtt.connect()
        self.mqttClient = mqtt
        
        print("正在连接到MQTT服务器: \(host):\(port)")
    }
    
    /// 断开连接 - 用户主动调用
    func disconnect() {
        print("MQTT - 用户主动断开连接")
        // 设置用户主动断开标志
        userDisconnected = true
        UserDefaults.standard.set(true, forKey: "mqtt_userDisconnected")
        
        // 实际实现时，断开CocoaMQTT连接
        if let mqtt = mqttClient {
            mqtt.disconnect()
        }
        
        print("已断开MQTT连接")
        connectionStatus = false
    }
    
    /// 订阅主题
    func subscribe(to topic: String, qos: Int = 1) {
        // 实际实现时，使用CocoaMQTT订阅
        if let mqtt = mqttClient {
            mqtt.subscribe(topic, qos: CocoaMQTTQoS(rawValue: UInt8(qos))!)
        }
        
        print("已订阅主题: \(topic)")
    }
    
    /// 发布消息
    func publish(to topic: String, message: MQTTMessage, qos: Int = 1) {
        do {
            let jsonData = try JSONEncoder().encode(message)
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                // 实际实现时，使用CocoaMQTT发布
                if let mqtt = mqttClient {
                    mqtt.publish(topic, withString: jsonString, qos: CocoaMQTTQoS(rawValue: UInt8(qos))!)
                }
                
                print("已发布消息到主题: \(topic)")
                print("消息内容: \(jsonString)")
            }
        } catch {
            print("消息编码失败: \(error)")
            lastError = error
        }
    }
    
    /// 切换自动连接设置
    func toggleAutoConnect(_ enabled: Bool) {
        autoConnect = enabled
        UserDefaults.standard.set(enabled, forKey: "mqtt_autoConnect")
        
        // 如果启用自动连接且当前未连接，则尝试连接
        if autoConnect && !connectionStatus && !userDisconnected {
            connect()
        }
    }
    
    // MARK: - 辅助方法
    
    /// 获取戒指主题
    func ringTopic(ringId: String, type: String) -> String {
        return "\(ringTopicPrefix)\(ringId)/\(type)"
    }
    
    /// 获取用户主题
    func userTopic(userId: String, type: String) -> String {
        return "\(userTopicPrefix)\(userId)/\(type)"
    }
    
    /// 获取应用主题
    func appTopic(deviceId: String, type: String) -> String {
        return "\(appTopicPrefix)\(deviceId)/\(type)"
    }
    
    /// 发布健康数据
    func publishHealthData(ringId: String, data: [String: Any]) {
        let message = MQTTMessage(type: .healthData, data: data)
        publish(to: ringTopic(ringId: ringId, type: "health_data"), message: message)
    }
    
    /// 发布设备状态
    func publishDeviceStatus(ringId: String, status: [String: Any]) {
        let message = MQTTMessage(type: .deviceStatus, data: status)
        publish(to: ringTopic(ringId: ringId, type: "status"), message: message)
    }
    
    /// 订阅用户通知
    func subscribeToUserNotifications(userId: String) {
        subscribe(to: userTopic(userId: userId, type: "notifications"))
    }
    
    /// 订阅戒指命令
    func subscribeToRingCommands(ringId: String) {
        subscribe(to: ringTopic(ringId: ringId, type: "commands"))
    }
}

// MARK: - MQTT代理
extension MQTTService: CocoaMQTTDelegate {
    func mqtt(_ mqtt: CocoaMQTT, didConnectAck ack: CocoaMQTTConnAck) {
        if ack == .accept {
            print("MQTT连接成功")
            connectionStatus = true
        } else {
            print("MQTT连接失败: \(ack)")
            connectionStatus = false
        }
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didPublishMessage message: CocoaMQTTMessage, id: UInt16) {
        print("消息发布成功: \(message.topic)")
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didPublishAck id: UInt16) {
        print("消息发布确认: \(id)")
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didReceiveMessage message: CocoaMQTTMessage, id: UInt16) {
        print("收到消息: \(message.topic), 内容: \(message.string ?? "")")
        
        if let string = message.string, let data = string.data(using: .utf8) {
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    receivedMessages[message.topic] = json
                }
            } catch {
                print("消息解析失败: \(error)")
            }
        }
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didSubscribeTopics success: NSDictionary, failed: [String]) {
        print("订阅成功: \(success), 失败: \(failed)")
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didUnsubscribeTopics topics: [String]) {
        print("取消订阅: \(topics)")
    }
    
    func mqtt(_ mqtt: CocoaMQTT, didReceive trust: SecTrust, completionHandler: @escaping (Bool) -> Void) {
        // 默认信任所有证书，生产环境中应该进行适当的证书验证
        completionHandler(true)
    }
    
    func mqttDidPing(_ mqtt: CocoaMQTT) {
        print("MQTT Ping")
    }
    
    func mqttDidReceivePong(_ mqtt: CocoaMQTT) {
        print("MQTT Pong")
    }
    
    func mqttDidDisconnect(_ mqtt: CocoaMQTT, withError err: Error?) {
        connectionStatus = false
        if let error = err {
            print("MQTT断开连接: \(error)")
            lastError = error
        } else {
            print("MQTT断开连接")
        }
    }
} 