import Foundation
import Combine
import CoreData
import Network
import CRPSmartRing

/// 压力数据上传服务
class StressUploadService {
    // 单例模式
    static let shared = StressUploadService()
    
    // 依赖服务
    private let deviceService = WindRingDeviceService.shared
    private let healthDataManager = HealthDataManager.shared
    private let authService = AuthService.shared
    private let userService = WindRingUserService.shared
    
    // 上传API路径
    private let apiPath = "/app-api/iot/stress/upload/data"
    
    // 上传状态跟踪
    private var isUploading: Bool = false
    private var lastUploadAttempt: Date?
    private var cancellables = Set<AnyCancellable>()
    
    // 私有初始化方法，配置通知订阅
    private init() {
        setupNotifications()
    }
    
    // MARK: - 公共方法
    
    /// 从设备获取压力历史数据并保存到本地
    /// - Parameter completion: 完成回调，返回获取的数据数量和可能的错误
    func fetchAndSaveStressHistory(completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        print("从设备获取压力历史数据...")
        
        // 调用SDK获取历史压力数据
        CRPSmartRingSDK.sharedInstance.getStressRecord { [weak self] records, error in
            guard let self = self else { return }
            
            if error == .none && !records.isEmpty {
                print("成功获取\(records.count)条压力历史数据")
                
                // 将数据保存到本地数据库
                self.saveStressData(records: records) { savedCount in
                    print("已保存\(savedCount)条新压力数据到本地")
                    DispatchQueue.main.async {
                        completion(savedCount, nil)
                        
                        // 自动尝试上传
                        if savedCount > 0 {
                            self.uploadPendingStressData()
                        }
                    }
                }
            } else {
                let errorMessage = error == .none ? "未获取到数据" : "错误: \(error)"
                print("获取压力历史数据失败: \(errorMessage)")
                DispatchQueue.main.async {
                    completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: errorMessage]))
                }
            }
        }
    }
    
    /// 从设备获取定时压力数据并保存到本地
    /// - Parameters:
    ///   - day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
    ///   - completion: 完成回调，返回获取的数据数量和可能的错误
    func fetchAndSaveTimingStress(day: Int = 0, completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        print("从设备获取压力数据...")
        
        // 使用与fetchAndSaveStressHistory相同的方式，直接调用SDK获取所有压力数据
        CRPSmartRingSDK.sharedInstance.getStressRecord { [weak self] records, error in
            guard let self = self else { return }
            
            if error == .none && !records.isEmpty {
                print("成功获取\(records.count)条压力数据记录")
                
                // 打印前5条数据用于调试
                for (index, record) in records.prefix(5).enumerated() {
                    let date = Date(timeIntervalSince1970: TimeInterval(record.time))
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                    print("记录\(index+1): 压力值 \(record.stress)，时间: \(formatter.string(from: date))")
                }
                
                // 将数据保存到本地数据库
                self.saveStressData(records: records) { savedCount in
                    print("已保存\(savedCount)条新压力数据到本地")
                    DispatchQueue.main.async {
                        completion(savedCount, nil)
                        
                        // 自动尝试上传
                        if savedCount > 0 {
                            self.uploadPendingStressData()
                        }
                    }
                }
            } else {
                let errorMessage = error == .none ? "未获取到数据" : "错误: \(error)"
                print("获取压力数据失败: \(errorMessage)")
                DispatchQueue.main.async {
                    completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: errorMessage]))
                }
            }
        }
    }
    
    /// 获取并上传最近几天的定时压力数据
    /// - Parameters:
    ///   - days: 要获取的天数，从0（今天）开始
    ///   - completion: 完成回调，返回获取的总数据数量和可能的错误
    func syncTimingStressData(days: Int = 3, completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        print("开始同步压力数据...")
        
        // 直接使用fetchAndSaveStressHistory方法获取所有压力数据
        fetchAndSaveStressHistory { count, error in
            if let error = error {
                print("获取压力数据失败: \(error.localizedDescription)")
                completion(0, error)
                return
            }
            
            print("成功获取\(count)条压力数据")
            
            if count > 0 {
                // 执行上传操作
                self.uploadPendingStressData { uploadCount, uploadError in
                    if let uploadError = uploadError {
                        print("上传压力数据失败: \(uploadError.localizedDescription)")
                    } else {
                        print("成功上传\(uploadCount)条压力数据")
                    }
                    
                    completion(count, uploadError)
                }
            } else {
                print("没有获取到压力数据")
                completion(0, nil)
            }
        }
    }
    
    /// 上传待上传的压力数据
    /// - Parameter completion: 完成回调，包含上传成功的数据数量和可能的错误
    func uploadPendingStressData(completion: ((Int, Error?) -> Void)? = nil) {
        // 防止重复上传
        guard !isUploading else {
            print("已有上传任务正在进行中")
            completion?(0, nil)
            return
        }
        
        // 检查网络可用性
        guard NetworkMonitor.shared.isConnected else {
            print("网络不可用，稍后将自动重试上传")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "网络不可用"]))
            return
        }
        
        // 检查用户是否登录
        guard let userId = authService.currentUser?.id else {
            print("用户未登录，无法上传数据")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        // 验证Token是否存在
        guard let token = authService.currentToken?.accessToken else {
            print("用户未登录或Token已过期，无法上传数据")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录或Token已过期"]))
            return
        }
        
        print("用户已登录，Token: \(token.prefix(10))...")
        
        isUploading = true
        lastUploadAttempt = Date()
        
        // 获取所有压力数据（因为没有isUploaded标记，所以上传所有）
        // 使用最近7天的数据
        let calendar = Calendar.current
        let endDate = Date()
        let startDate = calendar.date(byAdding: .day, value: -7, to: endDate)!
        
        let stressData = healthDataManager.getStress(userId: userId, startDate: startDate, endDate: endDate)
        
        if stressData.isEmpty {
            print("没有需要上传的压力数据")
            isUploading = false
            completion?(0, nil)
            return
        }
        
        print("找到\(stressData.count)条待上传的压力数据")
        
        // 按日期分组
        let groupedData = Dictionary(grouping: stressData) { stress -> String in
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            return dateFormatter.string(from: stress.timestamp!)
        }
        
        print("压力数据按\(groupedData.count)个日期分组")
        
        // 逐日上传
        var uploadedCount = 0
        let group = DispatchGroup()
        var uploadError: Error? = nil
        
        for (dateString, stressValues) in groupedData {
            group.enter()
            
            // 准备上传数据
//            let uploadData = prepareStressUploadData(dateString: dateString, stressValues: stressValues)
            
            // 执行上传
//            uploadStressData(data: uploadData) { success, error in
//                if success {
//                    print("成功上传\(dateString)的\(stressValues.count)条压力数据")
//                    uploadedCount += stressValues.count
//                } else {
//                    print("上传\(dateString)的压力数据失败: \(error?.localizedDescription ?? "未知错误")")
//                    uploadError = error
//                }
//                
//                group.leave()
//            }
        }
        
        // 所有上传完成后的回调
        group.notify(queue: .main) {
            self.isUploading = false
            
            // 发送通知
            NotificationCenter.default.post(
                name: .stressDataUploaded,
                object: nil,
                userInfo: [
                    "count": uploadedCount,
                    "success": uploadError == nil
                ]
            )
            
            completion?(uploadedCount, uploadError)
        }
    }
    
    // MARK: - 私有方法
    
    /// 初始化通知订阅
    private func setupNotifications() {
        // 设备连接成功后，自动获取历史数据
        NotificationCenter.default.publisher(for: .deviceSyncCompleted)
            .filter { notification in
                notification.userInfo?["success"] as? Bool == true
            }
            .debounce(for: .seconds(2), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                guard let self = self else { return }
                print("设备同步完成，准备获取定时压力数据")
                self.syncTimingStressData(days: 3) { count, error in
                    if error == nil && count > 0 {
                        print("自动获取并上传了\(count)条定时压力数据")
                    }
                }
            }
            .store(in: &cancellables)
        
        // 网络状态变化时，尝试上传
        NotificationCenter.default.publisher(for: .networkStatusChanged)
            .filter { notification in
                notification.userInfo?["connected"] as? Bool == true
            }
            .sink { [weak self] _ in
                guard let self = self else { return }
                print("网络已连接，尝试上传待上传的压力数据")
                self.uploadPendingStressData()
            }
            .store(in: &cancellables)
    }
    
    /// 将从设备获取的压力数据保存到本地数据库
    /// - Parameters:
    ///   - records: 设备返回的压力记录数组
    ///   - completion: 完成回调，返回成功保存的记录数量
    private func saveStressData(records: [CRPStressRecordModel], completion: @escaping (Int) -> Void) {
        guard let userId = authService.currentUser?.id else {
            print("用户未登录，无法保存压力数据")
            completion(0)
            return
        }
        
        print("准备保存压力数据，共\(records.count)条记录，用户ID：\(userId)")
        
        var savedCount = 0
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown"
        
        // 按日期分组记录，用于后续计算每日平均值
        let calendar = Calendar.current
        var recordsByDay: [Date: [CRPStressRecordModel]] = [:]
        
        for record in records {
            // 如果stress值大于0，则认为是有效数据
            if record.stress > 0 {
                // 打印每条记录的详细信息（调试用）
                let timestamp = Date(timeIntervalSince1970: TimeInterval(record.time))
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                print("保存压力记录: 值=\(record.stress), 时间=\(dateFormatter.string(from: timestamp))")
                
                // 使用健康数据管理器保存记录
//                if healthDataManager.addStressRecord(userId: userId, score: record.stress, timestamp: timestamp) {
//                    savedCount += 1
//                    
//                    // 将记录按天分组
//                    let dayStart = calendar.startOfDay(for: timestamp)
//                    if recordsByDay[dayStart] == nil {
//                        recordsByDay[dayStart] = []
//                    }
//                    recordsByDay[dayStart]?.append(record)
//                } else {
//                    print("保存压力记录失败: 值=\(record.stress), 时间=\(dateFormatter.string(from: timestamp))")
//                }
            }
        }
        
        print("成功保存\(savedCount)/\(records.count)条压力记录")
        
        // 为每个日期计算并保存日平均压力值
        var calculatedDays = 0
        
        for (date, dayRecords) in recordsByDay {
            print("计算日期\(date)的平均压力值，共\(dayRecords.count)条记录")
            
            // 计算该日的平均压力值
            let totalStress = dayRecords.reduce(0) { $0 + $1.stress }
            let averageStress = Double(totalStress) / Double(dayRecords.count)
            
            // 保存日平均值到DailyStress表
            if healthDataManager.addOrUpdateDailyStress(userId: userId, date: date, value: averageStress) {
                calculatedDays += 1
                print("已保存\(date)的日平均压力值: \(averageStress)")
            }
        }
        
        print("已计算并保存\(calculatedDays)天的日平均压力值")
        completion(savedCount)
    }
    
    /// 准备上传数据
    /// - Parameters:
    ///   - dateString: 日期字符串
    ///   - stressValues: 压力数据数组
    /// - Returns: 上传数据模型
//    private func prepareStressUploadData(dateString: String, stressValues: [StressEntity]) -> StressUploadData {
//        var records: [StressRecord] = []
//        
//        print("准备\(dateString)的\(stressValues.count)条压力数据，使用毫秒级时间戳格式")
//        
//        for stress in stressValues {
//            if let timestamp = stress.timestamp {
//                // 使用毫秒级时间戳
//                let timeStampSeconds = Int64(timestamp.timeIntervalSince1970)
//                let timeStampMilliseconds = timeStampSeconds * 1000
//                
//                // 打印转换过程，方便调试
//                let dateFormatter = DateFormatter()
//                dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
//                let timeString = dateFormatter.string(from: timestamp)
//                print("时间转换：\(timeString) -> 秒级时间戳：\(timeStampSeconds) -> 毫秒级时间戳：\(timeStampMilliseconds)")
//                
//                records.append(StressRecord(
//                    stress: Int(stress.value),
//                    time: timeStampMilliseconds
//                ))
//            }
//        }
//        
//        return StressUploadData(
//            date: dateString,
//            records: records
//        )
//    }
    
    /// 上传压力数据到服务器
    /// - Parameters:
    ///   - data: 压力上传数据模型
    ///   - completion: 完成回调
    private func uploadStressData(data: StressUploadData, completion: @escaping (Bool, Error?) -> Void) {
        // 构建API URL
         // 使用开发环境API基础URL
        guard let baseURLString = AuthService.shared.currentToken?.accessToken, let baseURL = URL(string: baseURLString) else {
            completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的服务器URL"]))
            return
        }
        
        let uploadURL = baseURL.appendingPathComponent("/app-api/iot/stress/upload/data")
        print("上传压力数据到URL: \(uploadURL.absoluteString)")
        
        // 创建请求
        var request = URLRequest(url: uploadURL)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加认证信息
        if let token = authService.currentToken?.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("添加Authorization请求头: Bearer \(token.prefix(10))...")
        } else {
            print("警告: 未找到有效的token")
        }
        
        // 添加租户ID
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        print("添加tenant-id请求头: 1")
        
        // 打印所有请求头
        print("请求头:")
        request.allHTTPHeaderFields?.forEach { key, value in
            print("\(key): \(value.prefix(30))...")
        }
        
        // 准备请求体
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let jsonData = try encoder.encode(data)
            
            // 打印上传的JSON数据
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("上传的压力数据JSON: \n\(jsonString)")
            }
            
            request.httpBody = jsonData
            
            // 创建并执行网络请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 处理网络错误
                if let error = error {
                    print("上传压力数据网络错误: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(false, error)
                    }
                    return
                }
                
                // 检查HTTP响应
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("无效的HTTP响应")
                    DispatchQueue.main.async {
                        completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
                    }
                    return
                }
                
                // 打印响应头
                print("响应状态码: \(httpResponse.statusCode)")
                print("响应头:")
                httpResponse.allHeaderFields.forEach { key, value in
                    print("\(key): \(value)")
                }
                
                // 打印响应数据
                if let responseData = data, let responseString = String(data: responseData, encoding: .utf8) {
                    print("服务器响应: \(responseString)")
                }
                
                // 检查状态码
                if httpResponse.statusCode == 200 {
                    print("压力数据上传成功")
                    DispatchQueue.main.async {
                        completion(true, nil)
                    }
                } else {
                    let responseString = data != nil ? String(data: data!, encoding: .utf8) ?? "无数据" : "无数据"
                    print("压力数据上传失败，状态码: \(httpResponse.statusCode), 响应: \(responseString)")
                    
                    // 解析错误响应
                    var errorMessage = "上传失败，HTTP状态码: \(httpResponse.statusCode)"
                    if let data = data {
                        do {
                            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                                print("错误响应JSON: \(json)")
                                
                                if let code = json["code"] as? Int {
                                    print("错误码: \(code)")
                                }
                                
                                if let msg = json["msg"] as? String {
                                    print("错误消息: \(msg)")
                                    errorMessage = msg
                                }
                                
                                if let errorData = json["data"] as? [String: Any] {
                                    print("错误数据: \(errorData)")
                                }
                            }
                        } catch {
                            print("解析错误响应失败: \(error.localizedDescription)")
                        }
                    }
                    
                    // 处理特定错误情况
                    if httpResponse.statusCode == 401 {
                        errorMessage = "用户未登录或身份验证已过期，请重新登录"
                    } else if httpResponse.statusCode == 403 {
                        errorMessage = "用户无权限执行此操作"
                    }
                    
                    DispatchQueue.main.async {
                        let error = NSError(
                            domain: "com.windring.error",
                            code: httpResponse.statusCode,
                            userInfo: [NSLocalizedDescriptionKey: errorMessage]
                        )
                        completion(false, error)
                    }
                }
            }.resume()
        } catch {
            print("编码压力数据失败: \(error.localizedDescription)")
            completion(false, error)
        }
    }
}

// MARK: - 模型定义





// MARK: - 通知扩展
// 注意：所有通知名称应统一在 NotificationExtensions.swift 中定义，此段代码已被注释掉
/*
extension Notification.Name {
    /// 压力数据上传通知
    static let stressDataUploaded = Notification.Name("stressDataUploaded")
} 
*/ 
