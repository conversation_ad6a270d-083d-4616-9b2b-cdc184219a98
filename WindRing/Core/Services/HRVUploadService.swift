import Foundation
import Combine
import CoreData
import Network
#if os(iOS)
import CRPSmartRing
#endif

// 导入HRVData模型
import SwiftUI

/// HRV数据上传服务
public class HRVUploadService {
    /// 单例访问点
    public static let shared = HRVUploadService()
    
    // 依赖服务
    private let deviceService = WindRingDeviceService.shared
    private let apiService = APIService.shared
    private let authService = AuthService.shared
    private let healthDataManager = HealthDataManager.shared
    private let storageManager = StorageManager.shared
    private let networkMonitor = NetworkMonitor.shared
    
    // API路径
    private let apiPath = "/api/health/hrv"
    
    // 取消令牌
    private var cancellables = Set<AnyCancellable>()
    
    // 上传状态
    private var isUploading = false
    
    /// 私有初始化方法
    private init() {
        setupNotifications()
    }
    
    /// 获取HRV数据
    /// - Parameters:
    ///   - day: 指定天数（0表示今天，1表示昨天，以此类推）
    ///   - completion: 完成回调
    public func getHRVData(day: Int, completion: @escaping ([WindRing.HRVRecord]?, Error?) -> Void) {
        print("获取第\(day)天的HRV数据...")
        
        #if os(iOS)
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            let error = NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"])
            completion(nil, error)
            return
        }
        
        // 使用SDK获取HRV数据
        // 正确使用getTimingHRV方法，而不是getHRVData
//        CRPSmartRingSDK.sharedInstance.getTimingHRV(day) { hrvModel, error in
//            if error != .none {
//                print("获取第\(day)天HRV数据失败: \(error)")
//                completion(nil, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取HRV数据失败: \(error)"]))
//                return
//            }
//            
//            // 格式化日期
//            let dateFormatter = ISO8601DateFormatter()
//            dateFormatter.formatOptions = [.withInternetDateTime]
//            
//            // 处理HRV数据
//            var records: [WindRing.HRVRecord] = []
//            
//            // 这里需要根据SDK返回的数据结构进行调整
//            // 假设SDK返回的是一个包含时间戳和HRV值的数组
//            let hrvs = hrvModel.hrvs
//            for (index, hrv) in hrvs.enumerated() {
//                // 计算时间戳
//                let calendar = Calendar.current
//                let today = Date()
//                let targetDate = calendar.date(byAdding: .day, value: -day, to: today) ?? today
//                let startOfDay = calendar.startOfDay(for: targetDate)
//                
//                // 假设每个index代表一个5分钟间隔，从0点开始
//                let timeIntervalInSeconds = TimeInterval(index * 5 * 60)
//                let timestamp = startOfDay.addingTimeInterval(timeIntervalInSeconds)
//                let timeString = dateFormatter.string(from: timestamp)
//                
//                // 只记录有效的HRV值（大于0）
//                if hrv > 0 {
//                    records.append(WindRing.HRVRecord(hrv: hrv, time: timeString))
//                }
//            }
//            
//            print("第\(day)天HRV数据获取成功，有效记录：\(records.count)条")
//            completion(records, nil)
//        }
        #else
        // 在非iOS平台上使用模拟数据
        DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) {
            // 创建模拟数据
            var records: [WindRing.HRVRecord] = []
            
            // 生成一天的随机HRV数据
            // 假设每3小时有一个数据点
            let calendar = Calendar.current
            let today = Date()
            let targetDate = calendar.date(byAdding: .day, value: -day, to: today) ?? today
            let startOfDay = calendar.startOfDay(for: targetDate)
            
            let dateFormatter = ISO8601DateFormatter()
            dateFormatter.formatOptions = [.withInternetDateTime]
            
            for hour in stride(from: 0, to: 24, by: 3) {
                let timestamp = calendar.date(byAdding: .hour, value: hour, to: startOfDay) ?? startOfDay
                let timeString = dateFormatter.string(from: timestamp)
                
                // 生成随机HRV值（通常在20-100之间）
                let hrvValue = Int.random(in: 30...80)
                records.append(WindRing.HRVRecord(hrv: hrvValue, time: timeString))
            }
            
            print("第\(day)天HRV模拟数据生成成功，记录：\(records.count)条")
            completion(records, nil)
        }
        #endif
    }
    
    /// 上传HRV数据
    /// - Parameters:
    ///   - data: 要上传的HRV数据
    ///   - completion: 完成回调
    public func uploadHRVData(data: WindRing.HRVUploadData, completion: @escaping (Bool, Error?) -> Void) {
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        guard let token = authService.currentToken?.accessToken else {
            let error = NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])
            completion(false, error)
            return
        }
        
        isUploading = true
        
        print("开始上传HRV数据，日期：\(data.date)，记录数：\(data.records.count)")
        
        // 构建URL
        guard let baseURL = URL(string: "http://ring-api-dev.weaving-park.com") else {
            let error = NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            isUploading = false
            completion(false, error)
            return
        }
        
        let url = baseURL.appendingPathComponent(apiPath)
        
        // 构建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        
        do {
            // 编码请求数据
            let encoder = JSONEncoder()
            request.httpBody = try encoder.encode(data)
            
            // 发送请求
            URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
                guard let self = self else { return }
                
                self.isUploading = false
                
                if let error = error {
                    print("HRV数据上传失败: \(error.localizedDescription)")
                    completion(false, error)
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    let error = NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的响应"])
                    completion(false, error)
                    return
                }
                
                if httpResponse.statusCode == 200 {
                    print("HRV数据上传成功")
                    completion(true, nil)
                } else {
                    var errorMessage = "服务器错误，状态码：\(httpResponse.statusCode)"
                    
                    if httpResponse.statusCode == 401 {
                        errorMessage = "用户未登录或身份验证已过期，请重新登录"
                    } else if httpResponse.statusCode == 403 {
                        errorMessage = "用户无权限执行此操作"
                    }
                    
                    let error = NSError(
                        domain: "com.windring.error",
                        code: httpResponse.statusCode,
                        userInfo: [NSLocalizedDescriptionKey: errorMessage]
                    )
                    completion(false, error)
                }
            }.resume()
        } catch {
            print("编码HRV数据失败: \(error.localizedDescription)")
            isUploading = false
            completion(false, error)
        }
    }
    
    /// 从设备同步多天的HRV数据并上传
    /// - Parameters:
    ///   - days: 要同步的天数
    ///   - completion: 完成回调
    func syncAndUploadHRVData(days: Int = 7, completion: @escaping (Bool, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        print("开始同步最近\(days)天的HRV数据...")
        
        let group = DispatchGroup()
        var allSuccess = true
        var lastError: Error? = nil
        
        // 获取并上传过去几天的数据
        for day in 0..<days {
            group.enter()
            
            // 获取HRV数据
            self.getHRVData(day: day) { records, error in
                if let error = error {
                    print("获取第\(day)天HRV数据失败: \(error.localizedDescription)")
                    allSuccess = false
                    lastError = error
                    group.leave()
                    return
                }
                
                guard let records = records, !records.isEmpty else {
                    print("第\(day)天没有HRV数据可上传")
                    group.leave()
                    return
                }
                
                // 格式化日期
                let calendar = Calendar.current
                let today = Date()
                let targetDate = calendar.date(byAdding: .day, value: -day, to: today) ?? today
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                let dateString = dateFormatter.string(from: targetDate)
                
                // 准备上传数据
                let uploadData = WindRing.HRVUploadData(date: dateString, records: records)
                
                // 上传数据
                self.uploadHRVData(data: uploadData) { success, uploadError in
                if success {
                        print("第\(day)天HRV数据上传成功")
                } else {
                        print("第\(day)天HRV数据上传失败: \(uploadError?.localizedDescription ?? "未知错误")")
                        allSuccess = false
                        lastError = uploadError ?? lastError
                    }
                    
                    group.leave()
                }
            }
            
            // 稍微延迟一下，避免设备或SDK无法处理连续请求
            Thread.sleep(forTimeInterval: 1.0)
        }
        
        // 所有天数的数据同步完成后，返回结果
        group.notify(queue: .main) {
            if allSuccess {
                print("成功同步并上传了\(days)天的HRV数据")
                completion(true, nil)
            } else {
                print("同步或上传HRV数据过程中发生错误")
                completion(false, lastError)
            }
        }
    }
    
    /// 同步最近几天的HRV定时数据
    /// - Parameters:
    ///   - days: 天数，默认7天
    ///   - completion: 完成回调，返回同步的数据条数和可能的错误
    func syncTimingHRVData(days: Int = 7, completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            let error = NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接，无法同步HRV数据"])
            completion(0, error)
            return
        }
        
        print("开始同步最近\(days)天的HRV定时数据...")
        
        var totalCount = 0
        let group = DispatchGroup()
        var lastError: Error? = nil
        
        // 获取并存储过去几天的数据
        for day in 0..<days {
            group.enter()
            
            // 获取HRV数据
            self.getHRVData(day: day) { [weak self] records, error in
                guard let self = self else {
                    group.leave()
                    return
                }
                
                if let error = error {
                    print("获取第\(day)天HRV数据失败: \(error.localizedDescription)")
                    lastError = error
                    group.leave()
                    return
                }
                
                guard let records = records, !records.isEmpty else {
                    print("第\(day)天没有HRV数据可保存")
                    group.leave()
                    return
                }
                
                // 格式化日期
                let calendar = Calendar.current
                let today = Date()
                let targetDate = calendar.date(byAdding: .day, value: -day, to: today) ?? today
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                let dateString = dateFormatter.string(from: targetDate)
                
                print("成功获取第\(day)天(\(dateString))的HRV数据，共\(records.count)条")
                
                // 这里应该将数据保存到本地数据库，以便后续上传
                self.saveHRVDataToLocalDB(date: dateString, records: records) { success in
                    if success {
                        totalCount += records.count
                        print("成功保存第\(day)天HRV数据到本地，共\(records.count)条")
                    } else {
                        print("保存第\(day)天HRV数据到本地失败")
                    }
                group.leave()
                }
            }
            
            // 增加延迟时间，为设备或SDK处理请求提供足够时间
            Thread.sleep(forTimeInterval: 1.0)
        }
        
        // 所有天数的数据同步完成后，返回结果
        group.notify(queue: .main) {
            if totalCount > 0 {
                print("成功同步了\(days)天的HRV数据，共\(totalCount)条")
                
                // 发送通知
                NotificationCenter.default.post(
                    name: Notification.Name.hrvDataSynced,
                    object: nil,
                    userInfo: ["count": totalCount, "success": true]
                )
                
                // 如果有网络，尝试多次自动上传数据，以确保所有数据都被上传
                if self.networkMonitor.isConnected {
                    // 延迟2秒后再上传，确保数据已经保存完成
                    DispatchQueue.global().asyncAfter(deadline: .now() + 2.0) {
                self.uploadPendingHRVData { uploadCount, uploadError in
                            if let uploadError = uploadError {
                                print("同步后自动上传HRV数据失败: \(uploadError.localizedDescription)")
                                
                                // 首次上传失败，等待3秒后重试一次
                                DispatchQueue.global().asyncAfter(deadline: .now() + 3.0) {
                                    self.uploadPendingHRVData { retryCount, retryError in
                                        if let retryError = retryError {
                                            print("重试上传HRV数据失败: \(retryError.localizedDescription)")
                                        } else {
                                            print("重试上传HRV数据成功，上传了\(retryCount)条")
                                        }
                                    }
                                }
                            } else {
                                print("同步后自动上传HRV数据成功，上传了\(uploadCount)条")
                            }
                        }
                    }
                }
                
                completion(totalCount, nil)
            } else {
                print("没有同步到任何HRV数据")
                completion(0, lastError ?? NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "没有找到HRV数据"]))
            }
        }
    }
    
    /// 保存HRV数据到本地数据库
    /// - Parameters:
    ///   - date: 日期字符串，格式为"yyyy-MM-dd"
    ///   - records: HRV数据记录
    ///   - completion: 完成回调
    private func saveHRVDataToLocalDB(date: String, records: [WindRing.HRVRecord], completion: @escaping (Bool) -> Void) {
        // 这里实现保存HRV数据到CoreData的逻辑
        // 示例实现，实际应根据项目的数据库结构调整
        storageManager.performBackgroundTask { context in
            do {
                // 插入HRV数据
                for record in records {
                    // 创建或获取HRV实体
                    let entity = NSEntityDescription.entity(forEntityName: "HRVEntity", in: context)!
                    let hrvEntity = NSManagedObject(entity: entity, insertInto: context)
                    
                    // 设置属性
                    hrvEntity.setValue(record.hrv, forKey: "hrvValue")
                    hrvEntity.setValue(record.time, forKey: "timeISOString")
                    hrvEntity.setValue(date, forKey: "dateString")
                    hrvEntity.setValue(false, forKey: "isUploaded")
                    
                    // 如果需要，设置其他属性
                    // ...
                }
                
                // 上下文在performBackgroundTask中自动保存
                DispatchQueue.main.async {
                    completion(true)
                }
            } catch {
                print("保存HRV数据到CoreData失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(false)
                }
            }
        }
    }
    
    /// 上传待处理的HRV数据
    /// - Parameter completion: 完成回调，返回上传的数据条数和可能的错误
    func uploadPendingHRVData(completion: @escaping (Int, Error?) -> Void) {
        guard networkMonitor.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "网络未连接"]))
            return
        }
        
        guard let token = authService.currentToken?.accessToken else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        // 获取未上传的HRV数据
        fetchPendingHRVData { pendingData in
            if pendingData.isEmpty {
            print("没有待上传的HRV数据")
                completion(0, nil)
            return
        }
        
            print("发现\(pendingData.count)个日期的HRV数据待上传")
            
            var totalUploaded = 0
            let group = DispatchGroup()
            var lastError: Error? = nil
            
            // 按日期上传数据
            for (date, records) in pendingData {
                if records.isEmpty {
                    continue
                }
                
            group.enter()
            
            // 准备上传数据
                let uploadData = WindRing.HRVUploadData(date: date, records: records)
            
                // 上传数据
                self.uploadHRVData(data: uploadData) { success, error in
                if success {
                        totalUploaded += records.count
                        print("成功上传\(date)的\(records.count)条HRV数据")
                        
                    // 标记这些数据为已上传
                        self.markHRVDataAsUploaded(date: date, records: records)
                } else {
                        print("上传\(date)的HRV数据失败: \(error?.localizedDescription ?? "未知错误")")
                        lastError = error
                }
                
                group.leave()
            }
        }
        
            // 所有上传完成后，返回结果
        group.notify(queue: .main) {
                if totalUploaded > 0 {
                    print("成功上传了\(totalUploaded)条HRV数据")
                    
                    // 发送通知
                    NotificationCenter.default.post(
                        name: Notification.Name.hrvDataUploaded,
                        object: nil,
                        userInfo: ["count": totalUploaded, "success": true]
                    )
                    
                    completion(totalUploaded, nil)
                } else {
                    print("没有成功上传任何HRV数据")
            
            // 发送通知
            NotificationCenter.default.post(
                        name: Notification.Name.hrvDataUploaded,
                object: nil,
                        userInfo: ["count": 0, "success": false, "error": lastError as Any]
                    )
                    
                    completion(0, lastError)
                }
            }
        }
    }
    
    /// 获取待上传的HRV数据
    /// - Parameter completion: 完成回调，返回按日期分组的HRV记录
    private func fetchPendingHRVData(completion: @escaping ([String: [WindRing.HRVRecord]]) -> Void) {
        // 这里实现从CoreData获取未上传HRV数据的逻辑
        // 示例实现，实际应根据项目的数据库结构调整
        let context = self.storageManager.viewContext()
        
        let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: "HRVEntity")
        fetchRequest.predicate = NSPredicate(format: "isUploaded == %@", NSNumber(value: false))
        
        do {
            guard let results = try context.fetch(fetchRequest) as? [NSManagedObject] else {
                DispatchQueue.main.async {
                    completion([:])
                }
                return
            }
            
            // 按日期分组数据
            var groupedData: [String: [WindRing.HRVRecord]] = [:]
            
            for result in results {
                guard let dateString = result.value(forKey: "dateString") as? String,
                      let hrvValue = result.value(forKey: "hrvValue") as? Int,
                      let timeString = result.value(forKey: "timeISOString") as? String else {
                    continue
                }
                
                let record = WindRing.HRVRecord(hrv: hrvValue, time: timeString)
                
                if groupedData[dateString] == nil {
                    groupedData[dateString] = []
                }
                
                groupedData[dateString]?.append(record)
            }
            
            DispatchQueue.main.async {
                completion(groupedData)
            }
        } catch {
            print("获取待上传HRV数据失败: \(error.localizedDescription)")
            DispatchQueue.main.async {
                completion([:])
            }
        }
    }
    
    /// 标记HRV数据为已上传
    /// - Parameters:
    ///   - date: 日期字符串
    ///   - records: 已上传的记录
    private func markHRVDataAsUploaded(date: String, records: [WindRing.HRVRecord]) {
        // 这里实现更新CoreData中HRV数据上传状态的逻辑
        // 示例实现，实际应根据项目的数据库结构调整
        storageManager.performBackgroundTask { context in
            let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: "HRVEntity")
            fetchRequest.predicate = NSPredicate(format: "dateString == %@ AND isUploaded == %@", date, NSNumber(value: false))
            
            do {
                guard let results = try context.fetch(fetchRequest) as? [NSManagedObject] else {
                    return
                }
                
                // 收集所有的时间字符串作为查找依据
                let timeStrings = Set(records.map { $0.time })
                
                for result in results {
                    if let timeString = result.value(forKey: "timeISOString") as? String,
                       timeStrings.contains(timeString) {
                        result.setValue(true, forKey: "isUploaded")
                    }
                }
                
                // 上下文在performBackgroundTask中自动保存
            } catch {
                print("标记HRV数据为已上传失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 私有方法
    
    /// 初始化通知订阅
    private func setupNotifications() {
        // 设备连接成功后，自动获取历史数据
        NotificationCenter.default.publisher(for: .deviceSyncCompleted)
            .filter { notification in
                notification.userInfo?["success"] as? Bool == true
            }
            .debounce(for: .seconds(2), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                guard let self = self else { return }
                print("设备同步完成，准备获取HRV数据")
                // 自动同步最近7天的HRV数据
                self.syncAndUploadHRVData(days: 7) { success, error in
                    if success {
                        print("自动同步并上传HRV数据成功")
                    } else if let error = error {
                        print("自动同步并上传HRV数据失败: \(error.localizedDescription)")
                    }
                }
            }
            .store(in: &cancellables)
        
        // 网络状态变化时，尝试上传
        NotificationCenter.default.publisher(for: .networkStatusChanged)
            .filter { notification in
                notification.userInfo?["connected"] as? Bool == true
            }
            .sink { [weak self] _ in
                guard let self = self else { return }
                print("网络已连接，尝试上传HRV数据")
                self.syncAndUploadHRVData() { success, error in 
                    if success {
                        print("网络连接后自动同步并上传HRV数据成功")
                    } else if let error = error {
                        print("网络连接后自动同步并上传HRV数据失败: \(error.localizedDescription)")
                    }
                }
            }
            .store(in: &cancellables)
    }
} 
