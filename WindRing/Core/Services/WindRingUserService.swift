import Foundation
import Combine

/// WindRing用户服务类 - 管理用户登录状态和基本信息
public class WindRingUserService: ObservableObject {
    // MARK: - 单例
    public static let shared = WindRingUserService()
    
    // MARK: - 属性
    
    // 使用存储属性和发布器
    @Published public private(set) var isLogin: Bool = false
    @Published public private(set) var userName: String = "游客"
    
    // 发布器对象
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // 从UserDefaults加载初始值
        self.isLogin = UserDefaults.standard.bool(forKey: "isLoggedIn")
        self.userName = UserDefaults.standard.string(forKey: "userName") ?? "游客"
        
        // 监听变化并存储到UserDefaults
        setupObservers()
    }
    
    private func setupObservers() {
        // 监听isLogin变化
        $isLogin
            .dropFirst() // 忽略初始值
            .sink { [weak self] newValue in
                UserDefaults.standard.set(newValue, forKey: "isLoggedIn")
                if !newValue {
                    // 登出时重置用户名
                    self?.userName = "游客"
                }
            }
            .store(in: &cancellables)
        
        // 监听userName变化
        $userName
            .dropFirst() // 忽略初始值
            .sink { newValue in
                UserDefaults.standard.set(newValue, forKey: "userName")
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 方法
    
    /// 登出用户
    public func logout() {
        isLogin = false
        userName = "游客"
        
        // 清除其他用户相关数据
        UserDefaults.standard.removeObject(forKey: "userToken")
        
        // 发出登出通知
        NotificationCenter.default.post(name: NSNotification.Name("userDidLogout"), object: nil)
        
        print("[用户服务] 用户已登出")
    }
    
    /// 设置用户登录状态
    /// - Parameters:
    ///   - isLogin: 是否登录
    ///   - userName: 用户名
    public func setLoginStatus(isLogin: Bool, userName: String?) {
        self.isLogin = isLogin
        
        if let name = userName, !name.isEmpty {
            self.userName = name
        }
        
        if isLogin {
            print("[用户服务] 用户已登录: \(self.userName)")
        }
    }
} 