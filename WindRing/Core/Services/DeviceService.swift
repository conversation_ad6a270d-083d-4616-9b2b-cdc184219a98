import Foundation
import Combine

class DeviceService {
    private let windRingDeviceService: WindRingDeviceService
    
    init(windRingDeviceService: WindRingDeviceService = WindRingDeviceService.shared) {
        self.windRingDeviceService = windRingDeviceService
    }
    
    /// 获取睡眠数据
    /// - Parameters:
    ///   - day: 天数，0表示今天，1表示昨天，以此类推
    ///   - completion: 完成回调
    func getSleepData(day: Int, completion: @escaping (Any?, Error?) -> Void) {
        // 确保设备已连接
        guard isDeviceConnected() else {
            let error = NSError(domain: "com.windring.device", code: 1001, userInfo: [NSLocalizedDescriptionKey: "设备未连接"])
            completion(nil, error)
            return
        }
        
        // 安全包装SDK调用
        do {
            // 这里调用实际的SDK获取睡眠数据
            // 根据实际情况修改此调用
            windRingDeviceService.getSleepData(day: day) { data, error in
                if let error = error {
                    completion(nil, error)
                    return
                }
                
                // 验证数据有效性
                guard let data = data, self.isValidSleepData(data) else {
                    let validationError = NSError(domain: "com.windring.device", code: 1002, 
                                              userInfo: [NSLocalizedDescriptionKey: "无效的睡眠数据"])
                    completion(nil, validationError)
                    return
                }
                
                completion(data, nil)
            }
        } catch {
            // 捕获任何可能的崩溃或异常
            print("getSleepData方法发生异常: \(error)")
            completion(nil, error)
        }
    }
    
    /// 检查设备是否已连接
    /// - Returns: 设备连接状态
    func isDeviceConnected() -> Bool {
        return windRingDeviceService.connectionState == .connected
    }
    
    /// 验证睡眠数据是否有效
    /// - Parameter data: 睡眠数据
    /// - Returns: 是否有效
    private func isValidSleepData(_ data: Any) -> Bool {
        // 根据实际数据结构实现验证逻辑
        // 例如检查是否为空、关键字段是否存在等
        return true
    }
} 