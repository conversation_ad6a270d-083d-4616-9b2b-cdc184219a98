import Foundation
import Combine
// import Models.HealthReportModels

// Use types from HealthReportModels
// Use the public typealias from HealthReportModels.swift

/// 健康报告存储服务
/// 负责健康报告的持久化存储、检索和管理
public class HealthReportStorageService {
    // MARK: - 单例
    public static let shared = HealthReportStorageService()
    
    // MARK: - 属性
    private let healthReportService: HealthReportService
    private let userDefaults = UserDefaults.standard
    private let fileManager = FileManager.default
    private let encoder = JSONEncoder()
    private let decoder = CleanJSONDecoder()
    
    /// 报告存储状态变更发布者
    public let storageUpdatePublisher = PassthroughSubject<StorageUpdateEvent, Never>()
    
    /// 最大本地缓存报告数量
    private let maxCachedReports = 100
    
    /// 报告存储目录名
    private let reportDirectoryName = "HealthReports"
    
    /// 用户报告键前缀
    private let userReportsKeyPrefix = "user_reports_"
    
    /// 存储键
    private let reportsKey = "healthReports"
    private let favoritesKey = "favoriteReports"
    
    /// 订阅存储
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化方法
    private init() {
        self.healthReportService = HealthReportService.shared
        
        // 订阅报告生成事件
        subscribeToReportGeneration()
        
        // 创建报告存储目录
        createReportDirectoryIfNeeded()
        
        encoder.dateEncodingStrategy = .iso8601
        decoder.dateDecodingStrategy = .iso8601
    }
    
    // MARK: - 报告存储方法
    
    /// 保存健康报告
    /// - Parameter report: 健康报告
    /// - Returns: 是否保存成功
    @discardableResult
    public func saveReport(_ report: HRMReport) -> Bool {
        var reports = getAllReports()
        
        // 检查是否已存在该报告
        if let index = reports.firstIndex(where: { $0.id == report.id }) {
            // 更新现有报告
            reports[index] = report
        } else {
            // 添加新报告
            reports.append(report)
        }
        
        return saveReports(reports)
    }
    
    /// 获取所有健康报告
    /// - Returns: 健康报告数组
    public func getAllReports() -> [HRMReport] {
        guard let data = userDefaults.data(forKey: reportsKey) else {
            return []
        }
        
        do {
            return try decoder.decode([HRMReport].self, from: data)
        } catch {
            print("解码健康报告失败: \(error)")
            return []
        }
    }
    
    /// 批量保存健康报告
    /// - Parameter reports: 健康报告数组
    /// - Returns: 是否保存成功
    private func saveReports(_ reports: [HRMReport]) -> Bool {
        do {
            let data = try encoder.encode(reports)
            userDefaults.set(data, forKey: reportsKey)
            
            // 更新报告元数据缓存
            for report in reports {
                updateReportMetadataCache(for: report)
            }
            
            // 发布存储更新事件
            if let lastReport = reports.last {
                // 尝试将字符串 ID 转换为 UUID 
                if let reportId = UUID(uuidString: lastReport.id) {
                    storageUpdatePublisher.send(.reportSaved(reportId))
                }
            }
            
            return true
        } catch {
            print("编码健康报告失败: \(error)")
            return false
        }
    }
    
    /// 获取指定ID的健康报告
    /// - Parameter id: 报告ID
    /// - Returns: 健康报告（如果存在）
    public func getReport(id: String) -> HRMReport? {
        return getAllReports().first { $0.id == id }
    }
    
    /// 获取特定类型的健康报告
    /// - Parameter type: 报告类型
    /// - Returns: 符合类型的健康报告数组
    public func getReports(type: HRMReportType) -> [HRMReport] {
        return getAllReports().filter { $0.type == type }
    }
    
    /// 获取特定用户的健康报告
    /// - Parameter userId: 用户ID
    /// - Returns: 符合类型的健康报告数组
    public func getReports(userId: String) -> [HRMReport] {
        return getAllReports().filter { $0.userId == userId }
    }
    
    /// 获取报告
    /// - Parameter metadata: 报告元数据
    /// - Returns: 健康报告
    public func getReport(metadata: HRMReportMetadata) -> HRMReport? {
        // 1. 获取报告文件路径
        guard let reportDirectoryURL = getReportDirectoryURL() else {
            print("Error: Failed to get report directory")
            return nil
        }
        
        let reportFileName = "\(metadata.id).json"
        let reportFileURL = reportDirectoryURL.appendingPathComponent(reportFileName)
        
        // 2. 读取报告文件
        guard let reportData = try? Data(contentsOf: reportFileURL) else {
            print("Error: Failed to read health report data")
            return nil
        }
        
        // 3. 解码报告
        guard let report = try? CleanJSONDecoder().decode(HRMReport.self, from: reportData) else {
            print("Error: Failed to decode health report")
            return nil
        }
        
        return report
    }
    
    /// 获取报告元数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - type: 报告类型（可选）
    ///   - startDate: 开始日期（可选）
    ///   - endDate: 结束日期（可选）
    ///   - minHealthScore: 最低健康评分（可选）
    ///   - maxHealthScore: 最高健康评分（可选）
    ///   - page: 页码（从1开始）
    ///   - pageSize: 每页数量
    /// - Returns: 报告元数据列表
    public func getReportMetadata(
        userId: String,
        type: HRMReportType? = nil,
        startDate: Date? = nil,
        endDate: Date? = nil,
        minHealthScore: Int? = nil,
        maxHealthScore: Int? = nil,
        page: Int = 1,
        pageSize: Int = 20
    ) -> [HRMReportMetadata] {
        // 1. 获取用户所有报告元数据
        var allMetadata = getUserReportMetadata(forUserId: userId)
        
        // 2. 应用筛选条件
        if let type = type, type != .all {
            allMetadata = allMetadata.filter { $0.type == type }
        }
        
        if let startDate = startDate {
            allMetadata = allMetadata.filter { $0.createdAt >= startDate }
        }
        
        if let endDate = endDate {
            allMetadata = allMetadata.filter { $0.createdAt <= endDate }
        }
        
        if let minHealthScore = minHealthScore {
            allMetadata = allMetadata.filter { $0.healthScore ?? 0 >= minHealthScore }
        }
        
        if let maxHealthScore = maxHealthScore {
            allMetadata = allMetadata.filter { $0.healthScore ?? 0 <= maxHealthScore }
        }
        
        // 3. 按创建时间排序（降序）
        allMetadata.sort { $0.createdAt > $1.createdAt }
        
        // 4. 分页
        let startIndex = (page - 1) * pageSize
        if startIndex >= allMetadata.count {
            return []
        }
        
        let endIndex = min(startIndex + pageSize, allMetadata.count)
        return Array(allMetadata[startIndex..<endIndex])
    }
    
    /// 获取最新报告
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - type: 报告类型
    /// - Returns: 最新的健康报告
    public func getLatestReport(userId: String, type: HRMReportType) -> HRMReport? {
        // 1. 获取满足条件的报告元数据
        let metadata = getReportMetadata(
            userId: userId,
            type: type,
            page: 1,
            pageSize: 1
        )
        
        // 2. 如果有报告，获取完整报告
        if let reportMetadata = metadata.first {
            return getReport(metadata: reportMetadata)
        }
        
        return nil
    }
    
    /// 删除报告
    /// - Parameter metadata: 报告元数据
    /// - Returns: 是否删除成功
    @discardableResult
    public func deleteReport(metadata: HRMReportMetadata) -> Bool {
        // 1. 获取报告文件路径
        guard let reportDirectoryURL = getReportDirectoryURL() else {
            print("Error: Failed to get report directory")
            return false
        }
        
        let reportFileName = "\(metadata.id).json"
        let reportFileURL = reportDirectoryURL.appendingPathComponent(reportFileName)
        
        // 2. 删除报告文件
        do {
            if fileManager.fileExists(atPath: reportFileURL.path) {
                try fileManager.removeItem(at: reportFileURL)
            }
        } catch {
            print("Error: Failed to delete health report file - \(error.localizedDescription)")
            return false
        }
        
        // 3. 从元数据缓存中删除
        removeReportMetadataFromCache(metadata: metadata)
        
        // 4. 发布存储更新事件
        if let reportId = UUID(uuidString: metadata.id) {
            storageUpdatePublisher.send(.reportDeleted(reportId))
        }
        
        return true
    }
    
    // MARK: - 私有方法
    
    /// 获取报告存储目录URL
    private func getReportDirectoryURL() -> URL? {
        guard let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }
        
        return documentsDirectory.appendingPathComponent(reportDirectoryName, isDirectory: true)
    }
    
    /// 创建报告存储目录
    private func createReportDirectoryIfNeeded() {
        guard let reportDirectoryURL = getReportDirectoryURL() else {
            print("Error: Failed to get report directory URL")
            return
        }
        
        if !fileManager.fileExists(atPath: reportDirectoryURL.path) {
            do {
                try fileManager.createDirectory(
                    at: reportDirectoryURL,
                    withIntermediateDirectories: true,
                    attributes: nil
                )
            } catch {
                print("Error: Failed to create report directory - \(error.localizedDescription)")
            }
        }
    }
    
    /// 订阅报告生成事件
    private func subscribeToReportGeneration() {
        // 如果将来有报告生成服务发布事件，可以在这里订阅
    }
    
    /// 获取用户报告元数据
    private func getUserReportMetadata(forUserId userId: String) -> [HRMReportMetadata] {
        let key = "\(userReportsKeyPrefix)\(userId)"
        
        if let data = userDefaults.data(forKey: key),
           let metadata = try? CleanJSONDecoder().decode([HRMReportMetadata].self, from: data) {
            return metadata
        }
        
        return []
    }
    
    /// 更新报告元数据缓存
    private func updateReportMetadataCache(for report: HRMReport) {
        let userId = report.userId
        var metadata = getUserReportMetadata(forUserId: userId)
        
        // 创建报告元数据
        let reportMetadata = HRMReportMetadata(
            id: report.id,
            userId: report.userId,
            title: report.title,
            type: report.type,
            createdAt: report.createdAt,
            startDate: report.startDate,
            endDate: report.endDate,
            summary: report.summary,
            healthScore: report.healthScore,
            isFavorite: report.isFavorite
        )
        
        // 移除已存在的相同ID的元数据
        metadata.removeAll(where: { $0.id == reportMetadata.id })
        
        // 添加新元数据
        metadata.append(reportMetadata)
        
        // 排序（最新的排在前面）
        metadata.sort { $0.createdAt > $1.createdAt }
        
        // 保存元数据
        saveUserReportMetadata(metadata, forUserId: userId)
    }
    
    /// 保存用户报告元数据
    private func saveUserReportMetadata(_ metadata: [HRMReportMetadata], forUserId userId: String) {
        let key = "\(userReportsKeyPrefix)\(userId)"
        
        if let data = try? JSONEncoder().encode(metadata) {
            userDefaults.set(data, forKey: key)
        }
    }
    
    /// 从缓存中删除报告元数据
    private func removeReportMetadataFromCache(metadata: HRMReportMetadata) {
        let userId = metadata.userId
        var userMetadata = getUserReportMetadata(forUserId: userId)
        
        // 移除指定ID的元数据
        userMetadata.removeAll(where: { $0.id == metadata.id })
        
        // 保存更新后的元数据
        saveUserReportMetadata(userMetadata, forUserId: userId)
    }
    
    /// 清理旧报告
    private func cleanupOldReports(forUserId userId: String) {
        var metadata = getUserReportMetadata(forUserId: userId)
        
        // 如果报告数量超过最大缓存数量，删除最旧的报告
        if metadata.count > maxCachedReports {
            // 按创建时间排序（升序，最旧的在前面）
            metadata.sort { $0.createdAt < $1.createdAt }
            
            // 计算需要删除的报告数量
            let reportsToDelete = metadata.count - maxCachedReports
            
            // 删除最旧的报告
            for i in 0..<reportsToDelete {
                if i < metadata.count {
                    let oldMetadata = metadata[i]
                    deleteReport(metadata: oldMetadata)
                }
            }
        }
    }
}

// MARK: - 存储更新事件
extension HealthReportStorageService {
    public enum StorageUpdateEvent {
        case reportSaved(UUID)
        case reportDeleted(UUID)
        case syncCompleted
    }
}

// MARK: - 辅助扩展
extension Date {
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: self)
    }
} 
