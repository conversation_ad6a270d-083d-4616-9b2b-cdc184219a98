import Foundation
import Combine
import CoreData
#if os(iOS)
import CRPSmartRing
#endif

/// 温度数据上传服务
/// 负责管理和上传设备收集的温度数据
public class TemperatureUploadService {
    // MARK: - 单例
    public static let shared = TemperatureUploadService()
    
    // MARK: - 依赖
    private let healthDataManager = HealthDataManager.shared
    private let authService = AuthService.shared
    private let deviceService = WindRingDeviceService.shared
    
    // MARK: - 状态
    @Published public private(set) var isUploading = false
    
    // MARK: - 属性
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    private init() {
        // 注册通知监听
        setupNotificationObservers()
    }
    
    // MARK: - 通知监听设置
    private func setupNotificationObservers() {
        // 监听设备连接状态变化
        NotificationCenter.default.publisher(for: NSNotification.Name("DeviceConnectionStateChanged"))
            .receive(on: RunLoop.main)
            .sink { [weak self] _ in
                // 当设备连接状态改变时，可以在这里执行一些操作
            }
            .store(in: &cancellables)
        
        // 监听用户登录状态变化
        NotificationCenter.default.publisher(for: Notification.Name("userDidLogin"))
            .receive(on: RunLoop.main)
            .sink { [weak self] _ in
                // 当用户登录后，可以在这里执行一些操作
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 从设备获取最近的温度数据并保存到本地
    /// - Parameters:
    ///   - days: 获取多少天的数据（默认为3天）
    ///   - completion: 完成回调，返回获取的数据条数和可能的错误
    public func fetchAndSaveTemperatureData(days: Int = 3, completion: @escaping (Int, Error?) -> Void) {
        // 确保设备已连接
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "TemperatureService", code: 400, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        // 获取用户ID
        guard let userId = authService.currentUser?.id else {
            completion(0, NSError(domain: "TemperatureService", code: 401, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        // 获取设备ID
        guard let deviceId = deviceService.deviceInfo?.mac else {
            print("无法获取设备MAC地址，尝试使用默认值")
            let defaultDeviceId = "default_device_id"
            // 继续执行，使用默认设备ID
            fetchAndSaveTemperatureDataInternal(days: days, deviceId: defaultDeviceId, userId: userId, completion: completion)
            return
        }
        
        fetchAndSaveTemperatureDataInternal(days: days, deviceId: deviceId, userId: userId, completion: completion)
    }
    
    /// 内部实现从设备获取温度数据并保存的功能
    private func fetchAndSaveTemperatureDataInternal(days: Int, deviceId: String, userId: String, completion: @escaping (Int, Error?) -> Void) {
        #if os(iOS)
        // 定义计数变量
        var totalSavedCount = 0
        let dispatchGroup = DispatchGroup()
        var lastError: Error? = nil
        
        // 获取睡眠温度监测状态
        dispatchGroup.enter()
//        CRPSmartRingSDK.sharedInstance.getSleepTemperatureState { isEnabled, error in
//            if error != .none {
//                lastError = NSError(domain: "TemperatureService", code: 403, userInfo: [NSLocalizedDescriptionKey: "获取睡眠体温监测状态失败: \(error)"]) 
//                dispatchGroup.leave()
//                return
//            }
//            
//            if !isEnabled {
//                print("警告：睡眠体温监测未启用，可能无法获取完整数据")
//            }
//            
//            dispatchGroup.leave()
//        }
        
        // 从设备获取最近几天的体温数据
        for day in 0..<days {
            dispatchGroup.enter()
            
//            CRPSmartRingSDK.sharedInstance.getSleepTemperatureData(day: day) { model, error in
//                defer { dispatchGroup.leave() }
//                
//                if error != .none {
//                    print("获取第\(day)天的睡眠体温数据失败: \(error)")
//                    return
//                }
//                
//                let validTemperatures = model.tempeartures.filter { $0 > 0 }
//                if validTemperatures.isEmpty {
//                    print("第\(day)天没有有效的体温数据")
//                    return
//                }
//                
//                print("成功获取第\(day)天的睡眠体温数据，共\(validTemperatures.count)个数据点")
//                
//                // 计算当天的日期
//                let calendar = Calendar.current
//                let now = Date()
//                guard let date = calendar.date(byAdding: .day, value: -day, to: now) else { return }
//                
//                // 将日期设置为午夜
//                var dateComponents = calendar.dateComponents([.year, .month, .day], from: date)
//                dateComponents.hour = 0
//                dateComponents.minute = 0
//                dateComponents.second = 0
//                
//                guard let startOfDay = calendar.date(from: dateComponents) else { return }
//                
//                // 每5分钟一个数据点，共288个点
//                var savedCount = 0
//                for (index, temperature) in model.tempeartures.enumerated() {
//                    if temperature <= 0 { continue } // 跳过无效值
//                    
//                    // 计算时间戳：当天午夜 + index*5分钟
//                    let timestamp = calendar.date(byAdding: .minute, value: index * 5, to: startOfDay) ?? Date()
//                    
//                    // 保存到数据库
//                    if self.healthDataManager.addTemperatureRecord(
//                        userId: userId,
//                        value: temperature,
//                        timestamp: timestamp,
//                        deviceId: deviceId,
//                        type: "sleep"
//                    ) {
//                        savedCount += 1
//                    }
//                }
//                
//                totalSavedCount += savedCount
//                print("已保存第\(day)天的\(savedCount)条体温数据")
//            }
        }
        
        // 等待所有请求完成
        dispatchGroup.notify(queue: .main) {
            completion(totalSavedCount, lastError)
            
            // 如果成功获取到数据，发送通知
            if totalSavedCount > 0 {
                NotificationCenter.default.post(
                    name: .temperatureDataFetched,
                    object: nil,
                    userInfo: ["count": totalSavedCount]
                )
            }
        }
        #else
        // 在非iOS平台上，只返回一个错误
        DispatchQueue.main.async {
            completion(0, NSError(domain: "TemperatureService", code: 500, userInfo: [NSLocalizedDescriptionKey: "非iOS平台不支持获取温度数据"]))
        }
        #endif
    }
    
    /// 上传待上传的温度数据到服务器
    /// - Parameter completion: 完成回调，返回上传的数据条数和可能的错误
    public func uploadPendingTemperatureData(completion: @escaping (Int, Error?) -> Void) {
        // 确保用户已登录
        guard let userId = authService.currentUser?.id,
              let token = authService.currentToken?.accessToken else {
            completion(0, NSError(domain: "TemperatureService", code: 401, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        // 设置上传状态
        isUploading = true
        
        // 获取过去90天的温度数据
        let calendar = Calendar.current
        let endDate = Date()
        guard let startDate = calendar.date(byAdding: .day, value: -90, to: endDate) else {
            isUploading = false
            completion(0, NSError(domain: "TemperatureService", code: 500, userInfo: [NSLocalizedDescriptionKey: "日期计算错误"]))
            return
        }
        
        // 获取需要上传的温度数据
        let temperatureData = healthDataManager.getTemperatures(userId: userId, startDate: startDate, endDate: endDate)
        if temperatureData.isEmpty {
            isUploading = false
            completion(0, nil)
            return
        }
        
        print("找到\(temperatureData.count)条待上传的温度数据")
        
        // 按日期分组温度数据
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        dateFormatter.timeZone = TimeZone.current
        
        // 按日期分组温度数据
        let groupedData = Dictionary(grouping: temperatureData) { temp -> String in
            return dateFormatter.string(from: temp.timestamp ?? Date())
        }
        
        // 创建上传任务组
        let uploadGroup = DispatchGroup()
        var successCount = 0
        var lastError: Error? = nil
        
        // 对每一天的数据进行处理
        for (dateString, records) in groupedData {
            // 对于每一天，准备上传数据
            let uploadData = self.prepareTemperatureUploadData(dateString: dateString, records: records)
            
            // 上传数据
            uploadGroup.enter()
            uploadTemperatureData(data: uploadData) { success, error in
                if success {
                    successCount += 1
                    
                    // 标记数据为已上传
                    for record in records {
                        // 注意：如果TemperatureEntity没有isUploaded属性，需要添加该属性
                        // 或者使用其他方式记录上传状态
                    }
                } else if let error = error {
                    lastError = error
                }
                
                uploadGroup.leave()
            }
        }
        
        // 等待所有上传任务完成
        uploadGroup.notify(queue: .main) {
            self.isUploading = false
            
            // 发送通知
            NotificationCenter.default.post(
                name: .temperatureDataUploaded,
                object: nil,
                userInfo: [
                    "count": successCount,
                    "success": lastError == nil
                ]
            )
            
            completion(successCount, lastError)
        }
    }
    
    /// 准备温度上传数据
    /// - Parameters:
    ///   - dateString: 日期字符串，格式为"yyyy-MM-dd"
    ///   - records: 该日期的温度记录
    /// - Returns: 准备好的上传数据
    private func prepareTemperatureUploadData(dateString: String, records: [TemperatureEntity]) -> TemperatureUploadData {
        var averageTemperature: Double = 0
        var maxTemperature: Double = 0
        var minTemperature: Double = 100 // 初始设置一个很高的值，确保会被更新
        
        // 计算平均值、最大值、最小值
        if !records.isEmpty {
            let totalValue = records.reduce(0.0) { $0 + $1.value }
            averageTemperature = totalValue / Double(records.count)
            
            maxTemperature = records.map { $0.value }.max() ?? 0
            minTemperature = records.map { $0.value }.min() ?? 0
        }
        
        // 准备温度记录数组
        var temperatureRecords: [TemperatureRecord] = []
        for record in records {
            guard let timestamp = record.timestamp else { continue }
            
            let milliseconds = Int64(timestamp.timeIntervalSince1970 * 1000)
            let temperatureRecord = TemperatureRecord(
                temperature: record.value,
                timestamp: milliseconds
            )
            temperatureRecords.append(temperatureRecord)
        }
        
        // 创建上传数据模型
        return TemperatureUploadData(
            date: dateString,
            averageTemperature: averageTemperature,
            maxTemperature: maxTemperature,
            minTemperature: minTemperature,
            records: temperatureRecords
        )
    }
    
    /// 上传温度数据到服务器
    /// - Parameters:
    ///   - data: 温度上传数据
    ///   - completion: 完成回调，返回是否成功和可能的错误
    private func uploadTemperatureData(data: TemperatureUploadData, completion: @escaping (Bool, Error?) -> Void) {
        // 确保用户已登录
        guard let token = authService.currentToken?.accessToken else {
            completion(false, NSError(domain: "TemperatureService", code: 401, userInfo: [NSLocalizedDescriptionKey: "缺少认证Token"]))
            return
        }
        
        // 准备URL
        guard let url = URL(string: "http://ring-api-dev.weaving-park.com/app-api/iot/sleep/upload/temperature/data") else {
            completion(false, NSError(domain: "TemperatureService", code: 400, userInfo: [NSLocalizedDescriptionKey: "无效的URL"]))
            return
        }
        
        // 准备请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        
        // 添加租户ID
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 将数据转换为JSON
        do {
            let jsonData = try JSONEncoder().encode(data)
            request.httpBody = jsonData
            
            // 打印上传的JSON数据，便于调试
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("上传的温度数据JSON: \(jsonString)")
            }
        } catch {
            completion(false, error)
            return
        }
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理响应
            if let error = error {
                DispatchQueue.main.async {
                    completion(false, error)
                }
                return
            }
            
            // 检查HTTP响应状态码
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(false, NSError(domain: "TemperatureService", code: 500, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
                }
                return
            }
            
            // 打印响应状态码
            print("温度数据上传响应状态码: \(httpResponse.statusCode)")
            
            // 打印响应数据
            if let data = data, let responseString = String(data: data, encoding: .utf8) {
                print("温度数据上传响应: \(responseString)")
                
                // 发送通知，包含响应数据
                NotificationCenter.default.post(
                    name: .temperatureDataUploadResponse,
                    object: nil,
                    userInfo: ["response": data]
                )
            }
            
            // 根据状态码判断是否成功
            let success = (200...299).contains(httpResponse.statusCode)
            
            DispatchQueue.main.async {
                if success {
                    completion(true, nil)
                } else {
                    let message = "服务器返回错误状态码: \(httpResponse.statusCode)"
                    completion(false, NSError(domain: "TemperatureService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: message]))
                }
            }
        }
        
        task.resume()
    }
    
    /// 使用Postman成功的设置上传温度数据
    /// - Parameters:
    ///   - date: 可选的日期字符串，如果为nil则使用当前日期
    ///   - completion: 完成回调，返回是否成功和可能的错误
    public func uploadTemperatureWithPostmanSettings(date: String? = nil, completion: @escaping (Bool, Error?) -> Void) {
        // 准备日期字符串
        let dateString = date ?? {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            return formatter.string(from: Date())
        }()
        
        // 准备URL
        guard let url = URL(string: "http://ring-api-dev.weaving-park.com/app-api/iot/sleep/upload/temperature/data") else {
            completion(false, NSError(domain: "TemperatureService", code: 400, userInfo: [NSLocalizedDescriptionKey: "无效的URL"]))
            return
        }
        
        // 准备请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        // 使用Postman中成功的认证Token
        request.addValue("Bearer 833cfe18dbcf4c39b3b38cc0d6716299", forHTTPHeaderField: "Authorization")
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 准备请求体
        let temperatureRecords: [[String: Any]] = [
            [
                "temperature": 36.5,
                "timestamp": 1716541200000 // 示例时间戳
            ],
            [
                "temperature": 36.8,
                "timestamp": 1716544800000 // 示例时间戳
            ],
            [
                "temperature": 37.1,
                "timestamp": 1716548400000 // 示例时间戳
            ],
            [
                "temperature": 36.2,
                "timestamp": 1716552000000 // 示例时间戳
            ]
        ]
        
        let requestBody: [String: Any] = [
            "date": dateString,
            "averageTemperature": 36.5,
            "maxTemperature": 37.1,
            "minTemperature": 36.2,
            "records": temperatureRecords
        ]
        
        // 将字典转换为JSON数据
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody, options: [])
            request.httpBody = jsonData
            
            // 打印上传的JSON数据，便于调试
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("使用Postman设置上传的温度数据JSON: \(jsonString)")
            }
        } catch {
            completion(false, error)
            return
        }
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理响应
            if let error = error {
                DispatchQueue.main.async {
                    completion(false, error)
                }
                return
            }
            
            // 检查HTTP响应状态码
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(false, NSError(domain: "TemperatureService", code: 500, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
                }
                return
            }
            
            // 打印响应状态码
            print("温度数据上传响应状态码: \(httpResponse.statusCode)")
            
            // 打印响应数据
            if let data = data, let responseString = String(data: data, encoding: .utf8) {
                print("温度数据上传响应: \(responseString)")
                
                // 发送通知，包含响应数据
                NotificationCenter.default.post(
                    name: .temperatureDataUploadResponse,
                    object: nil,
                    userInfo: ["response": data]
                )
            }
            
            // 根据状态码判断是否成功
            let success = (200...299).contains(httpResponse.statusCode)
            
            DispatchQueue.main.async {
                if success {
                    completion(true, nil)
                } else {
                    let message = "服务器返回错误状态码: \(httpResponse.statusCode)"
                    completion(false, NSError(domain: "TemperatureService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: message]))
                }
            }
        }
        
        task.resume()
    }
    
    /// 测试直接使用Postman的完整请求上传数据
    /// - Parameter completion: 完成回调，返回是否成功和可能的错误
    public func testDirectUploadWithPostman(completion: @escaping (Bool, Error?) -> Void) {
        print("开始测试直接使用Postman的完整请求上传温度数据")
        
        // 准备URL
        guard let url = URL(string: "http://ring-api-dev.weaving-park.com/app-api/iot/sleep/upload/temperature/data") else {
            completion(false, NSError(domain: "TemperatureService", code: 400, userInfo: [NSLocalizedDescriptionKey: "无效的URL"]))
            return
        }
        
        // 准备请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        // 完全使用Postman中的头部设置
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer 833cfe18dbcf4c39b3b38cc0d6716299", forHTTPHeaderField: "Authorization")
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 使用Postman中的完整请求体
        let jsonString = """
        {
            "date": "2025-03-24",
            "averageTemperature": 36.5,
            "maxTemperature": 37.1,
            "minTemperature": 36.2,
            "records": [
                {
                    "temperature": 36.5,
                    "timestamp": 1716541200000
                },
                {
                    "temperature": 36.8,
                    "timestamp": 1716544800000
                },
                {
                    "temperature": 37.1,
                    "timestamp": 1716548400000
                },
                {
                    "temperature": 36.2,
                    "timestamp": 1716552000000
                }
            ]
        }
        """
        
        request.httpBody = jsonString.data(using: .utf8)
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理响应
            if let error = error {
                DispatchQueue.main.async {
                    completion(false, error)
                }
                return
            }
            
            // 检查HTTP响应状态码
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(false, NSError(domain: "TemperatureService", code: 500, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
                }
                return
            }
            
            // 打印响应状态码和头部
            print("温度数据上传响应状态码: \(httpResponse.statusCode)")
            print("响应头部: \(httpResponse.allHeaderFields)")
            
            // 打印响应数据
            if let data = data, let responseString = String(data: data, encoding: .utf8) {
                print("温度数据上传响应: \(responseString)")
                
                // 发送通知，包含响应数据
                NotificationCenter.default.post(
                    name: .temperatureDataUploadResponse,
                    object: nil,
                    userInfo: ["response": data]
                )
            }
            
            // 根据状态码判断是否成功
            let success = (200...299).contains(httpResponse.statusCode)
            
            DispatchQueue.main.async {
                if success {
                    completion(true, nil)
                } else {
                    let message = "服务器返回错误状态码: \(httpResponse.statusCode)"
                    completion(false, NSError(domain: "TemperatureService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: message]))
                }
            }
        }
        
        task.resume()
    }
    
    /// 直接从设备获取指定日期的体温数据并上传
    /// - Parameters:
    ///   - day: 日期索引，0代表今天，1代表昨天，以此类推
    ///   - completion: 完成回调，返回是否成功和可能的错误
    public func uploadSleepTemperatureDataFromDevice(day: Int, completion: @escaping (Bool, Error?) -> Void) {
        // 确保设备已连接
        guard deviceService.connectionState.isConnected else {
            completion(false, NSError(domain: "TemperatureService", code: 400, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        // 获取用户认证信息
        guard let token = authService.currentToken?.accessToken else {
            completion(false, NSError(domain: "TemperatureService", code: 401, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        // 获取设备ID（不再作为必须条件，如果获取不到则使用默认值）
        let deviceId = deviceService.deviceInfo?.mac ?? "default_device_id"
        
        // 从设备获取指定日期的体温数据
//        CRPSmartRingSDK.sharedInstance.getSleepTemperatureData(day: day) { [weak self] model, error in
//            guard let self = self else { return }
//            
//            if error != .none {
//                let nsError = NSError(domain: "TemperatureService", code: 403, userInfo: [NSLocalizedDescriptionKey: "获取体温数据失败: \(error)"])
//                completion(false, nsError)
//                return
//            }
//            
//            // 过滤有效温度值
//            let validTemperatures = model.tempeartures.filter { $0 > 0 }
//            if validTemperatures.isEmpty {
//                let nsError = NSError(domain: "TemperatureService", code: 404, userInfo: [NSLocalizedDescriptionKey: "没有有效的体温数据"])
//                completion(false, nsError)
//                return
//            }
//            
//            print("获取到\(validTemperatures.count)个有效体温数据点，准备上传")
//            
//            // 计算平均值、最大值、最小值
//            let averageTemperature = validTemperatures.reduce(0, +) / Double(validTemperatures.count)
//            let maxTemperature = validTemperatures.max() ?? 0
//            let minTemperature = validTemperatures.min() ?? 0
//            
//            // 计算日期字符串
//            let dateFormatter = DateFormatter()
//            dateFormatter.dateFormat = "yyyy-MM-dd"
//            
//            let calendar = Calendar.current
//            let now = Date()
//            guard let date = calendar.date(byAdding: .day, value: -day, to: now) else {
//                completion(false, NSError(domain: "TemperatureService", code: 500, userInfo: [NSLocalizedDescriptionKey: "日期计算错误"]))
//                return
//            }
//            
//            let dateString = dateFormatter.string(from: date)
//            
//            // 将日期设置为午夜
//            var dateComponents = calendar.dateComponents([.year, .month, .day], from: date)
//            dateComponents.hour = 0
//            dateComponents.minute = 0
//            dateComponents.second = 0
//            
//            guard let startOfDay = calendar.date(from: dateComponents) else {
//                completion(false, NSError(domain: "TemperatureService", code: 500, userInfo: [NSLocalizedDescriptionKey: "日期计算错误"]))
//                return
//            }
//            
//            // 创建温度记录
//            var temperatureRecords: [TemperatureRecord] = []
//            for (index, temperature) in model.tempeartures.enumerated() {
//                if temperature <= 0 { continue } // 跳过无效值
//                
//                // 计算时间戳：当天午夜 + index*5分钟
//                let recordDate = calendar.date(byAdding: .minute, value: index * 5, to: startOfDay) ?? Date()
//                let milliseconds = Int64(recordDate.timeIntervalSince1970 * 1000)
//                
//                let record = TemperatureRecord(
//                    temperature: temperature,
//                    timestamp: milliseconds
//                )
//                temperatureRecords.append(record)
//            }
//            
//            // 创建上传数据模型
//            let uploadData = TemperatureUploadData(
//                date: dateString,
//                averageTemperature: averageTemperature,
//                maxTemperature: maxTemperature,
//                minTemperature: minTemperature,
//                records: temperatureRecords
//            )
//            
//            // 准备URL
//            guard let url = URL(string: "http://ring-api-dev.weaving-park.com/app-api/iot/sleep/upload/temperature/data") else {
//                completion(false, NSError(domain: "TemperatureService", code: 400, userInfo: [NSLocalizedDescriptionKey: "无效的URL"]))
//                return
//            }
//            
//            // 准备请求
//            var request = URLRequest(url: url)
//            request.httpMethod = "POST"
//            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
//            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
//            
//            // 添加租户ID
//            request.addValue("1", forHTTPHeaderField: "tenant-id")
//            
//            // 将数据转换为JSON
//            do {
//                let jsonData = try JSONEncoder().encode(uploadData)
//                request.httpBody = jsonData
//                
//                // 打印上传的JSON数据，便于调试
//                if let jsonString = String(data: jsonData, encoding: .utf8) {
//                    print("上传的体温数据JSON: \(jsonString)")
//                }
//            } catch {
//                completion(false, error)
//                return
//            }
//            
//            // 发送请求
//            let task = URLSession.shared.dataTask(with: request) { data, response, error in
//                // 处理响应
//                if let error = error {
//                    DispatchQueue.main.async {
//                        completion(false, error)
//                    }
//                    return
//                }
//                
//                // 检查HTTP响应状态码
//                guard let httpResponse = response as? HTTPURLResponse else {
//                    DispatchQueue.main.async {
//                        completion(false, NSError(domain: "TemperatureService", code: 500, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
//                    }
//                    return
//                }
//                
//                // 打印响应状态码
//                print("体温数据上传响应状态码: \(httpResponse.statusCode)")
//                
//                // 打印响应数据
//                if let data = data, let responseString = String(data: data, encoding: .utf8) {
//                    print("体温数据上传响应: \(responseString)")
//                    
//                    // 发送通知，包含响应数据
//                    NotificationCenter.default.post(
//                        name: .temperatureDataUploadResponse,
//                        object: nil,
//                        userInfo: ["response": data]
//                    )
//                }
//                
//                // 根据状态码判断是否成功
//                let success = (200...299).contains(httpResponse.statusCode)
//                
//                DispatchQueue.main.async {
//                    if success {
//                        // 发送成功上传通知
//                        NotificationCenter.default.post(
//                            name: .temperatureDataUploaded,
//                            object: nil,
//                            userInfo: [
//                                "count": temperatureRecords.count,
//                                "success": true
//                            ]
//                        )
//                        completion(true, nil)
//                    } else {
//                        let message = "服务器返回错误状态码: \(httpResponse.statusCode)"
//                        completion(false, NSError(domain: "TemperatureService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: message]))
//                    }
//                }
//            }
//            
//            task.resume()
//        }
    }
    
    /// 上传最近14天的睡眠体温数据
    /// - Parameter completion: 完成回调，返回成功上传的天数和可能的错误
    public func uploadLast14DaysSleepTemperatureData(completion: @escaping (Int, Error?) -> Void) {
        // 确保设备已连接
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "TemperatureService", code: 400, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        // 获取用户认证信息
        guard authService.currentToken?.accessToken != nil else {
            completion(0, NSError(domain: "TemperatureService", code: 401, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        // 设置上传状态
        isUploading = true
        
        // 创建上传任务组
        let uploadGroup = DispatchGroup()
        var successCount = 0
        var lastError: Error? = nil
        
        // 对最近14天的数据进行处理
        for day in 0..<14 {
            uploadGroup.enter()
            
            uploadSleepTemperatureDataFromDevice(day: day) { success, error in
                if success {
                    successCount += 1
                } else if let error = error {
                    print("上传第\(day)天的体温数据失败: \(error.localizedDescription)")
                    if lastError == nil {
                        lastError = error
                    }
                }
                
                uploadGroup.leave()
            }
        }
        
        // 等待所有上传任务完成
        uploadGroup.notify(queue: .main) {
            self.isUploading = false
            
            print("完成14天体温数据上传，成功天数: \(successCount)")
            completion(successCount, lastError)
        }
    }
    
    /// 检查睡眠体温监测状态，如果未启用则启用它
    /// - Parameter completion: 完成回调，返回是否成功启用以及可能的错误
    public func checkAndEnableSleepTemperatureMonitoring(completion: @escaping (Bool, Error?) -> Void) {
        // 确保设备已连接
        guard deviceService.connectionState.isConnected else {
            completion(false, NSError(domain: "TemperatureService", code: 400, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        // 获取睡眠温度监测状态
//        CRPSmartRingSDK.sharedInstance.getSleepTemperatureState { isEnabled, error in
//            if error != .none {
//                let nsError = NSError(domain: "TemperatureService", code: 403, userInfo: [NSLocalizedDescriptionKey: "获取睡眠体温监测状态失败: \(error)"])
//                completion(false, nsError)
//                return
//            }
//            
//            if isEnabled {
//                // 已经启用，直接返回成功
//                print("睡眠体温监测已启用")
//                completion(true, nil)
//            } else {
//                // 未启用，尝试启用
//                print("睡眠体温监测未启用，正在尝试启用...")
//                CRPSmartRingSDK.sharedInstance.setSleepTemperatureState(open: true)
//                
//                // 延迟一小段时间再次检查以确认启用状态
//                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
//                    CRPSmartRingSDK.sharedInstance.getSleepTemperatureState { isEnabledNow, error in
//                        if error != .none {
//                            let nsError = NSError(domain: "TemperatureService", code: 403, userInfo: [NSLocalizedDescriptionKey: "验证睡眠体温监测状态失败: \(error)"])
//                            completion(false, nsError)
//                            return
//                        }
//                        
//                        if isEnabledNow {
//                            print("睡眠体温监测已成功启用")
//                            completion(true, nil)
//                        } else {
//                            print("尝试启用睡眠体温监测失败")
//                            let nsError = NSError(domain: "TemperatureService", code: 404, userInfo: [NSLocalizedDescriptionKey: "无法启用睡眠体温监测"])
//                            completion(false, nsError)
//                        }
//                    }
//                }
//            }
//        }
    }
}

// MARK: - 数据模型

/// 温度上传数据模型
struct TemperatureUploadData: Codable {
    let date: String
    let averageTemperature: Double
    let maxTemperature: Double
    let minTemperature: Double
    let records: [TemperatureRecord]
}

/// 温度记录模型
struct TemperatureRecord: Codable {
    let temperature: Double
    let timestamp: Int64
}

// MARK: - 通知扩展
// 注意：所有通知名称应统一在 NotificationExtensions.swift 中定义，此段代码已被注释掉
/*
extension Notification.Name {
    static let temperatureDataFetched = Notification.Name("temperatureDataFetched")
    static let temperatureDataUploaded = Notification.Name("temperatureDataUploaded")
    static let temperatureDataUploadResponse = Notification.Name("temperatureDataUploadResponse")
} 
*/ 
