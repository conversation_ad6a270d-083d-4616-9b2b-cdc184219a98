import Foundation
import Combine
#if os(iOS)
import CRPSmartRing
#endif

/// 数据同步服务
/// 负责协调原始数据获取、上传和处理后数据下载的整个流程
public class DataSyncService: ObservableObject {
    // MARK: - 单例
    public static let shared = DataSyncService()
    
    // MARK: - 属性
    private let deviceService: WindRingDeviceService
    private let rawDataManager: RawDataManager
    private let processedDataManager: ProcessedDataManager
    private let mqttSyncService: MQTTSyncService
    
    /// 同步状态
    @Published public var isSyncing: Bool = false
    
    /// 同步进度 (0-100)
    @Published public var syncProgress: Int = 0
    
    /// 同步状态消息
    @Published public var statusMessage: String = "准备同步"
    
    /// 最后同步时间
    @Published public var lastSyncTime: Date? = nil
    
    /// 取消令牌
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    private init() {
        self.deviceService = WindRingDeviceService.shared
        self.rawDataManager = RawDataManager.shared
        self.processedDataManager = ProcessedDataManager.shared
        self.mqttSyncService = MQTTSyncService.shared
        
        // 监听设备连接状态变化
        deviceService.$connectionState
            .sink { [weak self] state in
                if state.isConnected {
                    print("设备已连接，可以开始数据同步")
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 开始数据同步流程
    /// - Parameter completion: 完成回调
    public func startSync(completion: @escaping (Bool, Error?) -> Void) {
        guard !isSyncing else {
            let error = NSError(domain: "DataSync", code: 400, userInfo: [NSLocalizedDescriptionKey: "同步已在进行中"])
            completion(false, error)
            return
        }
        
        guard deviceService.connectionState.isConnected else {
            let error = NSError(domain: "DataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"])
            completion(false, error)
            return
        }
        
        // 开始同步
        isSyncing = true
        syncProgress = 0
        statusMessage = "开始同步数据"
        
        // 执行同步流程
        syncAllData { success, error in
            DispatchQueue.main.async {
                self.isSyncing = false
                self.syncProgress = success ? 100 : 0
                self.statusMessage = success ? "同步完成" : "同步失败: \(error?.localizedDescription ?? "未知错误")"
                self.lastSyncTime = Date()
                completion(success, error)
            }
        }
    }
    
    /// 设置戒指自动上传数据
    /// - Parameter enabled: 是否启用自动上传
    public func setAutoUpload(enabled: Bool) {
        // 这里需要调用戒指SDK的相关方法设置自动上传
        // 由于SDK文档中没有明确的自动上传设置方法，这里只是一个示例
        print("设置戒指自动上传数据: \(enabled ? "启用" : "禁用")")
        
        // 如果启用自动上传，可以设置一个定时器定期检查和同步数据
        if enabled {
            // 设置定时器或其他机制
        }
    }
    
    // MARK: - 私有方法
    
    /// 同步所有数据
    private func syncAllData(completion: @escaping (Bool, Error?) -> Void) {
        // 定义同步步骤
        let steps = [
            syncRawData,
            uploadRawData,
            downloadProcessedData
        ]
        
        // 执行同步步骤
        executeSteps(steps, completion: completion)
    }
    
    /// 执行同步步骤
    private func executeSteps(_ steps: [(@escaping (Bool, Error?) -> Void) -> Void], completion: @escaping (Bool, Error?) -> Void) {
        guard !steps.isEmpty else {
            completion(true, nil)
            return
        }
        
        let step = steps[0]
        let remainingSteps = Array(steps.dropFirst())
        
        step { success, error in
            if !success {
                completion(false, error)
                return
            }
            
            // 更新进度
            let progress = Int(Double(steps.count - remainingSteps.count) / Double(steps.count) * 100)
            DispatchQueue.main.async {
                self.syncProgress = progress
            }
            
            // 执行下一步
            self.executeSteps(remainingSteps, completion: completion)
        }
    }
    
    /// 同步原始数据（从戒指获取数据并保存到本地）
    private func syncRawData(completion: @escaping (Bool, Error?) -> Void) {
        DispatchQueue.main.async {
            self.statusMessage = "正在从戒指获取原始数据"
        }
        
        // 同步心率数据
        syncHeartRateData { success, error in
            if !success {
                completion(false, error)
                return
            }
            
            // 同步血氧数据
            self.syncBloodOxygenData { success, error in
                if !success {
                    completion(false, error)
                    return
                }
                
                // 同步活动数据
                self.syncActivityData { success, error in
                    completion(success, error)
                }
            }
        }
    }
    
    /// 上传原始数据（将本地原始数据上传到服务器）
    private func uploadRawData(completion: @escaping (Bool, Error?) -> Void) {
        DispatchQueue.main.async {
            self.statusMessage = "正在上传原始数据到服务器"
        }
        
        // 获取未处理的原始数据
        let heartRateData = rawDataManager.getUnprocessedHeartRateData()
        let bloodOxygenData = rawDataManager.getUnprocessedBloodOxygenData()
        let activityData = rawDataManager.getUnprocessedActivityData()
        
        // 使用MQTT服务上传数据
        uploadHeartRateData(heartRateData) { success, error in
            if !success {
                completion(false, error)
                return
            }
            
            self.uploadBloodOxygenData(bloodOxygenData) { success, error in
                if !success {
                    completion(false, error)
                    return
                }
                
                self.uploadActivityData(activityData) { success, error in
                    completion(success, error)
                }
            }
        }
    }
    
    /// 下载处理后的数据（从服务器获取处理后的数据并保存到本地）
    private func downloadProcessedData(completion: @escaping (Bool, Error?) -> Void) {
        DispatchQueue.main.async {
            self.statusMessage = "正在从服务器下载处理后的数据"
        }
        
        // 这里应该实现从服务器下载处理后数据的逻辑
        // 由于没有具体的API接口信息，这里只是一个示例框架
        
        // 模拟下载处理后的心率数据
        downloadProcessedHeartRateData { success, error in
            if !success {
                completion(false, error)
                return
            }
            
            // 模拟下载处理后的血氧数据
            self.downloadProcessedBloodOxygenData { success, error in
                if !success {
                    completion(false, error)
                    return
                }
                
                // 模拟下载处理后的活动数据
                self.downloadProcessedActivityData { success, error in
                    completion(success, error)
                }
            }
        }
    }
    
    // MARK: - 心率数据相关方法
    
    /// 同步心率数据
    private func syncHeartRateData(completion: @escaping (Bool, Error?) -> Void) {
        // 获取当前用户ID和设备ID
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "current_user"
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown_device"
        
        // 使用SDK获取心率数据
        #if os(iOS)
        // 这里应该调用SDK的方法获取心率数据
        // 由于没有具体的API调用示例，这里使用模拟数据
        
        // 模拟获取心率数据
        let heartRates = [(Date(), 75), (Date().addingTimeInterval(-60), 76), (Date().addingTimeInterval(-120), 77)]
        
        // 保存到本地数据库
        var savedCount = 0
        for (timestamp, value) in heartRates {
            if rawDataManager.saveRawHeartRateData(value: value, userId: userId, deviceId: deviceId, timestamp: timestamp) {
                savedCount += 1
            }
        }
        
        print("已保存\(savedCount)条心率数据")
        completion(true, nil)
        #else
        // 非iOS平台，无法使用SDK
        completion(false, NSError(domain: "DataSync", code: 403, userInfo: [NSLocalizedDescriptionKey: "当前平台不支持SDK"]))
        #endif
    }
    
    /// 上传心率数据
    private func uploadHeartRateData(_ data: [HeartRateEntity], completion: @escaping (Bool, Error?) -> Void) {
        guard !data.isEmpty else {
            print("没有需要上传的心率数据")
            completion(true, nil)
            return
        }
        
        // 上传数据到服务器
        var uploadedCount = 0
        let totalCount = data.count
        
        for entity in data {
            guard let userId = entity.userId, let deviceId = entity.deviceId else { continue }
            
            // 构建上传数据
            let uploadData: [String: Any] = [
                "type": "heart_rate",
                "user_id": userId,
                "device_id": deviceId,
                "timestamp": entity.timestamp?.timeIntervalSince1970 ?? 0,
                "value": entity.value
            ]
            
            // 使用MQTT服务上传数据
            mqttSyncService.cacheHealthData(ringId: deviceId, data: uploadData)
            
            // 标记为已处理
            rawDataManager.markHeartRateAsProcessed(entity)
            
            uploadedCount += 1
        }
        
        print("已上传\(uploadedCount)/\(totalCount)条心率数据")
        completion(true, nil)
    }
    
    /// 下载处理后的心率数据
    private func downloadProcessedHeartRateData(completion: @escaping (Bool, Error?) -> Void) {
        // 获取当前用户ID和设备ID
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "current_user"
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown_device"
        
        // 这里应该实现从服务器下载处理后的心率数据的逻辑
        // 由于没有具体的API接口信息，这里只是一个示例
        
        // 模拟从服务器获取处理后的心率数据
        let processedHeartRates = [(Date(), 75, 95), (Date().addingTimeInterval(-60), 76, 96), (Date().addingTimeInterval(-120), 77, 97)]
        
        // 保存到本地数据库
        var savedCount = 0
        for (timestamp, value, confidence) in processedHeartRates {
            if processedDataManager.saveProcessedHeartRateData(value: value, userId: userId, deviceId: deviceId, timestamp: timestamp, confidence: confidence) {
                savedCount += 1
            }
        }
        
        print("已保存\(savedCount)条处理后的心率数据")
        completion(true, nil)
    }
    
    // MARK: - 血氧数据相关方法
    
    /// 同步血氧数据
    private func syncBloodOxygenData(completion: @escaping (Bool, Error?) -> Void) {
        // 获取当前用户ID和设备ID
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "current_user"
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown_device"
        
        // 使用SDK获取血氧数据
        #if os(iOS)
        // 这里应该调用SDK的方法获取血氧数据
        // 由于没有具体的API调用示例，这里使用模拟数据
        
        // 模拟获取血氧数据
        let bloodOxygens = [(Date(), 98.0), (Date().addingTimeInterval(-60), 97.5), (Date().addingTimeInterval(-120), 98.2)]
        
        // 保存到本地数据库
        var savedCount = 0
        for (timestamp, value) in bloodOxygens {
            if rawDataManager.saveRawBloodOxygenData(value: value, userId: userId, deviceId: deviceId, timestamp: timestamp) {
                savedCount += 1
            }
        }
        
        print("已保存\(savedCount)条血氧数据")
        completion(true, nil)
        #else
        // 非iOS平台，无法使用SDK
        completion(false, NSError(domain: "DataSync", code: 403, userInfo: [NSLocalizedDescriptionKey: "当前平台不支持SDK"]))
        #endif
    }
    
    /// 上传血氧数据
    private func uploadBloodOxygenData(_ data: [BloodOxygenEntity], completion: @escaping (Bool, Error?) -> Void) {
        guard !data.isEmpty else {
            print("没有需要上传的血氧数据")
            completion(true, nil)
            return
        }
        
        // 上传数据到服务器
        var uploadedCount = 0
        let totalCount = data.count
        
        for entity in data {
            guard let userId = entity.userId, let deviceId = entity.deviceId else { continue }
            
            // 构建上传数据
            let uploadData: [String: Any] = [
                "type": "blood_oxygen",
                "user_id": userId,
                "device_id": deviceId,
                "timestamp": entity.timestamp?.timeIntervalSince1970 ?? 0,
                "value": entity.value
            ]
            
            // 使用MQTT服务上传数据
            mqttSyncService.cacheHealthData(ringId: deviceId, data: uploadData)
            
            // 标记为已处理
            rawDataManager.markBloodOxygenAsProcessed(entity)
            
            uploadedCount += 1
        }
        
        print("已上传\(uploadedCount)/\(totalCount)条血氧数据")
        completion(true, nil)
    }
    
    /// 下载处理后的血氧数据
    private func downloadProcessedBloodOxygenData(completion: @escaping (Bool, Error?) -> Void) {
        // 获取当前用户ID和设备ID
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "current_user"
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown_device"
        
        // 这里应该实现从服务器下载处理后的血氧数据的逻辑
        // 由于没有具体的API接口信息，这里只是一个示例
        
        // 模拟从服务器获取处理后的血氧数据
        let processedBloodOxygens = [(Date(), 98.0, 95), (Date().addingTimeInterval(-60), 97.5, 96), (Date().addingTimeInterval(-120), 98.2, 97)]
        
        // 保存到本地数据库
        var savedCount = 0
        for (timestamp, value, confidence) in processedBloodOxygens {
            if processedDataManager.saveProcessedBloodOxygenData(value: value, userId: userId, deviceId: deviceId, timestamp: timestamp, confidence: confidence) {
                savedCount += 1
            }
        }
        
        print("已保存\(savedCount)条处理后的血氧数据")
        completion(true, nil)
    }
    
    // MARK: - 活动数据相关方法
    
    /// 同步活动数据
    private func syncActivityData(completion: @escaping (Bool, Error?) -> Void) {
        // 获取当前用户ID和设备ID
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "current_user"
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown_device"
        
        // 使用SDK获取活动数据
        #if os(iOS)
        // 这里应该调用SDK的方法获取活动数据
        // 由于没有具体的API调用示例，这里使用模拟数据
        
        // 模拟获取活动数据
        let activities = [(Date(), 8500, 5.2, 320), (Date().addingTimeInterval(-86400), 7800, 4.8, 290)]
        
        // 保存到本地数据库
        var savedCount = 0
        for (date, steps, distance, calories) in activities {
            if rawDataManager.saveRawActivityData(steps: steps, distance: distance, calories: calories, userId: userId, deviceId: deviceId, date: date) {
                savedCount += 1
            }
        }
        
        print("已保存\(savedCount)条活动数据")
        completion(true, nil)
        #else
        // 非iOS平台，无法使用SDK
        completion(false, NSError(domain: "DataSync", code: 403, userInfo: [NSLocalizedDescriptionKey: "当前平台不支持SDK"]))
        #endif
    }
    
    /// 上传活动数据
    private func uploadActivityData(_ data: [StepsEntity], completion: @escaping (Bool, Error?) -> Void) {
        guard !data.isEmpty else {
            print("没有需要上传的活动数据")
            completion(true, nil)
            return
        }
        
        // 上传数据到服务器
        var uploadedCount = 0
        let totalCount = data.count
        
        for entity in data {
            guard let userId = entity.userId, let deviceId = entity.deviceId, let date = entity.date else { continue }
            
            // 构建上传数据
            let uploadData: [String: Any] = [
                "type": "activity",
                "user_id": userId,
                "device_id": deviceId,
                "date": date.timeIntervalSince1970,
                "steps": entity.value,
                "distance": entity.distance,
                "calories": entity.calories
            ]
            
            // 使用MQTT服务上传数据
            mqttSyncService.cacheHealthData(ringId: deviceId, data: uploadData)
            
            // 标记为已处理
            rawDataManager.markActivityAsProcessed(entity)
            
            uploadedCount += 1
        }
        
        print("已上传\(uploadedCount)/\(totalCount)条活动数据")
        completion(true, nil)
    }
    
    /// 下载处理后的活动数据
    private func downloadProcessedActivityData(completion: @escaping (Bool, Error?) -> Void) {
        // 获取当前用户ID和设备ID
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "current_user"
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown_device"
        
        // 这里应该实现从服务器下载处理后的活动数据的逻辑
        // 由于没有具体的API接口信息，这里只是一个示例
        
        // 模拟从服务器获取处理后的活动数据
        let processedActivities = [(Date(), 8500, 5.2, 320), (Date().addingTimeInterval(-86400), 7800, 4.8, 290)]
        
        // 保存到本地数据库
        var savedCount = 0
        for (date, steps, distance, calories) in processedActivities {
            if processedDataManager.saveProcessedActivityData(steps: steps, distance: distance, calories: calories, userId: userId, deviceId: deviceId, date: date) {
                savedCount += 1
            }
        }
        
        print("已保存\(savedCount)条处理后的活动数据")
        completion(true, nil)
    }
}

