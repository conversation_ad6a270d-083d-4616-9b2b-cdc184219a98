import Foundation
#if os(iOS)
import CRPSmartRing
#endif
import Combine

/// GoMore算法服务 - 管理与GoMore算法相关的所有功能
class GoMoreService: ObservableObject {
    // MARK: - 单例
    public static let shared = GoMoreService()
    
    // MARK: - 发布属性
    // GoMore支持状态
    @Published var isGoMoreSupported: Bool = false
    @Published var algorithmType: Int = 0
    @Published var isKeyMatched: Bool = false
    @Published var chipID: String = ""
    
    // 操作状态
    @Published var isLoading: Bool = false
    @Published var errorMessage: String = ""
    
    // MARK: - 私有属性
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    private init() {
        // 注册通知
        NotificationCenter.default
            .publisher(for: .deviceConnected)
            .sink { [weak self] _ in
                self?.checkGoMoreSupportIfConnected()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 检查设备是否支持GoMore算法
    /// - Parameter completion: 完成回调，返回是否支持
    func checkGoMoreSupport(completion: ((Bool, CRPError?) -> Void)? = nil) {
        isLoading = true
        errorMessage = ""
        
        print("开始检查GoMore算法支持状态...")
        
        // 获取设备信息，便于调试
        if let deviceInfo = WindRingDeviceService.shared.deviceInfo {
            print("当前设备信息: 名称=\(deviceInfo.localName), 固件版本=\(deviceInfo.firmwareVersion?.description ?? ""), 硬件版本=了解以后在获取")//\(deviceInfo.hardwareVersion)
        }
        
        if let discovery = WindRingDeviceService.shared.currentDiscovery {
            print("原始设备信息: 名称=\(discovery.localName ?? "未知"), MAC=\(discovery.mac ?? "未知"), RSSI=\(discovery.RSSI)")
        }
//        let semaphore = DispatchSemaphore(value: 0)
        CRPSmartRingManage.shared.getGoMoreAlgorithmSupport { [weak self] model, error in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.isLoading = false
                
                if error == .none {
                    self.isGoMoreSupported = model.support
                    self.algorithmType = model.type
                    
                    print("GoMore支持状态: \(model.support ? "支持" : "不支持"), 类型: \(model.type)")
                    
                    // 特殊处理低端固件设备
                    if !model.support && model.type > 0 {
                        print("发现低端固件设备，算法类型: \(model.type)，但不完全支持GoMore算法")
                    }
                    
                    // 如果支持，自动检查密钥匹配状态
                    if model.support {
                        self.checkGoMoreKeySupport()
                    }
//                    result = model.support
                    completion?(model.support, nil)
                } else {
                    self.errorMessage = "检查GoMore支持失败: \(error)"
                    self.isGoMoreSupported = false
                    print("检查GoMore支持失败: \(error)")
//                    result = false
                    completion?(false, error)
                }
//                semaphore.signal()
                // 发送通知
                NotificationCenter.default.post(name: .goMoreSupportUpdated, object: model.support)
            }
        }
//        let waitResult = semaphore.wait(timeout: .now() + 5)
//        if waitResult == .timedOut {
//            print("❌ getGoMoreAlgorithmSupport 超时")
//            completion?(false, nil)
//            NotificationCenter.default.post(name: .goMoreSupportUpdated, object: false)
//            
//        }
        
    }
    
    
    /// 检查GoMore密钥是否匹配
    /// - Parameter completion: 完成回调，返回是否匹配
    func checkGoMoreKeySupport(completion: ((Bool, CRPError?) -> Void)? = nil) {
        guard isGoMoreSupported else {
            completion?(false, nil)
            return
        }
        
        isLoading = true
        
        
        CRPSmartRingManage.shared.getGoMoreKeySupport { [weak self] isMatched, error in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.isLoading = false
                
                if error == .none {
                    self.isKeyMatched = isMatched
                    print("GoMore密钥匹配状态: \(isMatched ? "已匹配" : "未匹配")")
                    
                    // 如果密钥已匹配，自动获取ChipID
                    if isMatched {
                        self.getGoMoreChipID()
                    }
                    
                    completion?(isMatched, nil)
                } else {
                    self.errorMessage = "检查GoMore密钥失败: \(error)"
                    print("检查GoMore密钥失败: \(error)")
                    completion?(false, error)
                }
            }
        }
    }
    
    /// 设置GoMore密钥
    /// - Parameters:
    ///   - key: 密钥字符串
    ///   - completion: 完成回调，返回是否成功
    func setGoMoreKey(_ key: String, completion: ((Bool, CRPError?) -> Void)? = nil) {
        guard isGoMoreSupported, !key.isEmpty else {
            completion?(false, nil)
            return
        }
        
        isLoading = true
        
        CRPSmartRingManage.shared.getGoMoreKeySupport(key: key) { [weak self] isSuccess, error in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.isLoading = false
                
                if error == .none {
                    self.isKeyMatched = isSuccess
                    
                    if isSuccess {
                        print("GoMore密钥设置成功")
                        // 密钥设置成功后获取ChipID
                        self.getGoMoreChipID()
                    } else {
                        self.errorMessage = "GoMore密钥设置失败，请检查密钥格式"
                        print("GoMore密钥设置失败")
                    }
                    
                    completion?(isSuccess, nil)
                } else {
                    self.errorMessage = "设置GoMore密钥失败: \(error)"
                    print("设置GoMore密钥失败: \(error)")
                    completion?(false, error)
                }
            }
        }
    }
    
    /// 获取GoMore ChipID
    /// - Parameter completion: 完成回调，返回ChipID
    func getGoMoreChipID(completion: ((String, CRPError?) -> Void)? = nil) {
        guard isGoMoreSupported, isKeyMatched else {
            completion?("", nil)
            return
        }
        
        isLoading = true
        
        CRPSmartRingManage.shared.getGoMoreChipID { [weak self] chipID, error in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.isLoading = false
                
                if error == .none {
                    self.chipID = chipID
                    print("获取到GoMore ChipID: \(chipID)")
                    completion?(chipID, nil)
                } else {
                    self.errorMessage = "获取GoMore ChipID失败: \(error)"
                    print("获取GoMore ChipID失败: \(error)")
                    completion?("", error)
                }
            }
        }
    }
    
    // MARK: - 私有方法
    
    /// 设备连接后自动检查GoMore支持状态
    private func checkGoMoreSupportIfConnected() {
        let deviceService = WindRingDeviceService.shared
        
        if deviceService.connectionState.isConnected {
//            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                self.checkGoMoreSupport()
//            }
        }
    }
}

// MARK: - 通知名称扩展
// 注意：所有通知名称已移至 NotificationExtensions.swift 中统一管理，此段代码已被注释掉
/*
extension Notification.Name {
    /// GoMore支持状态更新通知
    static let goMoreSupportUpdated = Notification.Name("goMoreSupportUpdated")
} 
*/ 
