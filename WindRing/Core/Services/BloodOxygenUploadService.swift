import Foundation
import Combine
import CoreData

#if os(iOS)
import CRPSmartRing
#endif

/// 血氧数据上传服务
class BloodOxygenUploadService {
    // 单例模式
    static let shared = BloodOxygenUploadService()
    
    // 依赖服务
    private let deviceService = WindRingDeviceService.shared
    private let healthDataManager = HealthDataManager.shared
    private let authService = AuthService.shared
    private let userService = WindRingUserService.shared
    
    // 上传API路径
    private let apiPath = "/app-api/iot/blood/upload/data"
    
    // 上传状态跟踪
    private var isUploading: Bool = false
    private var lastUploadAttempt: Date?
    private var cancellables = Set<AnyCancellable>()
    
    // 私有初始化方法，配置通知订阅
    private init() {
        setupNotifications()
    }
    
    // MARK: - 公共方法
    
    /// 从设备获取血氧历史数据并保存到本地
    /// - Parameter completion: 完成回调，返回获取的数据数量和可能的错误
    func fetchAndSaveBloodOxygenHistory(completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        print("从设备获取血氧历史数据...")
        
        // 调用SDK获取历史血氧数据
//        CRPSmartRingSDK.sharedInstance.getO2RecordData { [weak self] records, error in
//            guard let self = self else { return }
//            
//            if error == .none && !records.isEmpty {
//                print("成功获取\(records.count)条血氧历史数据")
//                
//                // 将数据保存到本地数据库
//                self.saveBloodOxygenData(records: records) { savedCount in
//                    print("已保存\(savedCount)条新血氧数据到本地")
//                    DispatchQueue.main.async {
//                        completion(savedCount, nil)
//                        
//                        // 自动尝试上传
//                        if savedCount > 0 {
//                            self.uploadPendingBloodOxygenData()
//                        }
//                    }
//                }
//            } else {
//                let errorMessage = error == .none ? "未获取到数据" : "错误: \(error)"
//                print("获取血氧历史数据失败: \(errorMessage)")
//                DispatchQueue.main.async {
//                    completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: errorMessage]))
//                }
//            }
//        }
    }
    
    /// 获取并上传血氧数据
    /// - Parameter completion: 完成回调，返回获取的总数据数量和可能的错误
    func syncBloodOxygenData(completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        print("开始同步血氧数据...")
        
        // 直接使用fetchAndSaveBloodOxygenHistory方法获取所有血氧数据
        fetchAndSaveBloodOxygenHistory { count, error in
            if let error = error {
                print("获取血氧数据失败: \(error.localizedDescription)")
                completion(0, error)
                return
            }
            
            print("成功获取\(count)条血氧数据")
            
            if count > 0 {
                // 执行上传操作
                self.uploadPendingBloodOxygenData { uploadCount, uploadError in
                    if let uploadError = uploadError {
                        print("上传血氧数据失败: \(uploadError.localizedDescription)")
                    } else {
                        print("成功上传\(uploadCount)条血氧数据")
                    }
                    
                    completion(count, uploadError)
                }
            } else {
                print("没有获取到血氧数据")
                completion(0, nil)
            }
        }
    }
    
    /// 上传待上传的血氧数据
    /// - Parameter completion: 完成回调，包含上传成功的数据数量和可能的错误
    func uploadPendingBloodOxygenData(completion: ((Int, Error?) -> Void)? = nil) {
        // 防止重复上传
        guard !isUploading else {
            print("已有上传任务正在进行中")
            completion?(0, nil)
            return
        }
        
        // 检查网络可用性
        guard NetworkMonitor.shared.isConnected else {
            print("网络不可用，稍后将自动重试上传")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "网络不可用"]))
            return
        }
        
        // 检查用户是否登录
        guard let userId = authService.currentUser?.id else {
            print("用户未登录，无法上传数据")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        // 验证Token是否存在
        guard let token = authService.currentToken?.accessToken else {
            print("用户未登录或Token已过期，无法上传数据")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录或Token已过期"]))
            return
        }
        
        print("用户已登录，Token: \(token.prefix(10))...")
        
        isUploading = true
        lastUploadAttempt = Date()
        
        // 获取所有血氧数据（因为没有isUploaded标记，所以上传所有）
        // 使用最近7天的数据
        let calendar = Calendar.current
        let endDate = Date()
        let startDate = calendar.date(byAdding: .day, value: -7, to: endDate)!
        
        let bloodOxygenData = healthDataManager.getBloodOxygens(userId: userId, startDate: startDate, endDate: endDate)
        
        if bloodOxygenData.isEmpty {
            print("没有需要上传的血氧数据")
            isUploading = false
            completion?(0, nil)
            return
        }
        
        print("找到\(bloodOxygenData.count)条待上传的血氧数据")
        
        // 按日期分组
        let groupedData = Dictionary(grouping: bloodOxygenData) { bloodOxygen -> String in
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            return dateFormatter.string(from: bloodOxygen.timestamp!)
        }
        
        print("血氧数据按\(groupedData.count)个日期分组")
        
        // 逐日上传
        var uploadedCount = 0
        let group = DispatchGroup()
        var uploadError: Error? = nil
        
        for (dateString, bloodOxygenValues) in groupedData {
            group.enter()
            
            // 准备上传数据
            let uploadData = prepareBloodOxygenUploadData(dateString: dateString, bloodOxygenValues: bloodOxygenValues)
            
            // 执行上传
            uploadBloodOxygenData(data: uploadData) { success, error in
                if success {
                    print("成功上传\(dateString)的\(bloodOxygenValues.count)条血氧数据")
                    uploadedCount += bloodOxygenValues.count
                } else {
                    print("上传\(dateString)的血氧数据失败: \(error?.localizedDescription ?? "未知错误")")
                    uploadError = error
                }
                
                group.leave()
            }
        }
        
        // 所有上传完成后的回调
        group.notify(queue: .main) {
            self.isUploading = false
            
            // 发送通知
            NotificationCenter.default.post(
                name: .bloodOxygenDataUploaded,
                object: nil,
                userInfo: [
                    "count": uploadedCount,
                    "success": uploadError == nil
                ]
            )
            
            completion?(uploadedCount, uploadError)
        }
    }
    
    // MARK: - 私有方法
    
    /// 初始化通知订阅
    private func setupNotifications() {
        // 设备连接成功后，自动获取历史数据
        NotificationCenter.default.publisher(for: .deviceSyncCompleted)
            .filter { notification in
                notification.userInfo?["success"] as? Bool == true
            }
            .debounce(for: .seconds(2), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                guard let self = self else { return }
                print("设备同步完成，准备获取血氧数据")
                self.syncBloodOxygenData { count, error in
                    if error == nil && count > 0 {
                        print("自动获取并上传了\(count)条血氧数据")
                    }
                }
            }
            .store(in: &cancellables)
        
        // 网络状态变化时，尝试上传
        NotificationCenter.default.publisher(for: .networkStatusChanged)
            .filter { notification in
                notification.userInfo?["connected"] as? Bool == true
            }
            .sink { [weak self] _ in
                guard let self = self else { return }
                print("网络已连接，尝试上传待上传的血氧数据")
                self.uploadPendingBloodOxygenData()
            }
            .store(in: &cancellables)
    }
    
    /// 将从设备获取的血氧数据保存到本地数据库
    /// - Parameters:
    ///   - records: 设备返回的血氧记录数组
    ///   - completion: 完成回调，返回成功保存的记录数量
    private func saveBloodOxygenData(records: [CRPO2RecordModel], completion: @escaping (Int) -> Void) {
        guard let userId = authService.currentUser?.id else {
            print("用户未登录，无法保存血氧数据")
            completion(0)
            return
        }
        
        print("准备保存血氧数据，共\(records.count)条记录，用户ID：\(userId)")
        
        var savedCount = 0
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown"
        
        for record in records {
            // 如果血氧值在有效范围内，则保存
            if record.value >= 70 && record.value <= 100 {
                // 打印每条记录的详细信息（调试用）
                let timestamp = Date(timeIntervalSince1970: TimeInterval(record.time))
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                print("保存血氧记录: 值=\(record.value), 时间=\(dateFormatter.string(from: timestamp))")
                
                // 使用健康数据管理器保存记录
                if healthDataManager.addBloodOxygen(userId: userId, value: record.value, timestamp: timestamp, deviceId: deviceId) {
                    savedCount += 1
                } else {
                    print("保存血氧记录失败: 值=\(record.value), 时间=\(dateFormatter.string(from: timestamp))")
                }
            }
        }
        
        print("成功保存\(savedCount)/\(records.count)条血氧记录")
        completion(savedCount)
    }
    
    /// 准备上传数据
    /// - Parameters:
    ///   - dateString: 日期字符串
    ///   - bloodOxygenValues: 血氧数据数组
    /// - Returns: 上传数据模型
    private func prepareBloodOxygenUploadData(dateString: String, bloodOxygenValues: [BloodOxygenEntity]) -> BloodOxygenUploadData {
        var records: [BloodOxygenRecord] = []
        
        print("准备\(dateString)的\(bloodOxygenValues.count)条血氧数据，使用毫秒级时间戳")
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        for bloodOxygen in bloodOxygenValues {
            if let timestamp = bloodOxygen.timestamp {
                // 使用毫秒级时间戳
                let timeStampSeconds = timestamp.timeIntervalSince1970
                let timeStampMilliseconds = Int64(timeStampSeconds * 1000)
                
                // 打印转换过程，方便调试
                let timeString = dateFormatter.string(from: timestamp)
                print("时间转换：\(timeString) -> 秒级时间戳：\(timeStampSeconds) -> 毫秒级时间戳：\(timeStampMilliseconds)")
                
                records.append(BloodOxygenRecord(
                    o2: Int(bloodOxygen.value),
                    time: timeStampMilliseconds
                ))
            }
        }
        
        return BloodOxygenUploadData(
            date: dateString,
            records: records
        )
    }
    
    /// 上传血氧数据到服务器
    /// - Parameters:
    ///   - data: 血氧上传数据模型
    ///   - completion: 完成回调
    private func uploadBloodOxygenData(data: BloodOxygenUploadData, completion: @escaping (Bool, Error?) -> Void) {
        // 构建API URL
        let baseURLString = "http://ring-api-dev.weaving-park.com" // 使用开发环境API基础URL
        guard let baseURL = URL(string: baseURLString) else {
            completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的服务器URL"]))
            return
        }
        
        let uploadURL = baseURL.appendingPathComponent(apiPath)
        print("上传血氧数据到URL: \(uploadURL.absoluteString)")
        
        // 创建请求
        var request = URLRequest(url: uploadURL)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加认证信息
        if let token = authService.currentToken?.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("添加Authorization请求头: Bearer \(token.prefix(10))...")
        } else {
            print("警告: 未找到有效的token")
        }
        
        // 添加租户ID
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        print("添加tenant-id请求头: 1")
        
        // 打印所有请求头
        print("请求头:")
        request.allHTTPHeaderFields?.forEach { key, value in
            print("\(key): \(value.prefix(30))...")
        }
        
        // 准备请求体
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let jsonData = try encoder.encode(data)
            
            // 打印上传的JSON数据
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("上传的血氧数据JSON: \n\(jsonString)")
            }
            
            request.httpBody = jsonData
            
            // 创建并执行网络请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 处理网络错误
                if let error = error {
                    print("上传血氧数据网络错误: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(false, error)
                    }
                    return
                }
                
                // 检查HTTP响应
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("无效的HTTP响应")
                    DispatchQueue.main.async {
                        completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
                    }
                    return
                }
                
                // 打印响应头
                print("响应状态码: \(httpResponse.statusCode)")
                print("响应头:")
                httpResponse.allHeaderFields.forEach { key, value in
                    print("\(key): \(value)")
                }
                
                // 打印响应数据
                if let responseData = data, let responseString = String(data: responseData, encoding: .utf8) {
                    print("服务器响应: \(responseString)")
                }
                
                // 检查状态码
                if httpResponse.statusCode == 200 {
                    print("血氧数据上传成功")
                    DispatchQueue.main.async {
                        completion(true, nil)
                    }
                } else {
                    let responseString = data != nil ? String(data: data!, encoding: .utf8) ?? "无数据" : "无数据"
                    print("血氧数据上传失败，状态码: \(httpResponse.statusCode), 响应: \(responseString)")
                    
                    // 解析错误响应
                    var errorMessage = "上传失败，HTTP状态码: \(httpResponse.statusCode)"
                    if let data = data {
                        do {
                            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                                print("错误响应JSON: \(json)")
                                
                                if let code = json["code"] as? Int {
                                    print("错误码: \(code)")
                                }
                                
                                if let msg = json["msg"] as? String {
                                    print("错误消息: \(msg)")
                                    errorMessage = msg
                                }
                                
                                if let errorData = json["data"] as? [String: Any] {
                                    print("错误数据: \(errorData)")
                                }
                            }
                        } catch {
                            print("解析错误响应失败: \(error.localizedDescription)")
                        }
                    }
                    
                    // 处理特定错误情况
                    if httpResponse.statusCode == 401 {
                        errorMessage = "用户未登录或身份验证已过期，请重新登录"
                    } else if httpResponse.statusCode == 403 {
                        errorMessage = "用户无权限执行此操作"
                    }
                    
                    DispatchQueue.main.async {
                        let error = NSError(
                            domain: "com.windring.error",
                            code: httpResponse.statusCode,
                            userInfo: [NSLocalizedDescriptionKey: errorMessage]
                        )
                        completion(false, error)
                    }
                }
            }.resume()
        } catch {
            print("编码血氧数据失败: \(error.localizedDescription)")
            completion(false, error)
        }
    }
}

// MARK: - 模型定义

/// 血氧记录模型
struct BloodOxygenRecord: Codable {
    let o2: Int
    let time: Int64 // 使用毫秒级时间戳
}

/// 血氧上传数据模型
struct BloodOxygenUploadData: Codable {
    let date: String     // 日期
    let records: [BloodOxygenRecord]  // 血氧记录集合
}

// MARK: - 通知扩展
// 注意：所有通知名称应统一在 NotificationExtensions.swift 中定义，此段代码已被注释掉
/*
extension Notification.Name {
    /// 血氧数据上传通知
    static let bloodOxygenDataUploaded = Notification.Name("bloodOxygenDataUploaded")
} 
*/ 
