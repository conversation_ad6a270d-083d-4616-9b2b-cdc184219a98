import Foundation
import CoreBluetooth
import Combine

/// 蓝牙系统设备管理器
/// 用于监控系统级别已连接的蓝牙设备
class BluetoothSystemDeviceManager: NSObject {
    // MARK: - 单例
    static let shared = BluetoothSystemDeviceManager()
    
    // MARK: - 属性
    var centralManager: CBCentralManager!
    private var connectedPeripherals: [CBPeripheral] = []
    
    // 通过Combine发布设备连接状态变化
    private let connectionStatusSubject = PassthroughSubject<[CBPeripheral], Never>()
    var connectionStatusPublisher: AnyPublisher<[CBPeripheral], Never> {
        return connectionStatusSubject.eraseToAnyPublisher()
    }
    
    // 初始化
    private override init() {
        super.init()
        
        // 在主队列上创建中央管理器
        centralManager = CBCentralManager(delegate: self, queue: .main, options: [
            CBCentralManagerOptionShowPowerAlertKey: false
        ])
        
        print("蓝牙系统设备管理器已初始化")
    }
    
    // MARK: - 公共方法
    
    /// 刷新系统已连接的蓝牙设备列表
    /// - Returns: 返回当前系统已连接的设备列表
    @discardableResult
    func refreshConnectedDevices() -> [CBPeripheral] {
        guard centralManager.state == .poweredOn else {
            print("蓝牙未开启，无法检测系统连接的设备")
            connectedPeripherals = []
            connectionStatusSubject.send([])
            return []
        }
        
        // 我们关心的服务UUID
        let targetServiceUUIDs = [
            CBUUID(string: "180D"), // 心率服务
            CBUUID(string: "180A"), // 设备信息服务
            CBUUID(string: "6E400001-B5A3-F393-E0A9-E50E24DCCA9E") // 自定义服务
        ]
        
        // 获取系统已连接的设备
        let peripherals = centralManager.retrieveConnectedPeripherals(withServices: targetServiceUUIDs)
        
        // 更新本地列表并发布更新
        if peripherals != connectedPeripherals {
            connectedPeripherals = peripherals
            
            // 打印设备列表以便调试
            peripherals.forEach { peripheral in
                print("系统已连接设备: \(peripheral.name ?? "未命名") (\(peripheral.identifier))")
            }
            
            // 发布更新
            connectionStatusSubject.send(peripherals)
        }
        
        return peripherals
    }
    
    /// 检查设备是否已被系统连接
    /// - Parameter identifier: 设备的UUID
    /// - Returns: 如果设备已被系统连接，返回true
    func isDeviceConnectedBySystem(identifier: UUID) -> Bool {
        return connectedPeripherals.contains { $0.identifier == identifier }
    }
    
    /// 获取所有系统已连接的设备
    /// - Returns: 设备列表
    func getAllConnectedDevices() -> [CBPeripheral] {
        return connectedPeripherals
    }
}

// MARK: - CBCentralManagerDelegate
extension BluetoothSystemDeviceManager: CBCentralManagerDelegate {
    
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        switch central.state {
        case .poweredOn:
            print("蓝牙已开启，开始检测系统已连接设备")
            refreshConnectedDevices()
            
            // 发送蓝牙状态变更通知
            NotificationCenter.default.post(
                name: .bluetoothStateChanged,
                object: nil,
                userInfo: ["enabled": true]
            )
            
        case .poweredOff:
            print("蓝牙已关闭")
            connectedPeripherals = []
            connectionStatusSubject.send([])
            
            // 发送蓝牙状态变更通知
            NotificationCenter.default.post(
                name: .bluetoothStateChanged,
                object: nil,
                userInfo: ["enabled": false]
            )
            
        case .unsupported:
            print("设备不支持蓝牙")
            
        case .unauthorized:
            print("蓝牙权限被拒绝")
            
        case .resetting:
            print("蓝牙正在重置")
            
        case .unknown:
            print("蓝牙状态未知")
            
        @unknown default:
            print("蓝牙未知状态")
        }
    }
    
    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        // 这里不会被调用，因为我们只是监控系统已连接的设备
        // 但是我们可以在设备连接状态变化时刷新设备列表
        print("系统连接新设备: \(peripheral.name ?? "未命名") (\(peripheral.identifier))")
        refreshConnectedDevices()
    }
    
    func centralManager(_ central: CBCentralManager, didDisconnectPeripheral peripheral: CBPeripheral, error: Error?) {
        // 这里不会被调用，因为我们只是监控系统已连接的设备
        // 但是我们可以在设备断开连接时刷新设备列表
        print("系统断开设备连接: \(peripheral.name ?? "未命名") (\(peripheral.identifier))")
        refreshConnectedDevices()
    }
}

// MARK: - 通知名扩展
extension Notification.Name {
    /// 蓝牙状态变化通知
    static let bluetoothStateChanged = Notification.Name("bluetoothStateChanged")
} 
