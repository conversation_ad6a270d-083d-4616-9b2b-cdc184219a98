//
//  ImageUploadService.swift
//  WindRing
//
//  Created by zx on 2025/6/19.
//

import UIKit

// MARK: - 图片上传服务
class ImageUploadService {
    static let shared = ImageUploadService()
    
    private init() {}
    
    // 上传图片到服务器并获取URL
    func uploadImage(_ image: UIImage, completion: @escaping (Result<String, Error>) -> Void) {
        // 1. 将图片转换为数据
        guard let imageData = image.jpegData(compressionQuality: 0.5) else {
            completion(.failure(NSError(domain: "ImageUploadError", code: 1001, userInfo: [NSLocalizedDescriptionKey: "图片转换失败"])))
            return
        }
        
        // 2. 创建上传请求
        let urlString = "\(AppGlobals.apiBaseURL)/app-api/common/upload"
        guard let url = URL(string: urlString) else {
            completion(.failure(NSError(domain: "ImageUploadError", code: 1002, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        // 3. 设置请求头
        let boundary = UUID().uuidString
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        // 4. 添加认证头
        if let token = AuthService.shared.currentToken {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        // 5. 设置请求体
        var body = Data()
        
        // 添加图片数据
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"file\"; filename=\"image.jpg\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
        body.append(imageData)
        body.append("\r\n".data(using: .utf8)!)
        
        // 结束标记
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        request.httpBody = body
        
        // 6. 创建上传任务
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理错误
            if let error = error {
                completion(.failure(error))
                return
            }
            
            // 检查响应
            guard let httpResponse = response as? HTTPURLResponse else {
                completion(.failure(NSError(domain: "ImageUploadError", code: 1003, userInfo: [NSLocalizedDescriptionKey: "无效的响应"])))
                return
            }
            
            // 检查状态码
            guard (200...299).contains(httpResponse.statusCode) else {
                completion(.failure(NSError(domain: "ImageUploadError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "服务器错误: \(httpResponse.statusCode)"])))
                return
            }
            
            // 解析响应数据
            guard let data = data else {
                completion(.failure(NSError(domain: "ImageUploadError", code: 1004, userInfo: [NSLocalizedDescriptionKey: "没有响应数据"])))
                return
            }
            
            do {
                // 解析JSON响应
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                   let dataObj = json["data"] as? [String: Any],
                   let url = dataObj["url"] as? String {
                    // 返回图片URL
                    completion(.success(url))
                } else {
                    completion(.failure(NSError(domain: "ImageUploadError", code: 1005, userInfo: [NSLocalizedDescriptionKey: "无法解析响应数据"])))
                }
            } catch {
                completion(.failure(error))
            }
        }
        
        // 7. 开始上传任务
        task.resume()
    }
}
