#if os(iOS)
import UIKit
#endif
import CoreData

#if os(iOS)
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> <PERSON><PERSON> {
        // 初始化日志
        print("====================================")
        print("应用启动: \(Date())")
        print("====================================")
        
        // 初始化设备连接服务 - 实例化对象即可，不调用setup方法
//        _ = CRPSmartRingManage.shared
        // 初始化MQTT服务
//        let mqttService = MQTTService.shared
//        
//        // 设置MQTT凭证
//        if let userId = UserDefaults.standard.string(forKey: "userId"),
//           let token = UserDefaults.standard.string(forKey: "userToken") {
//            mqttService.setCredentials(username: userId, password: token)
//        } else {
//            // 使用默认凭证
//            mqttService.setCredentials(username: "zhq", password: "123456")
//        }
//        
//        // 初始化StorageManager确保数据库已配置
//        _ = StorageManager.shared
//        
//        // 设置自动上传功能为开启状态
//        UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
//        
//        // 启动MQTT同步服务和数据上传服务
//        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
//            print("启动自动数据同步服务...")
//            MQTTSyncService.shared.start()
//            
//            // 自动启动数据上传服务
//            print("启动自动数据上传服务...")
//            RawDataUploadService.shared.startAutoUpload()
//            
//            // 打印自动上传服务状态
//            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
//                print("===== 应用启动 - 自动上传状态 =====")
//                RawDataUploadService.shared.checkAutoUploadStatus()
//                
//                // 强制启动自动上传服务
//                if WindRingDeviceService.shared.connectionState.isConnected {
//                    print("===== 应用启动 - 检测到设备已连接，强制启动自动上传 =====")
//                    RawDataUploadService.shared.startAutoUpload()
//                } else {
//                    print("===== 应用启动 - 设备未连接，等待设备连接后自动上传 =====")
//                }
//            }
//        }
        
        // 返回值
        return true
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene is being created.
        // Use this method to select a configuration to create the new scene with.
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }

    // MARK: - Core Data stack

    // 使用StorageManager的持久化容器
//    var persistentContainer: NSPersistentContainer {
//        return StorageManager.shared.persistentContainerPublic
//    }
//
//    // MARK: - Core Data Saving support
//
//    func saveContext () {
//        // 直接调用StorageManager的保存方法
//        StorageManager.shared.saveViewContext()
//    }
}
#else
// 非iOS平台的应用委托替代实现
class AppDelegate: NSObject {
    // 初始化方法
    override init() {
        super.init()
        
        // 初始化日志
        print("====================================")
        print("应用启动 (非iOS平台): \(Date())")
        print("====================================")
        
        // 初始化设备连接服务 - 实例化对象即可，不调用setup方法
        _ = WindRingDeviceService.shared
        
        // 初始化MQTT服务
        let mqttService = MQTTService.shared
        
        // 设置MQTT凭证
        if let userId = UserDefaults.standard.string(forKey: "userId"),
           let token = UserDefaults.standard.string(forKey: "userToken") {
            mqttService.setCredentials(username: userId, password: token)
        } else {
            // 使用默认凭证
            mqttService.setCredentials(username: "zhq", password: "123456")
        }
        
        // 初始化StorageManager确保数据库已配置
        _ = StorageManager.shared
        
        // 设置自动上传功能为开启状态
        UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
        
        // 启动MQTT同步服务和数据上传服务
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            print("启动自动数据同步服务...")
            MQTTSyncService.shared.start()
            
            // 自动启动数据上传服务
            print("启动自动数据上传服务...")
            RawDataUploadService.shared.startAutoUpload()
            
            // 打印自动上传服务状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                print("===== 应用启动 - 自动上传状态 =====")
                RawDataUploadService.shared.checkAutoUploadStatus()
                
                // 强制启动自动上传服务
                if WindRingDeviceService.shared.connectionState.isConnected {
                    print("===== 应用启动 - 检测到设备已连接，强制启动自动上传 =====")
                    RawDataUploadService.shared.startAutoUpload()
                } else {
                    print("===== 应用启动 - 设备未连接，等待设备连接后自动上传 =====")
                }
            }
        }
    }
    
    // MARK: - Core Data stack
    
    // 使用StorageManager的持久化容器
//    var persistentContainer: NSPersistentContainer {
//        return StorageManager.shared.persistentContainerPublic
//    }
    
    // MARK: - Core Data Saving support
    
    func saveContext() {
        // 直接调用StorageManager的保存方法
        StorageManager.shared.saveViewContext()
    }
}
#endif 
