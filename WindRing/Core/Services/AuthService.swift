import Foundation
import Combine
import SwiftUI
import CoreData

/// 认证状态
public enum AuthState: Equatable {
    case unauthenticated
    case authenticating
    case authenticated
    case error(AuthError)
    
    public static func == (lhs: AuthState, rhs: AuthState) -> <PERSON><PERSON> {
        switch (lhs, rhs) {
        case (.unauthenticated, .unauthenticated):
            return true
        case (.authenticating, .authenticating):
            return true
        case (.authenticated, .authenticated):
            return true
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}

/// 认证错误
public enum AuthError: Error, Equatable {
    case invalidCredentials
    case networkError
    case accountNotFound
    case emailAlreadyExists
    case weakPassword
    case serverError
    case unknownError
    case invalidEmail
    case accountAlreadyExists
    case businessError(String)
    case notAuthenticated
    
    public static func == (lhs: AuthError, rhs: AuthError) -> <PERSON><PERSON> {
        switch (lhs, rhs) {
        case (.invalidCredentials, .invalidCredentials),
             (.networkError, .networkError),
             (.accountNotFound, .accountNotFound),
             (.emailAlreadyExists, .emailAlreadyExists),
             (.weakPassword, .weakPassword),
             (.serverError, .serverError),
             (.unknownError, .unknownError),
             (.invalidEmail, .invalidEmail),
             (.accountAlreadyExists, .accountAlreadyExists),
             (.notAuthenticated, .notAuthenticated):
            return true
        case (.businessError(let lhsMessage), .businessError(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
    
    var localizedDescription: String {
        switch self {
        case .invalidCredentials:
            return "登录凭证无效，请检查邮箱和密码"
        case .networkError:
            return "网络连接错误，请检查网络连接"
        case .accountNotFound:
            return "未找到该账号"
        case .emailAlreadyExists:
            return "该邮箱已被注册"
        case .weakPassword:
            return "密码强度不足，请使用更复杂的密码"
        case .serverError:
            return "服务器错误，请稍后再试"
        case .unknownError:
            return "发生未知错误"
        case .invalidEmail:
            return "无效的邮箱地址"
        case .accountAlreadyExists:
            return "该账号已被使用"
        case .businessError(let message):
            return message
        case .notAuthenticated:
            return "用户未认证"
        }
    }
}

public class AuthService: ObservableObject {
    static let shared = AuthService()
    private let apiService: APIService
    /// 当前登录用户的 Token（可观察）
    @Published var currentToken: TokenModel?
    
    @Published var currentUser: UserInfo?

    private var cancellables = Set<AnyCancellable>()

    private init() {
        self.apiService = APIService.shared
        self.loadTokenFromKeychain()
    }

    /// 用于刷新当前 Token
    func refreshSession(completion: @escaping (Result<SessionInfoModel, Error>) -> Void) {
        guard let oldToken = currentToken,oldToken.refreshToken.count != 0 else {
            self.logout { bool in
                
            }
            completion(.failure(NSError(domain: "No token", code: 401)))
            return
        }

        let parameters: [String: Any] = [
            "refreshToken": oldToken.refreshToken
        ]

        NetworkManager.shared.request(
            endpoint: "/app-api/member/auth/refresh-token?refreshToken=\(oldToken.refreshToken)",
            method: .get,
//            parameters: parameters,
//            headers: ["tenant-id": "1"],
            responseType: TokenModel.self
        )
        .sink(receiveCompletion: { completionStatus in
            if case .failure(let error) = completionStatus {
                completion(.failure(error))
            }
        }, receiveValue: { [weak self] newToken in
            guard let self = self else { return }

//            let user = UserModel(userId: newToken.userId, openid: newToken.openid)
            let session = SessionInfoModel(token: newToken, user: currentUser)

            // ✅ 更新 token 并保存
            self.currentToken = newToken
            self.saveSessionToKeychain(session)

            completion(.success(session))
        })
        .store(in: &cancellables)
    }

    /// 手动登录成功后调用，用于设置 token + 保存
    func loginSucceeded(with session: SessionInfoModel) {
        self.currentToken = session.token
        saveSessionToKeychain(session)
    }

    /// 清除本地 Token
    func logout() {
        self.currentToken = nil
        self.currentUser = nil
        KeychainHelper.shared.delete(account: "sessionInfo")
    }
    /// 退出登录
    /// - Parameter completion: 完成回调
    public func logout(completion: ((Bool) -> Void)? = nil) {
        print("AuthService - 执行退出登录")
        
        // 清除认证状态
        logout()
        
        // 通知其他服务用户已退出
        NotificationCenter.default.post(name: Notification.Name("userDidLogout"), object: nil)
        
        // 调用WindRingUserService的logout方法确保全局状态一致
        WindRingUserService.shared.logout()
        
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: NSNotification.Name("LoginStateChanged"), object: nil)
        }
        
        // 执行回调
        completion?(true)
    }
    // MARK: - Keychain 存取

    private func saveSessionToKeychain(_ session: SessionInfoModel) {
        if let data = try? JSONEncoder().encode(session) {
            KeychainHelper.shared.save(data, account: "sessionInfo")
        }
    }

    private func loadTokenFromKeychain() {
        guard let data = KeychainHelper.shared.read(account: "sessionInfo"),
              let session = try? CleanJSONDecoder().decode(SessionInfoModel.self, from: data)
        else { return }

        self.currentToken = session.token
        self.currentUser = session.user
    }
    
    /// 注册新用户
    /// - Parameters:
    ///   - email: 邮箱
    ///   - password: 密码
    ///   - name: 姓名
    ///   - completion: 完成回调
    public func register(email: String, password: String, name: String, completion: ((Result<TokenModel, AuthError>) -> Void)? = nil) {
        
        // 调用API服务注册
        apiService.user.register(email: email, password: password, name: name)
            .sink(receiveCompletion: { [weak self] completionStatus in
                guard let self = self else { return }
                
                switch completionStatus {
                case .failure(let error):
                    // 处理注册失败
//                    self.authState = .unauthenticated
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion?(.failure(authError))
                case .finished:
                    break
                }
            }, receiveValue: { [weak self] userResponse in
                guard let self = self else { return }
                self.currentToken = userResponse
                let session = SessionInfoModel(token: userResponse, user: currentUser)
                self.saveSessionToKeychain(session)
                self.getUserInfo { result in
                    switch result {
                    case .success(_):
                        completion?(.success(userResponse))
                    case .failure(let error):
                        // 即使获取用户信息失败，也认为更新成功
                        print("AuthService - 更新后获取用户信息失败: \(error.localizedDescription)")
                        completion?(.success(userResponse))
                    }
                }
            })
            .store(in: &cancellables)
    }
    /// 获取用户信息
    /// - Parameter completion: 完成回调
    public func getUserInfo(completion: @escaping (Result<UserInfo, AuthError>) -> Void) {
        print("AuthService - 开始获取用户信息流程")
        
        // 检查是否有token
        guard let token = self.currentToken else {
            print("AuthService - 获取用户信息失败 - 未登录")
            completion(.failure(.invalidCredentials))
            return
        }
        
        // 调用API获取用户信息
        apiService.user.getUserInfo(token: token.accessToken)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completionStatus in
                guard let self = self else { return }
                
                switch completionStatus {
                case .failure(let error):
                    print("AuthService - 获取用户信息API调用失败 - 错误: \(error.localizedDescription)")
                    
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion(.failure(authError))
                case .finished:
                    print("AuthService - API流结束")
                    break
                }
            } receiveValue: { [weak self] response in
                guard let self = self else { return }
                self.currentUser = response
                let session = SessionInfoModel(token: token, user: response)
                self.saveSessionToKeychain(session)
                completion(.success(response))
                
//                completion(response)
            }
            .store(in: &cancellables)
    }
    
    /// 用户登录
    /// - Parameters:
    ///   - email: 邮箱或手机号
    ///   - password: 密码
    ///   - completion: 完成回调
    public func login(email: String, password: String, completion: ((Result<TokenModel, AuthError>) -> Void)? = nil) {
        print("AuthService - 开始登录流程 - 用户名: \(email)")

        // 调用API进行登录
        apiService.user.login(email: email, password: password)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] completionStatus in
                guard let self = self else { return }
                
                switch completionStatus {
                case .failure(let error):
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion?(.failure(authError))
                case .finished:
                    break
                }
            }, receiveValue: { [weak self] userAuth in
                guard let self = self else { return }
                self.currentToken = userAuth
                let session = SessionInfoModel(token: userAuth, user: currentUser)
                self.saveSessionToKeychain(session)
                self.getUserInfo { result in
                    switch result {
                    case .success(_):
                        completion?(.success(userAuth))
                    case .failure(let error):
                        // 即使获取用户信息失败，也认为更新成功
                        print("AuthService - 更新后获取用户信息失败: \(error.localizedDescription)")
                        completion?(.success(userAuth))
                    }
                }
            })
            .store(in: &cancellables)
    }
    
    public func login(mobile: String, code: String, completion: ((Result<TokenModel, AuthError>) -> Void)? = nil) {
        print("AuthService - 开始登录流程 - 手机号: \(mobile)")
        
        
        // 调用API进行登录
        apiService.user.smslogin(mobile: mobile, code: code)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] completionStatus in
                guard let self = self else { return }
                
                switch completionStatus {
                case .failure(let error):
                    // 处理登录失败
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion?(.failure(authError))
                case .finished:
                    break
                }
            }, receiveValue: { [weak self] userAuth in
                guard let self = self else { return }
                self.currentToken = userAuth
                let session = SessionInfoModel(token: userAuth, user: currentUser)
                self.saveSessionToKeychain(session)
                self.getUserInfo { result in
                    switch result {
                    case .success(_):
                        completion?(.success(userAuth))
                    case .failure(let error):
                        // 即使获取用户信息失败，也认为更新成功
                        print("AuthService - 更新后获取用户信息失败: \(error.localizedDescription)")
                        completion?(.success(userAuth))
                    }
                }
                
            })
            .store(in: &cancellables)
    }
    
    
    /// 使用手机号码和密码登录
    /// - Parameters:
    ///   - mobile: 手机号码
    ///   - password: 密码
    ///   - completion: 完成回调
    public func loginWithMobile(mobile: String, password: String, completion: ((Result<TokenModel, AuthError>) -> Void)? = nil) {
        print("AuthService - 开始手机登录流程 - 手机号: \(mobile)")
        
        // 调用API服务登录
        apiService.user.loginWithMobile(mobile: mobile, password: password)
            .receive(on: DispatchQueue.main)
            .sink { completionStatus in
                switch completionStatus {
                case .failure(let error):
                    // 处理登录失败
                    print("AuthService - 登录API调用失败 - 错误: \(error.localizedDescription)")
                    
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion?(.failure(authError))
                case .finished:
                    print("AuthService - API流结束")
                    break
                }
            } receiveValue: { [weak self] loginData in
                guard let self = self else { return }
                
                print("AuthService - 接收到登录数据 - userId: \(loginData.userId)")
                
                // 检查用户是否存在
                self.currentToken = loginData
                let session = SessionInfoModel(token: loginData, user: currentUser)
                self.saveSessionToKeychain(session)
                self.getUserInfo { result in
                    switch result {
                    case .success(_):
                        completion?(.success(loginData))
                    case .failure(let error):
                        // 即使获取用户信息失败，也认为更新成功
                        print("AuthService - 更新后获取用户信息失败: \(error.localizedDescription)")
                        completion?(.success(loginData))
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 注册相关方法
    
    /// 用户注册
    /// - Parameters:
    ///   - mobile: 手机号
    ///   - password: 密码
    ///   - confirmPassword: 确认密码
    ///   - code: 验证码
    ///   - completion: 完成回调
    public func register(mobile: String, password: String, confirmPassword: String, code: String, completion: @escaping (Result<Void, AuthError>) -> Void) {
        
        // 判断是手机号还是邮箱
        let isEmail = mobile.contains("@")
        let accountName = isEmail ? mobile.split(separator: "@").first.map(String.init) ?? mobile : mobile
        
        // 调用API进行注册
        let apiCall = apiService.user.registerApp(
            account: mobile,
            password: password,
            confirmPassword: confirmPassword,
            code: code
        )
        
        // 订阅并处理响应
        apiCall
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] result in
                guard let self = self else { return }
                
                switch result {
                case .failure(let error):
                    print("注册失败: \(error.localizedDescription)")
                    
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion(.failure(authError))
                    
                case .finished:
                    break
                }
            }, receiveValue: { [weak self] response in
                guard let self = self else { return }
                
                // 检查响应状态码
                if response.code == 0 {
                    print("注册成功: \(response)")
                    
                    // If registration returns a token, log the user in.
                    if let token = response.data {
                        self.currentToken = token
                        let session = SessionInfoModel(token: token, user: self.currentUser)
                        self.saveSessionToKeychain(session)
                        
                        // After saving the new token, get user info to complete the session
                        self.getUserInfo { result in
                            switch result {
                            case .success:
                                print("AuthService - 注册后获取用户信息成功")
                                completion(.success(()))
                            case .failure(let error):
                                // Even if getting user info fails, registration was successful.
                                print("AuthService - 注册后获取用户信息失败: \(error.localizedDescription)")
                                completion(.success(()))
                            }
                        }
                    } else {
                        // Registration was successful (code: 0) but no token was returned.
                        // This might be a server-side logic (e.g., must login after register).
                        // For now, we'll treat it as a success for the completion handler.
                        print("AuthService - 注册成功但未返回token。")
                        completion(.success(()))
                    }
                } else {
                    // 处理业务错误
                    print("注册业务错误: \(response.code), \(response.msg ?? "未知错误")")
                    
                    // 检查特定的业务错误码
                    switch response.code {
                    case *********: // 账号已被使用
                        completion(.failure(.accountAlreadyExists))
                    default:
                        // 其他业务错误，提供错误消息
                        if let errorMsg = response.msg {
                            completion(.failure(.businessError(errorMsg)))
                        } else {
                            completion(.failure(.unknownError))
                        }
                    }
                }
            })
            .store(in: &cancellables)
    }
    
    /// 获取验证码
    /// - Parameters:
    ///   - mobile: 手机号
    ///   - completion: 完成回调
    public func getVerificationCode(mobile: String, completion: @escaping (Result<Bool, AuthError>) -> Void) {
        // 检查是否是邮箱
        let isEmail = mobile.contains("@")
        
        // 调用API获取验证码
        let apiCall = apiService.user.getVerificationCode(mobile: mobile)
        
        // 订阅并处理响应
        apiCall
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { result in
                switch result {
                case .failure(let error):
                    print("获取验证码失败: \(error.localizedDescription)")
                    
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion(.failure(authError))
                    
                case .finished:
                    break
                }
            }, receiveValue: { response in
                // 检查响应状态码
                if response.code == 0 {
                    print("验证码发送成功")
                    completion(.success(true))
                } else {
                    // 处理业务错误
                    print("验证码发送失败: \(response.code), \(response.msg ?? "未知错误")")
                    completion(.failure(.businessError(response.msg ?? "Unknown error")))
                }
            })
            .store(in: &cancellables)
    }
    
    /// 获取邮箱验证码
    /// - Parameters:
    ///   - email: 邮箱地址
    ///   - completion: 完成回调
    public func getEmailVerificationCode(email: String, scene: EmailSceneEnum, completion: @escaping (Result<Bool, AuthError>) -> Void) {
        // 使用相同的接口，只是传入参数为邮箱
//        getVerificationCode(mobile: email, completion: completion)
        apiService.user.sendEmailCode(email: email, scene: scene)
            .receive(on: DispatchQueue.main)
            .sink { completionStatus in
                switch completionStatus {
                case .failure(let error):
                    print("AuthService - 发送邮件验证码失败 - 错误: \(error.localizedDescription)")
                    completion(.failure(.businessError(error.localizedDescription)))
                case .finished:
                    print("AuthService - API流结束")
                    break
                }
            } receiveValue: { response in
                print("AuthService - 邮件验证码发送成功")
                completion(.success(true))
            }
            .store(in: &cancellables)
    }
    
    
    
    /// 发送短信验证码
    /// - Parameters:
    ///   - mobile: 手机号
    ///   - completion: 完成回调
    public func sendSMSCode(mobile: String,scene:Int,countryCode:String, completion: @escaping (Result<Bool, AuthError>) -> Void) {
        print("AuthService - 开始发送短信验证码 - 手机号: \(mobile)")
        
        // 调用API发送短信验证码
        apiService.user.sendSMSCode(mobile: mobile, scene: scene, countryCode: countryCode)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completionStatus in
                guard let self = self else { return }
                
                switch completionStatus {
                case .failure(let error):
                    print("AuthService - 发送短信验证码失败 - 错误: \(error.localizedDescription)")
                    
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion(.failure(authError))
                case .finished:
                    print("AuthService - API流结束")
                    break
                }
            } receiveValue: { response in
                print("AuthService - 短信验证码发送成功")
                completion(.success(true))
            }
            .store(in: &cancellables)
    }
    
    /// 使用短信验证码登录
    /// - Parameters:
    ///   - mobile: 手机号
    ///   - code: 验证码
    ///   - completion: 完成回调
    public func loginWithSMSCode(mobile: String, code: String, completion: @escaping (Result<TokenModel, AuthError>) -> Void) {
        print("AuthService - 开始短信验证码登录流程 - 手机号: \(mobile)")
        
        // 更新认证状态
        // 调用API进行短信验证码登录
        apiService.user.loginWithSMSCode(mobile: mobile, code: code)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completionStatus in
                guard let self = self else { return }
                
                switch completionStatus {
                case .failure(let error):
                    // 处理登录失败
                    print("AuthService - 短信登录API调用失败 - 错误: \(error.localizedDescription)")
                    
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion(.failure(authError))
                case .finished:
                    print("AuthService - API流结束")
                    break
                }
            } receiveValue: { [weak self] loginData in
                guard let self = self else { return }
                
                print("AuthService - 接收到登录数据 - userId: \(loginData.userId)")
                
                self.currentToken = loginData
                let session = SessionInfoModel(token: loginData, user: currentUser)
                self.saveSessionToKeychain(session)
                // 更新成功后重新获取用户信息
                self.getUserInfo { result in
                    switch result {
                    case .success(_):
                        completion(.success(loginData))
                    case .failure(let error):
                        // 即使获取用户信息失败，也认为更新成功
                        print("AuthService - 更新后获取用户信息失败: \(error.localizedDescription)")
                        completion(.success(loginData))
                    }
                }
                
            }
            .store(in: &cancellables)
    }
    
    /// 上传用户头像
    func uploadAvatar(image: UIKit.UIImage, completion: @escaping (Result<[String], Error>) -> Void) {
        // 确保用户已登录
//        guard case .authenticated = authState else {
//            completion(.failure(AuthError.notAuthenticated))
//            return
//        }
        
        // 调用API上传头像
        APIService.shared.uploadAvatar(image: image) { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(let avatarUrl):
                print("头像上传成功，URL: \(avatarUrl)")
                completion(.success(avatarUrl))
                // 更新用户头像URL
//                if var user = self.currentUser {
//                    user.avatar = avatarUrl.first ?? ""
//                    self.currentUser = user
//                    
//                    // 保存到数据库
////                    self.saveUserToDatabase(user)
//                    
//                    // 获取最新的用户信息
//                    self.getUserInfo { _ in
//                        completion(.success(true))
//                    }
//                } else {
//                    completion(.success(true))
//                }
                
            case .failure(let error):
                print("头像上传失败: \(error.localizedDescription)")
                completion(.failure(error))
            }
        }
    }
    /// 更新用户信息
    /// - Parameters:
    ///   - userInfo: 用户信息
    ///   - completion: 完成回调
    public func updateUserInfo(userInfo: UpdateUserInfoRequest, completion: @escaping (Result<Bool, AuthError>) -> Void) {
        print("AuthService - 开始更新用户信息流程")
        
        // 检查是否有token
        guard let token = self.currentToken?.accessToken else {
            print("AuthService - 更新用户信息失败 - 未登录")
            completion(.failure(.invalidCredentials))
            return
        }
        
        // 调用API更新用户信息
        apiService.user.updateUserInfo(token: token, userInfo: userInfo)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completionStatus in
                guard let self = self else { return }
                
                switch completionStatus {
                case .failure(let error):
                    print("AuthService - 更新用户信息API调用失败 - 错误: \(error.localizedDescription)")
                    
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion(.failure(authError))
                case .finished:
                    print("AuthService - API流结束")
                    break
                }
            } receiveValue: { [weak self] response in
                guard let self = self else { return }
                
                print("AuthService - 更新用户信息成功")
                
                // 更新成功后重新获取用户信息
                self.getUserInfo { result in
                    switch result {
                    case .success(_):
                        completion(.success(true))
                    case .failure(let error):
                        // 即使获取用户信息失败，也认为更新成功
                        print("AuthService - 更新后获取用户信息失败: \(error.localizedDescription)")
                        completion(.success(true))
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    /// 校验手机验证码
    public func validateSmsCode(mobile: String, scene: SmsSceneEnum, code: String, completion: @escaping (Result<Bool, AuthError>) -> Void) {
        apiService.user.validateSmsCode(mobile: mobile, scene: scene, code: code)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { result in
                if case .failure(let error) = result {
                    completion(.failure(.businessError(error.localizedDescription)))
                }
            }, receiveValue: { response in
                if response.code == 0 {
                    completion(.success(true))
                } else {
                    completion(.failure(.businessError(response.msg ?? "Unknown validation error")))
                }
            })
            .store(in: &cancellables)
    }

    /// 校验邮箱验证码
    public func validateEmailCode(email: String, scene: EmailSceneEnum, code: String, completion: @escaping (Result<Bool, AuthError>) -> Void) {
        apiService.user.validateEmailCode(email: email, scene: scene, code: code)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { result in
                if case .failure(let error) = result {
                    completion(.failure(.businessError(error.localizedDescription)))
//                    completion(.failure(self.mapNetworkErrorToAuthError(error)))
                }
            }, receiveValue: { response in
                if response.code == 0 {
                    completion(.success(true))
                } else {
                    completion(.failure(.businessError(response.msg ?? "Unknown validation error")))
                }
            })
            .store(in: &cancellables)
    }
    
    /// 用户注册 (带个人信息)
    public func registerWithProfile(
        account: String, password: String, confirmPassword: String, code: String,
        nickname: String, avatar: String, sex: Int, birthday: String,
        height: Int, heightType: Int, weight: Int, weightType: Int,
        completion: @escaping (Result<Void, AuthError>) -> Void
    ) {
        let request = RegisterWithProfileRequest(
            account: account,
            password: password,
            confirmPassword: confirmPassword,
            code: code,
            nickname: nickname,
            avatar: avatar,
            sex: sex,
            birthday: birthday,
            height: height,
            heightType: heightType,
            weight: weight,
            weightType: weightType
        )

        apiService.user.registerWithProfile(request: request)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] result in
                guard let self = self else { return }
                
                switch result {
                case .failure(let error):
                    print("注册失败: \(error.localizedDescription)")
                    
                    let authError = AuthError.businessError(error.localizedDescription)
                    completion(.failure(authError))
                    
                case .finished:
                    break
                }
            }, receiveValue: { [weak self] response in
                guard let self = self else { return }
                
                // 检查响应状态码
                if response.code == 0 {
                    print("注册成功: \(response)")
                    completion(.success(()))
                } else {
                    // 处理业务错误
                    print("注册业务错误: \(response.code), \(response.msg ?? "未知错误")")
                    
                    // 检查特定的业务错误码
                    switch response.code {
                    case *********: // 账号已被使用
                        completion(.failure(.accountAlreadyExists))
                    default:
                        // 其他业务错误，提供错误消息
                        if let errorMsg = response.msg {
                            completion(.failure(.businessError(errorMsg)))
                        } else {
                            completion(.failure(.unknownError))
                        }
                    }
                }
            })
            .store(in: &cancellables)
    }

    public func resetPassword(account: String, code: String, password: String, confirmPassword: String, completion: @escaping (Result<Bool, AuthError>) -> Void) {
        apiService.user.resetPasswordWithCode(account: account, code: code, password: password, confirmPassword: confirmPassword)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { result in
                if case .failure(let error) = result {
                    completion(.failure(.businessError(error.localizedDescription)))
                }
            }, receiveValue: { response in
//                if response.code == 0 {
                    completion(.success(true))
//                } else {
//                    completion(.failure(.businessError(response.msg ?? "Unknown error")))
//                }
            })
            .store(in: &cancellables)
    }
}

// MARK: - 辅助扩展

/// AuthService SwiftUI 环境键
private struct AuthServiceKey: EnvironmentKey {
    static let defaultValue = AuthService.shared
}

extension EnvironmentValues {
    /// AuthService 环境值
    public var authService: AuthService {
        get { self[AuthServiceKey.self] }
        set { self[AuthServiceKey.self] = newValue }
    }
}

/// 视图扩展
extension View {
    /// 添加认证服务
    /// - Returns: 添加了认证服务的视图
    public func withAuthService() -> some View {
        self.environmentObject(AuthService.shared)
    }
} 
