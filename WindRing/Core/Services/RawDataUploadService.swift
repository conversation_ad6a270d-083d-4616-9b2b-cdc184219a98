import Foundation
import Combine
#if os(iOS)
//import CRPSmartRing
#endif

/// 原始数据上传服务
/// 负责收集并上传戒指设备的原始数据到服务器
public class RawDataUploadService: ObservableObject {
    // MARK: - 单例
    public static let shared = RawDataUploadService()
    
    // MARK: - 属性
    private let deviceService: WindRingDeviceService
    private let authService: AuthService
    private let networkMonitor = NetworkMonitor.shared
    
    // 上传状态
    @Published public var isUploading: Bool = false
    @Published public var uploadProgress: Double = 0.0
    @Published public var lastUploadTime: Date? = nil
    @Published public var isAutoUploadEnabled: Bool = false  // 是否启用自动上传
    @Published public var lastActivityUploadTime: Date? = nil  // 上次活动数据上传时间
    @Published public var lastHeartRateUploadTime: Date? = nil  // 上次心率数据上传时间
    @Published public var lastHRVUploadTime: Date? = nil  // 上次HRV数据上传时间
    @Published public var lastBloodOxygenUploadTime: Date? = nil  // 上次血氧数据上传时间
    @Published public var lastStressUploadTime: Date? = nil  // 上次压力数据上传时间
    @Published public var lastSleepUploadTime: Date? = nil  // 上次睡眠数据上传时间
    
    // 上传配置
    private let baseURL: String = "http://ring-api-dev.weaving-park.com"
    private let batchSize: Int = 50 // 每批上传的数据量
    
    // 取消令牌
    private var cancellables = Set<AnyCancellable>()
    private var autoUploadTimer: Timer? // 自动上传计时器
    
    // MARK: - 初始化
    private init() {
        self.deviceService = WindRingDeviceService.shared
        self.authService = AuthService.shared
        
        // 从 UserDefaults 读取自动上传状态
        self.isAutoUploadEnabled = UserDefaults.standard.bool(forKey: "auto_upload_enabled")
        
        // 监听设备连接状态变化
//        deviceService.$connectionState
//            .sink { [weak self] state in
//                if state.isConnected {
//                    print("RawDataUploadService: 设备connected".localized)
//                    // 发送设备连接通知
//                    NotificationCenter.default.post(name: NSNotification.Name("DeviceConnectedNotification"), object: nil)
//                    
//                    // 检查自动上传是否开启
//                    let shouldAutoUpload = self?.isAutoUploadEnabled == true || UserDefaults.standard.bool(forKey: "auto_upload_enabled")
//                    
//                    // 确保自动上传标志与UserDefaults设置一致
//                    if shouldAutoUpload {
//                        print("📲 设备已连接，启动数据自动上传服务...")
//                        
//                        // 设置状态标记
//                        self?.isAutoUploadEnabled = true
//                        UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
//                        
//                        // 强制启动自动上传服务，不进行额外的连接检查，因为我们已知设备已连接
//                        DispatchQueue.main.async {
//                            self?.forceStartUpload(interval: 300)
//                        }
//                    }
//                } else {
//                    print("RawDataUploadService: 设备已断开连接")
//                    // 只停止上传任务，但不更改自动上传设置
//                    // 这样当设备重新连接时会自动恢复上传
//                    if self?.isUploading == true {
//                        self?.isUploading = false
//                        self?.uploadProgress = 0.0
//                        self?.autoUploadTimer?.invalidate()
//                        self?.autoUploadTimer = nil
//                        print("RawDataUploadService: 因设备断开连接，暂停自动上传任务")
//                    }
//                }
//            }
//            .store(in: &cancellables)
        
        // 如果自动上传设置为开启，则尝试启动自动上传任务
        // 注意：实际上传会在设备连接后才会进行
        if self.isAutoUploadEnabled || UserDefaults.standard.bool(forKey: "auto_upload_enabled") {
            print("RawDataUploadService: 初始化时检测到自动上传设置为开启，将在设备连接后自动启动上传任务")
            // 确保设置一致
            isAutoUploadEnabled = true
            UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
        }
    }
    
    // MARK: - 公共方法
    
    /// 启动自动上传任务
    /// - Parameter interval: 上传间隔（秒），默认为300秒（5分钟）
    /// - Returns: 是否成功启动
    @discardableResult
    public func startAutoUpload(interval: TimeInterval = 300) -> Bool {
        guard !isUploading else {
            print("RawDataUploadService: 已经有上传任务在进行中")
            return false
        }
        
        // 设置自动上传标志，无论设备是否连接
        isAutoUploadEnabled = true
        // 保存自动上传状态到 UserDefaults
        UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
        
        // 检查设备连接状态，但仍然继续设置上传任务
        // 实际上传将在设备连接后执行
        if !deviceService.connectionState.isConnected {
            print("RawDataUploadService: 设备未连接，已设置自动上传标志，将在设备连接后自动启动上传")
            // 停止现有计时器并进行状态检查
            autoUploadTimer?.invalidate()
            checkAutoUploadStatus()
            return true
        }
        
        // 检查用户登录状态
        if !(authService.currentToken == nil) {
            print("RawDataUploadService: 用户未登录，已设置自动上传标志，将在用户登录后自动启动上传")
            // 停止现有计时器并进行状态检查
            autoUploadTimer?.invalidate()
            checkAutoUploadStatus()
            return true
        }
        
        print("🔄 正在启动数据自动上传服务...")
        
        // 停止现有计时器
        autoUploadTimer?.invalidate()
        
        // 检查自动上传状态
        checkAutoUploadStatus()
        
        // 首次立即执行一次上传
        uploadRawData { success, _ in
            if success {
                print("RawDataUploadService: 首次上传成功")
            } else {
                print("RawDataUploadService: 首次上传失败")
            }
            
            // 首次上传完成后，分别上传活动数据、心率数据、HRV数据、血氧数据、压力数据和睡眠数据
            self.uploadActivityDataFor7Days()
            
            // 等待一段时间后上传心率数据，避免同时大量请求
            DispatchQueue.global().asyncAfter(deadline: .now() + 3) {
                self.uploadHeartRateDataFor7Days()
            }
            
            // 等待一段时间后上传HRV数据，避免同时大量请求
            DispatchQueue.global().asyncAfter(deadline: .now() + 6) {
                self.uploadHRVDataFor7Days()
            }
            
            // 等待一段时间后上传血氧数据，避免同时大量请求
            DispatchQueue.global().asyncAfter(deadline: .now() + 9) {
                self.uploadBloodOxygenDataFor7Days()
            }
            
            // 等待一段时间后上传压力数据，避免同时大量请求
            DispatchQueue.global().asyncAfter(deadline: .now() + 12) {
                self.uploadStressDataFor7Days()
            }
            
            // 注释掉睡眠数据自动上传
            // 等待一段时间后上传睡眠数据，避免同时大量请求
            // DispatchQueue.global().asyncAfter(deadline: .now() + 15) {
            //     self.uploadSleepDataFor7Days()
            // }
        }
        
        // 设置定时任务
        autoUploadTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            guard let self = self, self.isAutoUploadEnabled else { return }
            
            // 上传原始数据
            self.uploadRawData { success, _ in
                if success {
                    print("RawDataUploadService: 定时上传成功")
                } else {
                    print("RawDataUploadService: 定时上传失败")
                }
                
                // 分别上传活动数据、心率数据、HRV数据、血氧数据、压力数据和睡眠数据
                self.uploadActivityDataFor7Days()
                
                // 等待一段时间后上传心率数据，避免同时大量请求
                DispatchQueue.global().asyncAfter(deadline: .now() + 3) {
                    self.uploadHeartRateDataFor7Days()
                }
                
                // 等待一段时间后上传HRV数据，避免同时大量请求
                DispatchQueue.global().asyncAfter(deadline: .now() + 6) {
                    self.uploadHRVDataFor7Days()
                }
                
                // 等待一段时间后上传血氧数据，避免同时大量请求
                DispatchQueue.global().asyncAfter(deadline: .now() + 9) {
                    self.uploadBloodOxygenDataFor7Days()
                }
                
                // 等待一段时间后上传压力数据，避免同时大量请求
                DispatchQueue.global().asyncAfter(deadline: .now() + 12) {
                    self.uploadStressDataFor7Days()
                }
                
                // 注释掉睡眠数据自动上传
                // 等待一段时间后上传睡眠数据，避免同时大量请求
                // DispatchQueue.global().asyncAfter(deadline: .now() + 15) {
                //     self.uploadSleepDataFor7Days()
                // }
            }
        }
        
        return true
    }
    
    /// 停止自动上传任务
    public func stopAutoUpload() {
        guard isAutoUploadEnabled else { return }
        
        print("RawDataUploadService: 停止自动上传任务")
        isAutoUploadEnabled = false
        isUploading = false
        uploadProgress = 0.0
        
        // 保存自动上传状态到 UserDefaults
        UserDefaults.standard.set(false, forKey: "auto_upload_enabled")
        
        // 停止计时器
        autoUploadTimer?.invalidate()
        autoUploadTimer = nil
        
        // 检查自动上传状态
        checkAutoUploadStatus()
    }
    
    /// 检查自动上传状态
    public func checkAutoUploadStatus() {
        print("===== 自动上传状态检查 =====")
        print("自动上传已启用: \(isAutoUploadEnabled)")
        print("上次上传时间: \(lastUploadTime?.description ?? "未上传")")
        print("上次活动数据上传时间: \(lastActivityUploadTime?.description ?? "未上传")")
        print("上次心率数据上传时间: \(lastHeartRateUploadTime?.description ?? "未上传")")
        print("上次HRV数据上传时间: \(lastHRVUploadTime?.description ?? "未上传")")
        print("上次血氧数据上传时间: \(lastBloodOxygenUploadTime?.description ?? "未上传")")
        print("上次压力数据上传时间: \(lastStressUploadTime?.description ?? "未上传")")
        print("上次睡眠数据上传时间: \(lastSleepUploadTime?.description ?? "未上传")")
        print("自动上传设置(UserDefaults): \(UserDefaults.standard.bool(forKey: "auto_upload_enabled"))")
        print("设备连接状态: \(deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized)")
        print("自动上传计时器: \(autoUploadTimer != nil ? "已激活" : "未激活")")
        print("==========================")
    }
    
    /// 手动触发一次原始数据上传
    /// - Parameter completion: 完成回调
    public func uploadRawData(completion: @escaping (Bool, Error?) -> Void) {
        guard !isUploading else {
            let error = NSError(domain: "RawDataUpload", code: 400, userInfo: [NSLocalizedDescriptionKey: "上传已在进行中"])
            completion(false, error)
            return
        }
        
        guard deviceService.connectionState.isConnected else {
            let error = NSError(domain: "RawDataUpload", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"])
            completion(false, error)
            return
        }
        
        guard (authService.currentToken == nil) else {
            let error = NSError(domain: "RawDataUpload", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])
            completion(false, error)
            return
        }
        
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "RawDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        isUploading = true
        uploadProgress = 0.0
        
        // 获取所有类型的原始数据
        fetchAllRawData { [weak self] rawData, error in
            guard let self = self else { return }
            
            if let error = error {
                print("RawDataUploadService: 获取原始数据失败: \(error.localizedDescription)")
                self.isUploading = false
                completion(false, error)
                return
            }
            
            guard let data = rawData, !data.isEmpty else {
                print("RawDataUploadService: 没有原始数据可上传")
                self.isUploading = false
                completion(true, nil) // 没有数据也视为成功
                return
            }
            
            // 上传原始数据到服务器
            self.uploadToServer(data: data) { success, error in
                self.isUploading = false
                self.uploadProgress = success ? 1.0 : 0.0
                
                if success {
                    self.lastUploadTime = Date()
                    print("RawDataUploadService: 原始数据上传成功，共\(data.count)条")
                    self.checkAutoUploadStatus()
                } else if let error = error {
                    print("RawDataUploadService: 原始数据上传失败: \(error.localizedDescription)")
                }
                
                completion(success, error)
            }
        }
    }
    
    /// 获取活动数据
    /// 仅获取当天数据，调用fetchHistoricalActivityData(days: 1)
    private func fetchActivityData(completion: @escaping ([RawDataPacket]?, Error?) -> Void) {
        fetchHistoricalActivityData(days: 1, completion: completion)
    }
    
    /// 获取步数详细数据
    /// 用于获取一天内的步数明细数据
    /// - Parameters:
    ///   - day: 指定天数（0表示今天，1表示昨天，以此类推）
    ///   - completion: 完成回调
    public func fetchStepDetailData(day: Int, completion: @escaping ([ActivityDetailData]?, Error?) -> Void) {
        // 记录开始时间，用于跟踪数据获取流程
        let startTime = Date()
        let calendar = Calendar.current
        let targetDate = calendar.date(byAdding: .day, value: -day, to: Date()) ?? Date()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: targetDate)
        
        print("📅 开始获取 \(dateString) (day=\(day)) 的步数明细数据，开始时间: \(startTime)")
        
        #if os(iOS)
        print("获取第\(day)天的步数明细数据...目标日期: \(dateString)")
        
        // 检查设备连接状态
        guard WindRingDeviceService.shared.connectionState.isConnected else {
            let error = NSError(domain: "RawDataUpload", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接，无法获取步数明细数据"])
            print("❌ 设备未连接，无法获取步数明细数据")
            completion(nil, error)
            return
        }
        
        // 使用SDK获取半小时步数明细数据，确保day参数正确
//        CRPSmartRingManage.shared.getHalfHourlyStepStats(day: day) { record, error in
//            // 计算数据获取耗时
//            let processingTime = Date().timeIntervalSince(startTime)
//            print("⏱️ 步数明细数据SDK调用耗时: \(String(format: "%.2f", processingTime))秒")
//            
//            if error != .none {
//                print("❌ 获取第\(day)天(\(dateString))步数明细数据失败: \(error)")
//                let nsError = NSError(domain: "RawDataUpload", code: 500,
//                                     userInfo: [NSLocalizedDescriptionKey: "获取步数明细数据失败: \(error)"])
//                completion(nil, nsError)
//                return
//            }
//            // 验证返回的数据是否匹配请求的日期，通过检查record.day是否等于请求的day
//            if record.day != day {
//                print("⚠️ 警告: SDK返回的day值(\(record.day))与请求的day参数(\(day))不匹配！")
//            }
//            
//            // 获取对应日期
//            let startOfDay = calendar.startOfDay(for: targetDate)
//            
//            var stepDetails: [ActivityDetailData] = []
//            
//            // 处理半小时步数数据 (一天48个半小时)
//            // record包含该日期的半小时步数统计数据，每天48个值
//            print("📊 步数明细数据长度: \(record.steps.count)，非零数据点: \(record.steps.filter { $0 > 0 }.count)")
//            
//            if !record.steps.isEmpty {
//                for (index, stepCount) in record.steps.enumerated() {
//                    // 只添加有步数的记录
//                    if stepCount > 0 {
//                        // 计算该半小时对应的时间戳
//                        // 每个index代表30分钟，从0点开始
//                        let halfHourInSeconds = TimeInterval(index * 30 * 60)
//                        let timestamp = startOfDay.addingTimeInterval(halfHourInSeconds)
//                        let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
//                        
//                        // 创建步数明细记录
//                        let stepDetail = ActivityDetailData(steps: stepCount, time: timeMs)
//                        
//                        // 打印明细数据用于调试
//                        let timeFormatter = DateFormatter()
//                        timeFormatter.dateFormat = "HH:mm"
//                        print("  - 索引\(index): \(timeFormatter.string(from: timestamp)) 步数: \(stepCount)")
//                        
//                        stepDetails.append(stepDetail)
//                    }
//                }
//            } else {
//                // 如果没有步数数据或数组为空，返回空数组
//                print("⚠️ 步数数组为空")
//                completion([], nil)
//                return
//            }
//            
//            // 如果没有有效步数数据，返回空数组
//            if stepDetails.isEmpty {
//                print("⚠️ 没有有效的步数数据记录")
//                completion([], nil)
//                return
//            }
//            
//            // 按时间排序
//            let sortedStepDetails = stepDetails.sorted { $0.time < $1.time }
//            
//            // 返回结果
//            print("✅ 成功获取 \(dateString) (day=\(day)) 的步数明细数据，共\(sortedStepDetails.count)条记录")
//            completion(sortedStepDetails, nil)
//        }
        #else
        // 在非iOS平台上使用模拟数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            var stepDetails: [ActivityDetailData] = []
            
            // 获取对应日期
            let calendar = Calendar.current
            let targetDate = calendar.date(byAdding: .day, value: -day, to: Date()) ?? Date()
            let startOfDay = calendar.startOfDay(for: targetDate)
            
            print("📱 模拟器环境，生成\(dateString)的随机步数数据")
            
            // 创建模拟数据 - 针对每个日期创建不同的随机种子，确保不同日期有不同的数据
            let dateComponents = calendar.dateComponents([.year, .month, .day], from: targetDate)
            let dateSeed = (dateComponents.year ?? 2025) * 10000 + (dateComponents.month ?? 4) * 100 + (dateComponents.day ?? 1)
            print("🎲 日期种子: \(dateSeed)")
            
            var randomGenerator = SystemRandomNumberGenerator()
            // 使用日期作为随机种子
            srand48(dateSeed)
            
            for hour in 0..<24 {
                for halfHour in 0..<2 {
                    // 使用基于日期的随机数，确保相同日期生成相同数据，不同日期生成不同数据
                    let randomValue = drand48() // 0.0-1.0之间的随机数
                    let hourFactor = Double(hour) / 24.0 // 按小时变化因子
                    let steps = Int(randomValue * 300.0 * (1.0 + hourFactor)) // 随机步数，早上较少，晚上较多
                    
                    if steps > 0 {
                        let minutes = halfHour * 30
                        let timestamp = startOfDay.addingTimeInterval(TimeInterval(hour * 3600 + minutes * 60))
                        let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
                        
                        let stepDetail = ActivityDetailData(steps: steps, time: timeMs)
                        stepDetails.append(stepDetail)
                        
                        // 打印生成的数据
                        let timeFormatter = DateFormatter()
                        timeFormatter.dateFormat = "HH:mm"
                        print("  - \(timeFormatter.string(from: timestamp)) 步数: \(steps)")
                    }
                }
            }
            
            let sortedDetails = stepDetails.sorted { $0.time < $1.time }
            print("✅ 成功生成 \(dateString) (day=\(day)) 的模拟步数明细数据，共\(sortedDetails.count)条记录")
            
            completion(sortedDetails, nil)
        }
        #endif
    }
    
    /// 上传活动数据到服务器
    /// 根据后端API要求的格式上传活动数据和步数明细
    /// - Parameters:
    ///   - day: 指定天数（0表示今天，1表示昨天，以此类推）
    ///   - completion: 完成回调
    public func uploadActivityData(day: Int, completion: @escaping (Bool, Error?) -> Void) {
        print("开始上传第\(day)天的活动数据...")
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            let error = NSError(domain: "ActivityDataUpload", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接，无法获取活动数据"])
            completion(false, error)
            return
        }
        
        // 检查网络连接状态
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "ActivityDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        // 获取用户认证信息
        guard let token = authService.currentToken?.accessToken else {
            let error = NSError(domain: "ActivityDataUpload", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])
            completion(false, error)
            return
        }
        
        // 首先获取活动汇总数据
//        CRPSmartRingSDK.sharedInstance.getTrainingData(day) { [weak self] record, trainingError in
//            guard let self = self else { return }
//            
//            if trainingError != .none {
//                print("获取第\(day)天活动汇总数据失败: \(trainingError)")
//                let error = NSError(domain: "ActivityDataUpload", code: 501, 
//                                  userInfo: [NSLocalizedDescriptionKey: "获取活动汇总数据失败: \(trainingError)"])
//                completion(false, error)
//                return
//            }
//            
//            // 然后获取步数明细数据
//            self.fetchStepDetailData(day: day) { stepDetails, detailError in
//                if let detailError = detailError {
//                    print("获取第\(day)天步数明细数据失败: \(detailError.localizedDescription)")
//                    completion(false, detailError)
//                    return
//                }
//                
//                // 格式化日期（今天减去day天）
//                let dateFormatter = DateFormatter()
//                dateFormatter.dateFormat = "yyyy-MM-dd"
//                let calendar = Calendar.current
//                let targetDate = calendar.date(byAdding: .day, value: -day, to: Date()) ?? Date()
//                let dateString = dateFormatter.string(from: targetDate)
//                
//                // 构建API要求的JSON请求体
//                var requestBody: [String: Any] = [
//                    "date": dateString,
//                    "steps": record.step,                     // 步数
//                    "calories": record.cal,                   // 卡路里(千卡)
//                    "time": record.step / 120,         // 活动时长(秒)，SDK返回的是分钟，需转换为秒
//                    "distance": record.distance / 100         // 距离(米)，SDK返回的是厘米，需转换为米
//                ]
//                
//                // 添加步数明细记录
//                if let details = stepDetails, !details.isEmpty {
//                    let records = details.map { detail -> [String: Any] in
//                        return [
//                            "steps": detail.steps,
//                            "time": detail.time  // 毫秒时间戳
//                        ]
//                    }
//                    requestBody["records"] = records
//                } else {
//                    requestBody["records"] = []
//                }
//                
//                // 发送请求到服务器
//                self.sendActivityDataToServer(requestBody: requestBody, token: token, completion: completion)
//            }
//        }
    }
    
    /// 发送活动数据到服务器
    /// - Parameters:
    ///   - requestBody: 请求体数据
    ///   - token: 认证令牌
    ///   - completion: 完成回调
    private func sendActivityDataToServer(requestBody: [String: Any], token: String, completion: @escaping (Bool, Error?) -> Void) {
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "ActivityDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        // 构建URL
        guard let url = URL(string: "\(baseURL)/app-api/iot/activity/upload/data") else {
            let error = NSError(domain: "ActivityDataUpload", code: 405, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(false, error)
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 将请求体转换为JSON数据
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            request.httpBody = jsonData
            
            // 详细日志记录
            print("📤 发送活动数据到服务器，URL: \(url.absoluteString)")
            print("📤 数据大小: \(jsonData.count) 字节")
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("📦 活动数据内容摘要: \(String(jsonString.prefix(200)))...")
            }
            
            // 发送请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 记录HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("🌐 服务器响应状态码: \(httpResponse.statusCode)")
                }
                
                if let error = error {
                    print("❌ 网络请求错误: \(error.localizedDescription)")
                    completion(false, error)
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    let error = NSError(domain: "ActivityDataUpload", code: 406, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])
                    print("❌ 无效的HTTP响应")
                    completion(false, error)
                    return
                }
                
                // 处理响应
                if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                    print("✅ 活动数据上传成功: 状态码\(httpResponse.statusCode)")
                    
                    // 解析响应数据
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器响应内容: \(responseString)")
                    }
                    
                    // 更新上次活动数据上传时间
                    DispatchQueue.main.async {
                        self.lastActivityUploadTime = Date()
                    }
                    
                    completion(true, nil)
                } else {
                    var errorMessage = "服务器返回错误: \(httpResponse.statusCode)"
                    
                    // 尝试解析错误信息
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器错误响应: \(responseString)")
                        errorMessage += ", 响应: \(responseString)"
                    }
                    
                    print("❌ 活动数据上传失败: \(errorMessage)")
                    let error = NSError(domain: "ActivityDataUpload", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    completion(false, error)
                }
            }.resume()
        } catch {
            print("❌ JSON序列化错误: \(error.localizedDescription)")
            completion(false, error)
        }
    }
    
    /// 获取心率详细数据
    /// 用于获取一天内的心率明细数据
    /// - Parameters:
    ///   - day: 指定天数（0表示今天，1表示昨天，以此类推）
    ///   - completion: 完成回调
    public func fetchHeartRateDetailData(day: Int, completion: @escaping ([HeartRateData]?, Error?) -> Void) {
        // 记录开始时间，用于跟踪数据获取流程
        let startTime = Date()
        let calendar = Calendar.current
        let targetDate = calendar.date(byAdding: .day, value: -day, to: Date()) ?? Date()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: targetDate)
        
        print("📅 开始获取 \(dateString) (day=\(day)) 的心率明细数据，开始时间: \(startTime)")
        
        #if os(iOS)
        print("获取第\(day)天的心率明细数据...目标日期: \(dateString)")
        
        // 检查设备连接状态
        guard WindRingDeviceService.shared.connectionState.isConnected else {
            let error = NSError(domain: "RawDataUpload", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接，无法获取心率明细数据"])
            print("❌ 设备未连接，无法获取心率明细数据")
            completion(nil, error)
            return
        }
        
        // 使用SDK获取心率明细数据
//        CRPSmartRingSDK.sharedInstance.getTimingHeartRate(day) { record, error in
//            // 计算数据获取耗时
//            let processingTime = Date().timeIntervalSince(startTime)
//            print("⏱️ 心率明细数据SDK调用耗时: \(String(format: "%.2f", processingTime))秒")
//            
//            if error != .none {
//                print("❌ 获取第\(day)天(\(dateString))心率明细数据失败: \(error)")
//                let nsError = NSError(domain: "RawDataUpload", code: 500, 
//                                     userInfo: [NSLocalizedDescriptionKey: "获取心率明细数据失败: \(error)"])
//                completion(nil, nsError)
//                return
//            }
//            
//            // 获取对应日期
//            let startOfDay = calendar.startOfDay(for: targetDate)
//            
//            var heartRateDetails: [HeartRateData] = []
//            
//            // 处理心率数据 (一天288个五分钟数据)
//            print("📊 心率明细数据长度: \(record.hearts.count)，非零数据点: \(record.hearts.filter { $0 > 0 }.count)")
//            
//            if !record.hearts.isEmpty {
//                for (index, heartRate) in record.hearts.enumerated() {
//                    // 只添加有效的心率记录 (大于0且在合理范围内的值)
//                    if heartRate > 0 && heartRate < 250 {
//                        // 计算该5分钟对应的时间戳
//                        // 每个index代表5分钟，从0点开始
//                        let fiveMinutesInSeconds = TimeInterval(index * 5 * 60)
//                        let timestamp = startOfDay.addingTimeInterval(fiveMinutesInSeconds)
//                        let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
//                        
//                        // 创建心率明细记录
//                        let heartRateDetail = HeartRateData(hearts: heartRate, time: timeMs)
//                        
//                        // 打印明细数据用于调试
//                        let timeFormatter = DateFormatter()
//                        timeFormatter.dateFormat = "HH:mm"
//                        print("  - 索引\(index): \(timeFormatter.string(from: timestamp)) 心率: \(heartRate)")
//                        
//                        heartRateDetails.append(heartRateDetail)
//                    }
//                }
//            } else {
//                // 如果没有心率数据或数组为空，返回空数组
//                print("⚠️ 心率数组为空")
//                completion([], nil)
//                return
//            }
//            
//            // 如果没有有效心率数据，返回空数组
//            if heartRateDetails.isEmpty {
//                print("⚠️ 没有有效的心率数据记录")
//                completion([], nil)
//                return
//            }
//            
//            // 按时间排序
//            let sortedHeartRateDetails = heartRateDetails.sorted { $0.time < $1.time }
//            
//            // 返回结果
//            print("✅ 成功获取 \(dateString) (day=\(day)) 的心率明细数据，共\(sortedHeartRateDetails.count)条记录")
//            completion(sortedHeartRateDetails, nil)
//        }
        #else
        // 在非iOS平台上使用模拟数据
        DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) {
            // 模拟心率数据
            var mockHeartRates: [HeartRateData] = []
            
            // 生成24小时的模拟心率数据，每小时4条（每15分钟一条）
            for hour in 0..<24 {
                for quarterHour in 0..<4 {
                    // 计算时间
                    let minutes = quarterHour * 15
                    let date = calendar.date(bySettingHour: hour, minute: minutes, second: 0, of: targetDate)!
                    let timeMs = Int64(date.timeIntervalSince1970 * 1000)
                    
                    // 生成符合规律的模拟心率值
                    var heartRate = 0
                    
                    // 睡眠时间(0-6点)心率较低
                    if hour >= 0 && hour < 6 {
                        heartRate = Int.random(in: 50...65)
                    }
                    // 晨起锻炼时间(6-9点)心率稍高
                    else if hour >= 6 && hour < 9 {
                        heartRate = Int.random(in: 65...85)
                    }
                    // 日间工作时间(9-18点)心率正常
                    else if hour >= 9 && hour < 18 {
                        heartRate = Int.random(in: 70...80)
                    }
                    // 晚间活动时间(18-22点)心率稍高
                    else if hour >= 18 && hour < 22 {
                        heartRate = Int.random(in: 75...90)
                    }
                    // 睡前放松时间(22-24点)心率降低
                    else {
                        heartRate = Int.random(in: 60...75)
                    }
                    
                    // 添加随机波动
                    heartRate += Int.random(in: -5...5)
                    
                    // 创建心率数据
                    let heartRateData = HeartRateData(hearts: heartRate, time: timeMs)
                    mockHeartRates.append(heartRateData)
                }
            }
            
            print("✅ 成功生成模拟心率数据，共\(mockHeartRates.count)条记录")
            completion(mockHeartRates, nil)
        }
        #endif
    }
    
    /// 上传心率数据
    /// - Parameters:
    ///   - day: 指定天数（0表示今天，1表示昨天，以此类推）
    ///   - completion: 完成回调
    public func uploadHeartRateData(day: Int, completion: @escaping (Bool, Error?) -> Void) {
        print("开始上传第\(day)天的心率数据...")
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            let error = NSError(domain: "HeartRateDataUpload", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接，无法获取心率数据"])
            completion(false, error)
            return
        }
        
        // 检查网络连接状态
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "HeartRateDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        // 获取用户认证信息
        guard let token = authService.currentToken?.accessToken else {
            let error = NSError(domain: "HeartRateDataUpload", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])
            completion(false, error)
            return
        }
        
        // 获取心率明细数据
        fetchHeartRateDetailData(day: day) { heartRateDetails, detailError in
            if let detailError = detailError {
                print("获取第\(day)天心率明细数据失败: \(detailError.localizedDescription)")
                completion(false, detailError)
                return
            }
            
            // 格式化日期（今天减去day天）
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let calendar = Calendar.current
            let targetDate = calendar.date(byAdding: .day, value: -day, to: Date()) ?? Date()
            let dateString = dateFormatter.string(from: targetDate)
            
            // 构建API要求的JSON请求体
            let requestBody: [String: Any] = [
                "date": dateString,
                "records": (heartRateDetails ?? []).map { detail -> [String: Any] in
                    return [
                        "hearts": detail.hearts,
                        "time": detail.time  // 毫秒时间戳
                    ]
                }
            ]
            
            // 发送请求到服务器
            self.sendHeartRateDataToServer(requestBody: requestBody, token: token, completion: completion)
        }
    }
    
    /// 发送心率数据到服务器
    /// - Parameters:
    ///   - requestBody: 请求体数据
    ///   - token: 认证令牌
    ///   - completion: 完成回调
    private func sendHeartRateDataToServer(requestBody: [String: Any], token: String, completion: @escaping (Bool, Error?) -> Void) {
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "HeartRateDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        // 构建URL
        guard let url = URL(string: "\(baseURL)/app-api/iot/hr/upload/data") else {
            let error = NSError(domain: "HeartRateDataUpload", code: 405, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(false, error)
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 将请求体转换为JSON数据
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            request.httpBody = jsonData
            
            // 详细日志记录
            print("📤 发送心率数据到服务器，URL: \(url.absoluteString)")
            print("📤 数据大小: \(jsonData.count) 字节")
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("📦 心率数据内容摘要: \(String(jsonString.prefix(200)))...")
            }
            
            // 发送请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 记录HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("🌐 服务器响应状态码: \(httpResponse.statusCode)")
                }
                
                if let error = error {
                    print("❌ 网络请求错误: \(error.localizedDescription)")
                    completion(false, error)
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    let error = NSError(domain: "HeartRateDataUpload", code: 406, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])
                    print("❌ 无效的HTTP响应")
                    completion(false, error)
                    return
                }
                
                // 处理响应
                if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                    print("✅ 心率数据上传成功: 状态码\(httpResponse.statusCode)")
                    
                    // 解析响应数据
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器响应内容: \(responseString)")
                    }
                    
                    // 更新上次心率数据上传时间
                    DispatchQueue.main.async {
                        self.lastHeartRateUploadTime = Date()
                    }
                    
                    completion(true, nil)
                } else {
                    var errorMessage = "服务器返回错误: \(httpResponse.statusCode)"
                    
                    // 尝试解析错误信息
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器错误响应: \(responseString)")
                        errorMessage += ", 响应: \(responseString)"
                    }
                    
                    print("❌ 心率数据上传失败: \(errorMessage)")
                    let error = NSError(domain: "HeartRateDataUpload", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    completion(false, error)
                }
            }.resume()
        } catch {
            print("❌ JSON序列化错误: \(error.localizedDescription)")
            completion(false, error)
        }
    }
    
    /// 获取HRV详细数据
    /// 用于获取一天内的HRV明细数据
    /// - Parameters:
    ///   - day: 指定天数（0表示今天，1表示昨天，以此类推）
    ///   - completion: 完成回调
    public func fetchHRVDetailData(day: Int, completion: @escaping ([HRVData]?, Error?) -> Void) {
        // 记录开始时间，用于跟踪数据获取流程
        let startTime = Date()
        let calendar = Calendar.current
        let targetDate = calendar.date(byAdding: .day, value: -day, to: Date()) ?? Date()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: targetDate)
        
        print("📅 开始获取 \(dateString) (day=\(day)) 的HRV明细数据，开始时间: \(startTime)")
        
        #if os(iOS)
        print("获取第\(day)天的HRV明细数据...目标日期: \(dateString)")
        
        // 检查设备连接状态
        guard WindRingDeviceService.shared.connectionState.isConnected else {
            let error = NSError(domain: "RawDataUpload", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接，无法获取HRV明细数据"])
            print("❌ 设备未连接，无法获取HRV明细数据")
            completion(nil, error)
            return
        }
        
        // 使用SDK获取HRV明细数据
//        CRPSmartRingSDK.sharedInstance.getTimingHRV(day) { record, error in
//            // 计算数据获取耗时
//            let processingTime = Date().timeIntervalSince(startTime)
//            print("⏱️ HRV明细数据SDK调用耗时: \(String(format: "%.2f", processingTime))秒")
//            
//            if error != .none {
//                print("❌ 获取第\(day)天(\(dateString))HRV明细数据失败: \(error)")
//                let nsError = NSError(domain: "RawDataUpload", code: 500, 
//                                     userInfo: [NSLocalizedDescriptionKey: "获取HRV明细数据失败: \(error)"])
//                completion(nil, nsError)
//                return
//            }
//            
//            // 获取对应日期
//            let startOfDay = calendar.startOfDay(for: targetDate)
//            
//            var hrvDetails: [HRVData] = []
//            
//            // 处理HRV数据
//            print("📊 HRV明细数据长度: \(record.hrvs.count)，非零数据点: \(record.hrvs.filter { $0 > 0 }.count)")
//            
//            if !record.hrvs.isEmpty {
//                for (index, hrv) in record.hrvs.enumerated() {
//                    // 只添加有效的HRV记录 (大于0且在合理范围内的值)
//                    if hrv > 0 && hrv < 150 {
//                        // 计算该时间点对应的时间戳
//                        // 每个index代表一个时间点，从0点开始
//                        let timeIntervalInSeconds = TimeInterval(index * 5 * 60) // 假设每5分钟一个数据点
//                        let timestamp = startOfDay.addingTimeInterval(timeIntervalInSeconds)
//                        let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
//                        
//                        // 创建HRV明细记录
//                        let hrvDetail = HRVData(hrv: hrv, time: timeMs)
//                        
//                        // 打印明细数据用于调试
//                        let timeFormatter = DateFormatter()
//                        timeFormatter.dateFormat = "HH:mm"
//                        print("  - 索引\(index): \(timeFormatter.string(from: timestamp)) HRV: \(hrv)")
//                        
//                        hrvDetails.append(hrvDetail)
//                    }
//                }
//            } else {
//                // 如果没有HRV数据或数组为空，返回空数组
//                print("⚠️ HRV数组为空")
//                completion([], nil)
//                return
//            }
//            
//            // 如果没有有效HRV数据，返回空数组
//            if hrvDetails.isEmpty {
//                print("⚠️ 没有有效的HRV数据记录")
//                completion([], nil)
//                return
//            }
//            
//            // 按时间排序
//            let sortedHRVDetails = hrvDetails.sorted { $0.time < $1.time }
//            
//            // 返回结果
//            print("✅ 成功获取 \(dateString) (day=\(day)) 的HRV明细数据，共\(sortedHRVDetails.count)条记录")
//            completion(sortedHRVDetails, nil)
//        }
        #else
        // 在非iOS平台上使用模拟数据
        DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) {
            // 模拟HRV数据
            var mockHRVs: [HRVData] = []
            
            // 生成24小时的模拟HRV数据，每小时4条（每15分钟一条）
            for hour in 0..<24 {
                for quarterHour in 0..<4 {
                    // 计算时间
                    let minutes = quarterHour * 15
                    let date = calendar.date(bySettingHour: hour, minute: minutes, second: 0, of: targetDate)!
                    let timeMs = Int64(date.timeIntervalSince1970 * 1000)
                    
                    // 生成符合规律的模拟HRV值
                    var hrvValue = 0
                    
                    // 睡眠时间(0-6点)HRV较高
                    if hour >= 0 && hour < 6 {
                        hrvValue = Int.random(in: 50...80)
                    }
                    // 晨起锻炼时间(6-9点)HRV较低
                    else if hour >= 6 && hour < 9 {
                        hrvValue = Int.random(in: 25...45)
                    }
                    // 日间工作时间(9-18点)HRV中等
                    else if hour >= 9 && hour < 18 {
                        hrvValue = Int.random(in: 35...55)
                    }
                    // 晚间活动时间(18-22点)HRV较低
                    else if hour >= 18 && hour < 22 {
                        hrvValue = Int.random(in: 30...50)
                    }
                    // 睡前放松时间(22-24点)HRV逐渐升高
                    else {
                        hrvValue = Int.random(in: 40...65)
                    }
                    
                    // 添加随机波动
                    hrvValue += Int.random(in: -5...5)
                    
                    // 创建HRV数据
                    let hrvData = HRVData(hrv: hrvValue, time: timeMs)
                    mockHRVs.append(hrvData)
                }
            }
            
            print("✅ 成功生成模拟HRV数据，共\(mockHRVs.count)条记录")
            completion(mockHRVs, nil)
        }
        #endif
    }
    
    /// 上传HRV数据
    /// - Parameters:
    ///   - day: 指定天数（0表示今天，1表示昨天，以此类推）
    ///   - completion: 完成回调
    public func uploadHRVData(day: Int, completion: @escaping (Bool, Error?) -> Void) {
        print("开始上传第\(day)天的HRV数据...")
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            let error = NSError(domain: "HRVDataUpload", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接，无法获取HRV数据"])
            completion(false, error)
            return
        }
        
        // 检查网络连接状态
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "HRVDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        // 获取用户认证信息
        guard let token = authService.currentToken?.accessToken else {
            let error = NSError(domain: "HRVDataUpload", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])
            completion(false, error)
            return
        }
        
        // 获取HRV明细数据
        fetchHRVDetailData(day: day) { hrvDetails, detailError in
            if let detailError = detailError {
                print("获取第\(day)天HRV明细数据失败: \(detailError.localizedDescription)")
                completion(false, detailError)
                return
            }
            
            // 格式化日期（今天减去day天）
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let calendar = Calendar.current
            let targetDate = calendar.date(byAdding: .day, value: -day, to: Date()) ?? Date()
            let dateString = dateFormatter.string(from: targetDate)
            
            // 构建API要求的JSON请求体
            let requestBody: [String: Any] = [
                "date": dateString,
                "records": (hrvDetails ?? []).map { detail -> [String: Any] in
                    return [
                        "hrv": detail.hrv,
                        "time": detail.time  // 毫秒时间戳
                    ]
                }
            ]
            
            // 发送请求到服务器
            self.sendHRVDataToServer(requestBody: requestBody, token: token, completion: completion)
        }
    }
    
    /// 发送HRV数据到服务器
    /// - Parameters:
    ///   - requestBody: 请求体数据
    ///   - token: 认证令牌
    ///   - completion: 完成回调
    private func sendHRVDataToServer(requestBody: [String: Any], token: String, completion: @escaping (Bool, Error?) -> Void) {
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "HRVDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        // 构建URL
        guard let url = URL(string: "\(baseURL)/app-api/iot/hrv/upload/data") else {
            let error = NSError(domain: "HRVDataUpload", code: 405, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(false, error)
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 将请求体转换为JSON数据
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            request.httpBody = jsonData
            
            // 详细日志记录
            print("📤 发送HRV数据到服务器，URL: \(url.absoluteString)")
            print("📤 数据大小: \(jsonData.count) 字节")
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("📦 HRV数据内容摘要: \(String(jsonString.prefix(200)))...")
            }
            
            // 发送请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 记录HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("🌐 服务器响应状态码: \(httpResponse.statusCode)")
                }
                
                if let error = error {
                    print("❌ 网络请求错误: \(error.localizedDescription)")
                    completion(false, error)
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    let error = NSError(domain: "HRVDataUpload", code: 406, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])
                    print("❌ 无效的HTTP响应")
                    completion(false, error)
                    return
                }
                
                // 处理响应
                if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                    print("✅ HRV数据上传成功: 状态码\(httpResponse.statusCode)")
                    
                    // 解析响应数据
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器响应内容: \(responseString)")
                    }
                    
                    // 更新上次HRV数据上传时间
                    DispatchQueue.main.async {
                        self.lastHRVUploadTime = Date()
                    }
                    
                    completion(true, nil)
                } else {
                    var errorMessage = "服务器返回错误: \(httpResponse.statusCode)"
                    
                    // 尝试解析错误信息
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器错误响应: \(responseString)")
                        errorMessage += ", 响应: \(responseString)"
                    }
                    
                    print("❌ HRV数据上传失败: \(errorMessage)")
                    let error = NSError(domain: "HRVDataUpload", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    completion(false, error)
                }
            }.resume()
        } catch {
            print("❌ JSON序列化错误: \(error.localizedDescription)")
            completion(false, error)
        }
    }
    
    // MARK: - 私有方法
    
    /// 获取所有类型的原始数据
    /// - Parameter completion: 完成回调
    private func fetchAllRawData(completion: @escaping ([RawDataPacket]?, Error?) -> Void) {
        print("RawDataUploadService: 开始获取所有类型的原始数据")
        
        // 创建一个数据容器
        var allData: [RawDataPacket] = []
        
        // 使用DispatchGroup来处理多个异步请求
        let group = DispatchGroup()
        var fetchError: Error? = nil
        
        // 1. 获取心率数据
        group.enter()
        fetchHeartRateData { data, error in
            if let error = error {
                print("获取心率数据失败: \(error.localizedDescription)")
                fetchError = error
            }
            if let data = data {
                allData.append(contentsOf: data)
            }
            group.leave()
        }
        
        // 2. 获取血氧数据
        group.enter()
        fetchBloodOxygenData { data, error in
            if let error = error {
                print("获取血氧数据失败: \(error.localizedDescription)")
                fetchError = error
            }
            if let data = data {
                allData.append(contentsOf: data)
            }
            group.leave()
        }
        
        // 3. 获取HRV数据
        group.enter()
        fetchHRVData { data, error in
            if let error = error {
                print("获取HRV数据失败: \(error.localizedDescription)")
                fetchError = error
            }
            if let data = data {
                allData.append(contentsOf: data)
            }
            group.leave()
        }
        
        // 4. 获取压力数据
        group.enter()
        fetchStressData { data, error in
            if let error = error {
                print("获取压力数据失败: \(error.localizedDescription)")
                fetchError = error
            }
            if let data = data {
                allData.append(contentsOf: data)
            }
            group.leave()
        }
        
        // 5. 获取睡眠数据
        group.enter()
        fetchSleepData { data, error in
            if let error = error {
                print("获取睡眠数据失败: \(error.localizedDescription)")
                fetchError = error
            }
            if let data = data {
                allData.append(contentsOf: data)
            }
            group.leave()
        }
        
        // 6. 获取活动数据
        group.enter()
        fetchHistoricalActivityData(days: 7) { data, error in
            if let error = error {
                print("获取活动数据失败: \(error.localizedDescription)")
                fetchError = error
            }
            if let data = data {
                allData.append(contentsOf: data)
            }
            group.leave()
        }
        
        // 7. 获取体征数据（如体温）
        group.enter()
        fetchVitalSignsData { data, error in
            if let error = error {
                print("获取体征数据失败: \(error.localizedDescription)")
                fetchError = error
            }
            if let data = data {
                allData.append(contentsOf: data)
            }
            group.leave()
        }
        
        // 当所有任务完成后，返回整合的数据
        group.notify(queue: .main) {
            if allData.isEmpty && fetchError != nil {
                // 如果没有数据并且有错误，则返回错误
                completion(nil, fetchError)
            } else {
                // 即使部分数据获取失败，只要有数据就返回
                completion(allData, nil)
            }
        }
    }
    
    /// 获取心率数据
    private func fetchHeartRateData(completion: @escaping ([RawDataPacket]?, Error?) -> Void) {
        #if os(iOS)
        // 使用SDK获取心率数据
        print("获取心率数据...")
        
        // 实际SDK调用例子 (您需要根据实际SDK调整)
        // CRPSmartRingSDK.sharedInstance.fetchHeartRateData(days: 1) { hrData, error in
        //     guard error == nil, let hrData = hrData else {
        //         completion(nil, error ?? NSError(...))
        //         return
        //     }
        //     
        //     let packets = hrData.map { hr in
        //         RawDataPacket(
        //             timestamp: hr.timestamp,
        //             type: .heartRate,
        //             values: [Double(hr.value)],
        //             confidence: hr.confidence
        //         )
        //     }
        //     completion(packets, nil)
        // }
        
        // 临时使用模拟数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let packets = self.generateMockData(type: .heartRate, count: 24)
            completion(packets, nil)
        }
        #else
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let packets = self.generateMockData(type: .heartRate, count: 24)
            completion(packets, nil)
        }
        #endif
    }
    
    /// 获取血氧数据
    private func fetchBloodOxygenData(completion: @escaping ([RawDataPacket]?, Error?) -> Void) {
        #if os(iOS)
        // 使用SDK获取血氧数据
        print("获取血氧数据...")
        
        // 实际SDK调用例子
        // CRPSmartRingSDK.sharedInstance.fetchBloodOxygenData(days: 1) { spo2Data, error in
        //     // 转换为 RawDataPacket
        // }
        
        // 临时使用模拟数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let packets = self.generateMockData(type: .bloodOxygen, count: 12)
            completion(packets, nil)
        }
        #else
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let packets = self.generateMockData(type: .bloodOxygen, count: 12)
            completion(packets, nil)
        }
        #endif
    }
    
    /// 获取HRV数据
    private func fetchHRVData(completion: @escaping ([RawDataPacket]?, Error?) -> Void) {
        #if os(iOS)
        print("获取HRV数据...")
        
        // 实际SDK调用例子
        // CRPSmartRingSDK.sharedInstance.fetchHRVData(days: 1) { hrvData, error in
        //     // 转换为 RawDataPacket
        // }
        
        // 临时使用模拟数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let packets = self.generateMockData(type: .hrv, count: 12)
            completion(packets, nil)
        }
        #else
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let packets = self.generateMockData(type: .hrv, count: 12)
            completion(packets, nil)
        }
        #endif
    }
    
    /// 获取压力数据
    private func fetchStressData(completion: @escaping ([RawDataPacket]?, Error?) -> Void) {
        #if os(iOS)
        print("获取压力数据...")
        
        // 实际SDK调用例子
        // CRPSmartRingSDK.sharedInstance.fetchStressData(days: 1) { stressData, error in
        //     // 转换为 RawDataPacket
        // }
        
        // 临时使用模拟数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let packets = self.generateMockData(type: .stress, count: 12)
            completion(packets, nil)
        }
        #else
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let packets = self.generateMockData(type: .stress, count: 12)
            completion(packets, nil)
        }
        #endif
    }
    
    /// 获取睡眠数据
    private func fetchSleepData(completion: @escaping ([RawDataPacket]?, Error?) -> Void) {
        #if os(iOS)
        print("获取睡眠数据...")
        
        // 实际SDK调用例子
        // CRPSmartRingSDK.sharedInstance.fetchSleepData(days: 1) { sleepData, error in
        //     // 转换为 RawDataPacket，睡眠数据可能需要特殊处理
        // }
        
        // 临时使用模拟数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let packets = self.generateMockData(type: .sleep, count: 1)
            completion(packets, nil)
        }
        #else
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let packets = self.generateMockData(type: .sleep, count: 1)
            completion(packets, nil)
        }
        #endif
    }
    
    /// 获取活动数据
    private func fetchHistoricalActivityData(days: Int, completion: @escaping ([RawDataPacket]?, Error?) -> Void) {
        #if os(iOS)
        print("获取过去\(days)天的活动数据...")
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            let error = NSError(domain: "RawDataUpload", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接，无法获取活动数据"])
            completion(nil, error)
            return
        }
        
        var allPackets: [RawDataPacket] = []
        let group = DispatchGroup()
        var fetchError: Error? = nil
        
        // 添加日志，记录开始获取数据的时间
        print("开始获取 \(Date().formatted(date: .numeric, time: .standard)) (day=0-\(days-1)) 的活动数据")
        
        // 获取每一天的数据
        for day in 0..<days {
            // 记录每次进入group以便调试
            print("开始获取 \(day) 天前的活动数据，group.enter()")
            group.enter()
            
            // 使用SDK的getTrainingData获取指定日期的活动数据
//            CRPSmartRingSDK.sharedInstance.getTrainingData(day) { record, error in
//                // 使用defer确保无论如何都会leave，即使闭包中提前return也会执行
//                defer {
//                    print("完成获取 \(day) 天前的活动数据，group.leave()")
//                    group.leave()
//                }
//                
//                if error != .none {
//                    print("获取第\(day)天活动数据失败: \(error)")
//                    // 只记录错误，继续获取其他日期的数据
//                    if fetchError == nil {
//                        fetchError = NSError(domain: "RawDataUpload", code: 500, 
//                                           userInfo: [NSLocalizedDescriptionKey: "获取部分活动数据失败"])
//                    }
//                    return // 返回前会执行defer中的group.leave()
//                }
//                
//                // 为每天创建一个时间戳，从当前时间减去对应的天数
//                let calendar = Calendar.current
//                let dayDate = calendar.date(byAdding: .day, value: -day, to: Date()) ?? Date()
//                let timestamp = dayDate.timeIntervalSince1970
//                
//                // 创建RawDataPacket格式的数据
//                let values: [Double] = [
//                    Double(record.step),          // 步数
//                    Double(record.distance),      // 距离(厘米)
//                    Double(record.cal)            // 卡路里
//                ]
//                
//                // 元数据
//                let metaData: [String: Any] = [
//                    "active_minutes": record.exerciseTime,
//                    "source": "device_training_data",
//                    "day": record.day
//                ]
//                
//                // 创建数据包
//                let packet = RawDataPacket(
//                    timestamp: timestamp,
//                    type: .activity,
//                    values: values,
//                    confidence: 100,  // 设备数据可信度高
//                    metaData: metaData
//                )
//                
//                // 添加到结果集
//                allPackets.append(packet)
//                print("已添加第\(day)天的活动数据到结果集")
//            }
        }
        
        // 等待所有请求完成
        group.notify(queue: .main) {
            print("所有活动数据获取完成，共\(allPackets.count)条记录")
            
            if allPackets.isEmpty && fetchError != nil {
                // 如果没有数据并且有错误，返回错误
                completion(nil, fetchError)
            } else {
                // 即使部分数据获取失败，只要有数据就返回
                completion(allPackets, nil)
            }
        }
        #else
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            var packets: [RawDataPacket] = []
            for _ in 0..<days {
                packets.append(contentsOf: self.generateMockData(type: .activity, count: 1))
            }
            completion(packets, nil)
        }
        #endif
    }
    
    /// 获取体征数据（包括体温等）
    private func fetchVitalSignsData(completion: @escaping ([RawDataPacket]?, Error?) -> Void) {
        #if os(iOS)
        print("获取体征数据...")
        
        // 实际SDK调用例子
        // CRPSmartRingSDK.sharedInstance.fetchTemperatureData(days: 1) { tempData, error in
        //     // 转换为 RawDataPacket
        // }
        
        // 临时使用模拟数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            var packets: [RawDataPacket] = []
            // 添加体温数据
            packets.append(contentsOf: self.generateMockData(type: .temperature, count: 24))
            // 添加其他体征数据
            packets.append(contentsOf: self.generateMockData(type: .skinTemperature, count: 24))
            packets.append(contentsOf: self.generateMockData(type: .bodyTemperature, count: 24))
            completion(packets, nil)
        }
        #else
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            var packets: [RawDataPacket] = []
            packets.append(contentsOf: self.generateMockData(type: .temperature, count: 24))
            packets.append(contentsOf: self.generateMockData(type: .skinTemperature, count: 24))
            packets.append(contentsOf: self.generateMockData(type: .bodyTemperature, count: 24))
            completion(packets, nil)
        }
        #endif
    }
    
    /// 上传数据到服务器
    /// - Parameters:
    ///   - data: 要上传的原始数据
    ///   - completion: 完成回调
    private func uploadToServer(data: [RawDataPacket], completion: @escaping (Bool, Error?) -> Void) {
        print("RawDataUploadService: 开始上传原始数据到服务器，共\(data.count)条")
        
        // 获取用户认证信息
        guard let token = authService.currentToken?.accessToken else {
            let error = NSError(domain: "RawDataUpload", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])
            completion(false, error)
            return
        }
        
        // 获取设备信息
        guard let deviceId = deviceService.deviceInfo?.mac else {
            let error = NSError(domain: "RawDataUpload", code: 404, userInfo: [NSLocalizedDescriptionKey: "无法获取设备ID"])
            completion(false, error)
            return
        }
        
        // 批量上传数据
        let totalBatches = (data.count + batchSize - 1) / batchSize
        var completedBatches = 0
        var hasError = false
        
        for i in stride(from: 0, to: data.count, by: batchSize) {
            let end = min(i + batchSize, data.count)
            let batchData = Array(data[i..<end])
            
            uploadBatch(batchData, deviceId: deviceId, token: token) { [weak self] success, error in
                guard let self = self else { return }
                
                completedBatches += 1
                self.uploadProgress = Double(completedBatches) / Double(totalBatches)
                
                if !success {
                    hasError = true
                    print("RawDataUploadService: 批次\(completedBatches)/\(totalBatches)上传失败: \(error?.localizedDescription ?? "未知错误")")
                } else {
                    print("RawDataUploadService: 批次\(completedBatches)/\(totalBatches)上传成功")
                }
                
                // 全部批次处理完成
                if completedBatches == totalBatches {
                    completion(!hasError, hasError ? NSError(domain: "RawDataUpload", code: 500, userInfo: [NSLocalizedDescriptionKey: "部分数据上传失败"]) : nil)
                }
            }
        }
    }
    
    /// 上传单个批次数据
    /// - Parameters:
    ///   - batchData: 批次数据
    ///   - deviceId: 设备ID
    ///   - token: 认证令牌
    ///   - completion: 完成回调
    private func uploadBatch(_ batchData: [RawDataPacket], deviceId: String, token: String, completion: @escaping (Bool, Error?) -> Void) {
        // 构建URL
        guard let url = URL(string: "\(baseURL)/app-api/iot/device/uploadRawData") else {
            let error = NSError(domain: "RawDataUpload", code: 405, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(false, error)
            return
        }
        
        // 构建请求体
        let requestBody: [String: Any] = [
            "deviceId": deviceId,
            "timestamp": Date().timeIntervalSince1970,
            "data": batchData.map { $0.toDictionary() },
            "dataCount": batchData.count
        ]
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            completion(false, error)
            return
        }
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(false, error)
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                let error = NSError(domain: "RawDataUpload", code: 406, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])
                print("❌ 无效的HTTP响应")
                completion(false, error)
                return
            }
            
            // 处理响应
            if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                completion(true, nil)
            } else {
                var errorMessage = "服务器返回错误: \(httpResponse.statusCode)"
                
                if let data = data, let responseString = String(data: data, encoding: .utf8) {
                    errorMessage += ", 响应: \(responseString)"
                }
                
                let error = NSError(domain: "RawDataUpload", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                completion(false, error)
            }
        }
        
        task.resume()
    }
    
    /// 生成特定类型的模拟数据
    private func generateMockData(type: RawDataType, count: Int) -> [RawDataPacket] {
        var result: [RawDataPacket] = []
        let now = Date().timeIntervalSince1970
        
        for i in 0..<count {
            // 根据数据类型计算合适的时间间隔
            let interval: TimeInterval
            switch type {
            case .heartRate, .temperature, .skinTemperature, .bodyTemperature:
                interval = 3600 // 每小时一条
            case .bloodOxygen, .hrv, .stress:
                interval = 7200 // 每2小时一条
            case .sleep:
                interval = 86400 // 每天一条
            case .activity:
                interval = 86400 // 每天一条
            }
            
            let timestamp = now - Double(i) * interval
            var values: [Double] = []
            var metaData: [String: Any]? = nil
            
            switch type {
            case .heartRate:
                values = [Double(Int.random(in: 60...120))]
            case .bloodOxygen:
                values = [Double(Int.random(in: 95...100))]
            case .temperature, .skinTemperature:
                values = [Double.random(in: 30.0...35.0)]
            case .bodyTemperature:
                values = [Double.random(in: 36.0...37.5)]
            case .activity:
                values = [
                    Double(Int.random(in: 0...15000)), // 步数
                    Double(Int.random(in: 0...10000)), // 距离
                    Double(Int.random(in: 0...500))    // 卡路里
                ]
                metaData = [
                    "active_minutes": Int.random(in: 0...120),
                    "goal_progress": Double.random(in: 0...1.0)
                ]
            case .sleep:
                values = [
                    Double(Int.random(in: 360...480)),  // 总睡眠时间(分钟)
                    Double(Int.random(in: 60...120)),   // 深睡眠
                    Double(Int.random(in: 180...240)),  // 浅睡眠
                    Double(Int.random(in: 60...120))    // REM睡眠
                ]
                metaData = [
                    "sleep_score": Int.random(in: 60...100),
                    "sleep_efficiency": Double.random(in: 0.7...0.95),
                    "stages": [
                        ["type": "deep", "duration": Int.random(in: 60...120)],
                        ["type": "light", "duration": Int.random(in: 180...240)],
                        ["type": "rem", "duration": Int.random(in: 60...120)]
                    ]
                ]
            case .stress:
                values = [Double(Int.random(in: 0...100))]
            case .hrv:
                values = [Double(Int.random(in: 20...80))]
            }
            
            let packet = RawDataPacket(
                timestamp: timestamp,
                type: type,
                values: values,
                confidence: Int.random(in: 80...100),
                metaData: metaData
            )
            
            result.append(packet)
        }
        
        return result
    }
    
    /// 生成混合的模拟数据
    private func generateMockRawData(count: Int) -> [RawDataPacket] {
        var result: [RawDataPacket] = []
        
        // 添加各种类型的数据
        result.append(contentsOf: generateMockData(type: .heartRate, count: count / 7))
        result.append(contentsOf: generateMockData(type: .bloodOxygen, count: count / 7))
        result.append(contentsOf: generateMockData(type: .temperature, count: count / 7))
        result.append(contentsOf: generateMockData(type: .activity, count: 1))
        result.append(contentsOf: generateMockData(type: .sleep, count: 1))
        result.append(contentsOf: generateMockData(type: .stress, count: count / 7))
        result.append(contentsOf: generateMockData(type: .hrv, count: count / 7))
        
        return result
    }
    
    /// 上传5天的活动数据
    private func uploadActivityDataFor7Days() {
        print("开始上传5天的活动数据...")
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            print("RawDataUploadService: 设备未连接，无法上传活动数据")
            return
        }
        
        // 创建计数器以跟踪enter和leave的调用次数
        let uploadGroup = DispatchGroup()
        var enterCount = 0
        var leaveCount = 0
        var successCount = 0
        var totalCount = 0
        
        // 上传活动数据（修改为5天）
        for day in 0..<5 {
            // 安全的enter调用
            enterCount += 1
            uploadGroup.enter()
            totalCount += 1
            
            // 添加随机延迟，避免同时发起太多请求
            let randomDelay = Double(day) * 0.7 + Double.random(in: 0.1...0.5)
            DispatchQueue.global().asyncAfter(deadline: .now() + randomDelay) {
                self.uploadActivityData(day: day) { success, _ in
                    if success {
                        print("RawDataUploadService: 定时上传 - 第\(day)天活动数据上传成功")
                        successCount += 1
                        if day == 0 {
                            self.lastActivityUploadTime = Date()
                        }
                    } else {
                        print("RawDataUploadService: 定时上传 - 第\(day)天活动数据上传失败")
                    }
                    
                    // 安全的leave调用
                    if leaveCount < enterCount {
                        leaveCount += 1
                        uploadGroup.leave()
                    } else {
                        print("⚠️ 警告：尝试调用活动数据uploadGroup.leave()的次数超过了enter()的次数，已跳过此调用")
                    }
                }
            }
        }
        
        uploadGroup.notify(queue: .global()) {
            print("✅ 活动数据上传完成，成功: \(successCount)/\(totalCount)")
        }
    }
    
    /// 上传5天的心率数据
    private func uploadHeartRateDataFor7Days() {
        print("开始上传5天的心率数据...")
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            print("RawDataUploadService: 设备未连接，无法上传心率数据")
            return
        }
        
        // 创建计数器以跟踪enter和leave的调用次数
        let heartRateGroup = DispatchGroup()
        var enterCount = 0
        var leaveCount = 0
        var successCount = 0
        var totalCount = 0
        
        // 上传心率数据（修改为5天）
        for day in 0..<5 {
            // 安全的enter调用
            enterCount += 1
            heartRateGroup.enter()
            totalCount += 1
            
            // 添加随机延迟，避免同时发起太多请求
            let randomDelay = Double(day) * 0.7 + Double.random(in: 0.1...0.5)
            DispatchQueue.global().asyncAfter(deadline: .now() + randomDelay) {
                self.uploadHeartRateData(day: day) { success, _ in
                    if success {
                        print("RawDataUploadService: 定时上传 - 第\(day)天心率数据上传成功")
                        successCount += 1
                        if day == 0 {
                            self.lastHeartRateUploadTime = Date()
                        }
                    } else {
                        print("RawDataUploadService: 定时上传 - 第\(day)天心率数据上传失败")
                    }
                    
                    // 安全的leave调用
                    if leaveCount < enterCount {
                        leaveCount += 1
                        heartRateGroup.leave()
                    } else {
                        print("⚠️ 警告：尝试调用心率数据heartRateGroup.leave()的次数超过了enter()的次数，已跳过此调用")
                    }
                }
            }
        }
        
        heartRateGroup.notify(queue: .global()) {
            print("✅ 心率数据上传完成，成功: \(successCount)/\(totalCount)")
        }
    }
    
    /// 上传5天的HRV数据
    private func uploadHRVDataFor7Days() {
        print("开始上传5天的HRV数据...")
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            print("RawDataUploadService: 设备未连接，无法上传HRV数据")
            return
        }
        
        // 创建计数器以跟踪enter和leave的调用次数
        let hrvGroup = DispatchGroup()
        var enterCount = 0
        var leaveCount = 0
        var successCount = 0
        var totalCount = 0
        
        // 上传HRV数据（修改为5天）
        for day in 0..<5 {
            // 安全的enter调用
            enterCount += 1
            hrvGroup.enter()
            totalCount += 1
            
            // 添加随机延迟，避免同时发起太多请求
            let randomDelay = Double(day) * 0.7 + Double.random(in: 0.1...0.5)
            DispatchQueue.global().asyncAfter(deadline: .now() + randomDelay) {
                self.uploadHRVData(day: day) { success, _ in
                    if success {
                        print("RawDataUploadService: 定时上传 - 第\(day)天HRV数据上传成功")
                        successCount += 1
                        if day == 0 {
                            self.lastHRVUploadTime = Date()
                        }
                    } else {
                        print("RawDataUploadService: 定时上传 - 第\(day)天HRV数据上传失败")
                    }
                    
                    // 安全的leave调用
                    if leaveCount < enterCount {
                        leaveCount += 1
                        hrvGroup.leave()
                    } else {
                        print("⚠️ 警告：尝试调用HRV数据hrvGroup.leave()的次数超过了enter()的次数，已跳过此调用")
                    }
                }
            }
        }
        
        hrvGroup.notify(queue: .global()) {
            print("✅ HRV数据上传完成，成功: \(successCount)/\(totalCount)")
        }
    }
    
    /// 无条件强制启动上传服务
    /// 跳过设备连接状态检查，用于已确认设备已连接的情况
    /// - Parameter interval: 上传间隔（秒）
    private func forceStartUpload(interval: TimeInterval = 300) {
        guard !isUploading else {
            print("RawDataUploadService: 已经有上传任务在进行中")
            return
        }
        
        print("💫 强制启动数据自动上传服务...")
        
        // 设置标志
        isAutoUploadEnabled = true
        UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
        
        // 停止现有计时器
        autoUploadTimer?.invalidate()
        
        // 检查自动上传状态
        checkAutoUploadStatus()
        
        // 首次立即执行一次上传
        uploadRawData { success, _ in
            if success {
                print("RawDataUploadService: 首次上传成功")
            } else {
                print("RawDataUploadService: 首次上传失败")
            }
            
            // 首次上传完成后，分别上传活动数据、心率数据、HRV数据、血氧数据、压力数据和睡眠数据
            self.uploadActivityDataFor7Days()
            
            // 等待一段时间后上传心率数据，避免同时大量请求
            DispatchQueue.global().asyncAfter(deadline: .now() + 3) {
                self.uploadHeartRateDataFor7Days()
            }
            
            // 等待一段时间后上传HRV数据，避免同时大量请求
            DispatchQueue.global().asyncAfter(deadline: .now() + 6) {
                self.uploadHRVDataFor7Days()
            }
            
            // 等待一段时间后上传血氧数据，避免同时大量请求
            DispatchQueue.global().asyncAfter(deadline: .now() + 9) {
                self.uploadBloodOxygenDataFor7Days()
            }
            
            // 等待一段时间后上传压力数据，避免同时大量请求
            DispatchQueue.global().asyncAfter(deadline: .now() + 12) {
                self.uploadStressDataFor7Days()
            }
            
            // 注释掉睡眠数据自动上传
            // 等待一段时间后上传睡眠数据，避免同时大量请求
            // DispatchQueue.global().asyncAfter(deadline: .now() + 15) {
            //     self.uploadSleepDataFor7Days()
            // }
        }
        
        // 设置定时任务
        autoUploadTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            guard let self = self, self.isAutoUploadEnabled else { return }
            
            // 上传原始数据
            self.uploadRawData { success, _ in
                if success {
                    print("RawDataUploadService: 定时上传成功")
                } else {
                    print("RawDataUploadService: 定时上传失败")
                }
                
                // 分别上传活动数据、心率数据、HRV数据、血氧数据、压力数据和睡眠数据
                self.uploadActivityDataFor7Days()
                
                // 等待一段时间后上传心率数据，避免同时大量请求
                DispatchQueue.global().asyncAfter(deadline: .now() + 3) {
                    self.uploadHeartRateDataFor7Days()
                }
                
                // 等待一段时间后上传HRV数据，避免同时大量请求
                DispatchQueue.global().asyncAfter(deadline: .now() + 6) {
                    self.uploadHRVDataFor7Days()
                }
                
                // 等待一段时间后上传血氧数据，避免同时大量请求
                DispatchQueue.global().asyncAfter(deadline: .now() + 9) {
                    self.uploadBloodOxygenDataFor7Days()
                }
                
                // 等待一段时间后上传压力数据，避免同时大量请求
                DispatchQueue.global().asyncAfter(deadline: .now() + 12) {
                    self.uploadStressDataFor7Days()
                }
                
                // 注释掉睡眠数据自动上传
                // 等待一段时间后上传睡眠数据，避免同时大量请求
                // DispatchQueue.global().asyncAfter(deadline: .now() + 15) {
                //     self.uploadSleepDataFor7Days()
                // }
            }
        }
    }
    
    /// 上传5天的血氧数据
    private func uploadBloodOxygenDataFor7Days() {
        print("开始上传5天的血氧数据...")
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            print("RawDataUploadService: 设备未连接，无法上传血氧数据")
            return
        }
        
        // 创建计数器以跟踪enter和leave的调用次数
        let bloodOxygenGroup = DispatchGroup()
        var enterCount = 0
        var leaveCount = 0
        var successCount = 0
        var totalCount = 0
        
        // 使用BloodOxygenUploadService同步并上传
        let bloodOxygenService = BloodOxygenUploadService.shared
        
        // 上传血氧数据
        // 强制刷新获取最新血氧数据
        enterCount += 1
        bloodOxygenGroup.enter()
        totalCount += 1
        
        bloodOxygenService.fetchAndSaveBloodOxygenHistory { fetchCount, fetchError in
            if let error = fetchError {
                print("RawDataUploadService: 获取血氧数据失败: \(error.localizedDescription)")
                // 安全的leave调用
                if leaveCount < enterCount {
                    leaveCount += 1
                    bloodOxygenGroup.leave()
                }
                return
            }
            
            print("RawDataUploadService: 成功获取\(fetchCount)条血氧数据，准备上传")
            
            // 随机延迟，避免同时发起太多请求
            DispatchQueue.global().asyncAfter(deadline: .now() + Double.random(in: 0.5...1.0)) {
                // 上传血氧数据
                bloodOxygenService.uploadPendingBloodOxygenData { uploadCount, uploadError in
                    if let error = uploadError {
                        print("RawDataUploadService: 上传血氧数据失败: \(error.localizedDescription)")
                    } else {
                        print("RawDataUploadService: 成功上传\(uploadCount)条血氧数据")
                        successCount += 1
                        self.lastBloodOxygenUploadTime = Date()
                    }
                    
                    // 安全的leave调用
                    if leaveCount < enterCount {
                        leaveCount += 1
                        bloodOxygenGroup.leave()
                    } else {
                        print("⚠️ 警告：尝试调用血氧数据bloodOxygenGroup.leave()的次数超过了enter()的次数，已跳过此调用")
                    }
                }
            }
        }
        
        bloodOxygenGroup.notify(queue: .global()) {
            print("✅ 血氧数据上传完成，成功: \(successCount)/\(totalCount)")
        }
    }

    /// 上传5天的压力数据
    private func uploadStressDataFor7Days() {
        print("开始上传5天的压力数据...")
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            print("RawDataUploadService: 设备未连接，无法上传压力数据")
            return
        }
        
        // 创建计数器以跟踪enter和leave的调用次数
        let stressGroup = DispatchGroup()
        var enterCount = 0
        var leaveCount = 0
        var successCount = 0
        var totalCount = 0
        
        // 使用StressUploadService同步并上传
        let stressService = StressUploadService.shared
        
        // 强制刷新获取最新压力数据
        enterCount += 1
        stressGroup.enter()
        totalCount += 1
        
        stressService.fetchAndSaveStressHistory { fetchCount, fetchError in
            if let error = fetchError {
                print("RawDataUploadService: 获取压力数据失败: \(error.localizedDescription)")
                // 安全的leave调用
                if leaveCount < enterCount {
                    leaveCount += 1
                    stressGroup.leave()
                }
                return
            }
            
            print("RawDataUploadService: 成功获取\(fetchCount)条压力数据，准备上传")
            
            // 随机延迟，避免同时发起太多请求
            DispatchQueue.global().asyncAfter(deadline: .now() + Double.random(in: 0.5...1.0)) {
                // 上传压力数据
                stressService.uploadPendingStressData { uploadCount, uploadError in
                    if let error = uploadError {
                        print("RawDataUploadService: 上传压力数据失败: \(error.localizedDescription)")
                    } else {
                        print("RawDataUploadService: 成功上传\(uploadCount)条压力数据")
                        successCount += 1
                        self.lastStressUploadTime = Date()
                    }
                    
                    // 安全的leave调用
                    if leaveCount < enterCount {
                        leaveCount += 1
                        stressGroup.leave()
                    } else {
                        print("⚠️ 警告：尝试调用压力数据stressGroup.leave()的次数超过了enter()的次数，已跳过此调用")
                    }
                }
            }
        }
        
        stressGroup.notify(queue: .global()) {
            print("✅ 压力数据上传完成，成功: \(successCount)/\(totalCount)")
        }
    }

    /// 上传最近7天的睡眠数据
    func uploadSleepDataFor7Days() {
        // 此方法已被禁用，不再自动上传睡眠数据
        print("睡眠数据自动上传已被禁用")
        
        /*
        print("开始上传最近7天的睡眠数据")
        
        // 检查用户是否登录
        guard let userId = AuthService.shared.currentUser?.id else {
            print("用户未登录，无法上传睡眠数据")
            return
        }
        
        let calendar = Calendar.current
        let endDate = Date()
        let startDate = calendar.date(byAdding: .day, value: -7, to: endDate)!
        
        // 获取设备ID
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown"
        
        // 获取未上传的睡眠数据
        let healthDataManager = HealthDataManager.shared
        var sleepData = healthDataManager.getSleep(userId: userId)
        
        // 仅保留最近7天的数据
        sleepData = sleepData.filter { sleep in
            guard let sleepDate = sleep.startTime else { return false }
            return sleepDate >= startDate && sleepDate <= endDate
        }
        
        if sleepData.isEmpty {
            print("没有找到最近7天的睡眠数据")
            lastSleepUploadTime = Date()
            return
        }
        
        print("找到\(sleepData.count)条睡眠数据，开始上传")
        
        // 上传每条睡眠数据
        SleepUploadService.shared.uploadPendingSleepData { uploadCount, uploadError in
            if let uploadError = uploadError {
                print("上传睡眠数据失败: \(uploadError.localizedDescription)")
            } else {
                print("成功上传\(uploadCount)条睡眠数据")
                self.lastSleepUploadTime = Date()
            }
        }
        */
    }
}

/// 原始数据类型
public enum RawDataType: String, Codable {
    case heartRate = "heart_rate"
    case bloodOxygen = "blood_oxygen"
    case temperature = "temperature"
    case skinTemperature = "skin_temperature"
    case bodyTemperature = "body_temperature"
    case activity = "activity"
    case sleep = "sleep"
    case stress = "stress"
    case hrv = "hrv"
}

/// 原始数据包
public struct RawDataPacket: Codable {
    /// 时间戳（秒）
    public let timestamp: TimeInterval
    
    /// 数据类型
    public let type: RawDataType
    
    /// 数据值（可能包含多个值）
    public let values: [Double]
    
    /// 数据置信度（0-100）
    public let confidence: Int
    
    /// 元数据（用于存储额外信息，比如睡眠阶段）
    public let metaData: [String: Any]?
    
    /// 初始化
    public init(timestamp: TimeInterval, type: RawDataType, values: [Double], confidence: Int, metaData: [String: Any]? = nil) {
        self.timestamp = timestamp
        self.type = type
        self.values = values
        self.confidence = confidence
        self.metaData = metaData
    }
    
    /// 转换为字典，用于JSON序列化
    public func toDictionary() -> [String: Any] {
        var dict: [String: Any] = [
            "timestamp": timestamp,
            "type": type.rawValue,
            "values": values,
            "confidence": confidence
        ]
        
        if let metaData = metaData {
            dict["metaData"] = metaData
        }
        
        return dict
    }
    
    // 由于metaData是[String: Any]类型，无法直接用于Codable，需要自定义编解码
    
    enum CodingKeys: String, CodingKey {
        case timestamp, type, values, confidence
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        timestamp = try container.decode(TimeInterval.self, forKey: .timestamp)
        type = try container.decode(RawDataType.self, forKey: .type)
        values = try container.decode([Double].self, forKey: .values)
        confidence = try container.decode(Int.self, forKey: .confidence)
        metaData = nil // 解码时不支持Any类型，设为nil
    }
    
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(timestamp, forKey: .timestamp)
        try container.encode(type, forKey: .type)
        try container.encode(values, forKey: .values)
        try container.encode(confidence, forKey: .confidence)
        // metaData不编码
    }
} 
