import Foundation
import Combine
import SwiftUI
import Network
// 不需要单独导入模型，因为它们在项目中可以直接访问

/// 活动服务
public class ActivityService {
    // MARK: - 单例
    public static let shared = ActivityService()
    
    // MARK: - 属性
    private let networkManager: NetworkManager
    private let networkMonitor = NetworkMonitor.shared
    
    // 访问令牌
//    @AppStorage("auth_token") private var token: String?
    
    // 数据缓存
    private var scoreCache = [String: (data: ActivityScoreData, timestamp: Date)]()
    private let cacheDuration: TimeInterval = 5 * 60 // 5分钟缓存时间
    
    // MARK: - 初始化方法
    private init() {
        self.networkManager = NetworkManager.shared
//        self.baseURL = "http://ring-api-dev.weaving-park.com"
    }
    
    /// 获取活动评分
    /// - Parameters:
    ///   - date: 日期
    ///   - forceRefresh: 是否强制刷新，不使用缓存
    ///   - retryCount: 失败时重试次数
    /// - Returns: 活动评分数据
    public func getActivityScore(
        for date: Date,
        forceRefresh: Bool = false,
        retryCount: Int = 2
    ) -> AnyPublisher<ActivityScoreData, Error> {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("ActivityService - 开始获取活动数据 - 日期: \(dateString), 强制刷新: \(forceRefresh)")
        
        // 检查缓存 (如果不是强制刷新)
//        if !forceRefresh,
//           let cached = scoreCache[dateString],
//           Date().timeIntervalSince(cached.timestamp) < cacheDuration {
//            print("ActivityService - 使用缓存数据 - 日期: \(dateString)")
//            return Just(cached.data)
//                .setFailureType(to: Error.self)
//                .eraseToAnyPublisher()
//        }
        
        // 检查网络连接
        guard networkMonitor.isConnected else {
            let error = NSError(
                domain: "com.windring.activity", 
                code: -3, 
                userInfo: [NSLocalizedDescriptionKey: "网络连接不可用"]
            )
            print("ActivityService - 网络连接不可用")
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        // 构建端点
        let endpoint = "/app-api/iot/activity/getScore"
        
        // 构建请求参数
        let parameters: [String: Any] = ["date": dateString]
        
        return networkManager.requestData(
            endpoint: endpoint,
            method: .get,
            parameters: parameters,
//            headers: headers
        )
        .tryMap { data -> ActivityScoreResponse in
                // 打印原始响应数据
                if let responseString = String(data: data, encoding: .utf8) {
                    print("\nActivityService - 原始响应数据:")
                    print(responseString)
                    
                    // 尝试格式化JSON以便阅读
                    if let jsonObject = try? JSONSerialization.jsonObject(with: data, options: []),
                       let prettyData = try? JSONSerialization.data(withJSONObject: jsonObject, options: [.prettyPrinted, .sortedKeys]),
                       let prettyString = String(data: prettyData, encoding: .utf8) {
                        print("\nActivityService - 格式化响应JSON:")
                        print(prettyString)
                    }
                }
                
            let decoder = CleanJSONDecoder()
            do {
                let response = try decoder.decode(ActivityScoreResponse.self, from: data)
                return response
            } catch {
                print("ActivityService - 解析活动评分数据失败: \(error)")
                throw error
            }
        }
            .tryMap { [weak self] response in
                if response.code == 0, let scoreData = response.data {
                    // 使用模型的辅助方法打印详细日志
                    print("\nActivityService - 成功获取活动数据")
                    
                    // 打印完整响应
                    if let jsonString = response.toJSONString() {
                        print("\nActivityService - 完整响应JSON:")
                        print(jsonString)
                    }
                    
                    // 打印数据摘要
                    print("\n" + scoreData.getSummary())
                    
                    // 缓存数据
//                self?.scoreCache[dateString] = (scoreData, Date())
                    
                    return scoreData
                } else {
                    let errorMessage = response.msg.isEmpty ? "获取活动数据失败" : response.msg
                    print("ActivityService - 服务器返回错误: \(errorMessage)")
                    
                    // 打印错误响应
                    if let jsonString = response.toJSONString() {
                        print("\nActivityService - 错误响应JSON:")
                        print(jsonString)
                    }
                    
                    throw NSError(
                        domain: "com.windring.activity", 
                        code: response.code, 
                        userInfo: [NSLocalizedDescriptionKey: errorMessage]
                    )
                }
            }
        .retry(retryCount) // 失败时自动重试
            .eraseToAnyPublisher()
    }
    
    /// 获取活动评分（异步版本）
    /// - Parameters:
    ///   - date: 日期
    ///   - forceRefresh: 是否强制刷新，不使用缓存
    /// - Returns: 活动评分数据
    @available(iOS 15.0, *)
    public func getActivityScore(for date: Date, forceRefresh: Bool = false) async throws -> ActivityScoreData {
        return try await getActivityScore(for: date, forceRefresh: forceRefresh).asyncFirst()
    }
    
    /// 清除活动评分缓存
    public func clearCache() {
        print("ActivityService - 清除所有缓存")
        scoreCache.removeAll()
    }
    
    /// 清除特定日期的活动评分缓存
    /// - Parameter date: 日期
    public func clearCache(for date: Date) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("ActivityService - 清除缓存 - 日期: \(dateString)")
        scoreCache.removeValue(forKey: dateString)
    }
    
    // MARK: - 活动状态接口
    
    /// 获取活动状态
    /// - Parameters:
    ///   - date: 日期
    ///   - forceRefresh: 是否强制刷新
    ///   - retryCount: 失败时重试次数
    /// - Returns: 活动状态数据数组
    public func getActivityStates(
        for date: Date,
        forceRefresh: Bool = false,
        retryCount: Int = 2
    ) -> AnyPublisher<[ActivityStateData], Error> {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("ActivityService - 开始获取活动状态数据 - 日期: \(dateString), 强制刷新: \(forceRefresh)")
        
        // 检查网络连接
        guard networkMonitor.isConnected else {
            let error = NSError(
                domain: "com.windring.activity", 
                code: -3, 
                userInfo: [NSLocalizedDescriptionKey: "网络连接不可用"]
            )
            print("ActivityService - 网络连接不可用")
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        // 构建端点
        let endpoint = "/app-api/iot/activity/getActivityStates"
        
        // 构建请求参数
        let parameters: [String: Any] = ["date": dateString]
        
        // 构建请求头
        //var headers: [String: String] = ["tenant-id": "1"]
        
        // 获取Authorization Token
//        if let token = AuthService.shared.currentToken?.accessToken, !token.isEmpty {
//            headers["Authorization"] = "Bearer \(token)"
//            print("ActivityService - 添加认证令牌")
//        } else {
//            print("ActivityService - 警告: 无认证令牌")
//        }
        
        return networkManager.requestData(
            endpoint: endpoint,
            method: .get,
            parameters: parameters,
//            headers: headers
        )
        .tryMap { data -> ActivityStateResponse in
                // 打印原始响应数据
                if let responseString = String(data: data, encoding: .utf8) {
                    print("\nActivityService - 原始活动状态响应数据:")
                    print(responseString)
                    
                    // 尝试格式化JSON以便阅读
                    if let jsonObject = try? JSONSerialization.jsonObject(with: data, options: []),
                       let prettyData = try? JSONSerialization.data(withJSONObject: jsonObject, options: [.prettyPrinted, .sortedKeys]),
                       let prettyString = String(data: prettyData, encoding: .utf8) {
                        print("\nActivityService - 格式化活动状态响应JSON:")
                        print(prettyString)
                    }
                }
                
            let decoder = CleanJSONDecoder()
            do {
                return try decoder.decode(ActivityStateResponse.self, from: data)
            } catch {
                print("ActivityService - 解析活动状态数据失败: \(error)")
                throw error
            }
        }
            .tryMap { response in
                if response.code == 0 {
                    let stateData = response.data ?? []
                    
                    // 使用模型的辅助方法打印详细日志
                    print("\nActivityService - 成功获取活动状态数据，共\(stateData.count)条记录")
                    
                    // 打印完整响应
                    if let jsonString = response.toJSONString() {
                        print("\nActivityService - 完整活动状态响应JSON:")
                        print(jsonString)
                    }
                    
                    // 打印部分样本数据
                    if !stateData.isEmpty {
                        let sampleCount = min(3, stateData.count)
                        print("\n活动状态数据样本(前\(sampleCount)条):")
                        for (index, state) in stateData.prefix(sampleCount).enumerated() {
                            print("[\(index+1)] 级别: \(state.title), 状态值: \(state.state), 时间: \(state.time), 半小时索引: \(state.halfHourIndex)")
                        }
                    }
                    
                    return stateData
                } else {
                    let errorMessage = response.msg.isEmpty ? "获取活动状态数据失败" : response.msg
                    print("ActivityService - 服务器返回错误: \(errorMessage)")
                    
                    // 打印错误响应
                    if let jsonString = response.toJSONString() {
                        print("\nActivityService - 错误响应JSON:")
                        print(jsonString)
                    }
                    
                    throw NSError(
                        domain: "com.windring.activity", 
                        code: response.code, 
                        userInfo: [NSLocalizedDescriptionKey: errorMessage]
                    )
                }
            }
        .retry(retryCount)
            .eraseToAnyPublisher()
    }
    
    /// 获取活动状态（异步版本）
    /// - Parameters:
    ///   - date: 日期
    ///   - forceRefresh: 是否强制刷新
    /// - Returns: 活动状态数据数组
    @available(iOS 15.0, *)
    public func getActivityStates(for date: Date, forceRefresh: Bool = false) async throws -> [ActivityStateData] {
        return try await getActivityStates(for: date, forceRefresh: forceRefresh).asyncFirst()
    }
    
    // MARK: - 活动明细接口
    
    /// 获取活动明细
    /// - Parameters:
    ///   - date: 日期
    ///   - forceRefresh: 是否强制刷新
    ///   - retryCount: 失败时重试次数
    /// - Returns: 活动明细数据数组
    public func getActivityDetails(
        for date: Date,
        forceRefresh: Bool = false,
        retryCount: Int = 2
    ) -> AnyPublisher<[ActivityDetailData], Error> {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("ActivityService - 开始获取活动明细数据 - 日期: \(dateString), 强制刷新: \(forceRefresh)")
        
        // 检查网络连接
        guard networkMonitor.isConnected else {
            let error = NSError(
                domain: "com.windring.activity", 
                code: -3, 
                userInfo: [NSLocalizedDescriptionKey: "网络连接不可用"]
            )
            print("ActivityService - 网络连接不可用")
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        // 构建端点
        let endpoint = "/app-api/iot/activity/getActivityDetails"
        
        // 构建请求参数
        let parameters: [String: Any] = ["date": dateString]
        
//        // 构建请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        
//        // 获取Authorization Token
//        if let token = AuthService.shared.currentToken?.accessToken, !token.isEmpty {
//            headers["Authorization"] = "Bearer \(token)"
//            print("ActivityService - 添加认证令牌")
//        } else {
//            print("ActivityService - 警告: 无认证令牌")
//        }
        
        return networkManager.requestData(
            endpoint: endpoint,
            method: .get,
            parameters: parameters,
//            headers: headers
        )
        .tryMap { data -> ActivityDetailResponse in
                // 打印原始响应数据
                if let responseString = String(data: data, encoding: .utf8) {
                    print("\nActivityService - 原始活动明细响应数据:")
                    print(responseString)
                    
                    // 尝试格式化JSON以便阅读
                    if let jsonObject = try? JSONSerialization.jsonObject(with: data, options: []),
                       let prettyData = try? JSONSerialization.data(withJSONObject: jsonObject, options: [.prettyPrinted, .sortedKeys]),
                       let prettyString = String(data: prettyData, encoding: .utf8) {
                        print("\nActivityService - 格式化活动明细响应JSON:")
                        print(prettyString)
                    }
                }
                
            let decoder = CleanJSONDecoder()
            do {
                return try decoder.decode(ActivityDetailResponse.self, from: data)
            } catch {
                print("ActivityService - 解析活动明细数据失败: \(error)")
                throw error
            }
        }
            .tryMap { response in
                if response.code == 0, let detailData = response.data {
                    print("\nActivityService - 成功获取活动明细数据 - 共 \(detailData.count) 条记录")
                    
                    // 打印完整响应
                    if let jsonString = response.toJSONString() {
                        print("\nActivityService - 完整活动明细响应JSON:")
                        print(jsonString)
                    }
                    
                    // 打印数据样本
                    if !detailData.isEmpty {
                        let sampleSize = min(3, detailData.count)
                        print("\n活动明细数据样本:")
                        for i in 0..<sampleSize {
                            let detail = detailData[i]
                            print("[\(i+1)] 时间: \(detail.time), 步数: \(detail.steps)")
                        }
                    }
                    
                    return detailData
                } else {
                    let errorMessage = response.msg.isEmpty ? "获取活动明细数据失败" : response.msg
                    print("ActivityService - 服务器返回错误: \(errorMessage)")
                    
                    throw NSError(
                        domain: "com.windring.activity", 
                        code: response.code, 
                        userInfo: [NSLocalizedDescriptionKey: errorMessage]
                    )
                }
            }
        .retry(retryCount)
            .eraseToAnyPublisher()
    }
    
    /// 获取活动明细（异步版本）
    /// - Parameters:
    ///   - date: 日期
    ///   - forceRefresh: 是否强制刷新
    /// - Returns: 活动明细数据数组
    @available(iOS 15.0, *)
    public func getActivityDetails(for date: Date, forceRefresh: Bool = false) async throws -> [ActivityDetailData] {
        return try await getActivityDetails(for: date, forceRefresh: forceRefresh).asyncFirst()
    }
    
    // MARK: - 活动卡路里接口
    
    /// 获取活动卡路里数据
    /// - Parameters:
    ///   - date: 日期
    ///   - forceRefresh: 是否强制刷新，不使用缓存
    ///   - retryCount: 失败时重试次数
    /// - Returns: 活动卡路里数据
    public func getActivityCalories(
        for date: Date,
        forceRefresh: Bool = false,
        retryCount: Int = 2
    ) -> AnyPublisher<[ActivityCalorieData], Error> {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("ActivityService - 开始获取活动卡路里数据 - 日期: \(dateString), 强制刷新: \(forceRefresh)")
        
        // 检查网络连接
        guard networkMonitor.isConnected else {
            let error = NSError(
                domain: "com.windring.activity", 
                code: -3, 
                userInfo: [NSLocalizedDescriptionKey: "网络连接不可用"]
            )
            print("ActivityService - 网络连接不可用")
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        // 构建端点
        let endpoint = "/app-api/iot/activity/getActivityCalories"
        
        // 构建请求参数
        let parameters: [String: Any] = ["date": dateString]
        
//        // 构建请求头
//        var headers: [String: String] = ["tenant-id": "1"]
//        
//        // 获取Authorization Token
//        if let token = AuthService.shared.currentToken?.accessToken, !token.isEmpty {
//            headers["Authorization"] = "Bearer \(token)"
//        }
        
        return networkManager.requestData(
            endpoint: endpoint,
            method: .get,
            parameters: parameters,
//            headers: headers
        )
        .tryMap { data -> ActivityCalorieResponse in
            let decoder = CleanJSONDecoder()
            do {
                return try decoder.decode(ActivityCalorieResponse.self, from: data)
            } catch {
                print("ActivityService - 解析活动卡路里数据失败: \(error)")
                throw error
            }
        }
        .tryMap { response -> [ActivityCalorieData] in
            if response.code == 0, let data = response.data {
                print("ActivityService - 成功获取活动卡路里数据，数据点数量: \(data.count)")
                return data
            } else {
                print("ActivityService - API返回错误码: \(response.code), 信息: \(response.msg)")
                throw NSError(
                    domain: "com.windring.activity", 
                    code: response.code, 
                    userInfo: [NSLocalizedDescriptionKey: response.msg]
                )
            }
        }
        .retry(retryCount) // 失败时自动重试
        .eraseToAnyPublisher()
    }
    
    /// 获取活动卡路里数据（异步版本）
    /// - Parameters:
    ///   - date: 日期
    ///   - forceRefresh: 是否强制刷新
    /// - Returns: 活动卡路里数据
    @available(iOS 13.0, *)
    public func getActivityCalories(for date: Date, forceRefresh: Bool = false) async throws -> [ActivityCalorieData] {
        return try await getActivityCalories(for: date, forceRefresh: forceRefresh).asyncFirst()
    }
    
    

    public func getActivityAvgState(for date: Date) -> AnyPublisher<ActivityAvgStateData, Error> {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        let endpoint = "/app-api/iot/activity/getActivityAvgState"
        let parameters: [String: Any] = ["date": dateString]
        return networkManager.request(
            endpoint: endpoint,
            method: .get,
            parameters: parameters,
            responseType: ActivityAvgStateData.self
        )
        .mapError { $0 as Error }
        .eraseToAnyPublisher()
    }
}

// MARK: - Publisher扩展，用于支持异步/等待
@available(iOS 15.0, *)
extension Publisher {
    func asyncFirst() async throws -> Output {
        try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            var finished = false
            
            cancellable = self.sink(
                receiveCompletion: { completion in
                    switch completion {
                    case .failure(let error):
                        continuation.resume(throwing: error)
                    case .finished:
                        if !finished {
                            continuation.resume(throwing: NSError(
                                domain: "com.windring",
                                code: -1,
                                userInfo: [NSLocalizedDescriptionKey: "Publisher完成但没有发出值"]
                            ))
                        }
                    }
                    cancellable?.cancel()
                },
                receiveValue: { value in
                    finished = true
                    continuation.resume(returning: value)
                    cancellable?.cancel()
                }
            )
        }
    }
} 
