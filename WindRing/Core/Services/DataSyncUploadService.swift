//
//  DataSyncUploadService.swift
//  WindRing
//
//  Created by zx on 2025/5/23.
//

import Combine
import Foundation

final class DataSyncUploadService: ObservableObject {
    static let shared = DataSyncUploadService()
//    private let coreData = StorageManager.shared
    private let deviceService = WindRingDeviceService.shared
    private let authService = AuthService.shared
    private let networkMonitor = NetworkMonitor.shared
    private let goMoreService = GoMoreService.shared
    
    private var cancellables = Set<AnyCancellable>()
    private let queue = DispatchQueue(label: "com.wring.sync", attributes: .concurrent)
    private var timer: Timer?
    init() {
        observeDeviceConnection()
        NotificationCenter.default.addObserver(
            forName: .goMoreSupportUpdated,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self else { return }
            if let support = notification.object as? Bool {
                // 处理 support 数据
                print("🔔 收到支持信息更新：\(support)")
//                if support {
                    Task {
                        print("开始上传睡眠数据")
//                        await self.uploadSleep(support: support)
                    }
//                }
                
            }
        }
    }
    private func observeDeviceConnection() {
        deviceService.$connectionState
            .receive(on: RunLoop.main)
            .sink { [weak self] state in
                guard let self else { return }
                if state.isConnected {
                    print("📲 设备已连接，启动自动上传服务...")
//                    NotificationCenter.default.post(name: .deviceConnected, object: nil)
                    self.start()
                } else {
                    print("📴 设备断开连接，停止上传")
                    self.stopTimer()
                }
            }
            .store(in: &cancellables)
    }

    func start() {
        Task {
            print("开始今日定时器")
            startTodayAutoTimer()
            
            print("开始上传睡眠数据")
            await uploadSleep(support: false)
            
            print("开始同步 14 天数据")
            await syncPast14Days()
            
           
            
            
        }
        
//        Task {
//            print("开始同步 14 天数据")
//            await syncPast14Days()
//            print("开始上传睡眠数据")
//            await self.uploadSleep(support: false)
//            
//        }
//        print("开始今日定时器")
//        startTodayAutoTimer()

    }

    private func startTodayAutoTimer(){
        stopTimer()
       
        timer = Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { [weak self] _ in
            guard self?.deviceService.connectionState.isConnected ?? false else { return }
            Task {
                await self?.syncOneDay(date: Date())
            }
        }
        RunLoop.main.add(timer!, forMode: .common)
        timer?.fire() // 立即执行一次
    }

    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    private func syncPast14Days() async {
        let calendar = Calendar.current
        let dates = (1...14).compactMap { calendar.date(byAdding: .day, value: -$0, to: Date()) }
        let maxConcurrentTasks = 3

        var dateIterator = dates.makeIterator()

        await withTaskGroup(of: Void.self) { group in
            // 启动最多 maxConcurrentTasks 个初始任务
            for _ in 0..<maxConcurrentTasks {
                if let date = dateIterator.next() {
                    group.addTask {
                        await self.syncOneDay(date: date)
                    }
                }
            }

            // 每当一个任务完成，就启动下一个任务，确保并发数恒定
            for await _ in group {
                if let nextDate = dateIterator.next() {
                    group.addTask {
                        await self.syncOneDay(date: nextDate)
                    }
                }
            }
        }

        print("✅ 所有历史数据采集和上传完成")
    }
    
    private func syncOneDay(date: Date) async {
        guard deviceService.connectionState.isConnected else {
            print("❌ \(date.string()) 设备未连接，跳过同步")
            return
        }
        
        let offset = Calendar.current.dateComponents([.day], from: date, to: Date()).day ?? 0
        
        do {
            print("开始同步\(offset)天前的血氧数据")
        await self.uploadBloodOxygen(for: date, dayOffset: offset)
        
            print("开始同步\(offset)天前的运动数据")
        await self.fetchAndUpload(for: date)

            print("开始同步\(offset)天前的心率数据")
        await self.uploadHeartRateData(for: date, dayOffset: offset)
            
            print("开始同步\(offset)天前的HRV数据")
        await self.uploadTimingHRVData(for: date, dayOffset: offset)
        
            print("开始同步\(offset)天前的体温数据")
        await self.uploadSleepTemperature(for: date, dayOffset: offset)
        
        if Calendar.current.isDateInToday(date) {
            DispatchQueue.main.async {
                NotificationCenter.default.post(name: .didSyncTodayData, object: nil)
            }
                print("✅ 今日数据同步完成，已发送刷新通知")
            }
        } catch {
            print("❌ \(date.string()) 数据同步失败: \(error.localizedDescription)")
        }
    }
    
    private func fetchAndUpload(for date: Date) async {
        guard deviceService.connectionState.isConnected else {
            print("❌ \(date.string()) 设备未连接，跳过")
            return
        }
        
        guard networkMonitor.isConnected else {
            print("❌ \(date.string()) 网络不可用，跳过")
            return
        }
        
        guard let token = authService.currentToken?.accessToken else {
            print("❌ \(date.string()) 未登录，跳过")
            return
        }

        // 1. 检查是否已上传
        if let existing = PlistManager.shared.get(for: .training, date: date, as: StepModel.self), existing.uploaded {
            print("📦 \(date.string()) 数据已上传，跳过")
            return
        }

        // 2. 获取运动数据，使用信号量确保同步完成
        let semaphore = DispatchSemaphore(value: 0)
        var exerciseRecord: StepModel?
        var stepDetails: [ActivityDetailData]?
        var fetchError: Error?

        CRPSmartRingManage.shared.getHistoricalExerciseStats(date) { res in
            switch res {
            case .success(let record):
                exerciseRecord = record
                // 获取步数明细
                CRPSmartRingManage.shared.getHalfHourlyStepStats(day: date) { details, error in
                    if let error = error {
                        fetchError = error
                        print("❌ \(date.string()) 获取步数明细失败: \(error)")
                    } else {
                        stepDetails = details
                    }
                    semaphore.signal()
                }
            case .failure(let error):
                fetchError = error
                print("❌ \(date.string()) 获取运动摘要失败: \(error)")
                semaphore.signal()
            }
        }

        // 等待数据获取完成
        _ = semaphore.wait(timeout: .now() + 15)

        // 3. 检查数据完整性
        guard let record = exerciseRecord, let details = stepDetails, fetchError == nil else {
            print("❌ \(date.string()) 数据获取失败或不完整")
            return
        }

        // 4. 构建上传数据
        let uploadData: [String: Any] = [
            "date": date.string(),
            "steps": record.steps,
            "calories": record.calory,
            "time": record.time,
            "distance": record.distance.double / 100.0,
            "records": details.map { ["steps": $0.steps, "time": $0.time] }
        ]
        
        // 5. 上传数据
        let uploadSemaphore = DispatchSemaphore(value: 0)
        var uploadSuccess = false
                        
        self.sendActivityData(uploadData) { success, error in
            uploadSuccess = success
                            if success {
                                if !date.isInToday {
                                    PlistManager.shared.markDataUploaded(for: .training, date: date)
                                }
                                print("✅ 成功上传 \(date.string()) 的运动数据")
                            } else if let error = error {
                                print("❌ \(date.string()) 上传失败: \(error.localizedDescription)")
            }
            uploadSemaphore.signal()
        }

        _ = uploadSemaphore.wait(timeout: .now() + 15)
        
        if !uploadSuccess {
            print("❌ \(date.string()) 上传超时或失败")
            }
    }
    
    private func sendActivityData(_ body: [String: Any], completion: @escaping (Bool, Error?) -> Void) {
        NetworkManager.shared.requestData(
            endpoint: "/app-api/iot/activity/upload/data",
            method: .post,
            parameters: body
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { result in
            switch result {
            case .failure(let error):
                completion(false, error)
            case .finished:
                break
            }
        }, receiveValue: { data in
            if let responseString = String(data: data, encoding: .utf8) {
                print("📤 活动数据上传响应: \(responseString)")
            }
            completion(true, nil)
        })
        .store(in: &cancellables)
    }

    /// 上传活动数据到服务器
    /// 根据后端API要求的格式上传活动数据和步数明细
    /// - Parameters:
    ///   - day: 指定天数（0表示今天，1表示昨天，以此类推）
    ///   - completion: 完成回调
    public func uploadActivityData(dayDate: Date,day:Int, completion: @escaping (Bool, Error?) -> Void) {
        print("开始上传第\(day)天的活动数据...")
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            let error = NSError(domain: "ActivityDataUpload", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接，无法获取活动数据"])
            completion(false, error)
            return
        }
        
        // 检查网络连接状态
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "ActivityDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        // 获取用户认证信息
        guard let token = authService.currentToken?.accessToken else {
            let error = NSError(domain: "ActivityDataUpload", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])
            completion(false, error)
            return
        }
        
        // 首先获取活动汇总数据
        CRPSmartRingManage.shared.getHistoricalExerciseStats(dayDate) { [weak self] result in
            guard let self = self else { return }
            switch result {
            case .success(let record):
                
                // 然后获取步数明细数据
                self.fetchStepDetailData(dayDate: dayDate, day: day) { stepDetails, detailError in
                    if let detailError = detailError {
                        print("获取第\(day)天步数明细数据失败: \(detailError.localizedDescription)")
                        completion(false, detailError)
                        return
                    }
                    let dateFormatter = DateFormatter()
                    dateFormatter.dateFormat = "yyyy-MM-dd"
                    let dateString = dateFormatter.string(from: dayDate)
                    
                    // 构建API要求的JSON请求体
                    var requestBody: [String: Any] = [
                        "date": dateString,
                        "steps": record.steps,                     // 步数
                        "calories": record.calory,                   // 卡路里(千卡)
                        "time": record.steps / 120,         // 活动时长(秒)，SDK返回的是分钟，需转换为秒
                        "distance": record.distance / 100         // 距离(米)，SDK返回的是厘米，需转换为米
                    ]
                    
                    // 添加步数明细记录
                    if let details = stepDetails, !details.isEmpty {
                        let records = details.map { detail -> [String: Any] in
                            return [
                                "steps": detail.steps,
                                "time": detail.time  // 毫秒时间戳
                            ]
                        }
                        requestBody["records"] = records
                    } else {
                        requestBody["records"] = []
                    }
//                    completion(true, nil)
                    // 发送请求到服务器
                    self.sendActivityDataToServer(requestBody: requestBody, token: token, completion: completion)
                }
            case .failure(let error):
                print("获取第\(day)天活动汇总数据失败: \(error)")
                let error = NSError(domain: "ActivityDataUpload", code: 501,
                                  userInfo: [NSLocalizedDescriptionKey: "获取活动汇总数据失败: \(error)"])
                completion(false, error)
                break
            }
            
            
        }
    }
    
    
    
    /// 获取步数详细数据
    /// 用于获取一天内的步数明细数据
    /// - Parameters:
    ///   - day: 指定天数（0表示今天，1表示昨天，以此类推）
    ///   - completion: 完成回调
    public func fetchStepDetailData(dayDate:Date,day: Int, completion: @escaping ([ActivityDetailData]?, Error?) -> Void) {
        // 记录开始时间，用于跟踪数据获取流程
        let startTime = Date()
       
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: dayDate)
        
        print("📅 开始获取 \(dateString) (day=\(day)) 的步数明细数据，开始时间: \(startTime)")
        
        print("获取第\(day)天的步数明细数据...目标日期: \(dateString)")
        
        // 检查设备连接状态
        guard WindRingDeviceService.shared.connectionState.isConnected else {
            let error = NSError(domain: "RawDataUpload", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接，无法获取步数明细数据"])
            print("❌ 设备未连接，无法获取步数明细数据")
            completion(nil, error)
            return
        }
        
        // 使用SDK获取半小时步数明细数据，确保day参数正确
        CRPSmartRingManage.shared.getHalfHourlyStepStats(day: dayDate) { record, error in
            // 计算数据获取耗时
            let processingTime = Date().timeIntervalSince(startTime)
            print("⏱️ 步数明细数据SDK调用耗时: \(String(format: "%.2f", processingTime))秒")
            
            completion(record, error)
        }

    }
    
    
    private func uploadTimingHRVData(for date: Date, dayOffset: Int) async  {
        if let existing = PlistManager.shared.get(for: .timingHRV, date: date, as: HRVDataModel.self), existing.uploaded {
            print("📦 \(date.string()) 数据已上传，跳过")
            return
        }
        
        if let timingHRV =  self.getTimingHRV(for: date) {
            // 构建API要求的JSON请求体
            let requestBody: [String: Any] = [
                "date": date.string(),
                "records": (timingHRV.heartRate).map { detail -> [String: Any] in
                    return [
                        "hrv": detail.hrv,
                        "time": detail.time  // 毫秒时间戳
                    ]
                }
            ]
            
            // 发送请求到服务器
            self.sendHRVDataToServer(requestBody: requestBody, completion: { bool, error in
                if bool{
                    if !date.isInToday{
                        PlistManager.shared.markDataUploaded(for: .timingHRV, date: date)
                    }
                    
                }
            })
        }
    }
    
    private func uploadHeartRateData(for date: Date, dayOffset: Int) async  {
        guard deviceService.connectionState.isConnected else {
            print("❌ \(date.string()) 设备未连接，跳过")
            return
        }
        
        guard networkMonitor.isConnected else {
            print("❌ \(date.string()) 网络不可用，跳过")
            return
        }
        
        guard let token = authService.currentToken?.accessToken else {
            print("❌ \(date.string()) 未登录，跳过")
            return
        }

        if let existing = PlistManager.shared.get(for: .timingHeartRate, date: date, as: HeartRateModel.self), existing.uploaded {
            print("📦 \(date.string()) 数据已上传，跳过")
            return
        }
        if let heartRate =  self.getTimingHeartRate(for: date) {
            let requestBody: [String: Any] = [
                "date": date.string(),
                "records": (heartRate.heartRate ).map { detail -> [String: Any] in
                    return [
                        "hearts": detail.hearts,
                        "time": detail.time  // 毫秒时间戳
                    ]
                }
            ]
            self.sendHeartRateDataToServer(requestBody: requestBody) { bool, error in
                if bool{
                    if !date.isInToday{
                        PlistManager.shared.markDataUploaded(for: .timingHeartRate, date: date)
                    }
                    
                }
            }
        }
        
    }
    func uploadHeartRateData(for date: Date,completion: @escaping (Bool) -> Void) {
        guard deviceService.connectionState.isConnected else {
            print("❌ \(date.string()) 设备未连接，跳过")
            return
        }
        
        guard networkMonitor.isConnected else {
            print("❌ \(date.string()) 网络不可用，跳过")
            return
        }
        
        guard (authService.currentToken?.accessToken) != nil else {
            print("❌ \(date.string()) 未登录，跳过")
            return
        }

        if let existing = PlistManager.shared.get(for: .timingHeartRate, date: date, as: HeartRateModel.self), existing.uploaded {
            print("📦 \(date.string()) 数据已上传，跳过")
            return
        }
        CRPSmartRingManage.shared.getTimingHeartRate(day: date) { model, error in
            if let heartRate =  model {
                let requestBody: [String: Any] = [
                    "date": date.string(),
                    "records": (heartRate.heartRate ).map { detail -> [String: Any] in
                        return [
                            "hearts": detail.hearts,
                            "time": detail.time  // 毫秒时间戳
                        ]
                    }
                ]
                self.sendHeartRateDataToServer(requestBody: requestBody) { bool, error in
                    if bool{
                        if !date.isInToday{
                            PlistManager.shared.markDataUploaded(for: .timingHeartRate, date: date)
                        }
                        completion(true)
                    }else{
                        completion(false)
                    }
                    
                }
            }
            
//            if let model = model {
//                result = model
//            }
//            semaphore.signal()
        }
//        if let heartRate =  self.getTimingHeartRate(for: date) {
//            
//        }
        
    }
    func uploadSleep(support:Bool) async{
        let bool = RingModelEnum.init(rawValue: deviceService.deviceInfo?.localName ?? "")?.isHighEnd ?? false
        if bool {
                // 设备支持GoMore算法
                print("设备支持GoMore算法，使用GoMore高级睡眠算法获取数据")
                
                CRPSmartRingManage.shared.getGoMoreSleepDataList()
                
                // 声明一个可变的观察者变量
                var observerRef: NSObjectProtocol?
                
                // 创建一个通知观察者，等待睡眠ID列表返回
                observerRef = NotificationCenter.default.addObserver(
                    forName: .receivedGoMoreSleepIdsNotification,
                    object: nil,
                    queue: .main
                ) { [weak self] notification in
                    guard let self = self else { return }
                    if let sleepRecords = notification.object as? [GoMoreSleepRecord] {
                        print("收到GoMore睡眠ID列表通知，ID数量: \(sleepRecords.count)")
                        if sleepRecords.count == 0 {
                            print("⚠️ 没有获取到GoMore睡眠数据ID，列表为空")
                            if let observer = observerRef {
                                NotificationCenter.default.removeObserver(observer)
                            }
                            return
                        }
                        if sleepRecords.count == 0 {
                            print("⚠️ 没有找到任何睡眠ID，无法获取数据")
                            if let observer = observerRef {
                                NotificationCenter.default.removeObserver(observer)
                            }
                            return
                        }
                        print("将处理 \(sleepRecords.count) 个睡眠ID")
                        Task {
                            await self.syncAllSleepData(gomoreSleep: sleepRecords)
                        }
                    }
                }
        }else{
            // 设备不支持GoMore算法，使用基础算法
            print("设备不支持GoMore算法，使用基础睡眠算法获取数据")
            let calendar = Calendar.current
            let dates = (1...14).compactMap { calendar.date(byAdding: .day, value: -$0, to: Date()) }
//            let maxConcurrentTasks = 3

//            var dateIterator = dates.makeIterator()
            for date in dates {
                let semaphore = DispatchSemaphore(value: 0)
                Task {
                    await self.uploadNormalSleep(for: date)
                    semaphore.signal()
                }
                semaphore.wait() // 阻塞直到当前任务完成
            }
//            await withTaskGroup(of: Void.self) { group in
//                // 启动最多 maxConcurrentTasks 个初始任务
//                for _ in 0..<maxConcurrentTasks {
//                    if let date = dateIterator.next() {
//                        group.addTask {
//                            await self.uploadNormalSleep(for: date)
//                        }
//                    }
//                }
//
//                // 每当一个任务完成，就启动下一个任务，确保并发数恒定
//                for await _ in group {
//                    if let nextDate = dateIterator.next() {
//                        group.addTask {
//                            await self.uploadNormalSleep(for: nextDate)
//                        }
//                    }
//                }
//            }
            
        }
        
    }
    ///获取睡眠评分
    func calculateSleepScore(totalTime: Int, deep: Int, light: Int, rem: Int, awake: Int, awakeCount: Int) -> Int {
        var score1 = Int(Float(totalTime) / 480.0 * 100)
        let sleepSum = deep + light + rem + awake
        var score2 = sleepSum > 0 ? Int(Float(deep + light + rem) / Float(sleepSum) * 100) : 0
        var score3 = Int(Float(deep) / 120.0 * 100)
        var score4 = 100 - (awakeCount * 10)

        // 限制各项评分上下限
        score1 = min(score1, 100)
        score2 = min(score2, 100)
        score3 = min(score3, 100)
        score4 = max(score4, 0)

        return (score1 + score2 + score3 + score4) / 4
    }
    
    func syncAllSleepData(gomoreSleep: [GoMoreSleepRecord]) async {
        for sleep in gomoreSleep {
            print("🔍 开始处理 sleepId: \(sleep.id)")
            let dicts : [String: Any] = (PlistManager.shared.get(for: .sleepRecord) ?? [:]) as! [String : Any]
//            dict?.allValues
            for (key, value) in dicts {
                if let innerDict = value as? [String: Any],
                   let id = innerDict["id"] {
                    print("Found id in \(key): \(id)")
                }
            }
            let date = Date(timeIntervalSince1970: TimeInterval(sleep.startTime))
            if (PlistManager.shared.get(for: .sleepRecord, date: date, as: SleepRecordModel.self) != nil) {
                print("📦 \(date.string()) 数据已上传，跳过")
                return
            }
            
            guard let sleepDetail =  getSleepDetail(sleepId: sleep.id) else {
                print("❌ 获取 Detail 失败: \(sleep.id)")
                return
            }

            guard let segmentation =  getSleepSegmentation(date: date, sleepId: sleep.id) else {
                print("❌ 获取 Segmentation 失败: \(sleep.id)")
                return
            }

            
            PlistManager.shared.save(data: sleepDetail, for: .goMoreSleepData, date: date)
            PlistManager.shared.save(data: segmentation, for: .sleepRecord, date: date)
            await uploadGoMoreSleep(date: date, sleepData: segmentation, goMoreSleepData: sleepDetail)
//            saveSleepData(detail: detail, segmentation: segmentation)
            print("✅ 已保存 sleepId: \(sleep.id)")
        }

        print("🎉 所有 sleepId 已处理完成")
    }
//    var sleepDetail: GoMoreSleepDataModel?
//    var sleepSegment: SleepRecordModel?
    func getSleepDetail(sleepId: Int) -> GoMoreSleepDataModel? {
        let semaphore = DispatchSemaphore(value: 0)
        var result: GoMoreSleepDataModel?

        CRPSmartRingManage.shared.getGoMoreSleepDataDetail(id: sleepId) { model, error in
            if error == .none {
                debugPrint("getGoMoreSleepSegmentationData:\(model)")
                let sleepModel = GoMoreSleepDataModel.init(type: model.type, startTime: model.startTime, endTime: model.endTime, totalTime: model.totalTime, sleepEfficiency: model.sleepEfficiency, sleepScore: model.sleepScore)
                result = sleepModel
            }
            semaphore.signal()
        }

        // 等待回调完成（阻塞当前线程）
        _ = semaphore.wait(timeout: .now() + 15)  // 等待最多10秒，防止死锁

        return result
    }
//    func getSleepDetail(sleepId: Int) -> GoMoreSleepDataModel? {
//        var result: GoMoreSleepDataModel?
////        let semaphore = DispatchSemaphore(value: 0)
//        CRPSmartRingManage.shared.getGoMoreSleepDataDetail(id: sleepId) { model, error in
//            if error == .none {
//                debugPrint("getGoMoreSleepDataDetail:\(model)")
//                let sleepModel = GoMoreSleepDataModel.init(type: model.type, startTime: model.startTime, endTime: model.endTime, totalTime: model.totalTime, sleepEfficiency: model.sleepEfficiency, sleepScore: model.sleepScore)
//                result = sleepModel
//            } else {
//
//            }
////            semaphore.signal()
//        }
////        semaphore.wait()
//        return result
//    }
    
    func getSleepSegmentation(date:Date,sleepId: Int) -> SleepRecordModel? {
        let semaphore = DispatchSemaphore(value: 0)
        var result: SleepRecordModel?
        CRPSmartRingManage.shared.getGoMoreSleepSegmentationData(id: sleepId) { model, error in
            if error == nil {
                PlistManager.shared.save(data: model, for: .sleepRecord, date: date)
                result = model
            }
            semaphore.signal()
        }
//        CRPSmartRingManage.shared.getGoMoreSleepSegmentationData(id: sleepId) { model, error in
            
//            if error == .none {
//                debugPrint("getGoMoreSleepSegmentationData:\(model)")
//                let data = try JSONSerialization.data(withJSONObject: model.detail, options: [])
//                let detail = try CleanJSONDecoder().decode([SleepRecordDetailModel].self, from: data)
////                SleepRecordDetailModel
//                let segmentModel = SleepRecordModel.init(day: 1, deep: model.deep, light: model.light, rem: model.rem, detail: model.detail)
//                result = segmentModel
//            }
            
//        }

        // 等待回调完成（阻塞当前线程）
        _ = semaphore.wait(timeout: .now() + 15)  // 等待最多10秒，防止死锁

        return result
    }
    

   
    func uploadSleepTemperature(for date: Date, dayOffset: Int) async {
        if let existing = PlistManager.shared.get(for: .sleepTemperature, date: date, as: SleepTemperatureDataModel.self), existing.uploaded {
            print("📦 \(date.string()) 数据已上传，跳过")
            return
        }
        debugPrint("开始执行获取睡眠体温数据")
        if let sleepTemperature =  self.getSleepTemperature(for: date){
            debugPrint("获取到了睡眠体温数据，正在准备上传")
            var averageTemperature: Double = 0
            var maxTemperature: Double = 0
            var minTemperature: Double = 100 // 初始设置一个很高的值，确保会被更新
            
            // 计算平均值、最大值、最小值
//            if !sleepTemperature.isEmpty {
            let records = sleepTemperature.sleepTemperatureData
//            let totalValue = records.reduce(0.0) { $0 + $1.value }
            let validRecords = sleepTemperature.sleepTemperatureData.filter { $0.value != 0 }
            let totalValue = validRecords.reduce(0.0) { $0 + $1.value }
            
            averageTemperature = totalValue / Double(validRecords.count)
                
            maxTemperature = validRecords.map { $0.value }.max() ?? 0
            minTemperature = validRecords.map { $0.value }.min() ?? 0
//            }
            
            // 准备温度记录数组
            var temperatureRecords: [TemperatureRecord] = []
            for record in records {
                let temperatureRecord = TemperatureRecord(
                    temperature: record.value,
                    timestamp: record.time
                )
                temperatureRecords.append(temperatureRecord)
            }
            let uploadData = TemperatureUploadData(
                date: date.string(),
                averageTemperature: averageTemperature,
                maxTemperature: maxTemperature,
                minTemperature: minTemperature,
                records: temperatureRecords
            )
            uploadTemperatureData(data: uploadData) { success, error in
                if success {
                    if !date.isInToday{
                        PlistManager.shared.markDataUploaded(for: .sleepTemperature, date: date)
                    }
                    print("成功上传\(date.string())的\(sleepTemperature.sleepTemperatureData.count)条睡眠体温数据")
                } else {
                    print("上传\(date.string())的睡眠体温数据失败: \(error?.localizedDescription ?? "未知错误")")

                }
            }
            
        }
       
    }
    ///上传普通睡眠
    func uploadNormalSleep(for date: Date) async {
        debugPrint("🚀 开始执行获取睡眠数据：\(date.string())")
        do {
            if let sleepData =  self.getSleep(for: date),sleepData.detail.count > 0{
                let data = try JSONSerialization.data(withJSONObject: sleepData.detail, options: [])
//                let detail = try CleanJSONDecoder().decode([SleepRecordData].self, from: data)
                let recordDatas = parseSleepRecords(from: sleepData.detail, baseDate: date)
                let startDate = recordDatas.first?.startTime.int ?? 0 //sleepData.detail.first?["start"]?.int ?? 0
                let endDate = recordDatas.last?.endTime.int ?? 0
                let sleepTime = sleepData.deep + sleepData.light + sleepData.rem

                ///低端计算评分没有awake和awakeCount
                let sleepScore = calculateSleepScore(totalTime: sleepTime, deep: sleepData.deep, light: sleepData.light, rem: sleepData.rem, awake: 0, awakeCount: 0)
                let uploadData: SleepUploadData = SleepUploadData.init(date: date.string(), deep: sleepData.deep, light: sleepData.light, rem: sleepData.rem, type: 0, startDate: startDate, endDate: endDate, sleepTime: Float(sleepTime), sleepEfficiency: 1.0, score: Float(sleepScore), records: recordDatas)
                await self.uploadSleepData(data: uploadData) { bool, error in
                    if bool {
                        PlistManager.shared.markDataUploaded(for: .sleepTemperature, date: date)
                        print("成功上传\(date.string())的条睡眠数据")
                    } else {
                        print("上传\(date.string())的睡眠数据失败: \(error?.localizedDescription ?? "未知错误")")

                    }
                }
            }
           } catch {
               print("❌ \(date.string()) 睡眠数据上传失败: \(error)")
           }
    }
    ///上传高端睡眠
    func uploadGoMoreSleep(date:Date,sleepData: SleepRecordModel,goMoreSleepData: GoMoreSleepDataModel) async {
        debugPrint("🚀 开始执行获取睡眠数据：\(date.string())")
    
        if sleepData.detail.count > 0{
            let recordDatas = parseSleepRecords(from: sleepData.detail, baseDate: date)
//                let data = try JSONSerialization.data(withJSONObject: sleepData.detail, options: [])
//                let detail = try CleanJSONDecoder().decode([SleepRecordDetailModel].self, from: data)
//                let recordDatas:[SleepRecordData] = detail.map { recordData in
//                    let startTimestamp = timeStringToTimestamp(base: date, timeStr: recordData.start)
//                    let endTimestamp = timeStringToTimestamp(base: date, timeStr: recordData.end)
//                    let data = SleepRecordData.init(type: recordData.type, total: recordData.total, startTime: startTimestamp?.description ?? "", endTime: endTimestamp?.description ?? "")
//                    return data
//                }
            let startDate = goMoreSleepData.startTime * 1000
            let endDate = goMoreSleepData.endTime * 1000
            let sleepTime = sleepData.deep + sleepData.light + sleepData.rem
            ///低端计算评分没有awake和awakeCount
            let sleepScore = goMoreSleepData.sleepScore
            let uploadData: SleepUploadData = SleepUploadData.init(date: date.string(), deep: sleepData.deep, light: sleepData.light, rem: sleepData.rem, type:goMoreSleepData.type, startDate: startDate, endDate: endDate, sleepTime: Float(sleepTime), sleepEfficiency: goMoreSleepData.sleepEfficiency, score: Float(sleepScore), records: recordDatas)
            await self.uploadSleepData(data: uploadData) { bool, error in
                if bool {
                    PlistManager.shared.markDataUploaded(for: .goMoreSleepData, date: date)
                    PlistManager.shared.markDataUploaded(for: .sleepRecord, date: date)
                    print("成功上传\(date.string())的睡眠数据")
                } else {
                    print("上传\(date.string())的睡眠数据失败: \(error?.localizedDescription ?? "未知错误")")

                }
            }
        }
 
    }
    func parseSleepRecords(from raw: [[String: String]], baseDate: Date) -> [SleepRecordData] {
        var result: [SleepRecordData] = []
        var isYesterday = false
        var previousMinutes: Int? = nil
        let calendar = Calendar.current

        for dict in raw {
            guard let startStr = dict["start"],
                  let endStr = dict["end"],
                  let typeStr = dict["type"],
                  let totalStr = dict["total"],
                  let type = Int(typeStr),
                  let total = Int(totalStr) else {
                continue
            }

            func timeToMinutes(_ time: String) -> Int? {
                let parts = time.split(separator: ":").map { Int($0) ?? 0 }
                guard parts.count == 2 else { return nil }
                return parts[0] * 60 + parts[1]
            }

            guard let startM = timeToMinutes(startStr),
                  let endM = timeToMinutes(endStr) else {
                continue
            }

            // 判断是否跨天（小于上一条，说明过了0点）
            if let previous = previousMinutes, startM < previous {
                isYesterday = false
            } else if previousMinutes == nil && startM > 720 {
                // 第一条就大于 12 点（720 分钟），说明是昨天的
                isYesterday = true
            }

            previousMinutes = startM

            // 计算正确的日期
            let date = isYesterday ? calendar.date(byAdding: .day, value: -1, to: baseDate)! : baseDate

            func timestamp(fromMinutes minutes: Int, date: Date) -> Int64 {
                var comps = calendar.dateComponents([.year, .month, .day], from: date)
                comps.hour = minutes / 60
                comps.minute = minutes % 60
                let finalDate = calendar.date(from: comps)!
                return Int64(finalDate.timeIntervalSince1970 * 1000)
            }

            let record = SleepRecordData(
                type: type,
                total: total,
                startTime: timestamp(fromMinutes: startM, date: date).description,
                endTime: timestamp(fromMinutes: endM, date: date).description
            )

            result.append(record)
        }

        return result
    }

    func timeStringToTimestamp(base: Date, timeStr: String) -> Int64? {
        let components = timeStr.split(separator: ":").map { Int($0) ?? 0 }
        guard components.count == 2 else { return nil }

        var calendar = Calendar.current
        calendar.timeZone = TimeZone.current
        let baseComponents = calendar.dateComponents([.year, .month, .day], from: base)

        var dateComponents = DateComponents()
        dateComponents.year = baseComponents.year
        dateComponents.month = baseComponents.month
        dateComponents.day = baseComponents.day
        dateComponents.hour = components[0]
        dateComponents.minute = components[1]

        guard let finalDate = calendar.date(from: dateComponents) else { return nil }

        return Int64(finalDate.timeIntervalSince1970 * 1000)
    }
    
    func uploadBloodOxygen(for date: Date, dayOffset: Int) async {
        debugPrint("🚀 开始执行获取血氧数据：\(date.string())")
        // 1. 先尝试从 plist 读取数据
        if let existing = PlistManager.shared.get(for: .timingO2Record, date: date, as: TimingO2RecordDataModel.self), existing.uploaded {
            print("📦 \(date.string()) 数据已上传，跳过")
            return
        }


        // 2. 若本地不存在，从设备获取并保存
        guard let fetchedModel = self.fetchTimingO2(for: date) else {
            debugPrint("❌ 无法获取血氧数据")
            return
        }
        let uploadData = TimingO2RecordBloodOxygenUploadData(
            date: date.string(),
            records: fetchedModel.timingO2Record
        )
        let success = await withCheckedContinuation { continuation in
            uploadBloodOxygenData(data: uploadData) { isSuccess, _ in
                continuation.resume(returning: isSuccess)
            }
        }
        if success {
            if !date.isInToday{
                PlistManager.shared.markDataUploaded(for: .timingO2Record, date: date)
            }
            
            print("✅ 上传 \(date.string()) 血氧成功")
        } else {
            print("❌ 上传 \(date.string()) 血氧失败")
        }
//        await handleO2Upload(date: date, model: fetchedModel)
    }
    
    ///
    /// 从设备获取压力历史数据并保存到本地
    /// - Parameter completion: 完成回调，返回获取的数据数量和可能的错误
    func fetchAndSaveStressHistory(completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        print("从设备获取压力历史数据...")
        CRPSmartRingManage.shared.getStressRecord { stressRecordModels, error in
//            guard let self = self else { return }
            
//            if error == .none && !records.isEmpty {
//                print("成功获取\(records.count)条压力历史数据")
//                
//                // 将数据保存到本地数据库
//                self.saveStressData(records: records) { savedCount in
//                    print("已保存\(savedCount)条新压力数据到本地")
//                    DispatchQueue.main.async {
//                        completion(savedCount, nil)
//                        
//                        // 自动尝试上传
//                        if savedCount > 0 {
//                            self.uploadPendingStressData()
//                        }
//                    }
//                }
//            } else {
//                let errorMessage = error == .none ? "未获取到数据" : "错误: \(error)"
//                print("获取压力历史数据失败: \(errorMessage)")
//                DispatchQueue.main.async {
//                    completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: errorMessage]))
//                }
//            }
        }
    }
    
    /// 上传上传的压力数据
    /// - Parameter completion: 完成回调，包含上传成功的数据数量和可能的错误
    func uploadPendingStressData(date:Date,recordModel:StressRecordModel,completion: ((Bool, Error?) -> Void)? = nil) {
        // 检查网络可用性
        guard NetworkMonitor.shared.isConnected else {
            print("网络不可用，稍后将自动重试上传")
            completion?(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "网络不可用"]))
            return
        }
        
        // 检查用户是否登录
        guard let userId = authService.currentUser?.id else {
            print("用户未登录，无法上传数据")
            completion?(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        // 验证Token是否存在
        guard let token = authService.currentToken?.accessToken else {
            print("用户未登录或Token已过期，无法上传数据")
            completion?(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录或Token已过期"]))
            return
        }
        
        print("用户已登录，Token: \(token.prefix(10))...")
//        guard let recordModel = PlistManager.shared.get(for: .stressRecord, date: date, as: StressRecordModel.self) else {
//            
//        }
//        guard let recordModel = PlistManager.shared.get(for: .stressRecord, date:  date, as: StressRecordModel.self) else {
//            print("获取不到压力数据")
//            completion?(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取不到压力数据"]))
//            return
//        }
        // 准备上传数据
//        let uploadData = prepareStressUploadData(dateString: Date().string(), stressValues: stressValues)
        
        let uploadData = StressUploadData.init(date: date.string(), records: recordModel.records)
        
        // 执行上传
        uploadStressData(data: uploadData) { success, error in
            if success {
                completion?(success, nil)
                print("成功上传\(Date().string())的\(recordModel.records.count)条压力数据")
//                uploadedCount += stressValues.count
            } else {
                completion?(false, error)
                print("上传\(Date().string())的压力数据失败: \(error?.localizedDescription ?? "未知错误")")
//                uploadError = error
            }
        }
    }
    
    /// 上传压力数据到服务器
    /// - Parameters:
    ///   - data: 压力上传数据模型
    ///   - completion: 完成回调
    private func uploadStressData(data: StressUploadData, completion: @escaping (Bool, Error?) -> Void) {
        // 构建API URL
         // 使用开发环境API基础URL
        guard let baseURL = URL(string: AppGlobals.apiBaseURL) else {
            completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的服务器URL"]))
            return
        }
        
        let uploadURL = baseURL.appendingPathComponent("/app-api/iot/stress/upload/data")
        print("上传压力数据到URL: \(uploadURL.absoluteString)")
        
        // 创建请求
        var request = URLRequest(url: uploadURL)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加认证信息
        if let token = authService.currentToken?.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("添加Authorization请求头: Bearer \(token.prefix(10))...")
        } else {
            print("警告: 未找到有效的token")
        }
        
        // 添加租户ID
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        print("添加tenant-id请求头: 1")
        
        // 打印所有请求头
        print("请求头:")
        request.allHTTPHeaderFields?.forEach { key, value in
            print("\(key): \(value.prefix(30))...")
        }
        
        // 准备请求体
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let jsonData = try encoder.encode(data)
            
            // 打印上传的JSON数据
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("上传的压力数据JSON: \n\(jsonString)")
            }
            
            request.httpBody = jsonData
            
            // 创建并执行网络请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 处理网络错误
                if let error = error {
                    print("上传压力数据网络错误: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(false, error)
                    }
                    return
                }
                
                // 检查HTTP响应
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("无效的HTTP响应")
                    DispatchQueue.main.async {
                        completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
                    }
                    return
                }
                
                // 打印响应头
                print("响应状态码: \(httpResponse.statusCode)")
                print("响应头:")
                httpResponse.allHeaderFields.forEach { key, value in
                    print("\(key): \(value)")
                }
                
                // 打印响应数据
                if let responseData = data, let responseString = String(data: responseData, encoding: .utf8) {
                    print("服务器响应: \(responseString)")
                }
                
                // 检查状态码
                if httpResponse.statusCode == 200 {
                    print("压力数据上传成功")
                    DispatchQueue.main.async {
                        completion(true, nil)
                    }
                } else {
                    let responseString = data != nil ? String(data: data!, encoding: .utf8) ?? "无数据" : "无数据"
                    print("压力数据上传失败，状态码: \(httpResponse.statusCode), 响应: \(responseString)")
                    
                    // 解析错误响应
                    var errorMessage = "上传失败，HTTP状态码: \(httpResponse.statusCode)"
                    if let data = data {
                        do {
                            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                                print("错误响应JSON: \(json)")
                                
                                if let code = json["code"] as? Int {
                                    print("错误码: \(code)")
                                }
                                
                                if let msg = json["msg"] as? String {
                                    print("错误消息: \(msg)")
                                    errorMessage = msg
                                }
                                
                                if let errorData = json["data"] as? [String: Any] {
                                    print("错误数据: \(errorData)")
                                }
                            }
                        } catch {
                            print("解析错误响应失败: \(error.localizedDescription)")
                        }
                    }
                    
                    // 处理特定错误情况
                    if httpResponse.statusCode == 401 {
                        errorMessage = "用户未登录或身份验证已过期，请重新登录"
                    } else if httpResponse.statusCode == 403 {
                        errorMessage = "用户无权限执行此操作"
                    }
                    
                    DispatchQueue.main.async {
                        let error = NSError(
                            domain: "com.windring.error",
                            code: httpResponse.statusCode,
                            userInfo: [NSLocalizedDescriptionKey: errorMessage]
                        )
                        completion(false, error)
                    }
                }
            }.resume()
        } catch {
            print("编码压力数据失败: \(error.localizedDescription)")
            completion(false, error)
        }
    }
    
    func getTimingHRV(for date: Date) -> HRVDataModel? {
        let semaphore = DispatchSemaphore(value: 0)
        var result: HRVDataModel?
        var fetchError: Error?

        CRPSmartRingManage.shared.getTimingHRV(date: date) { model, error in
            if let error = error {
                fetchError = error
                print("❌ 获取HRV数据失败: \(error.localizedDescription)")
            } else if let model = model {
                // 验证数据有效性
                if !model.heartRate.isEmpty {
                result = model
                } else {
                    print("⚠️ HRV数据为空")
                }
            }
            semaphore.signal()
        }

        _ = semaphore.wait(timeout: .now() + 15)
        
        if let error = fetchError {
            print("❌ HRV数据获取失败: \(error.localizedDescription)")
        }
        
        return result
    }

    private func getTimingHeartRate(for date: Date) -> HeartRateModel? {
        let semaphore = DispatchSemaphore(value: 0)
        var result: HeartRateModel?
        var fetchError: Error?

        CRPSmartRingManage.shared.getTimingHeartRate(day: date) { model, error in
            if let error = error {
                fetchError = error
                print("❌ 获取心率数据失败: \(error.localizedDescription)")
            } else if let model = model {
                // 验证数据有效性
                if !model.heartRate.isEmpty {
                result = model
                } else {
                    print("⚠️ 心率数据为空")
                }
            }
            semaphore.signal()
        }

        _ = semaphore.wait(timeout: .now() + 15)
        
        if let error = fetchError {
            print("❌ 心率数据获取失败: \(error.localizedDescription)")
        }
        
        return result
    }

    private func fetchTimingO2(for date: Date) -> TimingO2RecordDataModel? {
        let semaphore = DispatchSemaphore(value: 0)
        var result: TimingO2RecordDataModel?
        var fetchError: Error?

        CRPSmartRingManage.shared.getTimingO2(date: date) { data, error in
            if let error = error {
                fetchError = error
                print("❌ 获取血氧数据失败: \(error.localizedDescription)")
            } else if let data = data {
                // 验证数据有效性
                if !data.timingO2Record.isEmpty {
                result = data
                } else {
                    print("⚠️ 血氧数据为空")
                }
            }
            semaphore.signal()
        }

        _ = semaphore.wait(timeout: .now() + 15)
        
        if let error = fetchError {
            print("❌ 血氧数据获取失败: \(error.localizedDescription)")
        }
        
        return result
    }

    private func getSleepTemperature(for date: Date) -> SleepTemperatureDataModel? {
        let semaphore = DispatchSemaphore(value: 0)
        var result: SleepTemperatureDataModel?
        var fetchError: Error?

        CRPSmartRingManage.shared.getSleepTemperatureData(date: date) { data, error in
            if let error = error {
                fetchError = error
                print("❌ 获取睡眠体温数据失败: \(error.localizedDescription)")
            } else if let data = data {
                // 验证数据有效性
                if !data.sleepTemperatureData.isEmpty {
                result = data
                } else {
                    print("⚠️ 睡眠体温数据为空")
                }
            }
            semaphore.signal()
        }

        _ = semaphore.wait(timeout: .now() + 15)
        
        if let error = fetchError {
            print("❌ 睡眠体温数据获取失败: \(error.localizedDescription)")
        }
        
        return result
    }

    func getSleep(for date: Date) -> SleepRecordModel? {
        let semaphore = DispatchSemaphore(value: 0)
        var result: SleepRecordModel?

        CRPSmartRingManage.shared.getSleepDataForDay(date: date) { data, error in
            if let data = data {
                result = data
            }
            semaphore.signal()
        }

        _ = semaphore.wait(timeout: .now() + 15)
        return result
    }
    
    func getTiming(for date: Date) -> HRVDataModel? {
        let semaphore = DispatchSemaphore(value: 0)
        var result: HRVDataModel?

        CRPSmartRingManage.shared.getTimingHRV(date: date) { model, error in
            if let model = model {
                result = model
            }
            semaphore.signal()
        }

        _ = semaphore.wait(timeout: .now() + 15)
        return result
    }

    func getExerciseStats(for date: Date) -> StepModel? {
        let semaphore = DispatchSemaphore(value: 0)
        var result: StepModel?

        CRPSmartRingManage.shared.getHistoricalExerciseStats(date) { res in
            switch res {
            case .success(let model):
                result = model
            case .failure:
                result = nil
            }
            semaphore.signal()
        }

        // 等待回调完成（阻塞当前线程）
        _ = semaphore.wait(timeout: .now() + 15)  // 等待最多10秒，防止死锁

        return result
    }
    
    
    func getStepDetails(for date: Date) -> [ActivityDetailData] {
        let semaphore = DispatchSemaphore(value: 0)
        var result: [ActivityDetailData] = []

        CRPSmartRingManage.shared.getHalfHourlyStepStats(day: date) { data, error in
            if error == nil {
                result = data ?? []
            }
            semaphore.signal()
        }

        // 等待回调完成（阻塞当前线程）
        _ = semaphore.wait(timeout: .now() + 15)  // 等待最多10秒，防止死锁

        return result
    }


    
    /// 上传睡眠数据
    /// - Parameters:
    ///   - data: 上传数据模型
    ///   - completion: 完成回调，返回是否成功和可能的错误
    private func uploadSleepData(data: SleepUploadData, completion: @escaping (Bool, Error?) -> Void) async {
        guard let userId = authService.currentUser?.id else {
            completion(false, NSError(domain: "SleepUploadService", code: 401, userInfo: [NSLocalizedDescriptionKey: "User not logged in"]))
            return
        }
        
        guard let token = authService.currentToken?.accessToken else {
            completion(false, NSError(domain: "SleepUploadService", code: 401, userInfo: [NSLocalizedDescriptionKey: "No access token available"]))
            return
        }
        
        print("\n============= 睡眠数据上传开始 \(Date().string(withFormat: "dd/MM/yyyy HH:mm:ss")) =============")
        print("用户ID: \(userId)")
        print("认证令牌是否存在: \(token.isEmpty ? "否" : "是")")
        guard let url = URL(string: "\(AppGlobals.apiBaseURL)/app-api/iot/sleep/upload/sleep/data") else {
            let error = NSError(domain: "HeartRateDataUpload", code: 405, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(false, error)
            return
        }
        // 使用正确的API URL (与Postman一致)
        print("准备上传睡眠数据到URL: \(url.absoluteString)")
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.timeoutInterval = 30 // 设置30秒超时
        
        // 设置请求头 (格式与Postman一致，但使用动态值)
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.setValue("1", forHTTPHeaderField: "tenant-id")
        
        // 添加Cookie (如果有)
//        if let cookieValue = UserDefaults.standard.string(forKey: "sl-session") {
//            request.setValue("sl-session=\(cookieValue)", forHTTPHeaderField: "Cookie")
//            print("添加Cookie: sl-session=\(cookieValue)")
//        } else {
//            print("警告: 未找到sl-session Cookie，这可能导致请求失败")
//            print("参考: Postman成功的请求使用了Cookie: sl-session=MZWcGmTv32etBzfmWc9AXw==")
//            // 不设置默认Cookie，保持动态方式
//        }
        
        print("请求头: \(String(describing: request.allHTTPHeaderFields))")
        
        // 将数据转换为JSON并设置为请求体
        do {
            let jsonData = try JSONEncoder().encode(data)
            request.httpBody = jsonData
            
            // 打印完整的JSON请求数据用于调试
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("\n======= 睡眠数据JSON请求体 =======")
                print(jsonString)
                print("================================\n")
            }
            
//            logger.debug("Sleep data upload request: \(request)")
//            logger.debug("Sleep data upload request headers: \(String(describing: request.allHTTPHeaderFields))")
            
            // 创建一个信号量用于同步完成
            let semaphore = DispatchSemaphore(value: 0)
            var taskSuccess = false
            var taskError: Error? = nil
            
            print("开始发送网络请求...")
            
            let task = URLSession.shared.dataTask(with: request) { data, response, error in
                defer {
                    semaphore.signal() // 请求完成，发送信号
                }
                
                if let error = error {
//                    self.logger.error("Error uploading sleep data: \(error.localizedDescription)")
//                    print("上传睡眠数据网络错误: \(error.localizedDescription)")
                    
                    // 检查是否是网络连接问题
                    let nsError = error as NSError
                    if nsError.domain == NSURLErrorDomain {
                        print("网络错误类型: \(nsError.code)")
                        if nsError.code == NSURLErrorNotConnectedToInternet {
                            print("设备未连接到互联网")
                        } else if nsError.code == NSURLErrorTimedOut {
                            print("请求超时")
                        }
                    }
                    
                    taskSuccess = false
                    taskError = error
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
//                    self.logger.error("Invalid response")
                    print("上传睡眠数据收到无效响应")
                    taskSuccess = false
                    taskError = NSError(domain: "SleepUploadService", code: 500, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
                    return
                }
                
//                self.logger.debug("Sleep data upload response status: \(httpResponse.statusCode)")
                print("上传睡眠数据响应状态码: \(httpResponse.statusCode)")
                
                // 打印响应头
                print("响应头: \(httpResponse.allHeaderFields)")
                
                if let data = data, let responseString = String(data: data, encoding: .utf8) {
//                    self.logger.debug("Sleep data upload response: \(responseString)")
                    print("\n======= 上传睡眠数据响应体 =======")
                    print(responseString)
                    print("================================\n")
                } else {
                    print("无法解析响应数据或响应体为空")
                }
                
                // 更新session cookie如果存在
                if let cookies = httpResponse.allHeaderFields["Set-Cookie"] as? String {
                    if cookies.contains("sl-session=") {
                        let components = cookies.components(separatedBy: ";")
                        if let sessionComponent = components.first(where: { $0.contains("sl-session=") }) {
                            let sessionValue = sessionComponent.trimmingCharacters(in: .whitespaces)
                            print("从响应中获取到新的Cookie: \(sessionValue)")
                            // 存储到UserDefaults
                            if let equalIndex = sessionValue.firstIndex(of: "=") {
                                let value = String(sessionValue.suffix(from: sessionValue.index(after: equalIndex)))
                                UserDefaults.standard.set(value, forKey: "sl-session")
                                print("已保存新的sl-session值: \(value)")
                            }
                        }
                    }
                }
                
                if (200...299).contains(httpResponse.statusCode) {
//                    self.logger.debug("Sleep data uploaded successfully")
                    print("睡眠数据上传成功")
                    taskSuccess = true
                    taskError = nil
                } else {
//                    self.logger.error("Failed to upload sleep data. Status code: \(httpResponse.statusCode)")
                    print("睡眠数据上传失败。状态码: \(httpResponse.statusCode)")
                    
                    // 针对特定状态码提供更详细的错误信息
                    var errorMessage = "Failed to upload sleep data"
                    if httpResponse.statusCode == 401 {
                        errorMessage = "认证失败，请重新登录"
                        print("错误原因: 认证失败")
                        print("提示: 如果一直失败，可以尝试使用uploadSleepDataWithPostmanSettings方法测试固定令牌")
                    } else if httpResponse.statusCode == 403 {
                        errorMessage = "没有权限执行此操作"
                        print("错误原因: 权限不足")
                    } else if httpResponse.statusCode == 404 {
                        errorMessage = "资源不存在"
                        print("错误原因: 资源不存在")
                    } else if httpResponse.statusCode == 500 {
                        errorMessage = "服务器内部错误"
                        print("错误原因: 服务器内部错误")
                    }
                    
                    taskSuccess = false
                    taskError = NSError(domain: "SleepUploadService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                }
            }
            
            task.resume()
            
            // 等待网络请求完成（最多等待30秒）
            let waitResult = semaphore.wait(timeout: .now() + 30)
            if waitResult == .timedOut {
                print("网络请求超时")
                completion(false, NSError(domain: "SleepUploadService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Request timed out"]))
            } else {
                print("网络请求已完成，结果: \(taskSuccess ? "成功" : "失败")")
                completion(taskSuccess, taskError)
            }
            
            print("============= 睡眠数据上传结束 =============\n")
        } catch {
//            self.logger.error("Error serializing sleep data: \(error.localizedDescription)")
            print("JSON序列化错误: \(error.localizedDescription)")
            completion(false, error)
        }
    }
    
    /// 发送HRV数据到服务器
    /// - Parameters:
    ///   - requestBody: 请求体数据
    ///   - token: 认证令牌
    ///   - completion: 完成回调
    private func sendHRVDataToServer(requestBody: [String: Any], completion: @escaping (Bool, Error?) -> Void) {
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "HRVDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        // 构建URL
        guard let url = URL(string: "\(AppGlobals.apiBaseURL)/app-api/iot/hrv/upload/data") else {
            let error = NSError(domain: "HeartRateDataUpload", code: 405, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(false, error)
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(authService.currentToken?.accessToken ?? "")", forHTTPHeaderField: "Authorization")
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 将请求体转换为JSON数据
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            request.httpBody = jsonData
            
            // 详细日志记录
            print("📤 发送HRV数据到服务器，URL: \(url.absoluteString)")
            print("📤 数据大小: \(jsonData.count) 字节")
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("📦 HRV数据内容摘要: \(String(jsonString.prefix(200)))...")
            }
            
            // 发送请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 记录HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("🌐 服务器响应状态码: \(httpResponse.statusCode)")
                }
                
                if let error = error {
                    print("❌ 网络请求错误: \(error.localizedDescription)")
                    completion(false, error)
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    let error = NSError(domain: "HRVDataUpload", code: 406, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])
                    print("❌ 无效的HTTP响应")
                    completion(false, error)
                    return
                }
                
                // 处理响应
                if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                    print("✅ HRV数据上传成功: 状态码\(httpResponse.statusCode)")
                    
                    // 解析响应数据
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器响应内容: \(responseString)")
                    }
                    
                    // 更新上次HRV数据上传时间
                    DispatchQueue.main.async {
//                        self.lastHRVUploadTime = Date()
                    }
                    
                    completion(true, nil)
                } else {
                    var errorMessage = "服务器返回错误: \(httpResponse.statusCode)"
                    
                    // 尝试解析错误信息
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器错误响应: \(responseString)")
                        errorMessage += ", 响应: \(responseString)"
                    }
                    
                    print("❌ HRV数据上传失败: \(errorMessage)")
                    let error = NSError(domain: "HRVDataUpload", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    completion(false, error)
                }
            }.resume()
        } catch {
            print("❌ JSON序列化错误: \(error.localizedDescription)")
            completion(false, error)
        }
    }
    
    
    /// 发送心率数据到服务器
    /// - Parameters:
    ///   - requestBody: 请求体数据
    ///   - token: 认证令牌
    ///   - completion: 完成回调
    private func sendHeartRateDataToServer(requestBody: [String: Any], completion: @escaping (Bool, Error?) -> Void) {
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "HeartRateDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        // 构建URL
        guard let url = URL(string: "\(AppGlobals.apiBaseURL)/app-api/iot/hr/upload/data") else {
            let error = NSError(domain: "HeartRateDataUpload", code: 405, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(false, error)
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(authService.currentToken?.accessToken ?? "")", forHTTPHeaderField: "Authorization")
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 将请求体转换为JSON数据
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            request.httpBody = jsonData
            
            // 详细日志记录
            print("📤 发送心率数据到服务器，URL: \(url.absoluteString)")
            print("📤 数据大小: \(jsonData.count) 字节")
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("📦 心率数据内容摘要: \(String(jsonString.prefix(200)))...")
            }
            
            // 发送请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 记录HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("🌐 服务器响应状态码: \(httpResponse.statusCode)")
                }
                
                if let error = error {
                    print("❌ 网络请求错误: \(error.localizedDescription)")
                    completion(false, error)
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    let error = NSError(domain: "HeartRateDataUpload", code: 406, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])
                    print("❌ 无效的HTTP响应")
                    completion(false, error)
                    return
                }
                
                // 处理响应
                if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                    print("✅ 心率数据上传成功: 状态码\(httpResponse.statusCode)")
                    
                    // 解析响应数据
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器响应内容: \(responseString)")
                    }
                    
                    // 更新上次心率数据上传时间
                    DispatchQueue.main.async {
//                        self.lastHeartRateUploadTime = Date()
                    }
                    
                    completion(true, nil)
                } else {
                    var errorMessage = "服务器返回错误: \(httpResponse.statusCode)"
                    
                    // 尝试解析错误信息
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器错误响应: \(responseString)")
                        errorMessage += ", 响应: \(responseString)"
                    }
                    
                    print("❌ 心率数据上传失败: \(errorMessage)")
                    let error = NSError(domain: "HeartRateDataUpload", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    completion(false, error)
                }
            }.resume()
        } catch {
            print("❌ JSON序列化错误: \(error.localizedDescription)")
            completion(false, error)
        }
    }
    
    /// 上传温度数据到服务器
    /// - Parameters:
    ///   - data: 温度上传数据
    ///   - completion: 完成回调，返回是否成功和可能的错误
    private func uploadTemperatureData(data: TemperatureUploadData, completion: @escaping (Bool, Error?) -> Void) {
        // 确保用户已登录
        guard let token = authService.currentToken?.accessToken else {
            completion(false, NSError(domain: "TemperatureService", code: 401, userInfo: [NSLocalizedDescriptionKey: "缺少认证Token"]))
            return
        }
        
        // 准备URL
        guard let url = URL(string: "\(AppGlobals.apiBaseURL)/app-api/iot/sleep/upload/temperature/data") else {
            completion(false, NSError(domain: "TemperatureService", code: 400, userInfo: [NSLocalizedDescriptionKey: "无效的URL"]))
            return
        }
        
        // 准备请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        
        // 添加租户ID
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 将数据转换为JSON
        do {
            let jsonData = try JSONEncoder().encode(data)
            request.httpBody = jsonData
            
            // 打印上传的JSON数据，便于调试
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("上传的温度数据JSON: \(jsonString)")
            }
        } catch {
            completion(false, error)
            return
        }
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理响应
            if let error = error {
                DispatchQueue.main.async {
                    completion(false, error)
                }
                return
            }
            
            // 检查HTTP响应状态码
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(false, NSError(domain: "TemperatureService", code: 500, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
                }
                return
            }
            
            // 打印响应状态码
            print("温度数据上传响应状态码: \(httpResponse.statusCode)")
            
            // 打印响应数据
            if let data = data, let responseString = String(data: data, encoding: .utf8) {
                print("温度数据上传响应: \(responseString)")
                
                // 发送通知，包含响应数据
                NotificationCenter.default.post(
                    name: .temperatureDataUploadResponse,
                    object: nil,
                    userInfo: ["response": data]
                )
            }
            
            // 根据状态码判断是否成功
            let success = (200...299).contains(httpResponse.statusCode)
            
            DispatchQueue.main.async {
                if success {
                    completion(true, nil)
                } else {
                    let message = "服务器返回错误状态码: \(httpResponse.statusCode)"
                    completion(false, NSError(domain: "TemperatureService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: message]))
                }
            }
        }
        
        task.resume()
    }
    
    /// 上传血氧数据到服务器
    /// - Parameters:
    ///   - data: 血氧上传数据模型
    ///   - completion: 完成回调
    private func uploadBloodOxygenData(data: TimingO2RecordBloodOxygenUploadData, completion: @escaping (Bool, Error?) -> Void) {
        guard let uploadURL = URL(string: "\(AppGlobals.apiBaseURL)/app-api/iot/blood/upload/data") else {
//            throw UploadError.invalidURL
            completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的服务器URL"]))
            return
        }
        // 构建API URL
        print("上传血氧数据到URL: \(uploadURL.absoluteString)")
        
        // 创建请求
        var request = URLRequest(url: uploadURL)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加认证信息
        if let token = authService.currentToken?.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("添加Authorization请求头: Bearer \(token.prefix(10))...")
        } else {
            print("警告: 未找到有效的token")
        }
        
        // 添加租户ID
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        print("添加tenant-id请求头: 1")
        
        // 打印所有请求头
        print("请求头:")
        request.allHTTPHeaderFields?.forEach { key, value in
            print("\(key): \(value.prefix(30))...")
        }
        
        // 准备请求体
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let jsonData = try encoder.encode(data)
            
            // 打印上传的JSON数据
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("上传的血氧数据JSON: \n\(jsonString)")
            }
            
            request.httpBody = jsonData
            
            // 创建并执行网络请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 处理网络错误
                if let error = error {
                    print("上传血氧数据网络错误: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(false, error)
                    }
                    return
                }
                
                // 检查HTTP响应
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("无效的HTTP响应")
                    DispatchQueue.main.async {
                        completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
                    }
                    return
                }
                
                // 打印响应头
                print("响应状态码: \(httpResponse.statusCode)")
                print("响应头:")
                httpResponse.allHeaderFields.forEach { key, value in
                    print("\(key): \(value)")
                }
                
                // 打印响应数据
                if let responseData = data, let responseString = String(data: responseData, encoding: .utf8) {
                    print("服务器响应: \(responseString)")
                }
                
                // 检查状态码
                if httpResponse.statusCode == 200 {
                    print("血氧数据上传成功")
                    DispatchQueue.main.async {
                        completion(true, nil)
                    }
                } else {
                    let responseString = data != nil ? String(data: data!, encoding: .utf8) ?? "无数据" : "无数据"
                    print("血氧数据上传失败，状态码: \(httpResponse.statusCode), 响应: \(responseString)")
                    
                    // 解析错误响应
                    var errorMessage = "上传失败，HTTP状态码: \(httpResponse.statusCode)"
                    if let data = data {
                        do {
                            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                                print("错误响应JSON: \(json)")
                                
                                if let code = json["code"] as? Int {
                                    print("错误码: \(code)")
                                }
                                
                                if let msg = json["msg"] as? String {
                                    print("错误消息: \(msg)")
                                    errorMessage = msg
                                }
                                
                                if let errorData = json["data"] as? [String: Any] {
                                    print("错误数据: \(errorData)")
                                }
                            }
                        } catch {
                            print("解析错误响应失败: \(error.localizedDescription)")
                        }
                    }
                    
                    // 处理特定错误情况
                    if httpResponse.statusCode == 401 {
                        errorMessage = "用户未登录或身份验证已过期，请重新登录"
                    } else if httpResponse.statusCode == 403 {
                        errorMessage = "用户无权限执行此操作"
                    }
                    
                    DispatchQueue.main.async {
                        let error = NSError(
                            domain: "com.windring.error",
                            code: httpResponse.statusCode,
                            userInfo: [NSLocalizedDescriptionKey: errorMessage]
                        )
                        completion(false, error)
                    }
                }
            }.resume()
        } catch {
            print("编码血氧数据失败: \(error.localizedDescription)")
            completion(false, error)
        }
    }
    
    
    
    /// 发送活动数据到服务器
    /// - Parameters:
    ///   - requestBody: 请求体数据
    ///   - token: 认证令牌
    ///   - completion: 完成回调
    private func sendActivityDataToServer(requestBody: [String: Any], token: String, completion: @escaping (Bool, Error?) -> Void) {
        guard networkMonitor.isConnected else {
            let error = NSError(domain: "ActivityDataUpload", code: 403, userInfo: [NSLocalizedDescriptionKey: "网络未连接"])
            completion(false, error)
            return
        }
        
        // 构建URL
        guard let url = URL(string: "\(AppGlobals.apiBaseURL)/app-api/iot/activity/upload/data") else {
            let error = NSError(domain: "ActivityDataUpload", code: 405, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(false, error)
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 将请求体转换为JSON数据
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            request.httpBody = jsonData
            
            // 详细日志记录
            print("📤 发送活动数据到服务器，URL: \(url.absoluteString)")
            print("📤 数据大小: \(jsonData.count) 字节")
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("📦 活动数据内容摘要: \(String(jsonString.prefix(200)))...")
            }
            
            // 发送请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 记录HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("🌐 服务器响应状态码: \(httpResponse.statusCode)")
                }
                
                if let error = error {
                    print("❌ 网络请求错误: \(error.localizedDescription)")
                    completion(false, error)
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    let error = NSError(domain: "ActivityDataUpload", code: 406, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])
                    print("❌ 无效的HTTP响应")
                    completion(false, error)
                    return
                }
                
                // 处理响应
                if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                    print("✅ 活动数据上传成功: 状态码\(httpResponse.statusCode)")
                    
                    // 解析响应数据
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器响应内容: \(responseString)")
                    }
                    
                    // 更新上次活动数据上传时间
                    DispatchQueue.main.async {
//                        self.lastActivityUploadTime = Date()
                    }
                    
                    completion(true, nil)
                } else {
                    var errorMessage = "服务器返回错误: \(httpResponse.statusCode)"
                    
                    // 尝试解析错误信息
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📥 服务器错误响应: \(responseString)")
                        errorMessage += ", 响应: \(responseString)"
                    }
                    
                    print("❌ 活动数据上传失败: \(errorMessage)")
                    let error = NSError(domain: "ActivityDataUpload", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    completion(false, error)
                }
            }.resume()
        } catch {
            print("❌ JSON序列化错误: \(error.localizedDescription)")
            completion(false, error)
        }
    }
}
enum UploadError: LocalizedError {
    case deviceNotConnected
    case networkUnavailable
    case unauthenticated
    case invalidURL
    case invalidResponse
    case serverError(code: Int, message: String)

    var errorDescription: String? {
        switch self {
        case .deviceNotConnected: return "设备未连接"
        case .networkUnavailable: return "网络不可用"
        case .unauthenticated: return "用户未登录"
        case .invalidURL: return "无效的请求 URL"
        case .invalidResponse: return "无效响应"
        case let .serverError(code, message): return "服务器错误 \(code): \(message)"
        }
    }
}
struct ActivityUploadPayload {
    let date: Date
    let summary: StepModel?
    let details: [ActivityDetailData]

    func toDictionary() -> [String: Any] {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let dateString = formatter.string(from: date)

        return [
            "date": dateString,
            "steps": summary?.steps ?? 0,
            "calories": summary?.calory ?? 0 ,
            "time": (summary?.time ?? 0) ,
            "distance": (summary?.distance ?? 0),
            "records":  details.map { ["steps": $0.steps, "time": $0.time] }
        ]
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let didSyncTodayData = Notification.Name("didSyncTodayData")
}
