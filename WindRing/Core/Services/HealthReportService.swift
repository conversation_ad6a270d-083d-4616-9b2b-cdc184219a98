import Foundation
import Combine

// Import models using relative path
// import Models.HealthReportModels 

// Use the public typealiases from HealthReportModels.swift

/// 健康报告服务 - 负责生成不同类型的健康报告
class HealthReportService {
    // MARK: - 单例
    static let shared = HealthReportService()
    
    // MARK: - 初始化方法
    private init() {}
    
    // MARK: - 公开方法
    
    /// 生成日报
    /// - Parameter userId: 用户ID
    /// - Returns: 生成的健康日报
    func generateDailyReport(userId: String) -> HRMReport {
        // 创建报告
        var report = HRMReport(
            userId: userId,
            type: .daily,
            summary: "今日健康状况良好，心率正常，步数达到目标的78%。"
        )
        
        // 添加健康分数
        report.healthScore = 82
        
        // 添加报告部分
        report.sections = generateDailyReportSections()
        
        return report
    }
    
    /// 生成周报
    /// - Parameter userId: 用户ID
    /// - Returns: 生成的健康周报
    func generateWeeklyReport(userId: String) -> HRMReport {
        // 创建报告
        var report = HRMReport(
            userId: userId,
            type: .weekly,
            summary: "本周健康状况整体良好，活动水平有所提高，睡眠质量略有下降。"
        )
        
        // 添加健康分数
        report.healthScore = 85
        
        // 添加报告部分
        report.sections = generateWeeklyReportSections()
        
        return report
    }
    
    /// 生成月报
    /// - Parameter userId: 用户ID
    /// - Returns: 生成的健康月报
    func generateMonthlyReport(userId: String) -> HRMReport {
        // 创建报告
        var report = HRMReport(
            userId: userId,
            type: .monthly,
            summary: "本月健康状况稳定，心率和血压保持正常范围，活动水平逐渐提升。"
        )
        
        // 添加健康分数
        report.healthScore = 88
        
        // 添加报告部分
        report.sections = generateMonthlyReportSections()
        
        return report
    }
    
    /// 生成自定义报告
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 生成的自定义健康报告
    func generateCustomReport(userId: String, startDate: Date, endDate: Date) -> HRMReport {
        // 计算日期差异
        let days = Calendar.current.dateComponents([.day], from: startDate, to: endDate).day ?? 0
        let period = days >= 28 ? "月" : (days >= 7 ? "周" : "日")
        
        // 创建报告
        var report = HRMReport(
            userId: userId,
            type: .custom,
            title: "自定义健康报告",
            summary: "这段时间健康状况整体良好，有\(period)期波动。"
        )
        
        // 设置日期范围
        report.startDate = startDate
        report.endDate = endDate
        
        // 添加健康分数
        report.healthScore = 80
        
        // 添加报告部分
        report.sections = generateCustomReportSections()
        
        return report
    }
    
    // MARK: - 私有方法
    
    /// 生成日报部分
    private func generateDailyReportSections() -> [HRMSection] {
        // 健康摘要部分
        let summarySection = HRMSection(
            title: "健康摘要",
            content: "今日健康状况整体良好。心率保持在正常范围，步数为8,246步，距离目标还有1,754步。",
            dataPoints: [
                HRMDataPoint(label: "平均心率", value: "72次/分钟"),
                HRMDataPoint(label: "最低心率", value: "58次/分钟"),
                HRMDataPoint(label: "最高心率", value: "125次/分钟"),
                HRMDataPoint(label: "今日步数", value: "8,246步")
            ]
        )
        
        // 活动部分
        let activitySection = HRMSection(
            title: "活动情况",
            content: "今日活动共消耗320千卡，完成了两次中等强度运动，活动时间共计48分钟。",
            dataPoints: [
                HRMDataPoint(label: "活动消耗", value: "320千卡"),
                HRMDataPoint(label: "活动时间", value: "48分钟"),
                HRMDataPoint(label: "站立时间", value: "9小时")
            ],
            charts: [
                HRMChartData(
                    title: "活动分布",
                    type: .pie,
                    labels: ["静坐", "走路", "跑步", "健身"],
                    values: [65, 20, 5, 10],
                    unit: "%"
                )
            ]
        )
        
        // 睡眠部分
        let sleepSection = HRMSection(
            title: "睡眠情况",
            content: "昨晚睡眠时长为7小时20分钟，睡眠质量良好，深度睡眠占比略低。",
            dataPoints: [
                HRMDataPoint(label: "睡眠时长", value: "7小时20分钟"),
                HRMDataPoint(label: "入睡时间", value: "23:15"),
                HRMDataPoint(label: "醒来时间", value: "06:35"),
                HRMDataPoint(label: "睡眠评分", value: "78分")
            ],
            charts: [
                HRMChartData(
                    title: "睡眠阶段",
                    type: .bar,
                    labels: ["深睡", "浅睡", "REM", "清醒"],
                    values: [90, 245, 95, 10],
                    unit: "分钟"
                )
            ]
        )
        
        // 建议部分
        let suggestionsSection = HRMSection(
            title: "健康建议",
            content: "1. 今日步数未达到目标，建议傍晚增加一次15分钟步行\n2. 深度睡眠时间略短，建议睡前一小时避免使用电子设备\n3. 今日摄水量较少，建议增加饮水量至2000ml"
        )
        
        return [summarySection, activitySection, sleepSection, suggestionsSection]
    }
    
    /// 生成周报部分
    private func generateWeeklyReportSections() -> [HRMSection] {
        // 健康摘要部分
        let summarySection = HRMSection(
            title: "本周健康摘要",
            content: "本周健康状况良好，活动量较上周提升15%，平均每日步数8,768步，睡眠质量稳定。",
            dataPoints: [
                HRMDataPoint(label: "平均心率", value: "73次/分钟"),
                HRMDataPoint(label: "平均步数", value: "8,768步/天"),
                HRMDataPoint(label: "平均睡眠", value: "7.5小时/天"),
                HRMDataPoint(label: "健康评分", value: "85分")
            ]
        )
        
        // 活动趋势部分
        let activitySection = HRMSection(
            title: "活动趋势",
            content: "本周共进行了12次有效运动，总活动时长350分钟，消耗能量2,240千卡。周末活动水平明显高于工作日。",
            charts: [
                HRMChartData(
                    title: "每日步数",
                    type: .bar,
                    labels: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
                    values: [7500, 8200, 6800, 9100, 8600, 11200, 10200],
                    unit: "步"
                ),
                HRMChartData(
                    title: "活动热量消耗",
                    type: .line,
                    labels: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
                    values: [280, 320, 260, 340, 310, 420, 380],
                    unit: "千卡"
                )
            ]
        )
        
        // 睡眠分析部分
        let sleepSection = HRMSection(
            title: "睡眠分析",
            content: "本周平均睡眠时长7.5小时，比上周提高0.3小时。睡眠质量整体稳定，周末睡眠时长明显增加。",
            charts: [
                HRMChartData(
                    title: "睡眠时长",
                    type: .bar,
                    labels: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
                    values: [7.2, 7.0, 7.3, 7.1, 7.5, 8.2, 8.5],
                    unit: "小时"
                ),
                HRMChartData(
                    title: "睡眠评分",
                    type: .line,
                    labels: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
                    values: [75, 72, 78, 76, 80, 85, 83],
                    unit: "分"
                )
            ]
        )
        
        // 健康进步部分
        let progressSection = HRMSection(
            title: "健康进步",
            content: "与上周相比:\n- 日均步数增加了568步\n- 活动时长每天增加了8分钟\n- 睡眠质量提高了5%\n- 静息心率降低了2次/分钟",
            dataPoints: [
                HRMDataPoint(label: "活动提升", value: "+15%"),
                HRMDataPoint(label: "睡眠提升", value: "+5%"),
                HRMDataPoint(label: "心率改善", value: "-2次/分钟"),
                HRMDataPoint(label: "整体改善", value: "+8%")
            ]
        )
        
        // 建议部分
        let suggestionsSection = HRMSection(
            title: "健康建议",
            content: "1. 工作日活动水平仍有提升空间，建议在午休时间增加15分钟步行\n2. 周三和周四睡眠质量较低，建议这两天避免晚上摄入咖啡因\n3. 心率波动较大，建议增加5-10分钟的冥想练习"
        )
        
        return [summarySection, activitySection, sleepSection, progressSection, suggestionsSection]
    }
    
    /// 生成月报部分
    private func generateMonthlyReportSections() -> [HRMSection] {
        // 健康摘要部分
        let summarySection = HRMSection(
            title: "本月健康摘要",
            content: "本月健康状况稳定向好，各项指标都保持在健康范围内。活动水平有明显提升，睡眠质量整体良好。",
            dataPoints: [
                HRMDataPoint(label: "平均心率", value: "71次/分钟"),
                HRMDataPoint(label: "平均步数", value: "9,245步/天"),
                HRMDataPoint(label: "平均睡眠", value: "7.8小时/天"),
                HRMDataPoint(label: "健康评分", value: "88分")
            ]
        )
        
        // 健康趋势部分
        let trendsSection = HRMSection(
            title: "月度健康趋势",
            content: "本月进行了52次有效运动，总活动时长1,680分钟，比上月增加320分钟。心率和血压保持稳定，无异常波动。",
            charts: [
                HRMChartData(
                    title: "每周步数",
                    type: .bar,
                    labels: ["第1周", "第2周", "第3周", "第4周"],
                    values: [63500, 68200, 66800, 70500],
                    unit: "步"
                ),
                HRMChartData(
                    title: "健康评分趋势",
                    type: .line,
                    labels: ["第1周", "第2周", "第3周", "第4周"],
                    values: [82, 85, 86, 90],
                    unit: "分"
                )
            ]
        )
        
        // 睡眠分析部分
        let sleepSection = HRMSection(
            title: "睡眠分析",
            content: "本月平均睡眠时长7.8小时，睡眠质量整体良好。深度睡眠占比有所提高，入睡时间趋于规律。",
            charts: [
                HRMChartData(
                    title: "每周睡眠评分",
                    type: .line,
                    labels: ["第1周", "第2周", "第3周", "第4周"],
                    values: [76, 79, 81, 85],
                    unit: "分"
                ),
                HRMChartData(
                    title: "睡眠阶段分布",
                    type: .pie,
                    labels: ["深睡", "浅睡", "REM", "清醒"],
                    values: [25, 45, 25, 5],
                    unit: "%"
                )
            ]
        )
        
        // 活动分析部分
        let activitySection = HRMSection(
            title: "活动分析",
            content: "本月进行了15次中高强度运动，包括跑步、游泳和力量训练。活动习惯日趋规律，周末活动高峰明显。",
            charts: [
                HRMChartData(
                    title: "活动类型分布",
                    type: .pie,
                    labels: ["步行", "跑步", "游泳", "骑行", "力量"],
                    values: [50, 20, 10, 10, 10],
                    unit: "%"
                ),
                HRMChartData(
                    title: "每周活动时长",
                    type: .area,
                    labels: ["第1周", "第2周", "第3周", "第4周"],
                    values: [380, 420, 405, 475],
                    unit: "分钟"
                )
            ]
        )
        
        // 健康进步部分
        let progressSection = HRMSection(
            title: "月度健康进步",
            content: "与上月相比:\n- 日均步数增加了845步\n- 活动时长每周增加了80分钟\n- 睡眠质量提高了8%\n- 静息心率降低了3次/分钟",
            dataPoints: [
                HRMDataPoint(label: "活动提升", value: "+18%"),
                HRMDataPoint(label: "睡眠提升", value: "+8%"),
                HRMDataPoint(label: "心率改善", value: "-3次/分钟"),
                HRMDataPoint(label: "整体改善", value: "+12%")
            ]
        )
        
        // 建议部分
        let suggestionsSection = HRMSection(
            title: "健康建议",
            content: "1. 您的活动水平和睡眠质量都有明显提高，建议保持当前的健康生活方式\n2. 考虑增加一些柔韧性训练，如瑜伽或拉伸\n3. 血压略有波动，建议监测钠摄入量\n4. 建议每周安排2-3次15分钟的冥想，帮助减轻压力"
        )
        
        return [summarySection, trendsSection, sleepSection, activitySection, progressSection, suggestionsSection]
    }
    
    /// 生成自定义报告部分
    private func generateCustomReportSections() -> [HRMSection] {
        // 使用周报的部分作为参考
        var sections = generateWeeklyReportSections()
        
        // 修改第一个部分的标题
        if !sections.isEmpty {
            sections[0].title = "时段健康摘要"
        }
        
        return sections
    }
} 