import Foundation
import Combine
#if os(iOS)
import CRPSmartRing
#endif

/// 自动化数据同步服务
/// 负责自动化地从设备获取所有类型的数据，保存到本地数据库，并上传到服务器
public class AutoDataSyncService: ObservableObject {
    // MARK: - 属性
    
    /// 单例实例
    public static let shared = AutoDataSyncService()
    
    /// 设备服务
    private let deviceService: WindRingDeviceService
    
    /// 温度上传服务
    private let temperatureService: TemperatureUploadService
    
    /// 活动上传服务
    private let activityService: ActivityUploadService
    
    /// 血氧上传服务
    private let bloodOxygenService: BloodOxygenUploadService
    
    /// 心率上传服务
    private let heartRateService: HeartRateUploadService
    
    /// HRV上传服务
    private let hrvService: HRVUploadService
    
    /// 压力上传服务
    private let stressService: StressUploadService
    
    /// 睡眠上传服务
    private let sleepService: SleepUploadService
    
    /// 原始数据上传服务
    private let rawDataService: RawDataUploadService
    
    /// 认证服务
    private let authService: AuthService
    
    /// GoMore服务
    private let goMoreService: GoMoreService
    
    /// 健康数据管理器
    private let healthDataManager: HealthDataManager
    
    /// 是否正在同步中
    @Published public var isSyncing: Bool = false
    
    /// 同步进度 (0-100)
    @Published public var syncProgress: Int = 0
    
    /// 同步状态消息
    @Published public var statusMessage: String = "准备同步"
    
    /// 同步日志
    @Published public var syncLogs: [String] = []
    
    /// 取消令牌
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    private init() {
        self.deviceService = WindRingDeviceService.shared
        self.temperatureService = TemperatureUploadService.shared
        self.activityService = ActivityUploadService.shared
        self.bloodOxygenService = BloodOxygenUploadService.shared
        self.heartRateService = HeartRateUploadService.shared
        self.hrvService = HRVUploadService.shared
        self.stressService = StressUploadService.shared
        self.sleepService = SleepUploadService.shared
        self.rawDataService = RawDataUploadService.shared
        self.authService = AuthService.shared
        self.goMoreService = GoMoreService.shared
        self.healthDataManager = HealthDataManager.shared
        
        // 监听设备连接状态变化
        deviceService.$connectionState
            .sink { [weak self] state in
                if state.isConnected {
                    self?.addLog("设备已连接: \(self?.deviceService.deviceInfo?.localName ?? "未知设备")")
                } else {
                    self?.addLog("设备已断开连接")
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 开始自动同步过程
    /// - Parameter completion: 完成回调，返回是否成功和错误信息
    public func startAutoSync(completion: @escaping (Bool, Error?) -> Void) {
        guard !isSyncing else {
            let error = NSError(domain: "AutoDataSync", code: 400, userInfo: [NSLocalizedDescriptionKey: "同步已在进行中"])
            completion(false, error)
            return
        }
        
        guard deviceService.connectionState.isConnected else {
            let error = NSError(domain: "AutoDataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"])
            completion(false, error)
            return
        }
        
        guard (authService.currentToken == nil) else {
            let error = NSError(domain: "AutoDataSync", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])
            completion(false, error)
            return
        }
        
        // 开始同步
        isSyncing = true
        syncProgress = 0
        statusMessage = "开始同步数据"
        syncLogs.removeAll()
        addLog("开始自动同步过程")
        
        // 启动原始数据自动上传
        startRawDataAutoUpload()
        
        // 执行同步流程
        syncAllData { success, error in
            DispatchQueue.main.async {
                self.isSyncing = false
                self.syncProgress = success ? 100 : 0
                self.statusMessage = success ? "同步完成" : "同步失败: \(error?.localizedDescription ?? "未知错误")"
                self.addLog(success ? "✅ 同步完成" : "❌ 同步失败: \(error?.localizedDescription ?? "未知错误")")
                completion(success, error)
            }
        }
    }
    
    /// 取消正在进行的同步
    public func cancelSync() {
        guard isSyncing else { return }
        // 目前没有实现取消特定操作的机制，只是标记状态
        isSyncing = false
        statusMessage = "同步已取消"
        addLog("⚠️ 同步已手动取消")
    }
    
    // MARK: - 公共调试方法
    
    /// 单独测试心率数据同步
    public func testHeartRateSync(completion: @escaping (Bool, Error?) -> Void) {
        addLog("🧪 开始测试心率数据同步")
        syncHeartRateData { success, error in
            self.addLog("🧪 心率数据同步\(success ? "成功" : "失败")\(error != nil ? "，错误：\(error!.localizedDescription)" : "")")
            completion(success, error)
        }
    }
    
    /// 单独测试HRV数据同步
    public func testHRVSync(completion: @escaping (Bool, Error?) -> Void) {
        addLog("🧪 开始测试HRV数据同步")
        syncHRVData { success, error in
            self.addLog("🧪 HRV数据同步\(success ? "成功" : "失败")\(error != nil ? "，错误：\(error!.localizedDescription)" : "")")
            completion(success, error)
        }
    }
    
    /// 单独测试压力数据同步 
    public func testStressSync(completion: @escaping (Bool, Error?) -> Void) {
        addLog("🧪 开始测试压力数据同步")
        syncStressData { success, error in
            self.addLog("🧪 压力数据同步\(success ? "成功" : "失败")\(error != nil ? "，错误：\(error!.localizedDescription)" : "")")
            completion(success, error)
        }
    }
    
    /// 单独测试血氧数据同步
    public func testBloodOxygenSync(completion: @escaping (Bool, Error?) -> Void) {
        addLog("🧪 开始测试血氧数据同步")
        syncBloodOxygenData { success, error in
            self.addLog("🧪 血氧数据同步\(success ? "成功" : "失败")\(error != nil ? "，错误：\(error!.localizedDescription)" : "")")
            completion(success, error)
        }
    }
    
    /// 单独测试温度数据同步
    public func testTemperatureSync(completion: @escaping (Bool, Error?) -> Void) {
        addLog("🧪 开始测试温度数据同步")
        syncTemperatureData { success, error in
            self.addLog("🧪 温度数据同步\(success ? "成功" : "失败")\(error != nil ? "，错误：\(error!.localizedDescription)" : "")")
            completion(success, error)
        }
    }
    
    /// 单独测试活动数据同步
    public func testActivitySync(completion: @escaping (Bool, Error?) -> Void) {
        addLog("🧪 开始测试活动数据同步")
        syncActivityData { success, error in
            self.addLog("🧪 活动数据同步\(success ? "成功" : "失败")\(error != nil ? "，错误：\(error!.localizedDescription)" : "")")
            completion(success, error)
        }
    }
    
    /// 单独测试睡眠数据同步
    public func testSleepSync(completion: @escaping (Bool, Error?) -> Void) {
        addLog("🧪 开始测试睡眠数据同步")
        
        // 如果设备连接，尝试通过正常同步获取数据
        if deviceService.connectionState.isConnected {
            syncSleepData { success, error in
                if success {
                    self.addLog("🧪 睡眠数据同步成功")
                    completion(true, nil)
                } else {
                    self.addLog("⚠️ 设备同步失败，尝试创建测试数据: \(error?.localizedDescription ?? "未知错误")")
                    self.createTestSleepData(completion: completion)
                }
            }
        } else {
            // 设备未连接，直接创建测试数据
            addLog("⚠️ 设备未连接，直接创建测试数据")
            createTestSleepData(completion: completion)
        }
    }
    
    // MARK: - 私有方法
    
    /// 同步所有类型的数据
    private func syncAllData(completion: @escaping (Bool, Error?) -> Void) {
        // 定义需要同步的数据类型及其权重
        let syncTasks: [(task: (@escaping (Bool, Error?) -> Void) -> Void, weight: Int)] = [
            (syncHeartRateData, 15),      // 心率 (15%)
            (syncHRVData, 10),            // HRV (10%)
            (syncStressData, 10),         // 压力 (10%)
            (syncBloodOxygenData, 10),    // 血氧 (10%)
            (syncTemperatureData, 10),    // 温度 (10%)
            (syncActivityData, 15),       // 活动 (15%)
            (syncSleepData, 30)           // 睡眠 (30%)
        ]
        
        // 总权重
        let totalWeight = syncTasks.reduce(0) { $0 + $1.weight }
        // 已完成的权重
        var completedWeight = 0
        
        // 创建一个串行队列执行同步任务
        let syncQueue = DispatchQueue(label: "com.windring.autoSync")
        var overallSuccess = true
        var lastError: Error? = nil
        
        // 递归函数，依次执行每个任务
        func executeNextTask(index: Int) {
            // 如果已经取消同步或完成所有任务，则返回
            if !self.isSyncing || index >= syncTasks.count {
                completion(overallSuccess, lastError)
                return
            }
            
            let currentTask = syncTasks[index]
            
            // 更新状态
            let taskName: String
            switch index {
            case 0: taskName = "心率数据"
            case 1: taskName = "HRV数据"
            case 2: taskName = "压力数据"
            case 3: taskName = "血氧数据"
            case 4: taskName = "温度数据"
            case 5: taskName = "活动数据"
            case 6: taskName = "睡眠数据"
            default: taskName = "未知数据"
            }
            
            DispatchQueue.main.async {
                self.statusMessage = "正在同步\(taskName)"
                self.addLog("开始同步\(taskName)")
            }
            
            // 执行当前任务
            currentTask.task { success, error in
                syncQueue.async {
                    // 更新总体成功状态
                    if !success {
                        overallSuccess = false
                        lastError = error
                    }
                    
                    // 更新进度
                    completedWeight += currentTask.weight
                    let progress = Int(Double(completedWeight) / Double(totalWeight) * 100)
                    
                    DispatchQueue.main.async {
                        self.syncProgress = progress
                        if success {
                            self.addLog("✅ \(taskName)同步成功")
                        } else {
                            self.addLog("❌ \(taskName)同步失败: \(error?.localizedDescription ?? "未知错误")")
                        }
                    }
                    
                    // 执行下一个任务
                    executeNextTask(index: index + 1)
                }
            }
        }
        
        // 开始执行第一个任务
        syncQueue.async {
            executeNextTask(index: 0)
        }
    }
    
    /// 同步心率数据
    private func syncHeartRateData(completion: @escaping (Bool, Error?) -> Void) {
        self.addLog("🚀 开始同步心率数据流程")
        
        // 检查前置条件
        guard deviceService.connectionState.isConnected else {
            self.addLog("❌ 设备未连接，无法获取心率数据")
            completion(false, NSError(domain: "AutoDataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        self.addLog("✅ 设备连接状态正常")
        
        guard (authService.currentToken == nil) else {
            self.addLog("❌ 用户未登录，无法获取心率数据")
            completion(false, NSError(domain: "AutoDataSync", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        self.addLog("✅ 用户已登录")
        
        // 先获取心率数据并保存到本地
        self.addLog("🔄 开始从设备获取心率数据（最近7天）...")
        fetchAndSaveHeartRateData(days: 7) { count, fetchError in
            if let fetchError = fetchError {
                self.addLog("❌ 获取心率数据失败: \(fetchError.localizedDescription)")
                completion(false, fetchError)
                return
            }
            
            self.addLog("✅ 从设备成功获取并保存了\(count)条心率数据")
            
            // 然后上传到服务器
            self.addLog("🔄 开始上传心率数据到服务器...")
            self.heartRateService.uploadPendingHeartRateData { count, uploadError in
                if let uploadError = uploadError {
                    self.addLog("❌ 上传心率数据失败: \(uploadError.localizedDescription)")
                    completion(false, uploadError)
                    return
                }
                
                self.addLog("✅ 成功上传了\(count)条心率数据到服务器")
                completion(true, nil)
            }
        }
    }
    
    /// 同步HRV数据
    private func syncHRVData(completion: @escaping (Bool, Error?) -> Void) {
        self.addLog("🚀 开始同步HRV数据流程")
        
        // 检查前置条件
        guard deviceService.connectionState.isConnected else {
            self.addLog("❌ 设备未连接，无法获取HRV数据")
            completion(false, NSError(domain: "AutoDataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        self.addLog("✅ 设备连接状态正常")
        
        guard (authService.currentToken == nil) else {
            self.addLog("❌ 用户未登录，无法获取HRV数据")
            completion(false, NSError(domain: "AutoDataSync", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        self.addLog("✅ 用户已登录")
        
        // 先获取HRV数据并保存到本地
        self.addLog("🔄 开始从设备获取HRV数据（最近7天）...")
        fetchAndSaveHRVData(days: 7) { count, fetchError in
            if let fetchError = fetchError {
                self.addLog("❌ 获取HRV数据失败: \(fetchError.localizedDescription)")
                completion(false, fetchError)
                return
            }
            
            self.addLog("✅ 从设备成功获取并保存了\(count)条HRV数据")
            
            // 然后上传到服务器
            self.addLog("🔄 开始上传HRV数据到服务器...")
            self.hrvService.uploadPendingHRVData { count, uploadError in
                if let uploadError = uploadError {
                    self.addLog("❌ 上传HRV数据失败: \(uploadError.localizedDescription)")
                    completion(false, uploadError)
                    return
                }
                
                self.addLog("✅ 成功上传了\(count)条HRV数据到服务器")
                completion(true, nil)
            }
        }
    }
    
    /// 同步压力数据
    private func syncStressData(completion: @escaping (Bool, Error?) -> Void) {
        self.addLog("🚀 开始同步压力数据流程")
        
        // 检查前置条件
        guard deviceService.connectionState.isConnected else {
            self.addLog("❌ 设备未连接，无法获取压力数据")
            completion(false, NSError(domain: "AutoDataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        self.addLog("✅ 设备连接状态正常")
        
        guard (authService.currentToken == nil) else {
            self.addLog("❌ 用户未登录，无法获取压力数据")
            completion(false, NSError(domain: "AutoDataSync", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        self.addLog("✅ 用户已登录")
        
        // 先获取压力数据并保存到本地
        self.addLog("🔄 开始从设备获取压力数据（最近7天）...")
        fetchAndSaveStressData(days: 7) { count, fetchError in
            if let fetchError = fetchError {
                self.addLog("❌ 获取压力数据失败: \(fetchError.localizedDescription)")
                completion(false, fetchError)
                return
            }
            
            self.addLog("✅ 从设备成功获取并保存了\(count)条压力数据")
            
            // 然后上传到服务器
            self.addLog("🔄 开始上传压力数据到服务器...")
            self.stressService.uploadPendingStressData { count, uploadError in
                if let uploadError = uploadError {
                    self.addLog("❌ 上传压力数据失败: \(uploadError.localizedDescription)")
                    completion(false, uploadError)
                    return
                }
                
                self.addLog("✅ 成功上传了\(count)条压力数据到服务器")
                completion(true, nil)
            }
        }
    }
    
    /// 同步血氧数据
    private func syncBloodOxygenData(completion: @escaping (Bool, Error?) -> Void) {
        self.addLog("🚀 开始同步血氧数据流程")
        
        // 检查前置条件
        guard deviceService.connectionState.isConnected else {
            self.addLog("❌ 设备未连接，无法获取血氧数据")
            completion(false, NSError(domain: "AutoDataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        self.addLog("✅ 设备连接状态正常")
        
        guard (authService.currentToken == nil) else {
            self.addLog("❌ 用户未登录，无法获取血氧数据")
            completion(false, NSError(domain: "AutoDataSync", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        self.addLog("✅ 用户已登录")
        
        // 先获取血氧数据并保存到本地
        self.addLog("🔄 开始从设备获取血氧历史数据...")
        fetchAndSaveBloodOxygenData(days: 7) { count, fetchError in
            if let fetchError = fetchError {
                self.addLog("❌ 获取血氧数据失败: \(fetchError.localizedDescription)")
                completion(false, fetchError)
                return
            }
            
            self.addLog("✅ 从设备成功获取并保存了\(count)条血氧数据")
            
            // 然后上传到服务器
            self.addLog("🔄 开始上传血氧数据到服务器...")
            self.bloodOxygenService.uploadPendingBloodOxygenData { count, uploadError in
                if let uploadError = uploadError {
                    self.addLog("❌ 上传血氧数据失败: \(uploadError.localizedDescription)")
                    completion(false, uploadError)
                    return
                }
                
                self.addLog("✅ 成功上传了\(count)条血氧数据到服务器")
                completion(true, nil)
            }
        }
    }
    
    /// 同步温度数据
    private func syncTemperatureData(completion: @escaping (Bool, Error?) -> Void) {
        self.addLog("🚀 开始同步温度数据流程")
        
        // 检查前置条件
        guard deviceService.connectionState.isConnected else {
            self.addLog("❌ 设备未连接，无法获取温度数据")
            completion(false, NSError(domain: "AutoDataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        self.addLog("✅ 设备连接状态正常")
        
        guard (authService.currentToken == nil) else {
            self.addLog("❌ 用户未登录，无法获取温度数据")
            completion(false, NSError(domain: "AutoDataSync", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        self.addLog("✅ 用户已登录")
        
        // 先获取温度数据并保存到本地
        self.addLog("🔄 开始从设备获取温度数据（最近7天）...")
        temperatureService.fetchAndSaveTemperatureData(days: 7) { count, fetchError in
            if let fetchError = fetchError {
                self.addLog("❌ 获取温度数据失败: \(fetchError.localizedDescription)")
                completion(false, fetchError)
                return
            }
            
            self.addLog("✅ 从设备成功获取并保存了\(count)条温度数据")
            
            // 然后上传到服务器
            self.addLog("🔄 开始上传温度数据到服务器...")
            self.temperatureService.uploadPendingTemperatureData { count, uploadError in
                if let uploadError = uploadError {
                    self.addLog("❌ 上传温度数据失败: \(uploadError.localizedDescription)")
                    completion(false, uploadError)
                    return
                }
                
                self.addLog("✅ 成功上传了\(count)条温度数据到服务器")
                completion(true, nil)
            }
        }
    }
    
    /// 同步活动数据
    private func syncActivityData(completion: @escaping (Bool, Error?) -> Void) {
        self.addLog("🚀 开始同步活动数据流程")
        
        // 检查前置条件
        guard deviceService.connectionState.isConnected else {
            self.addLog("❌ 设备未连接，无法获取活动数据")
            completion(false, NSError(domain: "AutoDataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        self.addLog("✅ 设备连接状态正常")
        
        guard (authService.currentToken == nil) else {
            self.addLog("❌ 用户未登录，无法获取活动数据")
            completion(false, NSError(domain: "AutoDataSync", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        self.addLog("✅ 用户已登录")
        
        // 先获取活动数据并保存到本地
        self.addLog("🔄 开始从设备获取当前活动数据...")
        fetchAndSaveActivityData { success, fetchError in
            if let fetchError = fetchError {
                self.addLog("❌ 获取活动数据失败: \(fetchError.localizedDescription)")
                completion(false, fetchError)
                return
            }
            
            self.addLog("✅ 从设备成功获取并保存了活动数据")
            
            // 然后上传到服务器
            self.addLog("🔄 开始上传活动数据到服务器...")
            self.activityService.uploadCurrentActivityData { success, uploadError in
                if let uploadError = uploadError {
                    self.addLog("❌ 上传活动数据失败: \(uploadError.localizedDescription)")
                    completion(false, uploadError)
                    return
                }
                
                if success {
                    self.addLog("✅ 成功上传活动数据到服务器")
                    
                    // 添加历史活动数据同步功能
                    self.addLog("🔄 开始同步历史活动数据...")
                    self.fetchAndSaveHistoricalActivityData(days: 7) { histCount, histError in
                        self.addLog("📊 历史活动数据同步完成: 成功获取\(histCount)天的数据")
                        // 即使历史数据同步有错误，也返回当前数据同步成功
                        completion(true, nil)
                    }
                } else {
                    self.addLog("⚠️ 上传活动数据到服务器过程完成，但可能未成功上传")
                    completion(success, nil)
                }
            }
        }
    }
    
    /// 获取并保存活动数据
    private func fetchAndSaveActivityData(completion: @escaping (Bool, Error?) -> Void) {
        self.addLog("📈 开始获取和保存活动数据")
        
        // 使用ActivityUploadService获取基本活动数据
        self.addLog("🔍 请求设备提供当前基本活动数据")
        activityService.getBasicActivityData { stepModel, error in
            if let error = error {
                self.addLog("❌ 获取活动数据失败: \(error.localizedDescription)")
                completion(false, error)
                return
            }
            
            if let stepModel = stepModel {
                self.addLog("✅ 获取活动数据成功：")
                self.addLog("📊 步数：\(stepModel.steps)步")
                self.addLog("📊 距离：\(stepModel.distance)米")
                self.addLog("📊 卡路里：\(stepModel.calory)卡")
                
                // 自动保存到本地数据库
                self.addLog("💾 活动数据已自动保存到本地数据库")
                completion(true, nil)
            } else {
                self.addLog("⚠️ 未能获取有效活动数据 (返回为nil)")
                completion(false, NSError(domain: "AutoDataSync", code: 500, userInfo: [NSLocalizedDescriptionKey: "未能获取有效活动数据"])) 
            }
        }
    }
    
    /// 获取并保存历史活动数据
    /// - Parameters:
    ///   - days: 要同步的天数
    ///   - completion: 完成回调
    private func fetchAndSaveHistoricalActivityData(days: Int, completion: @escaping (Int, Error?) -> Void) {
        self.addLog("📈 开始同步过去\(days)天的历史活动数据")
        
        guard deviceService.connectionState.isConnected else {
            self.addLog("❌ 设备未连接，无法获取历史活动数据")
            completion(0, NSError(domain: "AutoDataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        // 使用ActivityUploadService同步历史活动数据
        activityService.syncHistoricalActivityData(days: days) { count, error in
            if let error = error {
                self.addLog("⚠️ 历史活动数据同步过程中出现错误: \(error.localizedDescription)")
            }
            
            if count > 0 {
                self.addLog("✅ 成功同步\(count)天的历史活动数据")
            } else {
                self.addLog("⚠️ 未能同步任何历史活动数据")
            }
            
            completion(count, error)
        }
    }
    
    /// 同步睡眠数据
    private func syncSleepData(completion: @escaping (Bool, Error?) -> Void) {
        // 检查设备是否支持GoMore算法
        goMoreService.checkGoMoreSupport { isSupported, error in
            if error != nil {
                self.addLog("检查GoMore支持失败")
                // 继续使用基础算法
                self.syncBasicSleepData(completion: completion)
                return
            }
            
            if isSupported {
                self.addLog("设备支持GoMore睡眠算法，使用GoMore获取数据")
                // 使用包装器处理Int返回类型到Bool的转换
                self.syncGoMoreSleepData { count, error in
                    if let error = error {
                        completion(false, error)
                    } else {
                        completion(count > 0, nil)
                    }
                }
            } else {
                self.addLog("设备使用基础睡眠算法")
                self.syncBasicSleepData(completion: completion)
            }
        }
    }
    
    /// 同步基础睡眠数据
    private func syncBasicSleepData(completion: @escaping (Bool, Error?) -> Void) {
        fetchAndSaveBasicSleepData(days: 14) { count, fetchError in
            if let fetchError = fetchError {
                self.addLog("获取基础睡眠数据失败: \(fetchError.localizedDescription)")
                completion(false, fetchError)
                return
            }
            
            self.addLog("从设备获取了\(count)天基础睡眠数据")
            
            // 注释掉自动上传代码
            // 然后上传到服务器
            //self.sleepService.uploadPendingSleepData { count, uploadError in
            //    if let uploadError = uploadError {
            //        self.addLog("上传睡眠数据失败: \(uploadError.localizedDescription)")
            //        completion(false, uploadError)
            //        return
            //    }
            //    
            //    self.addLog("上传了\(count)条睡眠数据到服务器")
            //    completion(true, nil)
            //}
            
            // 数据已获取成功，不需要上传，直接返回成功
            completion(true, nil)
        }
    }
    
    /// 同步GoMore睡眠数据
    private func syncGoMoreSleepData(completion: @escaping (Int, Error?) -> Void) {
        self.addLog("🚀 开始同步GoMore睡眠数据流程")
        fetchAndSaveGoMoreSleepData { count, fetchError in
            if let fetchError = fetchError {
                self.addLog("❌ 获取GoMore睡眠数据失败: \(fetchError.localizedDescription)")
                completion(0, fetchError)
                return
            }
            
            self.addLog("✅ 从设备成功获取并保存了\(count)天GoMore睡眠数据")
            
            // 注释掉自动上传代码
            // 然后上传到服务器
            //self.sleepService.uploadPendingSleepData { count, uploadError in
            //    if let uploadError = uploadError {
            //        self.addLog("❌ 上传睡眠数据失败: \(uploadError.localizedDescription)")
            //        completion(0, uploadError)
            //        return
            //    }
            //    
            //    self.addLog("✅ 成功上传了\(count)条睡眠数据到服务器")
            //    completion(count, nil)
            //}
            
            // 数据已获取成功，不需要上传，直接返回成功
            completion(count, nil)
        }
    }
    
    // MARK: - 数据获取和保存方法
    
    /// 获取并保存心率数据
    private func fetchAndSaveHeartRateData(days: Int, completion: @escaping (Int, Error?) -> Void) {
        self.addLog("📈 开始获取和保存心率数据")
        
        // 使用HeartRateUploadService获取定时心率数据
        self.addLog("🔍 请求设备提供最近\(days)天的定时心率数据")
        heartRateService.syncTimingHeartRateData(days: days) { count, error in
            if let error = error {
                self.addLog("❌ 同步心率数据失败: \(error.localizedDescription)")
            } else {
                self.addLog("✅ 成功同步并保存了\(count)条心率数据")
            }
            completion(count, error)
        }
    }
    
    /// 获取并保存HRV数据
    private func fetchAndSaveHRVData(days: Int, completion: @escaping (Int, Error?) -> Void) {
        self.addLog("📈 开始获取和保存HRV数据")
        
        // 使用HRVUploadService获取定时HRV数据
        self.addLog("🔍 请求设备提供最近\(days)天的定时HRV数据")
        hrvService.syncTimingHRVData(days: days) { count, error in
            if let error = error {
                self.addLog("❌ 同步HRV数据失败: \(error.localizedDescription)")
            } else {
                self.addLog("✅ 成功同步并保存了\(count)条HRV数据")
            }
            completion(count, error)
        }
    }
    
    /// 获取并保存压力数据
    private func fetchAndSaveStressData(days: Int, completion: @escaping (Int, Error?) -> Void) {
        self.addLog("📈 开始获取和保存压力数据")
        
        // 使用StressUploadService获取定时压力数据
        self.addLog("🔍 请求设备提供最近\(days)天的定时压力数据")
        stressService.syncTimingStressData(days: days) { count, error in
            if let error = error {
                self.addLog("❌ 同步压力数据失败: \(error.localizedDescription)")
            } else {
                self.addLog("✅ 成功同步并保存了\(count)条压力数据")
            }
            completion(count, error)
        }
    }
    
    /// 获取并保存血氧数据
    private func fetchAndSaveBloodOxygenData(days: Int, completion: @escaping (Int, Error?) -> Void) {
        self.addLog("📈 开始获取和保存血氧数据")
        
        // 使用BloodOxygenUploadService获取血氧数据
        // 注意：这个方法不支持指定天数，默认会获取设备上所有的历史数据
        self.addLog("🔍 请求设备提供历史血氧数据(不限天数)")
        bloodOxygenService.fetchAndSaveBloodOxygenHistory { count, error in
            if let error = error {
                self.addLog("❌ 获取血氧历史数据失败: \(error.localizedDescription)")
            } else {
                self.addLog("✅ 成功获取并保存了\(count)条血氧历史数据")
            }
            completion(count, error)
        }
    }
    
    /// 获取并保存基础睡眠数据
    private func fetchAndSaveBasicSleepData(days: Int, completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "AutoDataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        // 获取用户ID
        guard let userId = authService.currentUser?.id else {
            completion(0, NSError(domain: "AutoDataSync", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        addLog("开始获取最近\(days)天的基础睡眠数据...")
        
        let group = DispatchGroup()
        var processedDays = 0
        var successDays = 0
        var lastError: Error? = nil
        
        // 获取最近几天的数据
        for day in 0..<min(days, 30) { // 最多获取30天数据
            group.enter()
            
            // 获取指定天数的睡眠数据
            deviceService.getSleepData(day: day) { sleepData, error in
                defer { group.leave() }
                
                processedDays += 1
                
                if let error = error {
                    self.addLog("获取第\(day)天的睡眠数据失败: \(error.localizedDescription)")
                    lastError = error
                    return
                }
                
                guard let sleepData = sleepData else {
                    self.addLog("第\(day)天未返回有效睡眠数据")
                    return
                }
                
                // 确保有有效的睡眠数据
                let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
                if totalMinutes > 0 {
                    // 使用传入的用户ID保存数据到本地数据库
                    let saved = self.saveSleepDataToLocalDB(sleepData: sleepData, userId: userId)
                    
                    if saved {
                        successDays += 1
                        self.addLog("成功保存第\(day)天的睡眠数据")
                    } else {
                        self.addLog("保存第\(day)天的睡眠数据失败")
                    }
                } else {
                    self.addLog("第\(day)天的睡眠数据无效（总睡眠时间为0）")
                }
            }
            
            // 稍微延迟一下，避免设备压力过大
            Thread.sleep(forTimeInterval: 0.5)
        }
        
        group.notify(queue: .main) {
            self.addLog("完成基础睡眠数据获取，成功获取并保存了\(successDays)天数据")
            completion(successDays, lastError)
        }
    }
    
    /// 保存睡眠数据到本地数据库
    private func saveSleepDataToLocalDB(sleepData: WindRingDeviceService.SleepData, userId: String) -> Bool {
        self.addLog("💾 开始保存睡眠数据: 开始时间=\(sleepData.startTime), 结束时间=\(sleepData.endTime)")
        self.addLog("💾 睡眠阶段数据: 深睡=\(sleepData.deepSleepMinutes)分钟, 浅睡=\(sleepData.lightSleepMinutes)分钟, REM=\(sleepData.remSleepMinutes)分钟, 清醒=\(sleepData.awakeMinutes)分钟")
        
        // 总睡眠时间
        let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
        
        // 使用SleepScoreService计算睡眠评分，确保评分计算逻辑一致性
        let sleepScoreService = SleepScoreService.shared
        let sleepScore: Int
        
        // 如果设备已提供睡眠质量评分，则优先使用
        if sleepData.sleepQuality > 0 {
            sleepScore = sleepData.sleepQuality
            self.addLog("💾 使用设备提供的睡眠评分: \(sleepScore)")
        } else {
            // 否则根据睡眠数据计算评分
            sleepScore = sleepScoreService.calculateSleepScore(
                sleepDuration: totalMinutes,
                bedDuration: totalMinutes + sleepData.awakeMinutes,
                deepSleepDuration: sleepData.deepSleepMinutes,
                wakeCount: 0  // 简化处理，无法从原始数据获取醒来次数
            )
            self.addLog("💾 计算出的睡眠评分: \(sleepScore) (使用SleepScoreService)")
        }
        
        // 获取睡眠评分等级
        let scoreLevel = sleepScoreService.getSleepScoreLevel(score: sleepScore)
        self.addLog("💾 睡眠评分等级: \(scoreLevel)")
        
        // 生成睡眠阶段数据
        let sleepStages = createSleepStages(from: sleepData)
        let stagesCount = sleepStages?.count ?? 0
        self.addLog("💾 生成了\(stagesCount)个睡眠阶段")
        
        self.addLog("💾 总睡眠时间: \(totalMinutes)分钟")
        
        // 保存到数据库
        let deviceId = deviceService.deviceInfo?.mac ?? "ring_device"
        self.addLog("💾 准备保存到数据库, 用户ID: \(userId), 设备ID: \(deviceId)")
        
        let success = healthDataManager.addSleep(
            userId: userId,
            startTime: sleepData.startTime,
            endTime: sleepData.endTime,
            totalMinutes: Int16(totalMinutes),
            deepMinutes: Int16(sleepData.deepSleepMinutes),
            lightMinutes: Int16(sleepData.lightSleepMinutes),
            remMinutes: Int16(sleepData.remSleepMinutes),
            awakeMinutes: Int16(sleepData.awakeMinutes),
            score: Int16(sleepScore),
            efficiency: Int16(85), // 默认值
            deviceId: deviceId,
            sleepStages: sleepStages
        )
        
        if success {
            self.addLog("✅ 成功保存睡眠数据到本地数据库，评分: \(sleepScore) (\(scoreLevel))")
        } else {
            self.addLog("❌ 保存睡眠数据到本地数据库失败")
        }
        
        return success
    }
    
    /// 创建睡眠阶段数据
    private func createSleepStages(from sleepData: WindRingDeviceService.SleepData) -> [(type: String, startTime: Date, duration: Int16)]? {
        let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
        if totalMinutes == 0 { return nil }
        
        var stages: [(type: String, startTime: Date, duration: Int16)] = []
        var currentTime = sleepData.startTime
        
        // 简化模型：先深睡，再浅睡，最后REM
        if sleepData.deepSleepMinutes > 0 {
            stages.append(("deep", currentTime, Int16(sleepData.deepSleepMinutes)))
            currentTime = Calendar.current.date(byAdding: .minute, value: Int(sleepData.deepSleepMinutes), to: currentTime) ?? currentTime
        }
        
        if sleepData.lightSleepMinutes > 0 {
            stages.append(("light", currentTime, Int16(sleepData.lightSleepMinutes)))
            currentTime = Calendar.current.date(byAdding: .minute, value: Int(sleepData.lightSleepMinutes), to: currentTime) ?? currentTime
        }
        
        if sleepData.remSleepMinutes > 0 {
            stages.append(("rem", currentTime, Int16(sleepData.remSleepMinutes)))
        }
        
        return stages
    }
    
    /// 获取并保存GoMore睡眠数据
    private func fetchAndSaveGoMoreSleepData(completion: @escaping (Int, Error?) -> Void) {
        self.addLog("🔍 开始检查获取GoMore睡眠数据的前置条件")
        
        guard deviceService.connectionState.isConnected else {
            self.addLog("❌ 设备未连接，无法获取GoMore睡眠数据")
            completion(0, NSError(domain: "AutoDataSync", code: 401, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        self.addLog("✅ 设备连接状态正常: \(deviceService.connectionState)")
        
        // 获取用户ID
        guard let userId = authService.currentUser?.id else {
            self.addLog("❌ 用户未登录，无法获取GoMore睡眠数据")
            completion(0, NSError(domain: "AutoDataSync", code: 402, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        self.addLog("✅ 用户已登录，ID: \(userId)")
        
        addLog("🌟 开始获取GoMore睡眠数据流程...")
        
        // 清空之前的睡眠ID
        SDKDelegate.shared.gomoreSleepIds = []
        self.addLog("🔄 已清空先前的GoMore睡眠ID列表")
        
        // 获取GoMore睡眠数据列表
        self.addLog("🔄 请求设备提供GoMore睡眠数据列表")
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataList()
        
        // 声明一个可变的观察者变量
        var observerRef: NSObjectProtocol?
        
        // 设置超时保护，避免无限等待
        self.addLog("⏱️ 设置15秒超时保护")
        DispatchQueue.main.asyncAfter(deadline: .now() + 15.0) { [weak self] in
            guard let self = self else { return }
            
            if SDKDelegate.shared.gomoreSleepIds.isEmpty {
                self.addLog("⚠️ 获取GoMore睡眠数据ID超时（15秒）")
                if let observer = observerRef {
                    NotificationCenter.default.removeObserver(observer)
                    self.addLog("🔄 已移除通知观察者")
                }
                completion(0, NSError(domain: "AutoDataSync", code: 406, userInfo: [NSLocalizedDescriptionKey: "获取GoMore睡眠数据ID超时"]))
            }
        }
        
        // 创建一个通知观察者，等待睡眠ID列表返回
        self.addLog("👀 创建通知观察者，等待GoMore睡眠ID列表")
        observerRef = NotificationCenter.default.addObserver(
            forName: .receivedGoMoreSleepIdsNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            guard let self = self else {
                print("AutoSync: ⚠️ self已被释放，无法继续处理GoMore睡眠数据")
                return
            }
            
            // 检查是否有睡眠数据ID
            let sleepIds = SDKDelegate.shared.gomoreSleepIds
            self.addLog("🔍 收到GoMore睡眠ID列表通知，ID数量: \(sleepIds.count)")
            
            if sleepIds.isEmpty {
                self.addLog("⚠️ 没有获取到GoMore睡眠数据ID，列表为空")
                if let observer = observerRef {
                    NotificationCenter.default.removeObserver(observer)
                    self.addLog("🔄 已移除通知观察者")
                }
                completion(0, NSError(domain: "AutoDataSync", code: 403, userInfo: [NSLocalizedDescriptionKey: "未找到GoMore睡眠数据"]))
                return
            }
            
            self.addLog("✅ 成功获取到\(sleepIds.count)个GoMore睡眠数据ID")
            if !sleepIds.isEmpty {
                self.addLog("🔍 睡眠ID列表: \(sleepIds)")
            }
            
            // 只处理最近14天的数据（如果有那么多）
            let idsToProcess = Array(sleepIds.prefix(14))
            self.addLog("🔄 将处理最近\(idsToProcess.count)个睡眠ID")
            
            // 跟踪处理进度
            var processedCount = 0
            var successCount = 0
            var lastError: Error? = nil
            
            // 使用DispatchGroup来协调多个异步请求
            let group = DispatchGroup()
            
            // 使用串行队列处理每个ID，避免设备压力过大
            let processingQueue = DispatchQueue(label: "com.windring.goMoreSleepDataProcessing", qos: .userInitiated)
            self.addLog("🔄 创建串行处理队列，准备处理睡眠数据")
            
            // 递归函数处理每个ID
            func processSleepId(at index: Int) {
                guard index < idsToProcess.count else {
                    // 所有ID已处理完毕
                    if let observer = observerRef {
                        NotificationCenter.default.removeObserver(observer)
                        self.addLog("🔄 已移除通知观察者")
                    }
                    self.addLog("✅ 完成所有GoMore睡眠数据处理，成功保存\(successCount)/\(idsToProcess.count)条数据")
                    completion(successCount, lastError)
                    return
                }
                
                let sleepId = idsToProcess[index]
                self.addLog("🔄 开始处理睡眠ID[\(index+1)/\(idsToProcess.count)]: \(sleepId)")
                
                group.enter()
                
                // 临时存储详细数据和分段数据
                var sleepDetail: CRPGoMoreSleepDataModel?
                var sleepSegment: CRPGoMoreSleepRecordModel?
                
                // 创建子组来等待两个请求完成
                let subGroup = DispatchGroup()
                self.addLog("🔄 创建子组等待详细数据和分段数据")
                
                // 获取睡眠详细数据
                subGroup.enter()
                self.addLog("🔍 请求睡眠ID \(sleepId) 的详细数据")
                CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataDetail(id: sleepId) { model, error in
                    defer { subGroup.leave() }
                    
                    if error == .none {
                        self.addLog("✅ 成功获取睡眠ID \(sleepId) 的详细数据")
                        sleepDetail = model
                        self.addLog("📊 睡眠详细数据: 开始时间=\(model.startTime), 结束时间=\(model.endTime), 评分=\(model.sleepScore)")
                    } else {
                        self.addLog("❌ 获取睡眠详细数据失败，ID: \(sleepId), 错误: \(error)")
                        lastError = NSError(domain: "AutoDataSync", code: 404, userInfo: [NSLocalizedDescriptionKey: "获取睡眠详细数据失败: \(error)"])
                    }
                }
                
                // 获取睡眠分段数据
                subGroup.enter()
                self.addLog("🔍 请求睡眠ID \(sleepId) 的分段数据")
                CRPSmartRingSDK.sharedInstance.getGoMoreSleepSegmentationData(id: sleepId) { model, error in
                    defer { subGroup.leave() }
                    
                    if error == .none {
                        self.addLog("✅ 成功获取睡眠ID \(sleepId) 的分段数据")
                        sleepSegment = model
                        self.addLog("📊 睡眠分段数据: 深睡=\(model.deep)分钟, 浅睡=\(model.light)分钟, REM=\(model.rem)分钟")
                    } else {
                        self.addLog("❌ 获取睡眠分段数据失败，ID: \(sleepId), 错误: \(error)")
                        lastError = NSError(domain: "AutoDataSync", code: 405, userInfo: [NSLocalizedDescriptionKey: "获取睡眠分段数据失败: \(error)"])
                    }
                }
                
                // 等待详细数据和分段数据都获取完毕
                subGroup.notify(queue: processingQueue) {
                    processedCount += 1
                    self.addLog("🔄 睡眠ID \(sleepId) 的详细数据和分段数据获取完成")
                    
                    if let detail = sleepDetail, let segment = sleepSegment {
                        self.addLog("✅ 睡眠ID \(sleepId) 的详细数据和分段数据均获取成功")
                        
                        // 转换时间戳为日期
                        let startTimestamp = Double(detail.startTime) / 1000.0
                        let endTimestamp = Double(detail.endTime) / 1000.0
                        let startDate = Date(timeIntervalSince1970: startTimestamp)
                        let endDate = Date(timeIntervalSince1970: endTimestamp)
                        
                        self.addLog("🔄 转换时间戳: \(detail.startTime) -> \(startDate), \(detail.endTime) -> \(endDate)")
                        
                        // 创建SleepData对象
                        let sleepData = WindRingDeviceService.SleepData(
                            startTime: startDate,
                            endTime: endDate,
                            deepSleepMinutes: segment.deep,
                            lightSleepMinutes: segment.light,
                            remSleepMinutes: segment.rem,
                            awakeMinutes: 0, // GoMore没有wake属性或者使用segment.wake
                            sleepQuality: Int(detail.sleepScore)
                        )
                        
                        self.addLog("🔄 创建SleepData对象成功")
                        
                        // 计算总睡眠时间
                        let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
                        self.addLog("📊 总睡眠时间: \(totalMinutes)分钟")
                        
                        if totalMinutes > 0 {
                            self.addLog("✅ 睡眠数据有效，总时间 > 0")
                            // 保存到本地数据库
                            if self.saveSleepDataToLocalDB(sleepData: sleepData, userId: userId) {
                                successCount += 1
                                self.addLog("✅ 成功保存睡眠ID \(sleepId) 的数据到本地数据库")
                            } else {
                                self.addLog("❌ 保存睡眠ID \(sleepId) 的数据到本地数据库失败")
                            }
                        } else {
                            self.addLog("⚠️ 睡眠ID \(sleepId) 的数据无效（总睡眠时间为0）")
                        }
                    } else {
                        if sleepDetail == nil {
                            self.addLog("⚠️ 睡眠ID \(sleepId) 的详细数据为空")
                        }
                        if sleepSegment == nil {
                            self.addLog("⚠️ 睡眠ID \(sleepId) 的分段数据为空")
                        }
                        self.addLog("❌ 未能获取完整的睡眠ID \(sleepId) 数据")
                    }
                    
                    group.leave()
                    
                    // 添加一个短暂延迟，避免设备压力过大
                    self.addLog("⏱️ 添加0.5秒延迟，避免设备压力过大")
                    Thread.sleep(forTimeInterval: 0.5)
                    
                    // 处理下一个ID
                    self.addLog("🔄 准备处理下一个睡眠ID")
                    processSleepId(at: index + 1)
                }
            }
            
            // 开始处理第一个ID
            self.addLog("🚀 开始处理第一个睡眠ID")
            processingQueue.async {
                processSleepId(at: 0)
            }
        }
    }
    
    /// 创建测试睡眠数据
    private func createTestSleepData(completion: @escaping (Bool, Error?) -> Void) {
        // 创建随机用户ID，如果用户ID为空
        let userId = "319" // 使用固定用户ID以便于测试
        
        let calendar = Calendar.current
        
        // 创建10天的测试数据
        addLog("🧪 开始创建测试睡眠数据")
        let healthDataManager = HealthDataManager.shared
        let storageManager = StorageManager.shared
        
        // 确保用户存在
//        if healthDataManager.getUser(id: userId) == nil {
//            addLog("👤 创建测试用户 \(userId)")
//            let success = healthDataManager.createUser(
//                id: userId,
//                name: "测试用户",
//                email: "test_\(userId)@example.com", completion: {success in
//                    if !success {
//                        self.addLog("❌ 创建测试用户失败")
//                        completion(false, NSError(domain: "AutoDataSync", code: 500, userInfo: [NSLocalizedDescriptionKey: "创建测试用户失败"]))
//                        return
//                    }
//                    
//                    // 立即保存用户创建
//                    storageManager.saveViewContext()
//                }
//            )
//            
//            
//        }
        
        var createdCount = 0
        var errorCount = 0
        
        // 清除原有数据
        addLog("🧹 清除用户 \(userId) 原有睡眠数据")
        
        // 创建过去10天的数据
        for day in 0..<10 {
            // 创建睡眠开始和结束时间
            let sleepDate = calendar.date(byAdding: .day, value: -day, to: Date())!
            let sleepStartTime = calendar.date(
                bySettingHour: 22,
                minute: Int.random(in: 0...59),
                second: 0,
                of: calendar.date(byAdding: .day, value: -1, to: sleepDate)!
            )!
            
            let sleepDuration = TimeInterval(Double(Int.random(in: 6...9)) * 3600 + Double(Int.random(in: 0...59)) * 60)
            let sleepEndTime = sleepStartTime.addingTimeInterval(sleepDuration)
            
            // 计算各阶段睡眠分钟数
            let totalMinutes = Int(sleepDuration / 60)
            let deepSleepMinutes = Int(Double(totalMinutes) * Double.random(in: 0.15...0.3))
            let remSleepMinutes = Int(Double(totalMinutes) * Double.random(in: 0.2...0.35))
            let lightSleepMinutes = totalMinutes - deepSleepMinutes - remSleepMinutes
            
            // 醒来时间（分钟）
            let awakeMinutes = Int(Double(totalMinutes) * Double.random(in: 0.05...0.15))
            
            // 使用SleepScoreService计算睡眠评分
            let sleepScoreService = SleepScoreService.shared
            let sleepScore = sleepScoreService.calculateSleepScore(
                sleepDuration: totalMinutes,
                bedDuration: totalMinutes + awakeMinutes,
                deepSleepDuration: deepSleepMinutes,
                wakeCount: Int.random(in: 1...5)
            )
            
            // 计算睡眠效率
            let bedMinutes = totalMinutes + awakeMinutes
            let sleepEfficiency = bedMinutes > 0 ? Int16(Float(totalMinutes) / Float(bedMinutes) * 100) : 0
            
            // 测试设备ID
            let deviceId = "TestDevice_\(String(format: "%02d", day % 3))"
            
            addLog("📝 创建第\(day+1)天睡眠数据: \(sleepStartTime) -> \(sleepEndTime)")
            
            // 保存到数据库
            let success = healthDataManager.addSleep(
                userId: userId,
                startTime: sleepStartTime,
                endTime: sleepEndTime,
                totalMinutes: Int16(totalMinutes),
                deepMinutes: Int16(deepSleepMinutes),
                lightMinutes: Int16(lightSleepMinutes),
                remMinutes: Int16(remSleepMinutes),
                awakeMinutes: Int16(awakeMinutes),
                score: Int16(sleepScore),
                efficiency: sleepEfficiency,
                deviceId: deviceId
            )
            
            if success {
                createdCount += 1
                addLog("✅ 第\(day+1)天睡眠数据创建成功")
            } else {
                errorCount += 1
                addLog("❌ 第\(day+1)天睡眠数据创建失败")
            }
            
            // 确保所有更改都保存到数据库
            storageManager.saveViewContext()
        }
        
        // 最终保存
        storageManager.saveViewContext()
        
        // 验证创建的数据
        let createdSleeps = healthDataManager.getSleep(userId: userId)
        addLog("📊 验证: 用户 \(userId) 现有 \(createdSleeps.count) 条睡眠记录")
        
        // 打印数据库状态
        if let storeURL = storageManager.persistentContainerPublic.persistentStoreCoordinator.persistentStores.first?.url {
            addLog("📁 数据存储在: \(storeURL.path)")
            
            // 检查文件是否存在
            let fileManager = FileManager.default
            if fileManager.fileExists(atPath: storeURL.path) {
                do {
                    let attributes = try fileManager.attributesOfItem(atPath: storeURL.path)
                    if let fileSize = attributes[.size] as? UInt64 {
                        addLog("📊 数据库文件大小: \(fileSize) 字节")
                    }
                } catch {
                    addLog("⚠️ 无法获取数据库文件信息: \(error.localizedDescription)")
                }
            } else {
                addLog("❌ 数据库文件不存在")
            }
        }
        
        if createdCount > 0 {
            addLog("🧪 睡眠测试数据创建完成: 成功创建 \(createdCount) 条记录")
            completion(true, nil)
        } else {
            addLog("❌ 睡眠测试数据创建失败")
            completion(false, NSError(domain: "AutoDataSync", code: 501, userInfo: [NSLocalizedDescriptionKey: "创建测试睡眠数据失败"]))
        }
    }
    
    // MARK: - 原始数据处理
    
    /// 启动原始数据自动上传
    private func startRawDataAutoUpload() {
        addLog("🔄 启动原始数据自动上传服务")
        
        let success = rawDataService.startAutoUpload(interval: 600) // 10分钟自动上传一次
        
        if success {
            addLog("✅ 原始数据自动上传服务已启动")
        } else {
            addLog("⚠️ 原始数据自动上传服务启动失败")
        }
    }
    
    /// 手动触发一次原始数据上传
    public func syncRawData(completion: @escaping (Bool, Error?) -> Void) {
        addLog("🔄 开始同步原始数据")
        
        rawDataService.uploadRawData { success, error in
            DispatchQueue.main.async {
                if success {
                    self.addLog("✅ 原始数据上传成功")
                } else if let error = error {
                    self.addLog("❌ 原始数据上传失败: \(error.localizedDescription)")
                } else {
                    self.addLog("❌ 原始数据上传失败: 未知错误")
                }
                completion(success, error)
            }
        }
    }
    
    /// 停止原始数据自动上传
    public func stopRawDataAutoUpload() {
        addLog("🛑 停止原始数据自动上传服务")
        rawDataService.stopAutoUpload()
        addLog("✅ 原始数据自动上传服务已停止")
    }
    
    // MARK: - 日志管理
    /// 添加日志
    /// - Parameter message: 日志消息
    public func addLog(_ message: String) {
        // 添加特定数据类型标记
        var prefix = ""
        if message.contains("心率") {
            prefix = "[❤️心率] "
        } else if message.contains("HRV") {
            prefix = "[💓HRV] "
        } else if message.contains("压力") {
            prefix = "[😓压力] "
        } else if message.contains("血氧") {
            prefix = "[🫁血氧] "
        } else if message.contains("温度") {
            prefix = "[🌡温度] "
        } else if message.contains("活动") {
            prefix = "[🏃活动] "
        } else if message.contains("睡眠") {
            prefix = "[😴睡眠] "
        } else {
            prefix = "[📱通用] "
        }
        
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        let logMessage = "[\(timestamp)] \(prefix)\(message)"
        
        DispatchQueue.main.async {
            self.syncLogs.insert(logMessage, at: 0)
            print("AutoSync: \(logMessage)")
            
            // 最多保存1000条日志
            if self.syncLogs.count > 1000 {
                self.syncLogs.removeLast()
            }
            
            // 保存到文件
            self.saveLogsToFile()
        }
    }
    
    /// 保存日志到文件
    private func saveLogsToFile() {
        guard !syncLogs.isEmpty else { return }
        
        let fileManager = FileManager.default
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let logsPath = documentsPath.appendingPathComponent("sync_logs.txt")
        
        let logContent = syncLogs.joined(separator: "\n")
        do {
            try logContent.write(to: logsPath, atomically: true, encoding: .utf8)
        } catch {
            print("无法保存日志到文件: \(error)")
        }
    }
    
    /// 获取日志文件URL
    public func getLogsFileURL() -> URL {
        let fileManager = FileManager.default
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsPath.appendingPathComponent("sync_logs.txt")
    }
    
    /// 清除日志
    public func clearLogs() {
        syncLogs.removeAll()
        
        // 同时清除文件
        let fileManager = FileManager.default
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let logsPath = documentsPath.appendingPathComponent("sync_logs.txt")
        
        do {
            if fileManager.fileExists(atPath: logsPath.path) {
                try fileManager.removeItem(at: logsPath)
            }
        } catch {
            print("无法删除日志文件: \(error)")
        }
    }
} 
