import Foundation

// MARK: - 健康分析模型

/// 健康概览
public struct HAHealthOverview {
    public let userId: String
    public let userName: String
    public let date: Date
    public let heartRate: HeartRateInfo?
    public let steps: StepsInfo?
    public let sleep: SleepInfo?
    
    /// 心率信息
    public struct HeartRateInfo {
        public let current: Int
        public let timestamp: Date
    }
    
    /// 步数信息
    public struct StepsInfo {
        public let count: Int
        public let caloriesBurned: Int?
        public let distanceKm: Double?
    }
    
    /// 睡眠信息
    public struct SleepInfo {
        public let durationHours: Double
        public let quality: Int?
        public let startTime: Date
        public let endTime: Date
    }
}

// MARK: - 健康建议模型

/// 建议类型
public enum HARecommendationType {
    case heartRate
    case steps
    case sleep
    case general
    
    public var description: String {
        switch self {
        case .heartRate:
            return "心率"
        case .steps:
            return "步数"
        case .sleep:
            return "睡眠"
        case .general:
            return "一般"
        }
    }
}

/// 健康建议
public struct HARecommendation {
    public let id: UUID
    public let userId: String
    public let type: HARecommendationType
    public let title: String
    public let content: String
    public let date: Date
    public let isRead: Bool
    
    public init(userId: String, type: HARecommendationType, title: String, content: String) {
        self.id = UUID()
        self.userId = userId
        self.type = type
        self.title = title
        self.content = content
        self.date = Date()
        self.isRead = false
    }
}

// MARK: - 健康目标模型

/// 目标类型
public enum HAGoalType {
    case steps
    case calories
    case distance
    case heartRate
    case sleep
    case water
    case weight
    
    public var description: String {
        switch self {
        case .steps:
            return "步数"
        case .calories:
            return "卡路里"
        case .distance:
            return "距离"
        case .heartRate:
            return "心率"
        case .sleep:
            return "睡眠"
        case .water:
            return "饮水量"
        case .weight:
            return "体重"
        }
    }
    
    public var unit: String {
        switch self {
        case .steps:
            return "步"
        case .calories:
            return "卡路里"
        case .distance:
            return "公里"
        case .heartRate:
            return "次/分钟"
        case .sleep:
            return "小时"
        case .water:
            return "毫升"
        case .weight:
            return "千克"
        }
    }
}

/// 目标周期
public enum HAGoalPeriod {
    case daily
    case weekly
    case monthly
    
    public var description: String {
        switch self {
        case .daily:
            return "每日"
        case .weekly:
            return "每周"
        case .monthly:
            return "每月"
        }
    }
}

/// 目标状态
public enum HAGoalStatus {
    case active
    case completed
    case failed
    case abandoned
    
    public var description: String {
        switch self {
        case .active:
            return "进行中"
        case .completed:
            return "已完成"
        case .failed:
            return "未达成"
        case .abandoned:
            return "已放弃"
        }
    }
}

/// 健康目标
public struct HAHealthGoal {
    public let id: UUID
    public let userId: String
    public let type: HAGoalType
    public let period: HAGoalPeriod
    public let targetValue: Double
    public let startDate: Date
    public let endDate: Date?
    public var currentValue: Double
    public var status: HAGoalStatus
    public let createdAt: Date
    public var updatedAt: Date
    
    /// 目标进度百分比
    public var progressPercentage: Double {
        return min(currentValue / targetValue * 100.0, 100.0)
    }
    
    /// 目标描述
    public var description: String {
        return "\(period.description)\(type.description)目标：\(Int(targetValue))\(type.unit)"
    }
    
    public init(userId: String, type: HAGoalType, period: HAGoalPeriod, targetValue: Double, startDate: Date, endDate: Date? = nil) {
        self.id = UUID()
        self.userId = userId
        self.type = type
        self.period = period
        self.targetValue = targetValue
        self.startDate = startDate
        self.endDate = endDate
        self.currentValue = 0
        self.status = .active
        self.createdAt = Date()
        self.updatedAt = Date()
    }
} 