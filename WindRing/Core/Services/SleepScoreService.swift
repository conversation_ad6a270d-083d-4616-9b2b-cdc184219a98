import Foundation

/// 睡眠评分服务
/// 负责计算睡眠评分并提供评估信息
public class SleepScoreService {
    // MARK: - 单例
    public static let shared = SleepScoreService()
    
    // MARK: - 依赖项
    private let healthDataManager = HealthDataManager.shared
    
    // MARK: - 初始化方法
    private init() {}
    
    // MARK: - 公共方法
    
    /// 计算睡眠评分
    /// - Parameters:
    ///   - sleepDuration: 睡眠时长（分钟）
    ///   - bedDuration: 卧床时长（分钟）
    ///   - deepSleepDuration: 深睡时长（分钟）
    ///   - wakeCount: 醒来次数
    /// - Returns: 睡眠评分(0-100)
    public func calculateSleepScore(
        sleepDuration: Int,
        bedDuration: Int,
        deepSleepDuration: Int,
        wakeCount: Int
    ) -> Int {
        // 睡眠时长得分 (最大100分)
        let durationScore = min(Float(sleepDuration) / 480.0 * 100.0, 100.0)
        
        // 睡眠效率得分 (最大100分)
        let efficiencyScore = bedDuration > 0 ? Float(sleepDuration) / Float(bedDuration) * 100.0 : 0
        
        // 深睡得分 (最大100分)
        let deepSleepScore = min(Float(deepSleepDuration) / 120.0 * 100.0, 100.0)
        
        // 醒来次数得分 (最大100分，每次醒来减10分)
        let wakeScore = max(100.0 - Float(wakeCount) * 10.0, 0.0)
        
        // 总分 (4项平均)
        let totalScore = (durationScore + efficiencyScore + deepSleepScore + wakeScore) / 4.0
        
        return Int(totalScore.rounded())
    }
    
    /// 计算睡眠评分（从SleepEntity获取数据）
    /// - Parameter sleep: 睡眠实体
    /// - Returns: 睡眠评分(0-100)
    public func calculateSleepScore(from sleep: SleepEntity) -> Int {
        // 如果实体已有评分且大于0，则直接返回
        if sleep.score > 0 {
            return Int(sleep.score)
        }
        
        let sleepDuration = Int(sleep.totalMinutes)
        let bedDuration = sleepDuration + Int(sleep.awakeMinutes)
        let deepSleepDuration = Int(sleep.deepMinutes)
        
        // 计算醒来次数（从睡眠阶段中统计）
        var wakeCount = 0
        if let sleepStages = sleep.sleepStages?.allObjects as? [SleepStageEntity] {
            wakeCount = sleepStages.filter { $0.type == "awake" }.count
        }
        
        return calculateSleepScore(
            sleepDuration: sleepDuration,
            bedDuration: bedDuration,
            deepSleepDuration: deepSleepDuration,
            wakeCount: wakeCount
        )
    }
    
    /// 获取睡眠评分等级
    /// - Parameter score: 睡眠评分
    /// - Returns: 评分等级描述
    public func getSleepScoreLevel(score: Int) -> String {
        switch score {
        case 90...100:
            return "优秀"
        case 80..<90:
            return "良好"
        case 70..<80:
            return "一般"
        case 60..<70:
            return "较差"
        default:
            return "糟糕"
        }
    }
    
    /// 获取睡眠评分颜色代码
    /// - Parameter score: 睡眠评分
    /// - Returns: 颜色代码（十六进制）
    public func getSleepScoreColor(score: Int) -> String {
        switch score {
        case 90...100:
            return "#00E676" // 绿色
        case 80..<90:
            return "#64DD17" // 浅绿色
        case 70..<80:
            return "#FFEB3B" // 黄色
        case 60..<70:
            return "#FFC107" // 橙色
        default:
            return "#F44336" // 红色
        }
    }
    
    /// 获取睡眠评分建议
    /// - Parameters:
    ///   - score: 睡眠评分
    ///   - sleepDuration: 睡眠时长（分钟）
    ///   - deepSleepDuration: 深睡时长（分钟）
    ///   - wakeCount: 醒来次数
    /// - Returns: 改善建议
    public func getSleepScoreSuggestion(
        score: Int,
        sleepDuration: Int,
        deepSleepDuration: Int,
        wakeCount: Int
    ) -> String {
        var suggestions: [String] = []
        
        // 睡眠时长建议
        if sleepDuration < 420 { // 7小时 = 420分钟
            suggestions.append("您的睡眠时间不足，建议每晚保持7-8小时的睡眠")
        } else if sleepDuration > 540 { // 9小时 = 540分钟
            suggestions.append("您的睡眠时间偏长，过长的睡眠也可能影响健康，建议控制在7-9小时")
        }
        
        // 深睡眠建议
        let deepSleepRatio = Float(deepSleepDuration) / Float(sleepDuration)
        if deepSleepRatio < 0.2 { // 深睡眠比例低于20%
            suggestions.append("您的深度睡眠比例偏低，可以尝试在睡前放松，避免咖啡因和酒精")
        }
        
        // 醒来次数建议
        if wakeCount > 2 {
            suggestions.append("您夜间醒来次数较多，建议改善睡眠环境，保持安静、黑暗和适宜温度")
        }
        
        // 如果没有特定建议，返回通用建议
        if suggestions.isEmpty {
            if score >= 80 {
                return "您的睡眠质量良好，继续保持规律的作息时间和健康的生活习惯"
            } else {
                return "提高睡眠质量的通用建议：保持规律作息、睡前放松、创造舒适的睡眠环境"
            }
        }
        
        return suggestions.joined(separator: "；") + "。"
    }
    
    // MARK: - 调试方法
    
    /// 打印评分计算过程（用于调试）
    /// - Parameters:
    ///   - sleepDuration: 睡眠时长（分钟）
    ///   - bedDuration: 卧床时长（分钟）
    ///   - deepSleepDuration: 深睡时长（分钟）
    ///   - wakeCount: 醒来次数
    public func debugCalculation(
        sleepDuration: Int,
        bedDuration: Int,
        deepSleepDuration: Int,
        wakeCount: Int
    ) {
        print("\n===== 睡眠评分计算调试 =====")
        print("输入参数:")
        print("- 睡眠时长: \(sleepDuration) 分钟 (\(sleepDuration/60)小时\(sleepDuration%60)分钟)")
        print("- 卧床时长: \(bedDuration) 分钟 (\(bedDuration/60)小时\(bedDuration%60)分钟)")
        print("- 深睡时长: \(deepSleepDuration) 分钟 (\(deepSleepDuration/60)小时\(deepSleepDuration%60)分钟)")
        print("- 醒来次数: \(wakeCount) 次")
        
        // 睡眠时长得分 (最大100分)
        let durationScore = min(Float(sleepDuration) / 480.0 * 100.0, 100.0)
        print("\n1. 睡眠时长得分计算:")
        print("   公式: min(睡眠时长 / 480 * 100, 100)")
        print("   计算: min(\(sleepDuration) / 480 * 100, 100) = \(durationScore)")
        
        // 睡眠效率得分 (最大100分)
        let efficiencyScore = bedDuration > 0 ? Float(sleepDuration) / Float(bedDuration) * 100.0 : 0
        print("\n2. 睡眠效率得分计算:")
        print("   公式: 睡眠时长 / 卧床时长 * 100")
        print("   计算: \(sleepDuration) / \(bedDuration) * 100 = \(efficiencyScore)")
        
        // 深睡得分 (最大100分)
        let deepSleepScore = min(Float(deepSleepDuration) / 120.0 * 100.0, 100.0)
        print("\n3. 深睡得分计算:")
        print("   公式: min(深睡时长 / 120 * 100, 100)")
        print("   计算: min(\(deepSleepDuration) / 120 * 100, 100) = \(deepSleepScore)")
        
        // 醒来次数得分 (最大100分，每次醒来减10分)
        let wakeScore = max(100.0 - Float(wakeCount) * 10.0, 0.0)
        print("\n4. 醒来次数得分计算:")
        print("   公式: max(100 - 醒来次数 * 10, 0)")
        print("   计算: max(100 - \(wakeCount) * 10, 0) = \(wakeScore)")
        
        // 总分 (4项平均)
        let totalScore = (durationScore + efficiencyScore + deepSleepScore + wakeScore) / 4.0
        let finalScore = Int(totalScore.rounded())
        print("\n总评分计算:")
        print("   公式: (时长得分 + 效率得分 + 深睡得分 + 醒来得分) / 4")
        print("   计算: (\(durationScore) + \(efficiencyScore) + \(deepSleepScore) + \(wakeScore)) / 4 = \(totalScore)")
        print("   最终评分(四舍五入): \(finalScore)")
        
        print("评分等级: \(getSleepScoreLevel(score: finalScore))")
        print("===== 调试结束 =====\n")
    }
    
    /// 调试已有的睡眠实体
    /// - Parameter sleep: 睡眠实体
    public func debugEntity(sleep: SleepEntity) {
        print("\n===== 睡眠实体调试 =====")
        print("实体信息:")
        print("- ID: \(sleep.id ?? "未知")")
        print("- 用户ID: \(sleep.userId ?? "未知")")
        print("- 总睡眠时间: \(sleep.totalMinutes) 分钟")
        print("- 深睡时间: \(sleep.deepMinutes) 分钟")
        print("- 浅睡时间: \(sleep.lightMinutes) 分钟")
        print("- REM睡眠: \(sleep.remMinutes) 分钟")
        print("- 清醒时间: \(sleep.awakeMinutes) 分钟")
        print("- 睡眠效率: \(sleep.efficiency)%")
        print("- 开始时间: \(sleep.startTime?.description ?? "未知")")
        print("- 结束时间: \(sleep.endTime?.description ?? "未知")")
        print("- 原始评分: \(sleep.score)")
        
        // 计算评分
        let calculatedScore = calculateSleepScore(from: sleep)
        print("\n计算出的评分: \(calculatedScore)")
        print("评分等级: \(getSleepScoreLevel(score: calculatedScore))")
        print("===== 实体调试结束 =====\n")
    }
} 