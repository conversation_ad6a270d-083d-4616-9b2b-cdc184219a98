import Foundation
import Combine
import Network
#if os(iOS)
import CRPSmartRing
#endif

/// MQTT同步服务
class MQTTSyncService: ObservableObject {
    // 单例
    public static let shared: MQTTSyncService = {
        let instance = MQTTSyncService()
        return instance
    }()
    
    // 服务
    private var mqttService: MQTTService!
    private var cacheManager: MQTTCacheManager!
    private var deviceService: WindRingDeviceService!
    
    // 网络监视器
    private let networkMonitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "com.windring.mqtt.networkMonitor")
    
    // 同步计时器
    private var syncTimer: Timer?
    private let syncInterval: TimeInterval = 60 // 1分钟同步一次
    
    // 发布者
    @Published var isSyncing: Bool = false
    @Published var lastSyncTime: Date?
    @Published var syncError: Error?
    
    // 取消令牌
    private var cancellables = Set<AnyCancellable>()
    
    // 初始化
    private init() {
        // 延迟初始化其他服务，避免循环引用
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.mqttService = MQTTService.shared
            self.cacheManager = MQTTCacheManager.shared
            self.deviceService = WindRingDeviceService.shared
            
            self.setupNetworkMonitoring()
            self.setupObservers()
            
            // 自动启动同步服务 - 如果不是用户主动断开的情况
            if !self.mqttService.userDisconnected && self.mqttService.autoConnect {
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                    self?.start()
                }
            }
        }
    }
    
    // MARK: - 公共方法
    
    /// 启动同步服务
    func start() {
        // 确保服务已初始化
        guard mqttService != nil else {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                self?.start()
            }
            return
        }
        
        print("MQTT同步服务 - 开始启动")
        
        // 如果用户没有主动断开连接，则自动连接MQTT
        if !mqttService.userDisconnected {
            // 连接MQTT
            mqttService.connect()
            
            // 订阅主题
            subscribeToTopics()
            
            // 启动同步计时器
            startSyncTimer()
            
            print("MQTT同步服务 - 已启动并连接")
        } else {
            print("MQTT同步服务 - 用户曾主动断开，不自动连接")
        }
    }
    
    /// 停止同步服务
    func stop() {
        // 确保服务已初始化
        guard mqttService != nil else { return }
        
        print("MQTT同步服务 - 正在停止")
        
        // 断开MQTT连接
        mqttService.disconnect()
        
        // 停止同步计时器
        stopSyncTimer()
        
        print("MQTT同步服务 - 已停止")
    }
    
    /// 手动同步
    func syncNow() {
        // 确保服务已初始化
        guard mqttService != nil && cacheManager != nil else { return }
        
        // 如果未连接，先尝试连接
        if !mqttService.connectionStatus && !mqttService.userDisconnected {
            mqttService.connect()
            
            // 延迟执行同步，等待连接成功
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
                self?.syncCachedData()
            }
            return
        }
        
        syncCachedData()
    }
    
    // MARK: - 私有方法
    
    /// 设置网络监视
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                guard let self = self, self.mqttService != nil else { return }
                
                if path.status == .satisfied {
                    // 网络连接恢复，如果用户未主动断开且设置为自动连接，则重连MQTT
                    if !self.mqttService.userDisconnected && self.mqttService.autoConnect {
                        print("网络已恢复，自动重连MQTT")
                        self.mqttService.connect()
                        self.syncCachedData()
                    } else {
                        print("网络已恢复，但未自动重连MQTT（用户已主动断开或关闭了自动连接）")
                    }
                } else {
                    // 网络断开，断开MQTT连接，但不设置userDisconnected标志
                    if self.mqttService.connectionStatus {
                        print("网络已断开，MQTT连接断开")
                        // 使用公共断开方法，不标记为用户主动断开
                        if let mqtt = self.mqttService.client {
                            mqtt.disconnect()
                        }
                        self.mqttService.connectionStatus = false
                    }
                }
            }
        }
        networkMonitor.start(queue: monitorQueue)
    }
    
    /// 设置观察者
    private func setupObservers() {
        guard deviceService != nil && mqttService != nil else { return }
        
        // 监听设备连接状态
        deviceService.$connectionState
            .sink { [weak self] state in
                if state == .connected {
                    // 设备已连接，订阅设备主题
                    self?.subscribeToDeviceTopics()
                }
            }
            .store(in: &cancellables)
        
        // 监听MQTT连接状态
        mqttService.$connectionStatus
            .sink { [weak self] connected in
                if connected {
                    // MQTT已连接，同步缓存数据
                    self?.syncCachedData()
                }
            }
            .store(in: &cancellables)
    }
    
    /// 订阅主题
    private func subscribeToTopics() {
        guard mqttService != nil else { return }
        
        // 订阅用户通知
        if let userId = UserDefaults.standard.string(forKey: "userId") {
            mqttService.subscribeToUserNotifications(userId: userId)
        }
        
        // 订阅设备主题
        subscribeToDeviceTopics()
    }
    
    /// 订阅设备主题
    private func subscribeToDeviceTopics() {
        guard mqttService != nil && deviceService != nil else { return }
        
        // 如果有连接的设备，订阅设备命令
        if let deviceInfo = deviceService.deviceInfo,
           let deviceId = deviceInfo.mac {
            mqttService.subscribeToRingCommands(ringId: deviceId)
        }
    }
    
    /// 启动同步计时器
    private func startSyncTimer() {
        stopSyncTimer()
        
        syncTimer = Timer.scheduledTimer(withTimeInterval: syncInterval, repeats: true) { [weak self] _ in
            self?.syncCachedData()
        }
    }
    
    /// 停止同步计时器
    private func stopSyncTimer() {
        syncTimer?.invalidate()
        syncTimer = nil
    }
    
    /// 同步缓存数据
    private func syncCachedData() {
        // 确保服务已初始化
        guard let mqttService = mqttService, let cacheManager = cacheManager else { return }
        
        // 如果MQTT未连接或正在同步，则返回
        guard mqttService.connectionStatus && !isSyncing else {
            return
        }
        
        isSyncing = true
        
        // 获取未同步的消息
        let messages = cacheManager.getUnsyncedMessages()
        
        if messages.isEmpty {
            // 没有未同步的消息
            isSyncing = false
            lastSyncTime = Date()
            return
        }
        
        // 同步消息
        var syncedCount = 0
        
        for cachedMessage in messages {
            guard let topic = cachedMessage.topic, let data = cachedMessage.message else {
                continue
            }
            
            do {
                // 将数据转换为JSON字符串
                if let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let jsonData = try? JSONSerialization.data(withJSONObject: jsonObject),
                   let jsonString = String(data: jsonData, encoding: .utf8) {
                    
                    // 使用MQTTService的公共方法发布消息
                    let message = MQTTMessage(type: .healthData, data: jsonObject)
                    mqttService.publish(to: topic, message: message)
                    
                    // 标记为已同步
                    cacheManager.markAsSynced(message: cachedMessage)
                    syncedCount += 1
                }
            } catch {
                print("消息发布失败: \(error)")
                syncError = error
                break
            }
        }
        
        print("已同步 \(syncedCount)/\(messages.count) 条消息")
        
        // 删除旧的已同步消息
        cacheManager.deleteOldSyncedMessages()
        
        isSyncing = false
        lastSyncTime = Date()
    }
    
    /// 缓存健康数据
    func cacheHealthData(ringId: String, data: [String: Any]) {
        guard let mqttService = mqttService, let cacheManager = cacheManager else { return }
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: data)
            let topic = mqttService.ringTopic(ringId: ringId, type: "health_data")
            cacheManager.cacheMessage(topic: topic, message: jsonData)
            
            // 如果MQTT已连接，立即同步
            if mqttService.connectionStatus {
                syncCachedData()
            }
        } catch {
            print("缓存健康数据失败: \(error)")
            syncError = error
        }
    }
    
    /// 缓存设备状态
    func cacheDeviceStatus(ringId: String, status: [String: Any]) {
        guard let mqttService = mqttService, let cacheManager = cacheManager else { return }
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: status)
            let topic = mqttService.ringTopic(ringId: ringId, type: "status")
            cacheManager.cacheMessage(topic: topic, message: jsonData)
            
            // 如果MQTT已连接，立即同步
            if mqttService.connectionStatus {
                syncCachedData()
            }
        } catch {
            print("缓存设备状态失败: \(error)")
            syncError = error
        }
    }
    
    /// 从设备获取睡眠数据并同步到本地数据库
    /// - Parameter completion: 完成回调，返回是否成功和获取的数据数量
    func syncSleepData(completion: @escaping (Bool, Int) -> Void) {
        guard let deviceService = deviceService else {
            print("❌ 设备服务未初始化")
            completion(false, 0)
            return
        }
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            print("❌ 设备未连接，无法同步睡眠数据")
            completion(false, 0)
            return
        }
        
        print("===== 开始同步睡眠数据 =====")
        
        // 获取当前用户信息
//        let users = HealthDataManager.shared.getAllUsers()
//        if users == nil || users!.isEmpty {
//            print("⚠️ 系统中没有用户记录，将使用临时用户ID")
//        } else {
//            print("✅ 系统中存在 \(users!.count) 个用户记录")
//            print("📊 用户列表: \(users!.map { $0.id ?? "未知" })")
//        }
        
        // 设置查询时间范围（比如最近30天）
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -30, to: endDate)!
        
        print("查询时间范围（日广度）: \(startDate) 至 \(endDate)")
        print("正在连接MQTT服务器: \(mqttService != nil ? "connected".localized : "disconnected".localized)")
        
        var dataCount = 0
        
        // 首先检查设备是否支持GoMore算法
        print("正在检查设备是否支持GoMore算法...")
        GoMoreService.shared.checkGoMoreSupport { isSupported, error in
            if isSupported {
                print("✅ 设备支持GoMore算法，使用GoMore接口获取睡眠数据")
                self.syncGoMoreSleepData { success, count in
                    completion(success, count)
                }
            } else {
                print("ℹ️ 设备使用基础睡眠算法，使用基础接口获取睡眠数据")
                self.syncBasicSleepData { success, count in
                    completion(success, count)
                }
            }
        }
    }
    
    /// 使用基础睡眠算法同步数据
    private func syncBasicSleepData(completion: @escaping (Bool, Int) -> Void) {
        guard let deviceService = deviceService else {
            completion(false, 0)
            return
        }
        
        // 使用健康数据管理器获取当前用户
//        let users = HealthDataManager.shared.getAllUsers()
//        print("📊 系统中的用户列表: \(users?.map { $0.id ?? "未知" } ?? ["无用户"])")
//        
//        // 确定要使用的用户ID
//        let userId = users?.first?.id ?? "current_user"
//        print("🔍 使用用户ID: \(userId) 获取基础睡眠数据")
        
        var dataCount = 0
        
        // 创建一个函数用于递归获取睡眠数据
        func fetchDayData(day: Int, maxDays: Int) {
            guard day < maxDays else {
                // 所有天数都已处理完成
                print("===== 基础睡眠数据同步完成 =====")
                print("共同步 \(dataCount) 条睡眠记录")
                completion(true, dataCount)
                return
            }
            
            print("正在获取 \(day) 天前的睡眠数据...")
            
            // 从设备获取某一天的睡眠数据
            deviceService.getSleepData(day: day) { sleepData, error in
                if let error = error {
                    print("获取第 \(day) 天睡眠数据失败: \(error)")
                    // 继续获取下一天
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        fetchDayData(day: day + 1, maxDays: maxDays)
                    }
                    return
                }
                
                if let sleepData = sleepData {
                    // 计算总睡眠时间
                    let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
                    
                    if totalMinutes > 0 {
                        print("✅ 获取到第 \(day) 天睡眠数据: \(totalMinutes) 分钟")
                        
                        // 保存到本地数据库
//                        self.saveSleepDataToLocalDB(sleepData: sleepData, userId: userId)
                        dataCount += 1
                    } else {
                        print("⚠️ 第 \(day) 天没有有效睡眠数据")
                    }
                } else {
                    print("⚠️ 第 \(day) 天睡眠数据为空")
                }
                
                // 延迟获取下一天数据，避免设备过载
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    fetchDayData(day: day + 1, maxDays: maxDays)
                }
            }
        }
        
        // 开始获取数据，从第0天（今天）开始，最多获取14天
        fetchDayData(day: 0, maxDays: 14)
    }
    
    /// 使用GoMore算法同步数据
    private func syncGoMoreSleepData(completion: @escaping (Bool, Int) -> Void) {
        guard let deviceService = deviceService else {
            completion(false, 0)
            return
        }
        
        print("===== 开始同步GoMore睡眠数据 =====")
        var dataCount = 0
        
        // 声明变量但不立即初始化
        var observer: NSObjectProtocol?
        
        // 设置超时处理
        let timeoutHandler = DispatchWorkItem { [weak self] in
            guard let self = self else { return }
            
            // 如果10秒后仍未收到数据，使用基础算法
            if dataCount == 0 {
                print("⚠️ 获取GoMore睡眠数据列表超时")
                print("尝试使用基础算法作为备选...")
                
                // 清理通知监听器
                if let obs = observer {
                    NotificationCenter.default.removeObserver(obs)
                    observer = nil
                }
                
                self.syncBasicSleepData(completion: completion)
            }
        }
        
        // 设置通知监听器，接收GoMore睡眠ID列表
        observer = NotificationCenter.default.addObserver(
            forName: Notification.Name.receivedGoMoreSleepIdsNotification,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self else { return }
            
            // 获取GoMore睡眠ID列表
            if let sleepIds = notification.userInfo?["sleepIds"] as? [Int], !sleepIds.isEmpty {
                print("✅ 收到GoMore睡眠ID列表，共 \(sleepIds.count) 条记录")
                
                // 取消超时处理
                timeoutHandler.cancel()
                
                // 处理每个睡眠ID
                self.processGoMoreSleepIds(sleepIds) { processedCount in
                    // 清理通知监听器
                    if let obs = observer {
                        NotificationCenter.default.removeObserver(obs)
                        observer = nil
                    }
                    
                    print("===== GoMore睡眠数据同步完成 =====")
                    print("共同步 \(processedCount) 条睡眠记录")
                    completion(true, processedCount)
                }
            } else {
                print("⚠️ 没有收到有效的GoMore睡眠ID列表")
                
                // 没有收到有效ID，尝试基础算法
                print("尝试使用基础算法作为备选...")
                self.syncBasicSleepData(completion: completion)
                
                // 清理通知监听器
                if let obs = observer {
                    NotificationCenter.default.removeObserver(obs)
                    observer = nil
                }
            }
        }
        
        // 请求GoMore睡眠数据列表
        print("请求GoMore睡眠数据列表...")
//        CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataList()
        
        // 设置超时处理
        DispatchQueue.main.asyncAfter(deadline: .now() + 10.0, execute: timeoutHandler)
    }
    
    /// 处理GoMore睡眠ID列表
    private func processGoMoreSleepIds(_ sleepIds: [Int], completion: @escaping (Int) -> Void) {
        var processedCount = 0
        var processIndex = 0
        
        // 使用健康数据管理器获取当前用户
//        let users = HealthDataManager.shared.getAllUsers()
//        print("📊 系统中的用户列表: \(users?.map { $0.id ?? "未知" } ?? ["无用户"])")
        
        // 确定要使用的用户ID
//        let userId = users?.first?.id ?? "current_user"
//        print("🔍 使用用户ID: \(userId) 处理GoMore睡眠数据")
        
        // 处理所有睡眠ID
        func processSleepId(at index: Int) {
            guard index < sleepIds.count else {
                // 所有ID处理完成
                completion(processedCount)
                return
            }
            
            let sleepId = sleepIds[index]
            print("正在处理GoMore睡眠ID: \(sleepId)")
            
            // 同时获取详细数据和分段数据
            var sleepDetail: CRPSmartRing.CRPGoMoreSleepDataModel?
            var sleepSegment: CRPSmartRing.CRPGoMoreSleepRecordModel?
            let group = DispatchGroup()
            
            // 获取详细数据
            group.enter()
            CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataDetail(id: sleepId) { model, error in
                defer { group.leave() }
                
                if error == .none {
                    print("✅ 获取到GoMore睡眠详细数据，ID: \(sleepId)")
                    sleepDetail = model
                } else {
                    print("❌ 获取GoMore睡眠详细数据失败，ID: \(sleepId), 错误: \(error)")
                }
            }
            
            // 获取分段数据
            group.enter()
            CRPSmartRingSDK.sharedInstance.getGoMoreSleepSegmentationData(id: sleepId) { model, error in
                defer { group.leave() }
                
                if error == .none {
                    print("✅ 获取到GoMore睡眠分段数据，ID: \(sleepId)")
                    sleepSegment = model
                } else {
                    print("❌ 获取GoMore睡眠分段数据失败，ID: \(sleepId), 错误: \(error)")
                }
            }
            
            // 等待两个请求完成
            group.notify(queue: .main) {
                if sleepDetail != nil {
                    if sleepSegment != nil {
                        print("✅ 合并睡眠详细数据和分段数据，ID: \(sleepId)")
//                        self.saveCombinedSleepData(sleepDetail: sleepDetail!, sleepSegment: sleepSegment!, userId: userId)
                        processedCount += 1
                    } else {
                        print("⚠️ 只有睡眠详细数据，没有分段数据，使用默认值，ID: \(sleepId)")
                        if let sleepData = self.convertGoMoreSleepDataToSleepData(sleepDetail!) {
//                            self.saveSleepDataToLocalDB(sleepData: sleepData, userId: userId)
                            processedCount += 1
                        }
                    }
                } else {
                    print("❌ 没有获取到有效的睡眠数据，跳过ID: \(sleepId)")
                }
                
                // 延迟处理下一个ID
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    processSleepId(at: index + 1)
                }
            }
        }
        
        // 开始处理第一个ID
        processSleepId(at: 0)
    }
    
    /// 保存合并的GoMore睡眠数据
    private func saveCombinedSleepData(sleepDetail: CRPSmartRing.CRPGoMoreSleepDataModel, sleepSegment: CRPSmartRing.CRPGoMoreSleepRecordModel, userId: String) {
        // 创建时间
        let startTime = Date(timeIntervalSince1970: Double(sleepDetail.startTime) / 1000.0)
        let endTime = Date(timeIntervalSince1970: Double(sleepDetail.endTime) / 1000.0)
        
        // 从分段数据中获取睡眠阶段
        let deepSleepMinutes = sleepSegment.deep
        let lightSleepMinutes = sleepSegment.light
        let remSleepMinutes = sleepSegment.rem
        let awakeMinutes = 0 // 可能需要从其他字段计算
        
        // 计算睡眠评分
        let sleepQuality = Int(sleepDetail.sleepScore)
        
        // 创建睡眠数据对象
        let sleepData = WindRingDeviceService.SleepData(
            startTime: startTime,
            endTime: endTime,
            deepSleepMinutes: deepSleepMinutes,
            lightSleepMinutes: lightSleepMinutes,
            remSleepMinutes: remSleepMinutes,
            awakeMinutes: awakeMinutes,
            sleepQuality: sleepQuality
        )
        
        // 保存到本地数据库
        saveSleepDataToLocalDB(sleepData: sleepData, userId: userId)
    }
    
    /// 将GoMore睡眠数据转换为通用睡眠数据对象
    private func convertGoMoreSleepDataToSleepData(_ goMoreSleepData: Any) -> WindRingDeviceService.SleepData? {
        // 安全检查：确保输入是正确的类型
        guard let sleepDetail = goMoreSleepData as? CRPSmartRing.CRPGoMoreSleepDataModel else {
            print("❌ 无法转换GoMore睡眠数据，类型不匹配")
            return nil
        }
        
        // 从GoMore数据中提取信息
        let startTimeStamp = sleepDetail.startTime
        let endTimeStamp = sleepDetail.endTime
        
        // 转换时间戳为Date对象（假设时间戳是毫秒）
        let startTime = Date(timeIntervalSince1970: Double(startTimeStamp) / 1000.0)
        let endTime = Date(timeIntervalSince1970: Double(endTimeStamp) / 1000.0)
        
        // 获取分段数据以提取睡眠阶段
        // 注意：在实际应用中，我们需要通过额外的API调用来获取CRPGoMoreSleepRecordModel
        // 这里我们使用默认值，实际项目中应当等待CRPGoMoreSleepRecordModel的回调
        let deepSleepMinutes = 0
        let lightSleepMinutes = 0
        let remSleepMinutes = 0
        let awakeMinutes = 0
        
        // 使用睡眠评分，如果可用
        let sleepQuality = Int(sleepDetail.sleepScore)
        
        print("✅ 转换GoMore睡眠数据")
        print("  - 开始时间戳: \(startTimeStamp) -> \(startTime)")
        print("  - 结束时间戳: \(endTimeStamp) -> \(endTime)")
        print("  - 睡眠评分: \(sleepDetail.sleepScore)")
        print("  - 睡眠效率: \(sleepDetail.sleepEfficiency)")
        
        // 创建通用睡眠数据对象
        let sleepData = WindRingDeviceService.SleepData(
            startTime: startTime,
            endTime: endTime,
            deepSleepMinutes: deepSleepMinutes,
            lightSleepMinutes: lightSleepMinutes,
            remSleepMinutes: remSleepMinutes,
            awakeMinutes: awakeMinutes,
            sleepQuality: sleepQuality
        )
        
        return sleepData
    }
    
    /// 保存睡眠数据到本地数据库
    private func saveSleepDataToLocalDB(sleepData: WindRingDeviceService.SleepData, userId: String) {
        print("🔍 开始保存睡眠数据: \(sleepData)")
        print("🔍 用户ID: \(userId)")
        
        // 检查数据有效性
        guard sleepData.startTime < sleepData.endTime else {
            print("❌ 睡眠数据无效: 开始时间(\(sleepData.startTime))不早于结束时间(\(sleepData.endTime))")
            return
        }
        
        // 检查睡眠总时长是否合理
        let totalCalculatedMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
        let timeIntervalMinutes = Int(sleepData.endTime.timeIntervalSince(sleepData.startTime) / 60)
        if totalCalculatedMinutes > (timeIntervalMinutes + 30) { // 允许30分钟的误差
            print("⚠️ 警告: 计算的总睡眠时间(\(totalCalculatedMinutes)分钟)远大于开始结束时间间隔(\(timeIntervalMinutes)分钟)")
        }
        
        // 检查用户是否存在
        let healthDataManager = HealthDataManager.shared
        let storageManager = StorageManager.shared
        
        // 强制保存当前上下文，确保之前的数据已写入
        storageManager.saveViewContext()
        
//        if healthDataManager.getUser(id: userId) == nil {
//            print("❌ 睡眠数据保存失败: 用户ID \(userId) 不存在")
//            print("👉 创建临时用户以保存数据")
//            
//            // 创建临时用户
//            healthDataManager.createUser(
//                id: userId,
//                name: "临时用户",
//                email: "temp_\(userId)@example.com", completion: {_ in 
//                    
//                }
//            )
//        }
        
        // 预先查询是否已存在相同时间段的睡眠数据
        // 使用较宽松的时间窗口进行查询，以便找到近似的记录
        let existingSleeps = healthDataManager.getSleep(userId: userId, startDate: sleepData.startTime.addingTimeInterval(-3600), endDate: sleepData.endTime.addingTimeInterval(3600))
        if !existingSleeps.isEmpty {
            print("⚠️ 警告: 已存在\(existingSleeps.count)条相似时间段的睡眠数据")
            
            // 检查是否几乎相同的记录，如果是，则跳过保存
            for existingSleep in existingSleeps {
                let startDiff = abs(existingSleep.startTime?.timeIntervalSince(sleepData.startTime) ?? 0)
                let endDiff = abs(existingSleep.endTime?.timeIntervalSince(sleepData.endTime) ?? 0)
                
                if startDiff < 60 && endDiff < 60 {
                    print("⚠️ 发现几乎相同的睡眠记录，跳过保存")
                    print("  - 现有记录: 开始=\(existingSleep.startTime?.description ?? "未知"), 结束=\(existingSleep.endTime?.description ?? "未知")")
                    print("  - 新记录: 开始=\(sleepData.startTime), 结束=\(sleepData.endTime)")
                    return
                }
            }
        }
        
        // 总睡眠时间
        let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
        print("🔍 总睡眠时间(分钟): \(totalMinutes)")
        print("🔍 深睡时间(分钟): \(sleepData.deepSleepMinutes)")
        print("🔍 浅睡时间(分钟): \(sleepData.lightSleepMinutes)")
        print("🔍 REM时间(分钟): \(sleepData.remSleepMinutes)")
        
        // 使用SleepScoreService计算睡眠评分，确保评分计算逻辑一致性
        let sleepScoreService = SleepScoreService.shared
        let sleepScore: Int
        
        // 如果设备已提供睡眠质量评分，则优先使用
        if sleepData.sleepQuality > 0 {
            sleepScore = sleepData.sleepQuality
            print("🔍 使用设备提供的睡眠评分: \(sleepScore)")
        } else {
            // 否则根据睡眠数据计算评分
            sleepScore = sleepScoreService.calculateSleepScore(
                sleepDuration: totalMinutes,
                bedDuration: totalMinutes + sleepData.awakeMinutes,
                deepSleepDuration: sleepData.deepSleepMinutes,
                wakeCount: 0  // 简化处理，无法从原始数据获取醒来次数
            )
            print("🔍 计算的睡眠评分: \(sleepScore) (使用SleepScoreService)")
        }
        
        // 获取睡眠评分等级
        let scoreLevel = sleepScoreService.getSleepScoreLevel(score: sleepScore)
        print("🔍 睡眠评分等级: \(scoreLevel)")
        
        // 计算睡眠效率 (SleepData对象没有sleepEfficiency属性)
        // 睡眠效率 = 总睡眠时间 / 床上时间(包括清醒时间) * 100
        let bedMinutes = totalMinutes + sleepData.awakeMinutes
        let sleepEfficiency = bedMinutes > 0 ? Int16(Float(totalMinutes) / Float(bedMinutes) * 100) : 0
        print("🔍 计算的睡眠效率: \(sleepEfficiency)%")
        
        // 生成唯一的记录ID，以便后续验证
        let sleepRecordId = generateUniqueId()
        print("📝 生成睡眠记录ID: \(sleepRecordId)")
        
        // 保存到数据库
        print("💾 尝试保存睡眠数据到数据库...")
        let success = healthDataManager.addSleep(
            userId: userId,
            startTime: sleepData.startTime,
            endTime: sleepData.endTime,
            totalMinutes: Int16(totalMinutes),
            deepMinutes: Int16(sleepData.deepSleepMinutes),
            lightMinutes: Int16(sleepData.lightSleepMinutes),
            remMinutes: Int16(sleepData.remSleepMinutes),
            awakeMinutes: Int16(sleepData.awakeMinutes),
            score: Int16(sleepScore),
            efficiency: sleepEfficiency,
            deviceId: deviceService.deviceInfo?.mac ?? "unknown_device"
        )
        
        if success {
            print("✅ 睡眠数据保存成功")
            
            // 确保事务已提交
            storageManager.saveViewContext()
            print("💾 已保存视图上下文")
            
            // 延迟验证数据已保存
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.validateSleepDataSaved(userId: userId, startTime: sleepData.startTime, endTime: sleepData.endTime)
            }
        } else {
            print("❌ 睡眠数据保存失败")
        }
    }
    
    /// 验证睡眠数据是否成功保存
    private func validateSleepDataSaved(userId: String, startTime: Date, endTime: Date) {
        // 验证数据已保存
        let healthDataManager = HealthDataManager.shared
        let savedSleeps = healthDataManager.getSleep(userId: userId, startDate: startTime.addingTimeInterval(-60), endDate: endTime.addingTimeInterval(60))
        print("✅ 验证: 查询到\(savedSleeps.count)条相似时间段的睡眠数据")
        
        if savedSleeps.isEmpty {
            print("⚠️ 警告: 无法验证睡眠数据是否成功保存，尝试重新保存")
            
            // 再次保存视图上下文
            StorageManager.shared.saveViewContext()
            
            // 再次验证
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                let recheckSleeps = healthDataManager.getSleep(userId: userId, startDate: startTime.addingTimeInterval(-60), endDate: endTime.addingTimeInterval(60))
                print("🔄 再次验证: 查询到\(recheckSleeps.count)条相似时间段的睡眠数据")
                
                if recheckSleeps.isEmpty {
                    print("❌ 错误: 数据库保存操作可能失败，检查CoreData配置")
                }
            }
        }
        
        // 额外验证 - 查询所有睡眠数据
        let allSleeps = healthDataManager.getSleep(userId: userId)
        print("📊 用户当前总共有\(allSleeps.count)条睡眠记录")
        
        // 打印存储信息
        printDatabaseInfo()
    }
    
    /// 打印数据库信息
    private func printDatabaseInfo() {
        if let storeURL = StorageManager.shared.persistentContainerPublic.persistentStoreCoordinator.persistentStores.first?.url {
            print("📁 数据存储在: \(storeURL.path)")
            
            // 检查文件是否存在
            let fileManager = FileManager.default
            if fileManager.fileExists(atPath: storeURL.path) {
                print("✅ 数据库文件存在")
                
                // 查看文件大小
                do {
                    let attributes = try fileManager.attributesOfItem(atPath: storeURL.path)
                    if let fileSize = attributes[.size] as? UInt64 {
                        print("📊 数据库文件大小: \(fileSize) 字节")
                    }
                    if let modDate = attributes[.modificationDate] as? Date {
                        print("📊 最后修改时间: \(modDate)")
                    }
                } catch {
                    print("⚠️ 无法获取数据库文件属性: \(error)")
                }
            } else {
                print("❌ 警告: 数据库文件不存在")
            }
        }
    }
    
    /// 生成唯一ID
    private func generateUniqueId() -> String {
        return UUID().uuidString.lowercased()
    }
    
    /// 创建睡眠阶段数据
    private func createSleepStages(from sleepData: WindRingDeviceService.SleepData) -> [(type: String, startTime: Date, duration: Int16)] {
        var stages: [(type: String, startTime: Date, duration: Int16)] = []
        var currentTime = sleepData.startTime
        
        // 添加深睡阶段
        if sleepData.deepSleepMinutes > 0 {
            stages.append((type: "deep", startTime: currentTime, duration: Int16(sleepData.deepSleepMinutes)))
            currentTime = Calendar.current.date(byAdding: .minute, value: sleepData.deepSleepMinutes, to: currentTime)!
        }
        
        // 添加浅睡阶段
        if sleepData.lightSleepMinutes > 0 {
            stages.append((type: "light", startTime: currentTime, duration: Int16(sleepData.lightSleepMinutes)))
            currentTime = Calendar.current.date(byAdding: .minute, value: sleepData.lightSleepMinutes, to: currentTime)!
        }
        
        // 添加REM阶段
        if sleepData.remSleepMinutes > 0 {
            stages.append((type: "rem", startTime: currentTime, duration: Int16(sleepData.remSleepMinutes)))
            currentTime = Calendar.current.date(byAdding: .minute, value: sleepData.remSleepMinutes, to: currentTime)!
        }
        
        // 添加清醒阶段
        if sleepData.awakeMinutes > 0 {
            stages.append((type: "awake", startTime: currentTime, duration: Int16(sleepData.awakeMinutes)))
        }
        
        return stages
    }
}
