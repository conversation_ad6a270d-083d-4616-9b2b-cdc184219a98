import Foundation
import Combine

enum TemperatureUnit: String, CaseIterable, Identifiable {
    case celsius = "°C"
    case fahrenheit = "°F"

    var id: String { self.rawValue }

    func convertDeltaFromCelsius(_ celsius: Double) -> Double {
        switch self {
        case .celsius:
            return celsius
        case .fahrenheit:
            // This is a temperature offset, not an absolute temperature.
            // The conversion for an offset is: Δ°F = Δ°C * 9/5
            return celsius * 33.8
        }
    }
    
    var displayName: String {
        switch self {
        case .celsius:
            return "Celsius"
        case .fahrenheit:
            return "Fahrenheit"
        }
    }
}

enum MeasurementUnit: String, CaseIterable, Identifiable {
    case metric = "metric"
    case imperial = "imperial"

    var id: String { self.rawValue }
    var displayName: String {
        switch self {
        case .metric:
            return "metric".localized
        case .imperial:
            return "imperial".localized
        }
    }
}

class UserSettings: ObservableObject {
    static let shared = UserSettings()

    @Published var temperatureUnit: TemperatureUnit {
        didSet {
            UserDefaults.standard.set(temperatureUnit.rawValue, forKey: "temperatureUnit")
        }
    }
    
    @Published var measurementUnit: MeasurementUnit {
        didSet {
            UserDefaults.standard.set(measurementUnit.rawValue, forKey: "measurementUnit")
        }
    }

    @Published var lowBatteryAlert: Bool {
        didSet {
            UserDefaults.standard.set(lowBatteryAlert, forKey: "lowBatteryAlert")
        }
    }
    var glossaryDict: [String: [String: String]] = [:]
    @Published var currentItem: GlossaryItem? = nil
    @Published var isShowing: Bool = false
    @Published var glossaryItems: [GlossaryItem] = []

    private init() {
        let storedTempUnitRawValue = UserDefaults.standard.string(forKey: "temperatureUnit") ?? TemperatureUnit.celsius.rawValue
        self.temperatureUnit = TemperatureUnit(rawValue: storedTempUnitRawValue) ?? .celsius
        let storedMeasUnitRawValue = UserDefaults.standard.string(forKey: "measurementUnit") ?? MeasurementUnit.metric.rawValue
        self.measurementUnit = MeasurementUnit(rawValue: storedMeasUnitRawValue) ?? .metric
        self.lowBatteryAlert = UserDefaults.standard.object(forKey: "lowBatteryAlert") as? Bool ?? true
    }
    
    func show(forKey key: String) {
        guard let raw = glossaryDict[key] else {
            print("❌ Glossary item not found for key: \(key)")
            return
        }

        let item = parseGlossaryItem(from: raw)
        DispatchQueue.main.async {
            self.currentItem = item
            self.isShowing = true
        }
    }

//    func content(for key: String) -> String {
//        let lang = LanguageManager.shared.selectedLanguage
//        
//        return glossaryDict[key]?[lang] ?? glossaryItems[key]["en"] ?? "No content available."
//    }
    func parseGlossaryItem(from item: [String: String]) -> GlossaryItem? {
        guard let data = try? JSONSerialization.data(withJSONObject: item),
              let glossaryItem = try? CleanJSONDecoder().decode(GlossaryItem.self, from: data)
        else {
            return nil
        }
        return glossaryItem
    }
    func loadGlossaryDict(_ dict: [String: [String: String]]) {
        self.glossaryDict = dict
    }
    
    func glossaryItem(forKey key: String) -> GlossaryItem? {
        guard let raw = glossaryDict[key] else {
            print("❌ Glossary item not found for key: \(key)")
            return nil
        }

        let item = parseGlossaryItem(from: raw)
        return item
//        glossaryItems.first { $0.name == key }
    }

   
    func updateGlossaryItems(items: [GlossaryItem]) {
        self.glossaryItems = items
    }
} 

