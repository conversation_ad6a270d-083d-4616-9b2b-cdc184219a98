import Foundation

/// 压力评分服务
/// 负责计算压力评分并提供评估信息
public class StressScoreService {
    // MARK: - 单例
    public static let shared = StressScoreService()
    
    // MARK: - 依赖项
    private let healthDataManager = HealthDataManager.shared
    
    // MARK: - 初始化方法
    private init() {}
    
    // MARK: - 公共方法
    
    /// 计算压力评分
    /// - Parameter averageStressValue: 平均压力值 (0-100)
    /// - Returns: 压力评分 (0-100)
    public func calculateStressScore(from averageStressValue: Double) -> Int {
        // 公式: 100 - 平均压力值
        let score = 100 - Int(averageStressValue.rounded())
        
        // 确保评分在0-100范围内
        return max(0, min(100, score))
    }
    
    /// 获取压力等级
    /// - Parameter stressValue: 压力值 (0-100)
    /// - Returns: 压力等级描述
    public func getStressLevel(stressValue: Double) -> String {
        let value = Int(stressValue.rounded())
        
        switch value {
        case 0..<30:
            return "Relaxed"
        case 30..<60:
            return "Normal"
        case 60..<80:
            return "Moderate"
        case 80...100:
            return "High"
        default:
            return "Unknown"
        }
    }
    
    /// 获取压力颜色代码
    /// - Parameter stressValue: 压力值 (0-100)
    /// - Returns: 颜色代码（十六进制）
    public func getStressColor(stressValue: Double) -> String {
        let value = Int(stressValue.rounded())
        
        switch value {
        case 0..<30:
            return "#00E676" // 绿色
        case 30..<60:
            return "#64DD17" // 浅绿色
        case 60..<80:
            return "#FFC107" // 橙色
        case 80...100:
            return "#F44336" // 红色
        default:
            return "#9E9E9E" // 灰色
        }
    }
    
    /// 获取压力评分建议
    /// - Parameter stressValue: 压力值 (0-100)
    /// - Returns: 改善建议
    public func getStressSuggestion(stressValue: Double) -> String {
        let value = Int(stressValue.rounded())
        
        switch value {
        case 0..<30:
            return "Your stress level is low. Continue maintaining a good relaxed state."
        case 30..<60:
            return "Your stress level is normal. Maintain regular habits and adequate rest."
        case 60..<80:
            return "You're experiencing moderate stress. Consider adding relaxation activities like deep breathing, meditation, or light exercise."
        case 80...100:
            return "Your stress level is high. Try to identify sources of stress and take measures to reduce it. Consult a professional if necessary."
        default:
            return "Unable to assess your stress status. Please ensure the device is worn correctly."
        }
    }
    
    // MARK: - 调试方法
    
    /// 打印压力评分计算过程（用于调试）
    /// - Parameter averageStressValue: 平均压力值
    public func debugCalculation(averageStressValue: Double) {
        print("\n===== 压力评分计算调试 =====")
        print("输入参数:")
        print("- 平均压力值: \(averageStressValue)")
        
        // 计算评分
        let score = calculateStressScore(from: averageStressValue)
        print("\n评分计算:")
        print("   公式: 100 - 平均压力值")
        print("   计算: 100 - \(averageStressValue) = \(score)")
        
        // 压力等级
        let level = getStressLevel(stressValue: averageStressValue)
        print("\n压力等级: \(level)")
        
        // 建议
        let suggestion = getStressSuggestion(stressValue: averageStressValue)
        print("改善建议: \(suggestion)")
        
        print("===== 调试结束 =====\n")
    }
    
    /// 调试已有的压力实体
    /// - Parameter stress: 压力实体
    public func debugEntity(stress: StressEntity) {
        print("\n===== 压力实体调试 =====")
        print("实体信息:")
        print("- ID: \(stress.id ?? "未知")")
        print("- 用户ID: \(stress.userId ?? "未知")")
        print("- 压力值: \(stress.value)")
        print("- 记录时间: \(stress.timestamp?.description ?? "未知")")
        
        // 计算评分
        let calculatedScore = calculateStressScore(from: Double(stress.value))
        print("\n计算出的评分: \(calculatedScore)")
        print("压力等级: \(getStressLevel(stressValue: Double(stress.value)))")
        print("===== 实体调试结束 =====\n")
    }
} 