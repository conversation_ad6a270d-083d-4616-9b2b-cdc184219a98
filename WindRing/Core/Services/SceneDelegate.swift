import UIKit
import SwiftUI

class SceneDelegate: UIResponder, UIWindowSceneDelegate {
    
    var window: UIWindow?
    
    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        // 场景将要连接时
        print("场景将要连接...")
        
        // 配置 UISegmentedControl 外观，移除分割线
        let segmentedAppearance = UISegmentedControl.appearance()
        segmentedAppearance.setDividerImage(UIImage(), forLeftSegmentState: .normal, rightSegmentState: .normal, barMetrics: .default)
        
        // 初始化场景
        if let windowScene = scene as? UIWindowScene {
            let window = UIWindow(windowScene: windowScene)
            window.rootViewController = UIHostingController(rootView: MainTabView()
                                                            .environmentObject(MQTTService.shared)
                                                            .environmentObject(MQTTSyncService.shared)
                                                            .environmentObject(WindRingDeviceService.shared))
            self.window = window
            window.makeKeyAndVisible()
        }
    }
    
    func sceneDidBecomeActive(_ scene: UIScene) {
        // 场景变为活跃状态
        print("场景变为活跃状态，开始自动同步数据...")
        
        // 检查设备连接状态并尝试自动同步数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            // 尝试自动同步数据
            if WindRingDeviceService.shared.connectionState.isConnected {
                print("检测到已连接的设备，开始自动同步数据...")
                MQTTSyncService.shared.syncSleepData { success, count in
                    if success {
                        print("✅ 应用启动时自动同步成功：获取了 \(count) 条睡眠记录")
                        
                        // 发送通知刷新UI
                        NotificationCenter.default.post(name: Notification.Name("RefreshHealthData"), object: nil)
                    } else {
                        print("❌ 应用启动时自动同步失败或没有新数据")
                    }
                }
                
                // 启动自动上传功能 - 无需手动开启开关
                print("🔄 应用启动时自动启动数据上传功能...")
                // 将自动上传状态保存到 UserDefaults 中，确保即使应用重启也能保持开启状态
                UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
                
                // 启动数据上传服务
//                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
//                    let uploadService = RawDataUploadService.shared
//                    if !uploadService.isAutoUploadEnabled {
//                        let success = uploadService.startAutoUpload(interval: 300) // 5分钟自动上传一次
//                        print("🔄 自动上传服务启动" + (success ? "成功" : "失败"))
//                    } else {
//                        print("🔄 自动上传服务已经处于启动状态")
//                    }
//                }
                // 即使设备未连接，也将自动上传设置为开启状态
                // 这样当设备连接后，会自动启动上传功能
                UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
                print("🔄 已设置自动上传标志，将在设备连接后自动启动")
            } else {
                print("⚠️ 未检测到已连接的设备，跳过启动时的自动同步")
                
                // 即使设备未连接，也将自动上传设置为开启状态
                // 这样当设备连接后，会自动启动上传功能
                UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
                print("🔄 已设置自动上传标志，将在设备连接后自动启动")
            }
        }
    }
    
    func sceneWillResignActive(_ scene: UIScene) {
        // 场景将要进入非活跃状态
        print("场景将要进入非活跃状态...")
    }
    
    func sceneWillEnterForeground(_ scene: UIScene) {
        // 场景将要进入前台
        print("场景将要进入前台...")
    }
    
    func sceneDidEnterBackground(_ scene: UIScene) {
        // 场景已进入后台
        print("场景已进入后台...")
        
        // 保存数据
//        (UIApplication.shared.delegate as? AppDelegate)?.saveContext()
    }
} 
