import Foundation
import Combine

// 使用HealthAnalysisModels.swift中定义的模型
// (Models are defined in WindRing/Core/Services/Models/HealthAnalysisModels.swift)

public typealias HealthOverview = HAHealthOverview
public typealias HealthGoal = HAHealthGoal
public typealias GoalType = HAGoalType
public typealias GoalPeriod = HAGoalPeriod
public typealias GoalStatus = HAGoalStatus
public typealias Recommendation = HARecommendation
public typealias RecommendationType = HARecommendationType

/// 健康数据分析服务
/// 负责分析用户的健康数据并提供见解
public class HealthAnalysisService {
    // MARK: - 单例
    public static let shared = HealthAnalysisService()
    
    // MARK: - 属性
    private let healthDataManager: HealthDataManager
    
    /// 分析更新发布者
    public let analysisUpdatePublisher = PassthroughSubject<Void, Never>()
    
    // MARK: - 初始化方法
    private init() {
        self.healthDataManager = HealthDataManager.shared
    }
    
    // MARK: - 公共方法
    
    /// 获取用户健康概览
    /// - Parameter userId: 用户ID
    /// - Returns: 健康概览数据
    public func getHealthOverview(userId: String) -> HealthOverview? {
//        guard let user = healthDataManager.getUser(id: userId) else {
//            return nil
//        }
        
        // 获取最近的各类健康数据
        let latestHeartRate = healthDataManager.getLatestHeartRate(userId: userId)
        let dailySteps = healthDataManager.getDailySteps(userId: userId)
        let latestSleep = healthDataManager.getLatestSleep(userId: userId)
        
        // 构建健康概览
        let overview = HealthOverview(
            userId: "1",//userId,
            userName: "1",//user.name ?? "用户",
            date: Date(),
            heartRate: latestHeartRate != nil ? HealthOverview.HeartRateInfo(
                current: Int(latestHeartRate!.value),
                timestamp: latestHeartRate!.timestamp!
            ) : nil,
            steps: dailySteps != nil ? HealthOverview.StepsInfo(
                count: Int(dailySteps!.value),
                caloriesBurned: dailySteps!.calories != nil ? Int(dailySteps!.calories) : nil,
                distanceKm: dailySteps!.distance
            ) : nil,
            sleep: latestSleep != nil ? HealthOverview.SleepInfo(
                durationHours: Double(latestSleep!.totalMinutes) / 60.0,
                quality: latestSleep!.score != nil ? Int(latestSleep!.score) : nil,
                startTime: latestSleep!.startTime!,
                endTime: latestSleep!.endTime!
            ) : nil
        )
        
        return overview
    }
    
    /// 计算用户健康评分
    /// - Parameter userId: 用户ID
    /// - Returns: 健康评分 (0-100)
    public func calculateHealthScore(userId: String) -> Int {
//        guard let user = healthDataManager.getUser(id: userId) else {
//            return 0
//        }
        
        // 获取过去7天的数据
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -7, to: endDate)!
        
        // 获取各类健康数据统计
        var scoreComponents: [Int] = []
        var totalWeight: Int = 0
        
        // 心率评分 (权重: 25)
        if let heartRateStats = healthDataManager.getHeartRateStats(userId: userId, startDate: startDate, endDate: endDate) {
            let heartRateScore = calculateHeartRateScore(min: heartRateStats.min, max: heartRateStats.max, avg: heartRateStats.avg)
            scoreComponents.append(heartRateScore * 25)
            totalWeight += 25
        }
        
        // 步数评分 (权重: 30)
        if let stepsStats = healthDataManager.getStepsStats(userId: userId, startDate: startDate, endDate: endDate) {
            let stepsScore = calculateStepsScore(average: stepsStats.average)
            scoreComponents.append(stepsScore * 30)
            totalWeight += 30
        }
        
        // 睡眠评分 (权重: 35)
        if let sleepStats = healthDataManager.getSleepStats(userId: userId, startDate: startDate, endDate: endDate) {
            let sleepScore = calculateSleepScore(avgTotal: sleepStats.avgTotal, avgDeep: sleepStats.avgDeep, avgScore: sleepStats.avgScore)
            scoreComponents.append(sleepScore * 35)
            totalWeight += 35
        }
        
        // 如果没有任何数据，返回默认分数
        if totalWeight == 0 {
            return 50
        }
        
        // 计算加权平均分
        let weightedSum = scoreComponents.reduce(0, +)
        let finalScore = weightedSum / totalWeight
        
        return min(max(finalScore, 0), 100)
    }
    
    /// 分析心率趋势
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - period: 分析周期（天）
    /// - Returns: 心率趋势分析
    public func analyzeHeartRateTrend(userId: String, period: Int = 30) -> HealthTrend? {
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -period, to: endDate)!
        
        // 获取心率数据
        let heartRates = healthDataManager.getHeartRates(userId: userId, startDate: startDate, endDate: endDate)
        
        guard !heartRates.isEmpty else {
            return nil
        }
        
        // 按周划分数据
        var weeklyData: [[HeartRateEntity]] = []
        var currentWeekData: [HeartRateEntity] = []
        var currentWeekStart = startDate
        
        for heartRate in heartRates {
            let heartRateDate = heartRate.timestamp!
            if heartRateDate >= currentWeekStart && heartRateDate < Calendar.current.date(byAdding: .day, value: 7, to: currentWeekStart)! {
                currentWeekData.append(heartRate)
            } else {
                if !currentWeekData.isEmpty {
                    weeklyData.append(currentWeekData)
                }
                
                // 移动到下一周
                currentWeekStart = Calendar.current.date(byAdding: .day, value: 7, to: currentWeekStart)!
                currentWeekData = [heartRate]
            }
        }
        
        // 添加最后一周的数据
        if !currentWeekData.isEmpty {
            weeklyData.append(currentWeekData)
        }
        
        // 计算每周的平均值
        let weeklyAverages = weeklyData.map { weekData -> Double in
            let sum = weekData.reduce(0) { $0 + Double($1.value) }
            return sum / Double(weekData.count)
        }
        
        // 计算趋势
        let trend: TrendDirection
        if weeklyAverages.count >= 2 {
            let firstHalf = Array(weeklyAverages.prefix(weeklyAverages.count / 2))
            let secondHalf = Array(weeklyAverages.suffix(weeklyAverages.count - weeklyAverages.count / 2))
            
            let firstHalfAvg = firstHalf.reduce(0.0, +) / Double(firstHalf.count)
            let secondHalfAvg = secondHalf.reduce(0.0, +) / Double(secondHalf.count)
            
            // 计算变化百分比
            let changePercent = (secondHalfAvg - firstHalfAvg) / firstHalfAvg * 100.0
            
            if changePercent > 5.0 {
                trend = .increasing
            } else if changePercent < -5.0 {
                trend = .decreasing
            } else {
                trend = .stable
            }
        } else {
            trend = .stable
        }
        
        // 计算最大、最小和平均值
        let avgValue = weeklyAverages.reduce(0.0, +) / Double(weeklyAverages.count)
        let minValue = weeklyAverages.min() ?? avgValue
        let maxValue = weeklyAverages.max() ?? avgValue
        
        // 构建输出
        return HealthTrend(
            dataType: "心率",
            period: period,
            startDate: startDate,
            endDate: endDate,
            trend: trend,
            avgValue: avgValue,
            minValue: minValue,
            maxValue: maxValue,
            weeklyValues: weeklyAverages,
            unit: "次/分钟"
        )
    }
    
    /// 分析步数趋势
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - period: 分析周期（天）
    /// - Returns: 步数趋势分析
    public func analyzeStepsTrend(userId: String, period: Int = 30) -> HealthTrend? {
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -period, to: endDate)!
        
        // 获取步数数据
        let stepsData = healthDataManager.getSteps(userId: userId, startDate: startDate, endDate: endDate)
        
        guard !stepsData.isEmpty else {
            return nil
        }
        
        // 按周划分数据
        var weeklyData: [[StepsEntity]] = []
        var currentWeekData: [StepsEntity] = []
        var currentWeekStart = startDate
        
        for steps in stepsData {
            let stepsDate = steps.date!
            if stepsDate >= currentWeekStart && stepsDate < Calendar.current.date(byAdding: .day, value: 7, to: currentWeekStart)! {
                currentWeekData.append(steps)
            } else {
                if !currentWeekData.isEmpty {
                    weeklyData.append(currentWeekData)
                }
                
                // 移动到下一周
                currentWeekStart = Calendar.current.date(byAdding: .day, value: 7, to: currentWeekStart)!
                currentWeekData = [steps]
            }
        }
        
        // 添加最后一周的数据
        if !currentWeekData.isEmpty {
            weeklyData.append(currentWeekData)
        }
        
        // 计算每周的平均值
        let weeklyAverages = weeklyData.map { weekData -> Double in
            let sum = weekData.reduce(0) { $0 + Double($1.value) }
            return sum / Double(weekData.count)
        }
        
        // 计算趋势
        let trend: TrendDirection
        if weeklyAverages.count >= 2 {
            let firstHalf = Array(weeklyAverages.prefix(weeklyAverages.count / 2))
            let secondHalf = Array(weeklyAverages.suffix(weeklyAverages.count - weeklyAverages.count / 2))
            
            let firstHalfAvg = firstHalf.reduce(0.0, +) / Double(firstHalf.count)
            let secondHalfAvg = secondHalf.reduce(0.0, +) / Double(secondHalf.count)
            
            // 计算变化百分比
            let changePercent = (secondHalfAvg - firstHalfAvg) / firstHalfAvg * 100.0
            
            if changePercent > 10.0 {
                trend = .increasing
            } else if changePercent < -10.0 {
                trend = .decreasing
            } else {
                trend = .stable
            }
        } else {
            trend = .stable
        }
        
        // 计算最大、最小和平均值
        let avgValue = weeklyAverages.reduce(0.0, +) / Double(weeklyAverages.count)
        let minValue = weeklyAverages.min() ?? avgValue
        let maxValue = weeklyAverages.max() ?? avgValue
        
        // 构建输出
        return HealthTrend(
            dataType: "步数",
            period: period,
            startDate: startDate,
            endDate: endDate,
            trend: trend,
            avgValue: avgValue,
            minValue: minValue,
            maxValue: maxValue,
            weeklyValues: weeklyAverages,
            unit: "步"
        )
    }
    
    /// 分析睡眠趋势
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - period: 分析周期（天）
    /// - Returns: 睡眠趋势分析
    public func analyzeSleepTrend(userId: String, period: Int = 30) -> HealthTrend? {
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -period, to: endDate)!
        
        // 获取睡眠数据
        let sleepData = healthDataManager.getSleep(userId: userId, startDate: startDate, endDate: endDate)
        
        guard !sleepData.isEmpty else {
            return nil
        }
        
        // 按周划分数据
        var weeklyData: [[SleepEntity]] = []
        var currentWeekData: [SleepEntity] = []
        var currentWeekStart = startDate
        
        for sleep in sleepData {
            let sleepDate = sleep.startTime!
            if sleepDate >= currentWeekStart && sleepDate < Calendar.current.date(byAdding: .day, value: 7, to: currentWeekStart)! {
                currentWeekData.append(sleep)
            } else {
                if !currentWeekData.isEmpty {
                    weeklyData.append(currentWeekData)
                }
                
                // 移动到下一周
                currentWeekStart = Calendar.current.date(byAdding: .day, value: 7, to: currentWeekStart)!
                currentWeekData = [sleep]
            }
        }
        
        // 添加最后一周的数据
        if !currentWeekData.isEmpty {
            weeklyData.append(currentWeekData)
        }
        
        // 计算每周的平均值（分钟）
        let weeklyAverages = weeklyData.map { weekData -> Double in
            let sum = weekData.reduce(0) { $0 + Double($1.totalMinutes) }
            return sum / Double(weekData.count)
        }
        
        // 计算趋势
        let trend: TrendDirection
        if weeklyAverages.count >= 2 {
            let firstHalf = Array(weeklyAverages.prefix(weeklyAverages.count / 2))
            let secondHalf = Array(weeklyAverages.suffix(weeklyAverages.count - weeklyAverages.count / 2))
            
            let firstHalfAvg = firstHalf.reduce(0.0, +) / Double(firstHalf.count)
            let secondHalfAvg = secondHalf.reduce(0.0, +) / Double(secondHalf.count)
            
            // 计算变化百分比
            let changePercent = (secondHalfAvg - firstHalfAvg) / firstHalfAvg * 100.0
            
            if changePercent > 10.0 {
                trend = .increasing
            } else if changePercent < -10.0 {
                trend = .decreasing
            } else {
                trend = .stable
            }
        } else {
            trend = .stable
        }
        
        // 计算最大、最小和平均值
        let avgValue = weeklyAverages.reduce(0.0, +) / Double(weeklyAverages.count)
        let minValue = weeklyAverages.min() ?? avgValue
        let maxValue = weeklyAverages.max() ?? avgValue
        
        // 构建输出
        return HealthTrend(
            dataType: "睡眠",
            period: period,
            startDate: startDate,
            endDate: endDate,
            trend: trend,
            avgValue: avgValue / 60.0, // 转换为小时
            minValue: minValue / 60.0, // 转换为小时
            maxValue: maxValue / 60.0, // 转换为小时
            weeklyValues: weeklyAverages.map { $0 / 60.0 }, // 转换为小时
            unit: "小时"
        )
    }
    
    // MARK: - 私有方法
    
    /// 计算心率评分
    /// - Parameters:
    ///   - min: 最小心率
    ///   - max: 最大心率
    ///   - avg: 平均心率
    /// - Returns: 评分 (0-100)
    private func calculateHeartRateScore(min: Int16, max: Int16, avg: Double) -> Int {
        // 理想的静息心率范围是60-80bpm
        // 过低(<50)或过高(>100)的静息心率会降低得分
        
        // 计算静息心率得分
        let restingScore: Double
        if avg >= 60 && avg <= 80 {
            restingScore = 100.0  // 理想范围
        } else if avg > 80 && avg <= 100 {
            restingScore = 80.0 - (avg - 80.0) * 1.0  // 略高，适度扣分
        } else if avg > 100 {
            restingScore = 60.0 - Swift.min((avg - 100.0) * 1.5, 60.0)  // 过高，明显扣分
        } else if avg >= 50 && avg < 60 {
            restingScore = 80.0 - (60.0 - avg) * 2.0  // 略低，适度扣分
        } else {
            restingScore = 60.0 - Swift.min((50.0 - avg) * 3.0, 60.0)  // 过低，明显扣分
        }
        
        // 心率变异性评分 (最大和最小心率之间的差异)
        // 健康的心脏应该有一定的变异性，但不应过大
        let variabilityRange = Double(max - min)
        let variabilityScore: Double
        if variabilityRange >= 20 && variabilityRange <= 40 {
            variabilityScore = 100.0  // 理想范围
        } else if variabilityRange > 40 && variabilityRange <= 60 {
            variabilityScore = 80.0 - (variabilityRange - 40.0) * 1.0  // 略高
        } else if variabilityRange > 60 {
            variabilityScore = 60.0 - Swift.min((variabilityRange - 60.0) * 1.0, 50.0)  // 过高
        } else if variabilityRange >= 10 && variabilityRange < 20 {
            variabilityScore = 80.0 - (20.0 - variabilityRange) * 2.0  // 略低
        } else {
            variabilityScore = 60.0 - Swift.min((10.0 - variabilityRange) * 6.0, 50.0)  // 过低
        }
        
        // 综合评分 (静息心率占70%，变异性占30%)
        let finalScore = restingScore * 0.7 + variabilityScore * 0.3
        
        return Int(finalScore)
    }
    
    /// 计算步数评分
    /// - Parameter average: 平均每日步数
    /// - Returns: 评分 (0-100)
    private func calculateStepsScore(average: Double) -> Int {
        // 世界卫生组织和许多健康组织推荐每天至少10,000步
        // 我们根据中国用户的实际情况调整评分标准
        
        if average >= 10000 {
            return 100
        } else if average >= 8000 {
            return 90 + Int((average - 8000) / 2000 * 10)
        } else if average >= 6000 {
            return 80 + Int((average - 6000) / 2000 * 10)
        } else if average >= 4000 {
            return 60 + Int((average - 4000) / 2000 * 20)
        } else if average >= 2000 {
            return 40 + Int((average - 2000) / 2000 * 20)
        } else if average >= 1000 {
            return 20 + Int((average - 1000) / 1000 * 20)
        } else {
            return max(0, Int(average / 1000 * 20))
        }
    }
    
    /// 计算睡眠评分
    /// - Parameters:
    ///   - avgTotal: 平均总睡眠时间（分钟）
    ///   - avgDeep: 平均深睡眠时间（分钟）
    ///   - avgScore: 平均睡眠评分
    /// - Returns: 评分 (0-100)
    private func calculateSleepScore(avgTotal: Double, avgDeep: Double, avgScore: Double) -> Int {
        // 如果已有睡眠评分，直接使用
        if avgScore > 0 {
            return Int(avgScore)
        }
        
        // 否则，根据总睡眠时间和深睡眠时间计算
        // 理想睡眠时间：7-9小时（420-540分钟）
        // 理想深睡眠比例：20-25%
        
        // 睡眠时长评分
        let durationScore: Double
        if avgTotal >= 420 && avgTotal <= 540 {
            durationScore = 100.0  // 理想范围
        } else if avgTotal > 540 && avgTotal <= 600 {
            durationScore = 90.0 - (avgTotal - 540.0) / 60.0 * 10.0  // 略长
        } else if avgTotal > 600 {
            durationScore = 80.0 - Swift.min((avgTotal - 600.0) / 60.0 * 10.0, 30.0)  // 过长
        } else if avgTotal >= 360 && avgTotal < 420 {
            durationScore = 90.0 - (420.0 - avgTotal) / 60.0 * 10.0  // 略短
        } else if avgTotal >= 300 && avgTotal < 360 {
            durationScore = 80.0 - (360.0 - avgTotal) / 60.0 * 15.0  // 短
        } else {
            durationScore = 65.0 - Swift.min((300.0 - avgTotal) / 60.0 * 15.0, 65.0)  // 过短
        }
        
        // 深睡眠比例评分
        let deepSleepRatio = avgTotal > 0 ? avgDeep / avgTotal : 0
        let deepSleepScore: Double
        if deepSleepRatio >= 0.2 && deepSleepRatio <= 0.25 {
            deepSleepScore = 100.0  // 理想范围
        } else if deepSleepRatio > 0.25 && deepSleepRatio <= 0.3 {
            deepSleepScore = 90.0 - (deepSleepRatio - 0.25) * 200.0  // 略高
        } else if deepSleepRatio > 0.3 {
            deepSleepScore = 80.0 - Swift.min((deepSleepRatio - 0.3) * 250.0, 30.0)  // 过高
        } else if deepSleepRatio >= 0.15 && deepSleepRatio < 0.2 {
            deepSleepScore = 90.0 - (0.2 - deepSleepRatio) * 200.0  // 略低
        } else if deepSleepRatio >= 0.1 && deepSleepRatio < 0.15 {
            deepSleepScore = 80.0 - (0.15 - deepSleepRatio) * 300.0  // 低
        } else {
            deepSleepScore = 65.0 - Swift.min((0.1 - deepSleepRatio) * 500.0, 65.0)  // 过低
        }
        
        // 综合评分 (时长占70%，深睡眠比例占30%)
        let finalScore = durationScore * 0.7 + deepSleepScore * 0.3
        
        return Int(finalScore)
    }
}

// MARK: - 健康趋势模型

/// 趋势方向
public enum TrendDirection {
    case increasing
    case stable
    case decreasing
    
    public var description: String {
        switch self {
        case .increasing:
            return "上升"
        case .stable:
            return "稳定"
        case .decreasing:
            return "下降"
        }
    }
}

/// 健康趋势
public struct HealthTrend {
    public let dataType: String
    public let period: Int
    public let startDate: Date
    public let endDate: Date
    public let trend: TrendDirection
    public let avgValue: Double
    public let minValue: Double
    public let maxValue: Double
    public let weeklyValues: [Double]
    public let unit: String
    
    public var description: String {
        return "过去\(period)天，您的\(dataType)整体呈\(trend.description)趋势。平均值为\(String(format: "%.1f", avgValue))\(unit)，范围在\(String(format: "%.1f", minValue))-\(String(format: "%.1f", maxValue))\(unit)之间。"
    }
} 
