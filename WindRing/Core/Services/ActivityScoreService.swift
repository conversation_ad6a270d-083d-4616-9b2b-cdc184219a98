import Foundation

/// 活动评分服务
/// 负责计算活动评分并提供评估信息
public class ActivityScoreService {
    // MARK: - 单例
    public static let shared = ActivityScoreService()
    
    // MARK: - 依赖项
    private let healthDataManager = HealthDataManager.shared
    
    // MARK: - 初始化方法
    private init() {}
    
    // MARK: - 公共方法
    
    /// 计算活动评分
    /// - Parameters:
    ///   - steps: 当日步数
    ///   - moderateIntensityMinutes: 中等强度活动分钟数
    ///   - highIntensityMinutes: 高强度活动分钟数
    /// - Returns: 活动评分 (0-100)
    public func calculateActivityScore(steps: Int, moderateIntensityMinutes: Int = 0, highIntensityMinutes: Int = 0) -> Int {
        // 计算步数得分 (最高100分)
        let stepsScore = calculateStepsScore(steps: steps)
        
        // 计算中等强度活动得分 (最高100分)
        let moderateScore = calculateModerateIntensityScore(minutes: moderateIntensityMinutes)
        
        // 计算高强度活动得分 (最高100分)
        let highScore = calculateHighIntensityScore(minutes: highIntensityMinutes)
        
        // 计算综合得分 (权重可调整)
        let totalScore = (stepsScore * 0.5) + (moderateScore * 0.3) + (highScore * 0.2)
        
        // 确保评分在0-100范围内
        return max(0, min(100, Int(totalScore.rounded())))
    }
    
    /// 计算步数得分
    /// - Parameter steps: 当日步数
    /// - Returns: 步数得分 (0-100)
    private func calculateStepsScore(steps: Int) -> Double {
        // 根据表格：(今日步数/8000)*最高100
        let score = Double(steps) / 8000.0 * 100.0
        return min(100.0, score)
    }
    
    /// 计算中等强度活动得分
    /// - Parameter minutes: 中等强度活动分钟数
    /// - Returns: 中等强度活动得分 (0-100)
    private func calculateModerateIntensityScore(minutes: Int) -> Double {
        // 根据表格：(当日中等强度时长/30分钟)*100
        let score = Double(minutes) / 30.0 * 100.0
        return min(100.0, score)
    }
    
    /// 计算高强度活动得分
    /// - Parameter minutes: 高强度活动分钟数
    /// - Returns: 高强度活动得分 (0-100)
    private func calculateHighIntensityScore(minutes: Int) -> Double {
        // 根据表格：(当日高等强度时长/15分钟)*100
        let score = Double(minutes) / 15.0 * 100.0
        return min(100.0, score)
    }
    
    /// 获取活动等级
    /// - Parameter score: 活动评分 (0-100)
    /// - Returns: 活动等级描述
    public func getActivityLevel(score: Int) -> String {
        switch score {
        case 0..<30:
            return "Low"
        case 30..<50:
            return "Moderate"
        case 50..<80:
            return "Active"
        case 80...100:
            return "Very Active"
        default:
            return "Unknown"
        }
    }
    
    /// 获取活动颜色代码
    /// - Parameter score: 活动评分 (0-100)
    /// - Returns: 颜色代码（十六进制）
    public func getActivityColor(score: Int) -> String {
        switch score {
        case 0..<30:
            return "#FF5252" // 红色
        case 30..<50:
            return "#FFC107" // 橙色
        case 50..<80:
            return "#64DD17" // 浅绿色
        case 80...100:
            return "#00E676" // 绿色
        default:
            return "#9E9E9E" // 灰色
        }
    }
    
    /// 获取活动评分建议
    /// - Parameter score: 活动评分 (0-100)
    /// - Returns: 改善建议
    public func getActivitySuggestion(score: Int) -> String {
        switch score {
        case 0..<30:
            return "Your activity level is low. Try to incorporate more movement into your daily routine."
        case 30..<50:
            return "You have a moderate activity level. Consider increasing your daily steps and add some moderate intensity activities."
        case 50..<80:
            return "Good job! You're active. Continue with your current routine and try to add more high-intensity activities for better results."
        case 80...100:
            return "Excellent! You're very active. Maintain your current activity level for optimal health benefits."
        default:
            return "Unable to assess your activity status. Please ensure your device is tracking correctly."
        }
    }
    
    // MARK: - 调试方法
    
    /// 打印活动评分计算过程（用于调试）
    /// - Parameters:
    ///   - steps: 当日步数
    ///   - moderateIntensityMinutes: 中等强度活动分钟数
    ///   - highIntensityMinutes: 高强度活动分钟数
    public func debugCalculation(steps: Int, moderateIntensityMinutes: Int = 0, highIntensityMinutes: Int = 0) {
        print("\n===== 活动评分计算调试 =====")
        print("输入参数:")
        print("- 步数: \(steps)")
        print("- 中等强度活动: \(moderateIntensityMinutes) 分钟")
        print("- 高强度活动: \(highIntensityMinutes) 分钟")
        
        // 计算各项得分
        let stepsScore = calculateStepsScore(steps: steps)
        let moderateScore = calculateModerateIntensityScore(minutes: moderateIntensityMinutes)
        let highScore = calculateHighIntensityScore(minutes: highIntensityMinutes)
        
        print("\n各项得分:")
        print("- 步数得分 (权重50%): \(stepsScore.rounded()) / 100")
        print("- 中等强度活动得分 (权重30%): \(moderateScore.rounded()) / 100")
        print("- 高强度活动得分 (权重20%): \(highScore.rounded()) / 100")
        
        // 计算综合得分
        let totalScore = (stepsScore * 0.5) + (moderateScore * 0.3) + (highScore * 0.2)
        let finalScore = max(0, min(100, Int(totalScore.rounded())))
        
        print("\n综合评分计算:")
        print("   公式: (步数得分 * 0.5) + (中等强度活动得分 * 0.3) + (高强度活动得分 * 0.2)")
        print("   计算: (\(stepsScore.rounded()) * 0.5) + (\(moderateScore.rounded()) * 0.3) + (\(highScore.rounded()) * 0.2) = \(finalScore)")
        
        // 活动等级
        let level = getActivityLevel(score: finalScore)
        print("\n活动等级: \(level)")
        
        // 建议
        let suggestion = getActivitySuggestion(score: finalScore)
        print("改善建议: \(suggestion)")
        
        print("===== 调试结束 =====\n")
    }
} 