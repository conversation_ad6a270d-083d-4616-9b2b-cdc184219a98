import Foundation
import Combine

/// 活动数据上传服务
public class ActivityUploadService {
    // MARK: - 单例
    public static let shared = ActivityUploadService()
    
    // MARK: - 属性
    private let activityService: ActivityService
    
    // MARK: - 初始化
    private init() {
        self.activityService = ActivityService.shared
    }
    
    // MARK: - 公共方法
    
    /// 上传当前活动数据
    /// - Parameter completion: a completion handler that returns a boolean indicating success and an optional error
    public func uploadCurrentActivityData(completion: @escaping (Bool, Error?) -> Void) {
        print("开始上传当前活动数据")
        
        // 模拟上传成功
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            print("活动数据上传成功")
            completion(true, nil)
        }
    }
    
    /// 获取基本活动数据
    /// - Parameter completion: 完成回调，返回步数模型和可能的错误
    public func getBasicActivityData(completion: @escaping (StepModel?, Error?) -> Void) {
        print("获取基本活动数据")
        
        // 创建一个模拟的步数模型
        let stepModel = StepModel(steps: 8000, distance: 5600, calory: 320, time: 3600)
        
        // 模拟异步操作
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            print("获取基本活动数据成功")
            completion(stepModel, nil)
        }
    }
    
    /// 同步历史活动数据
    /// - Parameters:
    ///   - days: 要同步的天数
    ///   - completion: 完成回调，返回同步的天数和可能的错误
    public func syncHistoricalActivityData(days: Int, completion: @escaping (Int, Error?) -> Void) {
        print("开始同步过去\(days)天的历史活动数据")
        
        // 模拟同步操作
        let syncedDays = Int.random(in: 1...days)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            print("成功同步了\(syncedDays)天的历史活动数据")
            completion(syncedDays, nil)
        }
    }
}

/// 步数模型
public struct StepModel:Codable,UploadedMarkable {
    var uploaded: Bool = false
    /// 步数
    public let steps: Int
    /// 距离（米）
    public let distance: Int
    /// 卡路里
    public let calory: Int
    /// 活动时间（秒）
    public let time: Int
    
    public init(steps: Int, distance: Int, calory: Int, time: Int = 0) {
        self.steps = steps
        self.distance = distance
        self.calory = calory
        self.time = time
    }
}
