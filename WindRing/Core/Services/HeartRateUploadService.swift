//
//  HeartRateUploadService.swift
//  WindRing
//
//  Created by 1234 on 2025/3/25.
//

import Foundation
import Combine
import CoreData
import Network
#if os(iOS)
import CRPSmartRing
#endif

/// 心率数据上传服务
class HeartRateUploadService {
    // 单例模式
    static let shared = HeartRateUploadService()
    
    // 依赖服务
    private let deviceService = WindRingDeviceService.shared
    private let healthDataManager = HealthDataManager.shared
    private let authService = AuthService.shared
    private let userService = WindRingUserService.shared
    
    // 上传API路径
    private let apiPath = "/app-api/iot/hr/upload/data"
    
    // 上传状态跟踪
    private var isUploading: Bool = false
    private var lastUploadAttempt: Date?
    private var cancellables = Set<AnyCancellable>()
    
    // 私有初始化方法，配置通知订阅
    private init() {
        setupNotifications()
    }
    
    // MARK: - 公共方法
    
    /// 从设备获取心率历史数据并保存到本地
    /// - Parameter completion: 完成回调，返回获取的数据数量和可能的错误
    func fetchAndSaveHeartRateHistory(completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        print("从设备获取心率历史数据...")
        
        // 调用SDK获取历史心率数据 - 使用正确的API方法名
//        CRPSmartRingSDK.sharedInstance.getHeartRecordData { [weak self] records, error in
//            guard let self = self else { return }
//            
//            if error == .none && !records.isEmpty {
//                print("成功获取\(records.count)条心率历史数据")
//                
//                // 将数据保存到本地数据库
//                self.saveHeartRateData(records: records) { savedCount in
//                    print("已保存\(savedCount)条新心率数据到本地")
//                    DispatchQueue.main.async {
//                        completion(savedCount, nil)
//                        
//                        // 自动尝试上传
//                        if savedCount > 0 {
//                            self.uploadPendingHeartRateData()
//                        }
//                    }
//                }
//            } else {
//                let errorMessage = error == .none ? "未获取到数据" : "错误: \(error)"
//                print("获取心率历史数据失败: \(errorMessage)")
//                DispatchQueue.main.async {
//                    completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: errorMessage]))
//                }
//            }
//        }
    }
    
    /// 从设备获取定时心率数据并保存到本地
    /// - Parameters:
    ///   - day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
    ///   - completion: 完成回调，返回获取的数据数量和可能的错误
    func fetchAndSaveTimingHeartRate(day: Int = 0, completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        print("从设备获取第\(day)天的定时心率数据...")
        
        // 调用SDK获取定时心率数据
//        CRPSmartRingSDK.sharedInstance.getTimingHeartRate(day) { [weak self] model, error in
//            guard let self = self else { return }
//            
//            if error == .none {
//                // 获取日期
//                let calendar = Calendar.current
//                let today = Date()
//                let targetDate = calendar.date(byAdding: .day, value: -day, to: today) ?? today
//                
//                // 计算当天0点的时间
//                let startOfDay = calendar.startOfDay(for: targetDate)
//                
//                print("成功获取第\(day)天的定时心率数据，共\(model.hearts.count)条记录")
//                
//                // 过滤有效的心率值（大于0且小于255的值）
//                let validHeartRates = model.hearts.enumerated().filter { $0.element > 0 && $0.element < 255 }
//                
//                if validHeartRates.isEmpty {
//                    print("没有有效的定时心率数据")
//                    DispatchQueue.main.async {
//                        completion(0, nil)
//                    }
//                    return
//                }
//                
//                print("有效定时心率数据: \(validHeartRates.count)条")
//                
//                // 转换为批量添加格式
//                guard let userId = authService.currentUser?.id else {
//                    print("用户未登录，无法保存心率数据")
//                    DispatchQueue.main.async {
//                        completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
//                    }
//                    return
//                }
//                
//                let deviceId = deviceService.deviceInfo?.mac ?? "unknown"
//                var heartRateData: [(value: Int16, timestamp: Date, deviceId: String?, confidence: Int16?)] = []
//                
//                // 定时心率每5分钟一条，总共288条
//                for (index, value) in validHeartRates {
//                    // 计算时间戳：当天0点 + index * 5分钟
//                    let timestamp = calendar.date(byAdding: .minute, value: index * 5, to: startOfDay) ?? startOfDay
//                    
//                    heartRateData.append((
//                        value: Int16(value),
//                        timestamp: timestamp,
//                        deviceId: deviceId,
//                        confidence: 100 // 假设设备数据可信度为100%
//                    ))
//                }
//                
//                // 批量添加数据
//                let success = healthDataManager.addHeartRates(userId: userId, data: heartRateData)
//                
//                if success {
//                    print("已保存\(heartRateData.count)条定时心率数据到本地")
//                    DispatchQueue.main.async {
//                        completion(heartRateData.count, nil)
//                        
//                        // 自动尝试上传
//                        if heartRateData.count > 0 {
//                            self.uploadPendingHeartRateData()
//                        }
//                    }
//                } else {
//                    print("保存定时心率数据失败")
//                    DispatchQueue.main.async {
//                        completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "保存数据失败"]))
//                    }
//                }
//            } else {
//                let errorMessage = "获取定时心率数据失败: \(error)"
//                print(errorMessage)
//                DispatchQueue.main.async {
//                    completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: errorMessage]))
//                }
//            }
//        }
    }
    
    /// 获取并上传最近几天的定时心率数据
    /// - Parameters:
    ///   - days: 要获取的天数，从0（今天）开始
    ///   - completion: 完成回调，返回获取的总数据数量和可能的错误
    func syncTimingHeartRateData(days: Int = 3, completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        print("开始同步最近\(days)天的定时心率数据...")
        
        let group = DispatchGroup()
        var totalCount = 0
        var lastError: Error? = nil
        
        // 获取最近几天的数据
        for day in 0..<days {
            group.enter()
            
            fetchAndSaveTimingHeartRate(day: day) { count, error in
                if let error = error {
                    print("获取第\(day)天定时心率数据失败: \(error.localizedDescription)")
                    lastError = error
                } else {
                    print("成功获取第\(day)天\(count)条定时心率数据")
                    totalCount += count
                }
                
                group.leave()
            }
            
            // 稍微延迟一下，避免设备或SDK无法处理连续请求
            Thread.sleep(forTimeInterval: 1.0)
        }
        
        // 所有天数的数据获取完成后，执行上传
        group.notify(queue: .main) {
            if totalCount > 0 {
                print("成功获取\(totalCount)条定时心率数据，准备上传...")
                self.uploadPendingHeartRateData { uploadCount, uploadError in
                    completion(uploadCount, uploadError ?? lastError)
                }
            } else {
                print("没有获取到定时心率数据")
                completion(0, lastError)
            }
        }
    }
    
    /// 上传待上传的心率数据
    /// - Parameter completion: 完成回调，包含上传成功的数据数量和可能的错误
    func uploadPendingHeartRateData(completion: ((Int, Error?) -> Void)? = nil) {
        // 防止重复上传
        guard !isUploading else {
            print("已有上传任务正在进行中")
            completion?(0, nil)
            return
        }
        
        // 检查网络可用性
        guard NetworkMonitor.shared.isConnected else {
            print("网络不可用，稍后将自动重试上传")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "网络不可用"]))
            return
        }
        
        // 检查用户是否登录
        guard let userId = authService.currentUser?.id else {
            print("用户未登录，无法上传数据")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        // 验证Token是否存在
        guard let token = authService.currentToken?.accessToken else {
            print("用户未登录或Token已过期，无法上传数据")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录或Token已过期"]))
            return
        }
        
        print("用户已登录，Token: \(token.prefix(10))...")
        
        isUploading = true
        lastUploadAttempt = Date()
        
        // 获取未上传的心率数据
        let unuploadedHeartRates = getUnuploadedHeartRateData(userId: userId)
        
        if unuploadedHeartRates.isEmpty {
            print("没有待上传的心率数据")
            isUploading = false
            completion?(0, nil)
            return
        }
        
        print("找到\(unuploadedHeartRates.count)条待上传的心率数据（所有本地数据）")
        
        // 按日期分组
        let groupedData = Dictionary(grouping: unuploadedHeartRates) { heartRate -> String in
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            return dateFormatter.string(from: heartRate.timestamp!)
        }
        
        print("心率数据按\(groupedData.count)个日期分组")
        
        // 逐日上传
        var uploadedCount = 0
        let group = DispatchGroup()
        var uploadError: Error? = nil
        
        for (dateString, heartRates) in groupedData {
            group.enter()
            
            // 准备上传数据
            let uploadData = prepareHeartRateUploadData(dateString: dateString, heartRates: heartRates)
            
            // 执行上传
            uploadHeartRateData(data: uploadData) { success, error in
                if success {
                    print("成功上传\(dateString)的\(heartRates.count)条心率数据")
                    // 标记这些数据为已上传
                    self.markHeartRateDataAsUploaded(heartRates: heartRates)
                    uploadedCount += heartRates.count
                } else {
                    print("上传\(dateString)的心率数据失败: \(error?.localizedDescription ?? "未知错误")")
                    uploadError = error
                }
                
                group.leave()
            }
        }
        
        // 所有上传完成后的回调
        group.notify(queue: .main) {
            self.isUploading = false
            
            // 发送通知
            NotificationCenter.default.post(
                name: .heartRateDataUploaded,
                object: nil,
                userInfo: [
                    "count": uploadedCount,
                    "success": uploadError == nil
                ]
            )
            
            completion?(uploadedCount, uploadError)
        }
    }
    
    func uploadHeartRateData(completion: ((Int, Error?) -> Void)? = nil) {
        // 检查网络可用性
        guard NetworkMonitor.shared.isConnected else {
            print("网络不可用，稍后将自动重试上传")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "网络不可用"]))
            return
        }
        
        // 检查用户是否登录
        guard let userId = authService.currentUser?.id else {
            print("用户未登录，无法上传数据")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录"]))
            return
        }
        
        // 验证Token是否存在
        guard let token = authService.currentToken?.accessToken else {
            print("用户未登录或Token已过期，无法上传数据")
            completion?(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录或Token已过期"]))
            return
        }
        
        print("用户已登录，Token: \(token.prefix(10))...")
        
        isUploading = true
        lastUploadAttempt = Date()
        
        // 获取未上传的心率数据
        let unuploadedHeartRates = getUnuploadedHeartRateData(userId: userId)
        
        if unuploadedHeartRates.isEmpty {
            print("没有待上传的心率数据")
            isUploading = false
            completion?(0, nil)
            return
        }
        
        print("找到\(unuploadedHeartRates.count)条待上传的心率数据（所有本地数据）")
        
        // 按日期分组
        let groupedData = Dictionary(grouping: unuploadedHeartRates) { heartRate -> String in
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            return dateFormatter.string(from: heartRate.timestamp!)
        }
        
        print("心率数据按\(groupedData.count)个日期分组")
        
        // 逐日上传
        var uploadedCount = 0
        let group = DispatchGroup()
        var uploadError: Error? = nil
        
        for (dateString, heartRates) in groupedData {
            group.enter()
            
            // 准备上传数据
            let uploadData = prepareHeartRateUploadData(dateString: dateString, heartRates: heartRates)
            
            // 执行上传
            uploadHeartRateData(data: uploadData) { success, error in
                if success {
                    print("成功上传\(dateString)的\(heartRates.count)条心率数据")
                    // 标记这些数据为已上传
                    self.markHeartRateDataAsUploaded(heartRates: heartRates)
                    uploadedCount += heartRates.count
                } else {
                    print("上传\(dateString)的心率数据失败: \(error?.localizedDescription ?? "未知错误")")
                    uploadError = error
                }
                
                group.leave()
            }
        }
        
        // 所有上传完成后的回调
        group.notify(queue: .main) {
            self.isUploading = false
            
            // 发送通知
            NotificationCenter.default.post(
                name: .heartRateDataUploaded,
                object: nil,
                userInfo: [
                    "count": uploadedCount,
                    "success": uploadError == nil
                ]
            )
            
            completion?(uploadedCount, uploadError)
        }
    }
    
    /// 获取特定日期的心率数据
    /// - Parameters:
    ///   - day: 日期（0表示今天，1表示昨天，以此类推）
    ///   - completion: 完成回调，返回心率记录和可能的错误
    func getHeartRateData(day: Int, completion: @escaping ([HeartRateRecord]?, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(nil, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        // 确定目标日期
        let calendar = Calendar.current
        let today = Date()
        let targetDate = calendar.date(byAdding: .day, value: -day, to: today) ?? today
        let startOfDay = calendar.startOfDay(for: targetDate)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)?.addingTimeInterval(-1) ?? startOfDay
        
        // 日期格式化器
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: targetDate)
        
        print("获取\(dateString)的心率数据")
        
        #if os(iOS)
        // 使用智能戒指SDK获取日期对应的定时心率数据
//        CRPSmartRingSDK.sharedInstance.getTimingHeartRate(day) { heartRateModel, error in
//            if error == .none {
//                print("获取到\(dateString)的定时心率数据")
//                
//                var records: [HeartRateRecord] = []
//                
//                // 处理心率数据
//                if !heartRateModel.hearts.isEmpty {
//                    // 过滤有效的心率值（大于0且小于250的值）
//                    let validHeartRates = heartRateModel.hearts.enumerated().filter { $0.element > 0 && $0.element < 250 }
//                    
//                    for (index, heartRate) in validHeartRates {
//                        // 计算该5分钟对应的时间戳
//                        // 每个index代表5分钟，从0点开始
//                        let fiveMinutesInSeconds = TimeInterval(index * 5 * 60)
//                        let timestamp = startOfDay.addingTimeInterval(fiveMinutesInSeconds)
//                        let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
//                        
//                        // 创建心率记录
//                        let heartRateRecord = HeartRateRecord(hearts: heartRate, time: timeMs)
//                        records.append(heartRateRecord)
//                    }
//                }
//                
//                if records.isEmpty {
//                    // 如果没有有效记录，尝试从数据库获取
//                    if let userId = self.authService.currentUser?.id {
//                        let heartRates = self.healthDataManager.getHeartRates(
//                            userId: userId,
//                            startDate: startOfDay,
//                            endDate: endOfDay
//                        )
//                        
//                        if !heartRates.isEmpty {
//                            print("从数据库获取到\(heartRates.count)条心率数据")
//                            
//                            for heartRate in heartRates {
//                                if let timestamp = heartRate.timestamp {
//                                    // 使用毫秒级时间戳
//                                    let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
//                                    records.append(HeartRateRecord(
//                                        hearts: Int(heartRate.value),
//                                        time: timeMs
//                                    ))
//                                }
//                            }
//                        }
//                    }
//                }
//                
//                // 按时间排序
//                let sortedRecords = records.sorted { $0.time < $1.time }
//                
//                // 返回结果
//                print("成功获取\(dateString)的心率数据，共\(sortedRecords.count)条记录")
//                completion(sortedRecords, nil)
//            } else {
//                let errorMessage = error == .none ? "未获取到数据" : "错误：\(error)"
//                print("获取心率数据失败：\(errorMessage)")
//                completion(nil, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: errorMessage]))
//            }
//        }
        #else
        // 非iOS平台暂不支持
        completion(nil, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "当前平台不支持获取心率数据"]))
        #endif
    }
    
    // MARK: - 私有方法
    
    /// 初始化通知订阅
    private func setupNotifications() {
        // 设备连接成功后，自动获取历史数据
//        NotificationCenter.default.publisher(for: .deviceSyncCompleted)
//            .filter { notification in
//                notification.userInfo?["success"] as? Bool == true
//            }
//            .debounce(for: .seconds(2), scheduler: RunLoop.main)
//            .sink { [weak self] _ in
//                guard let self = self else { return }
//                print("设备同步完成，准备获取定时心率数据")
//                // 使用定时心率数据替代单次测量的心率历史
//                self.syncTimingHeartRateData(days: 3) { count, error in
//                    if error == nil && count > 0 {
//                        print("自动获取并上传了\(count)条定时心率数据")
//                    }
//                }
//            }
//            .store(in: &cancellables)
        
        // 网络状态变化时，尝试上传
        NotificationCenter.default.publisher(for: .networkStatusChanged)
            .filter { notification in
                notification.userInfo?["connected"] as? Bool == true
            }
            .sink { [weak self] _ in
                guard let self = self else { return }
                print("网络已连接，尝试上传待上传的心率数据")
                self.uploadPendingHeartRateData()
            }
            .store(in: &cancellables)
    }
    
    /// 将从设备获取的心率数据保存到本地数据库
    /// - Parameters:
    ///   - records: 设备返回的心率记录数组
    ///   - completion: 完成回调，返回成功保存的记录数量
    private func saveHeartRateData(records: [CRPHeartRecordModel], completion: @escaping (Int) -> Void) {
        guard let userId = authService.currentUser?.id else {
            print("用户未登录，无法保存心率数据")
            completion(0)
            return
        }
        
        // 如果没有用户ID，检查设备信息
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown"
        
        // 转换为批量添加格式
        var heartRateData: [(value: Int16, timestamp: Date, deviceId: String?, confidence: Int16?)] = []
        
        for record in records {
            if record.value > 0 && record.value < 250 { // 基本有效范围检查
                // 将记录时间戳(Unix时间戳)转换为Date对象
                let timestamp = Date(timeIntervalSince1970: TimeInterval(record.time))
                
                heartRateData.append((
                    value: Int16(record.value),
                    timestamp: timestamp,
                    deviceId: deviceId,
                    confidence: 100 // 假设设备数据可信度为100%
                ))
            }
        }
        
        // 检查是否需要添加数据
        if heartRateData.isEmpty {
            print("没有有效的心率数据需要保存")
            completion(0)
            return
        }
        
        // 批量添加数据
        let success = healthDataManager.addHeartRates(userId: userId, data: heartRateData)
        
        // 返回结果
        if success {
            completion(heartRateData.count)
        } else {
            print("保存心率数据失败")
            completion(0)
        }
    }
    
    /// 获取未上传的心率数据
    /// - Parameter userId: 用户ID
    /// - Returns: 未上传的心率数据数组
    private func getUnuploadedHeartRateData(userId: String) -> [HeartRateEntity] {
        // 不限制时间范围，获取所有数据
        let heartRates = healthDataManager.getHeartRates(userId: userId, startDate: nil, endDate: nil)
        
        // 使用isUploaded属性过滤出未上传的数据
        return heartRates.filter { $0.isUploaded == false }
    }
    
    /// 准备上传数据
    /// - Parameters:
    ///   - dateString: 日期字符串
    ///   - heartRates: 心率数据数组
    /// - Returns: 上传数据模型
    private func prepareHeartRateUploadData(dateString: String, heartRates: [HeartRateEntity]) -> HeartRateUploadData {
        var records: [HeartRateRecord] = []
        
        print("准备\(dateString)的\(heartRates.count)条心率数据，使用毫秒级时间戳格式")
        
        for heartRate in heartRates {
            if let timestamp = heartRate.timestamp {
                // 使用毫秒级时间戳
                let timeStampSeconds = Int64(timestamp.timeIntervalSince1970)
                let timeStampMilliseconds = timeStampSeconds * 1000
                
                // 打印转换过程，方便调试
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                let timeString = dateFormatter.string(from: timestamp)
                print("时间转换：\(timeString) -> 秒级时间戳：\(timeStampSeconds) -> 毫秒级时间戳：\(timeStampMilliseconds)")
                
                records.append(HeartRateRecord(
                    hearts: Int(heartRate.value),
                    time: timeStampMilliseconds
                ))
            }
        }
        
        return HeartRateUploadData(
            date: dateString,
            records: records
        )
    }
    
    /// 上传心率数据到服务器
    /// - Parameters:
    ///   - data: 心率上传数据模型
    ///   - completion: 完成回调
    private func uploadHeartRateData(data: HeartRateUploadData, completion: @escaping (Bool, Error?) -> Void) {
        // 构建API URL
        let baseURLString = "http://ring-api-dev.weaving-park.com" // 使用开发环境API基础URL
        guard let baseURL = URL(string: baseURLString) else {
            completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的服务器URL"]))
            return
        }
        
        let uploadURL = baseURL.appendingPathComponent(apiPath)
        print("上传心率数据到URL: \(uploadURL.absoluteString)")
        
        // 创建请求
        var request = URLRequest(url: uploadURL)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加认证信息
        if let token = authService.currentToken?.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("添加Authorization请求头: Bearer \(token.prefix(10))...")
        } else {
            print("警告: 未找到有效的token")
        }
        
        // 添加租户ID
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        print("添加tenant-id请求头: 1")
        
        // 打印所有请求头
        print("请求头:")
        request.allHTTPHeaderFields?.forEach { key, value in
            print("\(key): \(value.prefix(30))...")
        }
        
        // 准备请求体
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let jsonData = try encoder.encode(data)
            
            // 打印上传的JSON数据
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("上传的心率数据JSON: \n\(jsonString)")
            }
            
            request.httpBody = jsonData
            
            // 创建并执行网络请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                // 处理网络错误
                if let error = error {
                    print("上传心率数据网络错误: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(false, error)
                    }
                    return
                }
                
                // 检查HTTP响应
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("无效的HTTP响应")
                    DispatchQueue.main.async {
                        completion(false, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
                    }
                    return
                }
                
                // 打印响应头
                print("响应状态码: \(httpResponse.statusCode)")
                print("响应头:")
                httpResponse.allHeaderFields.forEach { key, value in
                    print("\(key): \(value)")
                }
                
                // 打印响应数据
                if let responseData = data, let responseString = String(data: responseData, encoding: .utf8) {
                    print("服务器响应: \(responseString)")
                }
                
                // 检查状态码
                if httpResponse.statusCode == 200 {
                    print("心率数据上传成功")
                    DispatchQueue.main.async {
                        completion(true, nil)
                    }
                } else {
                    let responseString = data != nil ? String(data: data!, encoding: .utf8) ?? "无数据" : "无数据"
                    print("心率数据上传失败，状态码: \(httpResponse.statusCode), 响应: \(responseString)")
                    
                    // 解析错误响应
                    var errorMessage = "上传失败，HTTP状态码: \(httpResponse.statusCode)"
                    if let data = data {
                        do {
                            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                                print("错误响应JSON: \(json)")
                                
                                if let code = json["code"] as? Int {
                                    print("错误码: \(code)")
                                }
                                
                                if let msg = json["msg"] as? String {
                                    print("错误消息: \(msg)")
                                    errorMessage = msg
                                }
                                
                                if let errorData = json["data"] as? [String: Any] {
                                    print("错误数据: \(errorData)")
                                }
                            }
                        } catch {
                            print("解析错误响应失败: \(error.localizedDescription)")
                        }
                    }
                    
                    // 处理特定错误情况
                    if httpResponse.statusCode == 401 {
                        errorMessage = "用户未登录或身份验证已过期，请重新登录"
                    } else if httpResponse.statusCode == 403 {
                        errorMessage = "用户无权限执行此操作"
                    }
                    
                    DispatchQueue.main.async {
                        let error = NSError(
                            domain: "com.windring.error",
                            code: httpResponse.statusCode,
                            userInfo: [NSLocalizedDescriptionKey: errorMessage]
                        )
                        completion(false, error)
                    }
                }
            }.resume()
        } catch {
            print("编码心率数据失败: \(error.localizedDescription)")
            completion(false, error)
        }
    }
    
    /// 将心率数据标记为已上传
    /// - Parameter heartRates: 心率数据数组
    private func markHeartRateDataAsUploaded(heartRates: [HeartRateEntity]) {
        // 获取Core Data上下文
        let context = StorageManager.shared.viewContext()
        
        // 设置isUploaded属性
        for heartRate in heartRates {
            heartRate.isUploaded = true
        }
        
        // 保存上下文
        do {
            try context.save()
            print("已将\(heartRates.count)条心率数据标记为已上传")
        } catch {
            print("将心率数据标记为已上传失败: \(error.localizedDescription)")
        }
    }
}

// MARK: - 模型定义

/// 心率记录模型
struct HeartRateRecord: Codable {
    let hearts: Int
    let time: Int64 // 使用Int64类型，表示毫秒级时间戳
}

/// 心率上传数据模型
struct HeartRateUploadData: Codable {
    let date: String     // 日期
    let records: [HeartRateRecord]  // 心率记录集合
}

// MARK: - 通知扩展
// 注意：所有通知名称应统一在 NotificationExtensions.swift 中定义，此段代码已被注释掉
/*
extension Notification.Name {
    /// 心率数据上传通知
    static let heartRateDataUploaded = Notification.Name("heartRateDataUploaded")
} 
*/ 
