import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

/// WindRing 应用主题
struct WRTheme {
    /// 主题颜色
    struct Colors {
        /// 主色调
        #if canImport(UIKit)
        static let primary = Color(UIColor(red: 0.98, green: 0.42, blue: 0.18, alpha: 1.0)) // #FA6C2D
        #else
        static let primary = Color(red: 0.98, green: 0.42, blue: 0.18) // #FA6C2D
        #endif
        
        /// 次要色调
        #if canImport(UIKit)
        static let secondary = Color(UIColor(red: 0.29, green: 0.56, blue: 0.91, alpha: 1.0)) // #4A8FE7
        #else
        static let secondary = Color(red: 0.29, green: 0.56, blue: 0.91) // #4A8FE7
        #endif
        
        /// 成功色调
        #if canImport(UIKit)
        static let success = Color(UIColor(red: 0.3, green: 0.69, blue: 0.31, alpha: 1.0)) // #4CAF50
        #else
        static let success = Color(red: 0.3, green: 0.69, blue: 0.31) // #4CAF50
        #endif
        
        /// 警告色调
        #if canImport(UIKit)
        static let warning = Color(UIColor(red: 1.0, green: 0.76, blue: 0.03, alpha: 1.0)) // #FFC107
        #else
        static let warning = Color(red: 1.0, green: 0.76, blue: 0.03) // #FFC107
        #endif
        
        /// 错误色调
        #if canImport(UIKit)
        static let error = Color(UIColor(red: 0.96, green: 0.26, blue: 0.21, alpha: 1.0)) // #F44336
        #else
        static let error = Color(red: 0.96, green: 0.26, blue: 0.21) // #F44336
        #endif
        
        /// 背景色
        #if canImport(UIKit)
        static let background = Color(UIColor(red: 0.96, green: 0.96, blue: 0.96, alpha: 1.0)) // #F5F5F5
        #else
        static let background = Color(red: 0.96, green: 0.96, blue: 0.96) // #F5F5F5
        #endif
        
        /// 卡片背景色
        static let cardBackground = Color.white
        
        /// 文本主色
        #if canImport(UIKit)
        static let textPrimary = Color(UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0)) // #333333
        #else
        static let textPrimary = Color(red: 0.2, green: 0.2, blue: 0.2) // #333333
        #endif
        
        /// 文本次要色
        #if canImport(UIKit)
        static let textSecondary = Color(UIColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1.0)) // #666666
        #else
        static let textSecondary = Color(red: 0.4, green: 0.4, blue: 0.4) // #666666
        #endif
        
        /// 文本提示色
        #if canImport(UIKit)
        static let textHint = Color(UIColor(red: 0.6, green: 0.6, blue: 0.6, alpha: 1.0)) // #999999
        #else
        static let textHint = Color(red: 0.6, green: 0.6, blue: 0.6) // #999999
        #endif
        
        /// 边框色
        #if canImport(UIKit)
        static let border = Color(UIColor(red: 0.93, green: 0.93, blue: 0.93, alpha: 1.0)) // #EEEEEE
        #else
        static let border = Color(red: 0.93, green: 0.93, blue: 0.93) // #EEEEEE
        #endif
        
        /// 分隔线颜色
        #if canImport(UIKit)
        static let divider = Color(UIColor(red: 0.88, green: 0.88, blue: 0.88, alpha: 1.0)) // #E0E0E0
        #else
        static let divider = Color(red: 0.88, green: 0.88, blue: 0.88) // #E0E0E0
        #endif
    }
    
    /// 字体大小
    struct FontSizes {
        static let small: CGFloat = 12
        static let medium: CGFloat = 16
        static let large: CGFloat = 20
        static let xLarge: CGFloat = 24
        static let xxLarge: CGFloat = 30
    }
    
    /// 间距
    struct Spacing {
        static let xSmall: CGFloat = 4
        static let small: CGFloat = 8
        static let medium: CGFloat = 16
        static let large: CGFloat = 24
        static let xLarge: CGFloat = 32
    }
    
    /// 边框圆角
    struct Radius {
        static let small: CGFloat = 4
        static let medium: CGFloat = 8
        static let large: CGFloat = 12
        static let xLarge: CGFloat = 16
        static let round: CGFloat = 9999
    }
    
    /// 阴影
    struct Shadows {
        static let small = ShadowStyle(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        static let medium = ShadowStyle(color: .black.opacity(0.15), radius: 8, x: 0, y: 4)
        static let large = ShadowStyle(color: .black.opacity(0.2), radius: 16, x: 0, y: 8)
    }
}

/// 阴影样式结构体
struct ShadowStyle {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
    
    func apply() -> some View {
        return AnyView(
            Color.clear.shadow(color: color, radius: radius, x: x, y: y)
        )
    }
} 