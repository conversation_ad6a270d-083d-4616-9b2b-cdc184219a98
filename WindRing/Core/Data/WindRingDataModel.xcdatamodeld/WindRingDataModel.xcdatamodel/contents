<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="23788" systemVersion="24D2059" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Activity" representedClassName="ActivityEntity" syncable="YES" codeGenerationType="class">
        <attribute name="calories" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="distance" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="duration" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="endTime" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="steps" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="type" attributeType="String"/>
        <attribute name="userId" attributeType="String"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="BloodOxygen" representedClassName="BloodOxygenEntity" syncable="YES" codeGenerationType="class">
        <attribute name="confidence" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="BloodPressure" representedClassName="BloodPressureEntity" syncable="YES" codeGenerationType="class">
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="diastolic" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="pulse" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="systolic" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="CachedMQTTMessage" representedClassName="CachedMQTTMessage" syncable="YES">
        <attribute name="id" attributeType="UUID" defaultValueString="00000000-0000-0000-0000-000000000000" usesScalarValueType="NO"/>
        <attribute name="isSynced" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="message" optional="YES" attributeType="Binary"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="topic" optional="YES" attributeType="String"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="DailyStress" representedClassName="DailyStressEntity" syncable="YES" codeGenerationType="class">
        <attribute name="date" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="score" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
            <uniquenessConstraint>
                <constraint value="userId"/>
                <constraint value="date"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Device" representedClassName="DeviceEntity" syncable="YES" codeGenerationType="class">
        <attribute name="batteryLevel" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="connectionStatus" attributeType="String"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="firmwareVersion" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="lastSyncTime" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="manufacturer" attributeType="String"/>
        <attribute name="model" attributeType="String"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="settings" optional="YES" attributeType="Transformable" valueTransformerName="NSSecureUnarchiveFromDataTransformer" customClassName="[String: String]"/>
        <attribute name="type" attributeType="String"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="HealthGoal" representedClassName="HealthGoalEntity" syncable="YES" codeGenerationType="class">
        <attribute name="completed" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="current" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="endDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="startDate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="target" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="type" attributeType="String"/>
        <attribute name="unit" attributeType="String"/>
        <attribute name="userId" attributeType="String"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="HeartRate" representedClassName="HeartRateEntity" syncable="YES" codeGenerationType="class">
        <attribute name="confidence" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="isUploaded" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="HRV" representedClassName="HRVEntity" syncable="YES" codeGenerationType="class">
        <attribute name="confidence" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="isUploaded" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Meditation" representedClassName="MeditationEntity" syncable="YES" codeGenerationType="class">
        <attribute name="duration" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="endTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="type" optional="YES" attributeType="String"/>
        <attribute name="userId" attributeType="String"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Sleep" representedClassName="SleepEntity" syncable="YES" codeGenerationType="class">
        <attribute name="awakeMinutes" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="deepMinutes" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="efficiency" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="endTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="lightMinutes" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="remMinutes" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="score" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="totalMinutes" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="userId" attributeType="String"/>
        <relationship name="sleepStages" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="SleepStage" inverseName="sleep" inverseEntity="SleepStage"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="SleepStage" representedClassName="SleepStageEntity" syncable="YES" codeGenerationType="class">
        <attribute name="duration" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="sleepId" attributeType="String"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="type" attributeType="String"/>
        <relationship name="sleep" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Sleep" inverseName="sleepStages" inverseEntity="Sleep"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Steps" representedClassName="StepsEntity" syncable="YES" codeGenerationType="class">
        <attribute name="calories" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="date" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="distance" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Stress" representedClassName="StressEntity" syncable="YES" codeGenerationType="class">
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Temperature" representedClassName="TemperatureEntity" syncable="YES" codeGenerationType="class">
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="type" attributeType="String"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="TrainingRecord" representedClassName="TrainingRecordEntity" syncable="YES" codeGenerationType="class">
        <attribute name="calory" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="date" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="distance" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="exerciseTime" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="steps" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
    </entity>
    <entity name="Water" representedClassName="WaterEntity" syncable="YES" codeGenerationType="class">
        <attribute name="date" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <entity name="Weight" representedClassName="WeightEntity" syncable="YES" codeGenerationType="class">
        <attribute name="bmi" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="bodyFat" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="deviceId" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="muscleMass" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="String"/>
        <attribute name="value" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="id"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
</model>