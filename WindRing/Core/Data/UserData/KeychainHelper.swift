//
//  KeychainHelper.swift
//  WindRing
//
//  Created by zx on 2025/5/21.
//
import Foundation
import Security

import Foundation
import Security

class KeychainHelper {
    static let shared = KeychainHelper()

    func save(_ data: Data, account: String) {
        let query: CFDictionary = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: AppGlobals.apiBaseURL,
            kSecAttrAccount: account,
            kSecValueData: data
        ] as CFDictionary

        SecItemDelete(query)
        SecItemAdd(query, nil)
    }

    func read( account: String) -> Data? {
        let query: CFDictionary = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: AppGlobals.apiBaseURL,
            kSecAttrAccount: account,
            kSecReturnData: true,
            kSecMatchLimit: kSecMatchLimitOne
        ] as CFDictionary

        var result: AnyObject?
        SecItemCopyMatching(query, &result)
        return result as? Data
    }

    func delete(account: String) {
        let query: CFDictionary = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: AppGlobals.apiBaseURL,
            kSecAttrAccount: account
        ] as CFDictionary

        SecItemDelete(query)
    }
    
    func saveUserInfo(_ user: UserInfo) {
        if let data = try? JSONEncoder().encode(user) {
            KeychainHelper.shared.save(data, account: "userInfo")
        }
    }
    
    func loadUserInfo() -> UserInfo? {
        if let data = KeychainHelper.shared.read(account: "userInfo"),
           let user = try? CleanJSONDecoder().decode(UserInfo.self, from: data) {
            return user
        }
        return nil
    }
    
    func deleteUserInfo() {
        KeychainHelper.shared.delete(account: "userInfo")
    }
}
