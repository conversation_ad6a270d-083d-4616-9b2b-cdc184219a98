//
//  UserInfo.swift
//  WindRing
//
//  Created by zx on 2025/5/21.
//

import Foundation

public struct TokenModel: Codable {
    let userId: Int
    let accessToken: String
    let refreshToken: String
    let expiresTime: Date
    let openid: String
//    let expiresAt: TimeInterval
}

public struct SessionInfoModel: Codable {
    let token: TokenModel?
    let user: UserInfo?
}

/// 用户信息
public struct UserInfo: Codable {
    /// 用户编号（主键ID）
    let id: String

    /// 用户唯一标识 ID
    let identityId: String

    /// 用户昵称
    var nickname: String

    /// 用户头像 URL
    var avatar: String

    /// 用户手机号
    var mobile: String

    /// 用户性别（例如 0 未知，1 男，2 女）
    var sex: Int

    /// 用户积分
    let point: Int

    /// 用户经验值
    let experience: Int

    /// 用户等级信息（可选）
    let level: LevelModel?

    /// 出生日期（可选）
    var birthday: Int?

    /// 邮箱地址（可选）
    var email: String?

    /// 身高（可选，单位视 heightType 而定）
    var height: Double?

    /// 身高类型（可选，例如 1 表示 cm）
    var heightType: Int?

    /// 体重（可选，单位视 weightType 而定）
    var weight: Double?

    /// 体重类型（可选，例如 1 表示 kg）
    var weightType: Int?

    /// 是否是推广员
    let brokerageEnabled: Bool

    /// 设备名称（可选）
    let deviceName: String?

    /// 设备序列号（可选）
    let deviceSn: String?

    /// 戒指设备 MAC 地址（可选）
    let deviceMac: String?

    /// 设备绑定时间（可选）
    let bindTime: Date?

    /// 用户类型（可选，1 表示会员，2 表示访客等）
    let userType: Int?
}

/// 用户 App - 会员等级信息
struct LevelModel: Codable, Equatable {
    /// 等级编号
    let id: Int

    /// 等级名称
    let name: String

    /// 等级数值
    let level: Int

    /// 等级图标（可选）
    let icon: String?
}
