import Foundation
import CoreData

/// 原始数据管理器
/// 负责管理从戒指获取的原始数据，存储到本地数据库
public class RawDataManager {
    // MARK: - 单例
    public static let shared = RawDataManager()
    
    // MARK: - 属性
    private let storageManager: StorageManager
    
    // MARK: - 初始化
    private init() {
        self.storageManager = StorageManager.shared
    }
    
    // MARK: - 公共方法
    
    /// 保存原始心率数据
    /// - Parameters:
    ///   - data: 心率数据
    ///   - userId: 用户ID
    ///   - deviceId: 设备ID
    ///   - timestamp: 时间戳
    ///   - isProcessed: 是否已处理
    /// - Returns: 是否保存成功
    @discardableResult
    public func saveRawHeartRateData(value: Int, userId: String, deviceId: String, timestamp: Date, isProcessed: Bool = false) -> Bool {
        let context = storageManager.viewContext()
        
        // 创建唯一ID
        let id = UUID().uuidString
        
        // 检查是否已存在相同时间戳的数据
        let fetchRequest: NSFetchRequest<HeartRateEntity> = HeartRateEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "userId == %@ AND timestamp == %@", userId, timestamp as NSDate)
        
        do {
            let existingData = try context.fetch(fetchRequest)
            if !existingData.isEmpty {
                print("已存在相同时间戳的心率数据，跳过保存")
                return false
            }
        } catch {
            print("检查现有心率数据失败: \(error)")
            return false
        }
        
        // 创建新的心率数据实体
        let heartRateEntity = HeartRateEntity(context: context)
        heartRateEntity.id = id
        heartRateEntity.userId = userId
        heartRateEntity.deviceId = deviceId
        heartRateEntity.timestamp = timestamp
        heartRateEntity.value = Int16(value)
        
        // 添加是否已处理的标记
        heartRateEntity.setValue(isProcessed, forKey: "isProcessed")
        
        // 保存上下文
        do {
            try context.save()
            return true
        } catch {
            print("保存心率数据失败: \(error)")
            return false
        }
    }
    
    /// 保存原始血氧数据
    /// - Parameters:
    ///   - value: 血氧值
    ///   - userId: 用户ID
    ///   - deviceId: 设备ID
    ///   - timestamp: 时间戳
    ///   - isProcessed: 是否已处理
    /// - Returns: 是否保存成功
    @discardableResult
    public func saveRawBloodOxygenData(value: Double, userId: String, deviceId: String, timestamp: Date, isProcessed: Bool = false) -> Bool {
        let context = storageManager.viewContext()
        
        // 创建唯一ID
        let id = UUID().uuidString
        
        // 检查是否已存在相同时间戳的数据
        let fetchRequest: NSFetchRequest<BloodOxygenEntity> = BloodOxygenEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "userId == %@ AND timestamp == %@", userId, timestamp as NSDate)
        
        do {
            let existingData = try context.fetch(fetchRequest)
            if !existingData.isEmpty {
                print("已存在相同时间戳的血氧数据，跳过保存")
                return false
            }
        } catch {
            print("检查现有血氧数据失败: \(error)")
            return false
        }
        
        // 创建新的血氧数据实体
        let bloodOxygenEntity = BloodOxygenEntity(context: context)
        bloodOxygenEntity.id = id
        bloodOxygenEntity.userId = userId
        bloodOxygenEntity.deviceId = deviceId
        bloodOxygenEntity.timestamp = timestamp
        bloodOxygenEntity.value = value
        
        // 添加是否已处理的标记
        bloodOxygenEntity.setValue(isProcessed, forKey: "isProcessed")
        
        // 保存上下文
        do {
            try context.save()
            return true
        } catch {
            print("保存血氧数据失败: \(error)")
            return false
        }
    }
    
    /// 保存原始活动数据
    /// - Parameters:
    ///   - steps: 步数
    ///   - distance: 距离
    ///   - calories: 卡路里
    ///   - userId: 用户ID
    ///   - deviceId: 设备ID
    ///   - date: 日期
    ///   - isProcessed: 是否已处理
    /// - Returns: 是否保存成功
    @discardableResult
    public func saveRawActivityData(steps: Int, distance: Double, calories: Int, userId: String, deviceId: String, date: Date, isProcessed: Bool = false) -> Bool {
        let context = storageManager.viewContext()
        
        // 创建唯一ID
        let id = UUID().uuidString
        
        // 检查是否已存在相同日期的数据
        let fetchRequest: NSFetchRequest<StepsEntity> = StepsEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "userId == %@ AND date == %@", userId, date as NSDate)
        
        do {
            let existingData = try context.fetch(fetchRequest)
            if !existingData.isEmpty {
                print("已存在相同日期的活动数据，跳过保存")
                return false
            }
        } catch {
            print("检查现有活动数据失败: \(error)")
            return false
        }
        
        // 创建新的活动数据实体
        let stepsEntity = StepsEntity(context: context)
        stepsEntity.id = id
        stepsEntity.userId = userId
        stepsEntity.deviceId = deviceId
        stepsEntity.date = date
        stepsEntity.timestamp = date
        stepsEntity.value = Int32(steps)
        stepsEntity.distance = distance
        stepsEntity.calories = Int32(calories)
        
        // 添加是否已处理的标记
        stepsEntity.setValue(isProcessed, forKey: "isProcessed")
        
        // 保存上下文
        do {
            try context.save()
            return true
        } catch {
            print("保存活动数据失败: \(error)")
            return false
        }
    }
    
    /// 获取未处理的原始心率数据
    /// - Returns: 未处理的心率数据列表
    public func getUnprocessedHeartRateData() -> [HeartRateEntity] {
        let fetchRequest: NSFetchRequest<HeartRateEntity> = HeartRateEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "isProcessed == %@", NSNumber(value: false))
        fetchRequest.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
        
        do {
            return try storageManager.viewContext().fetch(fetchRequest)
        } catch {
            print("获取未处理的心率数据失败: \(error)")
            return []
        }
    }
    
    /// 获取未处理的原始血氧数据
    /// - Returns: 未处理的血氧数据列表
    public func getUnprocessedBloodOxygenData() -> [BloodOxygenEntity] {
        let fetchRequest: NSFetchRequest<BloodOxygenEntity> = BloodOxygenEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "isProcessed == %@", NSNumber(value: false))
        fetchRequest.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
        
        do {
            return try storageManager.viewContext().fetch(fetchRequest)
        } catch {
            print("获取未处理的血氧数据失败: \(error)")
            return []
        }
    }
    
    /// 获取未处理的原始活动数据
    /// - Returns: 未处理的活动数据列表
    public func getUnprocessedActivityData() -> [StepsEntity] {
        let fetchRequest: NSFetchRequest<StepsEntity> = StepsEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "isProcessed == %@", NSNumber(value: false))
        fetchRequest.sortDescriptors = [NSSortDescriptor(key: "date", ascending: true)]
        
        do {
            return try storageManager.viewContext().fetch(fetchRequest)
        } catch {
            print("获取未处理的活动数据失败: \(error)")
            return []
        }
    }
    
    /// 标记心率数据为已处理
    /// - Parameter entity: 心率数据实体
    /// - Returns: 是否标记成功
    @discardableResult
    public func markHeartRateAsProcessed(_ entity: HeartRateEntity) -> Bool {
        entity.setValue(true, forKey: "isProcessed")
        
        do {
            try storageManager.viewContext().save()
            return true
        } catch {
            print("标记心率数据为已处理失败: \(error)")
            return false
        }
    }
    
    /// 标记血氧数据为已处理
    /// - Parameter entity: 血氧数据实体
    /// - Returns: 是否标记成功
    @discardableResult
    public func markBloodOxygenAsProcessed(_ entity: BloodOxygenEntity) -> Bool {
        entity.setValue(true, forKey: "isProcessed")
        
        do {
            try storageManager.viewContext().save()
            return true
        } catch {
            print("标记血氧数据为已处理失败: \(error)")
            return false
        }
    }
    
    /// 标记活动数据为已处理
    /// - Parameter entity: 活动数据实体
    /// - Returns: 是否标记成功
    @discardableResult
    public func markActivityAsProcessed(_ entity: StepsEntity) -> Bool {
        entity.setValue(true, forKey: "isProcessed")
        
        do {
            try storageManager.viewContext().save()
            return true
        } catch {
            print("标记活动数据为已处理失败: \(error)")
            return false
        }
    }
}