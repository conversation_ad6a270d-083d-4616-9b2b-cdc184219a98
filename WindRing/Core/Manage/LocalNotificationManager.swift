//
//  LocalNotificationService.swift
//  WindRing
//
//  Created by zx on 2025/7/1.
//

import Foundation
import UserNotifications

import Foundation
import UserNotifications
import Combine

extension LocalNotificationManager{
    func SedentaryReminderNotification(){
        LocalNotificationManager.shared.scheduleNotification(
                                title: NSLocalizedString("notification_sedentary_title", comment: "Sedentary reminder title"),
                                body: NSLocalizedString("notification_sedentary_body", comment: "Sedentary reminder body"),
                                after: 5
                            )
    }
    
    func BatteryReminderNotification(){	
        LocalNotificationManager.shared.scheduleNotification(
            title: NSLocalizedString("notification_battery_title", comment: "Battery reminder title"),
            body: NSLocalizedString("notification_battery_body", comment: "Battery reminder body"),
            after: 5
        )
    }
}

class LocalNotificationManager: NSObject, ObservableObject {
    static let shared = LocalNotificationManager()
    
    @Published var didReceiveNotification: Bool = false
    private override init() {
        super.init()
        UNUserNotificationCenter.current().delegate = self
    }
    
    /// 请求权限
    func requestPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if granted {
                self.setupCategories()
            } else {
                print("❌ 通知权限被拒绝")
            }
        }
    }

    /// 设置通知分类（用于按钮/交互）
    private func setupCategories() {
        let openAction = UNNotificationAction(identifier: "OPEN_ACTION", title: "查看", options: [.foreground])
        let dismissAction = UNNotificationAction(identifier: "DISMISS_ACTION", title: "忽略", options: [.destructive])

        let category = UNNotificationCategory(
            identifier: "ALERT_CATEGORY",
            actions: [openAction, dismissAction],
            intentIdentifiers: [],
            options: []
        )

        UNUserNotificationCenter.current().setNotificationCategories([category])
    }

    /// 调度通知
    func scheduleNotification(
        title: String,
        body: String,
        after seconds: TimeInterval = 5
    ) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.categoryIdentifier = "ALERT_CATEGORY"

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: seconds, repeats: false)
        let request = UNNotificationRequest(identifier: UUID().uuidString, content: content, trigger: trigger)

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("❌ 通知调度失败：\(error.localizedDescription)")
            } else {
                print("✅ 通知调度成功")
            }
        }
    }
}

extension LocalNotificationManager: UNUserNotificationCenterDelegate {
    // App 在前台收到通知
    func userNotificationCenter(_ center: UNUserNotificationCenter,
                                willPresent notification: UNNotification,
                                withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        print("📬 收到通知（前台）")
        completionHandler([.banner, .sound])
    }

    // 用户点击通知
    func userNotificationCenter(_ center: UNUserNotificationCenter,
                                didReceive response: UNNotificationResponse,
                                withCompletionHandler completionHandler: @escaping () -> Void) {
        print("📲 用户点击通知: \(response.actionIdentifier)")
        if response.actionIdentifier == "OPEN_ACTION" ||
           response.actionIdentifier == UNNotificationDefaultActionIdentifier {
            DispatchQueue.main.async {
                self.didReceiveNotification = true
            }
        }
        completionHandler()
    }
}
