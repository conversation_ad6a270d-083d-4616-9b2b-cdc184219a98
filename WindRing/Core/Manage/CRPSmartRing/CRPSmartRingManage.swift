//
//  CRPSmartRingManage.swift
//  WindRing
//
//  Created by zx on 2025/5/19.
//

import Foundation
import CRPSmartRing // 导入 SDK
import SwiftUI

// MARK: - CRPSmartRingManageDelegate Protocol
protocol CRPSmartRingManageDelegate: AnyObject {
    // MARK: - 连接与扫描回调
    /// 发现外设回调
    /// - Parameter peripherals: 发现的设备数组 (CRPDiscovery类型)
    func didDiscoverPeripherals(peripherals: [CRPDiscovery])
    
    /// 扫描外设失败回调
    /// - Parameter error: 错误信息 (CRPError类型)
    func didFailToScanPeripherals(error: CRPError)
    
    /// 设备连接状态更新回调
    /// - Parameter state: 当前连接状态 (CRPState类型)
    func didUpdateState(state: CRPState)
    
    /// 蓝牙状态更新回调
    /// - Parameter state: 当前蓝牙状态 (CRPBluetoothState类型)
    func didUpdateBluetoothState(state: CRPBluetoothState)
    
    /// 设备配对状态更新回调
    /// - Parameter state: 当前配对状态 (CRPPairState类型)
    func didReceivePairState(state: CRPPairState)

    // MARK: - 健康数据实时回调
    /// 接收到步数数据回调
    /// - Parameter model: 步数数据模型 (CRPStepModel类型)
    func didReceiveSteps(model: CRPStepModel)
    
    /// 接收到单次心率测量结果回调
    /// - Parameter heartRate: 心率值
    func didReceiveHeartRate(heartRate: Int)
    
    /// 接收到实时心率数据回调 (通常用于UI展示，不建议存储)
    /// - Parameter heartRate: 实时心率值
    func didReceiveRealTimeHeartRate(heartRate: Int)
    
    /// 接收到HRV(心率变异性)测量结果回调
    /// - Parameter hrv: HRV值
    func didReceiveHRV(hrv: Int)
    
    /// 接收到SpO2(血氧饱和度)测量结果回调
    /// - Parameter o2: 血氧值
    func didReceiveSpO2(o2: Int)
    
    /// 接收到压力值测量结果回调
    /// - Parameter stress: 压力值
    func didReceiveStress(stress: Int)
    
    /// 接收到单次血压测量结果回调
    /// - Parameters:
    ///   - sbp: 收缩压
    ///   - dbp: 舒张压
    func didReceiveBloodPressure(sbp: Int, dbp: Int)
    
    /// 接收到单次温度测量结果回调
    /// - Parameter value: 温度值 (摄氏度)
    func didReceiveTemperature(value: Double)
    
    /// 接收到设备佩戴状态回调
    /// - Parameter state: 佩戴状态 (具体值含义参考SDK文档，如00:未佩戴, 01:已佩戴)
    func didReceiveWearState(state: Int)

    // MARK: - 设备控制与事件回调
    /// 固件升级状态与进度回调
    /// - Parameters:
    ///   - state: OTA升级状态 (CRPOTAState类型)
    ///   - progress: 升级进度 (0-100)
    func didReceiveOTAState(state: CRPOTAState, progress: Int)
    
    /// SOS触发回调
    func didReceiveSOS()
    
    /// 戒指端触发拍照回调
    func didReceviePhoto()
    
    /// 接收到触摸模式与类型回调
    /// - Parameter model: 触摸数据模型 (CRPTouchModel类型)
    func didReceiveTouchType(model: CRPTouchModel)
    
    /// 接收到敲击开关状态与类型回调
    /// - Parameter model: 触摸数据模型 (CRPTouchModel类型)
    func didReceiveKnockSwitch(model: CRPTouchModel)
    
    /// 接收到冥想状态回调
    /// - Parameter state: 冥想状态 (具体值含义参考SDK文档)
    func didReceiveMeditationState(state: Int)

    // MARK: - 数据获取结果回调
    /// 获取固件版本号结果回调
    /// - Parameters:
    ///   - version: 固件版本号字符串，若获取失败则为nil
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetFirmwareVersion(version: String?, error: CRPError?)
    
    /// 获取MAC地址结果回调
    /// - Parameters:
    ///   - mac: MAC地址字符串，若获取失败则为nil
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetMacAddress(mac: String?, error: CRPError?)
    
    /// 获取电池电量结果回调
    /// - Parameters:
    ///   - level: 电量百分比 (0-100)
    ///   - isInCharge: 是否正在充电
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetBatteryLevel(level: Int, isInCharge: Bool, error: CRPError?)
    
    /// 获取用户信息结果回调
    /// - Parameters:
    ///   - userInfo: 用户信息模型 (CRPUserInfoModel类型)，若获取失败则为nil
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetUserInfo(userInfo: CRPUserInfoModel?, error: CRPError?)
    
    /// 获取历史运动统计数据结果回调
    /// - Parameters:
    ///   - data: 运动记录数据模型 (CRPTrainingRecordModel类型)，若获取失败则为nil
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetTrainingData(data: CRPTrainingRecordModel?, error: CRPError?)
    
    /// 获取半小时步数统计数据结果回调
    /// - Parameters:
    ///   - data: 定时步数记录模型 (CRPTimingStepsRecordModel类型)，若获取失败则为nil
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetStepArchiveData(data: CRPTimingStepsRecordModel?, error: CRPError?)
    
    /// 获取睡眠数据结果回调
    /// - Parameters:
    ///   - data: 睡眠记录模型 (CRPSleepRecordModel类型)，若获取失败则为nil
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetSleepData(data: CRPSleepRecordModel?, error: CRPError?)

    // MARK: - 最新功能回调 (7.x)
    /// 接收到GoMore睡眠数据列表回调 (注意: 这是通过 CRPManagerDelegate 的 receiveSleepList 触发)
    /// - Parameter list: GoMore睡眠记录数组 (CRPGoMoreSleepRecord类型)
    func didReceiveGoMoreSleepList(list: [CRPGoMoreSleepRecord])

    /// 获取GoMore睡眠详情结果回调
    /// - Parameters:
    ///   - detail: GoMore睡眠详情数据模型 (例如 CRPGoMoreSleepDetailModel)，若获取失败则为nil
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetGoMoreSleepDataDetail(detail: CRPSmartRing.CRPGoMoreSleepDataModel?, error: CRPError?)

    /// 获取GoMore睡眠分段数据结果回调
    /// - Parameters:
    ///   - segmentationData: GoMore睡眠分段数据模型 (例如 CRPGoMoreSleepSegmentationModel)，若获取失败则为nil
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetGoMoreSleepSegmentationData(segmentationData: CRPSmartRing.CRPGoMoreSleepRecordModel?, error: CRPError?)

    /// 获取GoMore睡眠类型输出结果回调
    /// - Parameters:
    ///   - sleepType: GoMore睡眠类型数据模型 (例如 CRPGoMoreSleepTypeModel)，若获取失败则为nil
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetGoMoreSleepType(sleepType: CRPSmartRing.CRPGoMoreSleepType?, error: CRPError?)

    /// 结束睡眠获取算法输出结果回调
    /// - Parameters:
    ///   - status: 状态码 (00: 失败, 01: 成功, FF: 未睡眠)
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didSetTerminationSleep(status: Int, error: CRPError?)

    /// 获取摇一摇拍照开关状态结果回调
    /// - Parameters:
    ///   - isOn: 开关状态
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetShakeToTakePhotoStatus(isOn: Bool, error: CRPError?)

    /// 获取蓝牙传输功率结果回调
    /// - Parameters:
    ///   - level: 功率级别
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetTransmitPower(level: Int, error: CRPError?)

    /// 获取屏幕时间结果回调
    /// - Parameters:
    ///   - time: 屏幕显示时间 (秒)
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetAutoLockTime(time: Int, error: CRPError?)

    /// 获取屏幕亮度结果回调
    /// - Parameters:
    ///   - level: 亮度级别
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetScreenBrightness(level: Int, error: CRPError?)

    /// 获取屏幕支持的显示内容结果回调
    /// - Parameters:
    ///   - supportedTypes: 支持的内容类型数组 (Int数组，代表CRPDisplayType枚举原始值)
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetDisplaySupportInfo(supportedTypes: [Int]?, error: CRPError?)

    /// 获取屏幕内容设置结果回调
    /// - Parameters:
    ///   - displayTypes: 当前屏幕显示内容类型数组 (Int数组，代表CRPDisplayType枚举原始值)
    ///   - error: 错误信息，若成功则为nil (CRPError类型)
    func didGetDisplayContent(displayTypes: [Int]?, error: CRPError?)

    // MARK: - 心率相关回调
    /// 获取定时心率测量间隔结果回调
    /// - Parameters:
    ///   - interval: 测量间隔（分钟）
    ///   - error: 错误信息，若成功则为nil
    func didGetTimingHeartRateInterval(interval: Int, error: CRPError?)
    
    /// 获取心率记录数据结果回调
    /// - Parameters:
    ///   - data: 心率记录数据
    ///   - error: 错误信息，若成功则为nil
    func didGetHeartRateRecordData(data: [CRPSmartRing.CRPHeartRecordModel]?, error: CRPError?)
    
    // MARK: - HRV相关回调
    /// 获取定时HRV测量间隔结果回调
    /// - Parameters:
    ///   - interval: 测量间隔（分钟）
    ///   - error: 错误信息，若成功则为nil
    func didGetTimingHRVInterval(interval: Int, error: CRPError?)
    
    /// 获取HRV记录数据结果回调
    /// - Parameters:
    ///   - datas: HRV记录数据
    ///   - error: 错误信息，若成功则为nil
    func didGetHRVRecord(datas: [CRPSmartRing.CRPHRVRecordModel], error: CRPError?)
    
    // MARK: - 压力相关回调
    /// 获取压力记录数据结果回调
    /// - Parameters:
    ///   - datas: 压力记录数据
    ///   - error: 错误信息，若成功则为nil
    func didGetStressRecord(datas: [CRPSmartRing.CRPStressRecordModel], error: CRPError?)
    
    // MARK: - 血氧相关回调
    /// 获取定时血氧测量间隔结果回调
    /// - Parameters:
    ///   - interval: 测量间隔（分钟）
    ///   - error: 错误信息，若成功则为nil
    func didGetTimingO2Interval(interval: Int, error: CRPError?)
    
    /// 获取血氧记录数据结果回调
    /// - Parameters:
    ///   - data: 血氧记录数据
    ///   - error: 错误信息，若成功则为nil
    func didGetO2RecordData(datas: [CRPSmartRing.CRPO2RecordModel], error: CRPError?)
    
    /// 接收到久坐提醒
    /// - Parameters:
    ///   - isReminderNeeded: App是否需要被提醒 (true: 需要, false: 不需要)
    ///   - wearState: 佩戴状态 (仅在调用 setActivityReminderReach 时返回)
    ///   - time: 时间 (仅在调用 setActivityReminderReach 时返回)
    ///   - steps: 步数 (仅在调用 setActivityReminderReach 时返回)
    func didReceiveActivityReminder(isReminderNeeded: Bool, wearState: Int, time: Int, steps: Int)
}

// MARK: - CRPSmartRingManage Class
class CRPSmartRingManage: NSObject {
//    @EnvironmentObject var notificationManager: LocalNotificationManager
    static let shared = CRPSmartRingManage()

    private let smartRing = CRPSmartRingSDK.sharedInstance

    weak var delegate: CRPSmartRingManageDelegate?
    
    var deviceService: WindRingDeviceService!
    
    var currentCRPDiscovery:CRPDiscovery?
    
//    var gomoreSleepIds: [Int] = []

    private override init() {
        super.init()
        smartRing.delegate = self
        deviceService = WindRingDeviceService.shared
        #if DEBUG
        smartRing.showLog = false
        #endif
        let connecteds = smartRing.connectedPeripherial()
        currentCRPDiscovery = smartRing.currentCRPDiscovery
        ///初始化默认进行一次扫描 第一次扫描固定时会失败的，sdk的问题
        scanForPeripherals()
    }

    // MARK: - 4. 戒指扫描和连接 (根据文档章节)
    
    /// 扫描蓝牙设备
    /// - Parameters:
    ///   - duration: 扫描时长 (秒)
    ///   - progressHandler: 进度回调，每发现一个设备就会调用一次
    ///   - completionHandler: 完成回调，扫描结束后调用，返回所有发现的设备
    func scanForPeripherals(duration: TimeInterval = 10,
                            progressHandler: CRPSmartRing.scanProgressHandler? = nil, 
                            completionHandler: CRPSmartRing.scanCompletionHandler? = nil) {
        smartRing.scan(duration, progressHandler: progressHandler) { (discoveries, error) in
//            [weak self]
//            if error == .none { // 用户指定的错误处理逻辑
                print("扫描结果: error为:\(error)，发现 \(discoveries?.count ?? 0) 个戒指设备")
//                self?.delegate?.didFailToScanPeripherals(error: error)
//            } else {
//                // error 为 nil 的情况
//                print("扫描完成（error为nil），发现 \(discoveries?.count ?? 0) 个戒指设备")
//                if let discoveries = discoveries, !discoveries.isEmpty {
//                    self?.delegate?.didDiscoverPeripherals(peripherals: discoveries)
//                } else {
//                    // error 为 nil 但未发现设备
//                    self?.delegate?.didFailToScanPeripherals(error: .other) //  可以定义一个 .noDeviceFound 类型的错误
//                }
//            }
            completionHandler?(discoveries, error) // 确保原始回调也被调用
        }
    }

    ///取消扫描
    ///可以随时中断正在进行的戒指扫描过程。
    func interruptScan() {
        smartRing.interruptScan()
    }

    /// 连接指定的设备
    /// - Parameter peripheral: 通过扫描发现的 CRPDiscovery 对象
    func connect(peripheral: CRPDiscovery) {
        smartRing.connet(peripheral) // 使用 connect(discovery:)
    }
    
    ///如需获取系统中已连接的设备（原始CBPeripheral），可以使用connectedPeripheral()方法。如果需要连接此设备，则需要使用CBPeripheral创建CRPDiscovery，然后调用连接方法。
//    func getConnectedSystemPeripherals() -> [CBPeripheral]? {
//         return smartRing.connectedPeripheral()
//    }
    
    /// 解绑并断开连接 (调用 SDK 的 remove 方法)
    func disconnectAndRemoveBinding(handler: @escaping CRPSmartRing.removeHandler) {
         smartRing.remove(handler)
    }
    ///设备佩戴
    func getWearState() {
         smartRing.getWearState()
    }
    
    ///文档有写，但是sdk没有这个方法
    func reconnect() {
        smartRing.reConnet()
    }
    
    /// 检查当前是否连接
//    func isDeviceConnected() -> Bool {
//        return smartRing.isConnected()
//    }

    // MARK: - 5. 戒指交互
    
    // MARK: 5.1 时间同步
    /// 保持戒指与App端时间的一致性
    func syncTimeToRing() {
        smartRing.setTime()
    }

    // MARK: 5.2 固件升级
    /// 5.2.1 查询固件版本
    func getRingFirmwareVersion(handler: @escaping CRPSmartRing.stringHandler) {
        smartRing.getSoftver(handler)
//        { value, error in
//            
//        }
    }

    /// 5.2.2 获取戒指MAC地址
    func getRingMacAddress(handler: @escaping CRPSmartRing.stringHandler) {
        smartRing.getMac(handler)
    }

    /// 5.2.3 检查新固件
    func checkLatestFirmware(currentVersion: String, macAddress: String, handler: @escaping CRPSmartRing.versionHandler) {
        smartRing.checkLatest(currentVersion, macAddress, handler: handler)
    }

    /// 5.2.4 固件升级 - Goodix固件升级
    func startGoodixFirmwareUpgrade(zipPath: String) {
        smartRing.startGoodixUpgradeFromFile(zipPath: zipPath)
    }

    /// 5.2.4 固件升级 - 中科蓝讯固件升级
    func startBluetrumFirmwareUpgrade(filePath: String) {
        smartRing.startUpgradeFromFile(path: filePath)
    }

    /// 5.2.4 固件升级 - 汇顶5331固件升级
    func startGoodixBootloaderFirmwareUpgrade(zipPath: String) {
        smartRing.startBootLoaderUpgrade(zipPath: zipPath)
    }

    /// 5.2.5 中断升级
    func stopFirmwareUpgrade() {
        smartRing.stopUpgrade()
    }

    // MARK: 5.3 电量查询
    /// 查询戒指当前的电池电量
    func getRingBatteryLevel(handler: @escaping CRPSmartRing.intHandler) {
        
//        smartRing.getBattery(handler)
        smartRing.getBattery { value, error in
            handler(value, error)
            if error == .none, value < 5{
                LocalNotificationManager.shared.BatteryReminderNotification()
            }
        }
    }

    // MARK: 5.4 用户信息管理
    /// 5.4.1 获取用户信息
    func getUserProfile(handler: @escaping CRPSmartRing.profileHandler) {
        smartRing.getUserinfo(handler)//getUserInfo(handler)
    }

    /// 5.4.2 设置用户信息
    func setUserProfile(_ userInfo: CRPUserInfoModel) {
        smartRing.setUserinfo(userInfo)//setUserInfo(userInfo)
    }
    
    func getGitHashInfo(handler: @escaping CRPSmartRing.stringHandler) {
        smartRing.getGitHashInfo(handler: handler)//setUserInfo(userInfo)
    }
    
    
    // MARK: 5.21.1 获取戒指信息配置
    /// 获取戒指的配置信息，包括颜色、尺寸和类型。
    func getRingConfigurationInfo(handler: @escaping CRPSmartRing.ringInfoHandler) {
         smartRing.getRingInfo(handler: handler)//getRingInfo(handler)
    }

    // MARK: - 5.5 活动步数 (根据文档添加)
    
    /// 5.5.1 同步当前活动步数
    /// 同步戒指当前的活动步数数据，同步结果将通过代理方法`receiveSteps`返回。
    func syncCurrentActivitySteps() {
        smartRing.getSteps()
    }

    /// 获取历史运动统计数据
    /// - Parameters:
    ///   - day: 日期
    ///   - handler: 回调
    func getHistoricalExerciseStats(_ day: Date, handler: @escaping (Result<StepModel, Error>) -> Void) {
        
        // 计算日期差值（用于SDK调用）
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let targetDate = calendar.startOfDay(for: day)
        let components = calendar.dateComponents([.day], from: targetDate, to: today)
        let dayOffset = components.day ?? 0
        
        // 首先尝试从设备获取数据
        smartRing.getTrainingData(dayOffset, {model,error in 
            if error == .none {
                if model.day == dayOffset {
                    let step = StepModel.init(steps: model.step, distance: model.distance, calory: model.cal / 10000, time: model.step / 120)
                    PlistManager.shared.save(data: step, for: .training, date: Date.date(daysAgo: model.day))
                    handler(.success(step))
                }else{
                    debugPrint("回调结果日期偏移：\(model.day),实际日期\(day.string()),请求的偏移\(dayOffset)")
                }
                
            }else{
                print("❌ 从设备获取训练数据失败: \(error)")
                // 从数据库获取数据
                if let sleepData: StepModel = PlistManager.shared.get(for: .training, date: day, as: StepModel.self) {
                    print("✅ 获取训练数据成功")
                    handler(.success(sleepData))
                }else{
                    print("❌ 获取训练数据失败")
                    let error = NSError(domain: "error", code: 400)
                    handler(.failure(error))
                }
            }
        })
        
    }

    /// 5.5.3 获取半小时步数统计数据
    /// 获取指定日期的半小时步数统计数据，每天包含48个值，每半小时一个。
    /// - Parameters:
    ///   - day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
    ///   - handler: 回调函数，返回全天步数记录数据 (CRPTimingStepsRecordModel)
    func getHalfHourlyStepStats(day: Date, completion: @escaping ([ActivityDetailData]?, Error?) -> Void) {
        
        // 计算日期差值（用于SDK调用）
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let targetDate = calendar.startOfDay(for: day)
        let components = calendar.dateComponents([.day], from: targetDate, to: today)
        let dayOffset = components.day ?? 0
        smartRing.getStepArchiveData(dayOffset) { record, error in
            if error == .none{
                // 验证返回的数据是否匹配请求的日期，通过检查record.day是否等于请求的day
                if record.day != day.daysAgoFromToday() {
                    print("⚠️ 警告: SDK返回的day值(\(record.day))与请求的day参数(\(day))不匹配！")
                }
                
                // 获取对应日期
                let startOfDay = Calendar.current.startOfDay(for: day)
                
                var stepDetails: [ActivityDetailData] = []
                
                // 处理半小时步数数据 (一天48个半小时)
                // record包含该日期的半小时步数统计数据，每天48个值
                print("📊 步数明细数据长度: \(record.steps.count)，非零数据点: \(record.steps.filter { $0 > 0 }.count)")
                
                if !record.steps.isEmpty {
                    for (index, stepCount) in record.steps.enumerated() {
                        // 只添加有步数的记录
                        if stepCount > 0 {
                            // 计算该半小时对应的时间戳
                            // 每个index代表30分钟，从0点开始
                            let halfHourInSeconds = TimeInterval(index * 30 * 60)
                            let timestamp = startOfDay.addingTimeInterval(halfHourInSeconds)
                            let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
                            
                            // 创建步数明细记录
                            let stepDetail = ActivityDetailData(steps: stepCount, time: timeMs)
                            
                            // 打印明细数据用于调试
                            let timeFormatter = DateFormatter()
                            timeFormatter.dateFormat = "HH:mm"
                            print("  - 索引\(index): \(timeFormatter.string(from: timestamp)) 步数: \(stepCount)")
                            
                            stepDetails.append(stepDetail)
                        }
                    }
                } else {
                    // 如果没有步数数据或数组为空，返回空数组
                    print("⚠️ 步数数组为空")
                    completion([], nil)
                    return
                }
                
                // 如果没有有效步数数据，返回空数组
                if stepDetails.isEmpty {
                    print("⚠️ 没有有效的步数数据记录")
                    completion([], nil)
                    return
                }
                PlistManager.shared.saveArray(for: .stepArchive, date: day, dataArray: stepDetails)
                // 按时间排序
                let sortedStepDetails = stepDetails.sorted { $0.time < $1.time }
                
                // 返回结果
                print("✅ 成功获取  (day=\(day)) 的步数明细数据，共\(sortedStepDetails.count)条记录")
                completion(sortedStepDetails, nil)
                
            }else{
                print("❌ 从设备获取训练数据失败: \(error)")
                // 从数据库获取数据
                if let list: [ActivityDetailData] = PlistManager.shared.getArray(for: .activity, date: day, as: ActivityDetailData.self){
                    print("✅ 获取训练数据成功")
                    completion(list,nil)
                }else{
                    let error = NSError(domain: "error", code: 400)
                    completion([],error)
                }
            }
        }
        
        
        
    }

    /// 5.5.4 获取每分钟步数统计数据
    /// 获取每分钟的步数数据，一天共1440个值。
    /// - Parameter handler: 回调函数，返回处理后的数据 ([Int])
    func getPerMinuteStepStats(handler: @escaping CRPSmartRing.intsHandler) {
        smartRing.getStepsPerMin(handler)//getStepsPerMin(handler)
    }

    // MARK: - 5.6 睡眠监测 (根据文档添加)
    
    /// 5.6.1 获取睡眠数据
    /// 获取指定日期的睡眠数据，包括睡眠时长、深睡眠、浅睡眠等信息。
    /// - Parameters:
    ///   - day: 指定日期(0:今天，1:昨天，2:前天，以此类推)
    ///   - handler: 回调函数，返回睡眠记录数据 (CRPSleepRecordModel)
    func getSleepDataForDay(date: Date,completion: @escaping (SleepRecordModel?, Error?) -> Void) {
        let day = date.daysAgoFromToday() ?? 0
        smartRing.getSleepData(day) { model, error in
            if error != .none {
                print("❌ 从设备获取训练数据失败: \(error)")
                // 从数据库获取数据
                if let model = PlistManager.shared.get(for: .sleepRecord, date: date, as: SleepRecordModel.self){
                    print("✅ 获取训练数据成功")
                    completion(model,nil)
                }else{
                    let error = NSError(domain: "error", code: 400)
                    completion(nil,error)
                }
                return
            }
            // 计算该时间点对应的时间戳
            // 每个index代表一个时间点，从0点开始
            let dataModel = SleepRecordModel.init(day: model.day, deep: model.deep, light: model.light, rem: model.rem, detail: model.detail)
            
            PlistManager.shared.save(data: dataModel, for: .sleepRecord, date: date)
            completion(dataModel, nil)
        }

    }

    // MARK: - 5.7 运动目标 (根据文档添加)
    
    /// 5.7.1 获取日常运动目标
    /// 获取当前设置的日常运动目标，如步数、里程、卡路里和运动时间目标。
    /// - Parameter handler: 回调函数，返回日常运动目标数据 (CRPTrainingGoalsModel)
    func getCurrentNormalTrainingGoals(handler: @escaping CRPSmartRing.normalexerciseGoalDateHandler) {
        smartRing.getNormalTrainingGoal(handler)
    }

    /// 5.7.2 设置日常运动目标
    /// 设置日常运动目标，包括步数、里程、卡路里和运动时间目标。
    /// - Parameter goals: 日常运动目标模型 (CRPTrainingGoalsModel)
    func setCurrentNormalTrainingGoals(goals: CRPTrainingGoalsModel) {
        smartRing.setNormalTrainingGoal(goals)
    }

    /// 5.7.3 获取锻炼日目标
    /// 获取特定锻炼日的运动目标设置。
    /// - Parameter handler: 回调函数，返回锻炼日目标数据 (CRPTrainingGoalStateModel, CRPTrainingGoalsModel)
    /// 注意：文档中 handler 未明确指出返回类型，CRPManager 中对应的是 exerciseDayGoalHandler，它有两个参数。
    /// CRPSmartRing.exerciseGoalDataHandler 可能需要包含两个模型。
    func getExerciseDayGoals(handler: @escaping CRPSmartRing.exerciseGoalDateHandler) {
        smartRing.getTrainingDayGoal(handler)
    }

    /// 5.7.4 设置锻炼日目标
    /// 设置特定锻炼日的运动目标和相关配置。
    /// - Parameters:
    ///   - model: 锻炼目标状态模型 (CRPTrainingGoalStateModel)，包含开关状态和训练日设置
    ///   - goals: 锻炼目标模型 (CRPTrainingGoalsModel)，包含具体的目标值
    func setExerciseDayGoals(model: CRPTrainingGoalStateModel, goals: CRPTrainingGoalsModel) {
        smartRing.setTrainingDayGoal(model, goals)
    }

    // MARK: - 5.8 久坐提醒

    /// 5.8.1 获取久坐提醒设置
    ///
    /// 获取当前的久坐提醒设置，包括开关状态、提醒周期、步数阈值和时间范围。
    /// - Parameter handler: 回调函数，返回久- 坐提醒设置 (`CRPActivityReminderModel`)
    func getActivityReminderInfo(completion: @escaping (ActivityReminderModel?, Error?) -> Void)  {
        smartRing.getActivityReminderInfo { sitRemind, error in
            if error == .none {
                let model = ActivityReminderModel.init(open: sitRemind.open, period: sitRemind.period, steps: sitRemind.steps, startHour: sitRemind.startHour, endHour: sitRemind.endHour)
                completion(model,nil)
            }else{
                let error = NSError(domain: "error", code: 400)
                completion(nil,error)
            }
        }
//        smartRing.getActivityReminderInfo(handler)
    }

    /// 5.8.2 设置久坐提醒
    ///
    /// 配置久坐提醒功能，设置开关状态、提醒周期、步数阈值和时间范围。
    /// - Parameter activityReminder: 久坐提醒设置模型 (`CRPActivityReminderModel`)
    func setActivityReminder(_ activityReminder: ActivityReminderModel) {
        let model = CRPActivityReminderModel.init(open: activityReminder.open, period: activityReminder.period ?? 60, steps: activityReminder.steps ?? 50, startHour: activityReminder.startHour ?? 10, endHour: activityReminder.endHour ?? 22)
        smartRing.setActivityReminder(model)
    }

    // MARK: - 7. 最新功能详解

    // MARK: 7.1 GoMore睡眠算法功能
    /// 7.1.1 获取GoMore睡眠数据列表
    /// 获取GoMore算法处理的睡眠数据列表，结果通过代理方法`didReceiveGoMoreSleepList`回调返回 (由CRPManagerDelegate的receiveSleepList触发)。
    func getGoMoreSleepDataList() {
        smartRing.getGoMoreSleepDataList()
    }
    ///检查固件是否支持 GoMore 算法。
    ///类型是 GoMore 密钥的类型。不同类型的密钥需要不同的授权 ID 和密钥。
    func getGoMoreAlgorithmSupport(_ handler: @escaping CRPSmartRing.gomoreAlgorithmSupportHandler) {
        smartRing.getGoMoreAlgorithmSupport(handler)
    }
    ///查询固件GoMore算法的密钥是否已匹配
    func getGoMoreKeySupport(_ handler: @escaping CRPSmartRing.boolHandler) {
        smartRing.getGoMoreKeySupport(handler)
    }
    ///设置固件 GoMore 密钥
    func getGoMoreKeySupport(key: String, _ handler: @escaping CRPSmartRing.boolHandler) {
        smartRing.getGoMoreKeySupport(key: key, handler)
    }
    ///查询固件GoMore的ChipID
    func getGoMoreChipID(_ handler: @escaping CRPSmartRing.stringHandler) {
        smartRing.getGoMoreChipID(handler)
    }
    
    
    
    /// 7.1.2 获取GoMore睡眠详情
    /// 根据睡眠记录ID获取详细的睡眠数据。
    /// - Parameters:
    ///   - id: 睡眠记录ID
    ///   - handler: 回调函数，返回详细的睡眠数据 (假设为 CRPGoMoreSleepDetailModel)
    func getGoMoreSleepDataDetail(id: Int, handler: @escaping CRPSmartRing.goMoreSleepDataHandler) {
        // 假设 CRPSmartRing.goMoreSleepDataHandler 是 (CRPGoMoreSleepDetailModel?, CRPError?) -> Void
        smartRing.getGoMoreSleepDataDetail(id: id) { [weak self] (detail, error) in
            self?.delegate?.didGetGoMoreSleepDataDetail(detail: detail, error: error)
            handler(detail, error)
        }
    }

    /// 7.1.3 获取GoMore睡眠分段数据
    /// 获取根据ID的四段睡眠数据，仅适用于长睡眠类型，不适用于短睡眠类型。
    /// - Parameters:
    ///   - id: 睡眠记录ID
    ///   - handler: 回调函数，返回睡眠分段数据 (假设为 CRPGoMoreSleepSegmentationModel)
    func getGoMoreSleepSegmentationData(id: Int,completion: @escaping (SleepRecordModel?, Error?) -> Void){
        // 假设 CRPSmartRing.goMoreSleepRecordHandler 是 (CRPGoMoreSleepSegmentationModel?, CRPError?) -> Void
        smartRing.getGoMoreSleepSegmentationData(id: id) { (segmentationData, error) in
            do {
                if error == .none{
                    let data = try JSONSerialization.data(withJSONObject: segmentationData.detail, options: [])
                    let detail = try CleanJSONDecoder().decode([SleepRecordDetailModel].self, from: data)
                    let awake = detail.filter{$0.type == 0}.reduce(0, { x, model in
                        let formatter = DateFormatter()
                        formatter.dateFormat = "H:mm"
                        
                        guard let startDate = formatter.date(from: model.start),
                              let endDate = formatter.date(from: model.end) else {
                            return x + 0
                        }
                        
                        // 以当天日期为基础
                        let calendar = Calendar.current
                        let today = calendar.startOfDay(for: Date())

                        let fullStart = calendar.date(bySettingHour: calendar.component(.hour, from: startDate),
                                                      minute: calendar.component(.minute, from: startDate),
                                                      second: 0,
                                                      of: today)!

                        var fullEnd = calendar.date(bySettingHour: calendar.component(.hour, from: endDate),
                                                    minute: calendar.component(.minute, from: endDate),
                                                    second: 0,
                                                    of: today)!

                        // 如果结束时间早于开始时间，则跨天，加一天
                        if fullEnd <= fullStart {
                            fullEnd = calendar.date(byAdding: .day, value: 1, to: fullEnd)!
                        }

                        let minutes = Int(fullEnd.timeIntervalSince(fullStart) / 60)
                        return x + minutes
                        
                   })
                    let segmentModel = SleepRecordModel.init(day: 1, deep: segmentationData.deep, light: segmentationData.light, rem: segmentationData.rem, detail: segmentationData.detail,awake: awake)
                    completion(segmentModel, nil)
                }else{
                    let error = NSError(domain: "error", code: 400)
                    completion(nil, nil)
                }
            }
            catch{
                let error = NSError(domain: "error", code: 400)
                completion(nil,error)
            }
            
//                SleepRecordDetailModel
            
//            self?.delegate?.didGetGoMoreSleepSegmentationData(segmentationData: segmentationData, error: error)
            
        }
    }

    /// 7.1.4 获取GoMore睡眠类型输出
    /// 获取睡眠时间类型输出结果。
    /// - Parameter handler: 回调函数 (假设为 CRPGoMoreSleepTypeModel)
    func getGoMoreSleepType(handler: @escaping CRPSmartRing.goMoreSleepTypeHandler) {
        // 假设 CRPSmartRing.goMoreSleepTypeHandler 是 (CRPGoMoreSleepTypeModel?, CRPError?) -> Void
        smartRing.getGoMoreSleepType { [weak self] (sleepType, error) in
            self?.delegate?.didGetGoMoreSleepType(sleepType: sleepType, error: error)
            handler(sleepType, error)
        }
    }

    /// 7.1.5 结束睡眠获取算法输出
    /// APP结束睡眠并获取算法输出结果（00: 失败 01: 成功 FF: 未睡眠）。
    /// - Parameter handler: 回调函数，返回状态码
    func setTerminationSleep(handler: @escaping CRPSmartRing.intHandler) {
        smartRing.setTerminationSleep { [weak self] (status, error) in
            self?.delegate?.didSetTerminationSleep(status: status, error: error)
            handler(status, error)
        }
    }

    // MARK: 7.2 血压测量
    /// 7.2.1 开始单次血压测量
    /// 启动单次血压测量，测量结果将通过代理方法`didReceiveBloodPressure`返回。
    func startSingleBloodPressureMeasurement() { // Renamed from setStartBP for clarity
        smartRing.setStartBP()
    }

    /// 7.2.2 结束单次血压测量
    /// 结束血压测量过程。测量结果为255或0表示血压测量被中断。
    func stopSingleBloodPressureMeasurement() { // Renamed from setStopBP for clarity
        smartRing.setStopBP()
    }

    // MARK: 7.3 首次连接提示
    /// 7.3.1 设置首次连接闪烁
    /// 设置首次连接时戒指闪烁提示。
    func setFirstConnectPromptFlash() { // Renamed from setFirstConnect for clarity
        smartRing.setFirstConnect()
    }

    // MARK: 7.4 拍照功能
    /// 7.4.1 设置摇一摇拍照开关
    /// 设置摇一摇拍照功能的开关状态。
    /// - Parameter open: 开关状态(true:开启，false:关闭)
    func setShakeToTakePhoto(open: Bool) {
        smartRing.setShakeToTakePhoto(open: open)
    }

    /// 7.4.2 获取摇一摇拍照开关状态
    /// 获取当前摇一摇拍照功能的开关状态。
    /// - Parameter handler: 回调函数，返回开关状态
    func getShakeToTakePhotoStatus(handler: @escaping CRPSmartRing.boolHandler) {
        smartRing.getShakeToTakePhoto { [weak self] (isOn, error) in
            self?.delegate?.didGetShakeToTakePhotoStatus(isOn: isOn, error: error)
            handler(isOn, error)
        }
    }

    // MARK: 7.5 蓝牙传输频率
    /// 7.5.1 设置蓝牙传输功率
    /// 设置蓝牙传输的功率级别。
    /// - Parameter level: 功率级别
    func setBluetoothTransmitPower(level: Int) { // Renamed from setTransmitPower for clarity
        smartRing.setTransmitPower(level: level)
    }

    /// 7.5.2 获取蓝牙传输功率
    /// 获取当前蓝牙传输的功率级别设置。
    /// - Parameter handler: 回调函数，返回功率级别
    func getBluetoothTransmitPower(handler: @escaping CRPSmartRing.intHandler) {
        smartRing.getTransmitPower { [weak self] (level, error) in
            self?.delegate?.didGetTransmitPower(level: level, error: error)
            handler(level, error)
        }
    }

    // MARK: 7.6 屏幕时间
    /// 7.6.1 设置屏幕时间
    /// 设置屏幕显示的时间长度。
    /// - Parameter time: 屏幕显示时间，单位为秒
    func setScreenAutoLockTime(time: Int) { // Renamed from setAutoLockTime for clarity
        smartRing.setAutoLockTime(time: time)
    }

    /// 7.6.2 获取屏幕时间
    /// 获取当前设置的屏幕显示时间。
    /// - Parameter handler: 回调函数，返回屏幕时间
    func getScreenAutoLockTime(handler: @escaping CRPSmartRing.intHandler) {
        smartRing.getAutoLockTime { [weak self] (time, error) in
            self?.delegate?.didGetAutoLockTime(time: time, error: error)
            handler(time, error)
        }
    }

    // MARK: 7.7 屏幕亮度
    /// 7.7.1 设置屏幕亮度
    /// 设置屏幕的亮度级别。
    /// - Parameter level: 亮度级别，数值越大亮度越高
    func setScreenBrightnessLevel(level: Int) { // Renamed from setScreenBrightness for clarity
        smartRing.setScreenBrightness(level: level)
    }

    /// 7.7.2 获取屏幕亮度
    /// 获取当前设置的屏幕亮度级别。
    /// - Parameter handler: 回调函数，返回亮度级别
    func getScreenBrightnessLevel(handler: @escaping CRPSmartRing.intHandler) {
        smartRing.getScreenBrightness { [weak self] (level, error) in
            self?.delegate?.didGetScreenBrightness(level: level, error: error)
            handler(level, error)
        }
    }

    // MARK: 7.8 屏幕内容设置
    /// 7.8.1 获取屏幕支持的显示内容
    /// 获取屏幕支持显示的内容类型列表。
    /// - Parameter handler: 回调函数，返回支持的内容类型数组 (Int数组)
    func getScreenDisplaySupportInfo(handler: @escaping CRPSmartRing.displayTypeHandler) {
        // 假设 CRPSmartRing.displayTypeHandler 是 ([Int]?, CRPError?) -> Void
        smartRing.getDisplaySupportInfo { [weak self] (supportedTypes, error) in
            self?.delegate?.didGetDisplaySupportInfo(supportedTypes: supportedTypes, error: error)
            handler(supportedTypes, error)
        }
    }

    /// 7.8.2 设置屏幕内容
    /// 设置屏幕显示的内容类型，类型为CRPDisplayType枚举的原始值数组。
    /// - Parameter types: 内容类型数组，包含CRPDisplayType枚举的原始值
    func setScreenDisplayContent(types: [Int]) {
        smartRing.setDisplayContent(types: types)
    }

    /// 7.8.3 获取屏幕内容设置
    /// 获取当前设置的屏幕显示内容类型。
    /// - Parameter handler: 回调函数，返回当前屏幕内容类型数组 (Int数组)
    func getScreenDisplayContent(handler: @escaping CRPSmartRing.displayTypeHandler) {
        smartRing.getDisplayContent { [weak self] (displayTypes, error) in
            self?.delegate?.didGetDisplayContent(displayTypes: displayTypes, error: error)
            handler(displayTypes, error)
        }
    }

    // MARK: - 心率相关功能
    
    /// 设置定时心率测量间隔
    /// - Parameter interval: 测量间隔（分钟）
    func setTimingHeartRateInterval(interval: Int) {
        smartRing.setTimingHeartRate(interval)
    }
    
    /// 获取定时心率测量间隔
    /// - Parameter handler: 回调函数，返回测量间隔（分钟）
    func getTimingHeartRateInterval(handler: @escaping CRPSmartRing.intHandler) {
        smartRing.getTimingHeartRateInterval { [weak self] (interval, error) in
            self?.delegate?.didGetTimingHeartRateInterval(interval: interval, error: error)
            handler(interval, error)
        }
    }
    ///获取日期的心率
    func getTimingHeartRate(day:Date,completion: @escaping (HeartRateModel?, Error?) -> Void) {
        // 计算日期差值（用于SDK调用）
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let targetDate = calendar.startOfDay(for: day)
        let components = calendar.dateComponents([.day], from: targetDate, to: today)
        let dayOffset = components.day ?? 0
        smartRing.getTimingHeartRate(dayOffset) { record, error in
            if error != .none {
                print("❌ 从设备获取训练数据失败: \(error)")
                // 从数据库获取数据
                if let model: HeartRateModel = PlistManager.shared.get(for: .timingHeartRate, date: day, as: HeartRateModel.self){
                    print("✅ 获取训练数据成功")
                    completion(model,nil)
                }else{
                    let error = NSError(domain: "error", code: 400)
                    completion(nil,error)
                }
                return
            }
            
            // 获取对应日期
            let startOfDay = calendar.startOfDay(for: targetDate)
            
            var heartRateDetails: [HeartRateData] = []
            
            // 处理心率数据 (一天288个五分钟数据)
            print("📊 心率明细数据长度: \(record.hearts.count)，非零数据点: \(record.hearts.filter { $0 > 0 }.count)")
            
            if !record.hearts.isEmpty {
                for (index, heartRate) in record.hearts.enumerated() {
                    // 只添加有效的心率记录 (大于0且在合理范围内的值)
                    if heartRate > 0 && heartRate < 250 {
                        // 计算该5分钟对应的时间戳
                        // 每个index代表5分钟，从0点开始
                        let fiveMinutesInSeconds = TimeInterval(index * 5 * 60)
                        let timestamp = startOfDay.addingTimeInterval(fiveMinutesInSeconds)
                        let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
                        
                        // 创建心率明细记录
                        let heartRateDetail = HeartRateData(hearts: heartRate, time: timeMs)
                        
                        // 打印明细数据用于调试
                        let timeFormatter = DateFormatter()
                        timeFormatter.dateFormat = "HH:mm"
                        print("  - 索引\(index): \(timeFormatter.string(from: timestamp)) 心率: \(heartRate)")
                        
                        heartRateDetails.append(heartRateDetail)
                    }
                }
            } else {
                // 如果没有心率数据或数组为空，返回空数组
                print("⚠️ 心率数组为空")
                completion(nil, nil)
                return
            }
            
            // 如果没有有效心率数据，返回空数组
            if heartRateDetails.isEmpty {
                print("⚠️ 没有有效的心率数据记录")
                completion(nil, nil)
                return
            }
            
            // 按时间排序
            let sortedHeartRateDetails = heartRateDetails.sorted { $0.time < $1.time }
            let heartRateModel = HeartRateModel.init(heartRate: sortedHeartRateDetails)
            PlistManager.shared.save(data: heartRateModel, for: .timingHeartRate, date: day)
            // 返回结果
            print("✅ 成功获取 \(day) (day=\(dayOffset)) 的心率明细数据，共\(sortedHeartRateDetails.count)条记录")
            completion(heartRateModel, nil)
        }
    }
    
    
    
    /// 开始单次心率测量
    /// 测量结果将通过代理方法`didReceiveHeartRate`返回
    func startSingleHeartRateMeasurement() {
        smartRing.setStartSingleHR()
    }
    
    /// 停止单次心率测量
    func stopSingleHeartRateMeasurement() {
        smartRing.setStopSingleHR()
    }
    
    /// 获取心率记录数据
    /// - Parameter handler: 回调函数，返回心率记录数据
    func getHeartRateRecordData(handler: @escaping CRPSmartRing.heartRecordDataHandler) {
        smartRing.getHeartRecordData { [weak self] (data, error) in
            self?.delegate?.didGetHeartRateRecordData(data: data, error: error)
            handler(data, error)
        }
    }

    // MARK: - HRV相关功能
    
    /// 设置定时HRV测量间隔
    /// - Parameter interval: 测量间隔（分钟）
    func setTimingHRVInterval(interval: Int) {
        smartRing.setTimingHRV(interval)
    }
    
    /// 获取定时HRV测量间隔
    /// - Parameter handler: 回调函数，返回测量间隔（分钟）
    func getTimingHRVInterval(handler: @escaping CRPSmartRing.intHandler) {
        smartRing.getTimingHRVInterval { [weak self] (interval, error) in
            self?.delegate?.didGetTimingHRVInterval(interval: interval, error: error)
            handler(interval, error)
        }
    }
    
    func getTimingHRV(date: Date,completion: @escaping (HRVDataModel?, Error?) -> Void) {
        let day = date.daysAgoFromToday() ?? 0
        let dateString = date.string()
        smartRing.getTimingHRV(day) { record, error in
            if error != .none {
                print("❌ 从设备获取训练数据失败: \(error)")
                // 从数据库获取数据
                if let model: HRVDataModel = PlistManager.shared.get(for: .timingHRV, date: date, as: HRVDataModel.self){
                    print("✅ 获取训练数据成功")
                    completion(model,nil)
                }else{
                    let error = NSError(domain: "error", code: 400)
                    completion(nil,error)
                }
                return
            }
            // 计算数据获取耗时
            let processingTime = Date().timeIntervalSince(Date())
            print("⏱️ HRV明细数据SDK调用耗时: \(String(format: "%.2f", processingTime))秒")
            
            if error != .none {
                print("❌ 获取第\(day)天(\(date.string()))HRV明细数据失败: \(error)")
                let nsError = NSError(domain: "RawDataUpload", code: 500,
                                     userInfo: [NSLocalizedDescriptionKey: "获取HRV明细数据失败: \(error)"])
                completion(nil, nsError)
                return
            }
            
            // 获取对应日期
            let startOfDay = Calendar.current.startOfDay(for: date)
            
            var hrvDetails: [HRVData] = []
            
            // 处理HRV数据
            print("📊 HRV明细数据长度: \(record.hrvs.count)，非零数据点: \(record.hrvs.filter { $0 > 0 }.count)")
            
            if !record.hrvs.isEmpty {
                for (index, hrv) in record.hrvs.enumerated() {
                    // 只添加有效的HRV记录 (大于0且在合理范围内的值)
                    if hrv > 0 && hrv < 150 {
                        // 计算该时间点对应的时间戳
                        // 每个index代表一个时间点，从0点开始
                        let timeIntervalInSeconds = TimeInterval(index * 5 * 60) // 假设每5分钟一个数据点
                        let timestamp = startOfDay.addingTimeInterval(timeIntervalInSeconds)
                        let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
                        
                        // 创建HRV明细记录
                        let hrvDetail = HRVData(hrv: hrv, time: timeMs)
                        
                        // 打印明细数据用于调试
                        let timeFormatter = DateFormatter()
                        timeFormatter.dateFormat = "HH:mm"
                        print("  - 索引\(index): \(timeFormatter.string(from: timestamp)) HRV: \(hrv)")
                        
                        hrvDetails.append(hrvDetail)
                    }
                }
            } else {
                // 如果没有HRV数据或数组为空，返回空数组
                print("⚠️ HRV数组为空")
                completion(nil, nil)
                return
            }
            
            // 如果没有有效HRV数据，返回空数组
            if hrvDetails.isEmpty {
                print("⚠️ 没有有效的HRV数据记录")
                completion(nil, nil)
                return
            }
            
            // 按时间排序
            let sortedHRVDetails = hrvDetails.sorted { $0.time < $1.time }
            let hrvDataModel = HRVDataModel.init(heartRate: sortedHRVDetails)
            PlistManager.shared.save(data: hrvDataModel, for: .timingHRV, date: date)
            // 返回结果
            print("✅ 成功获取 \(dateString) (day=\(day)) 的HRV明细数据，共\(sortedHRVDetails.count)条记录")
            completion(hrvDataModel, nil)
        }
    }
    
    
    /// 开始HRV测量
    /// 测量结果将通过代理方法`didReceiveHRV`返回
    func startHRVMeasurement() {
        smartRing.setStartHRV()
    }
    
    /// 停止HRV测量
    func stopHRVMeasurement() {
        smartRing.setStopHRV()
    }
    
    /// 获取HRV记录数据
    /// - Parameter handler: 回调函数，返回HRV记录数据
    func getHRVRecord(handler: @escaping CRPSmartRing.hrvRecordDataHandler) {
        smartRing.getHRVRecord { [weak self] (data, error) in
            self?.delegate?.didGetHRVRecord(datas: data, error: error)
            handler(data, error)
        }
    }

    // MARK: - 压力相关功能
    
    /// 开始压力测量
    /// 测量结果将通过代理方法`didReceiveStress`返回
    func startStressMeasurement() {
        debugPrint("开始测量压力")
        smartRing.setStartStress()
    }
    
    /// 停止压力测量
    func stopStressMeasurement() {
        smartRing.setStopStress()
    }
    
    /// 获取压力记录数据
    /// - Parameter handler: 回调函数，返回压力记录数据
    func getStressRecord(handler: @escaping CRPSmartRing.stressRecordDataHandler) {
        smartRing.getStressRecord { [weak self] (data, error) in
            self?.delegate?.didGetStressRecord(datas: data, error: error)
            handler(data, error)
        }
    }

    // MARK: - 血氧相关功能
    
    /// 设置定时血氧测量间隔
    /// - Parameter interval: 测量间隔（分钟）
    func setTimingO2Interval(interval: Int) {
        smartRing.setTimingO2(interval)
    }
    
    func setSleepTemperatureState(open: Bool) {
//        smartRing.setTerminationSleep(<#T##handler: intHandler##intHandler##(_ value: Int, _ error: CRPError) -> Void#>)//setSleepTemperatureState(open: open)
    }
    
    
    
    /// 获取定时血氧测量间隔
    /// - Parameter handler: 回调函数，返回测量间隔（分钟）
    func getTimingO2Interval(handler: @escaping CRPSmartRing.intHandler) {
        smartRing.getTimingO2Interval { [weak self] (interval, error) in
            self?.delegate?.didGetTimingO2Interval(interval: interval, error: error)
            handler(interval, error)
        }
    }
    func getTimingO2(date: Date,completion: @escaping (TimingO2RecordDataModel?, Error?) -> Void) {
        let day = date.daysAgoFromToday() ?? 0
        smartRing.getTimingO2(day) { records, error in
            if error != .none {
                print("❌ 从设备获取训练数据失败: \(error)")
                // 从数据库获取数据
                if let model = PlistManager.shared.get(for: .timingO2Record, date: date, as: TimingO2RecordDataModel.self){
                    print("✅ 获取训练数据成功")
                    completion(model,nil)
                }else{
                    let error = NSError(domain: "error", code: 400)
                    completion(nil,error)
                }
                return
            }

            // 计算该时间点对应的时间戳
            // 每个index代表一个时间点，从0点开始
            let o2Records =  records.o2s.enumerated().map { index,element in
                let timeIntervalInSeconds = TimeInterval(index * 5 * 60) // 假设每5分钟一个数据点
                let timestamp = date.addingTimeInterval(timeIntervalInSeconds)
                let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
                // 创建HRV明细记录
                let o2Record = TimingO2RecordModel(o2: element, time: timeMs)
                return o2Record
            }
            let sortedDetails = o2Records.sorted { $0.time < $1.time }
            let dataModel = TimingO2RecordDataModel.init(timingO2Record: sortedDetails)
            PlistManager.shared.save(data: dataModel, for: .timingO2Record, date: date)
            completion(dataModel, nil)
        }
    }
    ///#### 5.18.3 获取睡眠体温数据
    ///获取指定日期的睡眠体温数据，包含整晚的体温变化记录。
    func getSleepTemperatureData(date: Date,completion: @escaping (SleepTemperatureDataModel?, Error?) -> Void) {
        let day = date.daysAgoFromToday() ?? 0
        smartRing.getTimingTemperatureData(day: day) { records, error in
            if error != .none {
                print("❌ 从设备获取训练数据失败: \(error)")
                // 从数据库获取数据
                if let model = PlistManager.shared.get(for: .sleepTemperature, date: date, as: SleepTemperatureDataModel.self){
                    print("✅ 获取训练数据成功")
                    completion(model,nil)
                }else{
                    let error = NSError(domain: "error", code: 400)
                    completion(nil,error)
                }
                return
            }
            // 计算该时间点对应的时间戳
            // 每个index代表一个时间点，从0点开始
            let o2Records =  records.tempeartures.enumerated().map { index,element in
                let timeIntervalInSeconds = TimeInterval(index * 5 * 60) // 假设每5分钟一个数据点
                let timestamp = date.addingTimeInterval(timeIntervalInSeconds)
                let timeMs = Int64(timestamp.timeIntervalSince1970 * 1000)
                // 创建HRV明细记录
                let o2Record = SleepTemperatureData(value: element, time: timeMs)
                return o2Record
            }
            let sortedDetails = o2Records.sorted { $0.time < $1.time }
            let dataModel = SleepTemperatureDataModel.init(sleepTemperatureData: sortedDetails)//(from: sortedDetails)
            PlistManager.shared.save(data: dataModel, for: .sleepTemperature, date: date)
            completion(dataModel, nil)
        }
//        smartRing.getSleepTemperatureData(day: day, handler: { records, error in
//            
//        })

    }
    
    
    
    
    /// 开始血氧测量
    /// 测量结果将通过代理方法`didReceiveSpO2`返回
    func startSpO2Measurement() {
        smartRing.setStartSpO2()
    }
    
    /// 停止血氧测量
    func stopSpO2Measurement() {
        smartRing.setStopSpO2()
    }
    
    ///开始体温测量
    func setStartTemperature() {
        smartRing.setStartTemperature()
    }
    ///停止体温测量
    func setStopTemperature() {
        smartRing.setStopTemperature()
    }
    
    /// 获取血氧记录数据
    /// - Parameter handler: 回调函数，返回血氧记录数据
    func getO2RecordData(handler: @escaping CRPSmartRing.o2RecordDataHandler) {
        smartRing.getO2RecordData { [weak self] (records, error) in
//            self?.delegate?.didGetO2RecordData(datas: data, error: error)
//            if error == .none && !records.isEmpty {
//                print("成功获取\(records.count)条血氧历史数据")
//                for record in records {
//                    // 如果血氧值在有效范围内，则保存
//                    if record.value >= 70 && record.value <= 100 {
//                        // 打印每条记录的详细信息（调试用）
//                        let timestamp = Date(timeIntervalSince1970: TimeInterval(record.time))
//                        let dateFormatter = DateFormatter()
//                        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
//                        print("保存血氧记录: 值=\(record.value), 时间=\(dateFormatter.string(from: timestamp))")
//                        
//                        // 使用健康数据管理器保存记录
//                        if healthDataManager.addBloodOxygen(userId: userId, value: record.value, timestamp: timestamp, deviceId: deviceId) {
//                            savedCount += 1
//                        } else {
//                            print("保存血氧记录失败: 值=\(record.value), 时间=\(dateFormatter.string(from: timestamp))")
//                        }
//                    }
//                }
//                // 将数据保存到本地数据库
//                self.saveBloodOxygenData(records: records) { savedCount in
//                    print("已保存\(savedCount)条新血氧数据到本地")
//                    DispatchQueue.main.async {
//                        completion(savedCount, nil)
//                        
//                        // 自动尝试上传
//                        if savedCount > 0 {
//                            self.uploadPendingBloodOxygenData()
//                        }
//                    }
//                }
            }
    }


    // MARK: - 5.16 Device Control
    
    /// 5.16.1 关机
    ///
    /// 使戒指关机。
    func shutdown(_ handler: @escaping CRPSmartRing.boolHandler) {
        smartRing.shutDown(handler)
    }
    
    /// 5.16.2 恢复出厂设置
    ///
    /// 将戒指恢复到出厂状态，清除所有用户数据和配置。
    func restoreFactory(_ handler: @escaping CRPSmartRing.boolHandler) {
        smartRing.reset(handler)
    }
    
    /// 5.16.3 重启设备
    ///
    /// 重启戒指设备。
    func reboot() {
        smartRing.reboot()
    }
    
    // MARK: - 6. 设备升级
    
    /// 6.1.1 检查固件更新
    ///
    // ... existing code ...

}

// MARK: - CRPManagerDelegate / CRPSmartRingSDKDelegate Implementation
extension CRPSmartRingManage: CRPManagerDelegate { 
    // 3.1 连接与状态
    func didState(_ state: CRPState) {
        print("[CRPSmartRingManage] Did State: \(state)")
        DispatchQueue.main.async { [weak self] in 
            guard let self = self else { return }
            
            print("📱 SDK连接状态变化: \(state.rawValue)-\(state)")
            // 获取当前连接设备的描述信息，用于日志
//            let deviceDesc = self.deviceService.currentDiscovery.map {
//                "\($0.kCABAdvidataLocalName ?? "未知设备") (\($0.mac ?? "未知MAC"))"
//            } ?? "未知设备"
            let device = CRPSmartRingManage.shared.smartRing.currentCRPDiscovery
            self.deviceService.deviceInfo?.peripheral = device
            switch state {
            case .connecting:
                self.deviceService.connectionState = .connecting
                print("📱 设备正在连接: \(device?.localName)")
                // 发送通知
                NotificationCenter.default.post(name: .deviceConnecting, object: nil)
                print("📱 已发送设备连接中通知: deviceConnecting")
            case .connected:
                ///连接成功切换相应的数据库
//                if let mac = self.deviceService.currentDiscovery?.mac,StorageManager.shared.switchToDatabase(for: mac) {
//                    // 设备连接成功，保存MAC地址并切换数据库
//                    StorageManager.shared.saveLastConnectedDevice(macAddress: mac)
//                    print("数据库切换成功")
//                }
                
                self.deviceService.connectionState = .connected
                self.deviceService.connectionRetryCount = 0 // 连接成功，重置重试计数
                print("📱 设备已连接: \(device?.localName)")
                
                self.deviceService.getMacAddress { mac in
                    // 连接成功后自动同步时间并获取基本信息
                    // 获取戒指信息
                    self.deviceService.getRingInfo { info in
                        // 发送通知
                        NotificationCenter.default.post(name: .deviceConnected, object: nil)
                        print("📱 已发送设备已连接通知: deviceConnected")
                        
                        // 同时发送与UserProfileView相关的通知
                        NotificationCenter.default.post(name: NSNotification.Name("DeviceConnectionStateChanged"), object: nil)
                        print("📱 已发送设备连接状态变化通知: DeviceConnectionStateChanged")
                        
                        self.deviceService.syncDeviceInfo()
                        
                        // 连接成功后设置定时心率测量(5分钟间隔)
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
                            guard let self = self else { return }
                            print("设置自动定时心率测量: 每5分钟")
                            
                            // 获取并打印当前设置的定时心率测量状态
                            self.deviceService.getTimingHeartRateMeasurementStatus { interval, error in
                                if let error = error {
                                    print("获取定时心率测量状态失败: \(error.localizedDescription)")
                                    self.deviceService.setTimingHeartRateMeasurement(interval: 1) // 1=5分钟间隔
                                } else if interval == 0 {
                                    print("当前定时心率测量已关闭")
                                    self.deviceService.setTimingHeartRateMeasurement(interval: 1) // 1=5分钟间隔
                                } else {
                                    if interval != 1{
                                        self.deviceService.setTimingHeartRateMeasurement(interval: 1) // 1=5分钟间隔
                                    }
                                    print("当前定时心率测量间隔: 每\(interval * 5)分钟")
                                }
                            }
                            self.deviceService.getTimingSpO2MeasurementStatus(completion:  { interval, error in
                                if let error = error {
                                    self.deviceService.setTimingSpO2Measurement(interval: 1) // 1=5分钟间隔
                                    print("获取定时SpO2测量状态失败: \(error.localizedDescription)")
                                } else if interval == 0 {
                                    print("当前定时SpO2测量已关闭")
                                    self.deviceService.setTimingSpO2Measurement(interval: 1) // 1=5分钟间隔
                                } else {
                                    if interval != 1{
                                        self.deviceService.setTimingSpO2Measurement(interval: 1) // 1=5分钟间隔
                                    }
                                    print("当前定时SpO2测量间隔: 每\(interval * 5)分钟")
                                }
                            })
                            self.deviceService.getTimingHRVMeasurementStatus(completion:  { interval, error in
                                if let error = error {
                                    self.deviceService.setTimingHRVMeasurement(interval: 1) // 1=5分钟间隔
                                    print("获取定时HRV测量状态失败: \(error.localizedDescription)")
                                } else if interval == 0 {
                                    self.deviceService.setTimingHRVMeasurement(interval: 1) // 1=5分钟间隔
                                    print("当前定时HRV测量已关闭")
                                } else {
                                    if interval != 1{
                                        self.deviceService.setTimingHRVMeasurement(interval: 1) // 1=5分钟间隔
                                    }
                                    print("当前定时HRV测量间隔: 每\(interval * 5)分钟")
                                }
                            })
                            
                        }
                    }
                }
                
            case .disconnected:
                let wasConnected = self.deviceService.connectionState.isConnected
                self.deviceService.connectionState = .disconnected
                print("📱 设备已断开: \(device?.localName), 之前状态是否为已连接: \(wasConnected)")
                
                // 如果是初始状态或未设置断开类型且之前是已连接状态，则认为是被动断开
                if self.deviceService.disconnectionType == .none && wasConnected {
                    self.deviceService.disconnectionType = .passive
                    print("📱 检测为被动断开连接")
                }
                
                // 发送通知
                NotificationCenter.default.post(name: .deviceDisconnected, object: nil)
                print("📱 已发送设备已断开通知: deviceDisconnected")
                
                // 同时发送与UserProfileView相关的通知
                NotificationCenter.default.post(name: NSNotification.Name("DeviceConnectionStateChanged"), object: nil)
                print("📱 已发送设备连接状态变化通知: DeviceConnectionStateChanged")
                
                // 如果是被动断开且自动重连开启，尝试重连
                if wasConnected && self.deviceService.autoReconnect && self.deviceService.disconnectionType == .passive {
                    // 延迟3秒再尝试重连，给设备一些恢复时间
                    print("📱 检测为被动断开且自动重连已开启，计划3秒后尝试自动重连...")
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
                        guard let self = self else { return }
                        self.deviceService.attemptAutoReconnect()
                    }
                } else if self.deviceService.disconnectionType == .manualUnbind {
                    print("📱 设备已主动解除绑定，不会尝试自动重连")
                    // 确保清空MAC地址
                    self.deviceService.lastConnectedDeviceMAC = nil
                }
                
            case .unbind:
                self.deviceService.connectionState = .disconnected
                print("设备解绑: \(device?.localName)")
                
            case .disconnecting:
                print("设备正在断开中: \(device?.localName)")
                // 不更改状态，等待断开完成
                
            case .syncing:
                print("设备正在同步数据: \(device?.localName)")
                // 不更改连接状态，保持为已连接
                
                // 发送通知
                NotificationCenter.default.post(name: .deviceSyncing, object: nil)
                
            case .syncSuccess:
                print("设备数据同步成功: \(device?.localName)")
                // 发送通知
                NotificationCenter.default.post(name: .deviceSyncCompleted, object: nil, userInfo: ["success": true])
                
                // 同步成功后，显示当前使用的睡眠算法
                let algorithmType = self.deviceService.sleepAlgorithmType.description
                print("设备同步完成，当前使用睡眠算法：\(algorithmType)")
                
                // 尝试获取并上传心率历史数据
                DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                    print("准备从设备获取心率历史数据并上传...")
                    HeartRateUploadService.shared.fetchAndSaveHeartRateHistory { count, error in
                        if let error = error {
                            print("获取心率历史数据失败: \(error.localizedDescription)")
                        } else {
                            print("成功获取并保存了\(count)条心率历史数据")
                        }
                    }
                }
                
            case .syncError:
                print("设备数据同步失败: \(device?.localName)")
                // 发送通知
                NotificationCenter.default.post(name: .deviceSyncCompleted, object: nil, userInfo: ["success": false])
                
            @unknown default:
                print("未知的设备连接状态: \(state.rawValue) - \(device?.localName)")
            }
        }
        
        delegate?.didUpdateState(state: state)
    }

    func didBluetoothState(_ state: CRPBluetoothState) {
        print("[CRPSmartRingManage] Did Bluetooth State: \(state)")
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            print("蓝牙状态变化: \(state)")
            
            switch state {
            case .poweredOn:
                print("蓝牙已开启")
                
                // 如果是被动断开的设备并且自动重连功能已启用，尝试重连
                if self.deviceService.disconnectionType == .passive || self.deviceService.disconnectionType == .systemRequest {
                    // 如果当前没有连接，且已保存了上次设备的MAC地址，尝试重连
                    if !self.deviceService.connectionState.isConnected && self.deviceService.autoReconnect {
                        print("蓝牙开启，尝试自动重连...")
                        // 延迟2秒尝试重连，确保蓝牙栈完全初始化
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
                            self?.deviceService.attemptAutoReconnect()
                        }
                    }
                }
                
                // 发送通知
                NotificationCenter.default.post(
                    name: Notification.Name(rawValue: "bluetoothStateChanged"),
                    object: nil,
                    userInfo: ["state": state.rawValue, "enabled": true]
                )
                
            case .poweredOff:
                print("蓝牙已关闭")
                
                // 蓝牙关闭时，强制断开当前连接
                if self.deviceService.connectionState.isConnected || self.deviceService.connectionState == .connecting {
                    // 记录当前断开是由系统蓝牙关闭导致的
                    self.deviceService.disconnectionType = .systemRequest
                    self.deviceService.connectionState = .disconnected
                    self.deviceService.currentDiscovery = nil
                    self.deviceService.currentPeripheral = nil
                    print("蓝牙关闭，设备连接已断开")
                }
                
                // 如果正在扫描，停止扫描
                if self.deviceService.isScanning {
                    self.deviceService.stopScan()
                }
                
                // 发送通知
                NotificationCenter.default.post(
                    name: Notification.Name(rawValue: "bluetoothStateChanged"),
                    object: nil,
                    userInfo: ["state": state.rawValue, "enabled": false]
                )
                
            default:
                print("其他蓝牙状态: \(state)")
                
                // 对于其他状态（如resetting、unauthorized等），如果设备已连接，强制断开连接
                if self.deviceService.connectionState.isConnected {
                    self.deviceService.disconnectionType = .systemRequest
                    self.deviceService.connectionState = .disconnected
                    print("蓝牙状态变化，设备连接已断开")
                }
                
                // 发送通知
                NotificationCenter.default.post(
                    name: Notification.Name(rawValue: "bluetoothStateChanged"),
                    object: nil,
                    userInfo: ["state": state.rawValue, "enabled": false]
                )
            }
        }
        
        delegate?.didUpdateBluetoothState(state: state)
    }
    
    // MARK: - 设备控制与事件回调
    /// 固件升级状态与进度回调
    /// - Parameters:
    ///   - state: OTA升级状态 (CRPOTAState类型)
    ///   - progress: 升级进度 (0-100)
    func receiveOTA(_ state: CRPOTAState, _ progress: Int) {
        print("[CRPSmartRingManage] Receive OTA State: \(state), Progress: \(progress)%")
        print("OTA更新状态: \(state), 进度: \(progress)%")
        
        switch state {
        case .uploading:
            print("固件升级中，进度: \(progress)%")
        case .completed:
            print("固件升级完成")
        case .failed:
            print("固件升级失败")
        @unknown default:
            print("未知的固件升级状态: \(state.rawValue)")
        }
        delegate?.didReceiveOTAState(state: state, progress: progress)
    }
    
    func receivePairState(_ state: CRPPairState) {
        print("[CRPSmartRingManage] Receive Pair State: \(state)")
        delegate?.didReceivePairState(state: state)
    }

    // 3.2 健康数据接收
    func receiveSteps(_ model: CRPStepModel) { 
        print("[CRPSmartRingManage] Receive Steps: \(model.steps)")
        print("接收到步数数据: 步数 \(model.steps), 距离 \(model.distance), 卡路里 \(model.calory)")
        
        // 发送通知
        NotificationCenter.default.post(
            name: .stepsUpdated,
            object: model
        )
        delegate?.didReceiveSteps(model: model)
    }

    func receiveHeartRate(_ heartRate: Int) {
//        DispatchQueue.main.async { [weak self] in
//            guard let self = self else { return }
            self.deviceService.lastHeartRate = heartRate
            self.deviceService.isMeasuringHeartRate = false
            print("接收到心率: \(heartRate)")
            DataSyncUploadService.shared.uploadHeartRateData(for: Date()) { result in
                // 发送通知
                NotificationCenter.default.post(
                    name: .heartRateDataUploaded,
                    object: nil,
                    userInfo: ["bool": result]
                )
                
            }// uploadHeartRateData
//            let requestBody: [String: Any] = [
//                "date": Date().string(),
//                "records": (heartRate.heartRate ).map { detail -> [String: Any] in
//                    return [
//                        "hearts": detail.hearts,
//                        "time": detail.time  // 毫秒时间戳
//                    ]
//                }
//            ]
//            self.sendHeartRateDataToServer(requestBody: requestBody) { bool, error in
//                if bool{
//                    if !date.isInToday{
//                        PlistManager.shared.markDataUploaded(for: .timingHeartRate, date: date)
//                    }
//                    
//                }
//            }
            
//        }
        delegate?.didReceiveHeartRate(heartRate: heartRate)
    }

    func receiveRealTimeHeartRate(_ heartRate: Int) {
        print("[CRPSmartRingManage] Receive RealTimeHeartRate: \(heartRate)")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.deviceService.lastHeartRate = heartRate
            print("接收到实时心率: \(heartRate)")
            
            // 发送通知
            NotificationCenter.default.post(
                name: .heartRateMeasured,
                object: nil,
                userInfo: ["value": heartRate, "isRealTime": true]
            )
        }
        delegate?.didReceiveRealTimeHeartRate(heartRate: heartRate)
    }

    func receiveHRV(_ hrv: Int) {
        print("[CRPSmartRingManage] Receive HRV: \(hrv)")
        delegate?.didReceiveHRV(hrv: hrv)
    }

    func receiveSpO2(_ o2: Int) {
        print("[CRPSmartRingManage] Receive SpO2: \(o2)")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.deviceService.lastBloodOxygen = o2
            self.deviceService.isMeasuringSpO2 = false
            print("接收到血氧: \(o2)%")
            
            // 发送通知
            NotificationCenter.default.post(
                name: .bloodOxygenMeasured,
                object: nil,
                userInfo: ["value": o2]
            )
            NotificationCenter.default.post(
                name: .spo2DataUploaded,
                object: nil,
                userInfo: ["value": o2]
            )
            
        }
        delegate?.didReceiveSpO2(o2: o2)
    }

    
    
    func receiveStress(_ stress: Int) {
        print("[CRPSmartRingManage] Receive Stress: \(stress)")
//        DispatchQueue.main.async { [weak self] in
//        guard let self = self else { return }
        self.deviceService.lastStress = stress
        self.deviceService.isMeasuringStress = false  // 收到压力值后，将测量状态设为false
        print("接收到压力值: \(stress)")
        let date = Date()
        var records:[StressRecord] = []
        ///查看今天有没有压力数据，有就增加
        if let recordModel = PlistManager.shared.get(for: .stressRecord, date:  date, as: StressRecordModel.self){
            records = recordModel.records
        }
        let time: Int64 = Int64(date.timeIntervalSince1970 * 1000)
        let record = StressRecord.init(stress: stress, time: time)
        records.append(record)
        let stressRecord:StressRecordModel =  StressRecordModel.init(records: records)
        // 保存压力数据到本地
        PlistManager.shared.save(data: stressRecord, for: .stressRecord, date: date)
        
        DataSyncUploadService.shared.uploadPendingStressData(date: date, recordModel: stressRecord) { bool, error in
            // 发送通知
            NotificationCenter.default.post(
                name: .stressDataUploaded,
                object: nil,
                userInfo: nil
            )
        }
        
        
        // 重新计算并更新当天的日平均压力值
//            if let averageStress = HealthDataManager.shared.calculateAndSaveDailyStress(userId: userId, date: Date()) {
//                print("已更新今日平均压力值: \(averageStress)，压力评分: \(100 - Int(averageStress.rounded()))")
//            }
        
        // 发送通知
        NotificationCenter.default.post(
            name: .stressMeasured,
            object: nil,
            userInfo: ["value": stress]
        )
//        }
        delegate?.didReceiveStress(stress: stress)
    }
    
    
    func receiveTouchType(_ model: CRPTouchModel) {
        print("[CRPSmartRingManage] Receive TouchType: \(model)")
        delegate?.didReceiveTouchType(model: model)
    }
    
    // 3.3 活动与训练数据
//    func receiveTrainingList(_ list: [CRPSportRecord]) {
//        print("[CRPSmartRingManage] Receive Training List: \(list.count) items")
//    }
    
    func receiveTrainingState(_ state: CRPTrainingType) { 
         print("[CRPSmartRingManage] Receive Training State: \(state)")
    }
    
    func receiveWearState(_ state: Int) {
        print("[CRPSmartRingManage] Receive Wear State: \(state)")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.deviceService.wearingState = state
            print("接收到佩戴状态: \(state == 1 ? "已佩戴" : "未佩戴")")
            
            // 发送通知
            NotificationCenter.default.post(
                name: .wearStateChanged,
                object: nil,
                userInfo: ["state": state]
            )
        }
        delegate?.didReceiveWearState(state: state)
    }
    
    func receiveDailyGoal(_ type: CRPTrainingGoalType, state: Int) {
        print("[CRPSmartRingManage] Receive Daily Goal: Type \(type), State \(state)")
    }

    func receiveActivityReminder(isReminder: Int, wearState: Int, time: Int, step: Int) {
        print("[CRPSmartRingManage] Receive Activity Reminder: isReminder \(isReminder), wearState \(wearState), time \(time), step \(step)")
        LocalNotificationManager.shared.SedentaryReminderNotification()
//        LocalNotificationManager.shared.scheduleNotification(
//                                title: "Sedentary reminder",
//                                body: "Sedentary sitting harms the body, cultivate good habits of activity",
//                                after: 5
//                            )

    }
    
    func receiveTrainingGoal(_ type: CRPTrainingGoalType, state: Int) {
        print("[CRPSmartRingManage] Receive Training Goal: Type \(type), State \(state)")
    }
    
    // 3.4 其他数据回调
    func receiveSOS() {
        print("[CRPSmartRingManage] Receive SOS Triggered")
        delegate?.didReceiveSOS()
    }
    
    func receiveMeditationList(_ list: [CRPMeditationRecord]) {
        print("[CRPSmartRingManage] Receive Meditation List: \(list.count) items")
    }
    
    func receiveMeditationState(_ state: CRPSmartRing.CRPMeditationGetState) {
        print("[CRPSmartRingManage] Receive Meditation State: \(state)")
        delegate?.didReceiveMeditationState(state: state.rawValue)
    }
    
//    func receiveSleepList(_ list: [CRPGoMoreSleepRecord]) {
//        print("[CRPSmartRingManage] Receive Sleep List (GoMore): \(list.count) items")
//        delegate?.didReceiveGoMoreSleepList(list: list)
//    }
    // 当接收到睡眠数据列表时调用
    func receiveSleepList(_ sleepRecords: [CRPGoMoreSleepRecord]) {
        print("接收到睡眠记录列表: \(sleepRecords.count)条记录")
        
        // 提取ID列表并存储到我们的属性中
//        self.gomoreSleepIds = sleepRecords.map { $0.id }
        let list = sleepRecords.filter{$0.startTime > 0}.map { record in
            return GoMoreSleepRecord.init(id: record.id, startTime: record.startTime, originalStartTime: record.originalStartTime)
        }
        print("接收到有效睡眠记录列表: \(list.count)条记录")
        // 发送通知，以便任何观察者能够响应数据
        NotificationCenter.default.post(
            name: .receivedGoMoreSleepIdsNotification,
            object: list
        )
    }
    
    func receiveBloodPressure(_ sbp: Int, _ dbp: Int) {
        print("[CRPSmartRingManage] Receive Blood Pressure: SBP \(sbp), DBP \(dbp)")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.deviceService.lastBloodPressure = (systolic: sbp, diastolic: dbp)
            self.deviceService.isMeasuringBloodPressure = false
            print("接收到血压测量结果: 收缩压 \(sbp), 舒张压 \(dbp)")
            
            // 发送通知
            NotificationCenter.default.post(
                name: .bloodPressureMeasured,
                object: nil,
                userInfo: ["systolic": sbp, "diastolic": dbp]
            )
        }
        delegate?.didReceiveBloodPressure(sbp: sbp, dbp: dbp)
    }
    
    func receiveTemperature(_ value: Double) {
        print("[CRPSmartRingManage] Receive Temperature: \(value)°C")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.deviceService.lastTemperature = value
            self.deviceService.isMeasuringTemperature = false
            print("接收到体温测量结果: \(value)°C")
            
            // 发送通知
            NotificationCenter.default.post(
                name: .temperatureMeasured,
                object: nil,
                userInfo: ["value": value]
            )
        }
        delegate?.didReceiveTemperature(value: value)
    }
    
    func recevieTakePhoto() { 
        print("接收到拍照指令")
        delegate?.didReceviePhoto()
    }
    
    func receiveKnockSwitch(_ model: CRPTouchModel) {
        print("接收到敲击开关状态: \(model)")
        delegate?.didReceiveKnockSwitch(model: model)
    }
    
    func goodixDidState(_ state: CRPState) {
        print("Goodix状态变化: \(state)")
    }
    
    func goodixRestoreState(_ state: CRPRestoreState) {
        print("Goodix恢复状态: \(state)")
    }
    
    // MARK: - GoMore相关回调
    
    /// 收到GoMore睡眠数据列表
    func didReceiveGoMoreSleepDataList(_ sleepIds: [Int]) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            print("📱 SDK回调：收到GoMore睡眠ID列表，共 \(sleepIds.count) 条")
            
            // 处理睡眠ID列表
            self.deviceService.receiveGoMoreSleepIDs(sleepIds)
        }
    }
    
    /// 收到GoMore睡眠详情数据
    func didReceiveGoMoreSleepData(_ sleepData: Any, id: Int) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            print("📱 SDK回调：收到GoMore睡眠详情数据，ID: \(id)")
            
            // 处理睡眠详情数据
            self.deviceService.receiveGoMoreSleepDetail(id: id, data: sleepData)
        }
    }

 
}

