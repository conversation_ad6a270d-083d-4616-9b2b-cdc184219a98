//
//  O2RecordDataModel.swift
//  WindRing
//
//  Created by zx on 2025/5/26.
//

public struct O2RecordDataModel: Codable,UploadedMarkable {
    var uploaded: Bool = false
    
    public let heartRate: [HRVData]
    
}

/// HRV数据模型，用于与服务器交互
public struct O2RecordData: Codable {
    public var value: Int

    public var time: Int

    public var originalStartTime: Int

}
public struct TimingO2RecordDataModel: Codable,UploadedMarkable {
    var uploaded: Bool = false
    
    public let timingO2Record: [TimingO2RecordModel]
}

public struct TimingO2RecordModel: Codable {
//    public var day: Int
    public var o2: Int
//    public let hrv: Int      // HRV值
    public let time: Int64   // 时间戳（毫秒）
}

/// 血氧上传数据模型
struct TimingO2RecordBloodOxygenUploadData: Codable {
    let date: String     // 日期
    let records: [TimingO2RecordModel]  // 血氧记录集合
}

