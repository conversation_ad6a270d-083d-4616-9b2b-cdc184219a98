//
//  Untitled.swift
//  WindRing
//
//  Created by zx on 2025/6/6.
//

public struct StressRecordModel: Codable,UploadedMarkable {
    
    var uploaded: Bool = false
    
    public var records: [StressRecord]
    
}

/// 压力记录模型
public struct StressRecord: Codable {
    let stress: Int
    let time: Int64 // 使用Int64类型，表示毫秒级时间戳
}
/// 压力上传数据模型
public struct StressUploadData: Codable {
    let date: String     // 日期
    let records: [StressRecord]  // 压力记录集合
}
