//
//  SleepTemperatureDataModel.swift
//  WindRing
//
//  Created by zx on 2025/5/27.
//

public struct SleepRecordModel: Codable,UploadedMarkable {
    var uploaded: Bool = false
    
    public var day: Int

    public var deep: Int

    public var light: Int

    public var rem: Int

    public var detail: [[String : String]]
    
    public var awake: Int?
}
struct SleepRecordDetailModel: Codable {
    let type: Int        // 状态: 0:清醒 1:浅睡 2:深睡 3:快速眼动
    let total: Int       // 睡眠时长（分钟）
    var start: String    // 开始时间（毫秒时间戳）
    var end: String      // 结束时间（毫秒时间戳）
}

struct SleepStageDetail: Codable {
    var type: String       // 0-清醒, 1-浅睡, 2-深睡, 3-快速眼动
    var total: String      // 睡眠长度（分钟）
    var start: String      // 开始时间
    var end: String        // 结束时间
}

public struct SleepTemperatureDataModel: Codable,UploadedMarkable {
    var uploaded: Bool = false
    
    public let sleepTemperatureData: [SleepTemperatureData]
    
}

/// 数据模型，用于与服务器交互
public struct SleepTemperatureData: Codable {
    public var value: Double

    public var time: Int64

//    public var originalStartTime: Int

}
public struct GoMoreSleepRecord : Codable {
    
    public var id: Int
    
    public var startTime: Int
    
    public var originalStartTime: Int

}

public struct GoMoreSleepDataModel: Codable,UploadedMarkable {
    var uploaded: Bool = false
    /// 1: 长睡眠; 2: 短睡眠
    var type: Int
    ///开始时间(时间戳)
    var startTime: Int              // 时间戳（单位秒）
    ///结束时间(时间戳)
    var endTime: Int
    ///总睡眠时间(分钟)
    var totalTime: Float            // 分钟
    ///睡眠效率
    var sleepEfficiency: Float
    ///睡眠评分
    var sleepScore: Float

}


