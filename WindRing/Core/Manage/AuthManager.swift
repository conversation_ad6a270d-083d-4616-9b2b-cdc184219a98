import Foundation

class AuthManager {
    static let shared = AuthManager()
    
    private let isLoggedInKey = "isLoggedIn"
    private let userIdKey = "userId"
    
    private init() {}
    
    var isLoggedIn: Bool {
        get {
            return UserDefaults.standard.bool(forKey: isLoggedInKey)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: isLoggedInKey)
            UserDefaults.standard.synchronize()
        }
    }
    
    var userId: String? {
        get {
            return UserDefaults.standard.string(forKey: userIdKey)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: userIdKey)
            UserDefaults.standard.synchronize()
        }
    }
    
    func login(userId: String) {
        self.userId = userId
        self.isLoggedIn = true
    }
    
    func logout() {
        self.userId = nil
        self.isLoggedIn = false
    }
} 
