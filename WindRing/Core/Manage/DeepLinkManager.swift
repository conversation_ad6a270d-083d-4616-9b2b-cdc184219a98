//
//  DeepLinkManager.swift
//  WindRing
//
//  Created by zx on 2025/6/19.
//

import Combine
import Foundation

class DeepLinkManager: ObservableObject {
    @Published var activeDeepLink: DeepLinkInfo?
    @Published var presentingAlert: Bool = false

    func handle(url: URL) {
        guard url.scheme == "siring",
              url.host == "share-data",
              let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems else { return }

        var dict = [String: String]()
        queryItems.forEach { item in
            dict[item.name] = item.value
        }

        guard let typeStr = dict["type"],
              let uuid = dict["uuid"],
              let nickname = dict["nickname"] else {
            print("⚠️ 参数不完整")
            return
        }

        let type: HealthDataAlertType = (typeStr == "1") ? .invitationShare : .activeShare

        DispatchQueue.main.async {
            self.activeDeepLink = DeepLinkInfo(
                type: type,
                uuid: uuid,
                nickname: nickname
            )
            self.presentingAlert = true
        }
    }

    func dismiss() {
        self.presentingAlert = false
        self.activeDeepLink = nil
    }
}
