import Foundation
import CoreData

/// MQTT缓存管理器
class MQTTCacheManager {
    // 单例
    public static let shared = MQTTCacheManager()
    
    // CoreData上下文 - 使用StorageManager
    private var context: NSManagedObjectContext {
        return StorageManager.shared.viewContext()
    }
    
    // 初始化
    private init() {}
    
    // MARK: - 公共方法
    
    /// 缓存消息
    func cacheMessage(topic: String, message: Data, timestamp: Date = Date()) {
        // 创建实体
        let entityDescription = NSEntityDescription.entity(forEntityName: "CachedMQTTMessage", in: context)!
        let cachedMessage = CachedMQTTMessage(entity: entityDescription, insertInto: context)
        
        // 设置属性
        cachedMessage.id = UUID()
        cachedMessage.topic = topic
        cachedMessage.message = message
        cachedMessage.timestamp = timestamp
        cachedMessage.isSynced = false
        
        // 保存上下文
        saveContext()
    }
    
    /// 获取未同步的消息
    func getUnsyncedMessages() -> [CachedMQTTMessage] {
        let fetchRequest: NSFetchRequest<CachedMQTTMessage> = CachedMQTTMessage.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "isSynced == %@", NSNumber(value: false))
        fetchRequest.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
        
        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("获取未同步消息失败: \(error)")
            return []
        }
    }
    
    /// 标记消息为已同步
    func markAsSynced(message: CachedMQTTMessage) {
        message.isSynced = true
        saveContext()
    }
    
    /// 删除已同步的消息
    func deleteOldSyncedMessages(olderThan date: Date = Date().addingTimeInterval(-7*24*60*60)) {
        let fetchRequest: NSFetchRequest<CachedMQTTMessage> = CachedMQTTMessage.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "isSynced == %@ AND timestamp < %@", 
                                            NSNumber(value: true), date as NSDate)
        
        do {
            let messages = try context.fetch(fetchRequest)
            for message in messages {
                context.delete(message)
            }
            saveContext()
        } catch {
            print("删除旧消息失败: \(error)")
        }
    }
    
    /// 保存上下文
    private func saveContext() {
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                print("保存上下文失败: \(error)")
            }
        }
    }
} 
