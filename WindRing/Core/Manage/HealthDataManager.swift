import Foundation
import CoreData
import Combine

/// 健康数据管理器
/// 负责管理和处理各种健康数据的存储与检索
public class HealthDataManager: ObservableObject {
    // MARK: - 单例
    public static let shared = HealthDataManager()
    
    // MARK: - 属性
    private let storageManager: StorageManager
    
    // MARK: - 状态属性
    @Published public private(set) var isLoading: Bool = false
    @Published public private(set) var lastUpdated: Date? = nil
    
    // MARK: - 初始化方法
    private init() {
        self.storageManager = StorageManager.shared
    }
    
    // MARK: - 用户管理
    
    /// 创建用户
    /// - Parameters:
    ///   - id: 用户ID
    ///   - name: 用户名称
    ///   - email: 用户邮箱
    /// - Returns: 是否成功
//    public func createUser(id: String, name: String, email: String,completion: ((Bool) -> Void)?) {
//        // 检查用户是否已存在
//        let predicate = NSPredicate(format: "id == %@ OR email == %@", id, email)
//        if storageManager.isEntityExists(entityName: "User", predicate: predicate) {
//            completion?(false)
//        }
//        storageManager.performBackgroundTask { context in
////            let record = User(context: context)
//            let user = NSEntityDescription.insertNewObject(forEntityName: "User", into: context) as! UserEntity
//            user.id = id
//            user.name = name
//            user.email = email
//            user.createdAt = Date()
//            user.updatedAt = Date()
//            do {
//                try context.save()
//                completion?(true)
//            } catch {
//                print("创建用户失败: \(error)")
//                completion?(false)
//            }
//        }
//        let context = storageManager.viewContext()
//        let user = NSEntityDescription.insertNewObject(forEntityName: "User", into: context) as! UserEntity
//        user.id = id
//        user.name = name
//        user.email = email
//        user.createdAt = Date()
//        user.updatedAt = Date()
//        
//        do {
//            try context.save()
//            return true
//        } catch {
//            print("创建用户失败: \(error)")
//            return false
//        }
//    }
    
    /// 获取用户
    /// - Parameter id: 用户ID
    /// - Returns: 用户实体
//    public func getUser(id: String) -> UserEntity? {
//        let predicate = NSPredicate(format: "id == %@", id)
//        return storageManager.fetchEntity(entityName: "User", predicate: predicate)
//    }
    
    /// 获取用户（通过邮箱）
    /// - Parameter email: 用户邮箱
    /// - Returns: 用户实体
//    public func getUserByEmail(email: String) -> UserEntity? {
//        let predicate = NSPredicate(format: "email == %@", email)
//        return storageManager.fetchEntity(entityName: "User", predicate: predicate)
//    }
    
    // MARK: - 心率数据管理
    
    /// 添加心率数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - value: 心率值
    ///   - timestamp: 时间戳
    ///   - deviceId: 设备ID
    ///   - confidence: 置信度
    /// - Returns: 是否成功
    public func addHeartRate(
        userId: String,
        value: Int16,
        timestamp: Date,
        deviceId: String? = nil,
        confidence: Int16? = nil
    ) -> Bool {
       
        
        let context = storageManager.viewContext()
        let heartRate = NSEntityDescription.insertNewObject(forEntityName: "HeartRate", into: context) as! HeartRateEntity
        heartRate.id = generateUniqueId()
        heartRate.userId = userId
        heartRate.value = value
        heartRate.timestamp = timestamp
        heartRate.deviceId = deviceId
        
        if let confidence = confidence {
            heartRate.confidence = confidence
        }
        
        do {
            try context.save()
            return true
        } catch {
            print("添加心率数据失败: \(error)")
            return false
        }
    }
    
    /// 批量添加心率数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - data: 心率数据数组
    /// - Returns: 是否成功
    public func addHeartRates(
        userId: String,
        data: [(value: Int16, timestamp: Date, deviceId: String?, confidence: Int16?)]
    ) -> Bool {
        
        
        let context = storageManager.viewContext()
        
        for item in data {
            let heartRate = NSEntityDescription.insertNewObject(forEntityName: "HeartRate", into: context) as! HeartRateEntity
            heartRate.id = generateUniqueId()
            heartRate.userId = userId
            heartRate.value = item.value
            heartRate.timestamp = item.timestamp
            heartRate.deviceId = item.deviceId
            
            if let confidence = item.confidence {
                heartRate.confidence = confidence
            }
        }
        
        do {
            try context.save()
            return true
        } catch {
            print("批量添加心率数据失败: \(error)")
            return false
        }
    }
    
    /// 获取心率数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期（可选）
    ///   - endDate: 结束日期（可选）
    /// - Returns: 心率数据数组
    public func getHeartRates(
        userId: String,
        startDate: Date? = nil,
        endDate: Date? = nil
    ) -> [HeartRateEntity] {
        var predicateString = "userId == %@"
        var predicateArguments: [Any] = [userId]
        
        if let startDate = startDate {
            predicateString += " AND timestamp >= %@"
            predicateArguments.append(startDate as NSDate)
        }
        
        if let endDate = endDate {
            predicateString += " AND timestamp <= %@"
            predicateArguments.append(endDate as NSDate)
        }
        
        let predicate = NSPredicate(format: predicateString, argumentArray: predicateArguments)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: true)
        
        return storageManager.fetchEntities(
            entityName: "HeartRate",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 获取最新心率数据
    /// - Parameter userId: 用户ID
    /// - Returns: 心率数据
    public func getLatestHeartRate(userId: String) -> HeartRateEntity? {
        let predicate = NSPredicate(format: "userId == %@", userId)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: false)
        
        let results: [HeartRateEntity] = storageManager.fetchEntities(
            entityName: "HeartRate",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
        
        return results.first
    }
    
    /// 获取心率统计数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 统计数据元组 (最小值, 最大值, 平均值)
    public func getHeartRateStats(
        userId: String,
        startDate: Date? = nil,
        endDate: Date? = nil
    ) -> (min: Int16, max: Int16, avg: Double)? {
        let heartRates = getHeartRates(userId: userId, startDate: startDate, endDate: endDate)
        
        guard !heartRates.isEmpty else {
            return nil
        }
        
        let values = heartRates.map { $0.value }
        let min = values.min() ?? 0
        let max = values.max() ?? 0
        let avg = Double(values.reduce(0, +)) / Double(values.count)
        
        return (min, max, avg)
    }
    
    /// 删除心率数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - before: 删除此日期之前的数据
    /// - Returns: 是否成功
    public func deleteHeartRates(userId: String, before: Date) -> Bool {
        let predicate = NSPredicate(format: "userId == %@ AND timestamp < %@", userId, before as NSDate)
        
        storageManager.batchDelete(entityName: "HeartRate", predicate: predicate)
        return true
    }
    
    /// 清空用户的所有心率数据
    /// - Parameter userId: 用户ID
    /// - Returns: 是否成功
    public func clearHeartRates(userId: String) -> Bool {
        let predicate = NSPredicate(format: "userId == %@", userId)
        
        // 使用批量删除以提高性能
        let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: "HeartRate")
        fetchRequest.predicate = predicate
        let batchDeleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)
        batchDeleteRequest.resultType = .resultTypeObjectIDs
        
        // 使用StorageManager的批量删除方法
        storageManager.batchDelete(entityName: "HeartRate", predicate: predicate)
        print("成功清空用户 \(userId) 的所有心率数据")
        return true
    }
    
    // MARK: - 步数数据管理
    
    /// 添加步数数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - value: 步数值
    ///   - timestamp: 时间戳
    ///   - date: 日期
    ///   - deviceId: 设备ID
    ///   - calories: 卡路里
    ///   - distance: 距离
    /// - Returns: 是否成功
    public func addSteps(
        userId: String,
        value: Int32,
        timestamp: Date,
        date: Date? = nil,
        deviceId: String? = nil,
        calories: Int32? = nil,
        distance: Double? = nil
    ) -> Bool {
        
        let context = storageManager.viewContext()
        let steps = NSEntityDescription.insertNewObject(forEntityName: "Steps", into: context) as! StepsEntity
        steps.id = generateUniqueId()
        steps.userId = userId
        steps.value = value
        steps.timestamp = timestamp
        steps.date = date ?? getCurrentDate()
        steps.deviceId = deviceId
        
        if let calories = calories {
            steps.calories = calories
        }
        
        if let distance = distance {
            steps.distance = distance
        }
        
        do {
            try context.save()
            return true
        } catch {
            print("添加步数数据失败: \(error)")
            return false
        }
    }
    
    /// 更新当日步数
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - value: 步数值
    ///   - date: 日期
    ///   - deviceId: 设备ID
    ///   - calories: 卡路里
    ///   - distance: 距离
    /// - Returns: 是否成功
    public func updateDailySteps(
        userId: String,
        value: Int32,
        date: Date? = nil,
        deviceId: String? = nil,
        calories: Int32? = nil,
        distance: Double? = nil
    ) -> Bool {
        let targetDate = date ?? getCurrentDate()
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day], from: targetDate)
        guard let startOfDay = calendar.date(from: components) else {
            return false
        }
        
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
        
        // 查找当天是否已有步数记录
        let predicate = NSPredicate(format: "userId == %@ AND date >= %@ AND date < %@",
                                 userId, startOfDay as NSDate, endOfDay as NSDate)
        
        if let existingSteps: StepsEntity = storageManager.fetchEntity(entityName: "Steps", predicate: predicate) {
            // 更新现有记录
            let context = storageManager.viewContext()
            existingSteps.value = value
            existingSteps.timestamp = Date()
            
            if let deviceId = deviceId {
                existingSteps.deviceId = deviceId
            }
            
            if let calories = calories {
                existingSteps.calories = calories
            }
            
            if let distance = distance {
                existingSteps.distance = distance
            }
            
            do {
                try context.save()
                return true
            } catch {
                print("更新步数数据失败: \(error)")
                return false
            }
        } else {
            // 创建新记录
            return addSteps(
                userId: userId,
                value: value,
                timestamp: Date(),
                date: targetDate,
                deviceId: deviceId,
                calories: calories,
                distance: distance
            )
        }
    }
    
    /// 获取步数数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 步数数据数组
    public func getSteps(
        userId: String,
        startDate: Date,
        endDate: Date
    ) -> [StepsEntity] {
        let predicate = NSPredicate(format: "userId == %@ AND date >= %@ AND date <= %@",
                                  userId, startDate as NSDate, endDate as NSDate)
        let sortDescriptor = NSSortDescriptor(key: "date", ascending: true)
        
        return storageManager.fetchEntities(
            entityName: "Steps",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 获取当日步数
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - date: 日期
    /// - Returns: 步数数据
    public func getDailySteps(
        userId: String,
        date: Date? = nil
    ) -> StepsEntity? {
        let targetDate = date ?? getCurrentDate()
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day], from: targetDate)
        guard let startOfDay = calendar.date(from: components) else {
            return nil
        }
        
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
        
        let predicate = NSPredicate(format: "userId == %@ AND date >= %@ AND date < %@",
                                 userId, startOfDay as NSDate, endOfDay as NSDate)
        
        return storageManager.fetchEntity(entityName: "Steps", predicate: predicate)
    }
    
    /// 获取步数统计数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 统计数据元组 (总步数, 日均步数, 最大步数)
    public func getStepsStats(
        userId: String,
        startDate: Date,
        endDate: Date
    ) -> (total: Int32, average: Double, max: Int32)? {
        let steps = getSteps(userId: userId, startDate: startDate, endDate: endDate)
        
        guard !steps.isEmpty else {
            return nil
        }
        
        let values = steps.map { $0.value }
        let total = values.reduce(0, +)
        let max = values.max() ?? 0
        let avg = Double(total) / Double(steps.count)
        
        return (total, avg, max)
    }
    
    /// 删除步数数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - before: 删除此日期之前的数据
    /// - Returns: 是否成功
    public func deleteSteps(userId: String, before: Date) -> Bool {
        let predicate = NSPredicate(format: "userId == %@ AND date < %@", userId, before as NSDate)
        
        storageManager.batchDelete(entityName: "Steps", predicate: predicate)
        return true
    }
    
    // MARK: - 睡眠数据管理
    
    /// 添加睡眠数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startTime: 开始时间
    ///   - endTime: 结束时间
    ///   - totalMinutes: 总睡眠时间（分钟）
    ///   - deepMinutes: 深睡眠时间（分钟）
    ///   - lightMinutes: 浅睡眠时间（分钟）
    ///   - remMinutes: REM睡眠时间（分钟）
    ///   - awakeMinutes: 清醒时间（分钟）
    ///   - score: 睡眠质量评分
    ///   - efficiency: 睡眠效率
    ///   - deviceId: 设备ID
    ///   - sleepStages: 睡眠阶段数据
    /// - Returns: 是否成功
    public func addSleep(
        userId: String,
        startTime: Date,
        endTime: Date,
        totalMinutes: Int16,
        deepMinutes: Int16? = nil,
        lightMinutes: Int16? = nil,
        remMinutes: Int16? = nil,
        awakeMinutes: Int16? = nil,
        score: Int16? = nil,
        efficiency: Int16? = nil,
        deviceId: String? = nil,
        sleepStages: [(type: String, startTime: Date, duration: Int16)]? = nil
    ) -> Bool {
        
        let context = storageManager.viewContext()
        let sleepId = generateUniqueId()
        
        // 创建睡眠记录
        let sleep = NSEntityDescription.insertNewObject(forEntityName: "Sleep", into: context) as! SleepEntity
        sleep.id = sleepId
        sleep.userId = userId
        sleep.startTime = startTime
        sleep.endTime = endTime
        sleep.totalMinutes = totalMinutes
        sleep.deviceId = deviceId
        
        if let deepMinutes = deepMinutes {
            sleep.deepMinutes = deepMinutes
        }
        
        if let lightMinutes = lightMinutes {
            sleep.lightMinutes = lightMinutes
        }
        
        if let remMinutes = remMinutes {
            sleep.remMinutes = remMinutes
        }
        
        if let awakeMinutes = awakeMinutes {
            sleep.awakeMinutes = awakeMinutes
        }
        
        if let score = score {
            sleep.score = score
        }
        
        if let efficiency = efficiency {
            sleep.efficiency = efficiency
        }
        
        // 创建睡眠阶段记录
        if let sleepStages = sleepStages {
            for stageData in sleepStages {
                let stage = NSEntityDescription.insertNewObject(forEntityName: "SleepStage", into: context) as! SleepStageEntity
                stage.id = generateUniqueId()
                stage.sleepId = sleepId
                stage.type = stageData.type
                stage.startTime = stageData.startTime
                stage.duration = stageData.duration
                stage.sleep = sleep
            }
        }
        
        do {
            try context.save()
            
            // 确保数据被完全提交到持久化存储
            storageManager.saveViewContext()
            
            print("✅ 添加睡眠数据成功: ID=\(sleepId), 用户=\(userId), 开始=\(startTime), 结束=\(endTime)")
            
            // 添加额外的数据验证
            validateSleepData(sleepId: sleepId, userId: userId)
            
            return true
        } catch {
            print("❌ 添加睡眠数据失败: \(error)")
            return false
        }
    }
    
    /// 验证睡眠数据是否已正确保存
    private func validateSleepData(sleepId: String, userId: String) {
        // 延迟一小段时间确保持久化完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            // 使用ID查询刚保存的数据
            let predicate = NSPredicate(format: "id == %@", sleepId)
            let results = self.storageManager.fetchEntities(
                entityName: "Sleep",
                predicate: predicate
            ) as! [SleepEntity]
            
            if let sleep = results.first {
                print("✅ 验证成功: 睡眠数据已正确保存到数据库 ID=\(sleep.id ?? "未知")")
            } else {
                print("⚠️ 验证警告: 无法找到刚才保存的睡眠数据(ID=\(sleepId))")
                
                // 尝试检查同一用户的所有睡眠数据
                let userPredicate = NSPredicate(format: "userId == %@", userId)
                let userResults = self.storageManager.fetchEntities(
                    entityName: "Sleep",
                    predicate: userPredicate
                ) as! [SleepEntity]
                
                print("📊 该用户总共有 \(userResults.count) 条睡眠记录")
                
                // 额外的保存以确保数据被写入
                self.storageManager.saveViewContext()
            }
        }
    }
    
    /// 获取所有睡眠数据
    /// - Parameter userId: 用户ID
    /// - Returns: 睡眠数据列表
    public func getSleep(userId: String) -> [SleepEntity] {
        let predicate = NSPredicate(format: "userId == %@", userId)
        let sortDescriptor = NSSortDescriptor(key: "endTime", ascending: false)
        
        let results = storageManager.fetchEntities(
            entityName: "Sleep",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        ) as! [SleepEntity]
        
        print("查询到用户 \(userId) 的睡眠数据: \(results.count) 条记录")
        
        return results
    }
    
    /// 获取指定日期范围内的睡眠数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 睡眠数据列表
    public func getSleep(userId: String, startDate: Date, endDate: Date) -> [SleepEntity] {
        let predicate = NSPredicate(format: "userId == %@ AND startTime >= %@ AND endTime <= %@", 
                                    userId, startDate as NSDate, endDate as NSDate)
        let sortDescriptor = NSSortDescriptor(key: "endTime", ascending: false)
        
        let results = storageManager.fetchEntities(
            entityName: "Sleep",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        ) as! [SleepEntity]
        
        print("查询到用户 \(userId) 在时间范围 \(startDate) 至 \(endDate) 的睡眠数据: \(results.count) 条记录")
        
        return results
    }
    
    /// 获取最近一次睡眠数据
    /// - Parameter userId: 用户ID
    /// - Returns: 睡眠数据
    public func getLatestSleep(userId: String) -> SleepEntity? {
        let predicate = NSPredicate(format: "userId == %@", userId)
        let sortDescriptor = NSSortDescriptor(key: "endTime", ascending: false)
        
        let results: [SleepEntity] = storageManager.fetchEntities(
            entityName: "Sleep",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
        
        return results.first
    }
    
    /// 获取睡眠统计数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 统计数据元组 (平均总时长, 平均深睡时长, 平均得分)
    public func getSleepStats(
        userId: String,
        startDate: Date,
        endDate: Date
    ) -> (avgTotal: Double, avgDeep: Double, avgScore: Double)? {
        let sleepRecords = getSleep(userId: userId, startDate: startDate, endDate: endDate)
        
        guard !sleepRecords.isEmpty else {
            return nil
        }
        
        let totalMinutes = sleepRecords.map { $0.totalMinutes }
        let avgTotal = Double(totalMinutes.reduce(0, +)) / Double(sleepRecords.count)
        
        var deepSum: Int = 0
        var deepCount: Int = 0
        for sleep in sleepRecords {
            if sleep.deepMinutes > 0 {
                deepSum += Int(sleep.deepMinutes)
                deepCount += 1
            }
        }
        let avgDeep = deepCount > 0 ? Double(deepSum) / Double(deepCount) : 0
        
        var scoreSum: Int = 0
        var scoreCount: Int = 0
        for sleep in sleepRecords {
            if sleep.score > 0 {
                scoreSum += Int(sleep.score)
                scoreCount += 1
            }
        }
        let avgScore = scoreCount > 0 ? Double(scoreSum) / Double(scoreCount) : 0
        
        return (avgTotal, avgDeep, avgScore)
    }
    
    /// 获取睡眠阶段数据
    /// - Parameter sleepId: 睡眠ID
    /// - Returns: 睡眠阶段数组
    public func getSleepStages(sleepId: String) -> [SleepStageEntity] {
        let predicate = NSPredicate(format: "sleepId == %@", sleepId)
        let sortDescriptor = NSSortDescriptor(key: "startTime", ascending: true)
        
        return storageManager.fetchEntities(
            entityName: "SleepStage",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 删除睡眠数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - before: 删除此日期之前的数据
    /// - Returns: 是否成功
    public func deleteSleep(userId: String, before: Date) -> Bool {
        let predicate = NSPredicate(format: "userId == %@ AND endTime < %@", userId, before as NSDate)
        
        // 首先获取要删除的睡眠记录
        let sleepRecords: [SleepEntity] = storageManager.fetchEntities(
            entityName: "Sleep",
            predicate: predicate
        )
        
        // 删除相关的睡眠阶段记录
        for sleep in sleepRecords {
            let stagesPredicate = NSPredicate(format: "sleepId == %@", sleep.id!)
            storageManager.batchDelete(entityName: "SleepStage", predicate: stagesPredicate)
        }
        
        // 删除睡眠记录
        storageManager.batchDelete(entityName: "Sleep", predicate: predicate)
        return true
    }
    
    // MARK: - 设备管理
    
    /// 添加设备
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - name: 设备名称
    ///   - type: 设备类型
    ///   - manufacturer: 制造商
    ///   - model: 型号
    ///   - firmwareVersion: 固件版本
    ///   - connectionStatus: 连接状态
    ///   - batteryLevel: 电池电量
    ///   - settings: 设备设置
    /// - Returns: 设备ID
    public func addDevice(
        userId: String,
        name: String,
        type: String,
        manufacturer: String,
        model: String,
        firmwareVersion: String? = nil,
        connectionStatus: String = "disconnected",
        batteryLevel: Int16? = nil,
        settings: [String: String]? = nil
    ) -> String? {
        
        let context = storageManager.viewContext()
        let device = NSEntityDescription.insertNewObject(forEntityName: "Device", into: context) as! DeviceEntity
        
        let deviceId = generateUniqueId()
        device.id = deviceId
        device.userId = userId
        device.name = name
        device.type = type
        device.manufacturer = manufacturer
        device.model = model
        device.firmwareVersion = firmwareVersion
        device.connectionStatus = connectionStatus
        device.batteryLevel = batteryLevel ?? 0
        device.settings = settings
        device.createdAt = Date()
        device.updatedAt = Date()
        
        do {
            try context.save()
            return deviceId
        } catch {
            print("添加设备失败: \(error)")
            return nil
        }
    }
    
    /// 更新设备
    /// - Parameters:
    ///   - deviceId: 设备ID
    ///   - name: 设备名称
    ///   - firmwareVersion: 固件版本
    ///   - connectionStatus: 连接状态
    ///   - batteryLevel: 电池电量
    ///   - lastSyncTime: 最后同步时间
    ///   - settings: 设备设置
    /// - Returns: 是否成功
    public func updateDevice(
        deviceId: String,
        name: String? = nil,
        firmwareVersion: String? = nil,
        connectionStatus: String? = nil,
        batteryLevel: Int16? = nil,
        lastSyncTime: Date? = nil,
        settings: [String: String]? = nil
    ) -> Bool {
        let predicate = NSPredicate(format: "id == %@", deviceId)
        guard let device: DeviceEntity = storageManager.fetchEntity(entityName: "Device", predicate: predicate) else {
            return false
        }
        
        let context = storageManager.viewContext()
        
        if let name = name {
            device.name = name
        }
        
        if let firmwareVersion = firmwareVersion {
            device.firmwareVersion = firmwareVersion
        }
        
        if let connectionStatus = connectionStatus {
            device.connectionStatus = connectionStatus
        }
        
        if let batteryLevel = batteryLevel {
            device.batteryLevel = batteryLevel
        }
        
        if let lastSyncTime = lastSyncTime {
            device.lastSyncTime = lastSyncTime
        }
        
        if let settings = settings {
            device.settings = settings
        }
        
        device.updatedAt = Date()
        
        do {
            try context.save()
            return true
        } catch {
            print("更新设备失败: \(error)")
            return false
        }
    }
    
    /// 获取设备
    /// - Parameter deviceId: 设备ID
    /// - Returns: 设备实体
    public func getDevice(deviceId: String) -> DeviceEntity? {
        let predicate = NSPredicate(format: "id == %@", deviceId)
        return storageManager.fetchEntity(entityName: "Device", predicate: predicate)
    }
    
    /// 获取用户的所有设备
    /// - Parameter userId: 用户ID
    /// - Returns: 设备数组
    public func getUserDevices(userId: String) -> [DeviceEntity] {
        let predicate = NSPredicate(format: "userId == %@", userId)
        let sortDescriptor = NSSortDescriptor(key: "updatedAt", ascending: false)
        
        return storageManager.fetchEntities(
            entityName: "Device",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 获取特定类型的用户设备
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - type: 设备类型
    /// - Returns: 设备数组
    public func getUserDevicesByType(userId: String, type: String) -> [DeviceEntity] {
        let predicate = NSPredicate(format: "userId == %@ AND type == %@", userId, type)
        let sortDescriptor = NSSortDescriptor(key: "updatedAt", ascending: false)
        
        return storageManager.fetchEntities(
            entityName: "Device",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 删除设备
    /// - Parameter deviceId: 设备ID
    /// - Returns: 是否成功
    public func deleteDevice(deviceId: String) -> Bool {
        let predicate = NSPredicate(format: "id == %@", deviceId)
        guard let device: DeviceEntity = storageManager.fetchEntity(entityName: "Device", predicate: predicate) else {
            return false
        }
        
        let context = storageManager.viewContext()
        context.delete(device)
        
        do {
            try context.save()
            return true
        } catch {
            print("删除设备失败: \(error)")
            return false
        }
    }
    
    /// 更新设备连接状态
    /// - Parameters:
    ///   - deviceId: 设备ID
    ///   - status: 连接状态
    /// - Returns: 是否成功
    public func updateDeviceConnectionStatus(deviceId: String, status: String) -> Bool {
        return updateDevice(deviceId: deviceId, connectionStatus: status)
    }
    
    /// 更新设备电池电量
    /// - Parameters:
    ///   - deviceId: 设备ID
    ///   - level: 电池电量
    /// - Returns: 是否成功
    public func updateDeviceBatteryLevel(deviceId: String, level: Int16) -> Bool {
        return updateDevice(deviceId: deviceId, batteryLevel: level)
    }
    
    /// 更新设备最后同步时间
    /// - Parameter deviceId: 设备ID
    /// - Returns: 是否成功
    public func updateDeviceLastSyncTime(deviceId: String) -> Bool {
        return updateDevice(deviceId: deviceId, lastSyncTime: Date())
    }
    
    // MARK: - 活动数据管理
    
    /// 添加活动记录
    /// - Parameters:
    ///   - userId: 用户ID (默认使用当前用户)
    ///   - activeMinutes: 活动分钟数
    ///   - calories: 消耗卡路里
    ///   - date: 活动日期
    ///   - type: 活动类型 (步行、跑步等)
    ///   - steps: 步数
    ///   - distance: 距离 (单位：米)
    ///   - deviceId: 记录设备ID
    /// - Returns: 是否成功添加
    public func addActivityRecord(
        userId: String = UserDefaults.standard.string(forKey: "userId") ?? "testUser",
        activeMinutes: Int,
        calories: Int,
        date: Date,
        type: String = "general",
        steps: Int = 0,
        distance: Double = 0.0,
        deviceId: String? = nil
    ) -> Bool {
        // 获取或创建用户
        
        let context = storageManager.viewContext()
        let activity = NSEntityDescription.insertNewObject(forEntityName: "Activity", into: context) as! ActivityEntity
        
        activity.id = generateUniqueId()
//        activity.userId = existingUser.id ?? userId
        activity.type = type
        activity.duration = Int32(activeMinutes)
        activity.calories = Int32(calories)
        activity.steps = Int32(steps)
        activity.distance = distance
        activity.startTime = date
        activity.endTime = Calendar.current.date(byAdding: .minute, value: activeMinutes, to: date)
        
        // 设置关系
//        activity.user = existingUser
        
        do {
            try context.save()
//            print("成功添加活动记录: \(activity.id ?? "未知ID"), 用户: \(existingUser.id ?? userId)")
            return true
        } catch {
            print("添加活动记录失败: \(error)")
            return false
        }
    }
    
    /// 获取活动记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 活动记录数组
    public func getActivities(
        userId: String,
        startDate: Date,
        endDate: Date
    ) -> [ActivityEntity] {
        let predicate = NSPredicate(format: "userId == %@ AND startTime >= %@ AND startTime <= %@",
                                  userId, startDate as NSDate, endDate as NSDate)
        let sortDescriptor = NSSortDescriptor(key: "startTime", ascending: false)
        
        return storageManager.fetchEntities(
            entityName: "Activity",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 获取活动统计数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 统计数据元组 (总活动分钟, 总消耗卡路里, 平均活动分钟/天)
    public func getActivityStats(
        userId: String,
        startDate: Date,
        endDate: Date
    ) -> (totalMinutes: Int32, totalCalories: Int32, avgMinutesPerDay: Double)? {
        let activities = getActivities(userId: userId, startDate: startDate, endDate: endDate)
        
        guard !activities.isEmpty else {
            return nil
        }
        
        let totalMinutes = activities.reduce(0) { $0 + $1.duration }
        let totalCalories = activities.reduce(0) { $0 + $1.calories }
        
        // 计算日期范围天数
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: startDate, to: endDate)
        let days = max(1, components.day ?? 1)
        
        let avgMinutesPerDay = Double(totalMinutes) / Double(days)
        
        return (totalMinutes, totalCalories, avgMinutesPerDay)
    }
    
    /// 删除活动记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - before: 删除此日期之前的数据
    /// - Returns: 是否成功
    public func deleteActivities(userId: String, before: Date) -> Bool {
        let predicate = NSPredicate(format: "userId == %@ AND startTime < %@", userId, before as NSDate)
        
        storageManager.batchDelete(entityName: "Activity", predicate: predicate)
        return true
    }
    
    // MARK: - 体重数据管理
    
    /// 添加体重数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - value: 体重值（千克）
    ///   - timestamp: 时间戳
    ///   - deviceId: 设备ID
    ///   - bmi: 体质指数
    ///   - bodyFat: 体脂率（百分比）
    ///   - muscleMass: 肌肉质量（千克）
    /// - Returns: 是否成功
    public func addWeight(
        userId: String,
        value: Double,
        timestamp: Date,
        deviceId: String? = nil,
        bmi: Double? = nil,
        bodyFat: Double? = nil,
        muscleMass: Double? = nil
    ) -> Bool {
//        guard getUser(id: userId) != nil else {
//            return false
//        }
        
        let context = storageManager.viewContext()
        let weight = NSEntityDescription.insertNewObject(forEntityName: "Weight", into: context) as! WeightEntity
        weight.id = generateUniqueId()
        weight.userId = userId
        weight.value = value
        weight.timestamp = timestamp
        weight.deviceId = deviceId
        weight.bmi = bmi ?? 0.0
        weight.bodyFat = bodyFat ?? 0.0
        weight.muscleMass = muscleMass ?? 0.0
        
        do {
            try context.save()
            return true
        } catch {
            print("添加体重数据失败: \(error)")
            return false
        }
    }
    
    /// 批量添加体重数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - data: 体重数据数组
    /// - Returns: 是否成功
    public func addWeights(
        userId: String,
        data: [(value: Double, timestamp: Date, deviceId: String?, bmi: Double?, bodyFat: Double?)]
    ) -> Bool {
//        guard !data.isEmpty, getUser(id: userId) != nil else {
//            return false
//        }
        
        let context = storageManager.viewContext()
        
        for item in data {
            let weight = NSEntityDescription.insertNewObject(forEntityName: "Weight", into: context) as! WeightEntity
            weight.id = generateUniqueId()
            weight.userId = userId
            weight.value = item.value
            weight.timestamp = item.timestamp
            weight.deviceId = item.deviceId
            weight.bmi = item.bmi ?? 0.0
            weight.bodyFat = item.bodyFat ?? 0.0
            weight.muscleMass = 0.0  // 设置默认值
        }
        
        do {
            try context.save()
            return true
        } catch {
            print("批量添加体重数据失败: \(error)")
            return false
        }
    }
    
    /// 获取体重数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 体重数据数组
    public func getWeights(
        userId: String,
        startDate: Date,
        endDate: Date
    ) -> [WeightEntity] {
        let predicate = NSPredicate(format: "userId == %@ AND timestamp >= %@ AND timestamp <= %@",
                                    userId, startDate as NSDate, endDate as NSDate)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: true)
        
        return storageManager.fetchEntities(
            entityName: "Weight",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 获取最近一次体重数据
    /// - Parameter userId: 用户ID
    /// - Returns: 体重数据
    public func getLatestWeight(userId: String) -> WeightEntity? {
        let predicate = NSPredicate(format: "userId == %@", userId)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: false)
        
        let results: [WeightEntity] = storageManager.fetchEntities(
            entityName: "Weight",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
        
        return results.first
    }
    
    /// 删除体重数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - before: 删除此日期之前的数据
    /// - Returns: 是否成功
    public func deleteWeights(userId: String, before: Date) -> Bool {
        let predicate = NSPredicate(format: "userId == %@ AND timestamp < %@", userId, before as NSDate)
        storageManager.batchDelete(entityName: "Weight", predicate: predicate)
        return true
    }
    
    // MARK: - 压力数据管理
    
    /// 添加压力数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - value: 压力值 (0-100)
    ///   - timestamp: 时间戳
    ///   - deviceId: 设备ID
    /// - Returns: 是否成功
    public func addStress(
        userId: String,
        value: Int16,
        timestamp: Date,
        deviceId: String? = nil
    ) -> Bool {
//        guard getUser(id: userId) != nil else {
//            return false
//        }
        
        let context = storageManager.viewContext()
        let stress = NSEntityDescription.insertNewObject(forEntityName: "Stress", into: context) as! StressEntity
        stress.id = generateUniqueId()
        stress.userId = userId
        stress.value = value
        stress.timestamp = timestamp
        stress.deviceId = deviceId
        
        do {
            try context.save()
            return true
        } catch {
            print("添加压力数据失败: \(error)")
            return false
        }
    }
    
    /// 批量添加压力数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - data: 压力数据数组
    /// - Returns: 是否成功
    public func addStresses(
        userId: String,
        data: [(value: Int16, timestamp: Date, deviceId: String?)]
    ) -> Bool {
//        guard !data.isEmpty, getUser(id: userId) != nil else {
//            return false
//        }
        
        let context = storageManager.viewContext()
        
        for item in data {
            let stress = NSEntityDescription.insertNewObject(forEntityName: "Stress", into: context) as! StressEntity
            stress.id = generateUniqueId()
            stress.userId = userId
            stress.value = item.value
            stress.timestamp = item.timestamp
            stress.deviceId = item.deviceId
        }
        
        do {
            try context.save()
            return true
        } catch {
            print("批量添加压力数据失败: \(error)")
            return false
        }
    }
    
    /// 获取压力数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期（可选）
    ///   - endDate: 结束日期（可选）
    /// - Returns: 压力数据数组
    public func getStresses(
        userId: String,
        startDate: Date? = nil,
        endDate: Date? = nil
    ) -> [StressEntity] {
        var predicateString = "userId == %@"
        var predicateArguments: [Any] = [userId]
        
        if let startDate = startDate {
            predicateString += " AND timestamp >= %@"
            predicateArguments.append(startDate as NSDate)
        }
        
        if let endDate = endDate {
            predicateString += " AND timestamp <= %@"
            predicateArguments.append(endDate as NSDate)
        }
        
        let predicate = NSPredicate(format: predicateString, argumentArray: predicateArguments)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: true)
        
        return storageManager.fetchEntities(
            entityName: "Stress",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 获取最新压力数据
    /// - Parameter userId: 用户ID
    /// - Returns: 压力数据
    public func getLatestStress(userId: String) -> StressEntity? {
        let predicate = NSPredicate(format: "userId == %@", userId)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: false)
        
        let results: [StressEntity] = storageManager.fetchEntities(
            entityName: "Stress",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
        
        return results.first
    }
    
    /// 获取压力统计数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期（可选）
    ///   - endDate: 结束日期（可选）
    /// - Returns: 统计数据元组 (最小值, 最大值, 平均值)
    public func getStressStats(
        userId: String,
        startDate: Date? = nil,
        endDate: Date? = nil
    ) -> (min: Int16, max: Int16, avg: Double)? {
        let stresses = getStresses(userId: userId, startDate: startDate, endDate: endDate)
        
        guard !stresses.isEmpty else {
            return nil
        }
        
        let values = stresses.map { $0.value }
        let min = values.min() ?? 0
        let max = values.max() ?? 0
        let avg = Double(values.reduce(0, +)) / Double(values.count)
        
        return (min, max, avg)
    }
    
    /// 获取日平均压力值
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - date: 日期
    /// - Returns: 日平均压力值
    public func getDailyAverageStress(
        userId: String,
        date: Date
    ) -> Double? {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
        
        let stresses = getStresses(userId: userId, startDate: startOfDay, endDate: endOfDay)
        
        guard !stresses.isEmpty else {
            return nil
        }
        
        let values = stresses.map { Double($0.value) }
        return values.reduce(0, +) / Double(values.count)
    }
    
    /// 删除压力数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - before: 删除此日期之前的数据
    /// - Returns: 是否成功
    public func deleteStresses(userId: String, before: Date) -> Bool {
        let predicate = NSPredicate(format: "userId == %@ AND timestamp < %@", userId, before as NSDate)
        
        storageManager.batchDelete(entityName: "Stress", predicate: predicate)
        return true
    }
    
    // MARK: - 日平均压力数据管理
    
    /// 添加或更新日平均压力数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - date: 日期
    ///   - value: 平均压力值 (0-100)
    /// - Returns: 是否成功
    public func addOrUpdateDailyStress(userId: String, date: Date, value: Double) -> Bool {
//        guard getUser(id: userId) != nil else {
//            print("用户ID不存在: \(userId)")
//            return false
//        }
        
        // 获取日期的开始时间（仅年月日）
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        
        // 检查是否已存在该日期的记录
        let existingRecord = getDailyStress(userId: userId, date: startOfDay)
        let context = storageManager.viewContext()
        
        // 计算压力评分 (100 - 压力值)
        let stressScore = Int16(100 - Int(value.rounded()))
        let stressScore16 = max(0, min(100, stressScore)) // 确保在0-100范围内
        
        if let existingRecord = existingRecord {
            // 更新现有记录
            existingRecord.value = value
            existingRecord.score = stressScore16
        } else {
            // 创建新记录
            let dailyStress = NSEntityDescription.insertNewObject(forEntityName: "DailyStress", into: context) as! DailyStressEntity
            dailyStress.id = generateUniqueId()
            dailyStress.userId = userId
            dailyStress.date = startOfDay
            dailyStress.value = value
            dailyStress.score = stressScore16
        }
        
        do {
            try context.save()
            print("成功保存日平均压力数据：\(startOfDay)，值：\(value)，评分：\(stressScore16)")
            return true
        } catch {
            print("保存日平均压力数据失败: \(error)")
            return false
        }
    }
    
    /// 获取特定日期的日平均压力数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - date: 日期
    /// - Returns: 日平均压力数据
    public func getDailyStress(userId: String, date: Date) -> DailyStressEntity? {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        
        let predicate = NSPredicate(format: "userId == %@ AND date == %@", userId, startOfDay as NSDate)
        
        let results: [DailyStressEntity] = storageManager.fetchEntities(
            entityName: "DailyStress",
            predicate: predicate,
            sortDescriptors: []
        )
        
        return results.first
    }
    
    /// 获取日平均压力数据列表
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 日平均压力数据数组
    public func getDailyStresses(userId: String, startDate: Date, endDate: Date) -> [DailyStressEntity] {
        let predicate = NSPredicate(format: "userId == %@ AND date >= %@ AND date <= %@",
                                    userId, startDate as NSDate, endDate as NSDate)
        let sortDescriptor = NSSortDescriptor(key: "date", ascending: true)
        
        return storageManager.fetchEntities(
            entityName: "DailyStress",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 计算并保存日平均压力值
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - date: 日期
    /// - Returns: 成功计算并保存返回平均值，否则返回nil
    public func calculateAndSaveDailyStress(userId: String, date: Date) -> Double? {
        guard let averageStress = getDailyAverageStress(userId: userId, date: date) else {
            print("无法计算平均压力值，该日期没有压力数据：\(date)")
            return nil
        }
        
        if addOrUpdateDailyStress(userId: userId, date: date, value: averageStress) {
            return averageStress
        } else {
            return nil
        }
    }
    
    // MARK: - 辅助方法
    
    /// 生成唯一ID
    /// - Returns: 唯一ID字符串
    private func generateUniqueId() -> String {
        return UUID().uuidString
    }
    
    /// 获取当前日期（仅包含年月日）
    /// - Returns: 日期对象
    private func getCurrentDate() -> Date {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day], from: Date())
        return calendar.date(from: components) ?? Date()
    }
    
    // MARK: - 简化接口方法
    
    /// 添加步数记录（简化接口）
    /// - Parameters:
    ///   - count: 步数
    ///   - date: 日期
    /// - Returns: 是否成功
    public func addStepsRecord(count: Int, date: Date) -> Bool {
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "testUser"
        return updateDailySteps(
            userId: userId,
            value: Int32(count),
            date: date
        )
    }
    
    /// 添加睡眠记录（简化接口）
    /// - Parameters:
    ///   - duration: 睡眠时长（小时）
    ///   - quality: 睡眠质量（百分比）
    ///   - date: 日期
    /// - Returns: 是否成功
    public func addSleepRecord(duration: Double, quality: Int, date: Date) -> Bool {
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "testUser"
        
        // 计算睡眠开始和结束时间
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day], from: date)
        guard let startOfDay = calendar.date(from: components) else {
            return false
        }
        
        // 假设睡眠从晚上10点开始
        let sleepStartTime = calendar.date(byAdding: .hour, value: 22, to: startOfDay)!
        let sleepEndTime = calendar.date(byAdding: .hour, value: Int(duration), to: sleepStartTime)!
        
        // 计算各阶段睡眠时间（分钟）
        let totalMinutes = Int16(duration * 60)
        let deepMinutes = Int16(Double(totalMinutes) * 0.25) // 假设25%是深度睡眠
        let lightMinutes = Int16(Double(totalMinutes) * 0.55) // 假设55%是浅度睡眠
        let remMinutes = Int16(Double(totalMinutes) * 0.20) // 假设20%是REM睡眠
        
        return addSleep(
            userId: userId,
            startTime: sleepStartTime,
            endTime: sleepEndTime,
            totalMinutes: totalMinutes,
            deepMinutes: deepMinutes,
            lightMinutes: lightMinutes,
            remMinutes: remMinutes,
            score: Int16(quality)
        )
    }
    
    /// 添加压力记录（简化接口）
    /// - Parameters:
    ///   - score: 压力分数
    ///   - date: 日期
    /// - Returns: 是否成功
    public func addStressRecord(score: Int, date: Date) -> Bool {
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "testUser"
        
        let context = storageManager.viewContext()
        let stress = NSEntityDescription.insertNewObject(forEntityName: "Stress", into: context) as! StressEntity
        
        stress.id = generateUniqueId()
        stress.userId = userId
        stress.value = Int16(score)
        stress.timestamp = date
        
        do {
            try context.save()
            return true
        } catch {
            print("添加压力记录失败: \(error)")
            return false
        }
    }
    
    /// 添加体温记录（简化接口）
    /// - Parameters:
    ///   - value: 体温值
    ///   - timestamp: 时间戳
    /// - Returns: 是否成功
    public func addTemperatureRecord(
        userId: String,
        value: Double,
        timestamp: Date,
        deviceId: String? = nil,
        type: String? = nil
    ) -> Bool {
//        guard getUser(id: userId) != nil else {
//            return false
//        }
//        
        let context = storageManager.viewContext()
        let temperature = NSEntityDescription.insertNewObject(forEntityName: "Temperature", into: context) as! TemperatureEntity
        
        temperature.id = generateUniqueId()
        temperature.userId = userId
        temperature.value = value
        temperature.timestamp = timestamp
        temperature.deviceId = deviceId
        temperature.type = type ?? "auto"
        
        do {
            try context.save()
            return true
        } catch {
            print("添加温度数据失败: \(error)")
            return false
        }
    }
    
    /// 获取体温记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 体温记录数组
    public func getTemperatures(
        userId: String,
        startDate: Date,
        endDate: Date
    ) -> [TemperatureEntity] {
        let predicate = NSPredicate(format: "userId == %@ AND timestamp >= %@ AND timestamp <= %@",
                                  userId, startDate as NSDate, endDate as NSDate)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: false)
        
        return storageManager.fetchEntities(
            entityName: "Temperature",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 获取压力记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 压力记录数组
    public func getStress(
        userId: String,
        startDate: Date,
        endDate: Date
    ) -> [StressEntity] {
        let predicate = NSPredicate(format: "userId == %@ AND timestamp >= %@ AND timestamp <= %@",
                                  userId, startDate as NSDate, endDate as NSDate)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: false)
        
        return storageManager.fetchEntities(
            entityName: "Stress",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 添加压力记录（带用户ID）
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - score: 压力分数
    ///   - timestamp: 时间戳
    /// - Returns: 是否成功
    public func addStressRecord(userId: String, score: Int, timestamp: Date) -> Bool {
        let context = storageManager.viewContext()
        let stress = NSEntityDescription.insertNewObject(forEntityName: "Stress", into: context) as! StressEntity
        
        stress.id = generateUniqueId()
        stress.userId = userId
        stress.value = Int16(score)
        stress.timestamp = timestamp
        
        do {
            try context.save()
            return true
        } catch {
            print("保存压力数据失败: \(error)")
            return false
        }
    }
    
    /// 添加血氧记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - value: 血氧值 (0-100)
    ///   - timestamp: 时间戳
    ///   - deviceId: 设备ID
    /// - Returns: 是否成功
    public func addBloodOxygen(
        userId: String,
        value: Int,
        timestamp: Date,
        deviceId: String? = nil
    ) -> Bool {
        let context = storageManager.viewContext()
        let bloodOxygen = NSEntityDescription.insertNewObject(forEntityName: "BloodOxygen", into: context) as! BloodOxygenEntity
        
        bloodOxygen.id = generateUniqueId()
        bloodOxygen.userId = userId
        bloodOxygen.value = Double(value)
        bloodOxygen.timestamp = timestamp
        bloodOxygen.deviceId = deviceId
        
        do {
            try context.save()
            return true
        } catch {
            print("保存血氧数据失败: \(error)")
            return false
        }
    }
    
    /// 获取血氧记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 血氧记录数组
    public func getBloodOxygens(
        userId: String,
        startDate: Date,
        endDate: Date
    ) -> [BloodOxygenEntity] {
        let predicate = NSPredicate(format: "userId == %@ AND timestamp >= %@ AND timestamp <= %@",
                                  userId, startDate as NSDate, endDate as NSDate)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: false)
        
        return storageManager.fetchEntities(
            entityName: "BloodOxygen",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 添加血氧记录（简化接口）
    /// - Parameters:
    ///   - value: 血氧值
    ///   - timestamp: 时间戳
    /// - Returns: 是否成功
    public func addBloodOxygenRecord(value: Int, timestamp: Date) -> Bool {
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "testUser"
        return addBloodOxygen(
            userId: userId,
            value: value,
            timestamp: timestamp
        )
    }
    
    // MARK: - HRV数据管理
    
    /// 添加HRV数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - value: HRV值
    ///   - timestamp: 时间戳
    ///   - deviceId: 设备ID
    ///   - confidence: 置信度
    /// - Returns: 是否成功
    public func addHRV(
        userId: String,
        value: Int16,
        timestamp: Date,
        deviceId: String? = nil,
        confidence: Int16? = nil
    ) -> Bool {
//        guard getUser(id: userId) != nil else {
//            return false
//        }
        
        let context = storageManager.viewContext()
        let hrv = NSEntityDescription.insertNewObject(forEntityName: "HRV", into: context) as! HRVEntity
        hrv.id = generateUniqueId()
        hrv.userId = userId
        hrv.value = value
        hrv.timestamp = timestamp
        hrv.deviceId = deviceId
        hrv.isUploaded = false
        
        if let confidence = confidence {
            hrv.confidence = confidence
        }
        
        do {
            try context.save()
            return true
        } catch {
            print("添加HRV数据失败: \(error)")
            return false
        }
    }
    
    /// 批量添加HRV数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - data: HRV数据数组
    /// - Returns: 是否成功
    public func addHRVs(
        userId: String,
        data: [(value: Int16, timestamp: Date, deviceId: String?, confidence: Int16?)]
    ) -> Bool {
//        guard !data.isEmpty, getUser(id: userId) != nil else {
//            return false
//        }
        
        let context = storageManager.viewContext()
        
        for item in data {
            let hrv = NSEntityDescription.insertNewObject(forEntityName: "HRV", into: context) as! HRVEntity
            hrv.id = generateUniqueId()
            hrv.userId = userId
            hrv.value = item.value
            hrv.timestamp = item.timestamp
            hrv.deviceId = item.deviceId
            hrv.isUploaded = false
            
            if let confidence = item.confidence {
                hrv.confidence = confidence
            }
        }
        
        do {
            try context.save()
            return true
        } catch {
            print("批量添加HRV数据失败: \(error)")
            return false
        }
    }
    
    /// 获取HRV数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期（可选）
    ///   - endDate: 结束日期（可选）
    /// - Returns: HRV数据数组
    public func getHRVs(
        userId: String,
        startDate: Date? = nil,
        endDate: Date? = nil
    ) -> [HRVEntity] {
        var predicateString = "userId == %@"
        var predicateArguments: [Any] = [userId]
        
        if let startDate = startDate {
            predicateString += " AND timestamp >= %@"
            predicateArguments.append(startDate as NSDate)
        }
        
        if let endDate = endDate {
            predicateString += " AND timestamp <= %@"
            predicateArguments.append(endDate as NSDate)
        }
        
        let predicate = NSPredicate(format: predicateString, argumentArray: predicateArguments)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: true)
        
        return storageManager.fetchEntities(
            entityName: "HRV",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
    }
    
    /// 获取最新HRV数据
    /// - Parameter userId: 用户ID
    /// - Returns: HRV数据
    public func getLatestHRV(userId: String) -> HRVEntity? {
        let predicate = NSPredicate(format: "userId == %@", userId)
        let sortDescriptor = NSSortDescriptor(key: "timestamp", ascending: false)
        
        let results: [HRVEntity] = storageManager.fetchEntities(
            entityName: "HRV",
            predicate: predicate,
            sortDescriptors: [sortDescriptor]
        )
        
        return results.first
    }
    
    /// 获取HRV统计数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期（可选）
    ///   - endDate: 结束日期（可选）
    /// - Returns: 统计数据元组 (最小值, 最大值, 平均值)
    public func getHRVStats(
        userId: String,
        startDate: Date? = nil,
        endDate: Date? = nil
    ) -> (min: Int16, max: Int16, avg: Double)? {
        let hrvs = getHRVs(userId: userId, startDate: startDate, endDate: endDate)
        
        guard !hrvs.isEmpty else {
            return nil
        }
        
        let values = hrvs.map { $0.value }
        let min = values.min() ?? 0
        let max = values.max() ?? 0
        let avg = Double(values.reduce(0, +)) / Double(values.count)
        
        return (min, max, avg)
    }
    
    /// 删除HRV数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - before: 删除此日期之前的数据
    /// - Returns: 是否成功
    public func deleteHRVs(userId: String, before: Date) -> Bool {
        let predicate = NSPredicate(format: "userId == %@ AND timestamp < %@", userId, before as NSDate)
        
        storageManager.batchDelete(entityName: "HRV", predicate: predicate)
        return true
    }
    
    // MARK: - 调试方法
    
    /// 以JSON格式打印所有用户数据
//    public func printAllUsersAsJSON() {
//        let context = storageManager.viewContext()
//        let fetchRequest = NSFetchRequest<UserEntity>(entityName: "User")
//        
//        do {
//            let userEntities = try context.fetch(fetchRequest)
//            print("\n=== 所有用户数据 (共 \(userEntities.count) 人) ===")
//            
//            if userEntities.isEmpty {
//                print("数据库中没有用户记录")
//                return
//            }
//            
//            for (index, user) in userEntities.enumerated() {
//                // 构建用户数据JSON
//                let userJson: [String: Any] = [
//                    "id": user.id ?? "unknown",
//                    "name": user.name ?? "unknown",
//                    "email": user.email ?? "unknown",
//                    "gender": user.gender ?? "unknown",
//                    "height": user.height,
//                    "weight": user.weight,
//                    "createdAt": formatDate(user.createdAt),
//                    "updatedAt": formatDate(user.updatedAt)
//                ]
//                
//                // 将JSON转换为字符串并打印
//                if let jsonData = try? JSONSerialization.data(withJSONObject: userJson, options: .prettyPrinted),
//                   let jsonString = String(data: jsonData, encoding: .utf8) {
//                    print("\n--- 用户 #\(index+1) ---")
//                    print(jsonString)
//                } else {
//                    print("无法将用户数据转换为JSON")
//                }
//            }
//            
//            print("\n=== 用户数据打印完成 ===\n")
//        } catch {
//            print("获取用户数据失败: \(error)")
//        }
//    }
    
    /// 以JSON格式打印所有睡眠数据
    public func printAllSleepDataAsJSON() {
        let context = storageManager.viewContext()
        let fetchRequest = NSFetchRequest<SleepEntity>(entityName: "Sleep")
        
        do {
            let sleepEntities = try context.fetch(fetchRequest)
            print("\n=== 所有睡眠数据 (共 \(sleepEntities.count) 条) ===")
            
            if sleepEntities.isEmpty {
                print("数据库中没有睡眠数据记录")
                return
            }
            
            for (index, sleep) in sleepEntities.enumerated() {
                // 构建基本睡眠数据JSON
                var sleepJson: [String: Any] = [
                    "id": sleep.id ?? "unknown",
                    "userId": sleep.userId ?? "unknown",
                    "startTime": formatDate(sleep.startTime),
                    "endTime": formatDate(sleep.endTime),
                    "totalMinutes": sleep.totalMinutes,
                    "deepMinutes": sleep.deepMinutes,
                    "lightMinutes": sleep.lightMinutes,
                    "remMinutes": sleep.remMinutes,
                    "awakeMinutes": sleep.awakeMinutes,
                    "score": sleep.score,
                    "efficiency": sleep.efficiency
                ]
                
                // 获取睡眠阶段数据
                if let sleepStages = sleep.sleepStages as? Set<SleepStageEntity>, !sleepStages.isEmpty {
                    var stagesArray: [[String: Any]] = []
                    
                    for stage in sleepStages {
                        let stageJson: [String: Any] = [
                            "id": stage.id ?? "unknown",
                            "type": stage.type ?? "unknown",
                            "startTime": formatDate(stage.startTime),
                            "duration": stage.duration
                        ]
                        stagesArray.append(stageJson)
                    }
                    
                    sleepJson["sleepStages"] = stagesArray
                }
                
                // 将JSON转换为字符串并打印
                if let jsonData = try? JSONSerialization.data(withJSONObject: sleepJson, options: .prettyPrinted),
                   let jsonString = String(data: jsonData, encoding: .utf8) {
                    print("\n--- 睡眠记录 #\(index+1) ---")
                    print(jsonString)
                } else {
                    print("无法将睡眠数据转换为JSON")
                }
            }
            
            print("\n=== 睡眠数据打印完成 ===\n")
        } catch {
            print("获取睡眠数据失败: \(error)")
        }
    }
    
    /// 格式化日期为可读字符串
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "null" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }

    // MARK: - 数据清理

    /// 清理指定用户的重复睡眠数据
    /// - Parameter userId: 用户ID
    /// - Returns: 删除的重复记录数量
    public func cleanupDuplicateSleepData(userId: String) -> Int {
        print("🧹 开始清理用户 \(userId) 的重复睡眠数据...")
        
        let context = storageManager.viewContext()
        // 获取该用户的所有睡眠记录，按开始时间排序
        let sortDescriptor = NSSortDescriptor(key: "startTime", ascending: true)
        let allSleepRecords = getSleep(userId: userId)
        
        guard allSleepRecords.count > 1 else {
            print("👍 用户 \(userId) 的睡眠记录不足两条，无需清理。")
            return 0
        }
        
        var recordsToDelete: [SleepEntity] = []
        var recordsToKeep: Set<NSManagedObjectID> = Set()
        
        // 使用一个更健壮的方法来识别重复项
        // 基本思路：遍历排序后的记录，如果发现与"保留"记录集合中的某条记录重复，则将其标记为删除
        
        var uniqueRecords: [SleepEntity] = [] // 存储被认为是唯一的记录
        
        for currentRecord in allSleepRecords {
            var isDuplicate = false
            
            // 与已确定唯一的记录进行比较
            for uniqueRecord in uniqueRecords {
                if isDuplicateSleepRecord(currentRecord, comparedTo: uniqueRecord) {
                    isDuplicate = true
                    break
                }
            }
            
            if isDuplicate {
                // 如果是重复的，添加到删除列表
                recordsToDelete.append(currentRecord)
                print("  标记删除 (重复): ID=\(currentRecord.id ?? "未知"), Start=\(currentRecord.startTime?.description ?? "")")
            } else {
                // 如果不是重复的，添加到唯一记录列表
                uniqueRecords.append(currentRecord)
                 print("  保留 (唯一): ID=\(currentRecord.id ?? "未知"), Start=\(currentRecord.startTime?.description ?? "")")
            }
        }
        
        // 执行删除
        if !recordsToDelete.isEmpty {
            print("🗑️ 准备删除 \(recordsToDelete.count) 条重复记录...")
            for record in recordsToDelete {
                context.delete(record)
            }
            
            do {
                try context.save()
                print("✅ 成功删除 \(recordsToDelete.count) 条重复记录。")
                return recordsToDelete.count
            } catch {
                print("❌ 删除重复睡眠记录失败: \(error)")
                // 如果保存失败，回滚更改
                context.rollback()
                return 0
            }
        } else {
            print("👍 未发现需要删除的重复睡眠记录。")
            return 0
        }
    }

    /// 辅助函数：判断两条睡眠记录是否重复
    /// - Parameters:
    ///   - record1: 记录1
    ///   - record2: 记录2
    /// - Returns: 是否重复
    private func isDuplicateSleepRecord(_ record1: SleepEntity, comparedTo record2: SleepEntity) -> Bool {
        guard let start1 = record1.startTime, let end1 = record1.endTime,
              let start2 = record2.startTime, let end2 = record2.endTime else {
            // 如果时间信息不完整，无法比较，认为不重复
            return false
        }
        
        // 1. 检查时间是否重叠 (允许一定的容差，例如 10 分钟)
        let tolerance: TimeInterval = 60 * 10 // 10 分钟的容差
        let hasOverlap = (start1.addingTimeInterval(-tolerance) < end2) && (end1.addingTimeInterval(tolerance) > start2)
        
        if !hasOverlap {
            return false // 时间不重叠，肯定不是重复
        }
        
        // 2. 检查关键睡眠指标是否相似 (允许 5 分钟误差)
        let deepSimilar = abs(Int(record1.deepMinutes) - Int(record2.deepMinutes)) <= 5
        let lightSimilar = abs(Int(record1.lightMinutes) - Int(record2.lightMinutes)) <= 5
        let remSimilar = abs(Int(record1.remMinutes) - Int(record2.remMinutes)) <= 5
        
        // 如果时间重叠且关键指标相似，则认为是重复
        return deepSimilar && lightSimilar && remSimilar
    }
} 
