//
//  PlistManager.swift
//  WindRing
//
//  Created by zx on 2025/5/24.
//

import Foundation

// MARK: - 数据模型
struct SleepData: Codable {
    let start: String
    let end: String
    let quality: String
    var uploaded: Bool = false
    
}

// MARK: - Plist 管理器
class PlistManager {
    static let shared = PlistManager()
    let deviceID = WindRingDeviceService.shared.deviceInfo?.mac
    private init() {}
    
    private func iCloudURL(for deviceID: String) -> URL? {
        guard let container = FileManager.default.url(forUbiquityContainerIdentifier: nil) else {
            print("⚠️ iCloud 容器不可用")
            return nil
        }
        let directory = container.appendingPathComponent("Documents", isDirectory: true)
        try? FileManager.default.createDirectory(at: directory, withIntermediateDirectories: true)
        return directory.appendingPathComponent("device_\(deviceID).plist")
    }
    
    private func localURL(for deviceID: String) -> URL {
        let dir = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        return dir.appendingPathComponent("device_\(deviceID).plist")
    }
    
    private func plistURL(for deviceID: String) -> URL {
        return localURL(for: deviceID)
    }
    
    func loadPlist(for deviceID: String) -> NSMutableDictionary {
        let url = plistURL(for: deviceID)
        if let dict = NSDictionary(contentsOf: url) as? NSMutableDictionary {
            return dict
        } else {
            return NSMutableDictionary()
        }
    }
    
    func savePlist(_ dict: NSMutableDictionary, for deviceID: String) {
        let url = plistURL(for: deviceID)
        dict.write(to: url, atomically: true)
    }
    
    // MARK: - 获取数据
    func getSleepData(for deviceID: String, date: String) -> SleepData? {
        let dict = loadPlist(for: deviceID)
        guard let sleepDict = dict["sleepData"] as? NSDictionary,
              let entry = sleepDict[date] as? [String: Any],
              let data = try? JSONSerialization.data(withJSONObject: entry),
              let result = try? PropertyListDecoder().decode(SleepData.self, from: data) else { return nil }
        return result
    }
    
    func getActivityData(for deviceID: String, date: String) -> ActivityData? {
        let dict = loadPlist(for: deviceID)
        guard let activityDict = dict["activityData"] as? NSDictionary,
              let entry = activityDict[date] as? [String: Any],
              let data = try? JSONSerialization.data(withJSONObject: entry),
              let result = try? PropertyListDecoder().decode(ActivityData.self, from: data) else { return nil }
        return result
    }
    
    // MARK: - 删除数据
    func deleteSleepData(for deviceID: String, date: String) {
        let dict = loadPlist(for: deviceID)
        guard let sleepDict = dict["sleepData"] as? NSMutableDictionary else { return }
        sleepDict.removeObject(forKey: date)
        dict["sleepData"] = sleepDict
        savePlist(dict, for: deviceID)
        print("🗑️ 已删除 device \(deviceID) 的睡眠数据：\(date)")
    }
    
    func deleteActivityData(for deviceID: String, date: String) {
        let dict = loadPlist(for: deviceID)
        guard let activityDict = dict["activityData"] as? NSMutableDictionary else { return }
        activityDict.removeObject(forKey: date)
        dict["activityData"] = activityDict
        savePlist(dict, for: deviceID)
        print("🗑️ 已删除 device \(deviceID) 的运动数据：\(date)")
    }
    
    // MARK: - 时间段统计
    func averageSleepDuration(for deviceID: String, from startDate: Date, to endDate: Date) -> Double {
        let dict = loadPlist(for: deviceID)
        guard let sleepDict = dict["sleepData"] as? NSDictionary else { return 0 }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        var totalHours: Double = 0
        var count = 0
        
        for key in sleepDict.allKeys {
            guard let dateStr = key as? String,
                  let entry = sleepDict[dateStr] as? [String: Any],
                  let startStr = entry["start"] as? String,
                  let endStr = entry["end"] as? String,
                  let date = dateFormatter.date(from: dateStr),
                  date >= startDate && date <= endDate else { continue }
            
            let formatter = DateFormatter()
            formatter.dateFormat = "HH:mm"
            guard let start = formatter.date(from: startStr),
                  let end = formatter.date(from: endStr) else { continue }
            
            let interval: TimeInterval = (end > start) ? end.timeIntervalSince(start) : end.addingTimeInterval(86400).timeIntervalSince(start)
            
            totalHours += interval / 3600.0
            count += 1
        }
        
        return count > 0 ? totalHours / Double(count) : 0
    }
    
    func averageActivity(for deviceID: String, from startDate: Date, to endDate: Date) -> (steps: Double, calories: Double) {
        let dict = loadPlist(for: deviceID)
        guard let activityDict = dict["activityData"] as? NSDictionary else { return (0, 0) }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        var totalSteps = 0
        var totalCalories = 0
        var count = 0
        
        for key in activityDict.allKeys {
            guard let dateStr = key as? String,
                  let entry = activityDict[dateStr] as? [String: Any],
                  let steps = entry["steps"] as? Int,
                  let calories = entry["calories"] as? Int,
                  let date = dateFormatter.date(from: dateStr),
                  date >= startDate && date <= endDate else { continue }
            
            totalSteps += steps
            totalCalories += calories
            count += 1
        }
        
        return count > 0 ? (Double(totalSteps) / Double(count), Double(totalCalories) / Double(count)) : (0, 0)
    }
    
    // MARK: - 同步到服务器
    func plistToJSON(for deviceID: String) -> [String: Any]? {
        let dict = loadPlist(for: deviceID)
        return dict as? [String: Any]
    }
    
    func uploadPlistToServer(for deviceID: String, serverURL: URL) {
        guard let json = plistToJSON(for: deviceID),
              let data = try? JSONSerialization.data(withJSONObject: json) else {
            print("❌ 无法转换为 JSON")
            return
        }
        
        var request = URLRequest(url: serverURL)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = data
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("❌ 上传失败：\(error)")
            } else {
                print("✅ 上传成功")
            }
        }
        
        task.resume()
    }
    
    enum SyncTarget {
        case iCloud
        case server(URL)
    }
    
    func sync(for deviceID: String, to target: SyncTarget) {
        switch target {
        case .iCloud:
            print("📤 自动使用 iCloud 存储路径，无需额外处理。")
        case .server(let url):
            uploadPlistToServer(for: deviceID, serverURL: url)
        }
    }
    ///标记上传状态（用 Date）
    func markDataUploaded(for category: PlistCategory, date: Date) {
        guard let deviceID = deviceID else { return }
        let dict = loadPlist(for: deviceID)
        let dateKey = date.string()
        let categoryDict = dict[category.key] as? NSMutableDictionary ?? NSMutableDictionary()
        
        if var existing = categoryDict[dateKey] as? [String: Any] {
            existing["uploaded"] = true
            categoryDict[dateKey] = existing
            dict[category.key] = categoryDict
            savePlist(dict, for: deviceID)
        }
    }
    
} // END

enum PlistCategory: String {
    case sleepRecord = "sleepRecord"
    case stepArchive = "stepArchiveData"
    case training = "trainingData"
    case timingHeartRate = "timingHeartRate"
    case timingHRV = "timingHRV"
    case timingO2Record = "timingO2Record"
    case activity = "activityData"
    case sleepTemperature = "sleepTemperatureDataModel"
    case goMoreSleepData = "goMoreSleepData"
    case stressRecord = "stressRecord"
    
    var key: String {
        return self.rawValue
    }
}
protocol UploadedMarkable {
    var uploaded: Bool { get set }
}

extension PlistManager {
    func save<T: Codable>(data: T, for category: PlistCategory, date: Date) {
        guard let deviceID = deviceID else { return }

        let dict = loadPlist(for: deviceID)
        let dateKey = date.string()
        let categoryDict = dict[category.key] as? NSMutableDictionary ?? NSMutableDictionary()

        var newData = data
        // 保留 uploaded 字段
        if let existing = categoryDict[dateKey] as? [String: Any]{
//            if let json = try? JSONSerialization.data(withJSONObject: existing){
//                if  let decoded = try? CleanJSONDecoder().decode(T.self, from: json) {
                    if  var uploadData = data as? UploadedMarkable{
                        uploadData.uploaded = (existing["uploaded"] as? Bool) ?? false
//                        uploadedData.uploaded = (existing["uploaded"] as? Bool) ?? false
                        if let casted = uploadData as? T {
                            newData = casted
                        }
                    }
//                }
//            }
        }

        if let encoded = try? PropertyListEncoder().encode(newData),
           let entry = try? PropertyListSerialization.propertyList(from: encoded, format: nil) as? NSDictionary {
            categoryDict[dateKey] = entry
            dict[category.key] = categoryDict
            savePlist(dict, for: deviceID)
        }
    }
    
    func get(for category: PlistCategory) -> NSDictionary? {
        guard let deviceID = deviceID else { return nil }
        let dict = loadPlist(for: deviceID)
        guard let categoryDict = dict[category.key] as? NSDictionary else { return nil }
        return categoryDict
    }
    
    func get<T: Codable>(for category: PlistCategory, date: Date, as type: T.Type) -> T? {
        guard let deviceID = deviceID else { return nil }
        let dict = loadPlist(for: deviceID)
        guard let categoryDict = dict[category.key] as? NSDictionary else { return nil }

        let key = date.string()
        guard let entry = categoryDict[key] as? [String: Any],
              let data = try? JSONSerialization.data(withJSONObject: entry),
              let decoded = try? CleanJSONDecoder().decode(T.self, from: data) else {
            return nil
        }

        return decoded
    }
    
    
    func delete(for category: PlistCategory, date: Date) {
        guard let deviceID = deviceID else { return }

        let dateKey = date.string()
        let dict = loadPlist(for: deviceID)
        guard let categoryDict = dict[category.key] as? NSMutableDictionary else { return }

        categoryDict.removeObject(forKey: dateKey)
        dict[category.key] = categoryDict
        savePlist(dict, for: deviceID)
    }
    
    /// 保存或追加某天的数组数据
    func saveArray<T: Codable>(
        for category: PlistCategory,
        date: Date,
        dataArray: [T],
        merge: Bool = false // true 表示合并追加，false 表示覆盖
    ) {
        guard let deviceID = deviceID else { return }

        let dict = loadPlist(for: deviceID)
        let dateKey = date.string()
        let categoryDict = dict[category.key] as? NSMutableDictionary ?? NSMutableDictionary()

        var existingArray: [T] = []

        if merge,
           let rawArray = categoryDict[dateKey] as? [Any],
           let rawData = try? JSONSerialization.data(withJSONObject: rawArray),
           let decoded = try? PropertyListDecoder().decode([T].self, from: rawData) {
            existingArray = decoded
        }

        let finalArray = merge ? existingArray + dataArray : dataArray

        if let encoded = try? PropertyListEncoder().encode(finalArray),
           let plistArray = try? PropertyListSerialization.propertyList(from: encoded, format: nil) as? NSArray {
            categoryDict[dateKey] = plistArray
        }

        dict[category.key] = categoryDict
        savePlist(dict, for: deviceID)
    }
    
    func getArray<T: Codable>(for category: PlistCategory, date: Date, as type: T.Type) -> [T]? {
        guard let deviceID = deviceID else { return nil }
        let dict = loadPlist(for: deviceID)
        guard let categoryDict = dict[category.key] as? NSDictionary else { return nil }

        let key = date.string()
        guard let categoryDict = dict[category.key] as? NSDictionary,
              let entry = categoryDict[date.string()] as? [Any],
              let data = try? JSONSerialization.data(withJSONObject: entry),
              let result = try? PropertyListDecoder().decode([T].self, from: data) else {
            return []
        }

        return result
    }
}
