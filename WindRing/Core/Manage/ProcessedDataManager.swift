import Foundation
import CoreData

/// 处理后数据管理器
/// 负责管理从服务器获取的处理后数据，存储到本地数据库
public class ProcessedDataManager {
    // MARK: - 单例
    public static let shared = ProcessedDataManager()
    
    // MARK: - 属性
    private let storageManager: StorageManager
    
    // MARK: - 初始化
    private init() {
        self.storageManager = StorageManager.shared
    }
    
    // MARK: - 公共方法
    
    /// 保存处理后的心率数据
    /// - Parameters:
    ///   - value: 心率值
    ///   - userId: 用户ID
    ///   - deviceId: 设备ID
    ///   - timestamp: 时间戳
    ///   - confidence: 置信度
    /// - Returns: 是否保存成功
    @discardableResult
    public func saveProcessedHeartRateData(value: Int, userId: String, deviceId: String, timestamp: Date, confidence: Int = 100) -> Bool {
        let context = storageManager.viewContext()
        
        // 创建唯一ID
        let id = UUID().uuidString
        
        // 检查是否已存在相同时间戳的数据
        let fetchRequest: NSFetchRequest<HeartRateEntity> = HeartRateEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "userId == %@ AND timestamp == %@", userId, timestamp as NSDate)
        
        do {
            let existingData = try context.fetch(fetchRequest)
            if !existingData.isEmpty {
                // 更新现有数据
                let entity = existingData.first!
                entity.value = Int16(value)
                entity.setValue(confidence, forKey: "confidence")
                entity.setValue(true, forKey: "isProcessed")
                
                try context.save()
                return true
            }
        } catch {
            print("检查现有心率数据失败: \(error)")
            return false
        }
        
        // 创建新的心率数据实体
        let heartRateEntity = HeartRateEntity(context: context)
        heartRateEntity.id = id
        heartRateEntity.userId = userId
        heartRateEntity.deviceId = deviceId
        heartRateEntity.timestamp = timestamp
        heartRateEntity.value = Int16(value)
        heartRateEntity.setValue(confidence, forKey: "confidence")
        heartRateEntity.setValue(true, forKey: "isProcessed")
        
        // 保存上下文
        do {
            try context.save()
            return true
        } catch {
            print("保存处理后心率数据失败: \(error)")
            return false
        }
    }
    
    /// 保存处理后的血氧数据
    /// - Parameters:
    ///   - value: 血氧值
    ///   - userId: 用户ID
    ///   - deviceId: 设备ID
    ///   - timestamp: 时间戳
    ///   - confidence: 置信度
    /// - Returns: 是否保存成功
    @discardableResult
    public func saveProcessedBloodOxygenData(value: Double, userId: String, deviceId: String, timestamp: Date, confidence: Int = 100) -> Bool {
        let context = storageManager.viewContext()
        
        // 创建唯一ID
        let id = UUID().uuidString
        
        // 检查是否已存在相同时间戳的数据
        let fetchRequest: NSFetchRequest<BloodOxygenEntity> = BloodOxygenEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "userId == %@ AND timestamp == %@", userId, timestamp as NSDate)
        
        do {
            let existingData = try context.fetch(fetchRequest)
            if !existingData.isEmpty {
                // 更新现有数据
                let entity = existingData.first!
                entity.value = value
                entity.setValue(confidence, forKey: "confidence")
                entity.setValue(true, forKey: "isProcessed")
                
                try context.save()
                return true
            }
        } catch {
            print("检查现有血氧数据失败: \(error)")
            return false
        }
        
        // 创建新的血氧数据实体
        let bloodOxygenEntity = BloodOxygenEntity(context: context)
        bloodOxygenEntity.id = id
        bloodOxygenEntity.userId = userId
        bloodOxygenEntity.deviceId = deviceId
        bloodOxygenEntity.timestamp = timestamp
        bloodOxygenEntity.value = value
        bloodOxygenEntity.setValue(confidence, forKey: "confidence")
        bloodOxygenEntity.setValue(true, forKey: "isProcessed")
        
        // 保存上下文
        do {
            try context.save()
            return true
        } catch {
            print("保存处理后血氧数据失败: \(error)")
            return false
        }
    }
    
    /// 保存处理后的活动数据
    /// - Parameters:
    ///   - steps: 步数
    ///   - distance: 距离
    ///   - calories: 卡路里
    ///   - userId: 用户ID
    ///   - deviceId: 设备ID
    ///   - date: 日期
    /// - Returns: 是否保存成功
    @discardableResult
    public func saveProcessedActivityData(steps: Int, distance: Double, calories: Int, userId: String, deviceId: String, date: Date) -> Bool {
        let context = storageManager.viewContext()
        
        // 创建唯一ID
        let id = UUID().uuidString
        
        // 检查是否已存在相同日期的数据
        let fetchRequest: NSFetchRequest<StepsEntity> = StepsEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "userId == %@ AND date == %@", userId, date as NSDate)
        
        do {
            let existingData = try context.fetch(fetchRequest)
            if !existingData.isEmpty {
                // 更新现有数据
                let entity = existingData.first!
                entity.value = Int32(steps)
                entity.distance = distance
                entity.calories = Int32(calories)
                entity.setValue(true, forKey: "isProcessed")
                
                try context.save()
                return true
            }
        } catch {
            print("检查现有活动数据失败: \(error)")
            return false
        }
        
        // 创建新的活动数据实体
        let stepsEntity = StepsEntity(context: context)
        stepsEntity.id = id
        stepsEntity.userId = userId
        stepsEntity.deviceId = deviceId
        stepsEntity.date = date
        stepsEntity.timestamp = date
        stepsEntity.value = Int32(steps)
        stepsEntity.distance = distance
        stepsEntity.calories = Int32(calories)
        stepsEntity.setValue(true, forKey: "isProcessed")
        
        // 保存上下文
        do {
            try context.save()
            return true
        } catch {
            print("保存处理后活动数据失败: \(error)")
            return false
        }
    }
    
    /// 获取处理后的心率数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 处理后的心率数据列表
    public func getProcessedHeartRateData(userId: String, startDate: Date, endDate: Date) -> [HeartRateEntity] {
        let fetchRequest: NSFetchRequest<HeartRateEntity> = HeartRateEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "userId == %@ AND isProcessed == %@ AND timestamp >= %@ AND timestamp <= %@", 
                                            userId, NSNumber(value: true), startDate as NSDate, endDate as NSDate)
        fetchRequest.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
        
        do {
            return try storageManager.viewContext().fetch(fetchRequest)
        } catch {
            print("获取处理后的心率数据失败: \(error)")
            return []
        }
    }
    
    /// 获取处理后的血氧数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 处理后的血氧数据列表
    public func getProcessedBloodOxygenData(userId: String, startDate: Date, endDate: Date) -> [BloodOxygenEntity] {
        let fetchRequest: NSFetchRequest<BloodOxygenEntity> = BloodOxygenEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "userId == %@ AND isProcessed == %@ AND timestamp >= %@ AND timestamp <= %@", 
                                            userId, NSNumber(value: true), startDate as NSDate, endDate as NSDate)
        fetchRequest.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
        
        do {
            return try storageManager.viewContext().fetch(fetchRequest)
        } catch {
            print("获取处理后的血氧数据失败: \(error)")
            return []
        }
    }
    
    /// 获取处理后的活动数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 处理后的活动数据列表
    public func getProcessedActivityData(userId: String, startDate: Date, endDate: Date) -> [StepsEntity] {
        let fetchRequest: NSFetchRequest<StepsEntity> = StepsEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "userId == %@ AND isProcessed == %@ AND date >= %@ AND date <= %@", 
                                            userId, NSNumber(value: true), startDate as NSDate, endDate as NSDate)
        fetchRequest.sortDescriptors = [NSSortDescriptor(key: "date", ascending: true)]
        
        do {
            return try storageManager.viewContext().fetch(fetchRequest)
        } catch {
            print("获取处理后的活动数据失败: \(error)")
            return []
        }
    }
}
