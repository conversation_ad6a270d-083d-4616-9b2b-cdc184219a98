import Foundation
import CoreData

/// 应用设置
public class SettingsObject: NSManagedObject {
    /// 创建设置对象
    /// - Parameters:
    ///   - context: 托管对象上下文
    /// - Returns: 新创建的设置对象
    static func create(in context: NSManagedObjectContext) -> SettingsObject {
        let settings = SettingsObject(context: context)
        settings.settings = [:]
        return settings
    }
}

/// 设置实体扩展
extension SettingsObject {
    @nonobjc public class func fetchRequest() -> NSFetchRequest<SettingsObject> {
        return NSFetchRequest<SettingsObject>(entityName: "Settings")
    }
    
    /// 设置字典 - 使用值转换器来处理字典类型
    @NSManaged public var settings: [String: String]
}

// MARK: - 设置键
extension SettingsObject {
    /// 设置键枚举
    enum SettingKey: String {
        /// 自动同步间隔（分钟）
        case autoSyncInterval = "autoSyncInterval"
        
        /// 是否显示详细调试信息
        case showDebugInfo = "showDebugInfo"
        
        /// 是否启用实验性功能
        case enableExperimentalFeatures = "enableExperimentalFeatures"
    }
    
    /// 获取设置值
    /// - Parameter key: 设置键
    /// - Returns: 设置值，如果不存在则为nil
    func getValue(for key: SettingKey) -> String? {
        return settings[key.rawValue]
    }
    
    /// 设置值
    /// - Parameters:
    ///   - value: 值
    ///   - key: 设置键
    func setValue(_ value: String, for key: SettingKey) {
        var currentSettings = settings
        currentSettings[key.rawValue] = value
        settings = currentSettings
    }
} 