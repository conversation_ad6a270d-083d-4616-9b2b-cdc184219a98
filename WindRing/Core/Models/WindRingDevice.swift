import Foundation
import CoreBluetooth

/// 表示WindRing设备的模型类
class WindRingDevice {
    /// 设备标识符
    var deviceID: String?
    
    /// 设备名称
    var name: String
    
    /// 设备MAC地址
    var macAddress: String?
    
    /// 设备固件版本
    var firmwareVersion: String?
    
    /// 电池电量
    var batteryLevel: Int = 0
    
    /// 系统蓝牙标识符
    var identifier: UUID?
    
    /// 是否已连接
    var isConnected: Bool = false
    
    /// 创建一个新的设备
    /// - Parameters:
    ///   - deviceID: 设备标识符
    ///   - name: 设备名称
    ///   - macAddress: MAC地址
    init(deviceID: String? = nil, name: String, macAddress: String? = nil) {
        self.deviceID = deviceID
        self.name = name
        self.macAddress = macAddress
        
        // 尝试将deviceID转换为UUID
        if let deviceID = deviceID {
            self.identifier = UUID(uuidString: deviceID)
        }
    }
    
    /// 从蓝牙外设创建设备
    /// - Parameter peripheral: 蓝牙外设
    convenience init(peripheral: CBPeripheral) {
        self.init(
            deviceID: peripheral.identifier.uuidString,
            name: peripheral.name ?? "未知设备"
        )
        self.identifier = peripheral.identifier
    }
}

// MARK: - Equatable 协议实现
extension WindRingDevice: Equatable {
    static func == (lhs: WindRingDevice, rhs: WindRingDevice) -> Bool {
        if let lhsID = lhs.deviceID, let rhsID = rhs.deviceID {
            return lhsID == rhsID
        } else if let lhsMAC = lhs.macAddress, let rhsMAC = rhs.macAddress {
            return lhsMAC == rhsMAC
        } else {
            return false
        }
    }
}

// MARK: - Hashable 协议实现
extension WindRingDevice: Hashable {
    func hash(into hasher: inout Hasher) {
        if let deviceID = deviceID {
            hasher.combine(deviceID)
        } else if let macAddress = macAddress {
            hasher.combine(macAddress)
        } else {
            hasher.combine(name)
        }
    }
} 