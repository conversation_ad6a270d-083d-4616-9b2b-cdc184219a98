import Foundation

// 通知中心扩展
extension Notification.Name {
    // 设备连接状态通知
    static let deviceConnected = Notification.Name("deviceConnected")
    static let deviceConnecting = Notification.Name("deviceConnecting")
    static let deviceDisconnected = Notification.Name("deviceDisconnected")
    static let deviceSyncing = Notification.Name("deviceSyncing")
    static let deviceSyncCompleted = Notification.Name("deviceSyncCompleted")
    
    // 蓝牙状态通知
    // 注释掉重复的声明，已在BluetoothSystemDeviceManager.swift中定义
    // static let bluetoothStateChanged = Notification.Name("bluetoothStateChanged")
    
    // 测量结果通知
    static let heartRateMeasured = Notification.Name("heartRateMeasured")
    static let autoHeartRateMeasured = Notification.Name("autoHeartRateMeasured")
    static let bloodOxygenMeasured = Notification.Name("bloodOxygenMeasured")
    static let spO2Measured = Notification.Name("spO2Measured")
    static let temperatureMeasured = Notification.Name("temperatureMeasured")
    static let bloodPressureMeasured = Notification.Name("bloodPressureMeasured")
    static let hrvMeasured = Notification.Name("hrvMeasured")
    static let stressMeasured = Notification.Name("stressMeasured")
    
    // 活动和佩戴状态通知
    static let stepsUpdated = Notification.Name("stepsUpdated")
    static let wearStateChanged = Notification.Name("wearStateChanged")
    
    // 其他通知
    static let settingsChanged = Notification.Name("settingsChanged")
    static let userProfileUpdated = Notification.Name("userProfileUpdated")
    
    // GoMore算法相关通知
    static let goMoreSupportUpdated = Notification.Name("goMoreSupportUpdated")
    static let goMoreKeyUpdated = Notification.Name("goMoreKeyUpdated")
    static let goMoreChipIDUpdated = Notification.Name("goMoreChipIDUpdated")
    static let deviceStatusUpdated = Notification.Name("deviceStatusUpdated")
    
    // 应用通知
    static let appPreferencesChanged = Notification.Name("appPreferencesChanged")
    
    // 固件类型相关
    static let firmwareTypeUpdated = Notification.Name("firmwareTypeUpdated")

    // 睡眠算法相关
    static let sleepAlgorithmChanged = Notification.Name("sleepAlgorithmChanged")
    
    // 数据同步和上传相关
    static let heartRateDataUploaded = Notification.Name("heartRateDataUploaded")
    static let bloodOxygenDataUploaded = Notification.Name("bloodOxygenDataUploaded")
    static let temperatureDataUploaded = Notification.Name("temperatureDataUploaded")
    static let stressDataUploaded = Notification.Name("stressDataUploaded")
    static let hrvDataUploaded = Notification.Name("hrvDataUploaded")
    static let hrvDataSynced = Notification.Name("hrvDataSynced")
    static let sleepDataUploaded = Notification.Name("com.windring.sleepDataUploaded")
    
    // 温度数据相关
    static let temperatureDataFetched = Notification.Name("temperatureDataFetched")
    static let temperatureDataUploadResponse = Notification.Name("temperatureDataUploadResponse")
    
    // 睡眠数据相关
    static let sleepDataSyncStarted = Notification.Name("com.windring.sleepDataSyncStarted")
    static let sleepDataUploadResponse = Notification.Name("com.windring.sleepDataUploadResponse")
    
    // 网络状态相关
    static let networkStatusChanged = Notification.Name("networkStatusChanged")
    
    // GoMore通知
    static let receivedGoMoreSleepIdsNotification = Notification.Name("receivedGoMoreSleepIds")
    static let receivedGoMoreSleepDetailNotification = Notification.Name("receivedGoMoreSleepDetail")
} 