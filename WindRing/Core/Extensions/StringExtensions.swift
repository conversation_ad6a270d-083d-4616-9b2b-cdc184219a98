// StringExtensions.swift - Copyright 2025 SwifterSwift

#if canImport(Foundation)
import Foundation
#endif

#if canImport(UIKit)
import UIKit
#endif

#if canImport(AppKit)
import AppKit
#endif

#if canImport(CoreGraphics)
import CoreGraphics
#endif

// MARK: - Properties

public extension String {
    #if canImport(Foundation)
    /// SwifterSwift: 从base64解码的字符串（如果适用）。
    ///
    ///        "SGVsbG8gV29ybGQh".base64Decoded = Optional("Hello World!")
    ///
    var base64Decoded: String? {
        if let data = Data(base64Encoded: self,
                           options: .ignoreUnknownCharacters) {
            return String(data: data, encoding: .utf8)
        }

        let remainder = count % 4

        var padding = ""
        if remainder > 0 {
            padding = String(repeating: "=", count: 4 - remainder)
        }

        guard let data = Data(base64Encoded: self + padding,
                              options: .ignoreUnknownCharacters) else { return nil }

        return String(data: data, encoding: .utf8)
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 编码为base64的字符串（如果适用）。
    ///
    ///        "Hello World!".base64Encoded -> Optional("SGVsbG8gV29ybGQh")
    ///
    var base64Encoded: String? {
        // https://github.com/Reza-Rg/Base64-Swift-Extension/blob/master/Base64.swift
        let plainData = data(using: .utf8)
        return plainData?.base64EncodedString()
    }
    #endif

    /// SwifterSwift: 字符串的字符数组。
    var charactersArray: [Character] {
        return Array(self)
    }

    #if canImport(Foundation)
    /// SwifterSwift: 字符串的驼峰式写法。
    ///
    ///        "sOme vAriable naMe".camelCased -> "someVariableName"
    ///
    var camelCased: String {
        let source = lowercased()
        let first = source[..<source.index(after: source.startIndex)]
        if source.contains(" ") {
            let connected = source.capitalized.replacingOccurrences(of: " ", with: "")
            let camel = connected.replacingOccurrences(of: "\n", with: "")
            let rest = String(camel.dropFirst())
            return first + rest
        }
        let rest = String(source.dropFirst())
        return first + rest
    }
    #endif

    /// SwifterSwift: 检查字符串是否包含一个或多个表情符号。
    ///
    ///        "Hello 😀".containEmoji -> true
    ///
    var containEmoji: Bool {
        // http://stackoverflow.com/questions/30757193/find-out-if-character-in-string-is-emoji
        for scalar in unicodeScalars {
            switch scalar.value {
            case 0x1F600...0x1F64F, // 表情符号
                 0x1F300...0x1F5FF, // 杂项符号和图形
                 0x1F680...0x1F6FF, // 交通和地图
                 0x1F1E6...0x1F1FF, // 区域国家旗帜
                 0x2600...0x26FF, // 杂项符号
                 0x2700...0x27BF, // 装饰符号
                 0xE0020...0xE007F, // 标签
                 0xFE00...0xFE0F, // 变体选择器
                 0x1F900...0x1F9FF, // 补充符号和图形
                 127_000...127_600, // 各种亚洲字符
                 65024...65039, // 变体选择器
                 9100...9300, // 杂项项目
                 8400...8447: // 组合变音标记符号
                return true
            default:
                continue
            }
        }
        return false
    }

    /// SwifterSwift: 字符串的第一个字符（如果适用）。
    ///
    ///        "Hello".firstCharacterAsString -> Optional("H")
    ///        "".firstCharacterAsString -> nil
    ///
    var firstCharacterAsString: String? {
        guard let first else { return nil }
        return String(first)
    }

    /// SwifterSwift: 检查字符串是否包含一个或多个字母。
    ///
    ///        "123abc".hasLetters -> true
    ///        "123".hasLetters -> false
    ///
    var hasLetters: Bool {
        return rangeOfCharacter(from: .letters, options: .numeric, range: nil) != nil
    }

    /// SwifterSwift: 检查字符串是否包含一个或多个数字。
    ///
    ///        "abcd".hasNumbers -> false
    ///        "123abc".hasNumbers -> true
    ///
    var hasNumbers: Bool {
        return rangeOfCharacter(from: .decimalDigits, options: .literal, range: nil) != nil
    }

    /// SwifterSwift: 检查字符串是否只包含字母。
    ///
    ///        "abc".isAlphabetic -> true
    ///        "123abc".isAlphabetic -> false
    ///
    var isAlphabetic: Bool {
        let hasLetters = rangeOfCharacter(from: .letters, options: .numeric, range: nil) != nil
        let hasNumbers = rangeOfCharacter(from: .decimalDigits, options: .literal, range: nil) != nil
        return hasLetters && !hasNumbers
    }

    /// SwifterSwift: 检查字符串是否至少包含一个字母和一个数字。
    ///
    ///        // 对密码很有用
    ///        "123abc".isAlphaNumeric -> true
    ///        "abc".isAlphaNumeric -> false
    ///
    var isAlphaNumeric: Bool {
        let hasLetters = rangeOfCharacter(from: .letters, options: .numeric, range: nil) != nil
        let hasNumbers = rangeOfCharacter(from: .decimalDigits, options: .literal, range: nil) != nil
        let comps = components(separatedBy: .alphanumerics)
        return comps.joined(separator: "").count == 0 && hasLetters && hasNumbers
    }

    /// SwifterSwift: 检查字符串是否是回文。
    ///
    ///     "abcdcba".isPalindrome -> true
    ///     "Mom".isPalindrome -> true
    ///     "A man a plan a canal, Panama!".isPalindrome -> true
    ///     "Mama".isPalindrome -> false
    ///
    var isPalindrome: Bool {
        let letters = filter(\.isLetter)
        guard !letters.isEmpty else { return false }
        let midIndex = letters.index(letters.startIndex, offsetBy: letters.count / 2)
        let firstHalf = letters[letters.startIndex..<midIndex]
        let secondHalf = letters[midIndex..<letters.endIndex].reversed()
        return !zip(firstHalf, secondHalf).contains(where: { $0.lowercased() != $1.lowercased() })
    }

    #if canImport(Foundation)
    /// SwifterSwift: 检查字符串是否是有效的电子邮件格式。
    ///
    /// - 注意：此属性不会对电子邮件地址进行验证。它只是尝试确定其格式是否适合电子邮件地址。
    ///
    ///        "<EMAIL>".isValidEmail -> true
    ///
    var isValidEmail: Bool {
        // http://emailregex.com/
        let regex =
            "^(?:[\\p{L}0-9!#$%\\&'*+/=?\\^_`{|}~-]+(?:\\.[\\p{L}0-9!#$%\\&'*+/=?\\^_`{|}~-]+)*|\"(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21\\x23-\\x5b\\x5d-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])*\")@(?:(?:[\\p{L}0-9](?:[a-z0-9-]*[\\p{L}0-9])?\\.)+[\\p{L}0-9](?:[\\p{L}0-9-]*[\\p{L}0-9])?|\\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[\\p{L}0-9-]*[\\p{L}0-9]:(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21-\\x5a\\x53-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])+)\\])$"
        return range(of: regex, options: .regularExpression, range: nil, locale: nil) != nil
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 检查字符串是否是有效的URL。
    ///
    ///        "https://google.com".isValidUrl -> true
    ///
    var isValidUrl: Bool {
        return URL(string: self) != nil
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 检查字符串是否是有效的带方案的URL。
    ///
    ///        "https://google.com".isValidSchemedUrl -> true
    ///        "google.com".isValidSchemedUrl -> false
    ///
    var isValidSchemedUrl: Bool {
        guard let url = URL(string: self) else { return false }
        return url.scheme != nil
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 检查字符串是否是有效的https URL。
    ///
    ///        "https://google.com".isValidHttpsUrl -> true
    ///
    var isValidHttpsUrl: Bool {
        guard let url = URL(string: self) else { return false }
        return url.scheme == "https"
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 检查字符串是否是有效的http URL。
    ///
    ///        "http://google.com".isValidHttpUrl -> true
    ///
    var isValidHttpUrl: Bool {
        guard let url = URL(string: self) else { return false }
        return url.scheme == "http"
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 检查字符串是否是有效的文件URL。
    ///
    ///        "file://Documents/file.txt".isValidFileUrl -> true
    ///
    var isValidFileUrl: Bool {
        return URL(string: self)?.isFileURL ?? false
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 检查字符串是否是有效的Swift数字。注意：在北美，"."是小数分隔符，
    /// 而在欧洲很多地区使用","。
    ///
    ///        "123".isNumeric -> true
    ///     "1.3".isNumeric -> true (en_US)
    ///     "1,3".isNumeric -> true (fr_FR)
    ///        "abc".isNumeric -> false
    ///
    var isNumeric: Bool {
        let scanner = Scanner(string: self)
        scanner.locale = NSLocale.current
        #if os(Linux) || os(Android) || targetEnvironment(macCatalyst)
        return scanner.scanDecimal() != nil && scanner.isAtEnd
        #else
        return scanner.scanDecimal(nil) && scanner.isAtEnd
        #endif
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 检查字符串是否只包含数字。
    ///
    ///     "123".isDigits -> true
    ///     "1.3".isDigits -> false
    ///     "abc".isDigits -> false
    ///
    var isDigits: Bool {
        return CharacterSet.decimalDigits.isSuperset(of: CharacterSet(charactersIn: self))
    }
    #endif

    /// SwifterSwift: 字符串的最后一个字符（如果适用）。
    ///
    ///        "Hello".lastCharacterAsString -> Optional("o")
    ///        "".lastCharacterAsString -> nil
    ///
    var lastCharacterAsString: String? {
        guard let last else { return nil }
        return String(last)
    }

    #if canImport(Foundation)
    /// SwifterSwift: 拉丁化的字符串。
    ///
    ///        "Hèllö Wórld!".latinized -> "Hello World!"
    ///
    var latinized: String {
        return folding(options: .diacriticInsensitive, locale: Locale.current)
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 字符串的布尔值（如果适用）。
    ///
    ///        "1".bool -> true
    ///        "False".bool -> false
    ///        "Hello".bool = nil
    ///
    var bool: Bool? {
        let selfLowercased = trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        switch selfLowercased {
        case "true", "yes", "1":
            return true
        case "false", "no", "0":
            return false
        default:
            return nil
        }
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 从"yyyy-MM-dd"格式的字符串创建Date对象。
    ///
    ///        "2007-06-29".date -> Optional(Date)
    ///
    var date: Date? {
        let selfLowercased = trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        let formatter = DateFormatter()
        formatter.timeZone = TimeZone.current
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.date(from: selfLowercased)
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 从"yyyy-MM-dd HH:mm:ss"格式的字符串创建Date对象。
    ///
    ///        "2007-06-29 14:23:09".dateTime -> Optional(Date)
    ///
    var dateTime: Date? {
        let selfLowercased = trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        let formatter = DateFormatter()
        formatter.timeZone = TimeZone.current
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.date(from: selfLowercased)
    }
    #endif

    /// SwifterSwift: 字符串的整数值（如果适用）。
    ///
    ///        "101".int -> 101
    ///
    var int: Int? {
        return Int(self)
    }

    /// SwifterSwift: 给定长度的Lorem ipsum字符串。
    ///
    /// - 参数 length: 限制lorem ipsum字符数的数量（默认为445 - 完整的lorem ipsum）。
    /// - 返回: Lorem ipsum dolor sit amet...字符串。
    static func loremIpsum(ofLength length: Int = 445) -> String {
        guard length > 0 else { return "" }

        // https://www.lipsum.com/
        let loremIpsum = """
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
        """
        if loremIpsum.count > length {
            return String(loremIpsum[loremIpsum.startIndex..<loremIpsum.index(loremIpsum.startIndex, offsetBy: length)])
        }
        return loremIpsum
    }

    #if canImport(Foundation)
    /// SwifterSwift: 从字符串创建URL（如果适用）。
    ///
    ///        "https://google.com".url -> URL(string: "https://google.com")
    ///        "not url".url -> nil
    ///
    var url: URL? {
        return URL(string: self)
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 去除开头和结尾的空格和换行符的字符串。
    ///
    ///        "   hello  \n".trimmed -> "hello"
    ///
    var trimmed: String {
        return trimmingCharacters(in: .whitespacesAndNewlines)
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 从URL字符串解码为可读字符串。
    ///
    ///        "it's%20easy%20to%20decode%20strings".urlDecoded -> "it's easy to decode strings"
    ///
    var urlDecoded: String {
        return removingPercentEncoding ?? self
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: URL转义字符串。
    ///
    ///        "it's easy to encode strings".urlEncoded -> "it's%20easy%20to%20encode%20strings"
    ///
    var urlEncoded: String {
        return addingPercentEncoding(withAllowedCharacters: .urlHostAllowed)!
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 用于在正则表达式模式中包含的转义字符串。
    ///
    /// "hello ^$ there" -> "hello \\^\\$ there"
    ///
    var regexEscaped: String {
        return NSRegularExpression.escapedPattern(for: self)
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 没有空格和换行符的字符串。
    ///
    ///        "   \n Swifter   \n  Swift  ".withoutSpacesAndNewLines -> "SwifterSwift"
    ///
    var withoutSpacesAndNewLines: String {
        return replacingOccurrences(of: " ", with: "").replacingOccurrences(of: "\n", with: "")
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 检查给定的字符串是否只包含空格。
    var isWhitespace: Bool {
        return trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    #endif

    #if os(iOS) || os(tvOS)
    /// SwifterSwift: 检查给定的字符串拼写是否正确。
    @MainActor
    var isSpelledCorrectly: Bool {
        let checker = UITextChecker()
        let range = NSRange(startIndex..<endIndex, in: self)

        let misspelledRange = checker.rangeOfMisspelledWord(
            in: self,
            range: range,
            startingAt: 0,
            wrap: false,
            language: Locale.preferredLanguages.first ?? "en")
        return misspelledRange.location == NSNotFound
    }
    #endif
}

// MARK: - Methods

public extension String {
    #if canImport(Foundation)
    /// SwifterSwift: 从字符串获取Float值（如果适用）。
    ///
    /// - 参数 locale: 区域设置（默认为Locale.current）
    /// - 返回: 从给定字符串获取的可选Float值。
    func float(locale: Locale = .current) -> Float? {
        let formatter = NumberFormatter()
        formatter.locale = locale
        formatter.allowsFloats = true
        return formatter.number(from: self)?.floatValue
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 从字符串获取Double值（如果适用）。
    ///
    /// - 参数 locale: 区域设置（默认为Locale.current）
    /// - 返回: 从给定字符串获取的可选Double值。
    func double(locale: Locale = .current) -> Double? {
        let formatter = NumberFormatter()
        formatter.locale = locale
        formatter.allowsFloats = true
        return formatter.number(from: self)?.doubleValue
    }
    #endif

    #if canImport(CoreGraphics) && canImport(Foundation)
    /// SwifterSwift: 从字符串获取CGFloat值（如果适用）。
    ///
    /// - 参数 locale: 区域设置（默认为Locale.current）
    /// - 返回: 从给定字符串获取的可选CGFloat值。
    func cgFloat(locale: Locale = .current) -> CGFloat? {
        let formatter = NumberFormatter()
        formatter.locale = locale
        formatter.allowsFloats = true
        return formatter.number(from: self) as? CGFloat
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 由换行符分隔的字符串数组。
    ///
    ///        "Hello\ntest".lines() -> ["Hello", "test"]
    ///
    /// - 返回: 由换行符分隔的字符串。
    func lines() -> [String] {
        var result = [String]()
        enumerateLines { line, _ in
            result.append(line)
        }
        return result
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 返回一个本地化的字符串，可选择为翻译人员提供注释。
    ///
    ///        "Hello world".localized() -> Hallo Welt
    ///
    /// - 参数:
    ///   - tableName: 包含键值对的表的名称。也是用于存储本地化字符串的文件的后缀
    /// （带有.strings扩展名的文件）。当tableName为nil或空字符串时，默认为`Localizable.strings`中的表。
    ///   - bundle: 包含表的字符串文件的bundle。如果未指定bundle，则使用主bundle。
    ///   - value: 开发区域设置的本地化字符串。对于其他区域设置，如果表中找不到键，则返回此值。
    ///   - comment: 字符串文件中键值对上方的注释。此参数为翻译人员提供了有关本地化字符串向用户呈现的一些上下文。
    /// - 返回: 本地化字符串。有关详细信息，请参阅`NSLocalizedString()`API的Xcode文档。
    func localized(
        tableName: String? = nil,
        bundle: Bundle = Bundle.main,
        value: String = "",
        comment: String = "") -> String {
        return NSLocalizedString(self, tableName: tableName, bundle: bundle, value: value, comment: comment)
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 返回一个格式化的本地化字符串。
    ///
    ///    "%d Swift %d Objective-C".formatLocalized(1, 2) -> 1 Swift 2 Objective-C
    ///
    /// - 参数:
    ///   - comment: 翻译人员的可选注释。
    ///   - arguments: 格式使用的参数。
    /// - 返回: 格式化的本地化字符串。
    func formatLocalized(comment: String = "", _ arguments: (any CVarArg)...) -> String {
        let format = NSLocalizedString(self, comment: comment)
        return String(format: format, arguments: arguments)
    }
    #endif

    /// SwifterSwift: 字符串中最常见的字符。
    ///
    ///        "This is a test, since e is appearing everywhere e should be the common character".mostCommonCharacter() -> "e"
    ///
    /// - 返回: 最常见的字符。
    func mostCommonCharacter() -> Character? {
        let mostCommon = withoutSpacesAndNewLines.reduce(into: [Character: Int]()) {
            let count = $0[$1] ?? 0
            $0[$1] = count + 1
        }.max { $0.1 < $1.1 }?.key

        return mostCommon
    }

    /// SwifterSwift: 字符串中所有字符的Unicode数组。
    ///
    ///        "SwifterSwift".unicodeArray() -> [83, 119, 105, 102, 116, 101, 114, 83, 119, 105, 102, 116]
    ///
    /// - 返回: 字符串中所有字符的Unicode。
    func unicodeArray() -> [Int] {
        return unicodeScalars.map { Int($0.value) }
    }

    #if canImport(Foundation)
    /// SwifterSwift: 字符串中所有单词的数组。
    ///
    ///        "Swift is amazing".words() -> ["Swift", "is", "amazing"]
    ///
    /// - 返回: 字符串中包含的单词。
    func words() -> [String] {
        // https://stackoverflow.com/questions/42822838
        let characterSet = CharacterSet.whitespacesAndNewlines.union(.punctuationCharacters)
        let comps = components(separatedBy: characterSet)
        return comps.filter { !$0.isEmpty }
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 字符串中单词的数量。
    ///
    ///        "Swift is amazing".wordsCount() -> 3
    ///
    /// - 返回: 字符串中包含的单词数量。
    func wordCount() -> Int {
        // https://stackoverflow.com/questions/42822838
        let characterSet = CharacterSet.whitespacesAndNewlines.union(.punctuationCharacters)
        let comps = components(separatedBy: characterSet)
        let words = comps.filter { !$0.isEmpty }
        return words.count
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 将字符串转换为slug格式。
    ///
    ///        "Swift is amazing".toSlug() -> "swift-is-amazing"
    ///
    /// - 返回: slug格式的字符串。
    func toSlug() -> String {
        let lowercased = lowercased()
        let latinized = lowercased.folding(options: .diacriticInsensitive, locale: Locale.current)
        let withDashes = latinized.replacingOccurrences(of: " ", with: "-")

        let alphanumerics = NSCharacterSet.alphanumerics
        var filtered = withDashes.filter {
            guard String($0) != "-" else { return true }
            guard String($0) != "&" else { return true }
            return String($0).rangeOfCharacter(from: alphanumerics) != nil
        }

        while filtered.lastCharacterAsString == "-" {
            filtered = String(filtered.dropLast())
        }

        while filtered.firstCharacterAsString == "-" {
            filtered = String(filtered.dropFirst())
        }

        return filtered.replacingOccurrences(of: "--", with: "-")
    }
    #endif

    /// SwifterSwift: 安全地用索引访问字符串。
    ///
    ///        "Hello World!"[safe: 3] -> "l"
    ///        "Hello World!"[safe: 20] -> nil
    ///
    /// - 参数 index: 索引。
    subscript(safe index: Int) -> Character? {
        guard index >= 0, index < count else { return nil }
        return self[self.index(startIndex, offsetBy: index)]
    }

    /// SwifterSwift: 安全地在给定范围内访问字符串。
    ///
    ///        "Hello World!"[safe: 6..<11] -> "World"
    ///        "Hello World!"[safe: 21..<110] -> nil
    ///
    /// - 参数 range: 范围表达式。
    subscript(safe range: Range<Int>) -> String? {
        guard range.lowerBound >= 0,
              range.upperBound <= count else {
            return nil
        }
        let startIndex = index(self.startIndex, offsetBy: range.lowerBound)
        let endIndex = index(self.startIndex, offsetBy: range.upperBound)
        return String(self[startIndex..<endIndex])
    }

    /// SwifterSwift: 安全地在给定范围内访问字符串。
    ///
    ///        "Hello World!"[safe: 6...11] -> "World!"
    ///        "Hello World!"[safe: 21...110] -> nil
    ///
    /// - 参数 range: 范围表达式。
    subscript(safe range: ClosedRange<Int>) -> String? {
        guard range.lowerBound >= 0,
              range.upperBound < count else {
            return nil
        }
        let startIndex = index(self.startIndex, offsetBy: range.lowerBound)
        let endIndex = index(self.startIndex, offsetBy: range.upperBound + 1)
        return String(self[startIndex..<endIndex])
    }

    /// SwifterSwift: 安全地在给定范围内访问字符串。
    ///
    ///        "Hello World!"[safe: ..<5] -> "Hello"
    ///        "Hello World!"[safe: ..<(-110)] -> nil
    ///
    /// - 参数 range: 范围表达式。
    subscript(safe range: PartialRangeUpTo<Int>) -> String? {
        guard range.upperBound >= 0,
              range.upperBound <= count else {
            return nil
        }
        let endIndex = index(self.startIndex, offsetBy: range.upperBound)
        return String(self[startIndex..<endIndex])
    }

    /// SwifterSwift: 安全地在给定范围内访问字符串。
    ///
    ///        "Hello World!"[safe: ...10] -> "Hello World"
    ///        "Hello World!"[safe: ...110] -> nil
    ///
    /// - 参数 range: 范围表达式。
    subscript(safe range: PartialRangeThrough<Int>) -> String? {
        guard range.upperBound >= 0,
              range.upperBound < count else {
            return nil
        }
        let endIndex = index(self.startIndex, offsetBy: range.upperBound + 1)
        return String(self[startIndex..<endIndex])
    }

    /// SwifterSwift: 安全地在给定范围内访问字符串。
    ///
    ///        "Hello World!"[safe: 6...] -> "World!"
    ///        "Hello World!"[safe: 50...] -> nil
    ///
    /// - 参数 range: 范围表达式。
    subscript(safe range: PartialRangeFrom<Int>) -> String? {
        guard range.lowerBound >= 0,
              range.lowerBound < count else {
            return nil
        }
        let startIndex = index(self.startIndex, offsetBy: range.lowerBound)
        return String(self[startIndex...])
    }

    #if os(iOS) || os(macOS)
    /// SwifterSwift: 将字符串复制到全局粘贴板。
    ///
    ///        "SomeText".copyToPasteboard() // 复制"SomeText"到粘贴板
    ///
    func copyToPasteboard() {
        #if os(iOS)
        UIPasteboard.general.string = self
        #elseif os(macOS)
        NSPasteboard.general.clearContents()
        NSPasteboard.general.setString(self, forType: .string)
        #endif
    }
    #endif

    /// SwifterSwift: 将字符串格式转换为驼峰式。
    ///
    ///        var str = "sOme vaRiabLe Name"
    ///        str.camelize()
    ///        print(str) // 打印 "someVariableName"
    ///
    @discardableResult
    mutating func camelize() -> String {
        let source = lowercased()
        let first = source[..<source.index(after: source.startIndex)]
        if source.contains(" ") {
            let connected = source.capitalized.replacingOccurrences(of: " ", with: "")
            let camel = connected.replacingOccurrences(of: "\n", with: "")
            let rest = String(camel.dropFirst())
            self = first + rest
            return self
        }
        let rest = String(source.dropFirst())

        self = first + rest
        return self
    }

    /// SwifterSwift: 字符串的第一个字符大写（如果适用），同时保持原始字符串。
    ///
    ///        "hello world".firstCharacterUppercased() -> "Hello world"
    ///        "".firstCharacterUppercased() -> ""
    ///
    mutating func firstCharacterUppercased() {
        guard let first else { return }
        self = String(first).uppercased() + dropFirst()
    }

    /// SwifterSwift: 检查字符串是否只包含唯一字符。
    ///
    func hasUniqueCharacters() -> Bool {
        guard count > 0 else { return false }
        var uniqueChars = Set<String>()
        for char in self {
            if uniqueChars.contains(String(char)) { return false }
            uniqueChars.insert(String(char))
        }
        return true
    }

    #if canImport(Foundation)
    /// SwifterSwift: 检查字符串是否包含一个或多个子字符串的实例。
    ///
    ///        "Hello World!".contain("O") -> false
    ///        "Hello World!".contain("o", caseSensitive: false) -> true
    ///
    /// - 参数:
    ///   - string: 要搜索的子字符串。
    ///   - caseSensitive: 是否区分大小写（默认为true）。
    /// - 返回: 如果字符串包含一个或多个子字符串实例，则为true。
    func contains(_ string: String, caseSensitive: Bool = true) -> Bool {
        if !caseSensitive {
            return range(of: string, options: .caseInsensitive) != nil
        }
        return range(of: string) != nil
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 字符串中子字符串的数量。
    ///
    ///        "Hello World!".count(of: "o") -> 2
    ///        "Hello World!".count(of: "L", caseSensitive: false) -> 3
    ///
    /// - 参数:
    ///   - string: 要搜索的子字符串。
    ///   - caseSensitive: 是否区分大小写（默认为true）。
    /// - 返回: 子字符串在字符串中出现的次数。
    func count(of string: String, caseSensitive: Bool = true) -> Int {
        if !caseSensitive {
            return lowercased().components(separatedBy: string.lowercased()).count - 1
        }
        return components(separatedBy: string).count - 1
    }
    #endif

    /// SwifterSwift: 检查字符串是否以子字符串结尾。
    ///
    ///        "Hello World!".ends(with: "!") -> true
    ///        "Hello World!".ends(with: "WoRld!", caseSensitive: false) -> true
    ///
    /// - 参数:
    ///   - suffix: 检查字符串是否以其结尾的子字符串。
    ///   - caseSensitive: 设置为true进行区分大小写的搜索（默认为true）。
    /// - 返回: 如果字符串以子字符串结尾，则为true。
    func ends(with suffix: String, caseSensitive: Bool = true) -> Bool {
        if !caseSensitive {
            return lowercased().hasSuffix(suffix.lowercased())
        }
        return hasSuffix(suffix)
    }

    #if canImport(Foundation)
    /// SwifterSwift: 拉丁化字符串。
    ///
    ///        var str = "Hèllö Wórld!"
    ///        str.latinize()
    ///        print(str) // 打印 "Hello World!"
    ///
    @discardableResult
    mutating func latinize() -> String {
        self = folding(options: .diacriticInsensitive, locale: Locale.current)
        return self
    }
    #endif

    /// SwifterSwift: 给定长度的随机字符串。
    ///
    ///        String.random(ofLength: 18) -> "u7MMZYvGo9obcOcPj8"
    ///
    /// - 参数 length: 字符串中的字符数。
    /// - 返回: 给定长度的随机字符串。
    static func random(ofLength length: Int) -> String {
        guard length > 0 else { return "" }
        let base = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        var randomString = ""
        for _ in 1...length {
            randomString.append(base.randomElement()!)
        }
        return randomString
    }

    /// SwifterSwift: 反转字符串。
    @discardableResult
    mutating func reverse() -> String {
        let chars: [Character] = reversed()
        self = String(chars)
        return self
    }

    /// SwifterSwift: 从起始索引开始指定长度的字符串片段。
    ///
    ///        "Hello World".slicing(from: 6, length: 5) -> "World"
    ///
    /// - 参数:
    ///   - index: 切片应该从哪个字符串索引开始。
    ///   - length: 给定索引后要切片的字符数量。
    /// - 返回: length数量字符的切片子字符串（如果适用）（例如："Hello World".slicing(from: 6, length: 5) -> "World"）。
    func slicing(from index: Int, length: Int) -> String? {
        guard length >= 0, index >= 0, index < count else { return nil }
        guard index.advanced(by: length) <= count else {
            return self[safe: index..<count]
        }
        guard length > 0 else { return "" }
        return self[safe: index..<index.advanced(by: length)]
    }

    /// SwifterSwift: 从起始索引开始按长度切片给定字符串（如果适用）。
    ///
    ///        var str = "Hello World"
    ///        str.slice(from: 6, length: 5)
    ///        print(str) // 打印 "World"
    ///
    /// - 参数:
    ///   - index: 切片应该从哪个字符串索引开始。
    ///   - length: 给定索引后要切片的字符数量。
    @discardableResult
    mutating func slice(from index: Int, length: Int) -> String {
        if let str = slicing(from: index, length: length) {
            self = String(str)
        }
        return self
    }

    /// SwifterSwift: 从起始索引到结束索引切片给定字符串（如果适用）。
    ///
    ///        var str = "Hello World"
    ///        str.slice(from: 6, to: 11)
    ///        print(str) // 打印 "World"
    ///
    /// - 参数:
    ///   - start: 切片应该从哪个字符串索引开始。
    ///   - end: 切片应该在哪个字符串索引结束。
    @discardableResult
    mutating func slice(from start: Int, to end: Int) -> String {
        guard end >= start else { return self }
        if let str = self[safe: start..<end] {
            self = str
        }
        return self
    }

    /// SwifterSwift: 从起始索引切片给定字符串（如果适用）。
    ///
    ///        var str = "Hello World"
    ///        str.slice(at: 6)
    ///        print(str) // 打印 "World"
    ///
    /// - 参数 index: 切片应该从哪个字符串索引开始。
    @discardableResult
    mutating func slice(at index: Int) -> String {
        guard index < count else { return self }
        if let str = self[safe: index..<count] {
            self = str
        }
        return self
    }

    /// SwifterSwift: 检查字符串是否以子字符串开头。
    ///
    ///        "hello World".starts(with: "h") -> true
    ///        "hello World".starts(with: "H", caseSensitive: false) -> true
    ///
    /// - 参数:
    ///   - suffix: 检查字符串是否以其开头的子字符串。
    ///   - caseSensitive: 设置为true进行区分大小写的搜索（默认为true）。
    /// - 返回: 如果字符串以子字符串开头，则为true。
    func starts(with prefix: String, caseSensitive: Bool = true) -> Bool {
        if !caseSensitive {
            return lowercased().hasPrefix(prefix.lowercased())
        }
        return hasPrefix(prefix)
    }

    #if canImport(Foundation)
    /// SwifterSwift: 从日期格式字符串获取Date对象。
    ///
    ///        "2017-01-15".date(withFormat: "yyyy-MM-dd") -> Date设置为2017年1月15日
    ///        "not date string".date(withFormat: "yyyy-MM-dd") -> nil
    ///
    /// - 参数 format: 日期格式。
    /// - 返回: 从字符串获取的Date对象（如果适用）。
    func date(withFormat format: String) -> Date? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = format
        return dateFormatter.date(from: self)
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 删除字符串开头和结尾的空格和换行符。
    ///
    ///        var str = "  \n Hello World \n\n\n"
    ///        str.trim()
    ///        print(str) // 打印 "Hello World"
    ///
    @discardableResult
    mutating func trim() -> String {
        self = trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
        return self
    }
    #endif

    /// SwifterSwift: 截断字符串（将其剪切为给定的字符数）。
    ///
    ///        var str = "This is a very long sentence"
    ///        str.truncate(toLength: 14)
    ///        print(str) // 打印 "This is a very..."
    ///
    /// - 参数:
    ///   - toLength: 截断前的最大字符数。
    ///   - trailing: 在截断字符串末尾添加的字符串（默认为"..."）。
    @discardableResult
    mutating func truncate(toLength length: Int, trailing: String? = "...") -> String {
        guard length > 0 else { return self }
        if count > length {
            self = self[startIndex..<index(startIndex, offsetBy: length)] + (trailing ?? "")
        }
        return self
    }

    /// SwifterSwift: 截断的字符串（限制为给定数量的字符）。
    ///
    ///        "This is a very long sentence".truncated(toLength: 14) -> "This is a very..."
    ///        "Short sentence".truncated(toLength: 14) -> "Short sentence"
    ///
    /// - 参数:
    ///   - toLength: 截断前的最大字符数。
    ///   - trailing: 在截断字符串末尾添加的字符串。
    /// - 返回: 截断的字符串（如：这是一个额外的...）。
    func truncated(toLength length: Int, trailing: String? = "...") -> String {
        guard 0..<count ~= length else { return self }
        return self[startIndex..<index(startIndex, offsetBy: length)] + (trailing ?? "")
    }

    #if canImport(Foundation)
    /// SwifterSwift: 将URL字符串转换为可读字符串。
    ///
    ///        var str = "it's%20easy%20to%20decode%20strings"
    ///        str.urlDecode()
    ///        print(str) // 打印 "it's easy to decode strings"
    ///
    @discardableResult
    mutating func urlDecode() -> String {
        if let decoded = removingPercentEncoding {
            self = decoded
        }
        return self
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 转义字符串。
    ///
    ///        var str = "it's easy to encode strings"
    ///        str.urlEncode()
    ///        print(str) // 打印 "it's%20easy%20to%20encode%20strings"
    ///
    @discardableResult
    mutating func urlEncode() -> String {
        if let encoded = addingPercentEncoding(withAllowedCharacters: .urlHostAllowed) {
            self = encoded
        }
        return self
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 验证字符串是否匹配正则表达式模式。
    ///
    /// - 参数 pattern: 要验证的模式。
    /// - 返回: 如果字符串匹配模式，返回`true`。
    func matches(pattern: String) -> Bool {
        return range(of: pattern, options: .regularExpression, range: nil, locale: nil) != nil
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 验证字符串是否匹配正则表达式。
    ///
    /// - 参数:
    ///   - regex: 要验证的正则表达式。
    ///   - options: 要使用的匹配选项。
    /// - 返回: 如果字符串匹配正则表达式，返回`true`。
    func matches(regex: NSRegularExpression, options: NSRegularExpression.MatchingOptions = []) -> Bool {
        let range = NSRange(startIndex..<endIndex, in: self)
        return regex.firstMatch(in: self, options: options, range: range) != nil
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 为匹配正则表达式重载Swift的'contains'运算符。
    ///
    /// - 参数:
    ///   - lhs: 要检查正则表达式模式的字符串。
    ///   - rhs: 要匹配的正则表达式模式。
    /// - 返回: 如果字符串匹配模式，返回true。
    static func ~= (lhs: String, rhs: String) -> Bool {
        return rhs.range(of: lhs, options: .regularExpression) != nil
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 为匹配正则表达式重载Swift的'contains'运算符。
    ///
    /// - 参数:
    ///   - lhs: 要检查的正则表达式。
    ///   - rhs: 要匹配的字符串。
    /// - 返回: 如果字符串中至少有一个正则表达式匹配项，则返回`true`。
    static func ~= (lhs: NSRegularExpression, rhs: String) -> Bool {
        let range = NSRange(rhs.startIndex..<rhs.endIndex, in: rhs)
        return lhs.firstMatch(in: rhs, range: range) != nil
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 返回一个新字符串，其中接收者指定范围内的所有正则表达式出现都被模板替换。
    /// - 参数:
    ///   - regex 要替换的正则表达式。
    ///   - template: 替换正则表达式的模板。
    ///   - options: 要使用的匹配选项
    ///   - searchRange: 接收者中要搜索的范围。
    /// - 返回: 一个新字符串，其中接收者searchRange中的所有正则表达式出现都被模板替换。
    func replacingOccurrences(
        of regex: NSRegularExpression,
        with template: String,
        options: NSRegularExpression.MatchingOptions = [],
        range searchRange: Range<String.Index>? = nil) -> String {
        let range = NSRange(searchRange ?? startIndex..<endIndex, in: self)
        return regex.stringByReplacingMatches(in: self, options: options, range: range, withTemplate: template)
    }
    #endif

    /// SwifterSwift: 用另一个字符串在开始处填充字符串以适应长度参数大小。
    ///
    ///   "hue".padStart(10) -> "       hue"
    ///   "hue".padStart(10, with: "br") -> "brbrbrbhue"
    ///
    /// - 参数:
    ///   - length: 要填充的目标长度。
    ///   - string: 填充字符串。默认为" "。
    @discardableResult
    mutating func padStart(_ length: Int, with string: String = " ") -> String {
        self = paddingStart(length, with: string)
        return self
    }

    /// SwifterSwift: 通过在开始处用另一个字符串填充来返回一个适应长度参数大小的字符串。
    ///
    ///   "hue".paddingStart(10) -> "       hue"
    ///   "hue".paddingStart(10, with: "br") -> "brbrbrbhue"
    ///
    /// - 参数:
    ///   - length: 要填充的目标长度。
    ///   - string: 填充字符串。默认为" "。
    /// - 返回: 在开始处填充的字符串。
    func paddingStart(_ length: Int, with string: String = " ") -> String {
        guard count < length else { return self }

        let padLength = length - count
        if padLength < string.count {
            return string[string.startIndex..<string.index(string.startIndex, offsetBy: padLength)] + self
        } else {
            var padding = string
            while padding.count < padLength {
                padding.append(string)
            }
            return padding[padding.startIndex..<padding.index(padding.startIndex, offsetBy: padLength)] + self
        }
    }

    /// SwifterSwift: 用另一个字符串在末尾填充字符串以适应长度参数大小。
    ///
    ///   "hue".padEnd(10) -> "hue       "
    ///   "hue".padEnd(10, with: "br") -> "huebrbrbrb"
    ///
    /// - 参数:
    ///   - length: 要填充的目标长度。
    ///   - string: 填充字符串。默认为" "。
    @discardableResult
    mutating func padEnd(_ length: Int, with string: String = " ") -> String {
        self = paddingEnd(length, with: string)
        return self
    }

    /// SwifterSwift: 通过在末尾用另一个字符串填充来返回一个适应长度参数大小的字符串。
    ///
    ///   "hue".paddingEnd(10) -> "hue       "
    ///   "hue".paddingEnd(10, with: "br") -> "huebrbrbrb"
    ///
    /// - 参数:
    ///   - length: 要填充的目标长度。
    ///   - string: 填充字符串。默认为" "。
    /// - 返回: 在末尾填充的字符串。
    func paddingEnd(_ length: Int, with string: String = " ") -> String {
        guard count < length else { return self }

        let padLength = length - count
        if padLength < string.count {
            return self + string[string.startIndex..<string.index(string.startIndex, offsetBy: padLength)]
        } else {
            var padding = string
            while padding.count < padLength {
                padding.append(string)
            }
            return self + padding[padding.startIndex..<padding.index(padding.startIndex, offsetBy: padLength)]
        }
    }

    /// SwifterSwift: 从字符串中移除给定的前缀。
    ///
    ///   "Hello, World!".removingPrefix("Hello, ") -> "World!"
    ///
    /// - 参数 prefix: 从字符串中移除的前缀。
    /// - 返回: 移除前缀后的字符串。
    func removingPrefix(_ prefix: String) -> String {
        guard hasPrefix(prefix) else { return self }
        return String(dropFirst(prefix.count))
    }

    /// SwifterSwift: 从字符串中移除给定的后缀。
    ///
    ///   "Hello, World!".removingSuffix(", World!") -> "Hello"
    ///
    /// - 参数 suffix: 从字符串中移除的后缀。
    /// - 返回: 移除后缀后的字符串。
    func removingSuffix(_ suffix: String) -> String {
        guard hasSuffix(suffix) else { return self }
        return String(dropLast(suffix.count))
    }

    /// SwifterSwift: 向字符串添加前缀。
    ///
    ///     "www.apple.com".withPrefix("https://") -> "https://www.apple.com"
    ///
    /// - 参数 prefix: 添加到字符串的前缀。
    /// - 返回: 带有前置前缀的字符串。
    func withPrefix(_ prefix: String) -> String {
        // https://www.hackingwithswift.com/articles/141/8-useful-swift-extensions
        guard !hasPrefix(prefix) else { return self }
        return prefix + self
    }
}

// MARK: - Initializers

public extension String {
    #if canImport(Foundation)
    /// SwifterSwift: 从base64字符串创建新字符串（如果适用）。
    ///
    ///        String(base64: "SGVsbG8gV29ybGQh") = "Hello World!"
    ///        String(base64: "hello") = nil
    ///
    /// - 参数 base64: base64字符串。
    init?(base64: String) {
        guard let decodedData = Data(base64Encoded: base64) else { return nil }
        self.init(data: decodedData, encoding: .utf8)
    }
    #endif
}

#if !os(Linux) && !os(Android)

// MARK: - NSAttributedString

public extension String {
    #if os(iOS) || os(macOS)
    /// SwifterSwift: 粗体字符串。
//    var bold: NSAttributedString {
//        return NSMutableAttributedString(
//            string: self,
//            attributes: [.font: SFFont.boldSystemFont(ofSize: SFFont.systemFontSize)])
//    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 带下划线的字符串
    var underline: NSAttributedString {
        return NSAttributedString(string: self, attributes: [.underlineStyle: NSUnderlineStyle.single.rawValue])
    }
    #endif

    #if canImport(Foundation)
    /// SwifterSwift: 带删除线的字符串。
    var strikethrough: NSAttributedString {
        return NSAttributedString(
            string: self,
            attributes: [.strikethroughStyle: NSNumber(value: NSUnderlineStyle.single.rawValue as Int)])
    }
    #endif

    #if os(iOS)
    /// SwifterSwift: 斜体字符串。
    var italic: NSAttributedString {
        return NSMutableAttributedString(
            string: self,
            attributes: [.font: UIFont.italicSystemFont(ofSize: UIFont.systemFontSize)])
    }
    #endif

    #if canImport(AppKit) || canImport(UIKit)
    /// SwifterSwift: 为字符串添加颜色。
    ///
    /// - 参数 color: 文本颜色。
    /// - 返回: 使用给定颜色着色的字符串的NSAttributedString版本。
//    func colored(with color: SFColor) -> NSAttributedString {
//        return NSMutableAttributedString(string: self, attributes: [.foregroundColor: color])
//    }
    #endif
}

#endif

// MARK: - Operators

public extension String {
    /// SwifterSwift: Repeat string multiple times.
    ///
    ///        'bar' * 3 -> "barbarbar"
    ///
    /// - Parameters:
    ///   - lhs: string to repeat.
    ///   - rhs: number of times to repeat character.
    /// - Returns: new string with given string repeated n times.
    static func * (lhs: String, rhs: Int) -> String {
        guard rhs > 0 else { return "" }
        return String(repeating: lhs, count: rhs)
    }

    /// SwifterSwift: Repeat string multiple times.
    ///
    ///        3 * 'bar' -> "barbarbar"
    ///
    /// - Parameters:
    ///   - lhs: number of times to repeat character.
    ///   - rhs: string to repeat.
    /// - Returns: new string with given string repeated n times.
    static func * (lhs: Int, rhs: String) -> String {
        guard lhs > 0 else { return "" }
        return String(repeating: rhs, count: lhs)
    }
}

#if canImport(Foundation)

// MARK: - NSString extensions

public extension String {
    /// SwifterSwift: NSString from a string.
    var nsString: NSString {
        return NSString(string: self)
    }

    /// SwifterSwift: The full `NSRange` of the string.
    var fullNSRange: NSRange { NSRange(startIndex..<endIndex, in: self) }

    /// SwifterSwift: NSString lastPathComponent.
    var lastPathComponent: String {
        return (self as NSString).lastPathComponent
    }

    /// SwifterSwift: NSString pathExtension.
    var pathExtension: String {
        return (self as NSString).pathExtension
    }

    /// SwifterSwift: NSString deletingLastPathComponent.
    var deletingLastPathComponent: String {
        return (self as NSString).deletingLastPathComponent
    }

    /// SwifterSwift: NSString deletingPathExtension.
    var deletingPathExtension: String {
        return (self as NSString).deletingPathExtension
    }

    /// SwifterSwift: NSString pathComponents.
    var pathComponents: [String] {
        return (self as NSString).pathComponents
    }

    /// SwifterSwift: Convert an `NSRange` into `Range<String.Index>`.
    /// - Parameter nsRange: The `NSRange` within the receiver.
    /// - Returns: The equivalent `Range<String.Index>` of `nsRange` found within the receiving string.
    func range(from nsRange: NSRange) -> Range<Index> {
        guard let range = Range(nsRange, in: self) else { fatalError("Failed to find range \(nsRange) in \(self)") }
        return range
    }

    /// SwifterSwift: Convert a `Range<String.Index>` into `NSRange`.
    /// - Parameter range: The `Range<String.Index>` within the receiver.
    /// - Returns: The equivalent `NSRange` of `range`.
    func nsRange(from range: Range<Index>) -> NSRange {
        return NSRange(range, in: self)
    }

    /// SwifterSwift: NSString appendingPathComponent(str: String)。
    ///
    /// - 注意: 此方法仅适用于文件路径（例如，不适用于URL的字符串表示）。
    ///   参见NSString [appendingPathComponent(_:)](https://developer.apple.com/documentation/foundation/nsstring/1417069-appendingpathcomponent)
    /// - 参数 str: 要附加到接收者的路径组件。
    /// - 返回: 一个新的字符串，通过将aString附加到接收者，必要时在前面加上路径分隔符。
    func appendingPathComponent(_ str: String) -> String {
        return (self as NSString).appendingPathComponent(str)
    }

    /// SwifterSwift: NSString appendingPathExtension(str: String)。
    ///
    /// - 参数 str: 要附加到接收者的扩展名。
    /// - 返回: 一个新的字符串，通过将扩展分隔符后跟ext附加到接收者（如果适用）。
    func appendingPathExtension(_ str: String) -> String? {
        return (self as NSString).appendingPathExtension(str)
    }

    /// SwifterSwift: 访问集合元素的连续子范围。
    /// - 参数 nsRange: 集合索引的范围。范围的边界必须是集合的有效索引。
    /// - 返回: 接收字符串的一部分。
    subscript(bounds: NSRange) -> Substring {
        guard let range = Range(bounds, in: self) else { fatalError("Failed to find range \(bounds) in \(self)") }
        return self[range]
    }
}

#endif
