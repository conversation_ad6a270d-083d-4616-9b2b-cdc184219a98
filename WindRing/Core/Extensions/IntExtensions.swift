// IntExtensions.swift - Copyright 2025 SwifterSwift

#if canImport(CoreGraphics)
import CoreGraphics
#endif

#if os(macOS) || os(iOS)
import Darwin
#elseif canImport(Android)
import Android
#elseif os(Linux)
import Glibc
#endif

// MARK: - 属性


public extension Int {
    

    
    /// SwifterSwift: CountableRange 0..<Int。
    var countableRange: CountableRange<Int> {
        return 0..<self
    }

    /// SwifterSwift: 输入角度的弧度值。
    var degreesToRadians: Double {
        return Double.pi * Double(self) / 180.0
    }

    /// SwifterSwift: 输入弧度的角度值。
    var radiansToDegrees: Double {
        return Double(self) * 180 / Double.pi
    }

    /// SwifterSwift: UInt 类型。
    var uInt: UInt {
        return UInt(self)
    }

    /// SwifterSwift: Double 类型。
    var double: Double {
        return Double(self)
    }

    /// SwifterSwift: Float 类型。
    var float: Float {
        return Float(self)
    }

    #if canImport(CoreGraphics)
    /// SwifterSwift: CGFloat 类型。
    var cgFloat: CGFloat {
        return CGFloat(self)
    }
    #endif

    /// SwifterSwift: 针对超过 ±1000 的值的格式化字符串（例如：1k, -2k, 100k, 1kk, -5kk..）。
    var kFormatted: String {
        var sign: String {
            return self >= 0 ? "" : "-"
        }
        let absoluteValue = Swift.abs(self)
        if absoluteValue == 0 {
            return "0k"
        } else if absoluteValue > 0, absoluteValue < 1000 {
            // 对于小于1000的值，根据原始逻辑也输出 "0k" 或带符号的 "0k"
            // 如果希望显示实际数字，可以修改这里的逻辑
            return "0k" // 或者 sign + "0k" 如果希望区分 +0k 和 -0k
        } else if absoluteValue >= 1000, absoluteValue < 1_000_000 {
            return String(format: "%@%ik", sign, absoluteValue / 1000)
        }
        return String(format: "%@%ikk", sign, absoluteValue / 100_000) // 注意：这里是除以100_000，不是1_000_000
    }

    /// SwifterSwift: 整数值的数字数组。
    var digits: [Int] {
        guard self != 0 else { return [0] }
        var digits = [Int]()
        var number = Swift.abs(self)

        while number != 0 {
            let xNumber = number % 10
            digits.append(xNumber)
            number /= 10
        }

        digits.reverse()
        return digits
    }

    /// SwifterSwift: 整数值的位数。
    var digitsCount: Int {
        guard self != 0 else { return 1 }
        let number = Double(Swift.abs(self))
        return Int(log10(number) + 1)
    }
}

// MARK: - Methods

public extension Int {
    ///将分钟转换为小时分
    func formatMinutesToHoursAndMinutes() -> String {
        let hours = self / 60
        let minutes = self % 60

        if hours > 0 && minutes > 0 {
            return "\(hours) hr \(minutes) min"
//            return "\(hours)小时\(minutes)分钟"
        } else if hours > 0 {
            return "\(hours) hr"
//            return "\(hours)小时"
        } else {
            return "\(hours) min"
//            return "\(minutes)分钟"
        }
    }
    ///将秒数转换为 小时和分钟 并返回两个 Int 值
    func convertSecondsToHourMinute() -> (Int, Int) {
        let hours = self / 3600
        let minutes = (self % 3600) / 60
        return (hours, minutes)
    }
    ///小时 + 分钟 ➝ 秒
    func convertHourMinuteToSeconds(hours:Int, minutes:Int) -> Int {
        return hours * 3600 + minutes * 60
    }
    /// SwifterSwift: 检查给定的整数是否为质数。警告：使用大数计算成本可能很高！
    /// - 返回: true 或 false，取决于是否为质数。
    func isPrime() -> Bool {
        // 为了提高后续循环的速度 :)
        if self == 2 { return true }

        guard self > 1, self % 2 != 0 else { return false }

        // 解释：检查到该数字的平方根即可。如果从N向上加1，
        // 其他乘数将向下减1以获得类似的结果（整数运算），这样可以提高运算速度
        let base = Int(sqrt(Double(self)))
        for int in Swift.stride(from: 3, through: base, by: 2) where self % int == 0 {
            return false
        }
        return true
    }

    /// SwifterSwift: 从整数获取罗马数字字符串（如果适用）。
    ///
    ///     10.romanNumeral() -> "X"
    ///
    /// - 返回: 罗马数字字符串。
    func romanNumeral() -> String? {
        // https://gist.github.com/kumo/a8e1cb1f4b7cff1548c7
        guard self > 0 else { // 0或负数没有罗马数字
            return nil
        }
        let romanValues = ["M", "CM", "D", "CD", "C", "XC", "L", "XL", "X", "IX", "V", "IV", "I"]
        let arabicValues = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1]

        var romanValue = ""
        var startingValue = self

        for (index, romanChar) in romanValues.enumerated() {
            let arabicValue = arabicValues[index]
            let div = startingValue / arabicValue
            for _ in 0..<div {
                romanValue.append(romanChar)
            }
            startingValue -= arabicValue * div
        }
        return romanValue
    }

    /// SwifterSwift: 四舍五入到最接近的n的倍数。
    func roundToNearest(_ number: Int) -> Int {
        return number == 0 ? self : Int(round(Double(self) / Double(number))) * number
    }
}

// MARK: - 运算符

precedencegroup PowerPrecedence { higherThan: MultiplicationPrecedence }
infix operator **: PowerPrecedence
/// SwifterSwift: 指数运算的值。
///
/// - 参数:
///   - lhs: 基数整数。
///   - rhs: 指数整数。
/// - 返回: 指数运算结果 (例如: 2 ** 3 = 8)。
public func ** (lhs: Int, rhs: Int) -> Double {
    // http://nshipster.com/swift-operators/
    return pow(Double(lhs), Double(rhs))
}

// swiftlint:disable identifier_name
prefix operator √
/// SwifterSwift: 整数的平方根。
///
/// - Parameter int: 要求平方根的整数值。
/// - Returns: 给定整数的平方根。
public prefix func √ (int: Int) -> Double {
    // http://nshipster.com/swift-operators/
    return sqrt(Double(int))
}

// swiftlint:enable identifier_name

// swiftlint:disable identifier_name
infix operator ±
/// SwifterSwift: 加减运算的元组。
///
/// - 参数:
///   - lhs: 整数。
///   - rhs: 整数。
/// - 返回: 加减运算的元组 (例如: 2 ± 3 -> (5, -1))。
public func ± (lhs: Int, rhs: Int) -> (Int, Int) {
    // http://nshipster.com/swift-operators/
    return (lhs + rhs, lhs - rhs)
}

// swiftlint:enable identifier_name

// swiftlint:disable identifier_name
prefix operator ±
/// SwifterSwift: 加减运算的元组。
///
/// - Parameter int: 整数。
/// - 返回: 加减运算的元组 (例如: ± 2 -> (2, -2))。
public prefix func ± (int: Int) -> (Int, Int) {
    // http://nshipster.com/swift-operators/
    return (int, -int)
}

// swiftlint:enable identifier_name
