import Foundation

/// 字典值转换器
/// 用于将[String: Any]类型转换为Data存储在Core Data中
@objc(DictionaryValueTransformer)
class DictionaryValueTransformer: NSSecureUnarchiveFromDataTransformer {
    
    static let name = NSValueTransformerName(rawValue: "DictionaryValueTransformer")
    
    override class var allowedTopLevelClasses: [AnyClass] {
        return [NSDictionary.self, NSString.self, NSNumber.self, NSDate.self, NSArray.self, NSNull.self]
    }
    
    /// 注册转换器
    public static func register() {
        let transformer = DictionaryValueTransformer()
        ValueTransformer.setValueTransformer(transformer, forName: name)
    }
}

/// 字符串字典值转换器
/// 用于将[String: String]类型转换为Data存储在Core Data中
@objc(StringDictionaryValueTransformer)
class StringDictionaryValueTransformer: NSSecureUnarchiveFromDataTransformer {
    
    static let name = NSValueTransformerName(rawValue: "StringDictionaryValueTransformer")
    
    override class var allowedTopLevelClasses: [AnyClass] {
        return [NSDictionary.self, NSString.self]
    }
    
    /// 注册转换器
    public static func register() {
        let transformer = StringDictionaryValueTransformer()
        ValueTransformer.setValueTransformer(transformer, forName: name)
    }
} 