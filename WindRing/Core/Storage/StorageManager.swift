import Foundation
import CoreData
import Combine

/// 数据存储管理器
/// 负责处理健康数据的本地存储
public class StorageManager {
    // MARK: - 单例
    public static let shared = StorageManager()
    
    // MARK: - 属性
    private var persistentContainer: NSPersistentContainer
    private var backgroundContext: NSManagedObjectContext
    
    /// 当前使用的数据库标识符
    private var currentDatabaseIdentifier: String?
    
    /// 数据变更发布者
    public let dataChangePublisher = PassthroughSubject<StorageChangeType, Never>()
    
    /// 获取持久化容器 - 用于与AppDelegate共享
    public var persistentContainerPublic: NSPersistentContainer {
        return persistentContainer
    }
    
    // MARK: - 常量
    private let lastConnectedDeviceKey = "LastConnectedDeviceMAC"
    private let currentUserKey = "CurrentUserID"
    
    // MARK: - 静态方法
    
    /// 注册值转换器 - 静态方法可以在实例初始化前调用
    static func registerValueTransformers() {
        // 注册字典值转换器
        ValueTransformer.setValueTransformer(
            DictionaryValueTransformer(),
            forName: NSValueTransformerName(rawValue: "DictionaryValueTransformer")
        )
        
        // 注册字符串字典值转换器
        ValueTransformer.setValueTransformer(
            StringDictionaryValueTransformer(),
            forName: NSValueTransformerName(rawValue: "StringDictionaryValueTransformer")
        )
    }
    
    // MARK: - 初始化方法
    private init() {
        // 首先注册值转换器（静态方法）
        StorageManager.registerValueTransformers()
        
        // 初始化默认数据库
        persistentContainer = NSPersistentContainer(name: "WindRingDataModel")
        backgroundContext = NSManagedObjectContext(concurrencyType: .privateQueueConcurrencyType)
        
        // 配置默认数据库
//        configurePersistentContainer()
        
        // 尝试加载上次连接的设备数据库
//        loadLastConnectedDeviceDatabase()
    }
    
    // MARK: - 用户数据库管理
    
    /// 切换到用户数据库
    /// - Parameter userId: 用户ID
    /// - Returns: 是否成功切换
    public func switchToUserDatabase(userId: String) -> Bool {
        // 如果已经是当前数据库，直接返回成功
        if currentDatabaseIdentifier == "user_\(userId)" {
            return true
        }
        
        // 保存当前上下文（如果有未保存的更改）
        saveViewContext()
        
        // 创建新的持久化容器
        let newContainer = NSPersistentContainer(name: "WindRingDataModel")
        let newBackgroundContext = NSManagedObjectContext(concurrencyType: .privateQueueConcurrencyType)
        
        // 配置新的持久化容器
        let storeDescription = NSPersistentStoreDescription()
        storeDescription.shouldMigrateStoreAutomatically = true
        storeDescription.shouldInferMappingModelAutomatically = true
        
        // 设置数据库文件路径
        let fileManager = FileManager.default
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let dbFolderURL = documentsURL.appendingPathComponent("WindRingData", isDirectory: true)
        let dbFileURL = dbFolderURL.appendingPathComponent("WindRingDataModel_user_\(userId).sqlite")
        
        // 创建目录（如果不存在）
        if !fileManager.fileExists(atPath: dbFolderURL.path) {
            do {
                try fileManager.createDirectory(at: dbFolderURL, withIntermediateDirectories: true)
                print("✅ 已创建数据库目录: \(dbFolderURL.path)")
            } catch {
                print("❌ 创建数据库目录失败: \(error)")
                return false
            }
        }
        
        // 设置数据库URL
        storeDescription.url = dbFileURL
        
        // 配置持久化存储
        newContainer.persistentStoreDescriptions = [storeDescription]
        
        // 加载持久化存储
        var loadError: Error?
        newContainer.loadPersistentStores { (storeDescription, error) in
            if let error = error {
                loadError = error
                print("❌ 无法加载持久化存储: \(error), \(error.localizedDescription)")
            } else {
                print("✅ 成功加载用户数据库: \(storeDescription.url?.path ?? "未知路径")")
            }
        }
        
        // 检查是否成功加载
        if loadError != nil {
            return false
        }
        
        // 配置新的后台上下文
        newBackgroundContext.parent = newContainer.viewContext
        
        // 配置视图上下文
        let viewContext = newContainer.viewContext
        viewContext.automaticallyMergesChangesFromParent = true
        viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        // 更新当前数据库
        persistentContainer = newContainer
        backgroundContext = newBackgroundContext
        currentDatabaseIdentifier = "user_\(userId)"
        
        // 保存当前用户ID
        UserDefaults.standard.set(userId, forKey: currentUserKey)
        UserDefaults.standard.synchronize()
        
        // 设置通知观察者
        setupNotificationObservers()
        
        // 打印数据库信息
        printDatabaseInfo()
        
        // 通知数据已更新
        DispatchQueue.main.async {
            self.dataChangePublisher.send(.updated)
        }
        
        return true
    }
    
    /// 切换到设备数据库
    /// - Parameter deviceId: 设备ID
    /// - Returns: 是否成功切换
    public func switchToDeviceDatabase(deviceId: String) -> Bool {
        // 如果已经是当前数据库，直接返回成功
        if currentDatabaseIdentifier == "device_\(deviceId)" {
            return true
        }
        
        // 保存当前上下文（如果有未保存的更改）
        saveViewContext()
        
        // 创建新的持久化容器
        let newContainer = NSPersistentContainer(name: "WindRingDataModel")
        let newBackgroundContext = NSManagedObjectContext(concurrencyType: .privateQueueConcurrencyType)
        
        // 配置新的持久化容器
        let storeDescription = NSPersistentStoreDescription()
        storeDescription.shouldMigrateStoreAutomatically = true
        storeDescription.shouldInferMappingModelAutomatically = true
        
        // 设置数据库文件路径
        let fileManager = FileManager.default
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let dbFolderURL = documentsURL.appendingPathComponent("WindRingData", isDirectory: true)
        let dbFileURL = dbFolderURL.appendingPathComponent("WindRingDataModel_device_\(deviceId).sqlite")
        
        // 创建目录（如果不存在）
        if !fileManager.fileExists(atPath: dbFolderURL.path) {
            do {
                try fileManager.createDirectory(at: dbFolderURL, withIntermediateDirectories: true)
                print("✅ 已创建数据库目录: \(dbFolderURL.path)")
            } catch {
                print("❌ 创建数据库目录失败: \(error)")
                return false
            }
        }
        
        // 设置数据库URL
        storeDescription.url = dbFileURL
        
        // 配置持久化存储
        newContainer.persistentStoreDescriptions = [storeDescription]
        
        // 加载持久化存储
        var loadError: Error?
        newContainer.loadPersistentStores { (storeDescription, error) in
            if let error = error {
                loadError = error
                print("❌ 无法加载持久化存储: \(error), \(error.localizedDescription)")
            } else {
                print("✅ 成功加载设备数据库: \(storeDescription.url?.path ?? "未知路径")")
            }
        }
        
        // 检查是否成功加载
        if loadError != nil {
            return false
        }
        
        // 配置新的后台上下文
        newBackgroundContext.parent = newContainer.viewContext
        
        // 配置视图上下文
        let viewContext = newContainer.viewContext
        viewContext.automaticallyMergesChangesFromParent = true
        viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        // 更新当前数据库
        persistentContainer = newContainer
        backgroundContext = newBackgroundContext
        currentDatabaseIdentifier = "device_\(deviceId)"
        
        // 保存最后连接的设备ID
        UserDefaults.standard.set(deviceId, forKey: lastConnectedDeviceKey)
        UserDefaults.standard.synchronize()
        
        // 设置通知观察者
        setupNotificationObservers()
        
        // 打印数据库信息
        printDatabaseInfo()
        
        // 通知数据已更新
        DispatchQueue.main.async {
            self.dataChangePublisher.send(.updated)
        }
        
        return true
    }
    
    /// 获取当前用户ID
    public func getCurrentUserId() -> String? {
        return UserDefaults.standard.string(forKey: currentUserKey)
    }
    
    /// 清除当前用户
    public func clearCurrentUser() {
        UserDefaults.standard.removeObject(forKey: currentUserKey)
        UserDefaults.standard.synchronize()
        
        // 切换到默认数据库
        currentDatabaseIdentifier = nil
        configurePersistentContainer()
    }
    
    // MARK: - 设备连接管理
    
    /// 保存最后连接的设备MAC地址
    /// - Parameter macAddress: 设备MAC地址
    public func saveLastConnectedDevice(macAddress: String) {
        UserDefaults.standard.set(macAddress, forKey: lastConnectedDeviceKey)
        UserDefaults.standard.synchronize()
        
        // 切换到该设备的数据库
        _ = switchToDatabase(for: macAddress)
    }
    
    /// 获取最后连接的设备MAC地址
    /// - Returns: 设备MAC地址，如果没有则返回nil
    public func getLastConnectedDevice() -> String? {
        return UserDefaults.standard.string(forKey: lastConnectedDeviceKey)
    }
    
    /// 清除最后连接的设备记录
    public func clearLastConnectedDevice() {
        UserDefaults.standard.removeObject(forKey: lastConnectedDeviceKey)
        UserDefaults.standard.synchronize()
        
        // 切换到默认数据库
        currentDatabaseIdentifier = nil
        configurePersistentContainer()
    }
    
    // MARK: - 数据库管理方法
    
    /// 切换到指定设备的数据库
    /// - Parameter deviceIdentifier: 设备标识符（通常是MAC地址）
    /// - Returns: 是否成功切换
    public func switchToDatabase(for deviceIdentifier: String) -> Bool {
        // 如果已经是当前数据库，直接返回成功
        if currentDatabaseIdentifier == deviceIdentifier {
            return true
        }
        
        // 保存当前上下文（如果有未保存的更改）
        saveViewContext()
        
        // 创建新的持久化容器
        let newContainer = NSPersistentContainer(name: "WindRingDataModel")
        let newBackgroundContext = NSManagedObjectContext(concurrencyType: .privateQueueConcurrencyType)
        
        // 配置新的持久化容器
        let storeDescription = NSPersistentStoreDescription()
        storeDescription.shouldMigrateStoreAutomatically = true
        storeDescription.shouldInferMappingModelAutomatically = true
        
        // 设置数据库文件路径
        let fileManager = FileManager.default
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let dbFolderURL = documentsURL.appendingPathComponent("WindRingData", isDirectory: true)
        let dbFileURL = dbFolderURL.appendingPathComponent("WindRingDataModel_\(deviceIdentifier).sqlite")
        
        // 创建目录（如果不存在）
        if !fileManager.fileExists(atPath: dbFolderURL.path) {
            do {
                try fileManager.createDirectory(at: dbFolderURL, withIntermediateDirectories: true)
                print("✅ 已创建数据库目录: \(dbFolderURL.path)")
            } catch {
                print("❌ 创建数据库目录失败: \(error)")
                return false
            }
        }
        
        // 设置数据库URL
        storeDescription.url = dbFileURL
        
        // 配置持久化存储
        newContainer.persistentStoreDescriptions = [storeDescription]
        
        // 加载持久化存储
        var loadError: Error?
        newContainer.loadPersistentStores { (storeDescription, error) in
            if let error = error {
                loadError = error
                print("❌ 无法加载持久化存储: \(error), \(error.localizedDescription)")
            } else {
                print("✅ 成功加载持久化存储: \(storeDescription.url?.path ?? "未知路径")")
            }
        }
        
        // 检查是否成功加载
        if loadError != nil {
            return false
        }
        
        // 配置新的后台上下文
        newBackgroundContext.parent = newContainer.viewContext
        
        // 配置视图上下文
        let viewContext = newContainer.viewContext
        viewContext.automaticallyMergesChangesFromParent = true
        viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        // 更新当前数据库
        persistentContainer = newContainer
        backgroundContext = newBackgroundContext
        currentDatabaseIdentifier = deviceIdentifier
        
        // 设置通知观察者
        setupNotificationObservers()
        
        // 打印数据库信息
        printDatabaseInfo()
        
        // 通知数据已更新
        DispatchQueue.main.async {
            self.dataChangePublisher.send(.updated)
        }
        
        return true
    }
    
    /// 获取所有可用的数据库
    /// - Returns: 数据库标识符数组
    public func getAvailableDatabases() -> [String] {
        let fileManager = FileManager.default
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let dbFolderURL = documentsURL.appendingPathComponent("WindRingData", isDirectory: true)
        
        do {
            let files = try fileManager.contentsOfDirectory(at: dbFolderURL, includingPropertiesForKeys: nil)
            return files.compactMap { url -> String? in
                let fileName = url.lastPathComponent
                if fileName.hasPrefix("WindRingDataModel_") && fileName.hasSuffix(".sqlite") {
                    let identifier = fileName.replacingOccurrences(of: "WindRingDataModel_", with: "")
                        .replacingOccurrences(of: ".sqlite", with: "")
                    return identifier
                }
                return nil
            }
        } catch {
            print("获取可用数据库失败: \(error)")
            return []
        }
    }
    
    /// 删除指定设备的数据库
    /// - Parameter deviceIdentifier: 设备标识符
    /// - Returns: 是否成功删除
    public func deleteDatabase(for deviceIdentifier: String) -> Bool {
        let fileManager = FileManager.default
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let dbFolderURL = documentsURL.appendingPathComponent("WindRingData", isDirectory: true)
        let dbFileURL = dbFolderURL.appendingPathComponent("WindRingDataModel_\(deviceIdentifier).sqlite")
        
        do {
            try fileManager.removeItem(at: dbFileURL)
            print("✅ 成功删除数据库: \(dbFileURL.path)")
            
            // 如果删除的是当前数据库，重置为默认数据库
            if currentDatabaseIdentifier == deviceIdentifier {
                currentDatabaseIdentifier = nil
                configurePersistentContainer()
            }
            
            return true
        } catch {
            print("❌ 删除数据库失败: \(error)")
            return false
        }
    }
    
    // MARK: - 私有方法
    
    /// 配置持久化容器
    private func configurePersistentContainer() {
        let storeDescription = NSPersistentStoreDescription()
        storeDescription.shouldMigrateStoreAutomatically = true
        storeDescription.shouldInferMappingModelAutomatically = true
        
        // 设置数据库文件路径
        let fileManager = FileManager.default
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let dbFolderURL = documentsURL.appendingPathComponent("WindRingData", isDirectory: true)
        
        // 创建目录（如果不存在）
        if !fileManager.fileExists(atPath: dbFolderURL.path) {
            do {
                try fileManager.createDirectory(at: dbFolderURL, withIntermediateDirectories: true)
                print("✅ 已创建数据库目录: \(dbFolderURL.path)")
            } catch {
                print("❌ 创建数据库目录失败: \(error)")
            }
        }
        
        // 设置数据库文件路径
        let dbFileURL = dbFolderURL.appendingPathComponent("WindRingDataModel.sqlite")
        storeDescription.url = dbFileURL
        
        // 配置持久化存储
        persistentContainer.persistentStoreDescriptions = [storeDescription]
        
//        // 加载持久化存储
//        persistentContainer.loadPersistentStores { (storeDescription, error) in
//            if let error = error as NSError? {
//                print("❌ 无法加载持久化存储: \(error), \(error.userInfo)")
//                fatalError("无法加载持久化存储: \(error), \(error.userInfo)")
//            } else {
//                print("✅ 成功加载持久化存储: \(storeDescription.url?.path ?? "未知路径")")
//            }
//        }
        
//        // 配置后台上下文
//        backgroundContext.parent = persistentContainer.viewContext
//        
//        // 配置视图上下文
//        let viewContext = persistentContainer.viewContext
//        viewContext.automaticallyMergesChangesFromParent = true
//        viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
//        
//        // 设置通知观察者
//        setupNotificationObservers()
//        
//        // 打印数据库信息
//        printDatabaseInfo()
    }
    
    /// 打印数据库信息
    private func printDatabaseInfo() {
        if let storeURL = persistentContainer.persistentStoreCoordinator.persistentStores.first?.url {
            print("📊 数据库信息:")
            print("  - 路径: \(storeURL.path)")
            
            let fileManager = FileManager.default
            if fileManager.fileExists(atPath: storeURL.path) {
                print("  - 状态: 文件已存在")
                
                do {
                    let attributes = try fileManager.attributesOfItem(atPath: storeURL.path)
                    if let fileSize = attributes[.size] as? UInt64 {
                        print("  - 大小: \(fileSize) 字节")
                    }
                    if let modDate = attributes[.modificationDate] as? Date {
                        print("  - 最后修改: \(modDate)")
                    }
                } catch {
                    print("  - 属性读取错误: \(error)")
                }
            } else {
                print("  - 状态: 文件不存在（将在首次保存时创建）")
            }
            
            // 检查目录是否可写
            let directory = storeURL.deletingLastPathComponent()
            if fileManager.isWritableFile(atPath: directory.path) {
                print("  - 目录权限: 可写")
            } else {
                print("  - 目录权限: 不可写，这可能会导致数据保存问题")
            }
        }
    }
    
    /// 加载上次连接的设备数据库
    private func loadLastConnectedDeviceDatabase() {
        if let lastDeviceMAC = getLastConnectedDevice() {
            // 检查数据库文件是否存在
            let fileManager = FileManager.default
            let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
            let dbFolderURL = documentsURL.appendingPathComponent("WindRingData", isDirectory: true)
            let dbFileURL = dbFolderURL.appendingPathComponent("WindRingDataModel_\(lastDeviceMAC).sqlite")
            
            if fileManager.fileExists(atPath: dbFileURL.path) {
                // 如果数据库文件存在，切换到该数据库
                _ = switchToDatabase(for: lastDeviceMAC)
            } else {
                // 如果数据库文件不存在，清除记录
                clearLastConnectedDevice()
            }
        }
    }
    
    // MARK: - 公共方法
    
    /// 获取视图上下文
    /// - Returns: 视图上下文
    public func viewContext() -> NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    /// 在后台上下文中执行操作
    /// - Parameter operation: 要执行的操作
    public func performBackgroundTask(_ operation: @escaping (NSManagedObjectContext) -> Void) {
        backgroundContext.perform {
            operation(self.backgroundContext)
            
            if self.backgroundContext.hasChanges {
                do {
                    try self.backgroundContext.save()
                } catch {
                    print("后台上下文保存失败: \(error)")
                }
            }
        }
    }
    
    /// 在后台上下文中执行操作并返回结果
    /// - Parameter operation: 要执行的操作
    /// - Returns: 操作结果
    public func performBackgroundTaskAndWait<T>(_ operation: @escaping (NSManagedObjectContext) -> T) -> T {
        var result: T!
        
        backgroundContext.performAndWait {
            result = operation(self.backgroundContext)
            
            if self.backgroundContext.hasChanges {
                do {
                    try self.backgroundContext.save()
                } catch {
                    print("后台上下文保存失败: \(error)")
                }
            }
        }
        
        return result
    }
    
    /// 保存视图上下文
    public func saveViewContext() {
        let context = persistentContainer.viewContext
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                let nsError = error as NSError
                print("无法保存视图上下文: \(nsError), \(nsError.userInfo)")
            }
        }
    }
    
    /// 清除所有数据
    public func clearAllData() {
        let entityNames = persistentContainer.managedObjectModel.entities.compactMap { $0.name }
        
        performBackgroundTask { context in
            for entityName in entityNames {
                let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)
                let batchDeleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)
                batchDeleteRequest.resultType = .resultTypeObjectIDs
                
                do {
                    let result = try context.execute(batchDeleteRequest) as? NSBatchDeleteResult
                    if let objectIDs = result?.result as? [NSManagedObjectID] {
                        let changes = [NSDeletedObjectsKey: objectIDs]
                        NSManagedObjectContext.mergeChanges(fromRemoteContextSave: changes, into: [self.persistentContainer.viewContext])
                    }
                } catch {
                    print("清除实体 \(entityName) 失败: \(error)")
                }
            }
            
            // 通知数据已清除
            DispatchQueue.main.async {
                self.dataChangePublisher.send(.cleared)
            }
        }
    }
    
    // MARK: - 私有方法
    
    /// 设置通知观察者
    private func setupNotificationObservers() {
        // 观察持久化存储远程变更通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(storeRemoteChange(_:)),
            name: .NSPersistentStoreRemoteChange,
            object: persistentContainer.persistentStoreCoordinator
        )
    }
    
    /// 处理持久化存储远程变更通知
    @objc private func storeRemoteChange(_ notification: Notification) {
        // 合并远程变更到视图上下文
        persistentContainer.viewContext.perform {
            self.persistentContainer.viewContext.mergeChanges(fromContextDidSave: notification)
        }
        
        // 通知数据已更新
        DispatchQueue.main.async {
            self.dataChangePublisher.send(.updated)
        }
    }
}

/// 数据存储变更类型
public enum StorageChangeType {
    case created
    case updated
    case deleted
    case cleared
}

// MARK: - 辅助扩展
extension StorageManager {
    /// 检查实体是否存在
    /// - Parameters:
    ///   - entityName: 实体名称
    ///   - predicate: 谓词
    ///   - context: 上下文
    /// - Returns: 是否存在
    public func isEntityExists(
        entityName: String,
        predicate: NSPredicate,
        in context: NSManagedObjectContext? = nil
    ) -> Bool {
        let context = context ?? persistentContainer.viewContext
        let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)
        fetchRequest.predicate = predicate
        fetchRequest.fetchLimit = 1
        
        do {
            let count = try context.count(for: fetchRequest)
            return count > 0
        } catch {
            print("检查实体是否存在失败: \(error)")
            return false
        }
    }
    
    /// 获取实体数量
    /// - Parameters:
    ///   - entityName: 实体名称
    ///   - predicate: 谓词
    ///   - context: 上下文
    /// - Returns: 实体数量
    public func getEntityCount(
        entityName: String,
        predicate: NSPredicate? = nil,
        in context: NSManagedObjectContext? = nil
    ) -> Int {
        let context = context ?? persistentContainer.viewContext
        let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)
        fetchRequest.predicate = predicate
        
        do {
            let count = try context.count(for: fetchRequest)
            return count
        } catch {
            print("获取实体数量失败: \(error)")
            return 0
        }
    }
}

// MARK: - 批量操作扩展
extension StorageManager {
    /// 批量插入数据
    /// - Parameters:
    ///   - entityName: 实体名称
    ///   - objects: 要插入的对象数组
    ///   - configure: 配置对象的闭包
    public func batchInsert<T>(
        entityName: String,
        objects: [T],
        configure: @escaping (NSManagedObject, T) -> Void
    ) {
        guard !objects.isEmpty else { return }
        
        performBackgroundTask { context in
            for object in objects {
                let managedObject = NSEntityDescription.insertNewObject(forEntityName: entityName, into: context)
                configure(managedObject, object)
            }
            
            // 通知数据已创建
            DispatchQueue.main.async {
                self.dataChangePublisher.send(.created)
            }
        }
    }
    
    /// 批量更新数据
    /// - Parameters:
    ///   - entityName: 实体名称
    ///   - predicate: 谓词
    ///   - configure: 配置对象的闭包
    public func batchUpdate(
        entityName: String,
        predicate: NSPredicate,
        configure: @escaping (NSManagedObject) -> Void
    ) {
        performBackgroundTask { context in
            let fetchRequest = NSFetchRequest<NSManagedObject>(entityName: entityName)
            fetchRequest.predicate = predicate
            
            do {
                let objects = try context.fetch(fetchRequest)
                for object in objects {
                    configure(object)
                }
                
                // 通知数据已更新
                DispatchQueue.main.async {
                    self.dataChangePublisher.send(.updated)
                }
            } catch {
                print("批量更新失败: \(error)")
            }
        }
    }
    
    /// 批量删除数据
    /// - Parameters:
    ///   - entityName: 实体名称
    ///   - predicate: 谓词
    public func batchDelete(
        entityName: String,
        predicate: NSPredicate
    ) {
        performBackgroundTask { context in
            let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)
            fetchRequest.predicate = predicate
            
            let batchDeleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)
            batchDeleteRequest.resultType = .resultTypeObjectIDs
            
            do {
                let result = try context.execute(batchDeleteRequest) as? NSBatchDeleteResult
                if let objectIDs = result?.result as? [NSManagedObjectID] {
                    let changes = [NSDeletedObjectsKey: objectIDs]
                    NSManagedObjectContext.mergeChanges(fromRemoteContextSave: changes, into: [self.persistentContainer.viewContext])
                }
                
                // 通知数据已删除
                DispatchQueue.main.async {
                    self.dataChangePublisher.send(.deleted)
                }
            } catch {
                print("批量删除失败: \(error)")
            }
        }
    }
}

// MARK: - 查询扩展
extension StorageManager {
    /// 获取所有实体
    /// - Parameters:
    ///   - entityName: 实体名称
    ///   - predicate: 谓词
    ///   - sortDescriptors: 排序描述符
    ///   - context: 上下文
    /// - Returns: 实体数组
    public func fetchEntities<T: NSManagedObject>(
        entityName: String,
        predicate: NSPredicate? = nil,
        sortDescriptors: [NSSortDescriptor]? = nil,
        in context: NSManagedObjectContext? = nil
    ) -> [T] {
        let context = context ?? persistentContainer.viewContext
        let fetchRequest = NSFetchRequest<T>(entityName: entityName)
        fetchRequest.predicate = predicate
        fetchRequest.sortDescriptors = sortDescriptors
        
        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("获取实体失败: \(error)")
            return []
        }
    }
    
    /// 获取单个实体
    /// - Parameters:
    ///   - entityName: 实体名称
    ///   - predicate: 谓词
    ///   - context: 上下文
    /// - Returns: 实体
    public func fetchEntity<T: NSManagedObject>(
        entityName: String,
        predicate: NSPredicate,
        in context: NSManagedObjectContext? = nil
    ) -> T? {
        let context = context ?? persistentContainer.viewContext
        let fetchRequest = NSFetchRequest<T>(entityName: entityName)
        fetchRequest.predicate = predicate
        fetchRequest.fetchLimit = 1
        
        do {
            let results = try context.fetch(fetchRequest)
            return results.first
        } catch {
            print("获取单个实体失败: \(error)")
            return nil
        }
    }
    
    /// 获取分页实体
    /// - Parameters:
    ///   - entityName: 实体名称
    ///   - predicate: 谓词
    ///   - sortDescriptors: 排序描述符
    ///   - page: 页码
    ///   - pageSize: 每页大小
    ///   - context: 上下文
    /// - Returns: 实体数组
    public func fetchPaginatedEntities<T: NSManagedObject>(
        entityName: String,
        predicate: NSPredicate? = nil,
        sortDescriptors: [NSSortDescriptor]? = nil,
        page: Int,
        pageSize: Int,
        in context: NSManagedObjectContext? = nil
    ) -> [T] {
        let context = context ?? persistentContainer.viewContext
        let fetchRequest = NSFetchRequest<T>(entityName: entityName)
        fetchRequest.predicate = predicate
        fetchRequest.sortDescriptors = sortDescriptors
        fetchRequest.fetchLimit = pageSize
        fetchRequest.fetchOffset = (page - 1) * pageSize
        
        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("获取分页实体失败: \(error)")
            return []
        }
    }
}

// MARK: - 异步操作扩展
extension StorageManager {
    /// 异步获取所有实体
    /// - Parameters:
    ///   - entityName: 实体名称
    ///   - predicate: 谓词
    ///   - sortDescriptors: 排序描述符
    /// - Returns: 包含实体数组的Publisher
    public func fetchEntitiesPublisher<T: NSManagedObject>(
        entityName: String,
        predicate: NSPredicate? = nil,
        sortDescriptors: [NSSortDescriptor]? = nil
    ) -> AnyPublisher<[T], Error> {
        return Future<[T], Error> { promise in
            self.performBackgroundTask { context in
                let fetchRequest = NSFetchRequest<T>(entityName: entityName)
                fetchRequest.predicate = predicate
                fetchRequest.sortDescriptors = sortDescriptors
                
                do {
                    let results = try context.fetch(fetchRequest)
                    promise(.success(results))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }
    
    /// 异步获取单个实体
    /// - Parameters:
    ///   - entityName: 实体名称
    ///   - predicate: 谓词
    /// - Returns: 包含实体的Publisher
    public func fetchEntityPublisher<T: NSManagedObject>(
        entityName: String,
        predicate: NSPredicate
    ) -> AnyPublisher<T?, Error> {
        return Future<T?, Error> { promise in
            self.performBackgroundTask { context in
                let fetchRequest = NSFetchRequest<T>(entityName: entityName)
                fetchRequest.predicate = predicate
                fetchRequest.fetchLimit = 1
                
                do {
                    let results = try context.fetch(fetchRequest)
                    promise(.success(results.first))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }
}

// MARK: - 导入/导出扩展
extension StorageManager {
    /// 导出数据库
    /// - Returns: 数据库URL
    public func exportDatabase() -> URL? {
        guard let storeURL = persistentContainer.persistentStoreCoordinator.persistentStores.first?.url else {
            return nil
        }
        
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let exportURL = documentsDirectory.appendingPathComponent("WindRingDataExport.sqlite")
        
        do {
            if fileManager.fileExists(atPath: exportURL.path) {
                try fileManager.removeItem(at: exportURL)
            }
            
            try fileManager.copyItem(at: storeURL, to: exportURL)
            return exportURL
        } catch {
            print("导出数据库失败: \(error)")
            return nil
        }
    }
    
    /// 导入数据库
    /// - Parameter url: 数据库URL
    /// - Returns: 是否成功
    public func importDatabase(from url: URL) -> Bool {
        guard let storeURL = persistentContainer.persistentStoreCoordinator.persistentStores.first?.url else {
            return false
        }
        
        let fileManager = FileManager.default
        
        do {
            // 移除当前持久化存储
            try persistentContainer.persistentStoreCoordinator.destroyPersistentStore(at: storeURL, ofType: NSSQLiteStoreType, options: nil)
            
            // 复制导入的数据库
            try fileManager.copyItem(at: url, to: storeURL)
            
            // 重新加载持久化存储
            try persistentContainer.persistentStoreCoordinator.addPersistentStore(ofType: NSSQLiteStoreType, configurationName: nil, at: storeURL, options: nil)
            
            // 通知数据已更新
            DispatchQueue.main.async {
                self.dataChangePublisher.send(.updated)
            }
            
            return true
        } catch {
            print("导入数据库失败: \(error)")
            return false
        }
    }
}

// MARK: - 设备管理
extension StorageManager {
    /// 保存设备信息
    func saveDevice(deviceId: String, deviceName: String?, deviceType: String?, firmwareVersion: String?, hardwareVersion: String?, macAddress: String?, userId: String?, batteryLevel: Int16 = 0, isConnected: Bool = false) {
        let context = persistentContainer.viewContext
        
        // 检查设备是否已存在
        let fetchRequest = NSFetchRequest<NSManagedObject>(entityName: "DeviceEntity")
        fetchRequest.predicate = NSPredicate(format: "id == %@", deviceId)
        
        do {
            let results = try context.fetch(fetchRequest)
            let device: NSManagedObject
            
            if let existingDevice = results.first {
                device = existingDevice
            } else {
                device = NSEntityDescription.insertNewObject(forEntityName: "DeviceEntity", into: context)
                device.setValue(deviceId, forKey: "id")
                device.setValue(Date(), forKey: "createdAt")
            }
            
            // 更新设备信息
            device.setValue(deviceName, forKey: "name")
            device.setValue(deviceType, forKey: "type")
            device.setValue(firmwareVersion, forKey: "firmwareVersion")
            device.setValue(hardwareVersion, forKey: "model")
            device.setValue(macAddress, forKey: "manufacturer")
            device.setValue(userId, forKey: "userId")
            device.setValue(batteryLevel, forKey: "batteryLevel")
            device.setValue(isConnected ? "connected" : "disconnected", forKey: "connectionStatus")
            device.setValue(Date(), forKey: "updatedAt")
            device.setValue(Date(), forKey: "lastSyncTime")
            
            try context.save()
        } catch {
            print("保存设备信息失败: \(error.localizedDescription)")
        }
    }
    
    /// 获取设备信息
    func getDevice(deviceId: String) -> NSManagedObject? {
        let context = persistentContainer.viewContext
        let fetchRequest = NSFetchRequest<NSManagedObject>(entityName: "DeviceEntity")
        fetchRequest.predicate = NSPredicate(format: "id == %@", deviceId)
        
        do {
            let results = try context.fetch(fetchRequest)
            return results.first
        } catch {
            print("获取设备信息失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 获取用户的所有设备
    func getDevices(userId: String) -> [NSManagedObject] {
        let context = persistentContainer.viewContext
        let fetchRequest = NSFetchRequest<NSManagedObject>(entityName: "DeviceEntity")
        fetchRequest.predicate = NSPredicate(format: "userId == %@", userId)
        
        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("获取用户设备列表失败: \(error.localizedDescription)")
            return []
        }
    }
    
    /// 删除设备
    func deleteDevice(deviceId: String) {
        let context = persistentContainer.viewContext
        let fetchRequest = NSFetchRequest<NSManagedObject>(entityName: "DeviceEntity")
        fetchRequest.predicate = NSPredicate(format: "id == %@", deviceId)
        
        do {
            let results = try context.fetch(fetchRequest)
            if let device = results.first {
                context.delete(device)
                try context.save()
            }
        } catch {
            print("删除设备失败: \(error.localizedDescription)")
        }
    }
}

// MARK: - 用户管理
extension StorageManager {
    /// 保存用户信息
    func saveUser(userId: String, username: String?, email: String?, mobile: String?, avatar: String?) {
        let context = persistentContainer.viewContext
        
        // 检查用户是否已存在
        let fetchRequest = NSFetchRequest<NSManagedObject>(entityName: "UserEntity")
        fetchRequest.predicate = NSPredicate(format: "id == %@", userId)
        
        do {
            let results = try context.fetch(fetchRequest)
            let user: NSManagedObject
            
            if let existingUser = results.first {
                user = existingUser
            } else {
                user = NSEntityDescription.insertNewObject(forEntityName: "UserEntity", into: context)
                user.setValue(userId, forKey: "id")
                user.setValue(Date(), forKey: "createdAt")
            }
            
            // 更新用户信息
            user.setValue(username, forKey: "name")
            user.setValue(email, forKey: "email")
            user.setValue(mobile, forKey: "phone")
            user.setValue(avatar, forKey: "avatarUrl")
            user.setValue(Date(), forKey: "updatedAt")
            
            try context.save()
        } catch {
            print("保存用户信息失败: \(error.localizedDescription)")
        }
    }
    
    /// 获取用户信息
    func getUser(userId: String) -> NSManagedObject? {
        let context = persistentContainer.viewContext
        let fetchRequest = NSFetchRequest<NSManagedObject>(entityName: "UserEntity")
        fetchRequest.predicate = NSPredicate(format: "id == %@", userId)
        
        do {
            let results = try context.fetch(fetchRequest)
            return results.first
        } catch {
            print("获取用户信息失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 删除用户
    func deleteUser(userId: String) {
        let context = persistentContainer.viewContext
        let fetchRequest = NSFetchRequest<NSManagedObject>(entityName: "UserEntity")
        fetchRequest.predicate = NSPredicate(format: "id == %@", userId)
        
        do {
            let results = try context.fetch(fetchRequest)
            if let user = results.first {
                context.delete(user)
                try context.save()
            }
        } catch {
            print("删除用户失败: \(error.localizedDescription)")
        }
    }
    
    /// 更新用户最后登录时间
    func updateUserLastLogin(userId: String) {
        if let user = getUser(userId: userId) {
            user.setValue(Date(), forKey: "lastLoginAt")
            try? persistentContainer.viewContext.save()
        }
    }
    
    /// 设置用户状态
    func setUserActive(userId: String, isActive: Bool) {
        if let user = getUser(userId: userId) {
            user.setValue(isActive, forKey: "isActive")
            try? persistentContainer.viewContext.save()
        }
    }
}




