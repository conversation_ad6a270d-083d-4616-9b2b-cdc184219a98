//
//  extension.swift
//  WindRing
//
//  Created by zx on 2025/5/20.
//
//import CRPSmartRing
import Foundation
// MARK: - 训练记录相关方法
extension StorageManager {
    
    /// 保存训练统计数据
    /// - Parameters:
    ///   - stats: 训练统计数据
    ///   - userId: 用户ID
    ///   - deviceId: 设备ID
    ///   - date: 日期
    ///   - completion: 完成回调
    func saveTrainingStats(_ step: StepModel,
                          deviceId: String?,
                          date: Date,
                          completion: ((Bool) -> Void)? = nil) {
        performBackgroundTask { context in
            // 创建新的训练记录
            let record = TrainingRecordEntity(context: context)//TrainingRecord(context: context)
//            record.userId = userId
            record.deviceId = deviceId
            record.date = date
            record.steps = Int32(step.steps)
            record.distance = Int32(step.distance)
            record.calory = Int32(step.calory)
            record.exerciseTime = Int32(step.time)
            
            // 保存上下文
            do {
                try context.save()
                print("✅ 成功保存训练统计数据")
                completion?(true)
            } catch {
                print("❌ 保存训练统计数据失败: \(error)")
                completion?(false)
            }
        }
    }
    
    /// 获取指定日期的训练统计数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - deviceId: 设备ID
    ///   - date: 日期
    /// - Returns: 训练统计数据
    func getTrainingStats(userId: String,
                         deviceId: String?,
                         date: Date) -> StepModel? {
        // 获取指定日期的开始和结束时间
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
        
        // 创建查询条件
        let predicate = NSPredicate(format: "userId == %@ AND deviceId == %@ AND date >= %@ AND date < %@",
                                  userId,
                                  deviceId ?? "",
                                  startOfDay as NSDate,
                                  endOfDay as NSDate)
        
        // 获取记录
        let records = fetchEntities(
            entityName: "TrainingRecord",
            predicate: predicate,
            sortDescriptors: [NSSortDescriptor(key: "date", ascending: false)]
        ) as [TrainingRecordEntity]
        
        // 如果找到记录，转换为 CRPTrainingStatsModel
        if let record = records.first {
            return StepModel(
                steps: Int(record.steps),
                distance: Int(record.distance),
                calory: Int(record.calory),
                time: Int(record.exerciseTime)
            )
        }
        
        return nil
    }
}
