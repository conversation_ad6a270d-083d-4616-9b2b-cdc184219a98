import SwiftUI

/// 健康数据卡片组件
struct HealthDataCard: View {
    // MARK: - 属性
    let title: String
    let iconName: String
    let iconColor: Color
    let value: String
    let unit: String
    let description: String
    let trend: Trend?
    
    // 初始化方法
    init(
        title: String,
        iconName: String,
        iconColor: Color,
        value: String,
        unit: String,
        description: String = "recent 7-day average",
        trend: Trend? = nil
    ) {
        self.title = title
        self.iconName = iconName
        self.iconColor = iconColor
        self.value = value
        self.unit = unit
        self.description = description
        self.trend = trend
    }
    
    // MARK: - 主视图
    var body: some View {
        VStack(spacing: 0) {
            // 卡片内容
            VStack(spacing: 16) {
                // 标题栏
                HStack {
                    // 图标和标题
                    HStack(spacing: 12) {
                        Image(systemName: iconName)
                            .font(.title2)
                            .foregroundColor(iconColor)
                        
                        Text(title)
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    
                    Spacer()
                    
                    // 趋势指示器
                    if let trend = trend {
                        HStack(spacing: 4) {
                            Image(systemName: trend.iconName)
                                .font(.caption)
                                .foregroundColor(trend.color)
                            
                            Text(trend.text)
                                .font(.caption)
                                .foregroundColor(trend.color)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(trend.color.opacity(0.1))
                        .cornerRadius(12)
                    }
                }
                
                // 数据值
                HStack(alignment: .lastTextBaseline, spacing: 4) {
                    Text(value)
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                    
                    Text(unit)
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .padding(.leading, 2)
                }
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.vertical, 8)
            }
            .padding()
            
            // 底部描述
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .trailing)
                .padding(.horizontal)
                .padding(.bottom, 12)
        }
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
}

// MARK: - 趋势枚举
enum Trend {
    case up
    case down
    case stable
    
    var iconName: String {
        switch self {
        case .up:
            return "arrow.up"
        case .down:
            return "arrow.down"
        case .stable:
            return "arrow.right"
        }
    }
    
    var text: String {
        switch self {
        case .up:
            return "上升"
        case .down:
            return "下降"
        case .stable:
            return "稳定"
        }
    }
    
    var color: Color {
        switch self {
        case .up:
            return .red
        case .down:
            return .green
        case .stable:
            return .blue
        }
    }
}

// MARK: - 预览
struct HealthDataCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            HealthDataCard(
                title: "心率",
                iconName: "heart.fill",
                iconColor: .pink,
                value: "72",
                unit: "bpm",
                trend: .stable
            )
            
            HealthDataCard(
                title: "睡眠",
                iconName: "moon.zzz.fill",
                iconColor: .purple,
                value: "7.5",
                unit: "小时",
                trend: .up
            )
            
            HealthDataCard(
                title: "步数",
                iconName: "figure.walk",
                iconColor: .blue,
                value: "8,542",
                unit: "步",
                trend: .down
            )
        }
        .padding()
        .background(Color.moduleBackground)
        .previewLayout(.sizeThatFits)
    }
} 