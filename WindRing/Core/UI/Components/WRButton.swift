import SwiftUI

/// WindRing应用的标准按钮组件
/// 支持多种样式和状态
public struct WRButton: View {
    // MARK: - 按钮样式枚举
    public enum ButtonStyle {
        case primary    // 主要按钮，用于关键操作
        case secondary  // 次要按钮，用于普通操作
        case tertiary   // 第三级按钮，用于次要操作
        case ghost      // 幽灵按钮，只有边框
        case text       // 文本按钮，没有背景和边框
        case danger     // 危险操作按钮
    }
    
    // MARK: - 按钮尺寸枚举
    public enum ButtonSize {
        case small      // 小尺寸按钮
        case medium     // 中等尺寸按钮
        case large      // 大尺寸按钮
    }
    
    // MARK: - 属性
    private let title: String
    private let icon: Image?
    private let style: ButtonStyle
    private let size: ButtonSize
    private let isFullWidth: Bool
    private let isLoading: Bool
    private let action: () -> Void
    
    // MARK: - 初始化方法
    public init(
        title: String,
        icon: Image? = nil,
        style: ButtonStyle = .primary,
        size: ButtonSize = .medium,
        isFullWidth: Bool = false,
        isLoading: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.style = style
        self.size = size
        self.isFullWidth = isFullWidth
        self.isLoading = isLoading
        self.action = action
    }
    
    // MARK: - Body
    public var body: some View {
        Button(action: {
            if !isLoading {
                action()
            }
        }) {
            HStack(spacing: 8) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: foregroundColor))
                        .frame(width: 16, height: 16)
                } else if let icon = icon {
                    icon
                        .resizable()
                        .scaledToFit()
                        .frame(width: iconSize, height: iconSize)
                }
                
                Text(title)
                    .font(font)
                    .fontWeight(.semibold)
            }
            .padding(padding)
            .frame(maxWidth: isFullWidth ? .infinity : nil)
            .background(backgroundColor)
            .foregroundColor(foregroundColor)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
        }
        .disabled(isLoading)
    }
    
    // MARK: - 辅助计算属性
    
    // 背景颜色
    private var backgroundColor: Color {
        switch style {
        case .primary:
            return Color.blue
        case .secondary:
            return Color.gray.opacity(0.1)
        case .tertiary:
            return Color.white
        case .ghost, .text:
            return Color.clear
        case .danger:
            return Color.red
        }
    }
    
    // 前景颜色（文字和图标）
    private var foregroundColor: Color {
        switch style {
        case .primary:
            return Color.white
        case .secondary, .tertiary:
            return Color.primary
        case .ghost:
            return Color.blue
        case .text:
            return Color.blue
        case .danger:
            return Color.white
        }
    }
    
    // 边框颜色
    private var borderColor: Color {
        switch style {
        case .ghost:
            return Color.blue
        case .tertiary:
            return Color.gray.opacity(0.3)
        default:
            return Color.clear
        }
    }
    
    // 边框宽度
    private var borderWidth: CGFloat {
        switch style {
        case .ghost, .tertiary:
            return 1
        default:
            return 0
        }
    }
    
    // 按钮内边距
    private var padding: EdgeInsets {
        switch size {
        case .small:
            return EdgeInsets(top: 6, leading: 12, bottom: 6, trailing: 12)
        case .medium:
            return EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 16)
        case .large:
            return EdgeInsets(top: 14, leading: 20, bottom: 14, trailing: 20)
        }
    }
    
    // 字体大小
    private var font: Font {
        switch size {
        case .small:
            return .subheadline
        case .medium:
            return .body
        case .large:
            return .title3
        }
    }
    
    // 图标大小
    private var iconSize: CGFloat {
        switch size {
        case .small:
            return 14
        case .medium:
            return 18
        case .large:
            return 22
        }
    }
}

// MARK: - 预览
struct WRButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            WRButton(title: "主要按钮", style: .primary) {}
            WRButton(title: "次要按钮", style: .secondary) {}
            WRButton(title: "第三级按钮", style: .tertiary) {}
            WRButton(title: "幽灵按钮", style: .ghost) {}
            WRButton(title: "文本按钮", style: .text) {}
            WRButton(title: "危险按钮", style: .danger) {}
            
            WRButton(title: "带图标的按钮", icon: Image(systemName: "heart.fill")) {}
            WRButton(title: "加载中按钮", isLoading: true) {}
            WRButton(title: "全宽按钮", isFullWidth: true) {}
            
            HStack {
                WRButton(title: "小", size: .small) {}
                WRButton(title: "中", size: .medium) {}
                WRButton(title: "大", size: .large) {}
            }
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
} 