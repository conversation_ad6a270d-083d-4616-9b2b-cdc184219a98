import SwiftUI

/// WindRing应用的标准卡片组件
/// 用于展示健康数据和信息
public struct WRCard<Content: View>: View {
    // MARK: - 卡片样式枚举
    public enum CardStyle {
        case standard    // 标准卡片，带阴影和圆角
        case outlined    // 带边框的卡片
        case flat        // 扁平卡片，只有背景色
        case elevated    // 高度提升的卡片，阴影更明显
    }
    
    // MARK: - 属性
    private let title: String?
    private let subtitle: String?
    private let icon: Image?
    private let style: CardStyle
    private let cornerRadius: CGFloat
    private let padding: CGFloat
    private let content: Content
    
    // MARK: - 初始化方法
    public init(
        title: String? = nil,
        subtitle: String? = nil,
        icon: Image? = nil,
        style: CardStyle = .standard,
        cornerRadius: CGFloat = 16,
        padding: CGFloat = 16,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.style = style
        self.cornerRadius = cornerRadius
        self.padding = padding
        self.content = content()
    }
    
    // MARK: - Body
    public var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            if title != nil || subtitle != nil || icon != nil {
                HStack(spacing: 12) {
                    if let icon = icon {
                        icon
                            .resizable()
                            .scaledToFit()
                            .frame(width: 24, height: 24)
                            .foregroundColor(.blue)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        if let title = title {
                            Text(title)
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        
                        if let subtitle = subtitle {
                            Text(subtitle)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                }
                
                Divider()
            }
            
            content
        }
        .padding(padding)
        .background(backgroundColor)
        .cornerRadius(cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: cornerRadius)
                .stroke(borderColor, lineWidth: borderWidth)
        )
        .shadow(color: shadowColor, radius: shadowRadius, x: 0, y: shadowY)
    }
    
    // MARK: - 辅助计算属性
    
    // 背景颜色
    private var backgroundColor: Color {
        Color.white
    }
    
    // 边框颜色
    private var borderColor: Color {
        style == .outlined ? Color.gray.opacity(0.2) : Color.clear
    }
    
    // 边框宽度
    private var borderWidth: CGFloat {
        style == .outlined ? 1 : 0
    }
    
    // 阴影颜色
    private var shadowColor: Color {
        switch style {
        case .standard:
            return Color.black.opacity(0.05)
        case .elevated:
            return Color.black.opacity(0.1)
        default:
            return Color.clear
        }
    }
    
    // 阴影半径
    private var shadowRadius: CGFloat {
        switch style {
        case .standard:
            return 4
        case .elevated:
            return 8
        default:
            return 0
        }
    }
    
    // 阴影Y偏移
    private var shadowY: CGFloat {
        switch style {
        case .standard:
            return 2
        case .elevated:
            return 4
        default:
            return 0
        }
    }
}

// MARK: - 预览
struct WRCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            WRCard(title: "睡眠质量", subtitle: "昨晚睡眠分析", icon: Image(systemName: "bed.double.fill")) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("睡眠时长: 7小时32分钟")
                    Text("深度睡眠: 2小时15分钟")
                    Text("睡眠评分: 85/100")
                }
            }
            
            WRCard(style: .outlined) {
                Text("这是一个简单的卡片，没有标题")
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 8)
            }
            
            WRCard(title: "活动记录", style: .elevated) {
                Text("今日步数: 8,456步")
                Text("消耗卡路里: 356千卡")
            }
            
            WRCard(style: .flat, padding: 0) {
                Image(systemName: "heart.fill")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 50, height: 50)
                    .foregroundColor(.red)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
                
                Text("心率监测")
                    .font(.headline)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.bottom)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .previewLayout(.sizeThatFits)
    }
} 