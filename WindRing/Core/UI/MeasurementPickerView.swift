import SwiftUI



struct MeasurementPickerView: View {
    let type: MeasurementType
    @Binding var value: Double // Always in metric (cm or kg)
    @Binding var unitSystem: UnitSystem
    @Binding var isPresented: Bool

    @State private var internalMetricValue: Int
    @State private var internalImperialValue: Int
    @State private var internalImperialValueInches: Int // For height in imperial

    // Ranges
    private let heightMetricRange = 50...250 // cm
    private let heightImperialFeetRange = 1...8 // ft
    private let heightImperialInchesRange = 0...11 // in
    private let weightMetricRange = 20...200 // kg
    private let weightImperialRange = 40...440 // lbs

    init(type: MeasurementType, value: Binding<Double>, unitSystem: Binding<UnitSystem>, isPresented: Binding<Bool>) {
        self.type = type
        self._value = value
        self._unitSystem = unitSystem
        self._isPresented = isPresented

        let initialValue = value.wrappedValue
        
        if type == .height {
            // cm
            _internalMetricValue = State(initialValue: Int(round(initialValue)))
            // feet and inches
            let totalInches = initialValue / 2.54
            let feet = floor(totalInches / 12)
            let inches = round(totalInches.truncatingRemainder(dividingBy: 12))
            _internalImperialValue = State(initialValue: Int(feet))
            _internalImperialValueInches = State(initialValue: Int(inches))

        } else { // Weight
            // kg
            _internalMetricValue = State(initialValue: Int(round(initialValue)))
            // lbs
            let pounds = initialValue * 2.20462
            _internalImperialValue = State(initialValue: Int(round(pounds)))
            _internalImperialValueInches = State(initialValue: 0) // Not used for weight
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Header
            ZStack {
                Text(type == .height ? "height".localized : "weight".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                HStack {
                    Spacer()
                    Button(action: { isPresented = false }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 15)

            // Unit System Picker
            Picker("Unit System", selection: $unitSystem) {
                ForEach(UnitSystem.allCases) { system in
                    Text(system.localized).tag(system)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .background(Color(hex: "#333333"))
            .cornerRadius(8)
            .padding(.horizontal, 20)
            .padding(.bottom, 10)
            
            // Value Picker
            pickerContent
                .padding(.horizontal, 20)


            // Confirm Button
            Button(action: confirmSelection) {
                Text("confirm".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(height: 44)
                    .frame(maxWidth: .infinity)
                    .background(Color.blue)
                    .cornerRadius(22)
            }
            .padding(20)
        }
        .background(Color(hex: "#1E1E1E").edgesIgnoringSafeArea(.bottom))
    }

    @ViewBuilder
    private var pickerContent: some View {
        if unitSystem == .metric {
            metricPicker
        } else {
            imperialPicker
        }
    }

    private var metricPicker: some View {
        let range = type == .height ? heightMetricRange : weightMetricRange
        let unit = type == .height ? "unit_cm".localized : "unit_kg".localized

        return GeometryReader { geometry in
            HStack(spacing: 0) {
                Picker(selection: $internalMetricValue, label: EmptyView()) {
                    ForEach(range, id: \.self) { value in
                        Text("\(value)").tag(value)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(width: geometry.size.width / 2)
                .clipped()

                Text(unit)
                    .font(.system(size: 16))
                    .frame(width: geometry.size.width / 2)
            }
        }
        .frame(height: 160)
    }

    private var imperialPicker: some View {
        GeometryReader { geometry in
            if type == .height {
                HStack(spacing: 0) {
                    // Feet
                    Picker(selection: $internalImperialValue, label: EmptyView()) {
                        ForEach(heightImperialFeetRange, id: \.self) { value in
                            Text("\(value)").tag(value)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(width: geometry.size.width * 0.4)
                    .clipped()
                    
                    Text("unit_ft".localized)
                        .font(.system(size: 16))
                        .frame(width: geometry.size.width * 0.2)

                    // Inches
                    Picker(selection: $internalImperialValueInches, label: EmptyView()) {
                        ForEach(heightImperialInchesRange, id: \.self) { value in
                            Text("\(value)").tag(value)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(width: geometry.size.width * 0.4)
                    .clipped()
                    
                     Text("in") // "in" is standard, maybe no need to localize
                        .font(.system(size: 16))
                }
            } else { // Weight
                HStack(spacing: 0) {
                    Picker(selection: $internalImperialValue, label: EmptyView()) {
                        ForEach(weightImperialRange, id: \.self) { value in
                            Text("\(value)").tag(value)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(width: geometry.size.width / 2)
                    .clipped()

                    Text("unit_lbs".localized)
                        .font(.system(size: 16))
                        .frame(width: geometry.size.width / 2)
                }
            }
        }
        .frame(height: 160)
    }

    private func confirmSelection() {
        if unitSystem == .metric {
            value = Double(internalMetricValue)
        } else {
            if type == .height {
                let totalInches = Double(internalImperialValue * 12 + internalImperialValueInches)
                value = totalInches * 2.54 // to cm
            } else {
                value = Double(internalImperialValue) / 2.20462 // to kg
            }
        }
        isPresented = false
    }
} 
