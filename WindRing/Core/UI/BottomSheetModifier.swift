//
//  BottomSheetModifier.swift
//  WindRing
//
//  Created by zx on 2025/7/3.
//

import SwiftUI


/// 弹窗内容结构体，存储 key 和对应视图
struct BottomSheetItem: Identifiable, Equatable {
    let id: String // key
    let content: AnyView
    static func == (lhs: BottomSheetItem, rhs: BottomSheetItem) -> Bool {
        return lhs.id == rhs.id
        // content 无法比较，只比较 id 就足够判定唯一性了
    }
}

/// 弹窗管理器（单例）
final class BottomSheetManager: ObservableObject {
    static let shared = BottomSheetManager()

    @Published private(set) var sheets: [BottomSheetItem] = []

    private init() {}

    /// 显示弹窗，key 唯一，content 视图
    func present<V: View>(key: String, @ViewBuilder content: @escaping () -> V) {
        DispatchQueue.main.async {
            if let idx = self.sheets.firstIndex(where: { $0.id == key }) {
                self.sheets[idx] = BottomSheetItem(id: key, content: AnyView(content()))
            } else {
                self.sheets.append(BottomSheetItem(id: key, content: AnyView(content())))
            }
        }
    }

    /// 隐藏弹窗
    func dismiss(key: String) {
        DispatchQueue.main.async {
            self.sheets.removeAll(where: { $0.id == key })
        }
    }

    /// 是否存在某个弹窗
    func isPresented(key: String) -> Bool {
        sheets.contains(where: { $0.id == key })
    }
}

struct BottomSheetContainer: View {
    @ObservedObject var manager = BottomSheetManager.shared

    var body: some View {
        ZStack {
            if let topSheet = manager.sheets.last {
                // 遮罩
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        manager.dismiss(key: topSheet.id)
                    }
                    .transition(.opacity)
                    .zIndex(999)

                // 底部弹出的弹窗内容
                VStack {
                    Spacer() // 占满上面空间，将内容推到底部
                    topSheet.content
//                        .background(
//                            // 可选：给弹窗背景加圆角和阴影
//                            RoundedRectangle(cornerRadius: 16)
//                                .fill(Color.white)
//                                .shadow(radius: 5)
//                        )
//                        .padding(.horizontal, 16)
//                        .padding(.bottom, 0) // 底部间距，避免紧贴屏幕边缘
                }
                .zIndex(1000)
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .animation(.easeInOut, value: manager.sheets)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .animation(.easeInOut, value: manager.sheets)
    }
}

struct BottomSheetModifier<SheetContent: View>: ViewModifier {
    @Binding var isPresented: Bool
    let backgroundOpacity: Double
    let onDismiss: (() -> Void)?
    let content: () -> SheetContent

    @GestureState private var dragOffset: CGSize = .zero
    @State private var sheetHeight: CGFloat = .zero
    
    func body(content base: Content) -> some View {
        ZStack {
            base
            
            if isPresented {
                Color.black
                    .opacity(backgroundOpacity)
                    .ignoresSafeArea()
                    .onTapGesture {
                        dismiss()
                    }

                VStack {
                    Spacer()

                    sheetView()
                        .background(GeometryReader { proxy in
                            Color.clear
                                .preference(key: SheetHeightKey.self, value: proxy.size.height)
                        })
                        .offset(y: max(dragOffset.height, 0))
                        .animation(.easeOut, value: dragOffset)
                        .gesture(
                            DragGesture()
                                .updating($dragOffset) { value, state, _ in
                                    state = value.translation
                                }
                                .onEnded { value in
                                    if value.translation.height > 100 {
                                        dismiss()
                                    }
                                }
                        )
                        .transition(.move(edge: .bottom))
                }
                .onPreferenceChange(SheetHeightKey.self) { height in
                    self.sheetHeight = height
                }
            }
        }
    }
    
    @ViewBuilder
    private func sheetView() -> some View {
        VStack {
            self.content()
        }
        .frame(maxWidth: .infinity)
        .background(Color(hex: "#242733"))
        .cornerRadius(20, corners: [.topLeft, .topRight])
    }

    private func dismiss() {
        withAnimation {
            isPresented = false
            onDismiss?()
        }
    }
}

private struct SheetHeightKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = max(value, nextValue())
    }
}
extension View {
    func bottomSheet<Content: View>(
        isPresented: Binding<Bool>,
        backgroundOpacity: Double = 0.5,
        onDismiss: (() -> Void)? = nil,
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        self.modifier(BottomSheetModifier(
            isPresented: isPresented,
            backgroundOpacity: backgroundOpacity,
            onDismiss: onDismiss,
            content: content
        ))
    }
}
