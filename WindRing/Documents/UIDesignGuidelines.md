# WindRing健康应用UI设计规范

## 品牌标识

### 品牌理念
WindRing代表"风环"，象征轻盈、自然、流动的健康生活方式。设计语言应体现轻量感、流畅性和科技感，同时保持简洁易用的特性。

### 标志
- **主标志**：环形设计，象征智能戒指和生命周期
- **辅助图形**：波浪线元素，代表健康数据的连续监测
- **标志尺寸**：提供多种尺寸规范，确保在不同场景下的清晰度
- **安全空间**：标志周围预留至少1x标志高度的安全空间

### 品牌色彩
- **主色**：深青色 #0A7B83（代表平静、健康、科技）
- **辅助色**：
  - 浅青色 #4CBBB9（活力、积极）
  - 深蓝色 #2E4057（稳重、专业）
  - 珊瑚红 #FF6B6B（警示、重要信息）
  - 淡黄色 #FFE66D（提示、积极反馈）

## 色彩系统

### 主色调
- **主色-100**：#E6F3F4
- **主色-200**：#BFE1E3
- **主色-300**：#8CCFD2
- **主色-400**：#59BDC1
- **主色-500**：#0A7B83（基准色）
- **主色-600**：#086269
- **主色-700**：#06494F
- **主色-800**：#043136
- **主色-900**：#02181C

### 中性色
- **灰色-100**：#F8F9FA
- **灰色-200**：#E9ECEF
- **灰色-300**：#DEE2E6
- **灰色-400**：#CED4DA
- **灰色-500**：#ADB5BD
- **灰色-600**：#6C757D
- **灰色-700**：#495057
- **灰色-800**：#343A40
- **灰色-900**：#212529

### 功能色
- **成功**：#28A745
- **信息**：#17A2B8
- **警告**：#FFC107
- **危险**：#DC3545
- **轻度**：#6C757D
- **深色**：#343A40
- **浅色**：#F8F9FA

### 数据可视化色彩
- **数据-1**：#4CBBB9
- **数据-2**：#2E4057
- **数据-3**：#FF6B6B
- **数据-4**：#FFE66D
- **数据-5**：#7A9E9F
- **数据-6**：#B8D8D8
- **数据-7**：#4F6367
- **数据-8**：#EEF5DB

## 排版系统

### 字体
- **主要字体**：
  - iOS：SF Pro Text（系统字体）
  - Android：Roboto（系统字体）
- **显示字体**（大标题、特殊强调）：
  - iOS：SF Pro Display
  - Android：Roboto Medium

### 字体大小
- **超大标题**：32px / 40px行高
- **大标题**：28px / 36px行高
- **标题1**：24px / 32px行高
- **标题2**：20px / 28px行高
- **标题3**：18px / 26px行高
- **正文（大）**：16px / 24px行高
- **正文（标准）**：14px / 22px行高
- **正文（小）**：12px / 20px行高
- **说明文字**：10px / 16px行高

### 字重
- **细体**：Light (300)
- **常规**：Regular (400)
- **中等**：Medium (500)
- **粗体**：Bold (700)

### 段落样式
- **行间距**：1.5倍字体大小
- **段落间距**：2倍字体大小
- **对齐方式**：左对齐（默认）

## 间距系统

### 基础间距
- **基础单位**：4px
- **间距比例**：4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px

### 内边距规范
- **卡片内边距**：16px
- **按钮内边距**：水平12px，垂直8px
- **输入框内边距**：水平12px，垂直10px
- **列表项内边距**：水平16px，垂直12px

### 外边距规范
- **组件间距**：16px
- **分组间距**：24px
- **屏幕边缘间距**：16px（手机），24px（平板）

## 组件库

### 基础组件

#### 按钮
- **主要按钮**：填充主色，圆角8px，高度44px
- **次要按钮**：描边主色，圆角8px，高度44px
- **文本按钮**：无背景，主色文字，高度44px
- **图标按钮**：圆形，直径44px
- **状态**：默认、悬停、按下、禁用

#### 输入框
- **标准输入框**：圆角8px，边框1px，高度48px
- **搜索输入框**：带搜索图标，圆角24px
- **多行输入框**：圆角8px，最小高度96px
- **状态**：默认、聚焦、错误、禁用

#### 选择器
- **单选框**：圆形选择器，直径20px
- **复选框**：方形选择器，圆角4px，尺寸20px
- **开关**：胶囊形状，宽度52px，高度32px
- **滑块**：高度4px，滑块直径24px

#### 卡片
- **标准卡片**：圆角12px，阴影0 2px 8px rgba(0,0,0,0.1)
- **突出卡片**：圆角12px，阴影0 4px 16px rgba(0,0,0,0.15)
- **平面卡片**：圆角12px，边框1px，无阴影

### 复合组件

#### 导航栏
- **顶部导航栏**：高度56px，阴影0 2px 4px rgba(0,0,0,0.1)
- **底部标签栏**：高度56px，顶部分隔线1px
- **侧边导航**：宽度240px（平板/桌面）

#### 列表
- **标准列表项**：高度64px，底部分隔线1px
- **小型列表项**：高度48px，底部分隔线1px
- **图片列表项**：高度80px，底部分隔线1px

#### 对话框
- **标准对话框**：圆角16px，宽度占屏幕宽度的85%
- **底部表单**：从底部滑出，圆角顶部16px
- **全屏对话框**：占满整个屏幕，顶部有关闭按钮

#### 通知
- **轻提示**：底部弹出，自动消失，圆角8px
- **横幅通知**：顶部弹出，可手动关闭，圆角8px
- **对话框通知**：中央弹出，需用户确认，圆角16px

### 数据展示组件

#### 图表
- **线图**：展示连续数据趋势，如心率、睡眠质量
- **柱状图**：展示离散数据对比，如每日活动量
- **饼图/环形图**：展示构成比例，如睡眠分期
- **雷达图**：展示多维健康评分

#### 进度指示器
- **环形进度**：展示目标完成度，如日活动目标
- **线性进度**：展示过程进度，如数据同步进度
- **阶段进度**：展示多阶段进度，如睡眠阶段

#### 数据卡片
- **概览卡片**：展示关键数据摘要，如健康评分
- **详情卡片**：展示详细数据分析，如睡眠分析
- **趋势卡片**：展示数据变化趋势，如周/月趋势

## 交互规范

### 手势交互
- **点击/轻触**：触发按钮、选择项目
- **长按**：显示上下文菜单、进入编辑模式
- **滑动**：滚动内容、切换页面、显示操作
- **捏合/展开**：缩放图表、图片
- **双击**：快速放大/缩小、激活特定功能

### 动效设计
- **页面转场**：自然流畅，持续时间300ms
- **组件状态变化**：平滑过渡，持续时间200ms
- **加载动画**：简洁明了，表达处理进度
- **反馈动画**：轻微弹性效果，增强交互感
- **数据更新动画**：渐变过渡，持续时间500ms

### 反馈机制
- **视觉反馈**：状态变化、高亮效果
- **触觉反馈**：轻微振动（成功、错误、警告）
- **声音反馈**：简短提示音（可在设置中关闭）

## 响应式设计

### 设备适配
- **手机**：iPhone各尺寸（最小支持iPhone SE）
- **平板**：iPad各尺寸
- **手表**：Apple Watch（配套应用）

### 布局系统
- **网格系统**：4列（手机竖屏）、8列（手机横屏）、12列（平板）
- **断点**：
  - 小型设备：< 375px
  - 中型设备：376px - 767px
  - 大型设备：768px - 1023px
  - 超大设备：> 1024px

### 自适应内容
- **字体缩放**：支持系统字体大小调整
- **组件缩放**：关键交互元素最小尺寸保证可点击
- **内容重排**：不同屏幕尺寸下的内容布局调整

## 无障碍设计

### 色彩对比
- 文本与背景对比度符合WCAG 2.1 AA标准
- 提供高对比度模式

### 辅助功能支持
- 支持VoiceOver/TalkBack屏幕阅读
- 所有交互元素添加适当的辅助标签
- 支持动作减弱、减少动画

### 操作便利性
- 交互元素最小点击区域44×44px
- 提供键盘导航支持（iPad外接键盘）
- 合理的标签和提示文本

## 设计资源

### 设计系统文件
- Figma组件库
- Sketch UI套件
- Adobe XD UI套件

### 图标库
- 线性图标集（24×24px网格）
- 填充图标集（24×24px网格）
- 特殊功能图标

### 插图系统
- 空状态插图
- 引导页插图
- 成就徽章插图

## 实现指南

### iOS实现
- 基于SwiftUI组件实现
- 使用SF Symbols作为图标系统
- 适配深色模式

### 设计审核
- 设计一致性检查清单
- UI评审流程
- 用户测试反馈整合机制 