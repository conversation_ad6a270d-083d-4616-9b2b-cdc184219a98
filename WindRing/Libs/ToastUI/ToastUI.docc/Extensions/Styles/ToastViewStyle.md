# ``ToastUI/ToastViewStyle``

## Topics

### Built-in Toast View Styles

- ``default``
- ``indeterminate``
- ``determinate(value:total:)``
- ``icon(content:)``
- ``success``
- ``failure``
- ``warning``
- ``information``

### Custom Toast View Styles

- ``makeBody(configuration:)``
- ``Configuration``
- ``Body``

### Supporting Types

- ``DefaultToastViewStyle``
- ``IndeterminateProgressToastViewStyle``
- ``DeterminateProgressToastViewStyle``
- ``IconToastViewStyle``
- ``SuccessToastViewStyle``
- ``FailureToastViewStyle``
- ``WarningToastViewStyle``
- ``InformationToastViewStyle``

## See Also

- ``ToastView/toastViewStyle(_:)``
