//
//  ToastManager.swift
//  WindRing
//
//  Created by zx on 2025/6/17.
//

import Foundation
import Combine

// 全局Toast管理器
class ToastManager: ObservableObject {
    @Published var presentingToast = false
    @Published var toastMessage = ""
    @Published var dismissAfter: Double = 2.0
    
    static let shared = ToastManager()
    
    private init() {}
    
    func showToast(message: String, dismissAfter: Double = 2.0) {
        self.toastMessage = message
        self.dismissAfter = dismissAfter
        self.presentingToast = true
    }
}

extension String {
    func showToast(){
        ToastManager.shared.showToast(message: self, dismissAfter: 1.0)
    }
}


