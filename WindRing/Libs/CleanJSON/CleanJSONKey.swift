// 
//  CleanJSONKey.swift
//  CleanJSON
//
//  Created by <PERSON><PERSON><PERSON>(<EMAIL>) on 2018/10/11
//  Copyright © 2018 Pircate. All rights reserved.
//

import Foundation

struct CleanJSONKey: CodingKey {
    
    public var stringValue: String
    
    public var intValue: Int?
    
    public init?(stringValue: String) {
        self.stringValue = stringValue
        self.intValue = nil
    }
    
    public init?(intValue: Int) {
        self.stringValue = "\(intValue)"
        self.intValue = intValue
    }
    
    public init(stringValue: String, intValue: Int?) {
        self.stringValue = stringValue
        self.intValue = intValue
    }
    
    init(index: Int) {
        self.stringValue = "Index \(index)"
        self.intValue = index
    }
    
    static let `super` = CleanJSONKey(stringValue: "super")!
}
