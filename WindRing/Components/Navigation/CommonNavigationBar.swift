import SwiftUI

/// 通用导航栏组件
struct CommonNavigationBar: View {
    // MARK: - 属性
    let title: String
    var titleFont: Font = .custom("PingFang-SC-Heavy", size: 19)
    var titleColor: Color = .white
    var backgroundColor: Color = .clear
    var showBackButton: Bool = true
    var backButtonColor: Color = .white
    var backButtonTitle: String = ""
    var rightItems: [NavigationBarItem] = []
    var onBackButtonTapped: (() -> Void)?
    
    @Environment(\.presentationMode) private var presentationMode
    
    // MARK: - 初始化方法
    init(
        title: String,
        titleFont: Font = .custom("PingFang-SC-Heavy", size: 19),
        titleColor: Color = .white,
        backgroundColor: Color = .clear,
        showBackButton: Bool = true,
        backButtonColor: Color = .white,
        backButtonTitle: String = "",
        rightItems: [NavigationBarItem] = [],
        onBackButtonTapped: (() -> Void)? = nil
    ) {
        self.title = title
        self.titleFont = titleFont
        self.titleColor = titleColor
        self.backgroundColor = backgroundColor
        self.showBackButton = showBackButton
        self.backButtonColor = backButtonColor
        self.backButtonTitle = backButtonTitle
        self.rightItems = rightItems
        self.onBackButtonTapped = onBackButtonTapped
    }
    
    // MARK: - 视图
    var body: some View {
        HStack(spacing: 0) {
            // 左侧返回按钮
            if showBackButton {
                backButton
            }
            
            // 标题
            Text(title)
                .font(titleFont)
                .foregroundColor(titleColor)
                .lineLimit(1)
                .padding(.leading, showBackButton ? 5 : 16)
            
            Spacer()
            
            // 右侧按钮组
            HStack(spacing: 16) {
                ForEach(rightItems) { item in
                    rightButton(item)
                }
            }
            .padding(.trailing, 16)
        }
        .frame(height: 44)
        .padding(.top, 8)
        .background(backgroundColor)
    }
    
    // MARK: - 返回按钮
    private var backButton: some View {
        Button(action: {
            handleBackAction()
        }) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 17, weight: .semibold))
                
                if !backButtonTitle.isEmpty {
                    Text(backButtonTitle)
                        .font(.system(size: 17))
                }
            }
            .foregroundColor(backButtonColor)
            .padding(.leading, 16)
        }
    }
    
    // MARK: - 右侧按钮
    private func rightButton(_ item: NavigationBarItem) -> some View {
        Button(action: item.action) {
            if let systemImage = item.systemImage {
                Image(systemName: systemImage)
                    .font(.system(size: 17))
                    .foregroundColor(item.tintColor)
            } else if let image = item.image {
                Image(image)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 22, height: 22)
                    .foregroundColor(item.tintColor)
            }
            
            if let title = item.title {
                Text(title)
                    .font(.system(size: 17))
                    .foregroundColor(item.tintColor)
            }
        }
    }
    
    // MARK: - 私有方法
    private func handleBackAction() {
        if let customAction = onBackButtonTapped {
            customAction()
        } else {
            // 尝试多种返回方式
            NotificationCenter.default.post(name: .navigateBack, object: nil)
            DispatchQueue.main.async {
                presentationMode.wrappedValue.dismiss()
            }
        }
    }
}

// MARK: - 导航栏按钮项模型
struct NavigationBarItem: Identifiable {
    let id = UUID()
    var systemImage: String?
    var image: String?
    var title: String?
    var tintColor: Color = .white
    var action: () -> Void
}

// MARK: - 预览
struct CommonNavigationBar_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            // 基本使用
            CommonNavigationBar(title: "首页")
            
            // 自定义样式
            CommonNavigationBar(
                title: "设置",
                titleColor: .blue,
                backgroundColor: Color.gray.opacity(0.2),
                backButtonColor: .blue,
                rightItems: [
                    NavigationBarItem(
                        systemImage: "gear",
                        tintColor: .blue,
                        action: {}
                    )
                ]
            )
            
            // 多个右侧按钮
            CommonNavigationBar(
                title: "详情",
                rightItems: [
                    NavigationBarItem(
                        systemImage: "square.and.arrow.up",
                        action: {}
                    ),
                    NavigationBarItem(
                        systemImage: "ellipsis",
                        action: {}
                    )
                ]
            )
        }
    }
}

// MARK: - 通知名称扩展
extension Notification.Name {
    static let navigateBack = Notification.Name("NavigateBack")
} 