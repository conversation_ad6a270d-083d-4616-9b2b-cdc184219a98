import SwiftUI
#if canImport(UIKit)
import UIKit // 用于提供震动反馈
#endif

/// 可重用的日历选择器组件
struct CalendarPickerView: View {
    // MARK: - 属性
    @Binding var selectedDate: Date
    @Binding var isShowing: Bool
    @Binding var animate: Bool
    var onDateSelected: (Date) -> Void
    
    // 日历状态
    @State private var currentMonth: Date = Date()
    @State private var selectedDayInMonth: Date? = nil
    
    // 触觉反馈生成器
    #if canImport(UIKit)
    private let feedbackGenerator = UIImpactFeedbackGenerator(style: .light)
    #endif
    
    // 数据源 - 日期与数据关联
    var datesWithData: [Date] = []
    var maxSelectableDate: Date = Date() // 默认今天是最大可选日期
    var showDataIndicator: Bool = true // 是否显示数据指示点
    var cancelButtonText: String = "取消"
    var confirmButtonText: String = "确认"
    var todayButtonText: String = "今天"
    var dateFormat: String = "yyyy年MM月"
    var weekdaySymbols: [String] = ["日", "一", "二", "三", "四", "五", "六"]
    
    // 检查日期是否有数据
    private func hasData(for date: Date) -> Bool {
        let calendar = Calendar.current
        return datesWithData.contains { 
            calendar.isDate($0, inSameDayAs: date)
        }
    }
    
    // MARK: - 主视图
    var body: some View {
        VStack(spacing: 0) {
            // 顶部控制栏
            HStack {
                // 取消按钮
                Button(action: {
                    // 取消选择，关闭日历
                    isShowing = false
                }) {
                    Text(cancelButtonText)
                        .foregroundColor(.white)
                        .font(.system(size: 16))
                }
                
                Spacer()
                
                // 日历标题
                Text(monthYearString(from: currentMonth))
                    .foregroundColor(.white)
                    .font(.system(size: 16, weight: .semibold))
                
                Spacer()
                
                // 确认按钮
                Button(action: {
                    // 如果有选中的日期，则调用回调
                    if let selectedDay = selectedDayInMonth {
                        onDateSelected(selectedDay)
                    } else {
                        onDateSelected(selectedDate)
                    }
                }) {
                    Text(confirmButtonText)
                        .foregroundColor(.blue)
                        .font(.system(size: 16, weight: .semibold))
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 10)
            
            // 周天标题
            HStack(spacing: 0) {
                ForEach(weekdaySymbols, id: \.self) { weekday in
                    Text(weekday)
                        .font(.system(size: 11))
                        .foregroundColor(Color.gray.opacity(0.7))
                        .frame(maxWidth: .infinity)
                }
            }
            .padding(.horizontal, 12)
            .padding(.bottom, 12)
            .padding(.top, 4)
            
            // 日历网格
            VStack(spacing: 12) {
                // 月份选择器
                HStack {
                    Button(action: {
                        withAnimation {
                            currentMonth = Calendar.current.date(
                                byAdding: .month,
                                value: -1,
                                to: currentMonth
                            ) ?? currentMonth
                            #if canImport(UIKit)
                            feedbackGenerator.impactOccurred()
                            #endif
                        }
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.gray)
                            .frame(width: 30, height: 30)
                    }
                    
                    Spacer()
                    
                    Button(action: {
                        withAnimation {
                            currentMonth = Date() // 回到当前月
                            #if canImport(UIKit)
                            feedbackGenerator.impactOccurred()
                            #endif
                        }
                    }) {
                        Text(todayButtonText)
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color.blue)
                            .cornerRadius(14)
                    }
                    
                    Spacer()
                    
                    Button(action: {
                        withAnimation {
                            currentMonth = Calendar.current.date(
                                byAdding: .month,
                                value: 1,
                                to: currentMonth
                            ) ?? currentMonth
                            #if canImport(UIKit)
                            feedbackGenerator.impactOccurred()
                            #endif
                        }
                    }) {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.gray)
                            .frame(width: 30, height: 30)
                    }
                }
                .padding(.horizontal, 16)
                
                // 日期网格
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 10) {
                    ForEach(extractDates()) { dateItem in
                        // 日期单元格
                        Button(action: {
                            // 只能选择今天和过去的日期
                            if dateItem.date <= maxSelectableDate {
                                withAnimation {
                                    selectedDayInMonth = dateItem.date
                                    #if canImport(UIKit)
                                    feedbackGenerator.impactOccurred()
                                    #endif
                                }
                            } else {
                                // 提供触觉反馈，表示不能选择未来日期
                                #if canImport(UIKit)
                                let errorFeedback = UINotificationFeedbackGenerator()
                                errorFeedback.notificationOccurred(.error)
                                #endif
                            }
                        }) {
                            VStack(spacing: 5) {
                                // 日期
                                Text("\(dateItem.day)")
                                    .font(.system(size: 16))
                                    .foregroundColor(textColor(for: dateItem))
                                
                                // 底部指示点
                                // 只显示一个指示点：选中状态或数据状态
                                if isSelected(date: dateItem.date) {
                                    // 选中指示器 - 不显示额外点，因为有背景色
                                    Circle()
                                        .fill(Color.clear)
                                        .frame(width: 5, height: 5)
                                } else if showDataIndicator && dateItem.month == 0 && dateItem.date <= maxSelectableDate && hasData(for: dateItem.date) {
                                    // 数据存在指示器
                                    Circle()
                                        .fill(Color.blue)
                                        .frame(width: 5, height: 5)
                                        .padding(.top, 1)
                                } else {
                                    // 空占位符，保持布局一致
                                    Circle()
                                        .fill(Color.clear)
                                        .frame(width: 5, height: 5)
                                }
                            }
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(isSelected(date: dateItem.date) ? Color.blue : (isToday(date: dateItem.date) ? Color.blue.opacity(0.2) : Color.clear))
                            )
                            .opacity(dateItem.month == 0 ? 1 : 0.4) // 其他月份的日期显示半透明
                            .disabled(dateItem.date > maxSelectableDate || dateItem.month != 0) // 禁用未来日期和其他月份日期
                        }
                    }
                }
                .padding(.horizontal, 10)
            }
            .padding(.bottom, 20)
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(hex: "#1A1A1A"))
                .shadow(color: Color.black.opacity(0.25), radius: 16, x: 0, y: 5)
        )
        .scaleEffect(animate ? 1.0 : 0.9)
        .opacity(animate ? 1.0 : 0.5)
        .padding(.horizontal, 16)
        .onAppear {
            #if canImport(UIKit)
            feedbackGenerator.prepare()
            #endif
            if let selectedDay = selectedDayInMonth {
                currentMonth = selectedDay
            } else {
                currentMonth = selectedDate
                selectedDayInMonth = selectedDate
            }
        }
    }
    
    // MARK: - 辅助方法
    
    // 获取月份和年份字符串
    private func monthYearString(from date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = dateFormat
        return formatter.string(from: date)
    }
    
    // 日期项结构
    struct DateItem: Identifiable {
        let id: String
        let day: Int
        let date: Date
        let month: Int // 0表示当前月，-1表示上个月，1表示下个月
    }
    
    // 提取当前月的所有日期
    private func extractDates() -> [DateItem] {
        let calendar = Calendar.current
        
        // 获取当前月的第一天和天数
        let currentMonthFirstDay = calendar.date(from: calendar.dateComponents([.year, .month], from: currentMonth))!
        
        // 获取当前月第一天是星期几
        let firstDayWeekday = calendar.component(.weekday, from: currentMonthFirstDay)
        
        // 计算需要显示的前一个月的天数（为了填充第一行）
        let daysInPrevMonth = (firstDayWeekday - 1) % 7
        
        // 获取当前月天数
        let range = calendar.range(of: .day, in: .month, for: currentMonth)!
        let numOfDaysInCurrentMonth = range.count
        
        // 计算总共需要显示多少天（前一个月的天数 + 当前月的天数 + 足够填充6行的天数）
        let totalDays = 42 // 6行 x 7列
        
        // 创建日期项数组
        var dates = [DateItem]()
        
        // 添加前一个月的天
        if daysInPrevMonth > 0 {
            let prevMonth = calendar.date(byAdding: .month, value: -1, to: currentMonthFirstDay)!
            let prevMonthDays = calendar.range(of: .day, in: .month, for: prevMonth)!.count
            
            for day in (prevMonthDays - daysInPrevMonth + 1)...prevMonthDays {
                let date = calendar.date(byAdding: .day, value: day - prevMonthDays, to: currentMonthFirstDay)!
                dates.append(DateItem(id: UUID().uuidString, day: day, date: date, month: -1))
            }
        }
        
        // 添加当前月的天
        for day in 1...numOfDaysInCurrentMonth {
            let date = calendar.date(byAdding: .day, value: day - 1, to: currentMonthFirstDay)!
            dates.append(DateItem(id: UUID().uuidString, day: day, date: date, month: 0))
        }
        
        // 添加下一个月的天，直到填满日历
        let remainingDays = totalDays - dates.count
        if remainingDays > 0 {
            let nextMonthFirstDay = calendar.date(byAdding: .month, value: 1, to: currentMonthFirstDay)!
            
            for day in 1...remainingDays {
                let date = calendar.date(byAdding: .day, value: day - 1, to: nextMonthFirstDay)!
                dates.append(DateItem(id: UUID().uuidString, day: day, date: date, month: 1))
            }
        }
        
        return dates
    }
    
    // 判断日期是否为今天
    private func isToday(date: Date) -> Bool {
        Calendar.current.isDateInToday(date)
    }
    
    // 判断日期是否被选中
    private func isSelected(date: Date) -> Bool {
        if let selectedDate = selectedDayInMonth {
            return Calendar.current.isDate(date, inSameDayAs: selectedDate)
        }
        return false
    }
    
    // 确定文本颜色
    private func textColor(for dateItem: DateItem) -> Color {
        if dateItem.date > maxSelectableDate {
            // 未来日期显示为灰色
            return Color.gray.opacity(0.5)
        } else if isSelected(date: dateItem.date) {
            // 选中日期显示为白色
            return .white
        } else if isToday(date: dateItem.date) {
            // 今天显示为蓝色
            return .blue
        } else {
            // 其他日期显示为白色
            return .white
        }
    }
}

// MARK: - 预览
struct CalendarPickerView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)
            
            CalendarPickerView(
                selectedDate: .constant(Date()),
                isShowing: .constant(true),
                animate: .constant(true),
                onDateSelected: { date in
                    print("选择了日期：\(date)")
                },
                datesWithData: [Date()] // 当前日期有数据
            )
        }
    }
} 