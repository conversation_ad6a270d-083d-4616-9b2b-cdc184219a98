import SwiftUI

/// 可重用的日期选择器组件
struct DateSelectionView: View {
    @Binding var selectedDate: Date
    var onDateSelected: ((Int, Date) -> Void)?
    private let calendar = Calendar.current
    // 当前可选日期范围：前后各 10 天，共 21 天
    @State private var draggingDate: Date?

    private var dateRange: [Date] {
        let calendar = Calendar.current
        let start = calendar.date(byAdding: .day, value: -10, to: selectedDate)!
        let end = calendar.date(byAdding: .day, value: 10, to: selectedDate)!
        
        var dates: [Date] = []
        var current = start
        while current <= end {
            dates.append(current)
            current = calendar.date(byAdding: .day, value: 1, to: current)!
        }
        return dates
    }
    @State private var shakeAmount: CGFloat = 0

    private func calculateOpacity(for date: Date) -> Double {
        let distance = abs(Calendar.current.dateComponents([.day], from: date, to: selectedDate).day ?? 0)
        switch distance {
        case 0: return 1.0
        case 1: return 0.8
        case 2: return 0.6
        default: return 0.4
        }
    }

    private func calculateWeekdayBrightness(for date: Date) -> Double {
        if date > Date() { return 0.4 }
        let distance = abs(Calendar.current.dateComponents([.day], from: date, to: selectedDate).day ?? 0)
        switch distance {
        case 0: return 1.0
        case 1: return 0.9
        case 2: return 0.7
        default: return 0.5
        }
    }

    private func calculateScale(for date: Date) -> CGFloat {
        let distance = abs(Calendar.current.dateComponents([.day], from: date, to: selectedDate).day ?? 0)
        switch distance {
        case 0...1: return 1.0
        case 2: return 0.9
        default: return 0.8
        }
    }

    private func triggerShakeAnimation() {
        let impactMed = UIImpactFeedbackGenerator(style: .rigid)
        impactMed.impactOccurred()

        withAnimation(.default.repeatCount(10, autoreverses: true).speed(10)) {
            shakeAmount = 2
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation {
                shakeAmount = 0
            }
        }
    }

    private func localizedWeekdaySymbol(for weekday: Int) -> String {
        let key: String
        switch weekday {
        case 1: key = "sleep_history_weekday_sun"
        case 2: key = "sleep_history_weekday_mon"
        case 3: key = "sleep_history_weekday_tue"
        case 4: key = "sleep_history_weekday_wed"
        case 5: key = "sleep_history_weekday_thu"
        case 6: key = "sleep_history_weekday_fri"
        case 7: key = "sleep_history_weekday_sat"
        default: key = ""
        }
        return NSLocalizedString(key, comment: "")
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .center) {
                // 高亮胶囊背景
                Capsule()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [Color.blue.opacity(0.6), Color.blue]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(width: 85, height: 40)
                    .offset(x: shakeAmount)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)

                ScrollViewReader { proxy in
                    let itemWidth: CGFloat = 40
                    let spacing: CGFloat = 12

                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: spacing) {
                            Spacer().frame(width: geometry.size.width / 2 - itemWidth / 2)
                            ForEach(Array(dateRange.enumerated()), id: \.offset) { index, date in
                                DateItemView(
                                    date: date,
                                    selectedDate: selectedDate,
                                    calendar: calendar
                                ) {
                                    withAnimation(.easeOut(duration: 0.25)) {
                                        selectedDate = date
                                        proxy.scrollTo(date, anchor: .center)
                                        onDateSelected?(date.daysAgoFromToday(), date)
                                    }
                                    UIImpactFeedbackGenerator(style: .light).impactOccurred()
                                }
                                .id(date)
                            }
//                            ForEach(dateRange, id: \.self) { date in
//                                DateItemView(
//                                    date: date,
//                                    selectedDate: selectedDate,
//                                    calendar: calendar
//                                ) {
//                                    withAnimation(.easeOut(duration: 0.25)) {
//                                        selectedDate = date
//                                        proxy.scrollTo(date, anchor: .center)
//                                        
//                                        onDateSelected?(date.day, date)
//                                    }
//                                    UIImpactFeedbackGenerator(style: .light).impactOccurred()
//                                }
//                            }

                            Spacer().frame(width: geometry.size.width / 2 - itemWidth / 2)
                        }
                    }
                    .scrollDisabled(true)
                    .onAppear {
                        if let idx = dateRange.firstIndex(where: { Calendar.current.isDate($0, inSameDayAs: selectedDate) }) {
                            proxy.scrollTo(idx, anchor: .center)
                        }
                    }
                    .onChange(of: selectedDate) { newDate in
                        if let idx = dateRange.firstIndex(where: { Calendar.current.isDate($0, inSameDayAs: newDate) }) {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                proxy.scrollTo(idx, anchor: .center)
                            }
                        }
                    }
                    .gesture(
                        DragGesture()
                        .onEnded { value in
                            let velocity = value.predictedEndLocation.x - value.location.x
                            let direction = velocity < 0 ? 1 : -1
                            let newDate = calendar.date(byAdding: .day, value: direction, to: selectedDate)!
                            if newDate <= Date() {
                                withAnimation {
                                    selectedDate = newDate
                                    proxy.scrollTo(newDate, anchor: .center)
                                    onDateSelected?(newDate.daysAgoFromToday(), newDate)
                                }
                            }
                        }
                    )
                }
            }
        }
        .frame(height: 60)
        .padding(.top, -4)
    }

    private func triggerShake() {
        UIImpactFeedbackGenerator(style: .rigid).impactOccurred()
        withAnimation(.default.repeatCount(3, autoreverses: true).speed(8)) {
            shakeAmount = 3
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            withAnimation { shakeAmount = 0 }
        }
    }
    
    func generateDateRange(from startDate: Date, to endDate: Date) -> [Date] {
        var result: [Date] = []
        var date = startDate
        while date <= endDate {
            result.append(date)
            date = Calendar.current.date(byAdding: .day, value: 1, to: date)!
        }
        return result
    }
}

struct DateItemView: View {
    let date: Date
    let selectedDate: Date
    let calendar: Calendar
    let onSelect: () -> Void
    private func localizedWeekdaySymbol(for weekday: Int) -> String {
        let key: String
        switch weekday {
        case 1: key = "sleep_history_weekday_sun"
        case 2: key = "sleep_history_weekday_mon"
        case 3: key = "sleep_history_weekday_tue"
        case 4: key = "sleep_history_weekday_wed"
        case 5: key = "sleep_history_weekday_thu"
        case 6: key = "sleep_history_weekday_fri"
        case 7: key = "sleep_history_weekday_sat"
        default: key = ""
        }
        return NSLocalizedString(key, comment: "")
    }
    var body: some View {
        let isToday = calendar.isDateInToday(date)
        let isSelected = calendar.isDate(date, inSameDayAs: selectedDate)
        let isFuture = date > Date()
        let weekday = calendar.component(.weekday, from: date)
        
        let weekdaySymbol = calendar.shortWeekdaySymbols[(weekday + 5) % 7]
        
        VStack(spacing: 4) {
            Text(localizedWeekdaySymbol(for: weekday))
                .font(.system(size: 11))
                .foregroundColor(.white.opacity(isFuture ? 0.4 : 1.0))

            Text(isToday ? "date_selection_today".localized : "\(calendar.component(.day, from: date))")
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(isFuture ? Color.gray.opacity(0.4) : .white)
        }
        .frame(width: 85)
        .scaleEffect(isSelected ? 1.1 : 0.95)
        .opacity(isSelected ? 1.0 : 0.5)
        .contentShape(Rectangle())
        .onTapGesture {
            if !isFuture {
                onSelect()
            }
        }
    }
}
// MARK: - 预览
//struct DateSelectionView_Previews: PreviewProvider {
//    static var previews: some View {
//        ZStack {
//            Color.black.edgesIgnoringSafeArea(.all)
//            
//            DateSelectionView(
//                selectedDay: .constant(0),
//                selectedDate: .constant(Date()),
//                onDateSelected: { day, date in
//                    print("选择了日期：\(day)，\(date)")
//                }
//            )
//            .padding()
//        }
//    }
//}
