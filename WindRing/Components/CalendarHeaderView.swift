import SwiftUI

/// 可重用的日历头部组件
struct CalendarHeaderView: View {
    // MARK: - 属性
    @Binding var selectedMonth: Int
    @Binding var selectedYear: Int
    @State private var showMonthPicker: Bool = false
    var onMonthYearChanged: ((Int, Int) -> Void)? // 月份年份变更回调
    
    // MARK: - 主视图
    var body: some View {
        VStack(spacing: 0) {
            // 月份标题和选择器
            Button(action: {
                withAnimation {
                    showMonthPicker.toggle()
                }
            }) {
                HStack(spacing: 8) {
                    Image("日期 (2)")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 16, height: 16)
                    
                    Text("\(getMonthName(selectedMonth)) \(selectedYear)")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Image(systemName: "chevron.down")
                        .foregroundColor(.white.opacity(0.7))
                        .font(.system(size: 10))
                        .rotationEffect(showMonthPicker ? .degrees(180) : .degrees(0))
                }
                .padding(.vertical, 8)
            }
            
            // 显示月份选择器
            if showMonthPicker {
                monthPickerView
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
    }
    
    // 月份选择器视图
    private var monthPickerView: some View {
        VStack {
            // 年份选择器
            HStack {
                Button(action: {
                    selectedYear -= 1
                    onMonthYearChanged?(selectedMonth, selectedYear)
                }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                Text("\(selectedYear)")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    selectedYear += 1
                    onMonthYearChanged?(selectedMonth, selectedYear)
                }) {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal)
            .padding(.top, 8)
            
            // 月份网格
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 10) {
                ForEach(1...12, id: \.self) { month in
                    Button(action: {
                        selectedMonth = month
                        withAnimation {
                            showMonthPicker = false
                        }
                        onMonthYearChanged?(month, selectedYear)
                    }) {
                        Text(getMonthName(month))
                            .font(.system(size: 14))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                            .background(month == selectedMonth ? Color.blue.opacity(0.3) : Color.clear)
                            .cornerRadius(8)
                            .foregroundColor(month == selectedMonth ? .white : .white.opacity(0.8))
                    }
                }
            }
            .padding()
        }
        .background(Color.black.opacity(0.7))
        .cornerRadius(12)
        .padding(.horizontal, 4)
        .padding(.bottom, 4)
    }
    
    // 获取月份名称
    private func getMonthName(_ month: Int) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MMM"
        
        let components = DateComponents(year: 2021, month: month, day: 1)
        if let date = Calendar.current.date(from: components) {
            return dateFormatter.string(from: date)
        }
        return "Unknown"
    }
}

// MARK: - 预览
struct CalendarHeaderView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)
            
            CalendarHeaderView(
                selectedMonth: .constant(Calendar.current.component(.month, from: Date())),
                selectedYear: .constant(Calendar.current.component(.year, from: Date())),
                onMonthYearChanged: { month, year in
                    print("选择了月份：\(month)，年份：\(year)")
                }
            )
            .padding()
        }
    }
} 