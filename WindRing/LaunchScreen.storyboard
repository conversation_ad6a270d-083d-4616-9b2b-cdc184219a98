<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Launch_bg" translatesAutoresizingMaskIntoConstraints="NO" id="2S1-Z3-ma8">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo" translatesAutoresizingMaskIntoConstraints="NO" id="MhP-hX-7oL">
                                <rect key="frame" x="158.66666666666666" y="268" width="76" height="76"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="76" id="7Ga-I3-vyY"/>
                                    <constraint firstAttribute="width" constant="76" id="lLK-w2-vhn"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="SiRings" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="L0u-kk-DxT">
                                <rect key="frame" x="168" y="354" width="57" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Launch_bottom" translatesAutoresizingMaskIntoConstraints="NO" id="8Z3-Ne-qde">
                                <rect key="frame" x="95.333333333333329" y="566.66666666666663" width="202.66666666666669" height="18.666666666666629"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="8Z3-Ne-qde" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="4nL-N1-VIK"/>
                            <constraint firstAttribute="trailing" secondItem="2S1-Z3-ma8" secondAttribute="trailing" id="K2O-Fw-SEs"/>
                            <constraint firstItem="MhP-hX-7oL" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" constant="-120" id="Oen-qu-LGb"/>
                            <constraint firstItem="L0u-kk-DxT" firstAttribute="centerX" secondItem="MhP-hX-7oL" secondAttribute="centerX" id="WWr-yo-jKf"/>
                            <constraint firstAttribute="bottom" secondItem="2S1-Z3-ma8" secondAttribute="bottom" id="Xny-It-gr9"/>
                            <constraint firstItem="L0u-kk-DxT" firstAttribute="top" secondItem="MhP-hX-7oL" secondAttribute="bottom" constant="10" id="Zuy-MA-mGx"/>
                            <constraint firstItem="2S1-Z3-ma8" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="abh-vp-YEz"/>
                            <constraint firstItem="MhP-hX-7oL" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="afa-a9-MwL"/>
                            <constraint firstItem="8Z3-Ne-qde" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" constant="150" id="edY-H1-cmL"/>
                            <constraint firstItem="2S1-Z3-ma8" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="pEo-iG-nyf"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="53" y="375"/>
        </scene>
    </scenes>
    <resources>
        <image name="Launch_bg" width="375" height="812"/>
        <image name="Launch_bottom" width="202.66667175292969" height="18.666666030883789"/>
        <image name="logo" width="76" height="76"/>
    </resources>
</document>
