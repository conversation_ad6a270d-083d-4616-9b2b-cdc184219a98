// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 6.0.2 effective-5.10 (swiftlang-6.0.2.1.2 clang-1600.0.26.4)
// swift-module-flags: -target arm64-apple-ios9.0 -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -enable-bare-slash-regex -module-name CRPSmartRing
// swift-module-flags-ignorable: -no-verify-emitted-module-interface
@_exported import CRPSmartRing
import CoreBluetooth
import Dispatch
import Foundation
import MobileCoreServices
import Swift
import SystemConfiguration
import UIKit
import _Concurrency
import _StringProcessing
import _SwiftConcurrencyShims
import CRPSmartRing.zipArchive_Public
public enum CRPHTTPMethod : Swift.String {
  case options
  case get
  case head
  case post
  case put
  case patch
  case delete
  case trace
  case connect
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public typealias CRPParameters = [Swift.String : Any]
public protocol CRPParameterEncoding {
  func encode(_ urlRequest: any CRPSmartRing.CRPURLRequestConvertible, with parameters: CRPSmartRing.CRPParameters?) throws -> Foundation.URLRequest
}
public struct CRPURLEncoding : CRPSmartRing.CRPParameterEncoding {
  public enum Destination {
    case methodDependent, queryString, httpBody
    public static func == (a: CRPSmartRing.CRPURLEncoding.Destination, b: CRPSmartRing.CRPURLEncoding.Destination) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  public enum ArrayEncoding {
    case brackets, noBrackets
    public static func == (a: CRPSmartRing.CRPURLEncoding.ArrayEncoding, b: CRPSmartRing.CRPURLEncoding.ArrayEncoding) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  public enum BoolEncoding {
    case numeric, literal
    public static func == (a: CRPSmartRing.CRPURLEncoding.BoolEncoding, b: CRPSmartRing.CRPURLEncoding.BoolEncoding) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  public static var `default`: CRPSmartRing.CRPURLEncoding {
    get
  }
  public static var methodDependent: CRPSmartRing.CRPURLEncoding {
    get
  }
  public static var queryString: CRPSmartRing.CRPURLEncoding {
    get
  }
  public static var httpBody: CRPSmartRing.CRPURLEncoding {
    get
  }
  public let destination: CRPSmartRing.CRPURLEncoding.Destination
  public let arrayEncoding: CRPSmartRing.CRPURLEncoding.ArrayEncoding
  public let boolEncoding: CRPSmartRing.CRPURLEncoding.BoolEncoding
  public init(destination: CRPSmartRing.CRPURLEncoding.Destination = .methodDependent, arrayEncoding: CRPSmartRing.CRPURLEncoding.ArrayEncoding = .brackets, boolEncoding: CRPSmartRing.CRPURLEncoding.BoolEncoding = .numeric)
  public func encode(_ urlRequest: any CRPSmartRing.CRPURLRequestConvertible, with parameters: CRPSmartRing.CRPParameters?) throws -> Foundation.URLRequest
  public func queryComponents(fromKey key: Swift.String, value: Any) -> [(Swift.String, Swift.String)]
  public func escape(_ string: Swift.String) -> Swift.String
}
public struct CRPJSONEncoding : CRPSmartRing.CRPParameterEncoding {
  public static var `default`: CRPSmartRing.CRPJSONEncoding {
    get
  }
  public static var prettyPrinted: CRPSmartRing.CRPJSONEncoding {
    get
  }
  public let options: Foundation.JSONSerialization.WritingOptions
  public init(options: Foundation.JSONSerialization.WritingOptions = [])
  public func encode(_ urlRequest: any CRPSmartRing.CRPURLRequestConvertible, with parameters: CRPSmartRing.CRPParameters?) throws -> Foundation.URLRequest
  public func encode(_ urlRequest: any CRPSmartRing.CRPURLRequestConvertible, withJSONObject jsonObject: Any? = nil) throws -> Foundation.URLRequest
}
public struct CRPPropertyListEncoding : CRPSmartRing.CRPParameterEncoding {
  public static var `default`: CRPSmartRing.CRPPropertyListEncoding {
    get
  }
  public static var xml: CRPSmartRing.CRPPropertyListEncoding {
    get
  }
  public static var binary: CRPSmartRing.CRPPropertyListEncoding {
    get
  }
  public let format: Foundation.PropertyListSerialization.PropertyListFormat
  public let options: Foundation.PropertyListSerialization.WriteOptions
  public init(format: Foundation.PropertyListSerialization.PropertyListFormat = .xml, options: Foundation.PropertyListSerialization.WriteOptions = 0)
  public func encode(_ urlRequest: any CRPSmartRing.CRPURLRequestConvertible, with parameters: CRPSmartRing.CRPParameters?) throws -> Foundation.URLRequest
}
public protocol CRPRequestAdapter {
  func adapt(_ urlRequest: Foundation.URLRequest) throws -> Foundation.URLRequest
}
public typealias CRPRequestRetryCompletion = (_ shouldRetry: Swift.Bool, _ timeDelay: Foundation.TimeInterval) -> Swift.Void
public protocol CRPRequestRetrier {
  func should(_ manager: CRPSmartRing.CRPSessionManager, retry request: CRPSmartRing.CRPRequest, with error: any Swift.Error, completion: @escaping CRPSmartRing.CRPRequestRetryCompletion)
}
public typealias HTTPHeaders = [Swift.String : Swift.String]
@_hasMissingDesignatedInitializers open class CRPRequest {
  public typealias ProgressHandler = (Foundation.Progress) -> Swift.Void
  open var delegate: CRPSmartRing.CRPTaskDelegate {
    get
  }
  open var task: Foundation.URLSessionTask? {
    get
  }
  final public let session: Foundation.URLSession
  open var request: Foundation.URLRequest? {
    get
  }
  open var response: Foundation.HTTPURLResponse? {
    get
  }
  open var retryCount: Swift.UInt {
    get
  }
  @discardableResult
  open func authenticate(user: Swift.String, password: Swift.String, persistence: Foundation.URLCredential.Persistence = .forSession) -> Self
  @discardableResult
  open func authenticate(usingCredential credential: Foundation.URLCredential) -> Self
  open class func authorizationHeader(user: Swift.String, password: Swift.String) -> (key: Swift.String, value: Swift.String)?
  open func resume()
  open func suspend()
  open func cancel()
  @objc deinit
}
extension CRPSmartRing.CRPRequest : Swift.CustomStringConvertible {
  open var description: Swift.String {
    get
  }
}
extension CRPSmartRing.CRPRequest : Swift.CustomDebugStringConvertible {
  open var debugDescription: Swift.String {
    get
  }
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers open class CRPDataRequest : CRPSmartRing.CRPRequest {
  override open var request: Foundation.URLRequest? {
    get
  }
  open var progress: Foundation.Progress {
    get
  }
  @discardableResult
  open func stream(closure: ((Foundation.Data) -> Swift.Void)? = nil) -> Self
  @discardableResult
  open func downloadProgress(queue: Dispatch.DispatchQueue = DispatchQueue.main, closure: @escaping CRPSmartRing.CRPDataRequest.ProgressHandler) -> Self
  @objc deinit
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers open class CRPDownloadRequest : CRPSmartRing.CRPRequest {
  public struct DownloadOptions : Swift.OptionSet {
    public let rawValue: Swift.UInt
    public static let createIntermediateDirectories: CRPSmartRing.CRPDownloadRequest.DownloadOptions
    public static let removePreviousFile: CRPSmartRing.CRPDownloadRequest.DownloadOptions
    public init(rawValue: Swift.UInt)
    public typealias ArrayLiteralElement = CRPSmartRing.CRPDownloadRequest.DownloadOptions
    public typealias Element = CRPSmartRing.CRPDownloadRequest.DownloadOptions
    public typealias RawValue = Swift.UInt
  }
  public typealias DownloadFileDestination = (_ temporaryURL: Foundation.URL, _ response: Foundation.HTTPURLResponse) -> (destinationURL: Foundation.URL, options: CRPSmartRing.CRPDownloadRequest.DownloadOptions)
  override open var request: Foundation.URLRequest? {
    get
  }
  open var resumeData: Foundation.Data? {
    get
  }
  open var progress: Foundation.Progress {
    get
  }
  override open func cancel()
  @discardableResult
  open func downloadProgress(queue: Dispatch.DispatchQueue = DispatchQueue.main, closure: @escaping CRPSmartRing.CRPDownloadRequest.ProgressHandler) -> Self
  open class func suggestedDownloadDestination(for directory: Foundation.FileManager.SearchPathDirectory = .documentDirectory, in domain: Foundation.FileManager.SearchPathDomainMask = .userDomainMask) -> CRPSmartRing.CRPDownloadRequest.DownloadFileDestination
  @objc deinit
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers open class CRPUploadRequest : CRPSmartRing.CRPDataRequest {
  override open var request: Foundation.URLRequest? {
    get
  }
  open var uploadProgress: Foundation.Progress {
    get
  }
  @discardableResult
  open func uploadProgress(queue: Dispatch.DispatchQueue = DispatchQueue.main, closure: @escaping CRPSmartRing.CRPUploadRequest.ProgressHandler) -> Self
  @objc deinit
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @available(iOS 9.0, macOS 10.11, tvOS 9.0, *)
open class CRPStreamRequest : CRPSmartRing.CRPRequest {
  @objc deinit
}
public protocol CRPDataResponseSerializerProtocol {
  associatedtype SerializedObject
  var serializeResponse: (Foundation.URLRequest?, Foundation.HTTPURLResponse?, Foundation.Data?, (any Swift.Error)?) -> CRPSmartRing.CRPResult<Self.SerializedObject> { get }
}
public struct CRPDataResponseSerializer<Value> : CRPSmartRing.CRPDataResponseSerializerProtocol {
  public typealias SerializedObject = Value
  public var serializeResponse: (Foundation.URLRequest?, Foundation.HTTPURLResponse?, Foundation.Data?, (any Swift.Error)?) -> CRPSmartRing.CRPResult<Value>
  public init(serializeResponse: @escaping (Foundation.URLRequest?, Foundation.HTTPURLResponse?, Foundation.Data?, (any Swift.Error)?) -> CRPSmartRing.CRPResult<Value>)
}
public protocol CRPDownloadResponseSerializerProtocol {
  associatedtype SerializedObject
  var serializeResponse: (Foundation.URLRequest?, Foundation.HTTPURLResponse?, Foundation.URL?, (any Swift.Error)?) -> CRPSmartRing.CRPResult<Self.SerializedObject> { get }
}
public struct CRPDownloadResponseSerializer<Value> : CRPSmartRing.CRPDownloadResponseSerializerProtocol {
  public typealias SerializedObject = Value
  public var serializeResponse: (Foundation.URLRequest?, Foundation.HTTPURLResponse?, Foundation.URL?, (any Swift.Error)?) -> CRPSmartRing.CRPResult<Value>
  public init(serializeResponse: @escaping (Foundation.URLRequest?, Foundation.HTTPURLResponse?, Foundation.URL?, (any Swift.Error)?) -> CRPSmartRing.CRPResult<Value>)
}
extension CRPSmartRing.CRPDataRequest {
  @discardableResult
  public func response(queue: Dispatch.DispatchQueue? = nil, completionHandler: @escaping (CRPSmartRing.CRPDefaultDataResponse) -> Swift.Void) -> Self
  @discardableResult
  public func response<T>(queue: Dispatch.DispatchQueue? = nil, responseSerializer: T, completionHandler: @escaping (CRPSmartRing.CRPDataResponse<T.SerializedObject>) -> Swift.Void) -> Self where T : CRPSmartRing.CRPDataResponseSerializerProtocol
}
extension CRPSmartRing.CRPDownloadRequest {
  @discardableResult
  public func response(queue: Dispatch.DispatchQueue? = nil, completionHandler: @escaping (CRPSmartRing.CRPDefaultDownloadResponse) -> Swift.Void) -> Self
  @discardableResult
  public func response<T>(queue: Dispatch.DispatchQueue? = nil, responseSerializer: T, completionHandler: @escaping (CRPSmartRing.CRPDownloadResponse<T.SerializedObject>) -> Swift.Void) -> Self where T : CRPSmartRing.CRPDownloadResponseSerializerProtocol
}
extension CRPSmartRing.CRPRequest {
  public static func serializeResponseData(response: Foundation.HTTPURLResponse?, data: Foundation.Data?, error: (any Swift.Error)?) -> CRPSmartRing.CRPResult<Foundation.Data>
}
extension CRPSmartRing.CRPDataRequest {
  public static func dataResponseSerializer() -> CRPSmartRing.CRPDataResponseSerializer<Foundation.Data>
  @discardableResult
  public func responseData(queue: Dispatch.DispatchQueue? = nil, completionHandler: @escaping (CRPSmartRing.CRPDataResponse<Foundation.Data>) -> Swift.Void) -> Self
}
extension CRPSmartRing.CRPDownloadRequest {
  public static func dataResponseSerializer() -> CRPSmartRing.CRPDownloadResponseSerializer<Foundation.Data>
  @discardableResult
  public func responseData(queue: Dispatch.DispatchQueue? = nil, completionHandler: @escaping (CRPSmartRing.CRPDownloadResponse<Foundation.Data>) -> Swift.Void) -> Self
}
extension CRPSmartRing.CRPRequest {
  public static func serializeResponseString(encoding: Swift.String.Encoding?, response: Foundation.HTTPURLResponse?, data: Foundation.Data?, error: (any Swift.Error)?) -> CRPSmartRing.CRPResult<Swift.String>
}
extension CRPSmartRing.CRPDataRequest {
  public static func stringResponseSerializer(encoding: Swift.String.Encoding? = nil) -> CRPSmartRing.CRPDataResponseSerializer<Swift.String>
  @discardableResult
  public func responseString(queue: Dispatch.DispatchQueue? = nil, encoding: Swift.String.Encoding? = nil, completionHandler: @escaping (CRPSmartRing.CRPDataResponse<Swift.String>) -> Swift.Void) -> Self
}
extension CRPSmartRing.CRPDownloadRequest {
  public static func stringResponseSerializer(encoding: Swift.String.Encoding? = nil) -> CRPSmartRing.CRPDownloadResponseSerializer<Swift.String>
  @discardableResult
  public func responseString(queue: Dispatch.DispatchQueue? = nil, encoding: Swift.String.Encoding? = nil, completionHandler: @escaping (CRPSmartRing.CRPDownloadResponse<Swift.String>) -> Swift.Void) -> Self
}
extension CRPSmartRing.CRPRequest {
  public static func serializeResponseJSON(options: Foundation.JSONSerialization.ReadingOptions, response: Foundation.HTTPURLResponse?, data: Foundation.Data?, error: (any Swift.Error)?) -> CRPSmartRing.CRPResult<Any>
}
extension CRPSmartRing.CRPDataRequest {
  public static func jsonResponseSerializer(options: Foundation.JSONSerialization.ReadingOptions = .allowFragments) -> CRPSmartRing.CRPDataResponseSerializer<Any>
  @discardableResult
  public func responseJSON(queue: Dispatch.DispatchQueue? = nil, options: Foundation.JSONSerialization.ReadingOptions = .allowFragments, completionHandler: @escaping (CRPSmartRing.CRPDataResponse<Any>) -> Swift.Void) -> Self
}
extension CRPSmartRing.CRPDownloadRequest {
  public static func jsonResponseSerializer(options: Foundation.JSONSerialization.ReadingOptions = .allowFragments) -> CRPSmartRing.CRPDownloadResponseSerializer<Any>
  @discardableResult
  public func responseJSON(queue: Dispatch.DispatchQueue? = nil, options: Foundation.JSONSerialization.ReadingOptions = .allowFragments, completionHandler: @escaping (CRPSmartRing.CRPDownloadResponse<Any>) -> Swift.Void) -> Self
}
extension CRPSmartRing.CRPRequest {
  public static func serializeResponsePropertyList(options: Foundation.PropertyListSerialization.ReadOptions, response: Foundation.HTTPURLResponse?, data: Foundation.Data?, error: (any Swift.Error)?) -> CRPSmartRing.CRPResult<Any>
}
extension CRPSmartRing.CRPDataRequest {
  public static func propertyListResponseSerializer(options: Foundation.PropertyListSerialization.ReadOptions = []) -> CRPSmartRing.CRPDataResponseSerializer<Any>
  @discardableResult
  public func responsePropertyList(queue: Dispatch.DispatchQueue? = nil, options: Foundation.PropertyListSerialization.ReadOptions = [], completionHandler: @escaping (CRPSmartRing.CRPDataResponse<Any>) -> Swift.Void) -> Self
}
extension CRPSmartRing.CRPDownloadRequest {
  public static func propertyListResponseSerializer(options: Foundation.PropertyListSerialization.ReadOptions = []) -> CRPSmartRing.CRPDownloadResponseSerializer<Any>
  @discardableResult
  public func responsePropertyList(queue: Dispatch.DispatchQueue? = nil, options: Foundation.PropertyListSerialization.ReadOptions = [], completionHandler: @escaping (CRPSmartRing.CRPDownloadResponse<Any>) -> Swift.Void) -> Self
}
@objc public protocol CRPManagerDelegate {
  @objc func didState(_ state: CRPSmartRing.CRPState)
  @objc func didBluetoothState(_ state: CRPSmartRing.CRPBluetoothState)
  @objc func receiveSteps(_ model: CRPSmartRing.CRPStepModel)
  @objc func receiveHeartRate(_ heartRate: Swift.Int)
  @objc func receiveRealTimeHeartRate(_ heartRate: Swift.Int)
  @objc func receiveHRV(_ hrv: Swift.Int)
  @objc func receiveSpO2(_ o2: Swift.Int)
  @objc func receiveOTA(_ state: CRPSmartRing.CRPOTAState, _ progress: Swift.Int)
  @objc func receiveStress(_ stress: Swift.Int)
  @objc optional func receiveTrainingList(_ list: [CRPSmartRing.CRPTrainingRecord])
  @objc optional func receiveTrainingState(_ state: CRPSmartRing.CRPTrainingType)
  @objc optional func receiveWearState(_ state: Swift.Int)
  @objc optional func receiveDailyGoal(_ type: CRPSmartRing.CRPTrainingGoalType, state: Swift.Int)
  @objc optional func receiveActivityReminder(isReminder: Swift.Int, wearState: Swift.Int, time: Swift.Int, step: Swift.Int)
  @objc optional func receiveTrainingGoal(_ type: CRPSmartRing.CRPTrainingGoalType, state: Swift.Int)
  @objc optional func receiveTouchType(_ model: CRPSmartRing.CRPTouchModel)
  @objc optional func receivePairState(_ state: CRPSmartRing.CRPPairState)
  @objc optional func receiveSOS()
  @objc optional func receiveMeditationList(_ list: [CRPSmartRing.CRPMeditationRecord])
  @objc optional func receiveMeditationState(_ state: CRPSmartRing.CRPMeditationGetState)
  @objc optional func receiveSleepList(_ list: [CRPSmartRing.CRPGoMoreSleepRecord])
  @objc optional func receiveTrainingRecognition(_ trainingInfo: CRPSmartRing.CRPGoMoreTrainingModel)
  @objc optional func receiveTrainingRecognitionList(_ list: [CRPSmartRing.CRPGoMoreTrainingRecord])
  @objc optional func receiveBloodPressure(_ sbp: Swift.Int, _ dbp: Swift.Int)
  @objc optional func receiveTemperature(_ value: Swift.Double)
  @objc optional func recevieTakePhoto()
  @objc optional func receiveKnockSwitch(_ model: CRPSmartRing.CRPTouchModel)
  @objc optional func goodixDidState(_ state: CRPSmartRing.CRPState)
  @objc optional func goodixRestoreState(_ state: CRPSmartRing.CRPRestoreState)
}
public enum CRPResult<Value> {
  case success(Value)
  case failure(any Swift.Error)
  public var isSuccess: Swift.Bool {
    get
  }
  public var isFailure: Swift.Bool {
    get
  }
  public var value: Value? {
    get
  }
  public var error: (any Swift.Error)? {
    get
  }
}
extension CRPSmartRing.CRPResult : Swift.CustomStringConvertible {
  public var description: Swift.String {
    get
  }
}
extension CRPSmartRing.CRPResult : Swift.CustomDebugStringConvertible {
  public var debugDescription: Swift.String {
    get
  }
}
extension CRPSmartRing.CRPResult {
  public init(value: () throws -> Value)
  public func unwrap() throws -> Value
  public func map<T>(_ transform: (Value) -> T) -> CRPSmartRing.CRPResult<T>
  public func flatMap<T>(_ transform: (Value) throws -> T) -> CRPSmartRing.CRPResult<T>
  public func mapError<T>(_ transform: (any Swift.Error) -> T) -> CRPSmartRing.CRPResult<Value> where T : Swift.Error
  public func flatMapError<T>(_ transform: (any Swift.Error) throws -> T) -> CRPSmartRing.CRPResult<Value> where T : Swift.Error
  @discardableResult
  public func withValue(_ closure: (Value) -> Swift.Void) -> CRPSmartRing.CRPResult<Value>
  @discardableResult
  public func withError(_ closure: (any Swift.Error) -> Swift.Void) -> CRPSmartRing.CRPResult<Value>
  @discardableResult
  public func ifSuccess(_ closure: () -> Swift.Void) -> CRPSmartRing.CRPResult<Value>
  @discardableResult
  public func ifFailure(_ closure: () -> Swift.Void) -> CRPSmartRing.CRPResult<Value>
}
public func == (lhs: CRPSmartRing.CRPDiscovery, rhs: CRPSmartRing.CRPDiscovery) -> Swift.Bool
@objc @objcMembers public class CRPDiscovery : ObjectiveC.NSObject {
  @objc public var localName: Swift.String? {
    @objc get
  }
  @objc public var kCABAdvidataLocalName: Swift.String?
  @objc final public let advertisementData: [Swift.String : Swift.AnyObject]
  @objc final public let remotePeripheral: CoreBluetooth.CBPeripheral
  @objc final public let RSSI: Swift.Int
  @objc public var mac: Swift.String?
  @objc public var ver: Swift.String
  @objc public var platform: Swift.Int
  @objc public var isPair: Swift.Bool
  @objc public var isTalk: Swift.Bool
  @objc public var info: CRPSmartRing.CRPRingInfoModel?
  public var hide: Swift.Int?
  @objc public init(advertisementData: [Swift.String : Swift.AnyObject], remotePeripheral: CoreBluetooth.CBPeripheral, RSSI: Swift.Int)
  @objc public init(remotePeripheral: CoreBluetooth.CBPeripheral, mac: Swift.String)
  @objc deinit
}
open class CRPServerTrustPolicyManager {
  final public let policies: [Swift.String : CRPSmartRing.CRPServerTrustPolicy]
  public init(policies: [Swift.String : CRPSmartRing.CRPServerTrustPolicy])
  open func serverTrustPolicy(forHost host: Swift.String) -> CRPSmartRing.CRPServerTrustPolicy?
  @objc deinit
}
public enum CRPServerTrustPolicy {
  case performDefaultEvaluation(validateHost: Swift.Bool)
  case performRevokedEvaluation(validateHost: Swift.Bool, revocationFlags: CoreFoundation.CFOptionFlags)
  case pinCertificates(certificates: [Security.SecCertificate], validateCertificateChain: Swift.Bool, validateHost: Swift.Bool)
  case pinPublicKeys(publicKeys: [Security.SecKey], validateCertificateChain: Swift.Bool, validateHost: Swift.Bool)
  case disableEvaluation
  case customEvaluation((_ serverTrust: Security.SecTrust, _ host: Swift.String) -> Swift.Bool)
  public static func certificates(in bundle: Foundation.Bundle = Bundle.main) -> [Security.SecCertificate]
  public static func publicKeys(in bundle: Foundation.Bundle = Bundle.main) -> [Security.SecKey]
  public func evaluate(_ serverTrust: Security.SecTrust, forHost host: Swift.String) -> Swift.Bool
}
public protocol CRPURLConvertible {
  func asURL() throws -> Foundation.URL
}
extension Swift.String : CRPSmartRing.CRPURLConvertible {
  public func asURL() throws -> Foundation.URL
}
extension Foundation.URL : CRPSmartRing.CRPURLConvertible {
  public func asURL() throws -> Foundation.URL
}
extension Foundation.URLComponents : CRPSmartRing.CRPURLConvertible {
  public func asURL() throws -> Foundation.URL
}
public protocol CRPURLRequestConvertible {
  func asURLRequest() throws -> Foundation.URLRequest
}
extension CRPSmartRing.CRPURLRequestConvertible {
  public var urlRequest: Foundation.URLRequest? {
    get
  }
}
extension Foundation.URLRequest : CRPSmartRing.CRPURLRequestConvertible {
  public func asURLRequest() throws -> Foundation.URLRequest
}
extension Foundation.URLRequest {
  public init(url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod, headers: CRPSmartRing.HTTPHeaders? = nil) throws
}
@discardableResult
public func request(_ url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .get, parameters: CRPSmartRing.CRPParameters? = nil, encoding: any CRPSmartRing.CRPParameterEncoding = CRPURLEncoding.default, headers: CRPSmartRing.HTTPHeaders? = nil) -> CRPSmartRing.CRPDataRequest
@discardableResult
public func request(_ urlRequest: any CRPSmartRing.CRPURLRequestConvertible) -> CRPSmartRing.CRPDataRequest
@discardableResult
public func download(_ url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .get, parameters: CRPSmartRing.CRPParameters? = nil, encoding: any CRPSmartRing.CRPParameterEncoding = CRPURLEncoding.default, headers: CRPSmartRing.HTTPHeaders? = nil, to destination: CRPSmartRing.CRPDownloadRequest.DownloadFileDestination? = nil) -> CRPSmartRing.CRPDownloadRequest
@discardableResult
public func download(_ urlRequest: any CRPSmartRing.CRPURLRequestConvertible, to destination: CRPSmartRing.CRPDownloadRequest.DownloadFileDestination? = nil) -> CRPSmartRing.CRPDownloadRequest
@discardableResult
public func download(resumingWith resumeData: Foundation.Data, to destination: CRPSmartRing.CRPDownloadRequest.DownloadFileDestination? = nil) -> CRPSmartRing.CRPDownloadRequest
@discardableResult
public func upload(_ fileURL: Foundation.URL, to url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .post, headers: CRPSmartRing.HTTPHeaders? = nil) -> CRPSmartRing.CRPUploadRequest
@discardableResult
public func upload(_ fileURL: Foundation.URL, with urlRequest: any CRPSmartRing.CRPURLRequestConvertible) -> CRPSmartRing.CRPUploadRequest
@discardableResult
public func upload(_ data: Foundation.Data, to url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .post, headers: CRPSmartRing.HTTPHeaders? = nil) -> CRPSmartRing.CRPUploadRequest
@discardableResult
public func upload(_ data: Foundation.Data, with urlRequest: any CRPSmartRing.CRPURLRequestConvertible) -> CRPSmartRing.CRPUploadRequest
@discardableResult
public func upload(_ stream: Foundation.InputStream, to url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .post, headers: CRPSmartRing.HTTPHeaders? = nil) -> CRPSmartRing.CRPUploadRequest
@discardableResult
public func upload(_ stream: Foundation.InputStream, with urlRequest: any CRPSmartRing.CRPURLRequestConvertible) -> CRPSmartRing.CRPUploadRequest
public func upload(multipartFormData: @escaping (CRPSmartRing.CRPMultipartFormData) -> Swift.Void, usingThreshold encodingMemoryThreshold: Swift.UInt64 = CRPSessionManager.multipartFormDataEncodingMemoryThreshold, to url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .post, headers: CRPSmartRing.HTTPHeaders? = nil, encodingCompletion: ((CRPSmartRing.CRPSessionManager.MultipartFormDataEncodingResult) -> Swift.Void)?)
public func upload(multipartFormData: @escaping (CRPSmartRing.CRPMultipartFormData) -> Swift.Void, usingThreshold encodingMemoryThreshold: Swift.UInt64 = CRPSessionManager.multipartFormDataEncodingMemoryThreshold, with urlRequest: any CRPSmartRing.CRPURLRequestConvertible, encodingCompletion: ((CRPSmartRing.CRPSessionManager.MultipartFormDataEncodingResult) -> Swift.Void)?)
@available(iOS 9.0, macOS 10.11, tvOS 9.0, *)
@discardableResult
public func stream(withHostName hostName: Swift.String, port: Swift.Int) -> CRPSmartRing.CRPStreamRequest
@available(iOS 9.0, macOS 10.11, tvOS 9.0, *)
@discardableResult
public func stream(with netService: Foundation.NetService) -> CRPSmartRing.CRPStreamRequest
extension CRPSmartRing.CRPRequest {
  public enum ValidationResult {
    case success
    case failure(any Swift.Error)
  }
}
extension CRPSmartRing.CRPDataRequest {
  public typealias Validation = (Foundation.URLRequest?, Foundation.HTTPURLResponse, Foundation.Data?) -> CRPSmartRing.CRPRequest.ValidationResult
  @discardableResult
  public func validate(_ validation: @escaping CRPSmartRing.CRPDataRequest.Validation) -> Self
  @discardableResult
  public func validate<S>(statusCode acceptableStatusCodes: S) -> Self where S : Swift.Sequence, S.Element == Swift.Int
  @discardableResult
  public func validate<S>(contentType acceptableContentTypes: S) -> Self where S : Swift.Sequence, S.Element == Swift.String
  @discardableResult
  public func validate() -> Self
}
extension CRPSmartRing.CRPDownloadRequest {
  public typealias Validation = (_ request: Foundation.URLRequest?, _ response: Foundation.HTTPURLResponse, _ temporaryURL: Foundation.URL?, _ destinationURL: Foundation.URL?) -> CRPSmartRing.CRPRequest.ValidationResult
  @discardableResult
  public func validate(_ validation: @escaping CRPSmartRing.CRPDownloadRequest.Validation) -> Self
  @discardableResult
  public func validate<S>(statusCode acceptableStatusCodes: S) -> Self where S : Swift.Sequence, S.Element == Swift.Int
  @discardableResult
  public func validate<S>(contentType acceptableContentTypes: S) -> Self where S : Swift.Sequence, S.Element == Swift.String
  @discardableResult
  public func validate() -> Self
}
@objc @_hasMissingDesignatedInitializers open class CRPTaskDelegate : ObjectiveC.NSObject {
  final public let queue: Foundation.OperationQueue
  public var data: Foundation.Data? {
    get
  }
  public var error: (any Swift.Error)?
  @objc deinit
}
public struct CRPTimeline {
  public let requestStartTime: CoreFoundation.CFAbsoluteTime
  public let initialResponseTime: CoreFoundation.CFAbsoluteTime
  public let requestCompletedTime: CoreFoundation.CFAbsoluteTime
  public let serializationCompletedTime: CoreFoundation.CFAbsoluteTime
  public let latency: Foundation.TimeInterval
  public let requestDuration: Foundation.TimeInterval
  public let serializationDuration: Foundation.TimeInterval
  public let totalDuration: Foundation.TimeInterval
  public init(requestStartTime: CoreFoundation.CFAbsoluteTime = 0.0, initialResponseTime: CoreFoundation.CFAbsoluteTime = 0.0, requestCompletedTime: CoreFoundation.CFAbsoluteTime = 0.0, serializationCompletedTime: CoreFoundation.CFAbsoluteTime = 0.0)
}
extension CRPSmartRing.CRPTimeline : Swift.CustomStringConvertible {
  public var description: Swift.String {
    get
  }
}
extension CRPSmartRing.CRPTimeline : Swift.CustomDebugStringConvertible {
  public var debugDescription: Swift.String {
    get
  }
}
open class CRPSessionManager {
  public enum MultipartFormDataEncodingResult {
    case success(request: CRPSmartRing.CRPUploadRequest, streamingFromDisk: Swift.Bool, streamFileURL: Foundation.URL?)
    case failure(any Swift.Error)
  }
  public static let `default`: CRPSmartRing.CRPSessionManager
  public static let defaultHTTPHeaders: CRPSmartRing.HTTPHeaders
  public static let multipartFormDataEncodingMemoryThreshold: Swift.UInt64
  final public let session: Foundation.URLSession
  final public let delegate: CRPSmartRing.CRPSessionDelegate
  open var startRequestsImmediately: Swift.Bool
  open var adapter: (any CRPSmartRing.CRPRequestAdapter)?
  open var retrier: (any CRPSmartRing.CRPRequestRetrier)? {
    get
    set
  }
  open var backgroundCompletionHandler: (() -> Swift.Void)?
  public init(configuration: Foundation.URLSessionConfiguration = URLSessionConfiguration.default, delegate: CRPSmartRing.CRPSessionDelegate = CRPSessionDelegate(), serverTrustPolicyManager: CRPSmartRing.CRPServerTrustPolicyManager? = nil)
  public init?(session: Foundation.URLSession, delegate: CRPSmartRing.CRPSessionDelegate, serverTrustPolicyManager: CRPSmartRing.CRPServerTrustPolicyManager? = nil)
  @objc deinit
  @discardableResult
  open func request(_ url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .get, parameters: CRPSmartRing.CRPParameters? = nil, encoding: any CRPSmartRing.CRPParameterEncoding = CRPURLEncoding.default, headers: CRPSmartRing.HTTPHeaders? = nil) -> CRPSmartRing.CRPDataRequest
  @discardableResult
  open func request(_ urlRequest: any CRPSmartRing.CRPURLRequestConvertible) -> CRPSmartRing.CRPDataRequest
  @discardableResult
  open func download(_ url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .get, parameters: CRPSmartRing.CRPParameters? = nil, encoding: any CRPSmartRing.CRPParameterEncoding = CRPURLEncoding.default, headers: CRPSmartRing.HTTPHeaders? = nil, to destination: CRPSmartRing.CRPDownloadRequest.DownloadFileDestination? = nil) -> CRPSmartRing.CRPDownloadRequest
  @discardableResult
  open func download(_ urlRequest: any CRPSmartRing.CRPURLRequestConvertible, to destination: CRPSmartRing.CRPDownloadRequest.DownloadFileDestination? = nil) -> CRPSmartRing.CRPDownloadRequest
  @discardableResult
  open func download(resumingWith resumeData: Foundation.Data, to destination: CRPSmartRing.CRPDownloadRequest.DownloadFileDestination? = nil) -> CRPSmartRing.CRPDownloadRequest
  @discardableResult
  open func upload(_ fileURL: Foundation.URL, to url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .post, headers: CRPSmartRing.HTTPHeaders? = nil) -> CRPSmartRing.CRPUploadRequest
  @discardableResult
  open func upload(_ fileURL: Foundation.URL, with urlRequest: any CRPSmartRing.CRPURLRequestConvertible) -> CRPSmartRing.CRPUploadRequest
  @discardableResult
  open func upload(_ data: Foundation.Data, to url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .post, headers: CRPSmartRing.HTTPHeaders? = nil) -> CRPSmartRing.CRPUploadRequest
  @discardableResult
  open func upload(_ data: Foundation.Data, with urlRequest: any CRPSmartRing.CRPURLRequestConvertible) -> CRPSmartRing.CRPUploadRequest
  @discardableResult
  open func upload(_ stream: Foundation.InputStream, to url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .post, headers: CRPSmartRing.HTTPHeaders? = nil) -> CRPSmartRing.CRPUploadRequest
  @discardableResult
  open func upload(_ stream: Foundation.InputStream, with urlRequest: any CRPSmartRing.CRPURLRequestConvertible) -> CRPSmartRing.CRPUploadRequest
  open func upload(multipartFormData: @escaping (CRPSmartRing.CRPMultipartFormData) -> Swift.Void, usingThreshold encodingMemoryThreshold: Swift.UInt64 = CRPSessionManager.multipartFormDataEncodingMemoryThreshold, to url: any CRPSmartRing.CRPURLConvertible, method: CRPSmartRing.CRPHTTPMethod = .post, headers: CRPSmartRing.HTTPHeaders? = nil, encodingCompletion: ((CRPSmartRing.CRPSessionManager.MultipartFormDataEncodingResult) -> Swift.Void)?)
  open func upload(multipartFormData: @escaping (CRPSmartRing.CRPMultipartFormData) -> Swift.Void, usingThreshold encodingMemoryThreshold: Swift.UInt64 = CRPSessionManager.multipartFormDataEncodingMemoryThreshold, with urlRequest: any CRPSmartRing.CRPURLRequestConvertible, encodingCompletion: ((CRPSmartRing.CRPSessionManager.MultipartFormDataEncodingResult) -> Swift.Void)?)
  @available(iOS 9.0, macOS 10.11, tvOS 9.0, *)
  @discardableResult
  open func stream(withHostName hostName: Swift.String, port: Swift.Int) -> CRPSmartRing.CRPStreamRequest
  @available(iOS 9.0, macOS 10.11, tvOS 9.0, *)
  @discardableResult
  open func stream(with netService: Foundation.NetService) -> CRPSmartRing.CRPStreamRequest
}
public struct CRPDefaultDataResponse {
  public let request: Foundation.URLRequest?
  public let response: Foundation.HTTPURLResponse?
  public let data: Foundation.Data?
  public let error: (any Swift.Error)?
  public let timeline: CRPSmartRing.CRPTimeline
  public init(request: Foundation.URLRequest?, response: Foundation.HTTPURLResponse?, data: Foundation.Data?, error: (any Swift.Error)?, timeline: CRPSmartRing.CRPTimeline = CRPTimeline(), metrics: Swift.AnyObject? = nil)
}
public struct CRPDataResponse<Value> {
  public let request: Foundation.URLRequest?
  public let response: Foundation.HTTPURLResponse?
  public let data: Foundation.Data?
  public let result: CRPSmartRing.CRPResult<Value>
  public let timeline: CRPSmartRing.CRPTimeline
  public var value: Value? {
    get
  }
  public var error: (any Swift.Error)? {
    get
  }
  public init(request: Foundation.URLRequest?, response: Foundation.HTTPURLResponse?, data: Foundation.Data?, result: CRPSmartRing.CRPResult<Value>, timeline: CRPSmartRing.CRPTimeline = CRPTimeline())
}
extension CRPSmartRing.CRPDataResponse : Swift.CustomStringConvertible, Swift.CustomDebugStringConvertible {
  public var description: Swift.String {
    get
  }
  public var debugDescription: Swift.String {
    get
  }
}
extension CRPSmartRing.CRPDataResponse {
  public func map<T>(_ transform: (Value) -> T) -> CRPSmartRing.CRPDataResponse<T>
  public func flatMap<T>(_ transform: (Value) throws -> T) -> CRPSmartRing.CRPDataResponse<T>
  public func mapError<E>(_ transform: (any Swift.Error) -> E) -> CRPSmartRing.CRPDataResponse<Value> where E : Swift.Error
  public func flatMapError<E>(_ transform: (any Swift.Error) throws -> E) -> CRPSmartRing.CRPDataResponse<Value> where E : Swift.Error
}
public struct CRPDefaultDownloadResponse {
  public let request: Foundation.URLRequest?
  public let response: Foundation.HTTPURLResponse?
  public let temporaryURL: Foundation.URL?
  public let destinationURL: Foundation.URL?
  public let resumeData: Foundation.Data?
  public let error: (any Swift.Error)?
  public let timeline: CRPSmartRing.CRPTimeline
  public init(request: Foundation.URLRequest?, response: Foundation.HTTPURLResponse?, temporaryURL: Foundation.URL?, destinationURL: Foundation.URL?, resumeData: Foundation.Data?, error: (any Swift.Error)?, timeline: CRPSmartRing.CRPTimeline = CRPTimeline(), metrics: Swift.AnyObject? = nil)
}
public struct CRPDownloadResponse<Value> {
  public let request: Foundation.URLRequest?
  public let response: Foundation.HTTPURLResponse?
  public let temporaryURL: Foundation.URL?
  public let destinationURL: Foundation.URL?
  public let resumeData: Foundation.Data?
  public let result: CRPSmartRing.CRPResult<Value>
  public let timeline: CRPSmartRing.CRPTimeline
  public var value: Value? {
    get
  }
  public var error: (any Swift.Error)? {
    get
  }
  public init(request: Foundation.URLRequest?, response: Foundation.HTTPURLResponse?, temporaryURL: Foundation.URL?, destinationURL: Foundation.URL?, resumeData: Foundation.Data?, result: CRPSmartRing.CRPResult<Value>, timeline: CRPSmartRing.CRPTimeline = CRPTimeline())
}
extension CRPSmartRing.CRPDownloadResponse : Swift.CustomStringConvertible, Swift.CustomDebugStringConvertible {
  public var description: Swift.String {
    get
  }
  public var debugDescription: Swift.String {
    get
  }
}
extension CRPSmartRing.CRPDownloadResponse {
  public func map<T>(_ transform: (Value) -> T) -> CRPSmartRing.CRPDownloadResponse<T>
  public func flatMap<T>(_ transform: (Value) throws -> T) -> CRPSmartRing.CRPDownloadResponse<T>
  public func mapError<E>(_ transform: (any Swift.Error) -> E) -> CRPSmartRing.CRPDownloadResponse<Value> where E : Swift.Error
  public func flatMapError<E>(_ transform: (any Swift.Error) throws -> E) -> CRPSmartRing.CRPDownloadResponse<Value> where E : Swift.Error
}
@available(iOS 10.0, macOS 10.12, tvOS 10.0, *)
extension CRPSmartRing.CRPDefaultDataResponse {
  public var metrics: Foundation.URLSessionTaskMetrics? {
    get
  }
}
@available(iOS 10.0, macOS 10.12, tvOS 10.0, *)
extension CRPSmartRing.CRPDataResponse {
  public var metrics: Foundation.URLSessionTaskMetrics? {
    get
  }
}
@available(iOS 10.0, macOS 10.12, tvOS 10.0, *)
extension CRPSmartRing.CRPDefaultDownloadResponse {
  public var metrics: Foundation.URLSessionTaskMetrics? {
    get
  }
}
@available(iOS 10.0, macOS 10.12, tvOS 10.0, *)
extension CRPSmartRing.CRPDownloadResponse {
  public var metrics: Foundation.URLSessionTaskMetrics? {
    get
  }
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers open class GoodixBle : ObjectiveC.NSObject, CoreBluetooth.CBCentralManagerDelegate, CoreBluetooth.CBPeripheralDelegate {
  public static let GoodixUUID: CoreBluetooth.CBUUID
  public static let sharedInstance: CRPSmartRing.GoodixBle
  open var delegate: (any CRPSmartRing.BleProtocol)?
  open func scanWithDuration(_ duration: Foundation.TimeInterval = 3, progressHandler: CRPSmartRing.scanProgressHandler?, completionHandler: CRPSmartRing.scanCompletionHandler?)
  open func breakScan()
  open func disConnect()
  @objc open func centralManagerDidUpdateState(_ central: CoreBluetooth.CBCentralManager)
  @objc open func centralManager(_ central: CoreBluetooth.CBCentralManager, didConnect peripheral: CoreBluetooth.CBPeripheral)
  @objc open func centralManager(_ central: CoreBluetooth.CBCentralManager, didDisconnectPeripheral peripheral: CoreBluetooth.CBPeripheral, error: (any Swift.Error)?)
  @objc open func centralManager(_ central: CoreBluetooth.CBCentralManager, didDiscover peripheral: CoreBluetooth.CBPeripheral, advertisementData: [Swift.String : Any], rssi RSSI: Foundation.NSNumber)
  @objc open func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didUpdateNotificationStateFor characteristic: CoreBluetooth.CBCharacteristic, error: (any Swift.Error)?)
  @objc open func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didUpdateValueFor characteristic: CoreBluetooth.CBCharacteristic, error: (any Swift.Error)?)
  @objc public func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didDiscoverServices error: (any Swift.Error)?)
  @objc open func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didDiscoverCharacteristicsFor service: CoreBluetooth.CBService, error: (any Swift.Error)?)
  @objc public func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didWriteValueFor descriptor: CoreBluetooth.CBDescriptor, error: (any Swift.Error)?)
  @objc deinit
}
@objc @_inheritsConvenienceInitializers open class CRPSessionDelegate : ObjectiveC.NSObject {
  open var sessionDidBecomeInvalidWithError: ((Foundation.URLSession, (any Swift.Error)?) -> Swift.Void)?
  open var sessionDidReceiveChallenge: ((Foundation.URLSession, Foundation.URLAuthenticationChallenge) -> (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?))?
  open var sessionDidReceiveChallengeWithCompletion: ((Foundation.URLSession, Foundation.URLAuthenticationChallenge, @escaping (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?) -> Swift.Void) -> Swift.Void)?
  open var sessionDidFinishEventsForBackgroundURLSession: ((Foundation.URLSession) -> Swift.Void)?
  open var taskWillPerformHTTPRedirection: ((Foundation.URLSession, Foundation.URLSessionTask, Foundation.HTTPURLResponse, Foundation.URLRequest) -> Foundation.URLRequest?)?
  open var taskWillPerformHTTPRedirectionWithCompletion: ((Foundation.URLSession, Foundation.URLSessionTask, Foundation.HTTPURLResponse, Foundation.URLRequest, @escaping (Foundation.URLRequest?) -> Swift.Void) -> Swift.Void)?
  open var taskDidReceiveChallenge: ((Foundation.URLSession, Foundation.URLSessionTask, Foundation.URLAuthenticationChallenge) -> (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?))?
  open var taskDidReceiveChallengeWithCompletion: ((Foundation.URLSession, Foundation.URLSessionTask, Foundation.URLAuthenticationChallenge, @escaping (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?) -> Swift.Void) -> Swift.Void)?
  open var taskNeedNewBodyStream: ((Foundation.URLSession, Foundation.URLSessionTask) -> Foundation.InputStream?)?
  open var taskNeedNewBodyStreamWithCompletion: ((Foundation.URLSession, Foundation.URLSessionTask, @escaping (Foundation.InputStream?) -> Swift.Void) -> Swift.Void)?
  open var taskDidSendBodyData: ((Foundation.URLSession, Foundation.URLSessionTask, Swift.Int64, Swift.Int64, Swift.Int64) -> Swift.Void)?
  open var taskDidComplete: ((Foundation.URLSession, Foundation.URLSessionTask, (any Swift.Error)?) -> Swift.Void)?
  open var dataTaskDidReceiveResponse: ((Foundation.URLSession, Foundation.URLSessionDataTask, Foundation.URLResponse) -> Foundation.URLSession.ResponseDisposition)?
  open var dataTaskDidReceiveResponseWithCompletion: ((Foundation.URLSession, Foundation.URLSessionDataTask, Foundation.URLResponse, @escaping (Foundation.URLSession.ResponseDisposition) -> Swift.Void) -> Swift.Void)?
  open var dataTaskDidBecomeDownloadTask: ((Foundation.URLSession, Foundation.URLSessionDataTask, Foundation.URLSessionDownloadTask) -> Swift.Void)?
  open var dataTaskDidReceiveData: ((Foundation.URLSession, Foundation.URLSessionDataTask, Foundation.Data) -> Swift.Void)?
  open var dataTaskWillCacheResponse: ((Foundation.URLSession, Foundation.URLSessionDataTask, Foundation.CachedURLResponse) -> Foundation.CachedURLResponse?)?
  open var dataTaskWillCacheResponseWithCompletion: ((Foundation.URLSession, Foundation.URLSessionDataTask, Foundation.CachedURLResponse, @escaping (Foundation.CachedURLResponse?) -> Swift.Void) -> Swift.Void)?
  open var downloadTaskDidFinishDownloadingToURL: ((Foundation.URLSession, Foundation.URLSessionDownloadTask, Foundation.URL) -> Swift.Void)?
  open var downloadTaskDidWriteData: ((Foundation.URLSession, Foundation.URLSessionDownloadTask, Swift.Int64, Swift.Int64, Swift.Int64) -> Swift.Void)?
  open var downloadTaskDidResumeAtOffset: ((Foundation.URLSession, Foundation.URLSessionDownloadTask, Swift.Int64, Swift.Int64) -> Swift.Void)?
  @available(iOS 9.0, macOS 10.11, tvOS 9.0, *)
  open var streamTaskReadClosed: ((Foundation.URLSession, Foundation.URLSessionStreamTask) -> Swift.Void)? {
    get
    set
  }
  @available(iOS 9.0, macOS 10.11, tvOS 9.0, *)
  open var streamTaskWriteClosed: ((Foundation.URLSession, Foundation.URLSessionStreamTask) -> Swift.Void)? {
    get
    set
  }
  @available(iOS 9.0, macOS 10.11, tvOS 9.0, *)
  open var streamTaskBetterRouteDiscovered: ((Foundation.URLSession, Foundation.URLSessionStreamTask) -> Swift.Void)? {
    get
    set
  }
  @available(iOS 9.0, macOS 10.11, tvOS 9.0, *)
  open var streamTaskDidBecomeInputAndOutputStreams: ((Foundation.URLSession, Foundation.URLSessionStreamTask, Foundation.InputStream, Foundation.OutputStream) -> Swift.Void)? {
    get
    set
  }
  open subscript(task: Foundation.URLSessionTask) -> CRPSmartRing.CRPRequest? {
    get
    set
  }
  @objc override dynamic public init()
  @objc override dynamic open func responds(to selector: ObjectiveC.Selector) -> Swift.Bool
  @objc deinit
}
extension CRPSmartRing.CRPSessionDelegate : Foundation.URLSessionDelegate {
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, didBecomeInvalidWithError error: (any Swift.Error)?)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, didReceive challenge: Foundation.URLAuthenticationChallenge, completionHandler: @escaping (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?) -> Swift.Void)
  @objc dynamic open func urlSessionDidFinishEvents(forBackgroundURLSession session: Foundation.URLSession)
}
extension CRPSmartRing.CRPSessionDelegate : Foundation.URLSessionTaskDelegate {
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, task: Foundation.URLSessionTask, willPerformHTTPRedirection response: Foundation.HTTPURLResponse, newRequest request: Foundation.URLRequest, completionHandler: @escaping (Foundation.URLRequest?) -> Swift.Void)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, task: Foundation.URLSessionTask, didReceive challenge: Foundation.URLAuthenticationChallenge, completionHandler: @escaping (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?) -> Swift.Void)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, task: Foundation.URLSessionTask, needNewBodyStream completionHandler: @escaping (Foundation.InputStream?) -> Swift.Void)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, task: Foundation.URLSessionTask, didSendBodyData bytesSent: Swift.Int64, totalBytesSent: Swift.Int64, totalBytesExpectedToSend: Swift.Int64)
  @available(iOS 10.0, macOS 10.12, tvOS 10.0, *)
  @objc(URLSession:task:didFinishCollectingMetrics:) dynamic open func urlSession(_ session: Foundation.URLSession, task: Foundation.URLSessionTask, didFinishCollecting metrics: Foundation.URLSessionTaskMetrics)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, task: Foundation.URLSessionTask, didCompleteWithError error: (any Swift.Error)?)
}
extension CRPSmartRing.CRPSessionDelegate : Foundation.URLSessionDataDelegate {
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, dataTask: Foundation.URLSessionDataTask, didReceive response: Foundation.URLResponse, completionHandler: @escaping (Foundation.URLSession.ResponseDisposition) -> Swift.Void)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, dataTask: Foundation.URLSessionDataTask, didBecome downloadTask: Foundation.URLSessionDownloadTask)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, dataTask: Foundation.URLSessionDataTask, didReceive data: Foundation.Data)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, dataTask: Foundation.URLSessionDataTask, willCacheResponse proposedResponse: Foundation.CachedURLResponse, completionHandler: @escaping (Foundation.CachedURLResponse?) -> Swift.Void)
}
extension CRPSmartRing.CRPSessionDelegate : Foundation.URLSessionDownloadDelegate {
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, downloadTask: Foundation.URLSessionDownloadTask, didFinishDownloadingTo location: Foundation.URL)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, downloadTask: Foundation.URLSessionDownloadTask, didWriteData bytesWritten: Swift.Int64, totalBytesWritten: Swift.Int64, totalBytesExpectedToWrite: Swift.Int64)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, downloadTask: Foundation.URLSessionDownloadTask, didResumeAtOffset fileOffset: Swift.Int64, expectedTotalBytes: Swift.Int64)
}
@available(iOS 9.0, macOS 10.11, tvOS 9.0, *)
extension CRPSmartRing.CRPSessionDelegate : Foundation.URLSessionStreamDelegate {
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, readClosedFor streamTask: Foundation.URLSessionStreamTask)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, writeClosedFor streamTask: Foundation.URLSessionStreamTask)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, betterRouteDiscoveredFor streamTask: Foundation.URLSessionStreamTask)
  @objc dynamic open func urlSession(_ session: Foundation.URLSession, streamTask: Foundation.URLSessionStreamTask, didBecome inputStream: Foundation.InputStream, outputStream: Foundation.OutputStream)
}
@objc public protocol BleProtocol {
  @objc optional func didDiscover(_ name: Swift.String, rssi: Foundation.NSNumber)
  @objc optional func didConnect()
  @objc optional func didSyncing()
  @objc optional func didDisconnect()
  @objc optional func didBleReady()
  @objc optional func didReadRSSI(_ rssi: Foundation.NSNumber)
  @objc optional func didConnected()
  @objc optional func didDiscoverCharacteristics(_ peripheral: CoreBluetooth.CBPeripheral, characteristic: CoreBluetooth.CBCharacteristic)
  @objc optional func didUpdateValueForCharacteristic(_ characteristic: CoreBluetooth.CBCharacteristic)
  @objc optional func stateChange(_ central: CoreBluetooth.CBCentralManager)
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @objcMembers public class CRPSmartRingSDK : ObjectiveC.NSObject, CRPSmartRing.CRPManagerDelegate {
  @objc public static let sharedInstance: CRPSmartRing.CRPSmartRingSDK
  @objc public var showLog: Swift.Bool {
    @objc get
    @objc set
  }
  @objc open var delegate: (any CRPSmartRing.CRPManagerDelegate)?
  open var hasBlood: Swift.Int!
  @objc open var currentCRPDiscovery: CRPSmartRing.CRPDiscovery? {
    @objc get
  }
  @objc public func didState(_ state: CRPSmartRing.CRPState)
  @objc public func didBluetoothState(_ state: CRPSmartRing.CRPBluetoothState)
  @objc public func receiveSteps(_ model: CRPSmartRing.CRPStepModel)
  @objc public func receiveHeartRate(_ heartRate: Swift.Int)
  @objc public func receiveSpO2(_ o2: Swift.Int)
  @objc public func receiveHRV(_ hrv: Swift.Int)
  @objc public func receiveStress(_ stress: Swift.Int)
  @objc public func receiveRealTimeHeartRate(_ heartRate: Swift.Int)
  @objc public func receiveOTA(_ state: CRPSmartRing.CRPOTAState, _ progress: Swift.Int)
  @objc public func receiveTrainingList(_ list: [CRPSmartRing.CRPTrainingRecord])
  @objc public func receiveTrainingState(_ state: CRPSmartRing.CRPTrainingType)
  @objc public func receiveWearState(_ state: Swift.Int)
  @objc public func receiveDailyGoal(_ type: CRPSmartRing.CRPTrainingGoalType, state: Swift.Int)
  @objc public func receiveActivityReminder(isReminder: Swift.Int, wearState: Swift.Int, time: Swift.Int, step: Swift.Int)
  @objc public func receiveTrainingGoal(_ type: CRPSmartRing.CRPTrainingGoalType, state: Swift.Int)
  @objc public func receiveTouchType(_ model: CRPSmartRing.CRPTouchModel)
  @objc public func receivePairState(_ state: CRPSmartRing.CRPPairState)
  @objc public func receiveSOS()
  @objc public func receiveMeditationList(_ list: [CRPSmartRing.CRPMeditationRecord])
  @objc public func receiveMeditationState(_ state: CRPSmartRing.CRPMeditationGetState)
  @objc public func receiveSleepList(_ list: [CRPSmartRing.CRPGoMoreSleepRecord])
  @objc public func receiveTrainingRecognition(_ trainingInfo: CRPSmartRing.CRPGoMoreTrainingModel)
  @objc public func receiveTrainingRecognitionList(_ list: [CRPSmartRing.CRPGoMoreTrainingRecord])
  @objc public func receiveBloodPressure(_ sbp: Swift.Int, _ dbp: Swift.Int)
  @objc public func receiveTemperature(_ value: Swift.Double)
  @objc public func recevieTakePhoto()
  @objc public func receiveKnockSwitch(_ model: CRPSmartRing.CRPTouchModel)
  @objc public func goodixDidState(_ state: CRPSmartRing.CRPState)
  @objc public func goodixRestoreState(_ state: CRPSmartRing.CRPRestoreState)
  @objc open func scan(_ duration: Foundation.TimeInterval = 3, progressHandler: CRPSmartRing.scanProgressHandler?, completionHandler: CRPSmartRing.scanCompletionHandler?)
  @objc open func interruptScan()
  @objc open func connet(_ discovery: CRPSmartRing.CRPDiscovery)
  @objc open func reConnet()
  @objc open func remove(_ handler: @escaping CRPSmartRing.removeHandler)
  @objc open func getSteps()
  @objc open func getBattery(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func getSoftver(_ handler: @escaping CRPSmartRing.stringHandler)
  @objc open func getMac(_ handler: @escaping CRPSmartRing.stringHandler)
  @objc open func setUserinfo(_ Userinfo: CRPSmartRing.CRPUserInfoModel)
  @objc open func getUserinfo(_ handler: @escaping CRPSmartRing.profileHandler)
  @objc open func setTime()
  @objc open func setNormalTrainingGoal(_ goals: CRPSmartRing.CRPTrainingGoalsModel)
  @objc open func getNormalTrainingGoal(_ handler: @escaping CRPSmartRing.normalexerciseGoalDateHandler)
  @objc open func setTrainingDayGoal(_ model: CRPSmartRing.CRPTrainingGoalStateModel, _ goals: CRPSmartRing.CRPTrainingGoalsModel)
  @objc open func getTrainingDayGoal(_ handler: @escaping CRPSmartRing.exerciseGoalDateHandler)
  @objc open func setActivityReminder(_ ActivityReminder: CRPSmartRing.CRPActivityReminderModel)
  @objc open func getActivityReminderInfo(_ handler: @escaping CRPSmartRing.sitRemindHandler)
  @objc open func setHeartRateRemind(_ remind: CRPSmartRing.CRPHRRemind)
  @objc open func getHeartRateRemind(_ handler: @escaping CRPSmartRing.hrRemindHandler)
  @objc open func setTimingHeartRate(_ interval: Swift.Int)
  @objc open func getTimingHeartRateInterval(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func setTimingHRV(_ interval: Swift.Int)
  @objc open func getTimingHRVInterval(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func setTimingO2(_ interval: Swift.Int)
  @objc open func getTimingO2Interval(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func setStartSingleHR()
  @objc open func setStopSingleHR()
  @objc open func getHeartRecordData(_ handler: @escaping CRPSmartRing.heartRecordDataHandler)
  @objc open func setStartHRV()
  @objc open func setStopHRV()
  @objc open func setStartStress()
  @objc open func setStopStress()
  @objc open func getHRVRecord(_ handler: @escaping CRPSmartRing.hrvRecordDataHandler)
  @objc open func getStressRecord(_ handler: @escaping CRPSmartRing.stressRecordDataHandler)
  @objc open func setStartSpO2()
  @objc open func setStopSpO2()
  @objc open func getO2RecordData(_ handler: @escaping CRPSmartRing.o2RecordDataHandler)
  @objc open func setTraining(state: CRPSmartRing.CRPTrainingType, type: CRPSmartRing.CRPTrainingGoalType, goal: Swift.Int)
  open func getTrainingSupportList(_ handler: @escaping CRPSmartRing.exerciseSupportListHandler)
  @objc open func getTrainingData(_ day: Swift.Int, _ handler: @escaping CRPSmartRing.exerciseRecordHandler)
  @objc open func getSleepData(_ day: Swift.Int, _ handler: @escaping CRPSmartRing.sleepRecordHandler)
  @objc open func getTimingHeartRate(_ day: Swift.Int, _ handler: @escaping CRPSmartRing.fullDayHRRecordHandler)
  @objc open func getTimingHRV(_ day: Swift.Int, _ handler: @escaping CRPSmartRing.fullDayHRVRecordHandler)
  @objc open func getTimingO2(_ day: Swift.Int, _ hanler: @escaping CRPSmartRing.fullDayO2RecordHandler)
  @objc open func getStepArchiveData(_ day: Swift.Int, _ handler: @escaping CRPSmartRing.fullDayStepsRecordHandler)
  @objc open func getActivityArchiveData(_ day: Swift.Int, _ handler: @escaping CRPSmartRing.fullDayActivitysRecordHandler)
  @objc open func getTrainingtRecordList()
  @objc open func getTrainingRecordData(id: Swift.Int, _ handler: @escaping CRPSmartRing.sportDetailHandler)
  @objc open func getTraingingState(_ handler: @escaping CRPSmartRing.trainingStateHandler)
  @objc open func reset(_ handler: @escaping CRPSmartRing.boolHandler)
  @objc open func shutDown(_ handler: @escaping CRPSmartRing.boolHandler)
  @objc open func checkLatest(_ version: Swift.String, _ mac: Swift.String, handler: @escaping CRPSmartRing.versionHandler)
  @objc open func startGoodixUpgradeFromFile(zipPath: Swift.String)
  @objc open func checkDFUState(_ handler: @escaping CRPSmartRing.stringHandler)
  @objc open func stopUpgrade()
  @objc open func getGitHashInfo(handler: @escaping CRPSmartRing.stringHandler)
  @objc open func getGSensorInfo(handler: @escaping CRPSmartRing.gsensorHandler)
  @objc open func getBatteryInfo(handler: @escaping CRPSmartRing.batteryInfoHandler)
  @objc open func getWearState()
  @objc open func setTrainingData(step: Swift.Int, cal: Swift.Int, exerciseTime: Swift.Int, distance: Swift.Int)
  @objc open func setGoalReminder(type: CRPSmartRing.CRPTrainingGoalType)
  @objc open func setReceiveGoalReminder(type: CRPSmartRing.CRPTrainingGoalType, state: Swift.Int)
  @objc open func setActivityReminderReach()
  @objc open func setSleepData(data: [CRPSmartRing.CRPSleepDetailModel])
  @objc open func setTrainingGoalReach(type: CRPSmartRing.CRPTrainingGoalType)
  @objc open func setReceiveTrainingGoalReach(type: CRPSmartRing.CRPTrainingGoalType, state: Swift.Int)
  @objc open func connectedPeripherial() -> [CoreBluetooth.CBPeripheral]
  @objc open func setTimingTemperature(interval: Swift.Int)
  @objc open func getTimingTemperatureState(handler: @escaping CRPSmartRing.timingTemperatureStateHandler)
  @objc open func getTimingTemperatureData(day: Swift.Int, handler: @escaping CRPSmartRing.timingTemperatureDataHandler)
  @objc open func getMeasurementState(handler: @escaping CRPSmartRing.measurementStateHandler)
  @objc open func getRebootTimes(handler: @escaping CRPSmartRing.intHandler)
  @objc open func reboot()
  @objc open func getRingInfo(handler: @escaping CRPSmartRing.ringInfoHandler)
  @objc open func setRingInfo(info: CRPSmartRing.CRPRingInfoModel)
  @objc open func setTouchSwitch(open: Swift.Bool)
  @objc open func setTouchType(type: CRPSmartRing.CRPTouchType)
  @objc open func setTouchSize(width: Swift.Int, height: Swift.Int)
  @objc open func getTouchType()
  @objc open func setPair()
  @objc open func getPairstate()
  @objc open func clearPairInfo()
  @objc open func getNotificationSupportType(_ handler: @escaping CRPSmartRing.notificationsHandler)
  @objc open func getNotificationState(_ handler: @escaping CRPSmartRing.notificationsHandler)
  @objc open func setNotificationState(_ states: [Swift.Int])
  @objc open func getSOSState(_ handler: @escaping CRPSmartRing.boolHandler)
  @objc open func setSOSState(open: Swift.Bool)
  @objc open func getStepsPerMin(_ handler: @escaping CRPSmartRing.intsHandler)
  @objc open func setPageTurnMode(mode: Swift.Int)
  @objc open func getPageTurnMode(handler: @escaping CRPSmartRing.intHandler)
  @objc open func getMeditationState()
  @objc open func setMeditation(state: CRPSmartRing.CRPMeditationSetState, minute: Swift.Int)
  @objc open func getMeditationRecordList()
  @objc open func getMeditationRecordData(id: Swift.Int, _ handler: @escaping CRPSmartRing.meditationDetailHandler)
  @objc open func getGoMoreAlgorithmSupport(_ handler: @escaping CRPSmartRing.gomoreAlgorithmSupportHandler)
  @objc open func getGoMoreKeySupport(_ handler: @escaping CRPSmartRing.boolHandler)
  @objc open func getGoMoreChipID(_ handler: @escaping CRPSmartRing.stringHandler)
  @objc open func getGoMoreKeySupport(key: Swift.String, _ handler: @escaping CRPSmartRing.boolHandler)
  @objc open func getGoMoreSleepDataList()
  @objc open func getGoMoreSleepDataDetail(id: Swift.Int, _ handler: @escaping CRPSmartRing.goMoreSleepDataHandler)
  @objc open func getGoMoreSleepSegmentationData(id: Swift.Int, _ handler: @escaping CRPSmartRing.goMoreSleepRecordHandler)
  @objc open func getGoMoreSleepType(_ handler: @escaping CRPSmartRing.goMoreSleepTypeHandler)
  @objc open func setGoMoreSleepGenerate(startTime: Swift.Int, sleepTime: Swift.Int, awakeTime: Swift.Int)
  @objc open func setTerminationSleep(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func getGoMoreTrainingRecognitionList()
  @objc open func getGoMoreTrainingRecognitionDetail(id: Swift.Int, _ handler: @escaping CRPSmartRing.goMoreTrainingRecognitionDetailHandler)
  @objc open func setGoMoreTestExerciseRecognitionDataDetail(trainingInfo: CRPSmartRing.CRPGoMoreTrainingModel)
  @objc open func getSleepO2SupportState(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func startDIYUpgradeFromFile(path: Swift.String)
  @objc open func startUpgradeFromFile(path: Swift.String)
  @objc open func setBootLoaderMode()
  @objc open func getBootLoaderMode(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func startBootLoaderUpgrade(zipPath: Swift.String)
  @objc open func setStartBP()
  @objc open func setStopBP()
  @objc open func setStartTemperature()
  @objc open func setStopTemperature()
  @objc open func setFirstConnect()
  @objc open func setShakeToTakePhoto(open: Swift.Bool)
  @objc open func getShakeToTakePhoto(_ handler: @escaping CRPSmartRing.boolHandler)
  @objc open func setTransmitPower(level: Swift.Int)
  @objc open func getTransmitPower(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func setKnockSwitch(open: Swift.Bool)
  @objc open func getKnockSwitch()
  @objc open func goodixFixConnect(p: CRPSmartRing.CRPDiscovery)
  @objc open func disConnectGoodix()
  @objc open func startRestoreGoodix()
  @objc open func setAutoLockTime(time: Swift.Int)
  @objc open func getAutoLockTime(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func setScreenBrightness(level: Swift.Int)
  @objc open func getScreenBrightness(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func setDisplayContent(types: [Swift.Int])
  @objc open func getDisplaySupportInfo(_ handler: @escaping CRPSmartRing.displayTypeHandler)
  @objc open func getDisplayContent(_ handler: @escaping CRPSmartRing.displayTypeHandler)
  @objc open func setVibrationStrength(type: Swift.Int)
  @objc open func getVibrationStrength(handler: @escaping CRPSmartRing.intHandler)
  @objc open func setAlarm(_ alarms: [CRPSmartRing.CRPRingAlarmModel])
  @objc open func setDeleteAlarm(_ id: Swift.Int)
  @objc open func setClearAlarm()
  @objc open func getAlarm(_ handler: @escaping CRPSmartRing.alarmHandler)
  @objc open func setFindDevice(state: Swift.Int)
  @objc open func setActivationCode(state: CRPSmartRing.CRPActivationState, code: Swift.Int)
  @objc open func getActivationState(_ handler: @escaping CRPSmartRing.activationStateHandler)
  @objc open func clearActivationCode(_ handler: @escaping CRPSmartRing.boolHandler)
  @objc open func setTimingStress(_ interval: Swift.Int)
  @objc open func getTimingStressInterval(_ handler: @escaping CRPSmartRing.intHandler)
  @objc open func getTimingStress(_ day: Swift.Int, _ handler: @escaping CRPSmartRing.fullDayStressRecordHandler)
  @objc deinit
}
open class CRPMultipartFormData {
  open var contentType: Swift.String {
    get
    set
  }
  public var contentLength: Swift.UInt64 {
    get
  }
  final public let boundary: Swift.String
  public init()
  public func append(_ data: Foundation.Data, withName name: Swift.String)
  public func append(_ data: Foundation.Data, withName name: Swift.String, mimeType: Swift.String)
  public func append(_ data: Foundation.Data, withName name: Swift.String, fileName: Swift.String, mimeType: Swift.String)
  public func append(_ fileURL: Foundation.URL, withName name: Swift.String)
  public func append(_ fileURL: Foundation.URL, withName name: Swift.String, fileName: Swift.String, mimeType: Swift.String)
  public func append(_ stream: Foundation.InputStream, withLength length: Swift.UInt64, name: Swift.String, fileName: Swift.String, mimeType: Swift.String)
  public func append(_ stream: Foundation.InputStream, withLength length: Swift.UInt64, headers: CRPSmartRing.HTTPHeaders)
  public func encode() throws -> Foundation.Data
  public func writeEncodedData(to fileURL: Foundation.URL) throws
  @objc deinit
}
@_hasMissingDesignatedInitializers open class CRPNetworkReachabilityManager {
  public enum NetworkReachabilityStatus {
    case unknown
    case notReachable
    case reachable(CRPSmartRing.CRPNetworkReachabilityManager.ConnectionType)
  }
  public enum ConnectionType {
    case ethernetOrWiFi
    case wwan
    public static func == (a: CRPSmartRing.CRPNetworkReachabilityManager.ConnectionType, b: CRPSmartRing.CRPNetworkReachabilityManager.ConnectionType) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  public typealias Listener = (CRPSmartRing.CRPNetworkReachabilityManager.NetworkReachabilityStatus) -> Swift.Void
  open var isReachable: Swift.Bool {
    get
  }
  open var isReachableOnWWAN: Swift.Bool {
    get
  }
  open var isReachableOnEthernetOrWiFi: Swift.Bool {
    get
  }
  open var networkReachabilityStatus: CRPSmartRing.CRPNetworkReachabilityManager.NetworkReachabilityStatus {
    get
  }
  open var listenerQueue: Dispatch.DispatchQueue
  open var listener: CRPSmartRing.CRPNetworkReachabilityManager.Listener?
  open var flags: SystemConfiguration.SCNetworkReachabilityFlags? {
    get
  }
  open var previousFlags: SystemConfiguration.SCNetworkReachabilityFlags
  convenience public init?(host: Swift.String)
  convenience public init?()
  @objc deinit
  @discardableResult
  open func startListening() -> Swift.Bool
  open func stopListening()
}
extension CRPSmartRing.CRPNetworkReachabilityManager.NetworkReachabilityStatus : Swift.Equatable {
}
public func == (lhs: CRPSmartRing.CRPNetworkReachabilityManager.NetworkReachabilityStatus, rhs: CRPSmartRing.CRPNetworkReachabilityManager.NetworkReachabilityStatus) -> Swift.Bool
extension Foundation.NSNotification.Name {
  public struct Task {
    public static let DidResume: Foundation.Notification.Name
    public static let DidSuspend: Foundation.Notification.Name
    public static let DidCancel: Foundation.Notification.Name
    public static let DidComplete: Foundation.Notification.Name
  }
}
extension Foundation.Notification {
  public struct CRPKey {
    public static let Task: Swift.String
    public static let ResponseData: Swift.String
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class CRPUpgrade : ObjectiveC.NSObject {
  @objc public static let sharedInstance: CRPSmartRing.CRPUpgrade
  @objc public var bootLoaderUpgradeBlock: (() -> Swift.Void)?
  @objc open func checkLatest(_ version: Swift.String, _ mac: Swift.String)
  @objc open func startUpgrade(info: CRPSmartRing.CRPNewVersionInfo?)
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum CRPState : Swift.Int {
  case unbind
  case connecting
  case connected
  case disconnecting
  case disconnected
  case syncing
  case syncSuccess
  case syncError
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPBluetoothState : Swift.Int {
  case unknown
  case resetting
  case unsupported
  case unauthorized
  case poweredOff
  case poweredOn
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc indirect public enum CRPError : Swift.Int {
  case none
  case disconnected
  case busy
  case timeout
  case interrupted
  case internalError
  case noCentralManagerSet
  case other
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
indirect public enum CRPErrorHandle : Swift.Error {
  case none
  case disconnected
  case busy
  case timeout
  case interrupted
  case internalError(underlyingError: (any Swift.Error)?)
  case noCentralManagerSet
  case other
}
@objc @objcMembers public class CRPStepModel : ObjectiveC.NSObject {
  @objc public var steps: Swift.Int
  @objc public var distance: Swift.Int
  @objc public var calory: Swift.Int
  @objc public var time: Swift.Int
  @objc public init(steps: Swift.Int, distance: Swift.Int, calory: Swift.Int, _ time: Swift.Int = -1)
  @objc deinit
}
@objc @objcMembers public class CRPSleepModel : ObjectiveC.NSObject {
  @objc public var deep: Swift.Int
  @objc public var light: Swift.Int
  @objc public var rem: Swift.Int
  @objc public var detail: [Swift.Dictionary<Swift.String, Swift.String>]
  @objc public init(deep: Swift.Int, light: Swift.Int, rem: Swift.Int, detail: [Swift.Dictionary<Swift.String, Swift.String>])
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPSportModel : ObjectiveC.NSObject {
  @objc final public let date: Swift.String
  @objc final public let startTime: Swift.Int
  @objc final public let endTime: Swift.Int
  @objc final public let vaildTime: Swift.Int
  @objc final public let hrAvg: Swift.Int
  @objc final public let type: CRPSmartRing.CRPTrainingType
  @objc final public let step: Swift.Int
  @objc final public let distance: Swift.Int
  @objc final public let kcal: Swift.Int
  @objc final public let goalType: CRPSmartRing.CRPTrainingGoalType
  @objc final public let goal: Swift.Int
  @objc final public let hrAddress: Swift.Int
  @objc final public let hrLength: Swift.Int
  @objc final public let originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPMeditationRecord : ObjectiveC.NSObject {
  @objc public var id: Swift.Int
  @objc public var startTime: Swift.Int
  @objc public var originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPMeditationModel : ObjectiveC.NSObject {
  @objc final public let date: Swift.String
  @objc final public let startTime: Swift.Int
  @objc final public let endTime: Swift.Int
  @objc final public let vaildTime: Swift.Int
  @objc final public let hrAvg: Swift.Int
  @objc final public let hrAddress: Swift.Int
  @objc final public let hrLength: Swift.Int
  @objc final public let heartRate: [Swift.Int]
  @objc final public let originalStartTime: Swift.Int
  @objc deinit
}
@objc @objcMembers public class CRPUserInfoModel : ObjectiveC.NSObject {
  @objc public var height: Swift.Int
  @objc public var weight: Swift.Int
  @objc public var age: Swift.Int
  @objc public var gender: CRPSmartRing.CRPGenderOption
  @objc public var stepLength: Swift.Int
  @objc public init(height: Swift.Int, weight: Swift.Int, age: Swift.Int, gender: CRPSmartRing.CRPGenderOption, stepLength: Swift.Int)
  @objc deinit
}
@objc public enum CRPGenderOption : Swift.Int {
  case male
  case female
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPWeekDay : Swift.Int {
  case none
  case mon
  case tue
  case wed
  case thu
  case fri
  case sat
  case sun
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPTrainingGoalType : Swift.Int {
  case step = 0
  case kcal = 1
  case exerciseTime = 2
  case distance = 3
  case none = 255
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPTrainingState : Swift.Int {
  case unstarted = 0
  case exercising = 1
  case pause = 2
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPTrainingType : Swift.Int {
  case walking = 0
  case run = 1
  case cycling = 2
  case skipping = 3
  case badminton = 4
  case basketball = 5
  case football = 6
  case swimming = 7
  case hiking = 8
  case tennis = 9
  case rugby = 10
  case golf = 11
  case yoga = 12
  case workout = 13
  case dance = 14
  case baseball = 15
  case elliptical = 16
  case indoorCycling = 17
  case training = 18
  case rowingMachine = 19
  case trailRun = 20
  case ski = 21
  case bowling = 22
  case dumbbells = 23
  case sit_ups = 24
  case onfoot = 25
  case indoorWalk = 26
  case indoorRun = 27
  case cricket = 28
  case kabaddi = 29
  case gps_Walking = 30
  case gps_Run = 31
  case gps_Cycling = 32
  case gps_TrailRun = 33
  case gps_Onfoot = 34
  case Sailing = 35
  case Water_Polo = 36
  case Other_Water_Sports = 37
  case Paddle_Boarding = 38
  case Water_Skiing = 39
  case Kayaking = 40
  case Rafting = 41
  case Rowing = 42
  case Powerboating = 43
  case Finswimming = 44
  case Diving = 45
  case Artistic_Swimming = 46
  case Snorkeling = 47
  case Kitesurfing = 48
  case Open_Water_Swim = 49
  case Rock_Climbing = 50
  case Skateboarding = 51
  case Roller_Skating = 52
  case Parkour = 53
  case ATV = 54
  case Paragliding = 55
  case Triathlon = 56
  case Stair_Climber = 57
  case Stair_Climbing = 58
  case Stepper = 59
  case Core_Training = 60
  case Flexibility = 61
  case Pilates = 62
  case Gymnastics = 63
  case Stretching = 64
  case Strength = 65
  case Cross_Training = 66
  case Aerobics = 67
  case Physical_Training = 68
  case Wall_Ball = 69
  case Barbell = 70
  case Deadlift = 71
  case UpperBody = 72
  case Functional_Training = 73
  case Burpee = 74
  case Back = 75
  case Lower_Body = 76
  case Abs = 77
  case HIIT = 78
  case Weightlifting = 79
  case Square_Dancing = 80
  case Belly_Dance = 81
  case Ballet = 82
  case Zumba = 83
  case Latin_Dance = 84
  case Street_Dance = 85
  case Folk_Dance = 86
  case Jazz_Dance = 87
  case Boxing = 88
  case Wrestling = 89
  case Martial_Arts = 90
  case Tai_Chi = 91
  case Muay_Thai = 92
  case Judo = 93
  case Taekwondo = 94
  case Karate = 95
  case Kickboxing = 96
  case Fencing = 97
  case Kendo = 98
  case Volleyball = 99
  case Softball = 100
  case Hockey = 101
  case Table_Tennis = 102
  case Handball = 103
  case Squash = 104
  case Billiards = 105
  case Shuttlecock = 106
  case Beach_Soccer = 107
  case Beach_Volleyball = 108
  case Sepak_Takraw = 109
  case Ice_Skating = 110
  case Curling = 111
  case Other_Winter_Sports = 112
  case Snowmobile = 113
  case Ice_Hockey = 114
  case Bobsleigh = 115
  case Sledding = 116
  case Archery = 117
  case Darts = 118
  case Tug_of_War = 119
  case Hula_Hoop = 120
  case Kite_Flying = 121
  case House_Riding = 122
  case Frisbee = 123
  case Fishing = 124
  case Equestrian_Sports = 125
  case Athletics = 126
  case Auto_Racing = 127
  case proceed = 253
  case pause = 254
  case end = 255
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPOTAState : Swift.Int {
  case uploading
  case completed
  case failed
  public func description() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPRestoreState : Swift.Int {
  case Repairing
  case completed
  case failed
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPMeditationGetState : Swift.Int {
  case unstarted = 0
  case meditating = 1
  case pause = 2
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPMeditationSetState : Swift.Int {
  case start = 0xFC
  case proceed = 0xFD
  case pause = 0xFE
  case end = 0xFF
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc @objcMembers public class CRPHeartModel : ObjectiveC.NSObject {
  @objc public var starttime: Swift.Double
  @objc public var endtime: Swift.Double
  @objc public var detail: [Swift.Int]
  @objc public var originalStartTime: Swift.Int
  @objc public init(starttime: Swift.Double, endtime: Swift.Double, detail: [Swift.Int], originalStartTime: Swift.Int)
  @objc deinit
}
public func getCurrentLanguage() -> Swift.String
@objc public enum CRPOTAType : Swift.Int {
  case normal
  case bate
  case forced
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc @objcMembers public class CRPNewVersionInfo : ObjectiveC.NSObject {
  public var type: CRPSmartRing.CRPOTAType?
  @objc public var version: Swift.String?
  @objc public var log: Swift.String?
  @objc public var logEN: Swift.String?
  @objc public var mcu: Swift.Int
  @objc public var md5: Swift.String?
  @objc public var fileUrl: Swift.String?
  @objc public init(type: CRPSmartRing.CRPOTAType, version: Swift.String, log: Swift.String, logEN: Swift.String, mcu: Swift.Int, md5: Swift.String, fileUrl: Swift.String)
  @objc deinit
}
@objc @objcMembers public class CRPActivityReminderModel : ObjectiveC.NSObject {
  @objc public var open: Swift.Bool
  @objc public var period: Swift.Int
  @objc public var steps: Swift.Int
  @objc public var startHour: Swift.Int
  @objc public var endHour: Swift.Int
  @objc public init(open: Swift.Bool, period: Swift.Int, steps: Swift.Int, startHour: Swift.Int, endHour: Swift.Int)
  @objc deinit
}
@objc @objcMembers public class CRPHRRemind : ObjectiveC.NSObject {
  @objc public var isRemind: Swift.Bool
  @objc public var max: Swift.Int
  @objc public init(isRemind: Swift.Bool, max: Swift.Int)
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPHeartRecordModel : ObjectiveC.NSObject {
  @objc public var value: Swift.Int
  @objc public var time: Swift.Int
  @objc public var originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPBPRecordModel : ObjectiveC.NSObject {
  @objc public var SBP: Swift.Int
  @objc public var DBP: Swift.Int
  @objc public var time: Swift.Int
  @objc public var originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPO2RecordModel : ObjectiveC.NSObject {
  @objc public var value: Swift.Int
  @objc public var time: Swift.Int
  @objc public var originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPHRVRecordModel : ObjectiveC.NSObject {
  @objc public var hrv: Swift.Int
  @objc public var time: Swift.Int
  @objc public var originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPStressRecordModel : ObjectiveC.NSObject {
  @objc public var stress: Swift.Int
  @objc public var time: Swift.Int
  @objc public var originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPNewTrainingModel : ObjectiveC.NSObject {
  @objc final public let id: Swift.Int
  @objc final public let startTime: Swift.Int
  @objc final public let endTime: Swift.Int
  @objc final public let vaildTime: Swift.Int
  @objc final public let hrAvg: Swift.Int
  @objc final public let type: CRPSmartRing.CRPTrainingType
  @objc final public let step: Swift.Int
  @objc final public let distance: Swift.Int
  @objc final public let kcal: Swift.Int
  @objc final public let heartRate: [Swift.Int]
  @objc final public let goalType: CRPSmartRing.CRPTrainingGoalType
  @objc final public let goal: Swift.Int
  @objc final public let hrAddress: Swift.Int
  @objc final public let hrLength: Swift.Int
  @objc final public let originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPTrainingRecord : ObjectiveC.NSObject {
  @objc public var id: Swift.Int
  @objc public var startTime: Swift.Int
  @objc public var type: Swift.Int
  @objc public var originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPUDTVersionModel : ObjectiveC.NSObject {
  @objc public var udtMaxSize: Swift.Int
  @objc public var udtVersion: Swift.String
  @objc deinit
}
@objc @objcMembers public class CRPTrainingGoalsModel : ObjectiveC.NSObject {
  @objc public var step: Swift.Int
  @objc public var kcal: Swift.Int
  @objc public var distance: Swift.Int
  @objc public var exerciseTime: Swift.Int
  @objc public init(step: Swift.Int, kcal: Swift.Int, distance: Swift.Int, exerciseTime: Swift.Int)
  @objc deinit
}
@objc @objcMembers public class CRPTrainingGoalStateModel : ObjectiveC.NSObject {
  @objc public var open: Swift.Bool
  @objc public var weekday: [Swift.Int]
  @objc public init(open: Swift.Bool, weekday: [Swift.Int])
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPTrainingRecordModel : ObjectiveC.NSObject {
  @objc public var day: Swift.Int
  @objc public var step: Swift.Int
  @objc public var cal: Swift.Int
  @objc public var exerciseTime: Swift.Int
  @objc public var distance: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPSleepRecordModel : ObjectiveC.NSObject {
  @objc public var day: Swift.Int
  @objc public var deep: Swift.Int
  @objc public var light: Swift.Int
  @objc public var rem: Swift.Int
  @objc public var detail: [Swift.Dictionary<Swift.String, Swift.String>]
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPTimingHRRecordModel : ObjectiveC.NSObject {
  @objc public var day: Swift.Int
  @objc public var hearts: [Swift.Int]
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPTimingHRVRecordModel : ObjectiveC.NSObject {
  @objc public var day: Swift.Int
  @objc public var hrvs: [Swift.Int]
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPTimingO2RecordModel : ObjectiveC.NSObject {
  @objc public var day: Swift.Int
  @objc public var o2s: [Swift.Int]
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPTimingStepsRecordModel : ObjectiveC.NSObject {
  @objc public var day: Swift.Int
  @objc public var steps: [Swift.Int]
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPContinueActivitysRecordModel : ObjectiveC.NSObject {
  @objc public var day: Swift.Int
  @objc public var activitys: [Swift.Int]
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPGsensorInfoModel : ObjectiveC.NSObject {
  @objc public var x: Swift.Int
  @objc public var y: Swift.Int
  @objc public var z: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPBatteryInfoModel : ObjectiveC.NSObject {
  @objc public var value: Swift.Int
  @objc public var voltage: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPActivityReminderStateModel : ObjectiveC.NSObject {
  @objc public var wearState: Swift.Bool
  @objc public var time: Swift.Int
  @objc public var steps: Swift.Int
  @objc deinit
}
@objc @objcMembers public class CRPSleepDetailModel : ObjectiveC.NSObject {
  @objc public var type: CRPSmartRing.CRPSleepType
  @objc public var hour: Swift.Int
  @objc public var minute: Swift.Int
  @objc public init(type: CRPSmartRing.CRPSleepType, hour: Swift.Int, minute: Swift.Int)
  @objc deinit
}
@objc public enum CRPSleepType : Swift.Int {
  case awake = 0
  case light = 1
  case deep = 2
  case rem = 3
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPSleepTemperatureRecord : ObjectiveC.NSObject {
  @objc final public let time: Swift.Int
  @objc final public let address: Swift.Int
  @objc final public let length: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPTimingTemperatureDataModel : ObjectiveC.NSObject {
  @objc final public let day: Swift.Int
  @objc final public let tempeartures: [Swift.Double]
  @objc deinit
}
@objc public enum CRPMeasurementState : Swift.Int {
  case notMeasured = 0
  case respirationHR = 1
  case trainingHR = 2
  case staticHR = 3
  case bp = 4
  case spO2 = 5
  case ecg = 6
  case temperature = 7
  case hrv = 8
  case stress = 0x09
  case combination = 0x0A
  case factoryTest = 0x7F
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc @objcMembers public class CRPRingInfoModel : ObjectiveC.NSObject {
  @objc public var color: Swift.Int
  @objc public var size: Swift.Int
  @objc public var type: Swift.Int
  @objc public init(color: Swift.Int, size: Swift.Int, type: Swift.Int)
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPGoMoreSleepRecord : ObjectiveC.NSObject {
  @objc public var id: Swift.Int
  @objc public var startTime: Swift.Int
  @objc public var originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPGoMoreSleepDataModel : ObjectiveC.NSObject {
  @objc public var type: Swift.Int
  @objc public var startTime: Swift.Int
  @objc public var endTime: Swift.Int
  @objc public var totalTime: Swift.Float
  @objc public var sleepEfficiency: Swift.Float
  @objc public var sleepScore: Swift.Float
  @objc public var originalStartTime: Swift.Int
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPGoMoreSleepRecordModel : ObjectiveC.NSObject {
  @objc public var id: Swift.Int
  @objc public var deep: Swift.Int
  @objc public var light: Swift.Int
  @objc public var rem: Swift.Int
  @objc public var detail: [Swift.Dictionary<Swift.String, Swift.String>]
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPGoMoreSleepType : ObjectiveC.NSObject {
  @objc public var state: Swift.Int
  @objc public var type: Swift.Int
  @objc public var reliability: Swift.Int
  @objc public var bedtime: Swift.Int
  @objc deinit
}
@objc @objcMembers public class CRPGoMoreTrainingModel : ObjectiveC.NSObject {
  @objc public var type: Swift.Int
  @objc public var startTime: Swift.Int
  @objc public var endTime: Swift.Int
  @objc public var steps: Swift.Int
  @objc public var calory: Swift.Int
  @objc public var originalStartTime: Swift.Int
  @objc public init(type: Swift.Int, startTime: Swift.Int, endTime: Swift.Int, steps: Swift.Int, calory: Swift.Int, originalStartTime: Swift.Int)
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPGoMoreTrainingRecord : ObjectiveC.NSObject {
  @objc public var id: Swift.Int
  @objc public var startTime: Swift.Int
  @objc public var originalStartTime: Swift.Int
  @objc deinit
}
@objc public enum CRPTouchType : Swift.Int {
  case short_video = 0
  case music = 1
  case take_photo = 2
  case read = 3
  case slideshow = 4
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPPairState : Swift.Int {
  case unPair = 0
  case startPair = 1
  case pairSuccess = 2
  case pairFail = 3
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc @objcMembers public class CRPTouchModel : ObjectiveC.NSObject {
  @objc public var open: Swift.Bool
  @objc public var type: CRPSmartRing.CRPTouchType
  @objc public init(open: Swift.Bool, type: CRPSmartRing.CRPTouchType)
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPGoMoreSupportModel : ObjectiveC.NSObject {
  @objc final public let support: Swift.Bool
  @objc final public let type: Swift.Int
  @objc deinit
}
@objc public enum CRPDisplayType : Swift.Int {
  case time = 0
  case hr = 1
  case step = 2
  case kcal = 3
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPNotificationType : Swift.Int {
  case phone = 0
  case messages = 1
  case wechat = 2
  case qq = 3
  case facebook = 4
  case twitter = 5
  case instagram = 6
  case skype = 7
  case whatsApp = 8
  case line = 9
  case kakaoTalk = 10
  case email = 11
  case messenger = 12
  case zalo = 13
  case telegram = 14
  case viber = 15
  case NateOn = 16
  case Gmail = 17
  case Calenda = 18
  case DailyHunt = 19
  case Outlook = 20
  case Yahoo = 21
  case Inshorts = 22
  case Phonepe = 23
  case Gpay = 24
  case Paytm = 25
  case Swiggy = 26
  case Zomato = 27
  case Uber = 28
  case Ola = 29
  case ReflexApp = 30
  case Snapchat = 31
  case YtMusic = 32
  case YouTube = 33
  case LinkEdin = 34
  case Amazon = 35
  case Flipkart = 36
  case NetFlix = 37
  case Hotstar = 38
  case AmazonPrime = 39
  case GoogleChat = 40
  case Wynk = 41
  case GoogleDrive = 42
  case Dunzo = 43
  case Gaana = 44
  case MissCall = 45
  case WhatsAppBusiness = 46
  case others = 0x80
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc @objcMembers public class CRPRingAlarmModel : ObjectiveC.NSObject {
  @objc public var id: Swift.Int
  @objc public var enable: Swift.Int
  @objc public var type: CRPSmartRing.CRPRingAlarmType
  @objc public var hour: Swift.Int
  @objc public var minute: Swift.Int
  @objc public var year: Swift.Int
  @objc public var month: Swift.Int
  @objc public var day: Swift.Int
  @objc public var weekday: [Swift.Int]
  @objc public init(id: Swift.Int, enable: Swift.Int, type: CRPSmartRing.CRPRingAlarmType, hour: Swift.Int, minute: Swift.Int, year: Swift.Int, month: Swift.Int, day: Swift.Int, weekday: [Swift.Int])
  @objc deinit
}
@objc public enum CRPRingAlarmType : Swift.Int {
  case once
  case everyday
  case weekly
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum CRPActivationState : Swift.Int {
  case NotActivated = 0
  case ActivationStarts = 1
  case Activated = 2
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc @objcMembers public class CRPActivationStateModel : ObjectiveC.NSObject {
  @objc public var state: CRPSmartRing.CRPActivationState
  @objc public var time: Swift.Int
  @objc public init(state: CRPSmartRing.CRPActivationState, time: Swift.Int)
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPTimingStressRecordModel : ObjectiveC.NSObject {
  @objc public var day: Swift.Int
  @objc public var stress: [Swift.Int]
  @objc deinit
}
@objc @_hasMissingDesignatedInitializers @objcMembers public class CRPTimingtemperatureStateModel : ObjectiveC.NSObject {
  @objc public var interval: Swift.Int
  @objc public var type: Swift.Int
  @objc deinit
}
public typealias scanCompletionHandler = ((_ result: [CRPSmartRing.CRPDiscovery]?, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias scanProgressHandler = ((_ newDiscoveries: [CRPSmartRing.CRPDiscovery]) -> Swift.Void)
public typealias bindHandler = ((_ state: CRPSmartRing.CRPState, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias removeHandler = ((_ state: CRPSmartRing.CRPState, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias stepHandler = ((_ stepModel: CRPSmartRing.CRPStepModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias sleepHandler = ((_ sleepModel: CRPSmartRing.CRPSleepModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias allDataHandler = ((_ stepModels: [CRPSmartRing.CRPStepModel], _ sleepModels: [CRPSmartRing.CRPSleepModel], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias intHandler = ((_ value: Swift.Int, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias intsHandler = ((_ value: [Swift.Int], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias stringHandler = ((_ value: Swift.String, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias boolHandler = ((_ state: Swift.Bool, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias profileHandler = ((_ value: CRPSmartRing.CRPUserInfoModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias sportHandler = ((_ sportModels: [CRPSmartRing.CRPSportModel], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias versionHandler = ((_ info: CRPSmartRing.CRPNewVersionInfo?, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias connectedPeripherialHandler = ((_ peripherals: [CoreBluetooth.CBPeripheral], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias sitRemindHandler = (_ sitRemind: CRPSmartRing.CRPActivityReminderModel, _ error: CRPSmartRing.CRPError) -> Swift.Void
public typealias doublesHandler = ((_ value: [Swift.Double], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias hrRemindHandler = ((_ model: CRPSmartRing.CRPHRRemind, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias heartRecordDataHandler = ((_ heartRecordModels: [CRPSmartRing.CRPHeartRecordModel], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias bpRecordDataHandler = ((_ bpRecordModels: [CRPSmartRing.CRPBPRecordModel], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias o2RecordDataHandler = ((_ o2RecordModels: [CRPSmartRing.CRPO2RecordModel], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias sportDetailHandler = ((_ sportModel: CRPSmartRing.CRPNewTrainingModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias hrvRecordDataHandler = ((_ hrvRecordModels: [CRPSmartRing.CRPHRVRecordModel], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias normalexerciseGoalDateHandler = ((_ exerciseGoalsModel: CRPSmartRing.CRPTrainingGoalsModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias exerciseGoalDateHandler = ((_ exerciseGoalsStateModel: CRPSmartRing.CRPTrainingGoalStateModel, _ exerciseGoalsModel: CRPSmartRing.CRPTrainingGoalsModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias exerciseSupportListHandler = ((_ list: [CRPSmartRing.CRPTrainingType], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias exerciseRecordHandler = ((_ model: CRPSmartRing.CRPTrainingRecordModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias sleepRecordHandler = ((_ model: CRPSmartRing.CRPSleepRecordModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias fullDayHRRecordHandler = ((_ model: CRPSmartRing.CRPTimingHRRecordModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias fullDayHRVRecordHandler = ((_ model: CRPSmartRing.CRPTimingHRVRecordModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias fullDayO2RecordHandler = ((_ model: CRPSmartRing.CRPTimingO2RecordModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias fullDayStepsRecordHandler = ((_ model: CRPSmartRing.CRPTimingStepsRecordModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias fullDayActivitysRecordHandler = ((_ model: CRPSmartRing.CRPContinueActivitysRecordModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias gsensorHandler = ((_ model: CRPSmartRing.CRPGsensorInfoModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias batteryInfoHandler = ((_ model: CRPSmartRing.CRPBatteryInfoModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias sitRemindStateHandler = ((_ model: CRPSmartRing.CRPActivityReminderStateModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias trainingStateHandler = ((_ type: CRPSmartRing.CRPTrainingType, _ state: CRPSmartRing.CRPTrainingState, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias sleepTemperatureRecordListHandler = ((_ list: [CRPSmartRing.CRPSleepTemperatureRecord], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias timingTemperatureDataHandler = ((_ model: CRPSmartRing.CRPTimingTemperatureDataModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias measurementStateHandler = ((_ state: CRPSmartRing.CRPMeasurementState, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias ringInfoHandler = ((_ model: CRPSmartRing.CRPRingInfoModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias stressRecordDataHandler = ((_ stressRecordModels: [CRPSmartRing.CRPStressRecordModel], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias notificationsHandler = ((_ value: [Swift.Int], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias meditationDetailHandler = ((_ meditation: CRPSmartRing.CRPMeditationModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias meditationStateHandler = ((_ state: CRPSmartRing.CRPMeditationGetState, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias goMoreSleepDataHandler = ((_ model: CRPSmartRing.CRPGoMoreSleepDataModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias goMoreSleepRecordHandler = ((_ model: CRPSmartRing.CRPGoMoreSleepRecordModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias goMoreSleepTypeHandler = ((_ model: CRPSmartRing.CRPGoMoreSleepType, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias goMoreTrainingRecognitionDetailHandler = ((_ id: Swift.Int, _ model: CRPSmartRing.CRPGoMoreTrainingModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias gomoreAlgorithmSupportHandler = ((_ model: CRPSmartRing.CRPGoMoreSupportModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias displayTypeHandler = ((_ types: [Swift.Int], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias alarmHandler = ((_ value: [CRPSmartRing.CRPRingAlarmModel], _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias activationStateHandler = ((_ model: CRPSmartRing.CRPActivationStateModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias fullDayStressRecordHandler = ((_ model: CRPSmartRing.CRPTimingStressRecordModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public typealias timingTemperatureStateHandler = ((_ model: CRPSmartRing.CRPTimingtemperatureStateModel, _ error: CRPSmartRing.CRPError) -> Swift.Void)
public enum CRPAFError : Swift.Error {
  public enum ParameterEncodingFailureReason {
    case missingURL
    case jsonEncodingFailed(error: any Swift.Error)
    case propertyListEncodingFailed(error: any Swift.Error)
  }
  public enum MultipartEncodingFailureReason {
    case bodyPartURLInvalid(url: Foundation.URL)
    case bodyPartFilenameInvalid(in: Foundation.URL)
    case bodyPartFileNotReachable(at: Foundation.URL)
    case bodyPartFileNotReachableWithError(atURL: Foundation.URL, error: any Swift.Error)
    case bodyPartFileIsDirectory(at: Foundation.URL)
    case bodyPartFileSizeNotAvailable(at: Foundation.URL)
    case bodyPartFileSizeQueryFailedWithError(forURL: Foundation.URL, error: any Swift.Error)
    case bodyPartInputStreamCreationFailed(for: Foundation.URL)
    case outputStreamCreationFailed(for: Foundation.URL)
    case outputStreamFileAlreadyExists(at: Foundation.URL)
    case outputStreamURLInvalid(url: Foundation.URL)
    case outputStreamWriteFailed(error: any Swift.Error)
    case inputStreamReadFailed(error: any Swift.Error)
  }
  public enum ResponseValidationFailureReason {
    case dataFileNil
    case dataFileReadFailed(at: Foundation.URL)
    case missingContentType(acceptableContentTypes: [Swift.String])
    case unacceptableContentType(acceptableContentTypes: [Swift.String], responseContentType: Swift.String)
    case unacceptableStatusCode(code: Swift.Int)
  }
  public enum ResponseSerializationFailureReason {
    case inputDataNil
    case inputDataNilOrZeroLength
    case inputFileNil
    case inputFileReadFailed(at: Foundation.URL)
    case stringSerializationFailed(encoding: Swift.String.Encoding)
    case jsonSerializationFailed(error: any Swift.Error)
    case propertyListSerializationFailed(error: any Swift.Error)
  }
  case invalidURL(url: any CRPSmartRing.CRPURLConvertible)
  case parameterEncodingFailed(reason: CRPSmartRing.CRPAFError.ParameterEncodingFailureReason)
  case multipartEncodingFailed(reason: CRPSmartRing.CRPAFError.MultipartEncodingFailureReason)
  case responseValidationFailed(reason: CRPSmartRing.CRPAFError.ResponseValidationFailureReason)
  case responseSerializationFailed(reason: CRPSmartRing.CRPAFError.ResponseSerializationFailureReason)
}
extension CRPSmartRing.CRPAFError {
  public var isInvalidURLError: Swift.Bool {
    get
  }
  public var isParameterEncodingError: Swift.Bool {
    get
  }
  public var isMultipartEncodingError: Swift.Bool {
    get
  }
  public var isResponseValidationError: Swift.Bool {
    get
  }
  public var isResponseSerializationError: Swift.Bool {
    get
  }
}
extension CRPSmartRing.CRPAFError {
  public var urlConvertible: (any CRPSmartRing.CRPURLConvertible)? {
    get
  }
  public var url: Foundation.URL? {
    get
  }
  public var underlyingError: (any Swift.Error)? {
    get
  }
  public var acceptableContentTypes: [Swift.String]? {
    get
  }
  public var responseContentType: Swift.String? {
    get
  }
  public var responseCode: Swift.Int? {
    get
  }
  public var failedStringEncoding: Swift.String.Encoding? {
    get
  }
}
extension CRPSmartRing.CRPAFError : Foundation.LocalizedError {
  public var errorDescription: Swift.String? {
    get
  }
}
extension CRPSmartRing.CRPHTTPMethod : Swift.Equatable {}
extension CRPSmartRing.CRPHTTPMethod : Swift.Hashable {}
extension CRPSmartRing.CRPHTTPMethod : Swift.RawRepresentable {}
extension CRPSmartRing.CRPURLEncoding.Destination : Swift.Equatable {}
extension CRPSmartRing.CRPURLEncoding.Destination : Swift.Hashable {}
extension CRPSmartRing.CRPURLEncoding.ArrayEncoding : Swift.Equatable {}
extension CRPSmartRing.CRPURLEncoding.ArrayEncoding : Swift.Hashable {}
extension CRPSmartRing.CRPURLEncoding.BoolEncoding : Swift.Equatable {}
extension CRPSmartRing.CRPURLEncoding.BoolEncoding : Swift.Hashable {}
extension CRPSmartRing.CRPNetworkReachabilityManager.ConnectionType : Swift.Equatable {}
extension CRPSmartRing.CRPNetworkReachabilityManager.ConnectionType : Swift.Hashable {}
extension CRPSmartRing.CRPState : Swift.Equatable {}
extension CRPSmartRing.CRPState : Swift.Hashable {}
extension CRPSmartRing.CRPState : Swift.RawRepresentable {}
extension CRPSmartRing.CRPBluetoothState : Swift.Equatable {}
extension CRPSmartRing.CRPBluetoothState : Swift.Hashable {}
extension CRPSmartRing.CRPBluetoothState : Swift.RawRepresentable {}
extension CRPSmartRing.CRPError : Swift.Equatable {}
extension CRPSmartRing.CRPError : Swift.Hashable {}
extension CRPSmartRing.CRPError : Swift.RawRepresentable {}
extension CRPSmartRing.CRPGenderOption : Swift.Equatable {}
extension CRPSmartRing.CRPGenderOption : Swift.Hashable {}
extension CRPSmartRing.CRPGenderOption : Swift.RawRepresentable {}
extension CRPSmartRing.CRPWeekDay : Swift.Equatable {}
extension CRPSmartRing.CRPWeekDay : Swift.Hashable {}
extension CRPSmartRing.CRPWeekDay : Swift.RawRepresentable {}
extension CRPSmartRing.CRPTrainingGoalType : Swift.Equatable {}
extension CRPSmartRing.CRPTrainingGoalType : Swift.Hashable {}
extension CRPSmartRing.CRPTrainingGoalType : Swift.RawRepresentable {}
extension CRPSmartRing.CRPTrainingState : Swift.Equatable {}
extension CRPSmartRing.CRPTrainingState : Swift.Hashable {}
extension CRPSmartRing.CRPTrainingState : Swift.RawRepresentable {}
extension CRPSmartRing.CRPTrainingType : Swift.Equatable {}
extension CRPSmartRing.CRPTrainingType : Swift.Hashable {}
extension CRPSmartRing.CRPTrainingType : Swift.RawRepresentable {}
extension CRPSmartRing.CRPOTAState : Swift.Equatable {}
extension CRPSmartRing.CRPOTAState : Swift.Hashable {}
extension CRPSmartRing.CRPOTAState : Swift.RawRepresentable {}
extension CRPSmartRing.CRPRestoreState : Swift.Equatable {}
extension CRPSmartRing.CRPRestoreState : Swift.Hashable {}
extension CRPSmartRing.CRPRestoreState : Swift.RawRepresentable {}
extension CRPSmartRing.CRPMeditationGetState : Swift.Equatable {}
extension CRPSmartRing.CRPMeditationGetState : Swift.Hashable {}
extension CRPSmartRing.CRPMeditationGetState : Swift.RawRepresentable {}
extension CRPSmartRing.CRPMeditationSetState : Swift.Equatable {}
extension CRPSmartRing.CRPMeditationSetState : Swift.Hashable {}
extension CRPSmartRing.CRPMeditationSetState : Swift.RawRepresentable {}
extension CRPSmartRing.CRPOTAType : Swift.Equatable {}
extension CRPSmartRing.CRPOTAType : Swift.Hashable {}
extension CRPSmartRing.CRPOTAType : Swift.RawRepresentable {}
extension CRPSmartRing.CRPSleepType : Swift.Equatable {}
extension CRPSmartRing.CRPSleepType : Swift.Hashable {}
extension CRPSmartRing.CRPSleepType : Swift.RawRepresentable {}
extension CRPSmartRing.CRPMeasurementState : Swift.Equatable {}
extension CRPSmartRing.CRPMeasurementState : Swift.Hashable {}
extension CRPSmartRing.CRPMeasurementState : Swift.RawRepresentable {}
extension CRPSmartRing.CRPTouchType : Swift.Equatable {}
extension CRPSmartRing.CRPTouchType : Swift.Hashable {}
extension CRPSmartRing.CRPTouchType : Swift.RawRepresentable {}
extension CRPSmartRing.CRPPairState : Swift.Equatable {}
extension CRPSmartRing.CRPPairState : Swift.Hashable {}
extension CRPSmartRing.CRPPairState : Swift.RawRepresentable {}
extension CRPSmartRing.CRPDisplayType : Swift.Equatable {}
extension CRPSmartRing.CRPDisplayType : Swift.Hashable {}
extension CRPSmartRing.CRPDisplayType : Swift.RawRepresentable {}
extension CRPSmartRing.CRPNotificationType : Swift.Equatable {}
extension CRPSmartRing.CRPNotificationType : Swift.Hashable {}
extension CRPSmartRing.CRPNotificationType : Swift.RawRepresentable {}
extension CRPSmartRing.CRPRingAlarmType : Swift.Equatable {}
extension CRPSmartRing.CRPRingAlarmType : Swift.Hashable {}
extension CRPSmartRing.CRPRingAlarmType : Swift.RawRepresentable {}
extension CRPSmartRing.CRPActivationState : Swift.Equatable {}
extension CRPSmartRing.CRPActivationState : Swift.Hashable {}
extension CRPSmartRing.CRPActivationState : Swift.RawRepresentable {}
