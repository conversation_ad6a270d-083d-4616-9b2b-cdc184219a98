#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 6.0.2 effective-5.10 (swiftlang-6.0.2.1.2 clang-1600.0.26.4)
#ifndef CRPSMARTRING_SWIFT_H
#define CRPSMARTRING_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import CoreBluetooth;
@import Dispatch;
@import Foundation;
@import ObjectiveC;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="CRPSmartRing",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)
@class NSData;
@class NSString;

SWIFT_CLASS("_TtC12CRPSmartRing6Binary")
@interface Binary : NSObject
@property (nonatomic) NSInteger readingOffset;
- (nonnull instancetype)initWithBytes:(NSArray<NSNumber *> * _Nonnull)bytes OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull bytes;
- (nonnull instancetype)initWithData:(NSData * _Nonnull)data OBJC_DESIGNATED_INITIALIZER;
- (NSInteger)bit:(NSInteger)position SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)bits:(NSRange)range SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)bits:(NSInteger)start :(NSInteger)length SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)byte:(NSInteger)position SWIFT_WARN_UNUSED_RESULT;
- (NSArray<NSNumber *> * _Nonnull)bytes:(NSInteger)start :(NSInteger)length SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)getBytes:(NSInteger)start :(NSInteger)length SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)getCount:(NSInteger)position SWIFT_WARN_UNUSED_RESULT;
- (uint32_t)getUInt32:(NSInteger)start SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)getBytesBE:(NSInteger)start :(NSInteger)length SWIFT_WARN_UNUSED_RESULT;
- (void)putDataWithData:(NSData * _Nonnull)data;
- (void)putUInt8:(uint8_t)value;
- (void)putUInt16:(uint32_t)value;
- (float)getFloat:(NSInteger)start SWIFT_WARN_UNUSED_RESULT;
- (NSArray<NSNumber *> * _Nonnull)getFloatDataWithFloat:(float)float_ SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)getUInt8:(NSInteger)start SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)getUInt16BE:(NSInteger)start SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)getUInt16LE:(NSInteger)start SWIFT_WARN_UNUSED_RESULT;
- (BOOL)bitsWithInternalOffsetAvailable:(NSInteger)length SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)nextWithBits:(NSInteger)length SWIFT_WARN_UNUSED_RESULT;
- (BOOL)bytesWithInternalOffsetAvailable:(NSInteger)length SWIFT_WARN_UNUSED_RESULT;
- (NSArray<NSNumber *> * _Nonnull)nextWithBytes:(NSInteger)length SWIFT_WARN_UNUSED_RESULT;
+ (uint8_t)Int8:(NSArray<NSNumber *> * _Nonnull)bits SWIFT_WARN_UNUSED_RESULT;
+ (NSArray<NSNumber *> * _Nonnull)intToUint8:(NSInteger)number SWIFT_WARN_UNUSED_RESULT;
+ (NSInteger)hexTodecWithNumber:(NSString * _Nonnull)num SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)getInt16:(NSInteger)start SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class NSNumber;
@class CBPeripheral;
@class CBCharacteristic;
@class CBCentralManager;

/// easy protocol, to use ble
SWIFT_PROTOCOL("_TtP12CRPSmartRing11BleProtocol_")
@protocol BleProtocol
@optional
- (void)didDiscover:(NSString * _Nonnull)name rssi:(NSNumber * _Nonnull)rssi;
- (void)didConnect;
- (void)didSyncing;
- (void)didDisconnect;
- (void)didBleReady;
- (void)didReadRSSI:(NSNumber * _Nonnull)rssi;
- (void)didConnected;
- (void)didDiscoverCharacteristics:(CBPeripheral * _Nonnull)peripheral characteristic:(CBCharacteristic * _Nonnull)characteristic;
- (void)didUpdateValueForCharacteristic:(CBCharacteristic * _Nonnull)characteristic;
- (void)stateChange:(CBCentralManager * _Nonnull)central;
@end

typedef SWIFT_ENUM(NSInteger, CRPActivationState, open) {
  CRPActivationStateNotActivated = 0,
  CRPActivationStateActivationStarts = 1,
  CRPActivationStateActivated = 2,
};


SWIFT_CLASS("_TtC12CRPSmartRing23CRPActivationStateModel")
@interface CRPActivationStateModel : NSObject
/// 激活状态
@property (nonatomic) enum CRPActivationState state;
/// 激活时间
@property (nonatomic) NSInteger time;
- (nonnull instancetype)initWithState:(enum CRPActivationState)state time:(NSInteger)time OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing24CRPActivityReminderModel")
@interface CRPActivityReminderModel : NSObject
@property (nonatomic) BOOL open;
@property (nonatomic) NSInteger period;
@property (nonatomic) NSInteger steps;
@property (nonatomic) NSInteger startHour;
@property (nonatomic) NSInteger endHour;
- (nonnull instancetype)initWithOpen:(BOOL)open period:(NSInteger)period steps:(NSInteger)steps startHour:(NSInteger)startHour endHour:(NSInteger)endHour OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing29CRPActivityReminderStateModel")
@interface CRPActivityReminderStateModel : NSObject
@property (nonatomic) BOOL wearState;
@property (nonatomic) NSInteger time;
@property (nonatomic) NSInteger steps;
- (nonnull instancetype)initWithWearState:(BOOL)wearState time:(NSInteger)time steps:(NSInteger)steps OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing16CRPBPRecordModel")
@interface CRPBPRecordModel : NSObject
@property (nonatomic) NSInteger SBP;
@property (nonatomic) NSInteger DBP;
@property (nonatomic) NSInteger time;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithSBP:(NSInteger)SBP DBP:(NSInteger)DBP time:(NSInteger)time originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing19CRPBatteryInfoModel")
@interface CRPBatteryInfoModel : NSObject
@property (nonatomic) NSInteger value;
@property (nonatomic) NSInteger voltage;
- (nonnull instancetype)initWithValue:(NSInteger)value voltage:(NSInteger)voltage OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class CBUUID;
@class NSTimer;
@class CBService;
@class CRPDiscovery;
enum CRPError : NSInteger;
@class NSUUID;

SWIFT_CLASS("_TtC12CRPSmartRing6CRPBle")
@interface CRPBle : NSObject <CBCentralManagerDelegate, CBPeripheralDelegate>
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CBUUID * _Nonnull wechatServiceUUID;)
+ (CBUUID * _Nonnull)wechatServiceUUID SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CBUUID * _Nonnull enServiceUUID;)
+ (CBUUID * _Nonnull)enServiceUUID SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CBUUID * _Nonnull ttServiceUUID;)
+ (CBUUID * _Nonnull)ttServiceUUID SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CBUUID * _Nonnull mh08ServiceUUID;)
+ (CBUUID * _Nonnull)mh08ServiceUUID SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, readonly, copy) NSString * _Nonnull commandCharacteristicUUID;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CRPBle * _Nonnull sharedInstance;)
+ (CRPBle * _Nonnull)sharedInstance SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic) uint8_t bright;
@property (nonatomic) uint8_t color;
@property (nonatomic) NSInteger MTULength;
@property (nonatomic, strong) id <BleProtocol> _Nullable delegate;
@property (nonatomic, readonly, copy) NSArray<CBUUID *> * _Nonnull DFUUID;
@property (nonatomic, strong) NSTimer * _Nullable rescanTimer;
@property (nonatomic, strong) NSTimer * _Nullable rssiTimer;
@property (nonatomic, strong) CBCentralManager * _Null_unspecified centralManager;
@property (nonatomic, strong) CBPeripheral * _Nullable myperipheral;
@property (nonatomic, strong) CBCharacteristic * _Nullable mychar;
@property (nonatomic, strong) CBService * _Nullable myservice;
@property (nonatomic) BOOL isKeepConnect;
@property (nonatomic) BOOL isConnect;
@property (nonatomic) BOOL busy;
@property (nonatomic) BOOL initIsInvocked;
- (BOOL)initIsInvocked SWIFT_METHOD_FAMILY(none) SWIFT_WARN_UNUSED_RESULT;
/// 是否包含DIS的服务
@property (nonatomic) BOOL isHasGetDIS;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
- (void)scanWithDuration:(NSTimeInterval)duration progressHandler:(void (^ _Nullable)(NSArray<CRPDiscovery *> * _Nonnull))progressHandler completionHandler:(void (^ _Nullable)(NSArray<CRPDiscovery *> * _Nullable, enum CRPError))completionHandler;
/// stop scan ble device
- (void)breakScan;
- (void)interruptScan;
- (void)disConnect;
- (void)disConnectOnce;
/// connect a peripheral
- (void)connect:(CBPeripheral * _Nonnull)peripheral;
- (void)managerRetrievePeripheral:(CBPeripheral * _Nonnull)peripheral;
/// send one byte to connected peripheral
/// \param value one byte data will send
///
- (void)sendByte:(uint8_t)value;
/// send String to connected peripheral
/// \param value -> a string will send
///
- (void)sendString:(NSString * _Nonnull)value;
/// send data to control a car
/// \param left left speed
///
/// \param right right speed
///
- (void)sendRunCommandWithLeft:(int8_t)left right:(int8_t)right;
/// append new find peripheral to peripherals and update rssi
/// \param peripheral 
///
/// \param RSSI 
///
- (void)appendPeripheral:(CBPeripheral * _Nonnull)peripheral RSSI:(NSNumber * _Nonnull)RSSI;
/// return the max rssi peripheral tin peripherals
///
/// returns:
/// peripheral with best rssi or nil
- (CBPeripheral * _Nullable)getMaxPeripheral SWIFT_WARN_UNUSED_RESULT;
- (NSArray<CBPeripheral *> * _Nonnull)retrievePeripheralsWithIdentifier:(NSUUID * _Nonnull)identifier SWIFT_WARN_UNUSED_RESULT;
- (NSArray<CBPeripheral *> * _Nonnull)connectedPeripherial SWIFT_WARN_UNUSED_RESULT;
/// read rssi every seconds
- (void)updateRSSI;
- (void)writeData:(CBPeripheral * _Nonnull)peripheral characteristic:(CBCharacteristic * _Nonnull)characteristic data:(NSData * _Nonnull)data type:(CBCharacteristicWriteType)type;
- (void)writeOTAData:(CBPeripheral * _Nonnull)peripheral characteristic:(CBCharacteristic * _Nonnull)characteristic data:(NSData * _Nonnull)data type:(CBCharacteristicWriteType)type;
- (void)centralManagerDidUpdateState:(CBCentralManager * _Nonnull)central;
- (void)centralManager:(CBCentralManager * _Nonnull)central didConnectPeripheral:(CBPeripheral * _Nonnull)peripheral;
- (void)centralManager:(CBCentralManager * _Nonnull)central willRestoreState:(NSDictionary<NSString *, id> * _Nonnull)dict;
- (void)centralManager:(CBCentralManager * _Nonnull)central didDisconnectPeripheral:(CBPeripheral * _Nonnull)peripheral error:(NSError * _Nullable)error;
- (void)centralManager:(CBCentralManager * _Nonnull)central didFailToConnectPeripheral:(CBPeripheral * _Nonnull)peripheral error:(NSError * _Nullable)error;
- (void)centralManager:(CBCentralManager * _Nonnull)central didDiscoverPeripheral:(CBPeripheral * _Nonnull)peripheral advertisementData:(NSDictionary<NSString *, id> * _Nonnull)advertisementData RSSI:(NSNumber * _Nonnull)RSSI;
- (void)centralManager:(CBCentralManager * _Nonnull)central didRetrievePeripherals:(CBPeripheral * _Nonnull)peripherals;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didUpdateNotificationStateForCharacteristic:(CBCharacteristic * _Nonnull)characteristic error:(NSError * _Nullable)error;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didUpdateValueForCharacteristic:(CBCharacteristic * _Nonnull)characteristic error:(NSError * _Nullable)error;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didWriteValueForCharacteristic:(CBCharacteristic * _Nonnull)characteristic error:(NSError * _Nullable)error;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didReadRSSI:(NSNumber * _Nonnull)RSSI error:(NSError * _Nullable)error;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didDiscoverServices:(NSError * _Nullable)error;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didDiscoverCharacteristicsForService:(CBService * _Nonnull)service error:(NSError * _Nullable)error;
@end

typedef SWIFT_ENUM(NSInteger, CRPBluetoothState, open) {
  CRPBluetoothStateUnknown = 0,
  CRPBluetoothStateResetting = 1,
  CRPBluetoothStateUnsupported = 2,
  CRPBluetoothStateUnauthorized = 3,
  CRPBluetoothStatePoweredOff = 4,
  CRPBluetoothStatePoweredOn = 5,
};


SWIFT_CLASS("_TtC12CRPSmartRing31CRPContinueActivitysRecordModel")
@interface CRPContinueActivitysRecordModel : NSObject
@property (nonatomic) NSInteger day;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull activitys;
- (nonnull instancetype)initWithDay:(NSInteger)day activitys:(NSArray<NSNumber *> * _Nonnull)activitys OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class NSURLSession;
@class NSURLSessionTask;
@class NSHTTPURLResponse;
@class NSURLRequest;
@class NSURLAuthenticationChallenge;
@class NSURLCredential;
@class NSInputStream;

/// The task delegate is responsible for handling all delegate callbacks for the underlying task as well as
/// executing all operations attached to the serial operation queue upon task completion.
SWIFT_CLASS("_TtC12CRPSmartRing15CRPTaskDelegate")
@interface CRPTaskDelegate : NSObject
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task willPerformHTTPRedirection:(NSHTTPURLResponse * _Nonnull)response newRequest:(NSURLRequest * _Nonnull)request completionHandler:(void (^ _Nonnull)(NSURLRequest * _Nullable))completionHandler;
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task didReceiveChallenge:(NSURLAuthenticationChallenge * _Nonnull)challenge completionHandler:(void (^ _Nonnull)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler;
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task needNewBodyStream:(void (^ _Nonnull)(NSInputStream * _Nullable))completionHandler;
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task didCompleteWithError:(NSError * _Nullable)error;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class NSURLSessionDataTask;
@class NSURLResponse;
@class NSURLSessionDownloadTask;
@class NSCachedURLResponse;

SWIFT_CLASS("_TtC12CRPSmartRing19CRPDataTaskDelegate")
@interface CRPDataTaskDelegate : CRPTaskDelegate <NSURLSessionDataDelegate>
- (void)URLSession:(NSURLSession * _Nonnull)session dataTask:(NSURLSessionDataTask * _Nonnull)dataTask didReceiveResponse:(NSURLResponse * _Nonnull)response completionHandler:(void (^ _Nonnull)(NSURLSessionResponseDisposition))completionHandler;
- (void)URLSession:(NSURLSession * _Nonnull)session dataTask:(NSURLSessionDataTask * _Nonnull)dataTask didBecomeDownloadTask:(NSURLSessionDownloadTask * _Nonnull)downloadTask;
- (void)URLSession:(NSURLSession * _Nonnull)session dataTask:(NSURLSessionDataTask * _Nonnull)dataTask didReceiveData:(NSData * _Nonnull)data;
- (void)URLSession:(NSURLSession * _Nonnull)session dataTask:(NSURLSessionDataTask * _Nonnull)dataTask willCacheResponse:(NSCachedURLResponse * _Nonnull)proposedResponse completionHandler:(void (^ _Nonnull)(NSCachedURLResponse * _Nullable))completionHandler;
@end

@class CRPRingInfoModel;

SWIFT_CLASS("_TtC12CRPSmartRing12CRPDiscovery")
@interface CRPDiscovery : NSObject
@property (nonatomic, readonly, copy) NSString * _Nullable localName;
@property (nonatomic, copy) NSString * _Nullable kCABAdvidataLocalName;
@property (nonatomic, readonly, copy) NSDictionary<NSString *, id> * _Nonnull advertisementData;
/// The remote peripheral that was discovered.
@property (nonatomic, readonly, strong) CBPeripheral * _Nonnull remotePeripheral;
@property (nonatomic, readonly) NSInteger RSSI;
@property (nonatomic, copy) NSString * _Nullable mac;
/// 从广播数据中读取的版本号
@property (nonatomic, copy) NSString * _Nonnull ver;
/// 从广播数据中读取的平台  0:null, 1:Nordic, 2: Huntersun 3:RealTek, 4:Goodix
@property (nonatomic) NSInteger platform;
@property (nonatomic) BOOL isPair;
@property (nonatomic) BOOL isTalk;
@property (nonatomic, strong) CRPRingInfoModel * _Nullable info;
- (nonnull instancetype)initWithAdvertisementData:(NSDictionary<NSString *, id> * _Nonnull)advertisementData remotePeripheral:(CBPeripheral * _Nonnull)remotePeripheral RSSI:(NSInteger)RSSI OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithRemotePeripheral:(CBPeripheral * _Nonnull)remotePeripheral mac:(NSString * _Nonnull)mac OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPDisplayType, open) {
  CRPDisplayTypeTime = 0,
  CRPDisplayTypeHr = 1,
  CRPDisplayTypeStep = 2,
  CRPDisplayTypeKcal = 3,
};

@class NSURL;

SWIFT_CLASS("_TtC12CRPSmartRing23CRPDownloadTaskDelegate")
@interface CRPDownloadTaskDelegate : CRPTaskDelegate <NSURLSessionDownloadDelegate>
- (void)URLSession:(NSURLSession * _Nonnull)session downloadTask:(NSURLSessionDownloadTask * _Nonnull)downloadTask didFinishDownloadingToURL:(NSURL * _Nonnull)location;
- (void)URLSession:(NSURLSession * _Nonnull)session downloadTask:(NSURLSessionDownloadTask * _Nonnull)downloadTask didWriteData:(int64_t)bytesWritten totalBytesWritten:(int64_t)totalBytesWritten totalBytesExpectedToWrite:(int64_t)totalBytesExpectedToWrite;
- (void)URLSession:(NSURLSession * _Nonnull)session downloadTask:(NSURLSessionDownloadTask * _Nonnull)downloadTask didResumeAtOffset:(int64_t)fileOffset expectedTotalBytes:(int64_t)expectedTotalBytes;
@end

typedef SWIFT_ENUM(NSInteger, CRPError, open) {
  CRPErrorNone = 0,
  CRPErrorDisconnected = 1,
  CRPErrorBusy = 2,
  CRPErrorTimeout = 3,
  CRPErrorInterrupted = 4,
  CRPErrorInternalError = 5,
  CRPErrorNoCentralManagerSet = 6,
  CRPErrorOther = 7,
};

typedef SWIFT_ENUM(NSInteger, CRPGenderOption, open) {
  CRPGenderOptionMale = 0,
  CRPGenderOptionFemale = 1,
};


SWIFT_CLASS("_TtC12CRPSmartRing23CRPGoMoreSleepDataModel")
@interface CRPGoMoreSleepDataModel : NSObject
@property (nonatomic) NSInteger type;
@property (nonatomic) NSInteger startTime;
@property (nonatomic) NSInteger endTime;
@property (nonatomic) float totalTime;
@property (nonatomic) float sleepEfficiency;
@property (nonatomic) float sleepScore;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithType:(NSInteger)type startTime:(NSInteger)startTime endTime:(NSInteger)endTime totalTime:(float)totalTime sleepEfficiency:(float)sleepEfficiency sleepScore:(float)sleepScore originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing20CRPGoMoreSleepRecord")
@interface CRPGoMoreSleepRecord : NSObject
@property (nonatomic) NSInteger id;
@property (nonatomic) NSInteger startTime;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithId:(NSInteger)id startTime:(NSInteger)startTime originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing25CRPGoMoreSleepRecordModel")
@interface CRPGoMoreSleepRecordModel : NSObject
@property (nonatomic) NSInteger id;
@property (nonatomic) NSInteger deep;
@property (nonatomic) NSInteger light;
@property (nonatomic) NSInteger rem;
@property (nonatomic, copy) NSArray<NSDictionary<NSString *, NSString *> *> * _Nonnull detail;
- (nonnull instancetype)initWithId:(NSInteger)id deep:(NSInteger)deep light:(NSInteger)light rem:(NSInteger)rem detail:(NSArray<NSDictionary<NSString *, NSString *> *> * _Nonnull)detail OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing18CRPGoMoreSleepType")
@interface CRPGoMoreSleepType : NSObject
@property (nonatomic) NSInteger state;
@property (nonatomic) NSInteger type;
@property (nonatomic) NSInteger reliability;
@property (nonatomic) NSInteger bedtime;
- (nonnull instancetype)initWithState:(NSInteger)state type:(NSInteger)type reliability:(NSInteger)reliability bedtime:(NSInteger)bedtime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing21CRPGoMoreSupportModel")
@interface CRPGoMoreSupportModel : NSObject
@property (nonatomic, readonly) BOOL support;
@property (nonatomic, readonly) NSInteger type;
- (nonnull instancetype)initWithSupport:(BOOL)support type:(NSInteger)type OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing22CRPGoMoreTrainingModel")
@interface CRPGoMoreTrainingModel : NSObject
@property (nonatomic) NSInteger type;
@property (nonatomic) NSInteger startTime;
@property (nonatomic) NSInteger endTime;
@property (nonatomic) NSInteger steps;
@property (nonatomic) NSInteger calory;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithType:(NSInteger)type startTime:(NSInteger)startTime endTime:(NSInteger)endTime steps:(NSInteger)steps calory:(NSInteger)calory originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing23CRPGoMoreTrainingRecord")
@interface CRPGoMoreTrainingRecord : NSObject
@property (nonatomic) NSInteger id;
@property (nonatomic) NSInteger startTime;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithId:(NSInteger)id startTime:(NSInteger)startTime originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing19CRPGsensorInfoModel")
@interface CRPGsensorInfoModel : NSObject
@property (nonatomic) NSInteger x;
@property (nonatomic) NSInteger y;
@property (nonatomic) NSInteger z;
- (nonnull instancetype)initWithX:(NSInteger)x y:(NSInteger)y z:(NSInteger)z OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing11CRPHRRemind")
@interface CRPHRRemind : NSObject
@property (nonatomic) BOOL isRemind;
@property (nonatomic) NSInteger max;
- (nonnull instancetype)initWithIsRemind:(BOOL)isRemind max:(NSInteger)max OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing17CRPHRVRecordModel")
@interface CRPHRVRecordModel : NSObject
@property (nonatomic) NSInteger hrv;
@property (nonatomic) NSInteger time;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithHrv:(NSInteger)hrv time:(NSInteger)time originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing14CRPHeart24Data")
@interface CRPHeart24Data : NSObject
@property (nonatomic, copy) NSDictionary<NSNumber *, NSArray<NSNumber *> *> * _Nonnull heartMap;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CRPHeart24Data * _Nonnull sharedInstance;)
+ (CRPHeart24Data * _Nonnull)sharedInstance SWIFT_WARN_UNUSED_RESULT;
- (void)save:(NSArray<NSNumber *> * _Nonnull)bytes;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


SWIFT_CLASS("_TtC12CRPSmartRing13CRPHeartModel")
@interface CRPHeartModel : NSObject
@property (nonatomic) double starttime;
@property (nonatomic) double endtime;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull detail;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithStarttime:(double)starttime endtime:(double)endtime detail:(NSArray<NSNumber *> * _Nonnull)detail originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing19CRPHeartRecordModel")
@interface CRPHeartRecordModel : NSObject
@property (nonatomic) NSInteger value;
@property (nonatomic) NSInteger time;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithValue:(NSInteger)value time:(NSInteger)time originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

enum CRPState : NSInteger;
@class CRPStepModel;
@class CRPUserInfoModel;
@class CRPSportModel;
@class CRPO2RecordModel;
@class CRPNewTrainingModel;
@class CRPTrainingGoalsModel;
@class CRPTrainingGoalStateModel;
@class CRPTrainingRecordModel;
@class CRPSleepRecordModel;
@class CRPTimingHRRecordModel;
@class CRPTimingHRVRecordModel;
@class CRPTimingO2RecordModel;
@class CRPTimingStepsRecordModel;
enum CRPTrainingType : NSInteger;
enum CRPTrainingState : NSInteger;
@class CRPTimingtemperatureStateModel;
@class CRPSleepTemperatureRecord;
@class CRPTimingTemperatureDataModel;
enum CRPMeasurementState : NSInteger;
@class CRPStressRecordModel;
@class CRPMeditationModel;
enum CRPMeditationGetState : NSInteger;
@class CRPRingAlarmModel;
@class CRPTimingStressRecordModel;

SWIFT_CLASS("_TtC12CRPSmartRing18CRPManagerHandlers")
@interface CRPManagerHandlers : NSObject
@property (nonatomic, copy) void (^ _Null_unspecified removeHandler)(enum CRPState, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified batteryHandlers)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified heart24IntervalHandlers)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified upgradeTypeHandlers)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified macHandlers)(NSString * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified softverHandlets)(NSString * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified stepHandler)(CRPStepModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified profileHandler)(CRPUserInfoModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified sportHandler)(NSArray<CRPSportModel *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified isupgradeHandler)(NSString * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified connectedPeripherialHandler)(NSArray<CBPeripheral *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified sitRemindHandler)(CRPActivityReminderModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified hrRemindHandler)(CRPHRRemind * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified autoO2IntervalHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified hrvIntervalHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified fullDayO2StatusHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified heartRecordDataHandler)(NSArray<CRPHeartRecordModel *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified bpRecordDataHandler)(NSArray<CRPBPRecordModel *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified o2RecordDataHandler)(NSArray<CRPO2RecordModel *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified sportDetailHandler)(CRPNewTrainingModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified hrvRecordDataHandler)(NSArray<CRPHRVRecordModel *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified normalExerciseGoalHandler)(CRPTrainingGoalsModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified exerciseDayGoalHandler)(CRPTrainingGoalStateModel * _Nonnull, CRPTrainingGoalsModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified exerciseRecordHandler)(CRPTrainingRecordModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified sleepRecordHandle)(CRPSleepRecordModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified fullDayHRRecordHandler)(CRPTimingHRRecordModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified fullDayHRVRecordHandler)(CRPTimingHRVRecordModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified fullDayO2RecordHandler)(CRPTimingO2RecordModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified fullDayStepsRecordHandler)(CRPTimingStepsRecordModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified fullDayActivitysRecordHandler)(CRPContinueActivitysRecordModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified gitHashHandler)(NSString * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified gsensorHandler)(CRPGsensorInfoModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified batteryInfoHandler)(CRPBatteryInfoModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified sitRemindStateHandler)(CRPActivityReminderStateModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified trainingStateHandler)(enum CRPTrainingType, enum CRPTrainingState, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified timingTemperatureStateHandler)(CRPTimingtemperatureStateModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified sleepTemperatureRecordListHandler)(NSArray<CRPSleepTemperatureRecord *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified sleepTemperatureDataHandler)(CRPTimingTemperatureDataModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified measurementStateHandler)(enum CRPMeasurementState, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified rebootTimeHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified ringInfnHandler)(CRPRingInfoModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified stressRecordDataHandler)(NSArray<CRPStressRecordModel *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified notificationsSupportHandler)(NSArray<NSNumber *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified notificationsStateHanlder)(NSArray<NSNumber *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified sosStateHandler)(BOOL, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified stepsPerMinHandler)(NSArray<NSNumber *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified pageTurnModeHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified meditationHandler)(CRPMeditationModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified meditationStateHandler)(enum CRPMeditationGetState, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified gomoreAlgorithmSupportHandler)(CRPGoMoreSupportModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified gomoreKeySupportHandler)(BOOL, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified gomoreChipIDHandler)(NSString * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified gomoreKeyWriteResult)(BOOL, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified goMoreSleepDataHandler)(CRPGoMoreSleepDataModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified goMoreSleepRecordHandler)(CRPGoMoreSleepRecordModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified goMoreSleepTypeHandler)(CRPGoMoreSleepType * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified terminationSleepHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified goMoreTrainingRecognitionDetailHandler)(NSInteger, CRPGoMoreTrainingModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified sleepO2StateHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified shutDownHandler)(BOOL, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified takePhotoStateHandler)(BOOL, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified transmitPowerHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified bootLoadModeHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified autoLockTimeHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified screenBrightnessHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified displaySupportType)(NSArray<NSNumber *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified displayContent)(NSArray<NSNumber *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified resetHandler)(BOOL, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified alarmHandler)(NSArray<CRPRingAlarmModel *> * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified vibrationStrengthHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified activationStateHandler)(CRPActivationStateModel * _Nonnull, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified activationClearStateHandler)(BOOL, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified fullDayStressStatusHandler)(NSInteger, enum CRPError);
@property (nonatomic, copy) void (^ _Null_unspecified fullDayStressRecordHandler)(CRPTimingStressRecordModel * _Nonnull, enum CRPError);
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class CommandPacket;
@class CRPSleepModel;
@protocol CRPManagerDelegate;
enum CRPTrainingGoalType : NSInteger;
@class CRPSleepDetailModel;
enum CRPTouchType : NSInteger;

SWIFT_CLASS("_TtC12CRPSmartRing10CRPManager")
@interface CRPManager : CRPManagerHandlers <BleProtocol>
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CRPManager * _Nonnull sharedInstance;)
+ (CRPManager * _Nonnull)sharedInstance SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, readonly, copy) NSString * _Nonnull batteryCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull sportCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull commandCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull transmitCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull softVerCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull heartRateCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull macCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull enMacCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull upgradeCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull isUpgradeCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull uiCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull newVersionCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull ecgCharacteristicUUID;
@property (nonatomic, readonly, copy) NSString * _Nonnull newECGCharacteristicUUID;
@property (nonatomic, strong) CommandPacket * _Null_unspecified commandPacket;
@property (nonatomic, strong) CBPeripheral * _Null_unspecified peripheral;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified sportCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified batteryCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified transmitCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified commandCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified softVerCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified macAddressCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified heartRateCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified upgradeCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified isUpgradeCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified ecgCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified newECGCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified uiCharacteristic;
@property (nonatomic, strong) CBCharacteristic * _Null_unspecified newVersionCharacteristic;
@property (nonatomic) NSInteger battery;
@property (nonatomic, copy) NSString * _Nonnull ver;
@property (nonatomic, copy) NSArray<CRPStepModel *> * _Nonnull allSteps;
@property (nonatomic, copy) NSArray<CRPSleepModel *> * _Nonnull allSleeps;
@property (nonatomic, copy) NSArray<CRPSleepModel *> * _Nonnull allREMSleeps;
@property (nonatomic, strong) dispatch_queue_t _Nonnull wirteQueue;
/// 当前bootloader模式的状态 0：正常模式；1:bootloader模式
@property (nonatomic) NSInteger bootLoaderMode;
@property (nonatomic) NSInteger pkgSize;
- (void)connectTimerElapsed;
@property (nonatomic, strong) id <CRPManagerDelegate> _Null_unspecified delegate;
@property (nonatomic) enum CRPState state;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (void)connect:(CRPDiscovery * _Nonnull)discovery;
- (void)retrieveConnectWithIdentifier:(NSString * _Nonnull)identifier mac:(NSString * _Nonnull)mac;
- (void)bindedPeripheral:(CRPDiscovery * _Nonnull)discovery;
- (void)unbind;
- (void)checkIsBind;
- (void)disConnect;
- (void)readPedometer;
- (void)readBattery;
- (void)readMacAddress;
- (void)readSoftver;
- (void)readIsUpgrade;
- (void)sendProfile:(NSArray<NSNumber *> * _Nonnull)profile;
- (void)readProfile;
- (void)sendTime;
- (void)readTime;
- (void)sendNormalExerciseGoalWithGoals:(CRPTrainingGoalsModel * _Nonnull)goals;
- (void)readNormalExerciseGoal;
- (void)setExerciseDayGoalWithModel:(CRPTrainingGoalStateModel * _Nonnull)model goals:(CRPTrainingGoalsModel * _Nonnull)goals;
- (void)readExerciseDayGoal;
- (void)sendSitRemindInfoWithModel:(CRPActivityReminderModel * _Nonnull)model;
- (void)readSitRemindInfo;
- (void)sendHeartRateRemind:(NSArray<NSNumber *> * _Nonnull)cmd;
- (void)readHeartRateRemind;
- (void)send24HourHearts:(NSInteger)interval;
- (void)read24HourHeartsInterval;
- (void)sendFullDayHRV:(NSInteger)interval;
- (void)readFullDayHRVInterval;
- (void)sendFullDayO2Interval:(NSInteger)interval;
- (void)readFullDayO2Interval;
- (void)sendSingleHeartRateWithType:(NSInteger)type;
- (void)readHeartRecord;
- (void)sendSingleHRVWithType:(NSInteger)type;
- (void)readHRVRecord;
- (void)sendSingleO2WithType:(NSInteger)type;
- (void)readO2Record;
- (void)sendSingleStressWithType:(NSInteger)type;
- (void)readStressRecord;
- (void)sendExercise:(NSInteger)state type:(enum CRPTrainingGoalType)type goal:(NSInteger)goal;
- (void)readTrainingState;
- (void)sendFullDayTemperatureIntervalWithInterval:(NSInteger)interval;
- (void)readFullDayTemperatureState;
- (void)readExerciseList;
- (void)readStepDataWithDay:(NSInteger)day;
- (void)readSleepDataWithDay:(NSInteger)day;
- (void)readFullDayHeartsWithDay:(NSInteger)day i:(NSInteger)i;
- (void)readFullDayHRVWithDay:(NSInteger)day i:(NSInteger)i;
- (void)readFullDayO2WithDay:(NSInteger)day i:(NSInteger)i;
- (void)readStepArchiveDataWithDay:(NSInteger)day;
- (void)readActivityArchiveDataWithDay:(NSInteger)day i:(NSInteger)i;
- (void)readSportRecordList;
- (void)readSportRecordDataWithId:(NSInteger)id;
- (void)readSportRecordHRWithId:(NSInteger)id offset:(NSInteger)offset;
- (void)readSleepTemperatureRecordWithDay:(NSInteger)day i:(NSInteger)i;
- (void)readMeasurementState;
- (void)readSportState;
- (void)reset;
- (void)shutDown;
- (void)readManufacturerNameByCommand;
- (void)readSoftVerByCommand;
- (void)readGitHash;
- (void)readGsensor;
- (void)readBatteryInfo;
- (void)readWearState;
- (void)setExerciseDataWithStep:(NSInteger)step cal:(NSInteger)cal exerciseTime:(NSInteger)exerciseTime distance:(NSInteger)distance;
- (void)sendGoalReminderWithType:(enum CRPTrainingGoalType)type;
- (void)sendGoalReminderWithType:(enum CRPTrainingGoalType)type state:(NSInteger)state;
- (void)sendActivityReminder;
- (void)sendSleepDataWithData:(NSArray<CRPSleepDetailModel *> * _Nonnull)data;
- (void)sendTrainingGoalReachWithType:(enum CRPTrainingGoalType)type;
- (void)sendTrainingGoalReachWithType:(enum CRPTrainingGoalType)type state:(NSInteger)state;
- (void)getRebootTime;
- (void)getReboot;
- (void)checkUpgradeType;
- (void)sendDFUUpgrade:(NSArray<NSNumber *> * _Nonnull)cmd;
- (void)sendDIYUpgrade:(NSArray<NSNumber *> * _Nonnull)cmd;
- (void)checkBootLoadSupport;
- (void)sendBootLoadMode;
- (void)getRingInfo;
- (void)setRingInfoWithInfo:(CRPRingInfoModel * _Nonnull)info;
- (void)setTouchSwtichWithOpen:(BOOL)open;
- (void)setTouchTypeWithType:(enum CRPTouchType)type;
- (void)setTouchSizeWithWidth:(NSInteger)width height:(NSInteger)height;
- (void)setPair;
- (void)getTouchType;
- (void)getPairState;
- (void)clearPairInfo;
- (void)sendNotificationState:(NSArray<NSNumber *> * _Nonnull)states;
/// 获取消息推送的开关支持类型
- (void)readNotificationSupportType;
- (void)readNotificationState;
- (void)sendSOSSwitchWithOpen:(BOOL)open;
- (void)readSOSSwitch;
- (void)readStepsPerMinuteWithHour:(NSInteger)hour;
- (void)sendpageTurnModeWithMode:(NSInteger)mode;
- (void)readpageTurnMode;
- (void)sendMeditation:(NSInteger)state min:(NSInteger)min;
- (void)readMeditationState;
- (void)readSleepO2State;
- (void)readMeditationRecordList;
- (void)readMeditationRecordDataWithId:(NSInteger)id;
- (void)readMeditationRecordHRWithId:(NSInteger)id offset:(NSInteger)offset;
- (void)readGoMoreIsSupport;
- (void)readGoMoreKeyIsSupport;
- (void)readGoMoreChipID;
- (void)senfGoMoreWriteKeyWithKey:(NSString * _Nonnull)key;
- (void)readGoMoreSleepDataList;
- (void)readGoMoreSleepDataDetailWithId:(NSInteger)id;
- (void)readGoMoreSleepSegmentationDataWithId:(NSInteger)id;
- (void)readGoMoreSleepType;
- (void)readGoMoreSleepGenerateType;
- (void)sendGoMoreSleepGenerateWithStartTime:(NSInteger)startTime sleepTime:(NSInteger)sleepTime awakeTime:(NSInteger)awakeTime;
- (void)sendTerminationSleep;
- (void)readGoMoreExerciseRecognitionList;
- (void)readGoMoreExerciseRecognitionDataDetailWithId:(NSInteger)id;
- (void)sendGoMoreTestExerciseRecognitionDataDetailWithTrainingInfo:(CRPGoMoreTrainingModel * _Nonnull)trainingInfo;
- (void)sendSingleBPWithState:(NSInteger)state;
- (void)sendSingleTemperatureWithState:(NSInteger)state;
- (void)sendFirstConnect;
- (void)sendShakeToTakePhotoWithOpen:(BOOL)open;
- (void)readShakeToTakePhoto;
- (void)sendTransmitPowerWithLevel:(NSInteger)level;
- (void)readTransmitPower;
- (void)sendKnockSwitchWithOpen:(BOOL)open;
- (void)readKnockSwitch;
- (void)sendAutoLockTimeWithTime:(NSInteger)time;
- (void)readAutoLockTime;
- (void)sendScreenBrightnessWithTime:(NSInteger)time;
- (void)readScreenBrightness;
- (void)sendDisplayContentWithTypes:(NSArray<NSNumber *> * _Nonnull)types;
- (void)readDisplaySupportInfo;
- (void)readDisplayContent;
- (void)sendVibrationStrengthWithType:(NSInteger)type;
- (void)readVibrationStrength;
- (void)sendSetUpAlarm:(NSArray<NSNumber *> * _Nonnull)alarm;
- (void)sendDeleteAlarm:(NSInteger)id;
- (void)sendDeleteAllAlarm;
- (void)readAllAlarm;
- (void)sendFindDeviceWithState:(NSInteger)state;
- (void)sendActivationCodeWithState:(enum CRPActivationState)state code:(NSInteger)code;
- (void)readActivationState;
- (void)clearActivationCode;
- (void)sendFullDayStressInterval:(NSInteger)interval;
- (void)readFullDayStressInterval;
- (void)readFullDayStressWithDay:(NSInteger)day i:(NSInteger)i;
- (void)addProtocolWithCmd:(NSArray<NSNumber *> * _Nonnull)cmd :(NSArray<NSNumber *> * _Nonnull)header;
- (void)stateChange:(CBCentralManager * _Nonnull)central;
- (void)didDiscoverCharacteristics:(CBPeripheral * _Nonnull)peripheral characteristic:(CBCharacteristic * _Nonnull)characteristic;
- (void)didUpdateValueForCharacteristic:(CBCharacteristic * _Nonnull)characteristic;
- (void)parseCommandPacket:(CommandPacket * _Nonnull)commandPacket;
- (void)didConnected;
- (void)didConnect;
- (void)didSyncing;
- (void)didDisconnect;
- (void)reConnect;
- (BOOL)isConnected SWIFT_WARN_UNUSED_RESULT;
- (void)writeCRPUpgrade:(NSData * _Nonnull)data;
- (void)writeCRPScreen:(NSData * _Nonnull)data;
- (void)writeData:(NSData * _Nonnull)data;
- (void)getProtocolVerWithVerStr:(NSString * _Nonnull)verStr;
- (NSArray<NSNumber *> * _Nonnull)modifyToV2WithData:(NSArray<NSNumber *> * _Nonnull)data :(NSInteger)length SWIFT_WARN_UNUSED_RESULT;
- (void)checkUpgradeTypeWithHandler:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (NSString * _Nonnull)getVerNumber SWIFT_WARN_UNUSED_RESULT;
- (NSArray<NSNumber *> * _Nonnull)getLittleEndianCountWithCount:(NSInteger)count byteCount:(NSInteger)byteCount SWIFT_WARN_UNUSED_RESULT;
@end

enum CRPOTAState : NSInteger;
@class CRPTrainingRecord;
@class CRPTouchModel;
enum CRPPairState : NSInteger;
@class CRPMeditationRecord;
enum CRPRestoreState : NSInteger;

SWIFT_PROTOCOL("_TtP12CRPSmartRing18CRPManagerDelegate_")
@protocol CRPManagerDelegate
- (void)didState:(enum CRPState)state;
- (void)didBluetoothState:(enum CRPBluetoothState)state;
- (void)receiveSteps:(CRPStepModel * _Nonnull)model;
- (void)receiveHeartRate:(NSInteger)heartRate;
- (void)receiveRealTimeHeartRate:(NSInteger)heartRate;
- (void)receiveHRV:(NSInteger)hrv;
- (void)receiveSpO2:(NSInteger)o2;
- (void)receiveOTA:(enum CRPOTAState)state :(NSInteger)progress;
- (void)receiveStress:(NSInteger)stress;
@optional
- (void)receiveTrainingList:(NSArray<CRPTrainingRecord *> * _Nonnull)list;
- (void)receiveTrainingState:(enum CRPTrainingType)state;
- (void)receiveWearState:(NSInteger)state;
- (void)receiveDailyGoal:(enum CRPTrainingGoalType)type state:(NSInteger)state;
- (void)receiveActivityReminderWithIsReminder:(NSInteger)isReminder wearState:(NSInteger)wearState time:(NSInteger)time step:(NSInteger)step;
- (void)receiveTrainingGoal:(enum CRPTrainingGoalType)type state:(NSInteger)state;
- (void)receiveTouchType:(CRPTouchModel * _Nonnull)model;
- (void)receivePairState:(enum CRPPairState)state;
- (void)receiveSOS;
- (void)receiveMeditationList:(NSArray<CRPMeditationRecord *> * _Nonnull)list;
- (void)receiveMeditationState:(enum CRPMeditationGetState)state;
- (void)receiveSleepList:(NSArray<CRPGoMoreSleepRecord *> * _Nonnull)list;
- (void)receiveTrainingRecognition:(CRPGoMoreTrainingModel * _Nonnull)trainingInfo;
- (void)receiveTrainingRecognitionList:(NSArray<CRPGoMoreTrainingRecord *> * _Nonnull)list;
- (void)receiveBloodPressure:(NSInteger)sbp :(NSInteger)dbp;
- (void)receiveTemperature:(double)value;
- (void)recevieTakePhoto;
- (void)receiveKnockSwitch:(CRPTouchModel * _Nonnull)model;
- (void)goodixDidState:(enum CRPState)state;
- (void)goodixRestoreState:(enum CRPRestoreState)state;
@end


typedef SWIFT_ENUM(NSInteger, CRPMeasurementState, open) {
  CRPMeasurementStateNotMeasured = 0,
  CRPMeasurementStateRespirationHR = 1,
  CRPMeasurementStateTrainingHR = 2,
  CRPMeasurementStateStaticHR = 3,
  CRPMeasurementStateBp = 4,
  CRPMeasurementStateSpO2 = 5,
  CRPMeasurementStateEcg = 6,
  CRPMeasurementStateTemperature = 7,
  CRPMeasurementStateHrv = 8,
  CRPMeasurementStateStress = 0x09,
  CRPMeasurementStateCombination = 0x0A,
  CRPMeasurementStateFactoryTest = 0x7F,
};

typedef SWIFT_ENUM(NSInteger, CRPMeditationGetState, open) {
  CRPMeditationGetStateUnstarted = 0,
  CRPMeditationGetStateMeditating = 1,
  CRPMeditationGetStatePause = 2,
};


SWIFT_CLASS("_TtC12CRPSmartRing18CRPMeditationModel")
@interface CRPMeditationModel : NSObject
@property (nonatomic, readonly, copy) NSString * _Nonnull date;
@property (nonatomic, readonly) NSInteger startTime;
@property (nonatomic, readonly) NSInteger endTime;
@property (nonatomic, readonly) NSInteger vaildTime;
@property (nonatomic, readonly) NSInteger hrAvg;
@property (nonatomic, readonly) NSInteger hrAddress;
@property (nonatomic, readonly) NSInteger hrLength;
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull heartRate;
@property (nonatomic, readonly) NSInteger originalStartTime;
- (nonnull instancetype)initWithDate:(NSString * _Nonnull)date startTime:(NSInteger)startTime endTime:(NSInteger)endTime vaildTime:(NSInteger)vaildTime hrAvg:(NSInteger)hrAvg hrAddress:(NSInteger)hrAddress hrLength:(NSInteger)hrLength heartRate:(NSArray<NSNumber *> * _Nonnull)heartRate originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing19CRPMeditationRecord")
@interface CRPMeditationRecord : NSObject
@property (nonatomic) NSInteger id;
@property (nonatomic) NSInteger startTime;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithId:(NSInteger)id startTime:(NSInteger)startTime originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPMeditationSetState, open) {
  CRPMeditationSetStateStart = 0xFC,
  CRPMeditationSetStateProceed = 0xFD,
  CRPMeditationSetStatePause = 0xFE,
  CRPMeditationSetStateEnd = 0xFF,
};


SWIFT_CLASS("_TtC12CRPSmartRing19CRPNewTrainingModel")
@interface CRPNewTrainingModel : NSObject
@property (nonatomic, readonly) NSInteger id;
@property (nonatomic, readonly) NSInteger startTime;
@property (nonatomic, readonly) NSInteger endTime;
@property (nonatomic, readonly) NSInteger vaildTime;
@property (nonatomic, readonly) NSInteger hrAvg;
@property (nonatomic, readonly) enum CRPTrainingType type;
@property (nonatomic, readonly) NSInteger step;
@property (nonatomic, readonly) NSInteger distance;
@property (nonatomic, readonly) NSInteger kcal;
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull heartRate;
@property (nonatomic, readonly) enum CRPTrainingGoalType goalType;
@property (nonatomic, readonly) NSInteger goal;
@property (nonatomic, readonly) NSInteger hrAddress;
@property (nonatomic, readonly) NSInteger hrLength;
@property (nonatomic, readonly) NSInteger originalStartTime;
- (nonnull instancetype)initWithId:(NSInteger)id startTime:(NSInteger)startTime endTime:(NSInteger)endTime vaildTime:(NSInteger)vaildTime hrAvg:(NSInteger)hrAvg type:(enum CRPTrainingType)type step:(NSInteger)step distance:(NSInteger)distance kcal:(NSInteger)kcal heartRate:(NSArray<NSNumber *> * _Nonnull)heartRate goalType:(enum CRPTrainingGoalType)goalType goal:(NSInteger)goal hrAddress:(NSInteger)hrAddress hrLength:(NSInteger)hrLength originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

enum CRPOTAType : NSInteger;

SWIFT_CLASS("_TtC12CRPSmartRing17CRPNewVersionInfo")
@interface CRPNewVersionInfo : NSObject
@property (nonatomic, copy) NSString * _Nullable version;
@property (nonatomic, copy) NSString * _Nullable log;
@property (nonatomic, copy) NSString * _Nullable logEN;
@property (nonatomic) NSInteger mcu;
@property (nonatomic, copy) NSString * _Nullable md5;
@property (nonatomic, copy) NSString * _Nullable fileUrl;
- (nonnull instancetype)initWithType:(enum CRPOTAType)type version:(NSString * _Nonnull)version log:(NSString * _Nonnull)log logEN:(NSString * _Nonnull)logEN mcu:(NSInteger)mcu md5:(NSString * _Nonnull)md5 fileUrl:(NSString * _Nonnull)fileUrl OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPNotificationType, open) {
  CRPNotificationTypePhone = 0,
  CRPNotificationTypeMessages = 1,
  CRPNotificationTypeWechat = 2,
  CRPNotificationTypeQq = 3,
  CRPNotificationTypeFacebook = 4,
  CRPNotificationTypeTwitter = 5,
  CRPNotificationTypeInstagram = 6,
  CRPNotificationTypeSkype = 7,
  CRPNotificationTypeWhatsApp = 8,
  CRPNotificationTypeLine = 9,
  CRPNotificationTypeKakaoTalk = 10,
  CRPNotificationTypeEmail = 11,
  CRPNotificationTypeMessenger = 12,
  CRPNotificationTypeZalo = 13,
  CRPNotificationTypeTelegram = 14,
  CRPNotificationTypeViber = 15,
  CRPNotificationTypeNateOn = 16,
  CRPNotificationTypeGmail = 17,
  CRPNotificationTypeCalenda = 18,
  CRPNotificationTypeDailyHunt = 19,
  CRPNotificationTypeOutlook = 20,
  CRPNotificationTypeYahoo = 21,
  CRPNotificationTypeInshorts = 22,
  CRPNotificationTypePhonepe = 23,
  CRPNotificationTypeGpay = 24,
  CRPNotificationTypePaytm = 25,
  CRPNotificationTypeSwiggy = 26,
  CRPNotificationTypeZomato = 27,
  CRPNotificationTypeUber = 28,
  CRPNotificationTypeOla = 29,
  CRPNotificationTypeReflexApp = 30,
  CRPNotificationTypeSnapchat = 31,
  CRPNotificationTypeYtMusic = 32,
  CRPNotificationTypeYouTube = 33,
  CRPNotificationTypeLinkEdin = 34,
  CRPNotificationTypeAmazon = 35,
  CRPNotificationTypeFlipkart = 36,
  CRPNotificationTypeNetFlix = 37,
  CRPNotificationTypeHotstar = 38,
  CRPNotificationTypeAmazonPrime = 39,
  CRPNotificationTypeGoogleChat = 40,
  CRPNotificationTypeWynk = 41,
  CRPNotificationTypeGoogleDrive = 42,
  CRPNotificationTypeDunzo = 43,
  CRPNotificationTypeGaana = 44,
  CRPNotificationTypeMissCall = 45,
  CRPNotificationTypeWhatsAppBusiness = 46,
  CRPNotificationTypeOthers = 0x80,
};


SWIFT_CLASS("_TtC12CRPSmartRing16CRPO2RecordModel")
@interface CRPO2RecordModel : NSObject
@property (nonatomic) NSInteger value;
@property (nonatomic) NSInteger time;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithValue:(NSInteger)value time:(NSInteger)time originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPOTAState, open) {
  CRPOTAStateUploading = 0,
  CRPOTAStateCompleted = 1,
  CRPOTAStateFailed = 2,
};

typedef SWIFT_ENUM(NSInteger, CRPOTAType, open) {
  CRPOTATypeNormal = 0,
  CRPOTATypeBate = 1,
  CRPOTATypeForced = 2,
};

typedef SWIFT_ENUM(NSInteger, CRPPairState, open) {
  CRPPairStateUnPair = 0,
  CRPPairStateStartPair = 1,
  CRPPairStatePairSuccess = 2,
  CRPPairStatePairFail = 3,
};

typedef SWIFT_ENUM(NSInteger, CRPRestoreState, open) {
  CRPRestoreStateRepairing = 0,
  CRPRestoreStateCompleted = 1,
  CRPRestoreStateFailed = 2,
};

enum CRPRingAlarmType : NSInteger;

SWIFT_CLASS("_TtC12CRPSmartRing17CRPRingAlarmModel")
@interface CRPRingAlarmModel : NSObject
@property (nonatomic) NSInteger id;
@property (nonatomic) NSInteger enable;
@property (nonatomic) enum CRPRingAlarmType type;
@property (nonatomic) NSInteger hour;
@property (nonatomic) NSInteger minute;
@property (nonatomic) NSInteger year;
@property (nonatomic) NSInteger month;
@property (nonatomic) NSInteger day;
/// weekday的值参考CRPWeekday
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull weekday;
- (nonnull instancetype)initWithId:(NSInteger)id enable:(NSInteger)enable type:(enum CRPRingAlarmType)type hour:(NSInteger)hour minute:(NSInteger)minute year:(NSInteger)year month:(NSInteger)month day:(NSInteger)day weekday:(NSArray<NSNumber *> * _Nonnull)weekday OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPRingAlarmType, open) {
  CRPRingAlarmTypeOnce = 0,
  CRPRingAlarmTypeEveryday = 1,
  CRPRingAlarmTypeWeekly = 2,
};


SWIFT_CLASS("_TtC12CRPSmartRing16CRPRingInfoModel")
@interface CRPRingInfoModel : NSObject
@property (nonatomic) NSInteger color;
@property (nonatomic) NSInteger size;
@property (nonatomic) NSInteger type;
- (nonnull instancetype)initWithColor:(NSInteger)color size:(NSInteger)size type:(NSInteger)type OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


/// Responsible for handling all delegate callbacks for the underlying session.
SWIFT_CLASS("_TtC12CRPSmartRing18CRPSessionDelegate")
@interface CRPSessionDelegate : NSObject
/// Initializes the <code>SessionDelegate</code> instance.
///
/// returns:
/// The new <code>SessionDelegate</code> instance.
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// Returns a <code>Bool</code> indicating whether the <code>SessionDelegate</code> implements or inherits a method that can respond
/// to a specified message.
/// \param selector A selector that identifies a message.
///
///
/// returns:
/// <code>true</code> if the receiver implements or inherits a method that can respond to selector, otherwise <code>false</code>.
- (BOOL)respondsToSelector:(SEL _Nonnull)selector SWIFT_WARN_UNUSED_RESULT;
@end


@interface CRPSessionDelegate (SWIFT_EXTENSION(CRPSmartRing)) <NSURLSessionDownloadDelegate>
/// Tells the delegate that a download task has finished downloading.
/// \param session The session containing the download task that finished.
///
/// \param downloadTask The download task that finished.
///
/// \param location A file URL for the temporary file. Because the file is temporary, you must either
/// open the file for reading or move it to a permanent location in your app’s sandbox
/// container directory before returning from this delegate method.
///
- (void)URLSession:(NSURLSession * _Nonnull)session downloadTask:(NSURLSessionDownloadTask * _Nonnull)downloadTask didFinishDownloadingToURL:(NSURL * _Nonnull)location;
/// Periodically informs the delegate about the download’s progress.
/// \param session The session containing the download task.
///
/// \param downloadTask The download task.
///
/// \param bytesWritten The number of bytes transferred since the last time this delegate
/// method was called.
///
/// \param totalBytesWritten The total number of bytes transferred so far.
///
/// \param totalBytesExpectedToWrite The expected length of the file, as provided by the Content-Length
/// header. If this header was not provided, the value is
/// <code>NSURLSessionTransferSizeUnknown</code>.
///
- (void)URLSession:(NSURLSession * _Nonnull)session downloadTask:(NSURLSessionDownloadTask * _Nonnull)downloadTask didWriteData:(int64_t)bytesWritten totalBytesWritten:(int64_t)totalBytesWritten totalBytesExpectedToWrite:(int64_t)totalBytesExpectedToWrite;
/// Tells the delegate that the download task has resumed downloading.
/// \param session The session containing the download task that finished.
///
/// \param downloadTask The download task that resumed. See explanation in the discussion.
///
/// \param fileOffset If the file’s cache policy or last modified date prevents reuse of the
/// existing content, then this value is zero. Otherwise, this value is an
/// integer representing the number of bytes on disk that do not need to be
/// retrieved again.
///
/// \param expectedTotalBytes The expected length of the file, as provided by the Content-Length header.
/// If this header was not provided, the value is NSURLSessionTransferSizeUnknown.
///
- (void)URLSession:(NSURLSession * _Nonnull)session downloadTask:(NSURLSessionDownloadTask * _Nonnull)downloadTask didResumeAtOffset:(int64_t)fileOffset expectedTotalBytes:(int64_t)expectedTotalBytes;
@end

@class NSURLSessionStreamTask;
@class NSOutputStream;

SWIFT_AVAILABILITY(tvos,introduced=9.0) SWIFT_AVAILABILITY(macos,introduced=10.11) SWIFT_AVAILABILITY(ios,introduced=9.0)
@interface CRPSessionDelegate (SWIFT_EXTENSION(CRPSmartRing)) <NSURLSessionStreamDelegate>
/// Tells the delegate that the read side of the connection has been closed.
/// \param session The session.
///
/// \param streamTask The stream task.
///
- (void)URLSession:(NSURLSession * _Nonnull)session readClosedForStreamTask:(NSURLSessionStreamTask * _Nonnull)streamTask;
/// Tells the delegate that the write side of the connection has been closed.
/// \param session The session.
///
/// \param streamTask The stream task.
///
- (void)URLSession:(NSURLSession * _Nonnull)session writeClosedForStreamTask:(NSURLSessionStreamTask * _Nonnull)streamTask;
/// Tells the delegate that the system has determined that a better route to the host is available.
/// \param session The session.
///
/// \param streamTask The stream task.
///
- (void)URLSession:(NSURLSession * _Nonnull)session betterRouteDiscoveredForStreamTask:(NSURLSessionStreamTask * _Nonnull)streamTask;
/// Tells the delegate that the stream task has been completed and provides the unopened stream objects.
/// \param session The session.
///
/// \param streamTask The stream task.
///
/// \param inputStream The new input stream.
///
/// \param outputStream The new output stream.
///
- (void)URLSession:(NSURLSession * _Nonnull)session streamTask:(NSURLSessionStreamTask * _Nonnull)streamTask didBecomeInputStream:(NSInputStream * _Nonnull)inputStream outputStream:(NSOutputStream * _Nonnull)outputStream;
@end


@interface CRPSessionDelegate (SWIFT_EXTENSION(CRPSmartRing)) <NSURLSessionDataDelegate>
/// Tells the delegate that the data task received the initial reply (headers) from the server.
/// \param session The session containing the data task that received an initial reply.
///
/// \param dataTask The data task that received an initial reply.
///
/// \param response A URL response object populated with headers.
///
/// \param completionHandler A completion handler that your code calls to continue the transfer, passing a
/// constant to indicate whether the transfer should continue as a data task or
/// should become a download task.
///
- (void)URLSession:(NSURLSession * _Nonnull)session dataTask:(NSURLSessionDataTask * _Nonnull)dataTask didReceiveResponse:(NSURLResponse * _Nonnull)response completionHandler:(void (^ _Nonnull)(NSURLSessionResponseDisposition))completionHandler;
/// Tells the delegate that the data task was changed to a download task.
/// \param session The session containing the task that was replaced by a download task.
///
/// \param dataTask The data task that was replaced by a download task.
///
/// \param downloadTask The new download task that replaced the data task.
///
- (void)URLSession:(NSURLSession * _Nonnull)session dataTask:(NSURLSessionDataTask * _Nonnull)dataTask didBecomeDownloadTask:(NSURLSessionDownloadTask * _Nonnull)downloadTask;
/// Tells the delegate that the data task has received some of the expected data.
/// \param session The session containing the data task that provided data.
///
/// \param dataTask The data task that provided data.
///
/// \param data A data object containing the transferred data.
///
- (void)URLSession:(NSURLSession * _Nonnull)session dataTask:(NSURLSessionDataTask * _Nonnull)dataTask didReceiveData:(NSData * _Nonnull)data;
/// Asks the delegate whether the data (or upload) task should store the response in the cache.
/// \param session The session containing the data (or upload) task.
///
/// \param dataTask The data (or upload) task.
///
/// \param proposedResponse The default caching behavior. This behavior is determined based on the current
/// caching policy and the values of certain received headers, such as the Pragma
/// and Cache-Control headers.
///
/// \param completionHandler A block that your handler must call, providing either the original proposed
/// response, a modified version of that response, or NULL to prevent caching the
/// response. If your delegate implements this method, it must call this completion
/// handler; otherwise, your app leaks memory.
///
- (void)URLSession:(NSURLSession * _Nonnull)session dataTask:(NSURLSessionDataTask * _Nonnull)dataTask willCacheResponse:(NSCachedURLResponse * _Nonnull)proposedResponse completionHandler:(void (^ _Nonnull)(NSCachedURLResponse * _Nullable))completionHandler;
@end


@interface CRPSessionDelegate (SWIFT_EXTENSION(CRPSmartRing)) <NSURLSessionDelegate>
/// Tells the delegate that the session has been invalidated.
/// \param session The session object that was invalidated.
///
/// \param error The error that caused invalidation, or nil if the invalidation was explicit.
///
- (void)URLSession:(NSURLSession * _Nonnull)session didBecomeInvalidWithError:(NSError * _Nullable)error;
/// Requests credentials from the delegate in response to a session-level authentication request from the
/// remote server.
/// \param session The session containing the task that requested authentication.
///
/// \param challenge An object that contains the request for authentication.
///
/// \param completionHandler A handler that your delegate method must call providing the disposition
/// and credential.
///
- (void)URLSession:(NSURLSession * _Nonnull)session didReceiveChallenge:(NSURLAuthenticationChallenge * _Nonnull)challenge completionHandler:(void (^ _Nonnull)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler;
/// Tells the delegate that all messages enqueued for a session have been delivered.
/// \param session The session that no longer has any outstanding requests.
///
- (void)URLSessionDidFinishEventsForBackgroundURLSession:(NSURLSession * _Nonnull)session;
@end

@class NSURLSessionTaskMetrics;

@interface CRPSessionDelegate (SWIFT_EXTENSION(CRPSmartRing)) <NSURLSessionTaskDelegate>
/// Tells the delegate that the remote server requested an HTTP redirect.
/// \param session The session containing the task whose request resulted in a redirect.
///
/// \param task The task whose request resulted in a redirect.
///
/// \param response An object containing the server’s response to the original request.
///
/// \param request A URL request object filled out with the new location.
///
/// \param completionHandler A closure that your handler should call with either the value of the request
/// parameter, a modified URL request object, or NULL to refuse the redirect and
/// return the body of the redirect response.
///
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task willPerformHTTPRedirection:(NSHTTPURLResponse * _Nonnull)response newRequest:(NSURLRequest * _Nonnull)request completionHandler:(void (^ _Nonnull)(NSURLRequest * _Nullable))completionHandler;
/// Requests credentials from the delegate in response to an authentication request from the remote server.
/// \param session The session containing the task whose request requires authentication.
///
/// \param task The task whose request requires authentication.
///
/// \param challenge An object that contains the request for authentication.
///
/// \param completionHandler A handler that your delegate method must call providing the disposition
/// and credential.
///
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task didReceiveChallenge:(NSURLAuthenticationChallenge * _Nonnull)challenge completionHandler:(void (^ _Nonnull)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler;
/// Tells the delegate when a task requires a new request body stream to send to the remote server.
/// \param session The session containing the task that needs a new body stream.
///
/// \param task The task that needs a new body stream.
///
/// \param completionHandler A completion handler that your delegate method should call with the new body stream.
///
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task needNewBodyStream:(void (^ _Nonnull)(NSInputStream * _Nullable))completionHandler;
/// Periodically informs the delegate of the progress of sending body content to the server.
/// \param session The session containing the data task.
///
/// \param task The data task.
///
/// \param bytesSent The number of bytes sent since the last time this delegate method was called.
///
/// \param totalBytesSent The total number of bytes sent so far.
///
/// \param totalBytesExpectedToSend The expected length of the body data.
///
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task didSendBodyData:(int64_t)bytesSent totalBytesSent:(int64_t)totalBytesSent totalBytesExpectedToSend:(int64_t)totalBytesExpectedToSend;
/// Tells the delegate that the session finished collecting metrics for the task.
/// \param session The session collecting the metrics.
///
/// \param task The task whose metrics have been collected.
///
/// \param metrics The collected metrics.
///
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task didFinishCollectingMetrics:(NSURLSessionTaskMetrics * _Nonnull)metrics SWIFT_AVAILABILITY(tvos,introduced=10.0) SWIFT_AVAILABILITY(macos,introduced=10.12) SWIFT_AVAILABILITY(ios,introduced=10.0);
/// Tells the delegate that the task finished transferring data.
/// \param session The session containing the task whose request finished transferring data.
///
/// \param task The task whose request finished transferring data.
///
/// \param error If an error occurred, an error object indicating how the transfer failed, otherwise nil.
///
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task didCompleteWithError:(NSError * _Nullable)error;
@end

@class NSMutableData;

SWIFT_CLASS("_TtC12CRPSmartRing12CRPSleepData")
@interface CRPSleepData : NSObject
@property (nonatomic, strong) NSMutableData * _Null_unspecified nsdata;
@property (nonatomic) NSInteger shallowSleep;
@property (nonatomic) NSInteger deepSleep;
@property (nonatomic) NSInteger REMSleep;
@property (nonatomic, copy) NSArray<NSDictionary<NSString *, NSString *> *> * _Nonnull parts;
@property (nonatomic, readonly) NSInteger REM_START_TIME;
@property (nonatomic, readonly) NSInteger REM_PERIOD;
- (nonnull instancetype)init:(NSMutableData * _Nonnull)nsdata isAgoSleep:(BOOL)isAgoSleep isREM:(BOOL)isREM OBJC_DESIGNATED_INITIALIZER;
- (NSInteger)formatSleepTimeWithCurrentHour:(NSInteger)currentHour currentMinute:(NSInteger)currentMinute nextHour:(NSInteger)nextHour nextMinute:(NSInteger)nextMinute SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)getREMTimeWithLast:(NSInteger)last total:(NSInteger)total SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

enum CRPSleepType : NSInteger;

SWIFT_CLASS("_TtC12CRPSmartRing19CRPSleepDetailModel")
@interface CRPSleepDetailModel : NSObject
@property (nonatomic) enum CRPSleepType type;
@property (nonatomic) NSInteger hour;
@property (nonatomic) NSInteger minute;
- (nonnull instancetype)initWithType:(enum CRPSleepType)type hour:(NSInteger)hour minute:(NSInteger)minute OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing13CRPSleepModel")
@interface CRPSleepModel : NSObject
@property (nonatomic) NSInteger deep;
@property (nonatomic) NSInteger light;
@property (nonatomic) NSInteger rem;
@property (nonatomic, copy) NSArray<NSDictionary<NSString *, NSString *> *> * _Nonnull detail;
- (nonnull instancetype)initWithDeep:(NSInteger)deep light:(NSInteger)light rem:(NSInteger)rem detail:(NSArray<NSDictionary<NSString *, NSString *> *> * _Nonnull)detail OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing19CRPSleepRecordModel")
@interface CRPSleepRecordModel : NSObject
@property (nonatomic) NSInteger day;
@property (nonatomic) NSInteger deep;
@property (nonatomic) NSInteger light;
@property (nonatomic) NSInteger rem;
@property (nonatomic, copy) NSArray<NSDictionary<NSString *, NSString *> *> * _Nonnull detail;
- (nonnull instancetype)initWithDay:(NSInteger)day deep:(NSInteger)deep light:(NSInteger)light rem:(NSInteger)rem detail:(NSArray<NSDictionary<NSString *, NSString *> *> * _Nonnull)detail OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing25CRPSleepTemperatureRecord")
@interface CRPSleepTemperatureRecord : NSObject
@property (nonatomic, readonly) NSInteger time;
@property (nonatomic, readonly) NSInteger address;
@property (nonatomic, readonly) NSInteger length;
- (nonnull instancetype)initWithTime:(NSInteger)time address:(NSInteger)address length:(NSInteger)length OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPSleepType, open) {
  CRPSleepTypeAwake = 0,
  CRPSleepTypeLight = 1,
  CRPSleepTypeDeep = 2,
  CRPSleepTypeRem = 3,
};


SWIFT_CLASS("_TtC12CRPSmartRing15CRPSmartRingSDK")
@interface CRPSmartRingSDK : NSObject <CRPManagerDelegate>
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CRPSmartRingSDK * _Nonnull sharedInstance;)
+ (CRPSmartRingSDK * _Nonnull)sharedInstance SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, strong) CRPManager * _Nonnull manager;
@property (nonatomic) BOOL showLog;
@property (nonatomic, strong) id <CRPManagerDelegate> _Nullable delegate;
@property (nonatomic, readonly, strong) CRPDiscovery * _Nullable currentCRPDiscovery;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (void)didState:(enum CRPState)state;
- (void)didBluetoothState:(enum CRPBluetoothState)state;
- (void)receiveSteps:(CRPStepModel * _Nonnull)model;
- (void)receiveHeartRate:(NSInteger)heartRate;
- (void)receiveSpO2:(NSInteger)o2;
- (void)receiveHRV:(NSInteger)hrv;
- (void)receiveStress:(NSInteger)stress;
- (void)receiveRealTimeHeartRate:(NSInteger)heartRate;
- (void)receiveOTA:(enum CRPOTAState)state :(NSInteger)progress;
- (void)receiveTrainingList:(NSArray<CRPTrainingRecord *> * _Nonnull)list;
- (void)receiveTrainingState:(enum CRPTrainingType)state;
- (void)receiveWearState:(NSInteger)state;
- (void)receiveDailyGoal:(enum CRPTrainingGoalType)type state:(NSInteger)state;
- (void)receiveActivityReminderWithIsReminder:(NSInteger)isReminder wearState:(NSInteger)wearState time:(NSInteger)time step:(NSInteger)step;
- (void)receiveTrainingGoal:(enum CRPTrainingGoalType)type state:(NSInteger)state;
- (void)receiveTouchType:(CRPTouchModel * _Nonnull)model;
- (void)receivePairState:(enum CRPPairState)state;
- (void)receiveSOS;
- (void)receiveMeditationList:(NSArray<CRPMeditationRecord *> * _Nonnull)list;
- (void)receiveMeditationState:(enum CRPMeditationGetState)state;
- (void)receiveSleepList:(NSArray<CRPGoMoreSleepRecord *> * _Nonnull)list;
- (void)receiveTrainingRecognition:(CRPGoMoreTrainingModel * _Nonnull)trainingInfo;
- (void)receiveTrainingRecognitionList:(NSArray<CRPGoMoreTrainingRecord *> * _Nonnull)list;
- (void)receiveBloodPressure:(NSInteger)sbp :(NSInteger)dbp;
- (void)receiveTemperature:(double)value;
- (void)recevieTakePhoto;
- (void)receiveKnockSwitch:(CRPTouchModel * _Nonnull)model;
- (void)goodixDidState:(enum CRPState)state;
- (void)goodixRestoreState:(enum CRPRestoreState)state;
- (void)scan:(NSTimeInterval)duration progressHandler:(void (^ _Nullable)(NSArray<CRPDiscovery *> * _Nonnull))progressHandler completionHandler:(void (^ _Nullable)(NSArray<CRPDiscovery *> * _Nullable, enum CRPError))completionHandler;
- (void)interruptScan;
- (void)connet:(CRPDiscovery * _Nonnull)discovery;
- (void)reConnet;
- (void)remove:(void (^ _Nonnull)(enum CRPState, enum CRPError))handler;
- (void)getSteps;
- (void)getBattery:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (void)getSoftver:(void (^ _Nonnull)(NSString * _Nonnull, enum CRPError))handler;
- (void)getMac:(void (^ _Nonnull)(NSString * _Nonnull, enum CRPError))handler;
- (void)setUserinfo:(CRPUserInfoModel * _Nonnull)Userinfo;
- (void)getUserinfo:(void (^ _Nonnull)(CRPUserInfoModel * _Nonnull, enum CRPError))handler;
- (void)setTime;
- (void)setNormalTrainingGoal:(CRPTrainingGoalsModel * _Nonnull)goals;
- (void)getNormalTrainingGoal:(void (^ _Nonnull)(CRPTrainingGoalsModel * _Nonnull, enum CRPError))handler;
- (void)setTrainingDayGoal:(CRPTrainingGoalStateModel * _Nonnull)model :(CRPTrainingGoalsModel * _Nonnull)goals;
- (void)getTrainingDayGoal:(void (^ _Nonnull)(CRPTrainingGoalStateModel * _Nonnull, CRPTrainingGoalsModel * _Nonnull, enum CRPError))handler;
- (void)setActivityReminder:(CRPActivityReminderModel * _Nonnull)ActivityReminder;
- (void)getActivityReminderInfo:(void (^ _Nonnull)(CRPActivityReminderModel * _Nonnull, enum CRPError))handler;
- (void)setHeartRateRemind:(CRPHRRemind * _Nonnull)remind;
- (void)getHeartRateRemind:(void (^ _Nonnull)(CRPHRRemind * _Nonnull, enum CRPError))handler;
- (void)setTimingHeartRate:(NSInteger)interval;
- (void)getTimingHeartRateInterval:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (void)setTimingHRV:(NSInteger)interval;
- (void)getTimingHRVInterval:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (void)setTimingO2:(NSInteger)interval;
- (void)getTimingO2Interval:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (void)setStartSingleHR;
- (void)setStopSingleHR;
- (void)getHeartRecordData:(void (^ _Nonnull)(NSArray<CRPHeartRecordModel *> * _Nonnull, enum CRPError))handler;
- (void)setStartHRV;
- (void)setStopHRV;
- (void)setStartStress;
- (void)setStopStress;
- (void)getHRVRecord:(void (^ _Nonnull)(NSArray<CRPHRVRecordModel *> * _Nonnull, enum CRPError))handler;
- (void)getStressRecord:(void (^ _Nonnull)(NSArray<CRPStressRecordModel *> * _Nonnull, enum CRPError))handler;
- (void)setStartSpO2;
- (void)setStopSpO2;
- (void)getO2RecordData:(void (^ _Nonnull)(NSArray<CRPO2RecordModel *> * _Nonnull, enum CRPError))handler;
- (void)setTrainingWithState:(enum CRPTrainingType)state type:(enum CRPTrainingGoalType)type goal:(NSInteger)goal;
- (void)getTrainingData:(NSInteger)day :(void (^ _Nonnull)(CRPTrainingRecordModel * _Nonnull, enum CRPError))handler;
- (void)getSleepData:(NSInteger)day :(void (^ _Nonnull)(CRPSleepRecordModel * _Nonnull, enum CRPError))handler;
- (void)getTimingHeartRate:(NSInteger)day :(void (^ _Nonnull)(CRPTimingHRRecordModel * _Nonnull, enum CRPError))handler;
- (void)getTimingHRV:(NSInteger)day :(void (^ _Nonnull)(CRPTimingHRVRecordModel * _Nonnull, enum CRPError))handler;
- (void)getTimingO2:(NSInteger)day :(void (^ _Nonnull)(CRPTimingO2RecordModel * _Nonnull, enum CRPError))hanler;
- (void)getStepArchiveData:(NSInteger)day :(void (^ _Nonnull)(CRPTimingStepsRecordModel * _Nonnull, enum CRPError))handler;
- (void)getActivityArchiveData:(NSInteger)day :(void (^ _Nonnull)(CRPContinueActivitysRecordModel * _Nonnull, enum CRPError))handler;
- (void)getTrainingtRecordList;
- (void)getTrainingRecordDataWithId:(NSInteger)id :(void (^ _Nonnull)(CRPNewTrainingModel * _Nonnull, enum CRPError))handler;
- (void)getTraingingState:(void (^ _Nonnull)(enum CRPTrainingType, enum CRPTrainingState, enum CRPError))handler;
- (void)reset:(void (^ _Nonnull)(BOOL, enum CRPError))handler;
- (void)shutDown:(void (^ _Nonnull)(BOOL, enum CRPError))handler;
- (void)checkLatest:(NSString * _Nonnull)version :(NSString * _Nonnull)mac handler:(void (^ _Nonnull)(CRPNewVersionInfo * _Nullable, enum CRPError))handler;
- (void)startGoodixUpgradeFromFileWithZipPath:(NSString * _Nonnull)zipPath;
- (void)checkDFUState:(void (^ _Nonnull)(NSString * _Nonnull, enum CRPError))handler;
- (void)stopUpgrade;
- (void)getGitHashInfoWithHandler:(void (^ _Nonnull)(NSString * _Nonnull, enum CRPError))handler;
- (void)getGSensorInfoWithHandler:(void (^ _Nonnull)(CRPGsensorInfoModel * _Nonnull, enum CRPError))handler;
- (void)getBatteryInfoWithHandler:(void (^ _Nonnull)(CRPBatteryInfoModel * _Nonnull, enum CRPError))handler;
- (void)getWearState;
- (void)setTrainingDataWithStep:(NSInteger)step cal:(NSInteger)cal exerciseTime:(NSInteger)exerciseTime distance:(NSInteger)distance;
- (void)setGoalReminderWithType:(enum CRPTrainingGoalType)type;
- (void)setReceiveGoalReminderWithType:(enum CRPTrainingGoalType)type state:(NSInteger)state;
- (void)setActivityReminderReach;
- (void)setSleepDataWithData:(NSArray<CRPSleepDetailModel *> * _Nonnull)data;
- (void)setTrainingGoalReachWithType:(enum CRPTrainingGoalType)type;
- (void)setReceiveTrainingGoalReachWithType:(enum CRPTrainingGoalType)type state:(NSInteger)state;
- (NSArray<CBPeripheral *> * _Nonnull)connectedPeripherial SWIFT_WARN_UNUSED_RESULT;
/// 设置24小时体温测量间隔
- (void)setTimingTemperatureWithInterval:(NSInteger)interval;
/// 获取24小时体温测量间隔
- (void)getTimingTemperatureStateWithHandler:(void (^ _Nonnull)(CRPTimingtemperatureStateModel * _Nonnull, enum CRPError))handler;
/// 获取24小时体温测量数据
- (void)getTimingTemperatureDataWithDay:(NSInteger)day handler:(void (^ _Nonnull)(CRPTimingTemperatureDataModel * _Nonnull, enum CRPError))handler;
- (void)getMeasurementStateWithHandler:(void (^ _Nonnull)(enum CRPMeasurementState, enum CRPError))handler;
- (void)getRebootTimesWithHandler:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (void)reboot;
- (void)getRingInfoWithHandler:(void (^ _Nonnull)(CRPRingInfoModel * _Nonnull, enum CRPError))handler;
- (void)setRingInfoWithInfo:(CRPRingInfoModel * _Nonnull)info;
- (void)setTouchSwitchWithOpen:(BOOL)open;
- (void)setTouchTypeWithType:(enum CRPTouchType)type;
- (void)setTouchSizeWithWidth:(NSInteger)width height:(NSInteger)height;
- (void)getTouchType;
- (void)setPair;
- (void)getPairstate;
- (void)clearPairInfo;
- (void)getNotificationSupportType:(void (^ _Nonnull)(NSArray<NSNumber *> * _Nonnull, enum CRPError))handler;
- (void)getNotificationState:(void (^ _Nonnull)(NSArray<NSNumber *> * _Nonnull, enum CRPError))handler;
- (void)setNotificationState:(NSArray<NSNumber *> * _Nonnull)states;
- (void)getSOSState:(void (^ _Nonnull)(BOOL, enum CRPError))handler;
- (void)setSOSStateWithOpen:(BOOL)open;
- (void)getStepsPerMin:(void (^ _Nonnull)(NSArray<NSNumber *> * _Nonnull, enum CRPError))handler;
- (void)setPageTurnModeWithMode:(NSInteger)mode;
- (void)getPageTurnModeWithHandler:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (void)getMeditationState;
- (void)setMeditationWithState:(enum CRPMeditationSetState)state minute:(NSInteger)minute;
- (void)getMeditationRecordList;
- (void)getMeditationRecordDataWithId:(NSInteger)id :(void (^ _Nonnull)(CRPMeditationModel * _Nonnull, enum CRPError))handler;
- (void)getGoMoreAlgorithmSupport:(void (^ _Nonnull)(CRPGoMoreSupportModel * _Nonnull, enum CRPError))handler;
- (void)getGoMoreKeySupport:(void (^ _Nonnull)(BOOL, enum CRPError))handler;
- (void)getGoMoreChipID:(void (^ _Nonnull)(NSString * _Nonnull, enum CRPError))handler;
- (void)getGoMoreKeySupportWithKey:(NSString * _Nonnull)key :(void (^ _Nonnull)(BOOL, enum CRPError))handler;
- (void)getGoMoreSleepDataList;
- (void)getGoMoreSleepDataDetailWithId:(NSInteger)id :(void (^ _Nonnull)(CRPGoMoreSleepDataModel * _Nonnull, enum CRPError))handler;
- (void)getGoMoreSleepSegmentationDataWithId:(NSInteger)id :(void (^ _Nonnull)(CRPGoMoreSleepRecordModel * _Nonnull, enum CRPError))handler;
- (void)getGoMoreSleepType:(void (^ _Nonnull)(CRPGoMoreSleepType * _Nonnull, enum CRPError))handler;
- (void)setGoMoreSleepGenerateWithStartTime:(NSInteger)startTime sleepTime:(NSInteger)sleepTime awakeTime:(NSInteger)awakeTime;
- (void)setTerminationSleep:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (void)getGoMoreTrainingRecognitionList;
- (void)getGoMoreTrainingRecognitionDetailWithId:(NSInteger)id :(void (^ _Nonnull)(NSInteger, CRPGoMoreTrainingModel * _Nonnull, enum CRPError))handler;
- (void)setGoMoreTestExerciseRecognitionDataDetailWithTrainingInfo:(CRPGoMoreTrainingModel * _Nonnull)trainingInfo;
- (void)getSleepO2SupportState:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (void)startDIYUpgradeFromFileWithPath:(NSString * _Nonnull)path;
- (void)startUpgradeFromFileWithPath:(NSString * _Nonnull)path;
- (void)setBootLoaderMode;
- (void)getBootLoaderMode:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (void)startBootLoaderUpgradeWithZipPath:(NSString * _Nonnull)zipPath;
- (void)setStartBP;
- (void)setStopBP;
- (void)setStartTemperature;
- (void)setStopTemperature;
- (void)setFirstConnect;
- (void)setShakeToTakePhotoWithOpen:(BOOL)open;
- (void)getShakeToTakePhoto:(void (^ _Nonnull)(BOOL, enum CRPError))handler;
- (void)setTransmitPowerWithLevel:(NSInteger)level;
- (void)getTransmitPower:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
- (void)setKnockSwitchWithOpen:(BOOL)open;
- (void)getKnockSwitch;
- (void)goodixFixConnectWithP:(CRPDiscovery * _Nonnull)p;
- (void)disConnectGoodix;
- (void)startRestoreGoodix;
/// 设置亮屏时长time：秒
- (void)setAutoLockTimeWithTime:(NSInteger)time;
/// 获取亮屏时长time：秒
- (void)getAutoLockTime:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
/// 设置屏幕亮度level：0-100
- (void)setScreenBrightnessWithLevel:(NSInteger)level;
/// 设置屏幕亮度
- (void)getScreenBrightness:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
/// 设置屏幕显示内容
- (void)setDisplayContentWithTypes:(NSArray<NSNumber *> * _Nonnull)types;
/// 获取屏幕支持显示内容
- (void)getDisplaySupportInfo:(void (^ _Nonnull)(NSArray<NSNumber *> * _Nonnull, enum CRPError))handler;
/// 获取屏幕当前显示内容
- (void)getDisplayContent:(void (^ _Nonnull)(NSArray<NSNumber *> * _Nonnull, enum CRPError))handler;
/// 设置震动强度
- (void)setVibrationStrengthWithType:(NSInteger)type;
/// 获取震动强度
- (void)getVibrationStrengthWithHandler:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
/// 设置闹钟
- (void)setAlarm:(NSArray<CRPRingAlarmModel *> * _Nonnull)alarms;
/// 删除闹钟
- (void)setDeleteAlarm:(NSInteger)id;
/// 删除所有闹钟
- (void)setClearAlarm;
/// 获取所有闹钟
- (void)getAlarm:(void (^ _Nonnull)(NSArray<CRPRingAlarmModel *> * _Nonnull, enum CRPError))handler;
/// 查找手环
/// state 0：暂停 ；1：开始
- (void)setFindDeviceWithState:(NSInteger)state;
/// 设置激活
- (void)setActivationCodeWithState:(enum CRPActivationState)state code:(NSInteger)code;
/// 获取激活状态
- (void)getActivationState:(void (^ _Nonnull)(CRPActivationStateModel * _Nonnull, enum CRPError))handler;
/// 清空激活状态
- (void)clearActivationCode:(void (^ _Nonnull)(BOOL, enum CRPError))handler;
/// 设置24小时压力测量状态
- (void)setTimingStress:(NSInteger)interval;
/// 获取24小时压力测量状态
- (void)getTimingStressInterval:(void (^ _Nonnull)(NSInteger, enum CRPError))handler;
/// 获取24小时压力数据
- (void)getTimingStress:(NSInteger)day :(void (^ _Nonnull)(CRPTimingStressRecordModel * _Nonnull, enum CRPError))handler;
@end


SWIFT_CLASS("_TtC12CRPSmartRing12CRPSportData")
@interface CRPSportData : NSObject
@property (nonatomic, strong) NSMutableData * _Null_unspecified nsdata;
@property (nonatomic, readonly, copy) NSString * _Nonnull languageCode;
@property (nonatomic) NSInteger avg;
@property (nonatomic, copy) NSArray<CRPSportModel *> * _Nonnull sportData;
@property (nonatomic, copy) NSDictionary<NSNumber *, CRPSportModel *> * _Nonnull sportDetail;
@property (nonatomic, copy) NSDictionary<NSNumber *, NSArray<NSNumber *> *> * _Nonnull sportHeartRate;
@property (nonatomic, copy) NSDictionary<NSNumber *, CRPMeditationModel *> * _Nonnull meditationDetail;
@property (nonatomic, copy) NSDictionary<NSNumber *, NSArray<NSNumber *> *> * _Nonnull meditationHeartRate;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CRPSportData * _Nonnull sharedInstance;)
+ (CRPSportData * _Nonnull)sharedInstance SWIFT_WARN_UNUSED_RESULT;
- (void)saveSportDetail:(Binary * _Nonnull)binary;
- (void)saveSportHeartRate:(Binary * _Nonnull)binary;
- (void)saveMeditationDetail:(Binary * _Nonnull)binary;
- (void)saveMeditationHeartRate:(Binary * _Nonnull)binary;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


SWIFT_CLASS("_TtC12CRPSmartRing13CRPSportModel")
@interface CRPSportModel : NSObject
@property (nonatomic, readonly, copy) NSString * _Nonnull date;
@property (nonatomic, readonly) NSInteger startTime;
@property (nonatomic, readonly) NSInteger endTime;
@property (nonatomic, readonly) NSInteger vaildTime;
@property (nonatomic, readonly) NSInteger hrAvg;
@property (nonatomic, readonly) enum CRPTrainingType type;
@property (nonatomic, readonly) NSInteger step;
@property (nonatomic, readonly) NSInteger distance;
@property (nonatomic, readonly) NSInteger kcal;
@property (nonatomic, readonly) enum CRPTrainingGoalType goalType;
@property (nonatomic, readonly) NSInteger goal;
@property (nonatomic, readonly) NSInteger hrAddress;
@property (nonatomic, readonly) NSInteger hrLength;
@property (nonatomic, readonly) NSInteger originalStartTime;
- (nonnull instancetype)initWithDate:(NSString * _Nonnull)date startTime:(NSInteger)startTime endTime:(NSInteger)endTime vaildTime:(NSInteger)vaildTime hrAvg:(NSInteger)hrAvg type:(enum CRPTrainingType)type step:(NSInteger)step distance:(NSInteger)distance kcal:(NSInteger)kcal goalType:(enum CRPTrainingGoalType)goalType goal:(NSInteger)goal hrAddress:(NSInteger)hrAddress hrLength:(NSInteger)hrLength originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPState, open) {
  CRPStateUnbind = 0,
  CRPStateConnecting = 1,
  CRPStateConnected = 2,
  CRPStateDisconnecting = 3,
  CRPStateDisconnected = 4,
  CRPStateSyncing = 5,
  CRPStateSyncSuccess = 6,
  CRPStateSyncError = 7,
};


SWIFT_CLASS("_TtC12CRPSmartRing12CRPStepModel")
@interface CRPStepModel : NSObject
@property (nonatomic) NSInteger steps;
@property (nonatomic) NSInteger distance;
@property (nonatomic) NSInteger calory;
@property (nonatomic) NSInteger time;
- (nonnull instancetype)initWithSteps:(NSInteger)steps distance:(NSInteger)distance calory:(NSInteger)calory :(NSInteger)time OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing20CRPStressRecordModel")
@interface CRPStressRecordModel : NSObject
@property (nonatomic) NSInteger stress;
@property (nonatomic) NSInteger time;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithStress:(NSInteger)stress time:(NSInteger)time originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end



SWIFT_CLASS("_TtC12CRPSmartRing22CRPTimingHRRecordModel")
@interface CRPTimingHRRecordModel : NSObject
@property (nonatomic) NSInteger day;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull hearts;
- (nonnull instancetype)initWithDay:(NSInteger)day hearts:(NSArray<NSNumber *> * _Nonnull)hearts OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing23CRPTimingHRVRecordModel")
@interface CRPTimingHRVRecordModel : NSObject
@property (nonatomic) NSInteger day;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull hrvs;
- (nonnull instancetype)initWithDay:(NSInteger)day hrvs:(NSArray<NSNumber *> * _Nonnull)hrvs OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing22CRPTimingO2RecordModel")
@interface CRPTimingO2RecordModel : NSObject
@property (nonatomic) NSInteger day;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull o2s;
- (nonnull instancetype)initWithDay:(NSInteger)day o2s:(NSArray<NSNumber *> * _Nonnull)o2s OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing25CRPTimingStepsRecordModel")
@interface CRPTimingStepsRecordModel : NSObject
@property (nonatomic) NSInteger day;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull steps;
- (nonnull instancetype)initWithDay:(NSInteger)day steps:(NSArray<NSNumber *> * _Nonnull)steps OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing26CRPTimingStressRecordModel")
@interface CRPTimingStressRecordModel : NSObject
@property (nonatomic) NSInteger day;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull stress;
- (nonnull instancetype)initWithDay:(NSInteger)day stress:(NSArray<NSNumber *> * _Nonnull)stress OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing29CRPTimingTemperatureDataModel")
@interface CRPTimingTemperatureDataModel : NSObject
@property (nonatomic, readonly) NSInteger day;
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull tempeartures;
- (nonnull instancetype)initWithDay:(NSInteger)day tempeartures:(NSArray<NSNumber *> * _Nonnull)tempeartures OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing30CRPTimingtemperatureStateModel")
@interface CRPTimingtemperatureStateModel : NSObject
@property (nonatomic) NSInteger interval;
@property (nonatomic) NSInteger type;
- (nonnull instancetype)initWithInterval:(NSInteger)interval type:(NSInteger)type OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing13CRPTouchModel")
@interface CRPTouchModel : NSObject
@property (nonatomic) BOOL open;
@property (nonatomic) enum CRPTouchType type;
- (nonnull instancetype)initWithOpen:(BOOL)open type:(enum CRPTouchType)type OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPTouchType, open) {
  CRPTouchTypeShort_video = 0,
  CRPTouchTypeMusic = 1,
  CRPTouchTypeTake_photo = 2,
  CRPTouchTypeRead = 3,
  CRPTouchTypeSlideshow = 4,
};


SWIFT_CLASS("_TtC12CRPSmartRing25CRPTrainingGoalStateModel")
@interface CRPTrainingGoalStateModel : NSObject
@property (nonatomic) BOOL open;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull weekday;
- (nonnull instancetype)initWithOpen:(BOOL)open weekday:(NSArray<NSNumber *> * _Nonnull)weekday OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPTrainingGoalType, open) {
  CRPTrainingGoalTypeStep = 0,
  CRPTrainingGoalTypeKcal = 1,
  CRPTrainingGoalTypeExerciseTime = 2,
  CRPTrainingGoalTypeDistance = 3,
  CRPTrainingGoalTypeNone = 255,
};


SWIFT_CLASS("_TtC12CRPSmartRing21CRPTrainingGoalsModel")
@interface CRPTrainingGoalsModel : NSObject
@property (nonatomic) NSInteger step;
@property (nonatomic) NSInteger kcal;
@property (nonatomic) NSInteger distance;
@property (nonatomic) NSInteger exerciseTime;
- (nonnull instancetype)initWithStep:(NSInteger)step kcal:(NSInteger)kcal distance:(NSInteger)distance exerciseTime:(NSInteger)exerciseTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing17CRPTrainingRecord")
@interface CRPTrainingRecord : NSObject
@property (nonatomic) NSInteger id;
@property (nonatomic) NSInteger startTime;
@property (nonatomic) NSInteger type;
@property (nonatomic) NSInteger originalStartTime;
- (nonnull instancetype)initWithId:(NSInteger)id startTime:(NSInteger)startTime type:(NSInteger)type originalStartTime:(NSInteger)originalStartTime OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing22CRPTrainingRecordModel")
@interface CRPTrainingRecordModel : NSObject
@property (nonatomic) NSInteger day;
@property (nonatomic) NSInteger step;
@property (nonatomic) NSInteger cal;
@property (nonatomic) NSInteger exerciseTime;
@property (nonatomic) NSInteger distance;
- (nonnull instancetype)initWithDay:(NSInteger)day step:(NSInteger)step cal:(NSInteger)cal exerciseTime:(NSInteger)exerciseTime distance:(NSInteger)distance OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPTrainingState, open) {
  CRPTrainingStateUnstarted = 0,
  CRPTrainingStateExercising = 1,
  CRPTrainingStatePause = 2,
};

typedef SWIFT_ENUM(NSInteger, CRPTrainingType, open) {
  CRPTrainingTypeWalking = 0,
  CRPTrainingTypeRun = 1,
  CRPTrainingTypeCycling = 2,
  CRPTrainingTypeSkipping = 3,
  CRPTrainingTypeBadminton = 4,
  CRPTrainingTypeBasketball = 5,
  CRPTrainingTypeFootball = 6,
  CRPTrainingTypeSwimming = 7,
  CRPTrainingTypeHiking = 8,
  CRPTrainingTypeTennis = 9,
  CRPTrainingTypeRugby = 10,
  CRPTrainingTypeGolf = 11,
  CRPTrainingTypeYoga = 12,
  CRPTrainingTypeWorkout = 13,
  CRPTrainingTypeDance = 14,
  CRPTrainingTypeBaseball = 15,
  CRPTrainingTypeElliptical = 16,
  CRPTrainingTypeIndoorCycling = 17,
  CRPTrainingTypeTraining = 18,
  CRPTrainingTypeRowingMachine = 19,
  CRPTrainingTypeTrailRun = 20,
  CRPTrainingTypeSki = 21,
  CRPTrainingTypeBowling = 22,
  CRPTrainingTypeDumbbells = 23,
  CRPTrainingTypeSit_ups = 24,
  CRPTrainingTypeOnfoot = 25,
  CRPTrainingTypeIndoorWalk = 26,
  CRPTrainingTypeIndoorRun = 27,
  CRPTrainingTypeCricket = 28,
  CRPTrainingTypeKabaddi = 29,
  CRPTrainingTypeGps_Walking = 30,
  CRPTrainingTypeGps_Run = 31,
  CRPTrainingTypeGps_Cycling = 32,
  CRPTrainingTypeGps_TrailRun = 33,
  CRPTrainingTypeGps_Onfoot = 34,
  CRPTrainingTypeSailing = 35,
  CRPTrainingTypeWater_Polo = 36,
  CRPTrainingTypeOther_Water_Sports = 37,
  CRPTrainingTypePaddle_Boarding = 38,
  CRPTrainingTypeWater_Skiing = 39,
  CRPTrainingTypeKayaking = 40,
  CRPTrainingTypeRafting = 41,
  CRPTrainingTypeRowing = 42,
  CRPTrainingTypePowerboating = 43,
  CRPTrainingTypeFinswimming = 44,
  CRPTrainingTypeDiving = 45,
  CRPTrainingTypeArtistic_Swimming = 46,
  CRPTrainingTypeSnorkeling = 47,
  CRPTrainingTypeKitesurfing = 48,
  CRPTrainingTypeOpen_Water_Swim = 49,
  CRPTrainingTypeRock_Climbing = 50,
  CRPTrainingTypeSkateboarding = 51,
  CRPTrainingTypeRoller_Skating = 52,
  CRPTrainingTypeParkour = 53,
  CRPTrainingTypeATV = 54,
  CRPTrainingTypeParagliding = 55,
  CRPTrainingTypeTriathlon = 56,
  CRPTrainingTypeStair_Climber = 57,
  CRPTrainingTypeStair_Climbing = 58,
  CRPTrainingTypeStepper = 59,
  CRPTrainingTypeCore_Training = 60,
  CRPTrainingTypeFlexibility = 61,
  CRPTrainingTypePilates = 62,
  CRPTrainingTypeGymnastics = 63,
  CRPTrainingTypeStretching = 64,
  CRPTrainingTypeStrength = 65,
  CRPTrainingTypeCross_Training = 66,
  CRPTrainingTypeAerobics = 67,
  CRPTrainingTypePhysical_Training = 68,
  CRPTrainingTypeWall_Ball = 69,
  CRPTrainingTypeBarbell = 70,
  CRPTrainingTypeDeadlift = 71,
  CRPTrainingTypeUpperBody = 72,
  CRPTrainingTypeFunctional_Training = 73,
  CRPTrainingTypeBurpee = 74,
  CRPTrainingTypeBack = 75,
  CRPTrainingTypeLower_Body = 76,
  CRPTrainingTypeAbs = 77,
  CRPTrainingTypeHIIT = 78,
  CRPTrainingTypeWeightlifting = 79,
  CRPTrainingTypeSquare_Dancing = 80,
  CRPTrainingTypeBelly_Dance = 81,
  CRPTrainingTypeBallet = 82,
  CRPTrainingTypeZumba = 83,
  CRPTrainingTypeLatin_Dance = 84,
  CRPTrainingTypeStreet_Dance = 85,
  CRPTrainingTypeFolk_Dance = 86,
  CRPTrainingTypeJazz_Dance = 87,
  CRPTrainingTypeBoxing = 88,
  CRPTrainingTypeWrestling = 89,
  CRPTrainingTypeMartial_Arts = 90,
  CRPTrainingTypeTai_Chi = 91,
  CRPTrainingTypeMuay_Thai = 92,
  CRPTrainingTypeJudo = 93,
  CRPTrainingTypeTaekwondo = 94,
  CRPTrainingTypeKarate = 95,
  CRPTrainingTypeKickboxing = 96,
  CRPTrainingTypeFencing = 97,
  CRPTrainingTypeKendo = 98,
  CRPTrainingTypeVolleyball = 99,
  CRPTrainingTypeSoftball = 100,
  CRPTrainingTypeHockey = 101,
  CRPTrainingTypeTable_Tennis = 102,
  CRPTrainingTypeHandball = 103,
  CRPTrainingTypeSquash = 104,
  CRPTrainingTypeBilliards = 105,
  CRPTrainingTypeShuttlecock = 106,
  CRPTrainingTypeBeach_Soccer = 107,
  CRPTrainingTypeBeach_Volleyball = 108,
  CRPTrainingTypeSepak_Takraw = 109,
  CRPTrainingTypeIce_Skating = 110,
  CRPTrainingTypeCurling = 111,
  CRPTrainingTypeOther_Winter_Sports = 112,
  CRPTrainingTypeSnowmobile = 113,
  CRPTrainingTypeIce_Hockey = 114,
  CRPTrainingTypeBobsleigh = 115,
  CRPTrainingTypeSledding = 116,
  CRPTrainingTypeArchery = 117,
  CRPTrainingTypeDarts = 118,
  CRPTrainingTypeTug_of_War = 119,
  CRPTrainingTypeHula_Hoop = 120,
  CRPTrainingTypeKite_Flying = 121,
  CRPTrainingTypeHouse_Riding = 122,
  CRPTrainingTypeFrisbee = 123,
  CRPTrainingTypeFishing = 124,
  CRPTrainingTypeEquestrian_Sports = 125,
  CRPTrainingTypeAthletics = 126,
  CRPTrainingTypeAuto_Racing = 127,
  CRPTrainingTypeProceed = 253,
  CRPTrainingTypePause = 254,
  CRPTrainingTypeEnd = 255,
};


SWIFT_CLASS("_TtC12CRPSmartRing18CRPUDTVersionModel")
@interface CRPUDTVersionModel : NSObject
@property (nonatomic) NSInteger udtMaxSize;
@property (nonatomic, copy) NSString * _Nonnull udtVersion;
- (nonnull instancetype)initWithUdtMaxSize:(NSInteger)udtMaxSize udtVersion:(NSString * _Nonnull)udtVersion OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


SWIFT_CLASS("_TtC12CRPSmartRing10CRPUpgrade")
@interface CRPUpgrade : NSObject
@property (nonatomic, copy) NSData * _Nonnull data;
@property (nonatomic) uint16_t crc_last;
@property (nonatomic) NSInteger totalParts;
@property (nonatomic) NSInteger part;
@property (nonatomic) NSInteger last_index;
@property (nonatomic, copy) NSString * _Null_unspecified ver;
@property (nonatomic, copy) NSString * _Nonnull verNumber;
@property (nonatomic, copy) NSString * _Null_unspecified fileUrl;
@property (nonatomic, copy) NSURL * _Null_unspecified filePath;
@property (nonatomic, copy) void (^ _Null_unspecified handler)(CRPNewVersionInfo * _Nullable, enum CRPError);
@property (nonatomic) NSInteger pkgSize;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CRPUpgrade * _Nonnull sharedInstance;)
+ (CRPUpgrade * _Nonnull)sharedInstance SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, copy) void (^ _Nullable bootLoaderUpgradeBlock)(void);
- (void)load:(NSURL * _Nonnull)url :(NSInteger)index;
- (NSArray<NSNumber *> * _Nonnull)getsub:(NSInteger)i SWIFT_WARN_UNUSED_RESULT;
- (void)calLastCrc;
- (void)stop;
- (void)stopDIY;
- (uint16_t)crc16Compute:(NSData * _Nonnull)p_data :(uint16_t)c SWIFT_WARN_UNUSED_RESULT;
- (void)checkLatest:(NSString * _Nonnull)version :(NSString * _Nonnull)mac;
- (void)returnInfo:(CRPNewVersionInfo * _Nullable)info;
- (void)startUpgradeWithInfo:(CRPNewVersionInfo * _Nullable)info;
- (void)startUpgradeFromFileWithPath:(NSString * _Nonnull)path;
- (void)startDIYUpgradeFromFileWithPath:(NSString * _Nonnull)path;
- (void)doUpgrade;
- (void)doDIYUpgrade;
- (NSString * _Nonnull)unZipDataWithZipPath:(NSString * _Nonnull)zipPath upgradeType:(NSInteger)upgradeType SWIFT_WARN_UNUSED_RESULT;
- (NSArray<NSString *> * _Nullable)getAllfirePathWithDirPath:(NSString * _Nonnull)dirPath SWIFT_WARN_UNUSED_RESULT;
- (void)goodixstartUpgradeWithZipPath:(NSString * _Nonnull)zipPath;
- (void)startUpgradeForBootLoaderWithZipPath:(NSString * _Nonnull)zipPath;
- (NSString * _Nonnull)unZipBootLoaderWithZipPath:(NSString * _Nonnull)zipPath SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


SWIFT_CLASS("_TtC12CRPSmartRing21CRPUploadTaskDelegate")
@interface CRPUploadTaskDelegate : CRPDataTaskDelegate
@end


SWIFT_CLASS("_TtC12CRPSmartRing16CRPUserInfoModel")
@interface CRPUserInfoModel : NSObject
@property (nonatomic) NSInteger height;
@property (nonatomic) NSInteger weight;
@property (nonatomic) NSInteger age;
@property (nonatomic) enum CRPGenderOption gender;
@property (nonatomic) NSInteger stepLength;
- (nonnull instancetype)initWithHeight:(NSInteger)height weight:(NSInteger)weight age:(NSInteger)age gender:(enum CRPGenderOption)gender stepLength:(NSInteger)stepLength OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, CRPWeekDay, open) {
  CRPWeekDayNone = 0,
  CRPWeekDayMon = 1,
  CRPWeekDayTue = 2,
  CRPWeekDayWed = 3,
  CRPWeekDayThu = 4,
  CRPWeekDayFri = 5,
  CRPWeekDaySat = 6,
  CRPWeekDaySun = 7,
};


SWIFT_CLASS("_TtC12CRPSmartRing13CommandPacket")
@interface CommandPacket : NSObject
@property (nonatomic) uint8_t mCmd;
@property (nonatomic) NSInteger mLength;
@property (nonatomic, strong) NSMutableData * _Nonnull data;
- (nonnull instancetype)initWithMCmd:(uint8_t)mCmd mLength:(NSInteger)mLength data:(NSMutableData * _Nonnull)data OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class CBDescriptor;

SWIFT_CLASS("_TtC12CRPSmartRing9GoodixBle")
@interface GoodixBle : NSObject <CBCentralManagerDelegate, CBPeripheralDelegate>
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
- (void)centralManagerDidUpdateState:(CBCentralManager * _Nonnull)central;
- (void)centralManager:(CBCentralManager * _Nonnull)central didConnectPeripheral:(CBPeripheral * _Nonnull)peripheral;
- (void)centralManager:(CBCentralManager * _Nonnull)central didDisconnectPeripheral:(CBPeripheral * _Nonnull)peripheral error:(NSError * _Nullable)error;
- (void)centralManager:(CBCentralManager * _Nonnull)central didDiscoverPeripheral:(CBPeripheral * _Nonnull)peripheral advertisementData:(NSDictionary<NSString *, id> * _Nonnull)advertisementData RSSI:(NSNumber * _Nonnull)RSSI;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didUpdateNotificationStateForCharacteristic:(CBCharacteristic * _Nonnull)characteristic error:(NSError * _Nullable)error;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didUpdateValueForCharacteristic:(CBCharacteristic * _Nonnull)characteristic error:(NSError * _Nullable)error;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didDiscoverServices:(NSError * _Nullable)error;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didDiscoverCharacteristicsForService:(CBService * _Nonnull)service error:(NSError * _Nullable)error;
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didWriteValueForDescriptor:(CBDescriptor * _Nonnull)descriptor error:(NSError * _Nullable)error;
@end


@interface NSMutableData (SWIFT_EXTENSION(CRPSmartRing))
- (void)appendBytes:(NSArray<NSNumber *> * _Nonnull)arrayOfBytes;
@end



#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
