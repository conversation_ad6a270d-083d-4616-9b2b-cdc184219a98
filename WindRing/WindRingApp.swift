//
//  WindRingApp.swift
//  WindRing
//
//  Created by 1234 on 2025/3/3.
//

import SwiftUI
import UIKit

import Sentry
import IQKeyboardManagerSwift
#if DEBUG
    // 调试工具， debug模式开启 relese版本不会开启
import FLEX
#endif
@main
struct WindRingApp: App {
    
    @Environment(\.scenePhase) private var scenePhase
    @State private var showLaunchView = true
    // 使用StorageManager获取上下文
    let viewContext = StorageManager.shared.viewContext()
    ///全局toast
    @StateObject private var toastManager = ToastManager.shared
    ///版本检查
    @StateObject private var versionService = VersionUpdateService()
    
    @StateObject var notificationManager = LocalNotificationManager.shared
    
    @StateObject private var appState = AppState()
    ///app跳转
    @StateObject var deepLinkManager = DeepLinkManager()
    
    // 延迟初始化的服务
//    @State private var mqttSyncService: MQTTSyncService?
//    @State private var mqttService: MQTTService?
    @State private var deviceService: WindRingDeviceService?
//    @State private var rawDataUploadService: RawDataUploadService?
    @State private var rawDataUploadService: DataSyncUploadService?
    
    @Environment(\.openURL) var openURL
    
    init() {
        // 配置UISegmentedControl外观，移除分割线
        UISegmentedControl.appearance().setDividerImage(UIImage(), forLeftSegmentState: .normal, rightSegmentState: .normal, barMetrics: .default)
        
        SentrySDK.start { options in
            options.dsn = AppGlobals.sentryKey
                options.debug = true // Enabling debug when first installing is always helpful

                // Adds IP for users.
                // For more information, visit: https://docs.sentry.io/platforms/apple/data-management/data-collected/
                options.sendDefaultPii = true
            }
        
        // 确保网络监控服务已启动
        _ = NetworkMonitor.shared
        
        // 初始化本地化服务
        _ = LanguageManager.shared
//        print("🌍 当前应用语言: \(LocalizationService.shared.currentLanguage.rawValue)")

    }


    var body: some Scene {
        WindowGroup {
            Group {

                if (AuthService.shared.currentToken?.accessToken) != nil {
                    MainTabView()
                        .environment(\.managedObjectContext, viewContext)
//                        .environmentObject(localizationService)
                        .environmentObject(notificationManager)
                        .environmentObject(versionService)
                        .environmentObject(appState)
                        .onAppear {
                            // 初始化服务
                            initializeServices()
                            notificationManager.requestPermission()
                        }
                        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ReloadAppLanguageNotification"))) { _ in
//                            localizationService.objectWillChange.send()
                        }
                        .overlay(
                            // Sheet 层
                            BottomSheetContainer()
                        )
                } else {
                    LoginView()
                        .environmentObject(appState)
//                        .environmentObject(localizationService)
                }
                
//                if showLaunchView {
//                    LaunchView()
//                        .transition(.opacity)
//                        .zIndex(1)
//                        .onAppear {
//                            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
//                                withAnimation {
//                                    showLaunchView = false
//                                }
//                            }
//                        }
//                }
            }
            .alert(item: $versionService.updateAlertItem) { (updateInfo: UpdateCheckResult) in
                updateInfo.toSwiftUIAlert { url in
                    openURL(url)
                }
            }
            .toast(isPresented: $toastManager.presentingToast, dismissAfter: toastManager.dismissAfter) {
              print("Toast dismissed")
            } content: {
                ZStack {
                    Color.clear // 保证 ZStack 填满父视图

                    VStack {
                        Text(toastManager.toastMessage)
                            .bold()
                            .multilineTextAlignment(.center)
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.accentColor)
                            .cornerRadius(8.0)
                            .shadow(radius: 4.0)
                    }
                    .padding()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
            .onAppear {
                // 确保 AppState 的登录状态与 AuthManager 同步
                appState.isLoggedIn = AuthManager.shared.isLoggedIn
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("LoginStateChanged"))) { _ in
                // 强制刷新视图
                appState.objectWillChange.send()
                // 重新检查登录状态
                appState.isLoggedIn = AuthManager.shared.isLoggedIn
            }
            .environmentObject(deepLinkManager)
            .onOpenURL { url in
                deepLinkManager.handle(url: url)
            }
            .onChange(of: scenePhase) { newPhase in
                switch newPhase {
                case .active:
                    print("✅ Scene became active")
                    #if DEBUG
                        // 调试工具， debug模式开启 relese版本不会开启
                        FLEXManager.shared.showExplorer()
                    #endif
                    IQKeyboardManager.shared.isEnabled = true
                    IQKeyboardManager.shared.resignOnTouchOutside = true
//                    IQKeyboardManager.shared.toolbarConfiguration.useTextInputViewTintColor = true
//                    IQKeyboardManager.shared.enableAutoToolbar = false
                    // 相当于 sceneDidBecomeActive()
                case .inactive:
                    print("⚠️ Scene inactive")
                case .background:
                    print("🔙 Scene in background")
                @unknown default:
                    break
                }
            }

        }
    }
    
    // 初始化所有服务
    private func initializeServices() {
        // 如果服务已经初始化，则不再重复初始化
//        if mqttSyncService == nil {
//            mqttSyncService = MQTTSyncService.shared
//        }
//        if mqttService == nil {
//            mqttService = MQTTService.shared
//        }
        if deviceService == nil {
            deviceService = WindRingDeviceService.shared
        }
        if rawDataUploadService == nil {
            rawDataUploadService = DataSyncUploadService.shared
        }
        _ = CRPSmartRingManage.shared
        // 设置MQTT服务
//        setupMQTTService()
        // 设置数据自动上传服务
//        setupAutoUploadService()
        ///刷新token
        AuthService.shared.refreshSession { result in
            
        }
    }
    
    
    /// 设置数据自动上传服务
    private func setupAutoUploadService() {
        guard let rawDataUploadService = rawDataUploadService else { return }
        
        // 始终确保自动上传设置为true
        UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
        UserDefaults.standard.set(true, forKey: "auto_upload_initialized")
        
        // 在应用启动后延迟2秒启动自动上传服务
//        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
//            print("🔄 正在启动数据自动上传服务...")
//            // 启动自动上传，每5分钟自动上传一次数据
//            _ = rawDataUploadService.startAutoUpload(interval: 300)
//            
//            // 5秒后再次确认自动上传是否启动
//            DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
//                if !rawDataUploadService.isAutoUploadEnabled {
//                    print("🔄 再次尝试启动数据自动上传服务...")
//                    _ = rawDataUploadService.startAutoUpload(interval: 300)
//                }
//            }
//        }
        
        // 监听设备连接状态变化
//        NotificationCenter.default.addObserver(forName: NSNotification.Name("DeviceConnectedNotification"), object: nil, queue: .main) { _ in
//            print("📲 设备已连接，启动数据自动上传服务...")
//            _ = rawDataUploadService.startAutoUpload(interval: 300)
//        }
    }
}

class AppState: ObservableObject {
    @Published var isLoggedIn: Bool = AuthManager.shared.isLoggedIn
    
    func login(userId: String) {
        AuthManager.shared.login(userId: userId)
        isLoggedIn = true
    }
    
    func logout() {
        // 清理服务资源
        cleanupServices()
        
        // 更新登录状态
        AuthManager.shared.logout()
        isLoggedIn = false
    }
    
    private func cleanupServices() {
        // 停止MQTT同步服务
        MQTTSyncService.shared.stop()
        
        // 断开MQTT连接
        MQTTService.shared.disconnect()
        
        // 停止设备服务
                WindRingDeviceService.shared.stopScan()
//                WindRingDeviceService.shared.disconnect()
        
        // 停止数据上传服务
        RawDataUploadService.shared.stopAutoUpload()
        
        // 清理通知观察者
        NotificationCenter.default.removeObserver(self)
        
        // 清理用户数据
        UserDefaults.standard.removeObject(forKey: "userToken")
        UserDefaults.standard.synchronize()
        
        // 销毁所有定时器
        cleanupTimers()
        
        print("🔒 已清理所有服务资源")
    }
    
    private func cleanupTimers() {
        // 销毁自动上传定时器
//        if let uploadTimer = RawDataUploadService.shared.uploadTimer {
//            uploadTimer.invalidate()
//            RawDataUploadService.shared.uploadTimer = nil
//        }
//        
//        // 销毁MQTT重连定时器
//        if let reconnectTimer = MQTTService.shared.reconnectTimer {
//            reconnectTimer.invalidate()
//            MQTTService.shared.reconnectTimer = nil
//        }
//        
//        // 销毁设备扫描定时器
//        if let scanTimer = WindRingDeviceService.shared.scanTimer {
//            scanTimer.invalidate()
//            WindRingDeviceService.shared.scanTimer = nil
//        }
//        
//        // 销毁数据同步定时器
//        if let syncTimer = MQTTSyncService.shared.syncTimer {
//            syncTimer.invalidate()
//            MQTTSyncService.shared.syncTimer = nil
//        }
//        
//        // 销毁版本检查定时器
//        if let versionCheckTimer = VersionUpdateService.shared.versionCheckTimer {
//            versionCheckTimer.invalidate()
//            VersionUpdateService.shared.versionCheckTimer = nil
//        }
        
        print("⏱️ 已销毁所有定时器")
    }
}
