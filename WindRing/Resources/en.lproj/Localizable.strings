/* Common */
"app_name" = "SiRing";
"save" = "Save";
"cancel" = "Cancel";
"confirm" = "Confirm";
"back" = "Back";
"next" = "Next";
"done" = "Done";
"ok" = "OK";
"loading" = "Loading...";
"error" = "Error";
"success" = "Success";

/* Main Tabs */
"insight" = "Insight";
"review" = "Review";
"mine" = "Mine";

/* Profile */
"user_information" = "User Information";
"profile_picture" = "Profile Picture";
"id" = "ID";
"username" = "Username";
"birthday" = "Birthday";
"gender" = "Gender";
"height" = "Height";
"weight" = "Weight";
"not_set" = "Not set";
"male" = "Male";
"female" = "Female";
"secrecy" = "Secrecy";
"log_out" = "Log out";
"log_out_confirm" = "Are you sure you want to log out?";
"log_out_success" = "Logged out successfully";

/* Account */
"login" = "Login";
"register" = "Register";
"forgot_password" = "Forgot Password";
"email" = "Email";
"password" = "Password";
"confirm_password" = "Confirm Password";
"verification_code" = "Verification Code";
"send_code" = "Send Code";
"mobile" = "Mobile";
"finish" = "finish";
/* Health */
"history_data" = "History Data";
"sleep" = "Sleep";
"activity" = "Activity";
"stress" = "Stress";
"vital_signs" = "Vital Signs";
"heart_rate" = "Heart Rate";
"hrv" = "HRV";
"body_temperature" = "Body Temperature";
"blood_oxygen" = "Blood Oxygen";
"steps" = "Steps";
"calories" = "Calories";
"standing" = "Standing";
"deep_sleep" = "Deep Sleep";
"light_sleep" = "Light Sleep";
"rem_sleep" = "REM Sleep";
"awake" = "Awake";
"hours" = "Hours";
"minutes" = "Minutes";
"hr" = "hr";
"min" = "min";
"score" = "Score";

/* Device */
"device" = "Device";
"connection_status" = "Connection Status";
"connected" = "Connected";
"disconnected" = "Disconnected";
"connecting" = "Connecting...";
"start_pairing" = "Start Pairing";
"bluetooth_settings" = "Bluetooth Settings";
"search_device" = "Search Device";
"bind_device" = "Bind Device";
"unbind_device" = "Unbind Device";
"battery_level" = "Battery Level";
"firmware_version" = "Firmware Version";
"serial_number" = "Serial Number";
"sync_data" = "Sync Data";
"syncing" = "Syncing...";

/* Settings */
"settings" = "Settings";
"language" = "Language";
"language_settings" = "Language Settings";
"english" = "English";
"chinese" = "中文";
"units" = "Units";
"metric" = "Metric";
"imperial" = "Imperial";
"notification" = "Notification";
"about" = "About";
"privacy_policy" = "Privacy Policy";
"terms_of_service" = "Terms of Service";
"version" = "Version";
"goal_setting" = "Goal Setting";
"usage_guide" = "Usage Guide";
"versioning" = "Versioning";
"other_settings" = "Other Settings";
"sales_support" = "Sales Support";
"about_us" = "About Us";
"developer_tools" = "Developer Tools";
"settings_unit_format" = "Unit Format";
"settings_temp_unit" = "Temperature Unit";
"settings_sedentary_reminder" = "Sedentary Reminder";
"settings_low_battery_alert" = "Low Battery Alert";
"settings_restore_factory" = "Restore Factory Settings";
"settings_shutdown" = "Shutdown";

/* Common phrases */
"health_data_explanation" = "Hello, gender, height, weight, and birthday will be used for personalized calculation of calorie consumption, heart rate range during exercise, and other indicators. This information is only used to provide you with more accurate data.";
"modified_successfully" = "Modified successfully!";
"bind_success" = "Device bound successfully!";
"unbind_success" = "Device unbound successfully!";
"upload_success" = "Upload successful!";
"download_success" = "Download successful!";
"set_language_restart" = "Language settings have been changed. Some changes may require restarting the app.";
"data_privacy_notice" = "Your data is stored securely and we respect your privacy.";

/* Notification Settings */
"notification_settings_title" = "Notification Settings";
"enable_notifications" = "Enable Notifications";
"sound_alerts" = "Sound Alerts";
"vibration" = "Vibration";
"notification_types" = "Notification Types";
"health_alerts" = "Health Alerts";
"sleep_alerts" = "Sleep Alerts";
"activity_alerts" = "Activity Alerts";
"system_notifications" = "System Notifications";

/* Privacy Settings */
"privacy_settings_title" = "Privacy Settings";
"data_privacy" = "Data Privacy";
"location_tracking" = "Location Tracking";
"health_data_collection" = "Health Data Collection";
"usage_analytics" = "Usage Analytics";
"account_security" = "Account Security";
"current_password" = "Current Password";
"new_password" = "New Password";
"confirm_new_password" = "Confirm New Password";
"update_password" = "Update Password";
"data_management" = "Data Management";
"clear_cache" = "Clear Cache";
"delete_all_data" = "Delete All Data";

/* Alerts */
"alert_select_temp_unit_title" = "Select Temperature Unit";
"alert_change_units_title" = "Change Units";
"alert_change_units_message" = "Are you sure you want to change the measurement units? This will affect how data is displayed throughout the app.";
"alert_change_temp_unit_title" = "Change Temperature Unit";
"alert_change_temp_unit_message" = "Are you sure you want to change the temperature unit? This will affect how temperature is displayed throughout the app.";
"alert_sedentary_reminder_title" = "Sedentary Reminder";
"alert_sedentary_reminder_message" = "Enabling this feature will remind you when you've been sitting too long. Would you like to enable it?";
"alert_low_battery_title" = "Low Battery Alert";
"alert_low_battery_message" = "Enabling this feature will notify you when your device battery is low. Would you like to enable it?";
"alert_factory_reset_title" = "Factory Reset";
"alert_factory_reset_message" = "This operation will erase all data in your ring and disconnect the Bluetooth connection. You will need to reconnect the Bluetooth.";
"alert_power_off_title" = "Power Off";
"alert_power_off_message" = "Are you sure you want to power off your device? You will need to manually turn it on again.\n（If you want to restart the ring, just let it charge）";

/* System Settings */
"system_settings_measurement_units" = "Measurement Units";
"system_settings_temp_units" = "Temperature Units";
"system_settings_sedentary_reminder" = "Sedentary Reminder";
"system_settings_low_battery_alert" = "Low Battery Alert";
"system_settings_factory_reset" = "Factory Reset";
"system_settings_power_off" = "Power Off";

/* Help & Support */
"help_support_title" = "Help & Support";
"help_search_placeholder" = "Search help topics";
"help_faq" = "Frequently Asked Questions";
"help_faq_q1" = "How to pair my ring?";
"help_faq_a1" = "Open the SiRing app, go to the \"Mine\" page, and tap the \"Start Pairing\" button. Make sure your ring is powered on and in pairing mode, then follow the on-screen instructions to complete the pairing.";
"help_faq_q2" = "What if my health data doesn't sync?";
"help_faq_a2" = "Please ensure your ring is connected and has sufficient battery. You can try restarting the app or the ring. If the problem persists, you can try re-pairing your device in the settings.";
"help_faq_q3" = "How to check my sleep data?";
"help_faq_a3" = "In the \"Insight\" tab on the app's home page, select the \"Sleep\" option to view detailed sleep data, including sleep stages, sleep quality scores, and more.";
"help_faq_q4" = "How to update the ring firmware?";
"help_faq_a4" = "The app will automatically prompt you to update when new firmware is available. You can also manually check for updates in \"Device Settings\" on the \"Mine\" page.";
"help_faq_q5" = "How long is the battery life?";
"help_faq_a5" = "The SiRing smart ring has a battery life of about 5-7 days under normal use, depending on your usage frequency and enabled features.";
"help_contact_support" = "Contact Support";
"help_send_email" = "Send Email";
"help_hotline" = "Customer Service Hotline";

/* Developer Tools */
"dev_tools_db_debugging" = "Database Debugging";
"dev_tools_api_testing" = "API Testing";
"dev_tools_device_testing" = "Device Testing";
"dev_tools_sensor_testing" = "Sensor Testing";
"dev_tools_log_viewer" = "Log Viewer";
"dev_tools_performance_testing" = "Performance Testing";
"dev_tools_quick_login" = "Quick Test Login";
"dev_tools_hr_flow_test_subtitle" = "Test heart rate data acquisition, storage, upload, download, and display";

/* Login & Register */
"login_welcome_title" = "HELLO,\nWelcome to use!";
"login_phone_placeholder" = "Please enter your phone number";
"login_password_placeholder" = "Please enter your password";
"login_email_placeholder" = "Please enter your email";
"login_code_placeholder" = "Enter verification code";
"login_send_code" = "Send";
"login_retrieve_password" = "Retrieve Password";
"login_button_text" = "Login";
"login_register_account" = "Register a new account";
"login_terms_agreement" = "By logging in, you agree to the Terms of Use and Privacy Policy";
"login_failed_title" = "Login Failed";
"login_mobile_tab_title" = "Mobile";
"login_email_tab_title" = "Email";
"login_sms_tab_title" = "Sms Login";
"login_error_agree_terms" = "Please agree to the Terms of Use and Privacy Policy first";
"login_error_invalid_phone" = "Please enter a valid phone number";
"login_error_enter_password" = "Please enter your password";
"login_error_invalid_email" = "Please enter a valid email";
"login_error_enter_code" = "Please enter the verification code";
"login_code_sent" = "Verification code sent";

/* Insight View */
"insight_health_status" = "Health Status";
"insight_health_overallRating" = "Overall Situation";
"insight_activity" = "Activity";
"insight_unit_steps" = "steps";
"insight_unit_kcal" = "kcal";
"insight_unit_min" = "min";
"insight_sleep" = "Sleep";
"insight_unit_hr" = "hr";
"insight_stress" = "Stress";
"insight_heart_rate" = "Heart rate";
"insight_unit_bpm" = "bpm";
"insight_connected_to_device" = "Connected to %@";
"insight_disconnect_alert_title" = "Disconnect Bluetooth";
"insight_disconnect_alert_message" = "Are you sure you want to disconnect from the device?";
"insight_disconnect_button_title" = "Disconnect";
"stress_state_relax" = "Relax";
"stress_state_normal" = "Normal";
"stress_state_stress" = "Stress";
"stress_state_high_stress" = "High Stress";
"stress_state_unknown" = "Unknown";
"no_data_available" = "No data";
"health_status_excellent" = "Excellent";
"health_status_good" = "Good";
"health_status_normal" = "Normal";
"health_status_fair" = "Fair";
"health_status_poor" = "Poor";
"health_metric_sleep" = "Sleep";
"health_metric_vital_signs" = "Vital signs";
"health_metric_overall_situation" = "Overall Situation";
"health_metric_activity" = "Activity";
"health_metric_stress" = "Stress";
"calendar_button_back_to_today" = "Back to Today";
"calendar_alert_future_date_title" = "Future Date Selected";
"calendar_alert_future_date_message" = "Data for %@ is not available yet.";

/* History View */
"history_footer_message" = "A healthy body and mind depend on developing good habits!";
"history_7day_average" = "recent 7-day average";
"history_no_detail_view" = "No detail view available";

/* User Profile View */
"user_profile_greeting_morning" = "Good Morning";
"user_profile_greeting_afternoon" = "Good Afternoon";
"user_profile_greeting_evening" = "Good Evening";
"user_profile_binding_state" = "Binding-State";
"user_profile_bound" = "Bound";
"user_profile_unbound" = "Unbound";
"user_profile_unbind_button" = "Unbind";
"user_profile_start_pairing_button" = "Start Pairing";
"user_profile_battery_info" = "%d%% charge, %d days since last charge ";
"user_profile_sharing_title" = "Sharing";
"user_profile_sharing_subtitle" = "Love begins with safeguarding health";
"user_profile_goal_setting" = "Goal Setting";
"user_profile_usage_guide" = "Usage Guide";
"user_profile_versioning" = "Versioning";
"user_profile_other_settings" = "Other Settings";
"user_profile_sales_support" = "Sales Support";
"user_profile_about_us" = "About Us";
"user_profile_dev_test_center" = "Developer Test Center";
"user_profile_device_settings" = "Device Settings";
"user_profile_disconnect_button" = "Disconnect";
"user_profile_about_my_ring" = "About My Ring";
"user_profile_dev_tools" = "Developer Tools";
"user_profile_db_debug_tools" = "Database Debugging Tools";
"user_profile_home_tab" = "Home";
"user_profile_health_tab" = "Health";
"user_profile_mine_tab" = "Mine";
"user_profile_guest_device_settings_hint" = "Access device settings without logging in";

/* Edit Profile View */
"edit_profile_title" = "Edit Profile";
"edit_profile_section_header" = "Profile Information";
"edit_profile_name_placeholder" = "Name";
"edit_profile_save_button" = "Save";

/* About Ring View */
"about_ring_title" = "About SiRing";
"about_ring_device_name" = "SiRing Device";
"about_ring_device_description" = "The SiRing is a next-generation smart wearable device designed to track your health metrics and provide actionable insights.";
"about_ring_key_features" = "Key Features";
"about_ring_feature_hr" = "Heart Rate Monitoring";
"about_ring_feature_hr_desc" = "Continuous heart rate tracking";
"about_ring_feature_sleep" = "Sleep Analysis";
"about_ring_feature_sleep_desc" = "Track sleep patterns and quality";
"about_ring_feature_activity" = "Activity Tracking";
"about_ring_feature_activity_desc" = "Count steps and activity levels";
"about_ring_feature_health_metrics" = "Health Metrics";
"about_ring_feature_health_metrics_desc" = "Advanced health data analysis";
"about_ring_tech_specs" = "Technical Specifications";
"about_ring_spec_battery" = "Battery Life";
"about_ring_spec_battery_value" = "Up to 7 days";
"about_ring_spec_water_resistance" = "Water Resistance";
"about_ring_spec_water_resistance_value" = "50 meters";
"about_ring_spec_sensors" = "Sensors";
"about_ring_spec_sensors_value" = "HR, Accelerometer, Gyroscope, Temperature";
"about_ring_spec_connectivity" = "Connectivity";
"about_ring_spec_connectivity_value" = "Bluetooth 5.0 LE";

/* Family Sharing View */
"family_sharing_title" = "Family Sharing";
"family_sharing_card_1_title" = "Share health data with family and friends";
"family_sharing_card_1_desc" = "You can safely share health data, care for each other's health together, and witness daily progress together!";
"family_sharing_card_2_title" = "Share through link";
"family_sharing_card_2_desc" = "You can choose to share your data with others or invite other users to share their health data with you, making you more closely connected with those you care about!";
"family_sharing_card_3_title" = "Data security is guaranteed";
"family_sharing_card_3_desc" = "You can choose to change the content of the shared data at any time, or stop sharing at any time!";
"family_sharing_agree_privacy" = "I have read and agree to the privacy policy";
"family_sharing_start_journey_button" = "Embark on a caring journey";
"family_sharing_alert_start_journey_title" = "Embark on a caring journey";
"family_sharing_alert_start_journey_message" = "You have agreed to the privacy policy, now you can start sharing health data with those you care about.";
"family_sharing_alert_privacy_title" = "Privacy Policy Agreement";
"family_sharing_alert_privacy_message" = "To start sharing your health data, you need to agree to our privacy policy first. Would you like to agree now?";
"agree" = "Agree";

/* Sharing View */
"sharing_title" = "Health Sharing";
"sharing_subtitle" = "Share your health data with family and friends";
"sharing_family_sharing_button" = "Family Sharing";
"sharing_personal_data_sharing_button" = "Personal Data Sharing";

/* Share Sheet View */
"share_sheet_title" = "【SiRing】Embark on a journey of caring for family and friends:";
"share_sheet_share_to" = "Share to";
"share_sheet_wechat_friends" = "WeChat Friends";
"share_sheet_wechat_favorites" = "WeChat Favorites";
"share_sheet_qq_friends" = "QQ Friends";
"share_sheet_qq_mail" = "QQ Mail";

/* Family Data Sharing View */
"family_data_sharing_title" = "Family Sharing";
"family_data_sharing_tab_family" = "Family data";
"family_data_sharing_tab_my_data" = "My data";
"family_data_sharing_prompt_invite" = "Go invite your relatives to share their data";
"family_data_sharing_prompt_let_know" = "Let my family know about my health condition";
"family_data_sharing_invite_button" = "Invite others to share";
"family_data_sharing_share_with_others_button" = "Share with others";
"family_data_sharing_dialog_title" = "We are about to invite other users to share their health data with you through the link, which is valid for 30 minutes!";

/* My Data View in Family Sharing */
"my_data_hr_abnormality" = "Heart Rate Abnormality";
"my_data_user_placeholder" = "User %d";
"my_data_unit_score" = "score";
"share_data_content_weekly_report" = "Weekly Report";

/* Share Data Content View */
"share_data_content_title" = "Share data content";
"share_data_content_description" = "Please select the data content you want to share, you can adjust the shared data content and end the sharing at any time";
"share_data_content_standard_data_header" = "Guiding Standard Data";
"share_data_content_sleep" = "Sleep";
"share_data_content_activity" = "Activity";
"share_data_content_stress" = "Stress";
"share_data_content_health_status" = "Health Status";
"share_data_content_report_header" = "Report";
"share_data_content_weekly_report" = "Weekly Report";
"share_data_content_link_notice" = "A one-time invitation link will be generated soon, with a validity period of 30 minutes. Any user who obtains this link can accept your invitation. Please take care to protect the security of the link.";
"percent_symbol" = "%";
"default_device_name" = "Device";
"loading_text" = "Loading...";
"unknown_device" = "Unknown device";

/* Activity Detail View */
"activity_detail_nav_title" = "Activity";
"activity_detail_score_title" = "Activity Score";
"activity_detail_unit_step" = "step";
"activity_detail_unit_steps" = "steps";
"activity_detail_unit_km" = "km";
"activity_detail_steps_card_title" = "Step Count";
"activity_detail_calories_card_title" = "Activity Calories";
"activity_detail_intensity_card_title" = "Activity Intensity";
"activity_detail_intensity_average" = "Average of Awake State";
"activity_detail_loading_intensity" = "Loading activity intensity data...";
"activity_intensity_vigorous" = "Vigorous";
"activity_intensity_moderate" = "Moderate";
"activity_intensity_low" = "Low";
"activity_intensity_inactive" = "Inactive";
"activity_detail_details_card_title" = "Activity Details";
"activity_detail_loading_details" = "Loading activity details...";
"activity_detail_error_loading_details" = "Failed to load activity details";
"activity_detail_retry_button" = "Retry";
"activity_detail_no_details_title" = "No Activity Details";
"activity_detail_no_details_message" = "No detailed step data recorded for this day";
"activity_detail_details_header_time" = "Time";
"activity_detail_details_header_steps" = "Steps";
"activity_detail_json_error_disconnected" = "Device not connected";
"activity_detail_json_error_connect_first" = "Please connect your ring first";
"activity_detail_json_loading" = "Fetching data from device...";
"activity_detail_json_error_conversion" = "JSON conversion failed";
"activity_detail_json_error_format" = "Data formatting failed";
"activity_detail_sdk_error_disconnected" = "Device is disconnected";
"activity_detail_sdk_error_timeout" = "Request timed out";
"activity_detail_sdk_error_busy" = "Device is busy, please try again later";
"activity_detail_sdk_error_interrupted" = "Operation interrupted";
"activity_detail_sdk_error_internal" = "Device internal error";
"activity_detail_sdk_error_other" = "Other error: %@";
"activity_detail_device_status_connected" = "Connected device: %@";
"activity_detail_device_status_unconnected" = "Device not connected";
"activity_detail_device_status_connecting" = "Connecting";

/* Sleep Detail View */
"sleep_detail_nav_title" = "Sleep";
"sleep_detail_score_title" = "Sleep Score";
"sleep_detail_score_info_title" = "Sleep Score Details";
"sleep_detail_score_info_content_format" = "Your sleep score of %d is rated as '%@'.";
"sleep_detail_score_info_breakdown" = "Breakdown:";
"sleep_detail_score_info_duration" = "Sleep duration: %d/100";
"sleep_detail_score_info_quality" = "Sleep quality: %d/100";
"sleep_detail_score_info_consistency" = "Sleep consistency: %d/100";
"sleep_detail_total_time_asleep" = "Total Time Asleep";
"sleep_detail_time_asleep" = "Time Asleep";
"sleep_detail_efficiency" = "Sleep Efficiency";
"sleep_detail_state_title" = "Sleep State";
"sleep_stage_awake" = "Awake";
"sleep_stage_rem" = "REM";
"sleep_stage_core" = "Core";
"sleep_stage_deep" = "Deep";
"sleep_stage_unknown" = "Unknown";
"sleep_stages_card_title" = "Sleep Stages";
"sleep_hr_card_title" = "Heart Rate(bpm)";
"sleep_hr_average" = "Average";
"sleep_hr_7day_average" = "Recent 7-night Average";
"sleep_data_loading" = "Loading...";
"sleep_no_hr_data" = "No heart rate data";
"sleep_retry_button" = "Retry";
"sleep_hr_legend_7day_average" = "Recent 7-night Average";
"sleep_hr_legend_standard" = "Sleep Standard HR";
"sleep_hrv_card_title" = "Heart Rate Variability(ms)";
"sleep_hrv_info_title" = "Sleep HRV";
"sleep_hrv_info_content" = "Heart Rate Variability (HRV) measures the variation in time between heartbeats. Higher HRV during sleep generally indicates better recovery and overall health. Low HRV may indicate stress or insufficient recovery.";
"sleep_no_hrv_data" = "No HRV data";
"sleep_spo2_card_title" = "SpO2(%)";
"sleep_spo2_info_title" = "Sleep SpO2";
"sleep_spo2_info_content" = "Blood Oxygen Saturation (SpO2) measures the oxygen level in your blood. During sleep, it's normal for SpO2 to fluctuate slightly. Consistently low SpO2 levels (e.g., below 90%) may indicate an issue and should be discussed with a healthcare professional.";
"sleep_spo2_loading" = "Loading SpO2 data...";
"sleep_no_spo2_data" = "No SpO2 data available.";
"sleep_spo2_legend_spo2" = "SpO2";
"sleep_spo2_legend_7day_avg" = "7-Day Avg";
"sleep_spo2_legend_optimal" = "Optimal";
"sleep_spo2_tip" = "During sleep, the overall trend of SpO2 is normal!";
"sleep_temp_card_title" = "Temperature (°C)";
"sleep_temp_baseline" = "Baseline";
"sleep_temp_differ" = "Differ";
"sleep_temp_tip" = "Body temperature is within the normal range!";
"sleep_error_no_sleep_data" = "No sleep data for this date";
"sleep_error_api_error_format" = "API error: %@";
"sleep_score_excellent" = "Excellent";
"sleep_score_very_good" = "Very Good";
"sleep_score_good" = "Good";
"sleep_score_fair" = "Fair";
"sleep_score_poor" = "Poor";
"sleep_error_no_temp_data" = "No temperature data for this date";
"sleep_error_unknown" = "Unknown error";
"sleep_tip1" = "Your deep sleep was excellent last night!";
"sleep_tip2" = "You need more sleep to help your body recover quickly!";
"sleep_tip3" = "Try to maintain a consistent sleep schedule.";
"sleep_select_date" = "Select Date";
"sleep_done_button" = "Done";
"sleep_history_title" = "Sleep History";
"sleep_close_button" = "Close";
"sleep_bt_devices_title" = "Bluetooth Devices";
"sleep_bt_connected_devices" = "Connected Devices";
"sleep_bt_available_devices" = "Available Devices";
"sleep_bt_status_connected" = "Connected";
"sleep_bt_status_connect" = "Connect";

/* Stress Detail View */
"stress_detail_nav_title" = "Stress";
"stress_detail_stress_value" = "Stress Value";
"stress_detail_latest_hrv" = "Latest HRV";
"stress_detail_daily_average_hrv" = "Daily average HRV";
"stress_detail_unit_ms" = "ms";
"stress_detail_hrv_explanation" = "Generally speaking, high HRV (heart rate variability) represents a better state of mind";
"stress_detail_measuring" = "Measuring...";
"stress_detail_start_measuring" = "Start Measuring";
"stress_detail_last_7_records" = "Last 7 records";
"stress_detail_no_records_available" = "No record data available";
"stress_detail_select_record_prompt" = "Select a record to see details";
"stress_state_relaxed" = "Relaxed";
"stress_state_mild" = "Mild Stress";
"stress_state_high" = "High Stress";
"error_invalid_url" = "Invalid URL";
"error_load_data_format" = "Failed to load data: %@";
"error_http_code_format" = "HTTP Error: %d";
"error_no_data_returned" = "No data returned";
"error_api_format" = "API Error: %@";
"error_parsing_format" = "Parsing Error: %@";
"error_load_records_format" = "Failed to load records: %@";
"error_data_format_incorrect" = "Incorrect data format";
"error_data_format" = "Data format error";
"error_hr_measurement_timeout" = "Heart rate measurement timed out, please try again";
"error_connect_device_first" = "Please connect your device first";
"error_wear_device_first" = "Please wear your device first";
"error_invalid_hr_reading" = "Failed to get a valid heart rate, please make sure the device is worn correctly and try again";

/* Vital Signs Detail View */
"vital_signs_detail_nav_title" = "Vital Signs";
"vital_signs_no_data_today" = "No vital signs data for today";
"vital_signs_wear_device_prompt" = "Wear the device to record data automatically, or tap the measure button to measure manually";
"vital_signs_status_title" = "Vitals Status";
"vital_signs_significant_outlier" = "Significant Outlier";
"vital_signs_unit_times" = "times";
"vital_signs_minor_outlier" = "Minor Outlier";
"vital_signs_level_high" = "High";
"vital_signs_level_normal" = "Normal";
"vital_signs_level_low" = "Low";
"vital_signs_metric_spo2" = "SpO2";
"vital_signs_latest" = "Latest";
"vital_signs_selected" = "Selected";
"vital_signs_high_intensity_activity" = "High-intensity activity";
"vital_signs_start_hr_measurement" = "Click to start measuring heart rate";
"vital_signs_latest_percent" = "Latest(%)";
"vital_signs_average_percent" = "Average(%)";
"vital_signs_spo2_normal" = "SpO2 level is normal";
"vital_signs_spo2_low" = "SpO2 level is low, please rest";
"vital_signs_spo2_very_low" = "SpO2 level is too low, please consult a doctor";
"vital_signs_start_spo2_measurement" = "Click to start measuring SpO2";
"vital_signs_latest_ms" = "Latest (ms)";
"vital_signs_range_max_ms" = "Range Max (ms)";
"vital_signs_average_ms" = "Average (ms)";
"vital_signs_range_min_ms" = "Range Min (ms)";
"vital_signs_hrv_range" = "HRV Range";
"vital_signs_heart_health_status_format" = "Heart Health: %@";
"vital_signs_stop_measurement" = "Stop Measurement";
"vital_signs_start_hrv_measurement" = "Click to start measuring HRV";
"vital_signs_measuring" = "Measuring...";
"vital_signs_keep_still_prompt" = "Please keep your finger still and avoid vigorous movement";
"vital_signs_temperature_celsius" = "Temperature (°C)";
"vitals_no_hr_data" = "No heart rate data";
"vitals_no_spo2_data" = "No SpO2 data";
"vitals_no_hrv_data" = "No HRV data";
"vitals_no_temp_data" = "No temperature data";
"vital_signs_range_min_ms" = "Range Min (ms)";
"hrv_advice_title" = "HRV Health Analysis";
"hrv_advice_current_status" = "Current Status:";
"hrv_advice_suggestion" = "Health Advice";
"hr_measurement_popup_title" = "Heart Rate Measurement";
"vital_signs_device_connected" = "Connected";
"vital_signs_device_disconnected" = "Disconnected";
"hr_measurement_latest_time" = "Latest %@";
"button_retry" = "Retry";
"hr_measurement_done" = "Done";

/* Sleep History View */
"sleep_history_nav_title" = "Sleep";
"sleep_history_time_range_week" = "Week";
"sleep_history_time_range_month" = "Month";
"sleep_history_time_range_year" = "Year";
"sleep_history_weekday_sun" = "Sun";
"sleep_history_weekday_mon" = "Mon";
"sleep_history_weekday_tue" = "Tue";
"sleep_history_weekday_wed" = "Wed";
"sleep_history_weekday_thu" = "Thu";
"sleep_history_weekday_fri" = "Fri";
"sleep_history_weekday_sat" = "Sat";
"sleep_history_month_w1" = "W1";
"sleep_history_month_w2" = "W2";
"sleep_history_month_w3" = "W3";
"sleep_history_month_w4" = "W4";
"sleep_history_month_w5" = "W5";
"sleep_history_year_jan" = "Jan";
"sleep_history_year_feb" = "Feb";
"sleep_history_year_mar" = "Mar";
"sleep_history_year_apr" = "Apr";
"sleep_history_year_may" = "May";
"sleep_history_year_jun" = "Jun";
"sleep_history_year_jul" = "Jul";
"sleep_history_year_aug" = "Aug";
"sleep_history_year_sep" = "Sep";
"sleep_history_year_oct" = "Oct";
"sleep_history_year_nov" = "Nov";
"sleep_history_year_dec" = "Dec";
"sleep_history_data_refresh_failed" = "Data refresh failed";
"sleep_history_metric_score" = "Sleep Score";
"sleep_history_metric_efficiency" = "Sleep Efficiency";
"sleep_history_metric_duration" = "Sleep Duration";
"sleep_history_metric_hr" = "Sleep Heart Rate";
"sleep_history_metric_hrv" = "Sleep HRV";
"sleep_history_metric_spo2" = "Sleep SpO2";
"sleep_history_metric_skin_temp" = "Sleep Skin Temperature";
"sleep_history_data_refresh_failed_for" = "%@ data refresh failed";
"sleep_history_load_failed_score" = "Sleep score data failed to load";
"sleep_history_load_failed_efficiency" = "Sleep efficiency data failed to load";
"sleep_history_load_failed_duration" = "Sleep duration data failed to load";
"sleep_history_load_failed_hr" = "Sleep heart rate data failed to load";
"sleep_history_load_failed_hrv" = "Sleep HRV data failed to load";
"sleep_history_load_failed_spo2" = "Sleep SpO2 data failed to load";
"sleep_history_load_failed_skin_temp" = "Sleep skin temperature data failed to load";
"sleep_history_load_failed_multiple" = "Failed to load data for: %@";
"sleep_history_section_title_score" = "Sleep Score";
"sleep_history_section_title_efficiency" = "AVG. sleep efficiency";
"sleep_history_section_title_duration" = "Total Time Asleep";
"sleep_history_section_title_hr" = "Sleep Heart Rate";
"sleep_history_section_title_hrv" = "Sleeping HRV";
"sleep_history_section_title_spo2" = "Sleeping SpO2";
"sleep_history_section_title_skin_temp" = "Sleep Skin Temperature";
"sleep_history_skin_temp_avg_deviation" = "Average Deviation";
"sleep_history_no_data_period" = "No data for this period";
"sleep_history_failed_to_load" = "Failed to load data";
"sleep_history_calendar_back_to_today" = "Back to Today";

/* Activity History View */
"activity_history_nav_title" = "Activity";
"activity_history_section_title_score" = "Total Activity Score";
"activity_history_metric_max" = "Max";
"activity_history_metric_ave" = "Ave";
"activity_history_metric_min" = "Mini";
"activity_history_unit_score" = "Score";
"activity_history_section_title_steps" = "Steps";
"activity_history_unit_steps" = "Steps";
"activity_history_section_title_calories" = "Activity Calories";
"activity_history_unit_kcal" = "Kcal";
"activity_history_section_title_standing" = "Standing Duration";
"activity_history_detail_sheet_close" = "Close";
"activity_history_detail_sheet_help" = "Help";
"activity_history_detail_sheet_maximum" = "Maximum";
"activity_history_detail_sheet_average" = "Average";
"activity_history_detail_sheet_minimum" = "Minimum";
"activity_history_detail_sheet_about" = "About %@";
"activity_history_detail_sheet_how_to_improve" = "How to improve";
"activity_history_detail_sheet_start_activity" = "Start Activity";
"activity_history_detail_sheet_no_info" = "Detailed information is not available.";
"activity_history_detail_sheet_no_suggestion" = "No specific suggestions.";
"activity_history_desc_title_score" = "Activity Score";
"activity_history_desc_title_steps" = "Daily Steps";
"activity_history_desc_title_distance" = "Daily Distance";
"activity_history_desc_title_calories" = "Active Calories";
"activity_history_desc_title_hr" = "Activity Heart Rate";
"activity_history_desc_title_zones" = "Activity Zones";
"activity_history_desc_content_score" = "The activity score is a comprehensive assessment of your daily activity level. It is calculated based on your step count, active time, exercise intensity, and frequency of activity. A high score indicates that you have met or exceeded the recommended daily activity level, which helps maintain a healthy state and improve physical fitness.";
"activity_history_desc_content_steps" = "Step count is an important indicator for measuring daily activity. Health experts recommend that most people walk at least 8,000-10,000 steps per day to maintain basic health. The step count includes all activities throughout the day, from daily walking to conscious exercise. Increasing your step count helps improve cardiovascular health and control weight.";
"activity_history_desc_content_distance" = "The total distance walked or run daily is an important indicator of activity level. This data, displayed in kilometers, reflects the physical distance you move each day. According to health recommendations, adults should walk at least 4-5 kilometers per day to maintain basic health.";
"activity_history_desc_content_calories" = "Active calories refer to the extra energy burned through exercise and daily activities (beyond a resting state). This does not include the calories your body burns to maintain basic functions (basal metabolic rate). Active calories are an important indicator for health management and weight control.";
"activity_history_desc_content_hr" = "Activity heart rate is the frequency of your heartbeats during physical activity. Different intensities of activity will cause your heart rate to rise to different levels. Monitoring your activity heart rate can help optimize training effects and ensure your exercise intensity is moderate and achieves the desired results.";
"activity_history_desc_content_zones" = "Activity zones are different exercise intensity zones based on a percentage of your maximum heart rate. Training in different heart rate zones can achieve different fitness goals: from improving basic endurance (zones 1-2) to increasing aerobic capacity (zone 3) and enhancing high-intensity performance (zones 4-5).";
"activity_history_improve_title_score" = "Activity Score";
"activity_history_improve_title_steps" = "Daily Steps";
"activity_history_improve_title_distance" = "Daily Distance";
"activity_history_improve_title_calories" = "Active Calories";
"activity_history_improve_title_hr" = "Activity Heart Rate";
"activity_history_improve_title_zones" = "Activity Zones";
"activity_history_improve_content_score" = "Set clear daily activity goals, such as a specific number of steps or active minutes.\nTry to incorporate more HIIT (High-Intensity Interval Training) to get greater activity benefits in less time.\nEnsure at least 30 minutes of moderate-intensity activity each day.\nReduce sedentary time by getting up and moving for a few minutes every hour.\nUse a fitness app or smartwatch to track and motivate yourself to stay active.";
"activity_history_improve_content_steps" = "Choose a parking spot farther away to increase walking distance.\nUse stairs instead of the elevator.\nTake a short walk during your lunch break.\nSchedule walking meetings instead of sitting meetings.\nSet reminders to get up and walk around every hour.\nConsider getting a treadmill to use while watching TV.";
"activity_history_improve_content_distance" = "Gradually increase your daily walking or running distance, by about 10% each week.\nExplore new walking or running routes to maintain interest and motivation.\nParticipate in virtual walking or running challenges and set monthly distance goals.\nSchedule longer walks or hikes on weekends.\nUse walking as a mode of transportation, choosing to walk instead of drive whenever possible.";
"activity_history_improve_content_calories" = "Combine aerobic exercise and strength training, as both are effective at burning calories.\nIncrease the intensity of your daily activities, such as brisk walking instead of slow strolling.\nTry interval training, which can continue to burn calories after your workout is over.\nEnsure adequate rest and recovery, which is crucial for maintaining an active lifestyle.\nPay attention to the balance between diet and exercise, and don't overeat just because you've burned calories.";
"activity_history_improve_content_hr" = "Use heart rate zone training to exercise at different intensities for comprehensive fitness benefits.\nPerform proper warm-ups and cool-downs to gradually raise and lower your heart rate.\nEngage in regular endurance training to improve heart efficiency and lower your resting heart rate.\nWatch for abnormally high or low activity heart rates, which may require consulting a doctor.\nEnsure adequate rest and sleep, which is essential for heart health.";
"activity_history_improve_content_zones" = "Use heart rate zone training for specific training goals: Zone 2 for basic endurance, Zone 3 for aerobic fitness, and Zones 4-5 for improving VO2 max.\nTry heart rate zone interval training, switching between different intensities to improve cardiorespiratory function.\nUse a smartwatch to monitor heart rate zones in real-time, ensuring appropriate training intensity.\nLog your weekly training time in each zone to ensure a balanced training load.\nAs your fitness level improves, adjust your heart rate zone settings to ensure continued progress.";

/* Stress History View */
"stress_history_nav_title" = "Stress";
"stress_history_section_title_score" = "Stress Score";
"stress_history_section_title_night_score" = "Night Stress Score";

/* Vital Signs History View */
"vitals_history_nav_title" = "Vital Signs";
"vitals_history_section_title_hr" = "Heart Rate";
"vitals_history_section_title_spo2" = "SpO2";
"vitals_history_section_title_hrv" = "HRV";
"vitals_history_button_retry" = "Retry";
"vitals_history_detail_sheet_close" = "Close";
"vitals_history_detail_sheet_more" = "More";
"vitals_history_detail_sheet_maximum" = "Maximum";
"vitals_history_detail_sheet_average" = "Average";
"vitals_history_detail_sheet_minimum" = "Minimum";
"vitals_history_detail_sheet_baseline" = "Baseline";
"vitals_history_detail_sheet_difference" = "Difference";
"vitals_history_detail_sheet_about" = "About %@";
"vitals_history_detail_sheet_suggestion" = "Suggestion";
"vitals_history_detail_sheet_start_management" = "Start Health Management";
"vitals_history_desc_title_hr" = "Heart Rate";
"vitals_history_desc_content_hr" = "Heart rate is the number of times the heart beats, usually expressed in beats per minute (bpm). A normal resting heart rate ranges from 60-100 bpm, and a lower resting heart rate usually indicates better heart health. Exercise, stress, illness, and medications can all cause heart rate to change.";
"vitals_history_desc_title_spo2" = "SpO2";
"vitals_history_desc_content_spo2" = "Blood oxygen saturation (SpO2) is the ratio of oxygen-carrying hemoglobin in the blood to total hemoglobin. A healthy person's blood oxygen saturation is usually between 95% and 100%. Readings below 90% may indicate a respiratory or circulatory problem and require medical attention.";
"vitals_history_desc_title_hrv" = "HRV";
"vitals_history_desc_content_hrv" = "Heart rate variability (HRV) is the variation in the time interval between consecutive heartbeats. A higher HRV usually indicates that the body has good adaptability and resilience, while a lower HRV may be related to stress, fatigue, or health problems.";
"vitals_history_desc_no_info" = "Detailed information about this metric is not yet available.";
"vitals_history_suggestion_title_hr" = "Heart Rate";
"vitals_history_suggestion_content_hr" = "Engage in regular aerobic exercise to improve heart health.\nAvoid excessive caffeine and alcohol intake.\nMaintain a healthy weight.\nManage stress and ensure adequate sleep.\nConsult a doctor if your heart rate is persistently abnormal or accompanied by other symptoms.";
"vitals_history_suggestion_title_spo2" = "SpO2";
"vitals_history_suggestion_content_spo2" = "Maintain good breathing habits and posture.\nAvoid prolonged exposure to heavily polluted air.\nIf you are a smoker, consider quitting.\nExercise regularly to improve lung function.\nConsult a medical professional if your blood oxygen saturation consistently falls below 95%.";
"vitals_history_suggestion_title_hrv" = "HRV";
"vitals_history_suggestion_content_hrv" = "Practice meditation and deep breathing to improve HRV.\nEnsure adequate, quality sleep.\nIncrease the frequency of aerobic exercise.\nEat a balanced diet and stay well-hydrated.\nManage stress and emotions.";
"vitals_history_suggestion_no_info" = "No specific suggestions are available for this metric at this time.";
"vitals_history_tooltip_max" = "Maximum: %@";
"vitals_history_tooltip_min" = "Minimum: %@";

/* Goal Setting View */
"goal_settings_title" = "Goal Settings";
"goal_sleep_title" = "Sleep Goals";
"goal_steps_title" = "Step Goals";
"goal_activity_calorie_title" = "Activity calorie\ngoal";
"goal_activity_duration_title" = "Activity duration \ngoal";
"goal_time_format" = "%d hr %d min";
"goal_calorie_format" = "%@kla";
"goal_activity_calorie_popup_title" = "Activity Calorie Goal";
"goal_activity_duration_popup_title" = "Activity Duration Goal";

/* Developer Tools in UserProfileView */
"user_profile_dev_tools_title" = "Developer Tools";
"user_profile_dev_step_test" = "Step Detail Data Test";
"user_profile_dev_hr_flow_test" = "Heart Rate Data Flow Test";
"user_profile_dev_hr_detail_test" = "Heart Rate Detail Data Test";
"user_profile_dev_hrv_detail_test" = "HRV Detail Data Test";
"user_profile_dev_db_tools" = "Database Debugging Tools";

/* Language Selection View */
"language_selection_title" = "Language";
"language_selection_simplified_chinese" = "Simplified Chinese";
"language_selection_english" = "English";

/* Register View */
"register_title" = "Register";
"register_button_get_code" = "Get Code";
"register_code_sent_success" = "Verification code sent successfully!";
"register_placeholder_mobile" = "Please enter your mobile number";
"register_placeholder_email" = "Please enter your email";
"register_placeholder_code" = "Enter verification code";
"register_error_title" = "Registration Failed";
"register_error_invalid_mobile" = "Please enter a valid mobile number";
"register_error_invalid_email" = "Please enter a valid email address";
"register_error_invalid_code" = "Please enter a valid verification code";
"register_alert_button_login" = "Go to Login";
"register_alert_button_continue" = "Continue Registering";
"register_validation_failed_title" = "Validation Failed";
"register_reset_password_failed_title" = "Reset Password Failed";

/* Personal Info View */
"personal_info_title" = "Personal Information";
"register_success_title" = "Registration Successful";
"personal_info_prompt" = "Please enter your personal information";
"personal_info_name_placeholder" = "Please enter your name";
"personal_info_height_placeholder" = "Enter your height";
"personal_info_weight_placeholder" = "Enter your weight";
"select_date" = "Select Date";
"unit_cm" = "cm";
"unit_kg" = "kg";
"unit_ft" = "ft";
"unit_lbs" = "lbs";
"unit_in" = "in";

/* Set Password View */
"set_password_title" = "Set Password";
"enter_password" = "Enter password";
"enter_again" = "enter again";
"password_placeholder" = "Please enter your password";
"confirm_password_placeholder" = "Please enter your password again";
"password_format_error" = "Password format error!";
"password_rules" = "The password must be 8-20 characters long and include at least one number, uppercase, and lowercase letter.";
"password_mismatch_error" = "Passwords do not match.";

/* Reset Password View */
"reset_password_button_title" = "Reset Password";
"back_to_login_button" = "Back to Login";
"reset_password_success_title" = "Password Reset Successfully";
"reset_password_success_message" = "Your password has been reset successfully. You can now login with your new password.";
"reset_password_failed_title" = "Password Reset Failed";
"placeholder_new_password" = "Enter new password";
"placeholder_confirm_password" = "Confirm password";

/* User Guide View */
"user_guide_title" = "Usage Guide";
"user_guide_app" = "App";
"user_guide_ring" = "Ring";
"user_guide_account_password" = "Account Password";

"app_guide_title" = "APP Usage Guide";
"app_guide_logout" = "Logout User";
"app_guide_logout_content" = "Content 1";
"app_guide_bind_ring" = "Bind Ring";
"app_guide_bind_ring_content" = "Content 2";
"app_guide_unbind_ring" = "Unbind Ring";
"app_guide_unbind_ring_content" = "Content 3";

"ring_guide_title" = "Ring Usage Guide";
"ring_guide_getting_started" = "Getting Started";
"ring_guide_getting_started_content" = "Content 1";
"ring_guide_button_operations" = "Button Operations";
"ring_guide_button_operations_content" = "Content 2";
"ring_guide_function_menu" = "Function Menu";
"ring_guide_function_menu_content" = "Content 3";

"account_guide_title" = "Account Password Guide";
"account_guide_register" = "Register Account";
"account_guide_register_content" = "Content 1";
"account_guide_login_methods" = "Login Methods";
"account_guide_login_methods_content" = "Content 2";
"account_guide_password_security" = "Password Security";
"account_guide_password_security_content" = "Content 3";

/* Sales Support View */
"sales_support_title" = "Feedback";
"sales_support_record_button" = "Record";
"sales_support_feedback_type_prompt" = "Please check the type of feedback you want to provide";
"sales_support_channel_prompt" = "Please let us know through which channel you purchased the ring";
"sales_support_channel_tip" = "Please fill in the accurate channel so that we can solve your problem more effectively";
"sales_support_content_prompt" = "Feedback Content";
"sales_support_abnormal_start_time_prompt" = "Abnormal start time";
"sales_support_start_time_placeholder" = "Start time";
"sales_support_abnormal_end_time_prompt" = "Abnormal end time";
"sales_support_end_time_placeholder" = "End time";
"sales_support_image_upload_prompt" = "Upload images (up to 6 images)";
"sales_support_submit_button" = "Submit";
"sales_support_select_channel_placeholder" = "Select Channel";
"sales_support_feedback_content_placeholder" = "Please provide a detailed description of your problem or suggestion, and we will reply as soon as possible via email.";
"sales_support_feedback_type_placeholder" = "Please select feedback type";
"sales_support_channel_official" = "Official Website";
"sales_support_channel_amazon" = "Amazon";
"sales_support_channel_kickstarter" = "Kickstarter";
"sales_support_channel_offline" = "Offline";
"sales_support_channel_indiegogo" = "Indiegogo";
"sales_support_channel_other" = "Other";
"sales_support_alert_title" = "Incomplete Information";
"sales_support_alert_type_missing" = "Please select a feedback type.";
"sales_support_alert_channel_missing" = "Please select a purchase channel.";
"sales_support_alert_content_missing" = "Please enter your feedback.";
"sales_support_submit_success" = "Feedback submitted successfully!";
"sales_support_submit_failure" = "Failed to submit feedback. Please try again.";

/* Sales Support New Fields */
"sales_support_contact_name_prompt" = "How do I address you";
"sales_support_contact_name_placeholder" = "Please enter your name or nickname";
"sales_support_contact_email_prompt" = "Your email address";
"sales_support_contact_email_placeholder" = "Please enter your email address";
"sales_support_record_button_icon" = "list.bullet.rectangle";

"about_us_app_name" = "SiRing";
"about_us_version" = "Current version %@";
"about_us_terms_of_use" = "Terms Of Use";
"about_us_privacy_policy" = "Privacy Policy";
"about_us_information_list" = "Information list";
"about_us_contact_us" = "Contact Us";
"about_us_email" = "<EMAIL>";
"about_us_icp" = "Guangdong ICP No. 202402222290-4A";
"about_us_company" = "Guangzhou Zhi Wang Technology Co., Ltd";
"about_us_copyright" = "Copyright ©2021-2024 juzhi ALL WindRing Reserved";
"about_us_title" = "Terms Of Use";

"terms_title" = "Terms and Conditions";
"terms_last_updated" = "Last updated: March 25, 2024";
"terms_intro" = "Please read these terms and conditions carefully before using the WindRing application.";
"terms_acceptance_title" = "1. Acceptance of Terms";
"terms_acceptance_content" = "By downloading, installing, or using the WindRing application, you agree to be bound by these Terms and Conditions. If you do not agree to these Terms, you should not use the application.";
"terms_use_title" = "2. Use of the Application";
"terms_use_content_1" = "WindRing grants you a limited, non-exclusive, non-transferable, revocable license to use the application for your personal, non-commercial purposes. You may not use the application for any illegal or unauthorized purpose.";
"terms_use_content_2" = "You agree not to modify, adapt, translate, reverse engineer, decompile, or disassemble the application or any part thereof.";
"terms_privacy_title" = "3. User Data and Privacy";
"terms_privacy_content" = "Your use of the application is also governed by our Privacy Policy, which is incorporated by reference into these Terms and Conditions.";
"terms_ip_title" = "4. Intellectual Property";
"terms_ip_content" = "The application, including all content, features, and functionality, is owned by Guangzhou Zhi Wang Technology Co., Ltd and is protected by international copyright, trademark, patent, trade secret, and other intellectual property laws.";
"terms_liability_title" = "5. Limitation of Liability";
"terms_liability_content" = "To the maximum extent permitted by law, in no event shall WindRing, its affiliates, or their licensors, service providers, employees, agents, officers, or directors be liable for damages of any kind, under any legal theory, arising out of or in connection with your use of the application.";
"terms_changes_title" = "6. Changes to Terms";
"terms_changes_content" = "We reserve the right to modify these Terms at any time. We will provide notice of any material changes by updating the \"Last Updated\" date at the top of these Terms.";
"terms_contact_title" = "7. Contact Information";
"terms_contact_content" = "If you have any questions about these Terms, please contact <NAME_EMAIL>.";

"privacy_policy_title" = "Privacy Policy";
"privacy_policy_last_updated" = "Last updated: March 25, 2024";
"privacy_policy_intro" = "This Privacy Policy describes how WindRing collects, uses, and discloses your personal information when you use our application.";
"privacy_policy_collection_title" = "1. Information We Collect";
"privacy_policy_collection_content" = "We collect several types of information from and about users of our application, including:";
"privacy_policy_collection_item_1" = "Personal information: name, email address, and contact details when you register";
"privacy_policy_collection_item_2" = "Health and fitness data: heart rate, sleep patterns, activity levels";
"privacy_policy_collection_item_3" = "Device information: device type, operating system, unique device identifiers";
"privacy_policy_collection_item_4" = "Usage data: how you interact with our application";
"privacy_policy_use_title" = "2. How We Use Your Information";
"privacy_policy_use_content" = "We use the information we collect to:";
"privacy_policy_use_item_1" = "Provide, maintain, and improve our services";
"privacy_policy_use_item_2" = "Process and complete transactions";
"privacy_policy_use_item_3" = "Send you technical notices and support messages";
"privacy_policy_use_item_4" = "Respond to your comments and questions";
"privacy_policy_use_item_5" = "Develop new products and services";
"privacy_policy_storage_title" = "3. Data Storage and Security";
"privacy_policy_storage_content" = "We implement appropriate technical and organizational measures to protect your personal information against unauthorized or unlawful processing, accidental loss, destruction, or damage.";
"privacy_policy_sharing_title" = "4. Data Sharing and Disclosure";
"privacy_policy_sharing_content" = "We may share your personal information with:";
"privacy_policy_sharing_item_1" = "Service providers who perform services on our behalf";
"privacy_policy_sharing_item_2" = "Business partners with your consent";
"privacy_policy_sharing_item_3" = "Legal authorities when required by law";
"privacy_policy_rights_title" = "5. Your Rights";
"privacy_policy_rights_content" = "Depending on your location, you may have certain rights regarding your personal information, including:";
"privacy_policy_rights_item_1" = "Access to your personal information";
"privacy_policy_rights_item_2" = "Correction of inaccurate information";
"privacy_policy_rights_item_3" = "Deletion of your information";
"privacy_policy_rights_item_4" = "Restriction of processing";
"privacy_policy_rights_item_5" = "Data portability";
"privacy_policy_changes_title" = "6. Changes to This Privacy Policy";
"privacy_policy_changes_content" = "We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the \"Last Updated\" date.";
"privacy_policy_contact_title" = "7. Contact Us";
"privacy_policy_contact_content" = "If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.";

"info_list_version_history_title" = "Version History";
"info_list_features_title" = "Features";
"info_list_specs_title" = "Technical Specifications";
"info_list_version_format" = "Version %@";
"info_list_feature_1_title" = "Health Monitoring";
"info_list_feature_1_desc" = "Track heart rate, sleep quality, and activity levels";
"info_list_feature_2_title" = "Smart Notifications";
"info_list_feature_2_desc" = "Receive alerts for calls, messages, and apps";
"info_list_feature_3_title" = "Workout Tracking";
"info_list_feature_3_desc" = "Monitor various exercise types with detailed metrics";
"info_list_feature_4_title" = "Water Resistance";
"info_list_feature_4_desc" = "IP68 rated for water and dust protection";
"info_list_feature_5_title" = "Battery Life";
"info_list_feature_5_desc" = "Up to 14 days of normal use on a single charge";
"info_list_spec_display_title" = "Display";
"info_list_spec_display_value" = "1.3\" AMOLED, 360x360 pixels";
"info_list_spec_sensors_title" = "Sensors";
"info_list_spec_sensors_value" = "Heart rate, Accelerometer, Gyroscope";
"info_list_spec_connectivity_title" = "Connectivity";
"info_list_spec_connectivity_value" = "Bluetooth 5.0, NFC";
"info_list_spec_battery_title" = "Battery";
"info_list_spec_battery_value" = "420mAh Li-ion";
"info_list_spec_water_resistance_title" = "Water Resistance";
"info_list_spec_water_resistance_value" = "5 ATM / IP68";
"info_list_spec_dimensions_title" = "Dimensions";
"info_list_spec_dimensions_value" = "45.4 x 45.4 x 11.8 mm";
"info_list_spec_weight_title" = "Weight";
"info_list_spec_weight_value" = "32g (without strap)";

/* Versioning View */
"versioning_software_version" = "Software version";
"versioning_app_version_format" = "APP version %@";
"versioning_firmware_version" = "Firmware version";
"versioning_equipment_name" = "Equipment Name";
"versioning_mac_address" = "MAC Address";
"versioning_last_version" = "Latest";
"versioning_update_check_failed" = "Update check failed";
"versioning_unknown_error" = "Unknown error";

/* Version Terms Of Use View */
"terms_of_use_title" = "WindRing Terms of Use";
"terms_of_use_last_updated_date_format" = "Last updated and effective date: January 12, 2023";
"terms_of_use_intro_title" = "Introduction";
"terms_of_use_intro_content" = "These Terms of Use (\"this Agreement\") describe the terms and conditions applicable to your access and use of the WindRing services, including WindRing smart ring devices (\"Products\"), applications (\"App\"), software, APIs, emails, newsletters, and services.\n\nThese Terms of Use (\"this Agreement\") describe the terms and conditions applicable to your access and use of the WindRing services, including WindRing smart ring devices (\"Products\"), applications (\"App\"), software, APIs, emails, newsletters, and services.\n\nThese Terms of Use (\"this Agreement\") describe the terms and conditions applicable to your access and use of the WindRing services, including WindRing smart ring devices (\"Products\"), applications (\"App\"), software, APIs, emails, newsletters, and services.";

/* Feedback History */
"feedback_history_title" = "Versioning";
"feedback_history_empty" = "No feedback history yet";
"feedback_status_replied" = "Replied";
"feedback_status_pending" = "Pending reply";

"feedback_detail_title" = "Feedback Details";
"feedback_status_replied_long" = "Replied";
"feedback_status_pending_long" = "Pending Reply";
"feedback_detail_time" = "Feedback Time";
"feedback_detail_type" = "Feedback Type";
"feedback_detail_channel" = "Purchase Channel";
"feedback_detail_content" = "Feedback Content";
"feedback_detail_address" = "Feedback Address";
"feedback_detail_images" = "Images";

"terms_of_use_privacy_title" = "Data Privacy";
"terms_of_use_privacy_content" = "These Terms of Use (\"this Agreement\") describe the terms and conditions applicable to your access and use of the WindRing services, including WindRing smart ring devices (\"Products\"), applications (\"App\"), software, APIs, emails, newsletters, and services.\n\nThese Terms of Use (\"this Agreement\") describe the terms and conditions applicable to your access and use of the WindRing services, including WindRing smart ring devices (\"Products\"), applications (\"App\"), software, APIs, emails, newsletters, and services.";

/* Version Privacy Policy View */
"privacy_policy_view_title" = "WindRing Privacy Policy";
"privacy_policy_last_updated_date_format" = "Last updated and effective date: January 12, 2023";
"privacy_policy_info_collection_title" = "Information Collection";
"privacy_policy_info_collection_content" = "We collect several types of information, including: personal information (such as name, email address), health and fitness data (such as heart rate, sleep patterns, activity levels), device information (device type, operating system), and usage data (how you interact with the app).\n\nWe only collect your personal data when necessary to provide and improve our services, process transactions, send technical notices and support messages, respond to your comments and questions, and develop new products and services.";
"privacy_policy_data_security_title" = "Data Security";
"privacy_policy_data_security_content" = "We implement appropriate technical and organizational measures to protect your personal information against unauthorized or unlawful processing, accidental loss, destruction, or damage. Your data is end-to-end encrypted, and only people you explicitly share with can access your information.\n\nWe may share your personal information with: service providers who perform services on our behalf, business partners with your consent, and legal authorities when required by law.";

/* Version Information List View */
"info_list_health_title" = "WindRing Health";
"info_list_health_description" = "WindRing Health (hereinafter referred to as \"we\") will strictly follow national laws and regulations, integrate legality and compliance into product design and business processes, and use industry-leading security technology to ensure data security. At the same time, we attach great importance to the protection of your personal information and privacy, and are fully committed to safeguarding everyone's right to privacy.";
"info_list_register_login_title" = "Register/Login";
"info_list_collect_personal_info" = "Personal Information Collected";
"info_list_register_login_info_content" = "Email address; phone number; WeChat account; QQ (Tim) account; user-filled information: avatar, nickname, date of birth, gender, height, weight;";
"info_list_business_scene" = "Business Scenario";
"info_list_register_login_scene_content" = "Create/login account";
"info_list_bind_device_title" = "Bind Device";
"info_list_bind_device_info_content" = "Device information: unique identifier, firmware version, etc.; location information;";
"info_list_bind_device_scene_content" = "Search for devices and bind them, activate, update, fix bugs, optimize products, or unbind devices";
"info_list_monitoring_title" = "Monitoring";
"info_list_monitoring_info_content" = "User account information, device-recorded information (steps);";
"info_list_monitoring_scene_content" = "Calculate/analyze exercise and health data";
"info_list_monitoring_reminder_title" = "Monitoring Reminder Function";
"info_list_monitoring_reminder_info_content" = "User account information, heart rate information, sedentary information, low battery information;";
"info_list_monitoring_reminder_scene_content" = "Wearing guide reminder, reminder for high heart rate, reminder for prolonged sitting, reminder for low ring battery";

/* Family Sharing */
"create_link_success_message" = "Create link success! You can jump to other app to share your link!";
"copy_the_link_button" = "Copy the link";

/* Date Selection View */
"date_selection_today" = "Today";

/* Device Pairing */
"device_add_ring_title" = "Add Ring";
"device_select_prompt" = "Please select your ring from the list below";
"device_not_found_prompt" = "Unable to find the ring, try charging it";
"loading_mac_address" = "Loading MAC...";
"default_ring_name" = "Windking Ring";
"mac_address_not_found" = "MAC address not found";
"device_searching_prompt" = "Searching for nearby rings...";
"bluetooth_permission_required_title" = "Bluetooth Permission Required";
"bluetooth_permission_required_message" = "Please allow Bluetooth access in Settings";
"go_to_settings" = "Go to Settings";
"error_connect_paired_device" = "Unable to connect to paired device. Please make sure the device is on and within range, or try restarting it.";
"error_connect_failed" = "Failed to connect to device, please try again";
"error_connect_failed_with_suggestion" = "Failed to connect to device. Please make sure the device is on and within range, then try again.";

/* Version Update */
"update_available_title" = "New Version Found";
"update_available_message" = "A new version %@ is available. We recommend updating now for a better experience.";
"error_api_business_error" = "API returned business error code: %@";
"error_no_version_info" = "Server did not return version information";

/* Glossary */
"glossary_title_history_sleep_score" = "Sleep Score";
"glossary_content_history_sleep_score" = "Sleep Score reflects your overall sleep quality on a scale of 0 to 100. It is based on your sleep duration, sleep efficiency, sleep stages, and number of nighttime awakenings. A higher score indicates better sleep.";
"glossary_title_health_status" = "Overall Situation";
"glossary_content_health_status" = "A composite score assessing your overall physical for the day, based on your daily sleep performance and activity levels.";
