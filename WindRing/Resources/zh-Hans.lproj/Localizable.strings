/* Common */
"app_name" = "SiRing";
"save" = "保存";
"cancel" = "取消";
"confirm" = "确认";
"back" = "返回";
"next" = "下一步";
"done" = "完成";
"ok" = "确定";
"loading" = "加载中...";
"error" = "错误";
"success" = "成功";

/* Main Tabs */
"insight" = "洞察";
"review" = "回顾";
"mine" = "我的";

/* Profile */
"user_information" = "用户信息";
"profile_picture" = "个人头像";
"id" = "账号ID";
"username" = "用户名";
"birthday" = "生日";
"gender" = "性别";
"height" = "身高";
"weight" = "体重";
"not_set" = "未设置";
"male" = "男";
"female" = "女";
"secrecy" = "保密";
"log_out" = "退出登录";
"log_out_confirm" = "您确定要退出登录吗？";
"log_out_success" = "退出登录成功";

/* Account */
"login" = "登录";
"register" = "注册";
"forgot_password" = "忘记密码";
"email" = "邮箱";
"password" = "密码";
"confirm_password" = "确认密码";
"verification_code" = "验证码";
"send_code" = "发送验证码";
"mobile" = "手机号";
"finish" = "完成";

/* Health */
"history_data" = "历史数据";
"sleep" = "睡眠";
"activity" = "活动";
"stress" = "压力";
"vital_signs" = "生命体征";
"heart_rate" = "心率";
"hrv" = "心率变异性";
"body_temperature" = "体温";
"blood_oxygen" = "血氧";
"steps" = "步数";
"calories" = "卡路里";
"standing" = "站立";
"deep_sleep" = "深度睡眠";
"light_sleep" = "浅度睡眠";
"rem_sleep" = "快速眼动睡眠";
"awake" = "清醒";
"hours" = "小时";
"minutes" = "分钟";
"hr" = "小时";
"min" = "分";
"score" = "评分";

/* Device */
"device" = "设备";
"connection_status" = "连接状态";
"connected" = "已连接";
"disconnected" = "未连接";
"connecting" = "连接中...";
"start_pairing" = "开始配对";
"bluetooth_settings" = "蓝牙设置";
"search_device" = "搜索设备";
"bind_device" = "绑定设备";
"unbind_device" = "解绑设备";
"battery_level" = "电池电量";
"firmware_version" = "固件版本";
"serial_number" = "序列号";
"sync_data" = "同步数据";
"syncing" = "同步中...";

/* Settings */
"settings" = "设置";
"language" = "语言";
"language_settings" = "语言设置";
"english" = "English";
"chinese" = "中文";
"units" = "单位";
"metric" = "公制";
"imperial" = "英制";
"notification" = "通知";
"about" = "关于";
"privacy_policy" = "隐私政策";
"terms_of_service" = "服务条款";
"version" = "版本";
"goal_setting" = "目标设置";
"usage_guide" = "使用指南";
"versioning" = "版本信息";
"other_settings" = "其他设置";
"sales_support" = "销售支持";
"about_us" = "关于我们";
"developer_tools" = "开发者工具";

/* Common phrases */
"health_data_explanation" = "您好，性别、身高、体重和生日将用于个性化计算卡路里消耗、运动过程中的心率范围以及其他指标。此信息仅用于为您提供更准确的数据。";
"modified_successfully" = "修改成功！";
"bind_success" = "设备绑定成功！";
"unbind_success" = "设备解绑成功！";
"upload_success" = "上传成功！";
"download_success" = "下载成功！";
"set_language_restart" = "语言设置已更改。某些更改可能需要重启应用才能生效。";
"data_privacy_notice" = "您的数据安全存储，我们尊重您的隐私。";

/* Insight View */
"insight_health_status" = "健康状态";
"insight_health_overallRating" = "综合评分";
"insight_activity" = "活动";
"insight_unit_steps" = "步";
"insight_unit_kcal" = "千卡";
"insight_unit_min" = "分钟";
"insight_sleep" = "睡眠";
"insight_unit_hr" = "小时";
"insight_stress" = "压力";
"insight_heart_rate" = "生命体征";
"insight_unit_bpm" = "次/分";
"insight_connected_to_device" = "已连接到 %@";
"insight_disconnect_alert_title" = "断开蓝牙连接";
"insight_disconnect_alert_message" = "确定要断开与设备的连接吗？";
"insight_disconnect_button_title" = "断开连接";
"stress_state_relax" = "放松";
"stress_state_normal" = "正常";
"stress_state_stress" = "有压力";
"stress_state_high_stress" = "高压力";
"stress_state_unknown" = "未知";
"no_data_available" = "暂无数据";
"health_status_excellent" = "优秀";
"health_status_good" = "良好";
"health_status_normal" = "正常";
"health_status_fair" = "一般";
"health_status_poor" = "较差";
"health_metric_sleep" = "睡眠";
"health_metric_vital_signs" = "生命体征";
"health_metric_overall_situation" = "综合情况";
"health_metric_activity" = "活动";
"health_metric_stress" = "压力";
"calendar_button_back_to_today" = "返回今天";
"calendar_alert_future_date_title" = "选择了未来日期";
"calendar_alert_future_date_message" = "%@ 的数据尚不可用。";

/* History View */
"history_footer_message" = "健康的身体和心灵取决于养成良好的习惯！";
"history_7day_average" = "最近7日平均";
"history_no_detail_view" = "无可用详情视图";

/* User Profile View */
"user_profile_greeting_morning" = "早上好";
"user_profile_greeting_afternoon" = "下午好";
"user_profile_greeting_evening" = "晚上好";
"user_profile_binding_state" = "绑定状态";
"user_profile_bound" = "已绑定";
"user_profile_unbound" = "未绑定";
"user_profile_unbind_button" = "解除绑定";
"user_profile_start_pairing_button" = "开始配对";
"user_profile_battery_info" = "%d%% 电量, 上次充电距今 %d 天 ";
"user_profile_sharing_title" = "共享";
"user_profile_sharing_subtitle" = "关爱从守护健康开始";
"user_profile_goal_setting" = "目标设置";
"user_profile_usage_guide" = "使用指南";
"user_profile_versioning" = "版本信息";
"user_profile_other_settings" = "其他设置";
"user_profile_sales_support" = "销售支持";
"user_profile_about_us" = "关于我们";
"user_profile_dev_test_center" = "开发者测试中心";
"user_profile_device_settings" = "设备设置";
"user_profile_disconnect_button" = "断开连接";
"user_profile_about_my_ring" = "关于我的戒指";
"user_profile_dev_tools" = "开发者工具";
"user_profile_db_debug_tools" = "数据库调试工具";
"user_profile_home_tab" = "首页";
"user_profile_health_tab" = "健康";
"user_profile_mine_tab" = "我的";
"user_profile_guest_device_settings_hint" = "无需登录即可访问设备设置";

/* Login & Register */
"login_welcome_title" = "您好，\n欢迎使用！";
"login_phone_placeholder" = "请输入手机号";
"login_password_placeholder" = "请输入密码";
"login_email_placeholder" = "请输入邮箱";
"login_code_placeholder" = "请输入验证码";
"login_send_code" = "发送";
"login_retrieve_password" = "找回密码";
"login_button_text" = "登录";
"login_register_account" = "注册新账号";
"login_terms_agreement" = "登录即表示您同意用户协议和隐私政策";
"login_failed_title" = "登录失败";
"login_mobile_tab_title" = "手机号";
"login_email_tab_title" = "邮箱";
"login_sms_tab_title" = "短信登录";
"login_error_agree_terms" = "请先同意用户协议和隐私政策";
"login_error_invalid_phone" = "请输入有效的手机号码";
"login_error_enter_password" = "请输入密码";
"login_error_invalid_email" = "请输入有效的邮箱地址";
"login_error_enter_code" = "请输入验证码";
"login_code_sent" = "验证码已发送";

"percent_symbol" = "%";
"default_device_name" = "设备";
"loading_text" = "正在加载...";
"unknown_device" = "未知设备";

/* Activity Detail View */
"activity_detail_nav_title" = "活动";
"activity_detail_score_title" = "活动评分";
"activity_detail_unit_step" = "步";
"activity_detail_unit_steps" = "步";
"activity_detail_unit_km" = "公里";
"activity_detail_steps_card_title" = "步数统计";
"activity_detail_calories_card_title" = "活动卡路里";
"activity_detail_intensity_card_title" = "活动强度";
"activity_detail_intensity_average" = "清醒状态平均值";
"activity_detail_loading_intensity" = "正在加载活动强度数据...";
"activity_intensity_vigorous" = "高强度";
"activity_intensity_moderate" = "中等强度";
"activity_intensity_low" = "低强度";
"activity_intensity_inactive" = "静止";
"activity_detail_details_card_title" = "活动详情";
"activity_detail_loading_details" = "正在加载活动明细数据...";
"activity_detail_error_loading_details" = "活动明细数据加载失败";
"activity_detail_retry_button" = "重试";
"activity_detail_no_details_title" = "暂无活动明细数据";
"activity_detail_no_details_message" = "本日没有记录详细步数数据";
"activity_detail_details_header_time" = "时间";
"activity_detail_details_header_steps" = "步数";
"activity_detail_json_error_disconnected" = "设备未连接";
"activity_detail_json_error_connect_first" = "请先连接戒指设备";
"activity_detail_json_loading" = "正在从设备获取数据...";
"activity_detail_json_error_conversion" = "JSON转换失败";
"activity_detail_json_error_format" = "数据格式化失败";
"activity_detail_sdk_error_disconnected" = "设备已断开连接";
"activity_detail_sdk_error_timeout" = "请求超时";
"activity_detail_sdk_error_busy" = "设备忙，请稍后再试";
"activity_detail_sdk_error_interrupted" = "操作被中断";
"activity_detail_sdk_error_internal" = "设备内部错误";
"activity_detail_sdk_error_other" = "其他错误: %@";
"activity_detail_device_status_connected" = "已连接设备: %@";
"activity_detail_device_status_unconnected" = "设备未连接";
"activity_detail_device_status_connecting" = "连接中";

/* Sleep Detail View */
"sleep_detail_nav_title" = "睡眠";
"sleep_detail_score_title" = "睡眠评分";
"sleep_detail_score_info_title" = "睡眠评分详情";
"sleep_detail_score_info_content_format" = "您的睡眠评分为 %d，评级为 \"%@\"。";
"sleep_detail_score_info_breakdown" = "细目分类:";
"sleep_detail_score_info_duration" = "睡眠时长: %d/100";
"sleep_detail_score_info_quality" = "睡眠质量: %d/100";
"sleep_detail_score_info_consistency" = "睡眠规律: %d/100";
"sleep_detail_total_time_asleep" = "总睡眠时长";
"sleep_detail_time_asleep" = "睡眠时长";
"sleep_detail_efficiency" = "睡眠效率";
"sleep_detail_state_title" = "睡眠状态";
"sleep_stage_awake" = "清醒";
"sleep_stage_rem" = "快速眼动";
"sleep_stage_core" = "核心睡眠";
"sleep_stage_deep" = "深睡";
"sleep_stage_unknown" = "未知";
"sleep_stages_card_title" = "睡眠阶段";
"sleep_hr_card_title" = "心率(次/分)";
"sleep_hr_average" = "平均值";
"sleep_hr_7day_average" = "近7晚平均";
"sleep_data_loading" = "加载中...";
"sleep_no_hr_data" = "无心率数据";
"sleep_retry_button" = "重试";
"sleep_hr_legend_7day_average" = "近7晚平均";
"sleep_hr_legend_standard" = "睡眠标准心率";
"sleep_hrv_card_title" = "心率变异性(ms)";
"sleep_hrv_info_title" = "睡眠心率变异性";
"sleep_hrv_info_content" = "心率变异性(HRV)衡量心跳之间时间间隔的变化。睡眠期间较高的HRV通常表示恢复得更好，整体健康状况更佳。较低的HRV可能表示压力或恢复不足。";
"sleep_no_hrv_data" = "无HRV数据";
"sleep_spo2_card_title" = "血氧饱和度(%)";
"sleep_spo2_info_title" = "睡眠血氧";
"sleep_spo2_info_content" = "血氧饱和度(SpO2)测量您血液中的氧气水平。在睡眠期间，SpO2轻微波动是正常的。持续较低的SpO2水平（例如低于90%）可能表示存在问题，应与医疗专业人员讨论。";
"sleep_spo2_loading" = "正在加载血氧数据...";
"sleep_no_spo2_data" = "无可用血氧数据。";
"sleep_spo2_legend_spo2" = "血氧饱和度";
"sleep_spo2_legend_7day_avg" = "7日平均";
"sleep_spo2_legend_optimal" = "最佳";
"sleep_spo2_tip" = "睡眠期间，血氧总体趋势正常！";
"sleep_temp_card_title" = "体温(°C)";
"sleep_temp_baseline" = "基线";
"sleep_temp_differ" = "差异";
"sleep_temp_tip" = "体温在正常范围内！";
"sleep_error_no_sleep_data" = "该日期无睡眠数据";
"sleep_error_api_error_format" = "API错误: %@";
"sleep_score_excellent" = "极好";
"sleep_score_very_good" = "很好";
"sleep_score_good" = "良好";
"sleep_score_fair" = "一般";
"sleep_score_poor" = "较差";
"sleep_error_no_temp_data" = "该日期无体温数据";
"sleep_error_unknown" = "未知错误";
"sleep_tip1" = "昨晚您的深度睡眠非常棒！";
"sleep_tip2" = "您需要更多睡眠来帮助身体快速恢复！";
"sleep_tip3" = "尽量保持规律的睡眠时间表。";
"sleep_select_date" = "选择日期";
"sleep_done_button" = "完成";
"sleep_history_title" = "睡眠历史";
"sleep_close_button" = "关闭";
"sleep_bt_devices_title" = "蓝牙设备";
"sleep_bt_connected_devices" = "已连接设备";
"sleep_bt_available_devices" = "可用设备";
"sleep_bt_status_connected" = "已连接";
"sleep_bt_status_connect" = "连接";

/* Notification Settings */
"notification_settings_title" = "通知设置";
"enable_notifications" = "启用通知";
"sound_alerts" = "声音提醒";
"vibration" = "振动";
"notification_types" = "通知类型";
"health_alerts" = "健康警报";
"sleep_alerts" = "睡眠警报";
"activity_alerts" = "活动警报";
"system_notifications" = "系统通知";

/* Privacy Settings */
"privacy_settings_title" = "隐私设置";
"data_privacy" = "数据隐私";
"location_tracking" = "位置跟踪";
"health_data_collection" = "健康数据收集";
"usage_analytics" = "使用分析";
"account_security" = "账户安全";
"current_password" = "当前密码";
"new_password" = "新密码";
"confirm_new_password" = "确认新密码";
"update_password" = "更新密码";
"data_management" = "数据管理";
"clear_cache" = "清除缓存";
"delete_all_data" = "删除所有数据";

/* Alerts */
"alert_select_temp_unit_title" = "选择温度单位";
"alert_change_units_title" = "更改单位";
"alert_change_units_message" = "您确定要更改测量单位吗？这将影响整个应用中的数据显示方式。";
"alert_change_temp_unit_title" = "更改温度单位";
"alert_change_temp_unit_message" = "您确定要更改温度单位吗？这将影响整个应用中的温度显示方式。";
"alert_sedentary_reminder_title" = "久坐提醒";
"alert_sedentary_reminder_message" = "启用此功能后，当您久坐过长时会提醒您。是否启用？";
"alert_low_battery_title" = "低电量提醒";
"alert_low_battery_message" = "启用此功能后，当您的设备电量不足时会通知您。是否启用？";
"alert_factory_reset_title" = "恢复出厂设置";
"alert_factory_reset_message" = "此操作将清除您戒指中的所有数据，并断开蓝牙连接，您需要重新连接蓝牙。";
"alert_power_off_title" = "关机";
"alert_power_off_message" = "您确定要关闭设备吗？您需要手动再次开启它。\n（如果你想重新启动戒指，就让它充电吧）";

/* System Settings */
"system_settings_measurement_units" = "测量单位";
"system_settings_temp_units" = "温度单位";
"system_settings_sedentary_reminder" = "久坐提醒";
"system_settings_low_battery_alert" = "低电量提醒";
"system_settings_factory_reset" = "恢复出厂设置";
"system_settings_power_off" = "关机";

/* Help & Support */
"help_support_title" = "帮助与支持";
"help_search_placeholder" = "搜索帮助主题";
"help_faq" = "常见问题";
"help_faq_q1" = "如何配对我的戒指？";
"help_faq_a1" = "打开SiRing应用，进入\"我的\"页面，点击\"开始配对\"按钮。确保您的戒指已开机并处于配对模式，然后按照屏幕上的指示完成配对。";
"help_faq_q2" = "如果我的健康数据不同步怎么办？";
"help_faq_a2" = "请确保您的戒指已连接且电量充足。您可以尝试重启应用或戒指。如果问题仍然存在，您可以在设置中尝试重新配对您的设备。";
"help_faq_q3" = "如何查看我的睡眠数据？";
"help_faq_a3" = "在应用首页的\"洞察\"选项卡中，选择\"睡眠\"选项即可查看详细的睡眠数据，包括睡眠阶段、睡眠质量得分等。";
"help_faq_q4" = "如何更新戒指固件？";
"help_faq_a4" = "当有新固件可用时，应用会自动提示您更新。您也可以在\"我的\"页面的\"设备设置\"中手动检查更新。";
"help_faq_q5" = "电池续航时间是多久？";
"help_faq_a5" = "SiRing智能戒指在正常使用情况下电池续航时间约为5-7天，具体取决于您的使用频率和启用的功能。";
"help_contact_support" = "联系支持";
"help_send_email" = "发送邮件";
"help_hotline" = "客服热线";

/* Family Sharing View */
"family_sharing_title" = "家人共享";
"family_sharing_card_1_title" = "与家人和朋友共享健康数据";
"family_sharing_card_1_desc" = "您可以安全地共享健康数据，共同关心彼此的健康，共同见证每日的进步！";
"family_sharing_card_2_title" = "通过链接共享";
"family_sharing_card_2_desc" = "您可以选择将您的数据共享给他人，或邀请其他用户与您共享他们的健康数据，让您与关心的人联系更紧密！";
"family_sharing_card_3_title" = "数据安全有保障";
"family_sharing_card_3_desc" = "您可以随时选择更改共享数据的内容，或随时停止共享！";
"family_sharing_agree_privacy" = "我已阅读并同意隐私政策";
"family_sharing_start_journey_button" = "开启关爱之旅";
"family_sharing_alert_start_journey_title" = "开启关爱之旅";
"family_sharing_alert_start_journey_message" = "您已同意隐私政策，现在可以开始分享健康数据给您关心的人了。";
"family_sharing_alert_privacy_title" = "隐私政策协议";
"family_sharing_alert_privacy_message" = "要开始共享您的健康数据，您需要先同意我们的隐私政策。您现在想同意吗？";
"agree" = "同意";

/* Family Data Sharing View */
"family_data_sharing_title" = "家庭数据共享";
"family_data_sharing_tab_family" = "家庭数据";
"family_data_sharing_tab_my_data" = "我的数据";
"family_data_sharing_prompt_invite" = "去邀请你的亲人共享他们的数据";
"family_data_sharing_prompt_let_know" = "让家人了解我的健康状况";
"family_data_sharing_invite_button" = "邀请他人共享";
"family_data_sharing_share_with_others_button" = "共享给他人";
"family_data_sharing_dialog_title" = "我们将通过链接邀请其他用户与您共享健康数据，链接有效期30分钟！";

/* My Data View in Family Sharing */
"my_data_hr_abnormality" = "心率异常";
"my_data_user_placeholder" = "用户 %d";
"my_data_unit_score" = "分";
"share_data_content_weekly_report" = "周报";

/* Stress Detail View */
"stress_detail_nav_title" = "压力";
"stress_detail_stress_value" = "压力值";
"stress_detail_latest_hrv" = "最新HRV";
"stress_detail_daily_average_hrv" = "每日平均HRV";
"stress_detail_unit_ms" = "毫秒";
"stress_detail_hrv_explanation" = "一般来说，高HRV（心率变异性）代表更好的心理状态";
"stress_detail_measuring" = "测量中...";
"stress_detail_start_measuring" = "开始测量";
"stress_detail_last_7_records" = "最近7条记录";
"stress_detail_no_records_available" = "没有可用的记录数据";
"stress_detail_select_record_prompt" = "选择记录查看详情";
"stress_state_relaxed" = "放松";
"stress_state_mild" = "轻度压力";
"stress_state_high" = "高压力";
"error_invalid_url" = "无效的URL";
"error_load_data_format" = "无法加载数据: %@";
"error_http_code_format" = "HTTP错误: %d";
"error_no_data_returned" = "没有返回数据";
"error_api_format" = "API错误: %@";
"error_parsing_format" = "解析错误: %@";
"error_load_records_format" = "无法加载记录: %@";
"error_data_format_incorrect" = "数据格式不正确";
"error_data_format" = "数据格式错误";
"error_hr_measurement_timeout" = "心率测量超时，请重试";
"error_connect_device_first" = "请先连接设备";
"error_wear_device_first" = "请先佩戴设备";
"error_invalid_hr_reading" = "无法获取有效心率，请确保设备正确佩戴并重试";

/* Vital Signs Detail View */
"vital_signs_detail_nav_title" = "生命体征";
"vital_signs_no_data_today" = "当日暂无生命体征数据";
"vital_signs_wear_device_prompt" = "佩戴设备可自动记录数据，或点击测量按钮手动测量";
"vital_signs_status_title" = "体征状态";
"vital_signs_significant_outlier" = "显著异常值";
"vital_signs_unit_times" = "次";
"vital_signs_minor_outlier" = "轻微异常值";
"vital_signs_level_high" = "高";
"vital_signs_level_normal" = "正常";
"vital_signs_level_low" = "低";
"vital_signs_metric_spo2" = "血氧";
"vital_signs_latest" = "最新";
"vital_signs_selected" = "选中";
"vital_signs_high_intensity_activity" = "高强度活动";
"vital_signs_start_hr_measurement" = "点击开始测量心率";
"vital_signs_latest_percent" = "最新(%)";
"vital_signs_average_percent" = "平均(%)";
"vital_signs_spo2_normal" = "血氧水平正常";
"vital_signs_spo2_low" = "血氧水平偏低，注意休息";
"vital_signs_spo2_very_low" = "血氧水平过低，请咨询医生";
"vital_signs_start_spo2_measurement" = "点击开始测量血氧";
"vital_signs_latest_ms" = "最新 (毫秒)";
"vital_signs_range_max_ms" = "范围最大值 (毫秒)";
"vital_signs_average_ms" = "平均 (毫秒)";
"vital_signs_range_min_ms" = "范围最小值 (毫秒)";
"vital_signs_hrv_range" = "HRV范围";
"vital_signs_heart_health_status_format" = "心脏健康状态: %@";
"vital_signs_stop_measurement" = "停止测量";
"vital_signs_start_hrv_measurement" = "点击开始测量HRV";
"vital_signs_measuring" = "测量中...";
"vital_signs_keep_still_prompt" = "请保持手指静止，避免剧烈运动";
"vital_signs_temperature_celsius" = "体温 (°C)";
"vitals_no_hr_data" = "暂无心率数据";
"vitals_no_spo2_data" = "暂无血氧数据";
"vitals_no_hrv_data" = "暂无HRV数据";
"vitals_no_temp_data" = "暂无体温数据";
"vital_signs_range_min_ms" = "范围最小值 (毫秒)";
"hrv_advice_title" = "HRV健康解析";
"hrv_advice_current_status" = "当前状态:";
"hrv_advice_suggestion" = "健康建议";
"hrv_advice_what_is_hrv" = "什么是HRV?";
"hrv_advice_explanation_1" = "心率变异性(HRV)是连续心跳之间时间间隔的变化。较高的HRV通常表示心脏对内外部刺激的反应能力更强，是良好的自主神经系统功能和更好的健康状态的标志。";
"hrv_advice_explanation_2" = "影响HRV的因素包括：年龄、体能状况、压力水平、睡眠质量、疾病和生活方式习惯。";
"hr_measurement_popup_title" = "心率测量";
"vital_signs_device_connected" = "已连接";
"vital_signs_device_disconnected" = "未连接";
"hr_measurement_latest_time" = "最近一次 %@";
"button_retry" = "重试";
"hr_measurement_done" = "完成";

/* Sleep History View */
"sleep_history_nav_title" = "睡眠";
"sleep_history_time_range_week" = "周";
"sleep_history_time_range_month" = "月";
"sleep_history_time_range_year" = "年";
"sleep_history_weekday_sun" = "周日";
"sleep_history_weekday_mon" = "周一";
"sleep_history_weekday_tue" = "周二";
"sleep_history_weekday_wed" = "周三";
"sleep_history_weekday_thu" = "周四";
"sleep_history_weekday_fri" = "周五";
"sleep_history_weekday_sat" = "周六";
"sleep_history_month_w1" = "W1";
"sleep_history_month_w2" = "W2";
"sleep_history_month_w3" = "W3";
"sleep_history_month_w4" = "W4";
"sleep_history_month_w5" = "W5";
"sleep_history_year_jan" = "一月";
"sleep_history_year_feb" = "二月";
"sleep_history_year_mar" = "三月";
"sleep_history_year_apr" = "四月";
"sleep_history_year_may" = "五月";
"sleep_history_year_jun" = "六月";
"sleep_history_year_jul" = "七月";
"sleep_history_year_aug" = "八月";
"sleep_history_year_sep" = "九月";
"sleep_history_year_oct" = "十月";
"sleep_history_year_nov" = "十一月";
"sleep_history_year_dec" = "十二月";
"sleep_history_data_refresh_failed" = "数据刷新失败";
"sleep_history_metric_score" = "睡眠评分";
"sleep_history_metric_efficiency" = "睡眠效率";
"sleep_history_metric_duration" = "睡眠时长";
"sleep_history_metric_hr" = "睡眠心率";
"sleep_history_metric_hrv" = "睡眠HRV";
"sleep_history_metric_spo2" = "睡眠血氧";
"sleep_history_metric_skin_temp" = "睡眠皮肤温度";
"sleep_history_data_refresh_failed_for" = "%@ 数据刷新失败";
"sleep_history_load_failed_score" = "睡眠评分数据加载失败";
"sleep_history_load_failed_efficiency" = "睡眠效率数据加载失败";
"sleep_history_load_failed_duration" = "睡眠时长数据加载失败";
"sleep_history_load_failed_hr" = "睡眠心率数据加载失败";
"sleep_history_load_failed_hrv" = "睡眠HRV数据加载失败";
"sleep_history_load_failed_spo2" = "睡眠血氧数据加载失败";
"sleep_history_load_failed_skin_temp" = "睡眠皮肤温度数据加载失败";
"sleep_history_load_failed_multiple" = "加载失败: %@";
"sleep_history_section_title_score" = "睡眠评分";
"sleep_history_section_title_efficiency" = "平均睡眠效率";
"sleep_history_section_title_duration" = "总睡眠时长";
"sleep_history_section_title_hr" = "睡眠心率";
"sleep_history_section_title_hrv" = "睡眠HRV";
"sleep_history_section_title_spo2" = "睡眠血氧";
"sleep_history_section_title_skin_temp" = "睡眠皮肤温度";
"sleep_history_skin_temp_avg_deviation" = "平均偏差";
"sleep_history_no_data_period" = "该时段暂无数据";
"sleep_history_failed_to_load" = "数据加载失败";
"sleep_history_calendar_back_to_today" = "返回今天";

/* Activity History View */
"activity_history_nav_title" = "活动";
"activity_history_section_title_score" = "总活动得分";
"activity_history_metric_max" = "最大";
"activity_history_metric_ave" = "平均";
"activity_history_metric_min" = "最小";
"activity_history_unit_score" = "分";
"activity_history_section_title_steps" = "步数";
"activity_history_unit_steps" = "步";
"activity_history_section_title_calories" = "活动卡路里";
"activity_history_unit_kcal" = "千卡";
"activity_history_section_title_standing" = "站立时长";
"activity_history_detail_sheet_close" = "关闭";
"activity_history_detail_sheet_help" = "帮助";
"activity_history_detail_sheet_maximum" = "最大值";
"activity_history_detail_sheet_average" = "平均值";
"activity_history_detail_sheet_minimum" = "最小值";
"activity_history_detail_sheet_about" = "关于 %@";
"activity_history_detail_sheet_how_to_improve" = "如何改善";
"activity_history_detail_sheet_start_activity" = "开始活动";
"activity_history_detail_sheet_no_info" = "详细信息暂不可用。";
"activity_history_detail_sheet_no_suggestion" = "暂无特定建议。";
"activity_history_desc_title_score" = "活动得分";
"activity_history_desc_title_steps" = "每日步数";
"activity_history_desc_title_distance" = "每日距离";
"activity_history_desc_title_calories" = "活动卡路里";
"activity_history_desc_title_hr" = "活动心率";
"activity_history_desc_title_zones" = "活动区间";
"activity_history_desc_content_score" = "活动得分是对您每日活动水平的综合评估。它基于您的步数、活跃时间、运动强度和活动频率计算得出。高分表示您达到或超过了推荐的日常活动水平，这有助于保持健康状态和提高体能。";
"activity_history_desc_content_steps" = "步数是衡量日常活动量的重要指标。健康专家建议大多数人每天至少走8,000-10,000步以维持基本健康。步数统计包括全天的各种活动，从日常走动到有意识的运动。增加步数有助于改善心血管健康和控制体重。";
"activity_history_desc_content_distance" = "日常行走或跑步的总距离是衡量活动水平的重要指标。此数据以公里为单位显示，反映您每天移动的物理距离。根据健康建议，成年人应该每天行走至少4-5公里以保持基本健康水平。";
"activity_history_desc_content_calories" = "活动卡路里是指通过锻炼和日常活动（超过安静状态）消耗的额外能量。这不包括身体维持基本功能消耗的卡路里（基础代谢率）。活动卡路里是健康管理和体重控制的重要指标。";
"activity_history_desc_content_hr" = "活动心率是指在体育活动期间心脏的跳动频率。不同强度的活动会导致心率升高到不同水平。监测活动心率可以帮助优化训练效果，确保您的锻炼强度适中并达到预期效果。";
"activity_history_desc_content_zones" = "活动区间是基于您的最大心率百分比划分的不同运动强度区间。在不同心率区间进行训练可以实现不同的健身目标：从提高基础耐力(区间1-2)到提高有氧能力(区间3)和增强高强度表现(区间4-5)。";
"activity_history_improve_title_score" = "活动得分";
"activity_history_improve_title_steps" = "每日步数";
"activity_history_improve_title_distance" = "每日距离";
"activity_history_improve_title_calories" = "活动卡路里";
"activity_history_improve_title_hr" = "活动心率";
"activity_history_improve_title_zones" = "活动区间";
"activity_history_improve_content_score" = "设定明确的每日活动目标，比如特定的步数或活动分钟数。\n尝试融入更多HIIT(高强度间歇训练)以在较短时间内获得更大活动收益。\n确保每天至少有30分钟的中等强度活动。\n减少久坐时间，每小时起身活动几分钟。\n使用健身应用程序或智能手表来跟踪和激励自己保持活跃。";
"activity_history_improve_content_steps" = "选择远一点的停车位，增加步行距离。\n使用楼梯代替电梯。\n午餐时间进行短距离步行。\n安排步行会议代替坐着开会。\n设置提醒，每小时起来走动几分钟。\n考虑购买一台走步机，在看电视时使用。";
"activity_history_improve_content_distance" = "逐渐增加日常步行或跑步的距离，每周增加约10%。\n探索新的步行或跑步路线以保持兴趣和动力。\n参加虚拟步行或跑步挑战，设定月度距离目标。\n在周末安排更长距离的步行或徒步旅行。\n使用步行作为交通方式，尽可能选择步行而非驾车。";
"activity_history_improve_content_calories" = "结合有氧运动和力量训练，这两种运动方式都能有效燃烧卡路里。\n增加日常活动强度，如快步走而不是慢走。\n尝试间歇训练，这种方法能在运动后持续燃烧卡路里。\n确保充分休息和恢复，这对于维持活跃生活方式至关重要。\n注意饮食与运动的平衡，不要因为运动消耗了卡路里就过量进食。";
"activity_history_improve_content_hr" = "使用心率区间训练，在不同强度下锻炼以获得全面的健身效果。\n进行适当的热身和冷却，使心率逐渐提高和降低。\n定期进行耐力训练以提高心脏效率，降低静息心率。\n注意观察异常高或低的活动心率，可能需要咨询医生。\n确保充分休息和睡眠，这对心脏健康至关重要。";
"activity_history_improve_content_zones" = "为不同训练目标针对性地使用心率区间：区间2适合基础耐力，区间3适合有氧适能，区间4-5适合提高最大摄氧量。\n尝试心率区间间歇训练，在不同强度间切换以提高心肺功能。\n使用智能手表实时监测心率区间，确保训练强度适中。\n记录每周在各区间的训练时间，确保训练负荷均衡。\n随着体能提高，调整心率区间设置，确保持续进步。";

/* Stress History View */
"stress_history_nav_title" = "压力";
"stress_history_section_title_score" = "压力值";
"stress_history_section_title_night_score" = "夜间压力值";

/* Vital Signs History View */
"vitals_history_nav_title" = "生命体征";
"vitals_history_section_title_hr" = "心率";
"vitals_history_section_title_spo2" = "血氧";
"vitals_history_section_title_hrv" = "心率变异性";
"vitals_history_button_retry" = "重试";
"vitals_history_detail_sheet_close" = "关闭";
"vitals_history_detail_sheet_more" = "更多";
"vitals_history_detail_sheet_maximum" = "最大值";
"vitals_history_detail_sheet_average" = "平均值";
"vitals_history_detail_sheet_minimum" = "最小值";
"vitals_history_detail_sheet_baseline" = "基线";
"vitals_history_detail_sheet_difference" = "差异";
"vitals_history_detail_sheet_about" = "关于 %@";
"vitals_history_detail_sheet_suggestion" = "建议";
"vitals_history_detail_sheet_start_management" = "开始健康管理";
"vitals_history_desc_title_hr" = "心率";
"vitals_history_desc_content_hr" = "心率是指心脏跳动的次数，通常以每分钟跳动次数(bpm)表示。正常的静息心率范围在60-100bpm之间，较低的静息心率通常表示更好的心脏健康状况。运动、压力、疾病和药物都可能导致心率变化。";
"vitals_history_desc_title_spo2" = "血氧";
"vitals_history_desc_content_spo2" = "血氧饱和度(SpO2)是血液中携带氧气的血红蛋白与总血红蛋白的比例。健康人的血氧饱和度通常在95%-100%之间。低于90%的读数可能表示存在呼吸或循环问题，需要医疗关注。";
"vitals_history_desc_title_hrv" = "心率变异性";
"vitals_history_desc_content_hrv" = "心率变异性(HRV)是指连续心跳之间时间间隔的变化。较高的HRV通常表示身体有良好的适应能力和恢复能力，而较低的HRV可能与压力、疲劳或健康问题有关。";
"vitals_history_desc_no_info" = "关于此指标的详细信息暂不可用。";
"vitals_history_suggestion_title_hr" = "心率";
"vitals_history_suggestion_content_hr" = "定期进行有氧运动以改善心脏健康\n避免过量摄入咖啡因和酒精\n保持健康体重\n管理压力并确保充足睡眠\n如果心率异常持续或伴随其他症状，请咨询医生";
"vitals_history_suggestion_title_spo2" = "血氧";
"vitals_history_suggestion_content_spo2" = "保持良好的呼吸习惯和姿势\n避免长时间待在空气污染严重的环境\n如果您是吸烟者，考虑戒烟\n定期锻炼以提高肺部功能\n如果血氧饱和度持续低于95%，请咨询医疗专业人员";
"vitals_history_suggestion_title_hrv" = "心率变异性";
"vitals_history_suggestion_content_hrv" = "练习冥想和深呼吸以改善HRV\n确保充足优质的睡眠\n增加有氧运动频率\n均衡饮食并保持良好水分摄入\n管理压力和情绪";
"vitals_history_suggestion_no_info" = "暂无针对此指标的特定建议。";
"vitals_history_tooltip_max" = "最大值: %@";
"vitals_history_tooltip_min" = "最小值: %@";

/* Developer Tools in UserProfileView */
"user_profile_dev_tools_title" = "开发者工具";
"user_profile_dev_step_test" = "步数明细数据测试";
"user_profile_dev_hr_flow_test" = "心率数据流程测试";
"user_profile_dev_hr_detail_test" = "心率明细数据测试";
"user_profile_dev_hrv_detail_test" = "HRV明细数据测试";
"user_profile_dev_db_tools" = "数据库调试工具";
"dev_tools_quick_login" = "快速测试登录";
"dev_tools_hr_flow_test_subtitle" = "测试心率数据的获取、存储、上传、下载和展示";

/* Language Selection View */
"language_selection_title" = "语言";
"language_selection_simplified_chinese" = "简体中文";
"language_selection_english" = "English";

/* Register View */
"register_title" = "注册";
"register_button_get_code" = "获取验证码";
"register_code_sent_success" = "验证码发送成功！";
"register_placeholder_mobile" = "请输入您的手机号码";
"register_placeholder_email" = "请输入您的电子邮箱";
"register_placeholder_code" = "输入验证码";
"register_error_title" = "注册失败";
"register_error_invalid_mobile" = "请输入有效的手机号码";
"register_error_invalid_email" = "请输入有效的邮箱地址";
"register_error_invalid_code" = "请输入有效的验证码";
"register_alert_button_login" = "去登录";
"register_alert_button_continue" = "继续注册";
"register_validation_failed_title" = "验证失败";
"register_reset_password_failed_title" = "重置密码失败";

/* Personal Info View */
"personal_info_title" = "个人信息";
"register_success_title" = "注册成功";
"personal_info_prompt" = "请输入您的个人信息";
"personal_info_name_placeholder" = "请输入您的姓名";
"personal_info_height_placeholder" = "输入您的身高";
"personal_info_weight_placeholder" = "输入您的体重";
"select_date" = "选择日期";
"unit_cm" = "厘米";
"unit_kg" = "公斤";
"unit_ft" = "英尺";
"unit_lbs" = "磅";
"unit_in" = "英寸";

/* Set Password View */
"set_password_title" = "设置密码";
"enter_password" = "输入密码";
"enter_again" = "再次输入";
"password_placeholder" = "请输入您的密码";
"confirm_password_placeholder" = "请再次输入您的密码";
"password_format_error" = "密码格式错误！";
"password_rules" = "密码长度必须为8-20个字符，且至少包含一个数字、一个大写字母和一个小写字母。";
"password_mismatch_error" = "两次输入的密码不一致。";

/* Reset Password View */
"reset_password_button_title" = "重置密码";
"back_to_login_button" = "返回登录";
"reset_password_success_title" = "密码重置成功";
"reset_password_success_message" = "您的密码已成功重置。您现在可以使用新密码登录。";
"reset_password_failed_title" = "重置密码失败";
"placeholder_new_password" = "输入新密码";
"placeholder_confirm_password" = "确认密码";

/* Goal Setting View */
"goal_settings_title" = "目标设置";
"goal_sleep_title" = "睡眠目标";
"goal_steps_title" = "步数目标";
"goal_activity_calorie_title" = "活动卡路里\n目标";
"goal_activity_duration_title" = "活动时长\n目标";
"goal_time_format" = "%d小时%d分钟";
"goal_calorie_format" = "%@千卡";
"goal_activity_calorie_popup_title" = "活动卡路里目标";
"goal_activity_duration_popup_title" = "活动时长目标";

/* Versioning View */
"versioning_software_version" = "软件版本";
"versioning_app_version_format" = "APP版本 %@";
"versioning_firmware_version" = "固件版本";
"versioning_equipment_name" = "设备名称";
"versioning_mac_address" = "MAC地址";
"versioning_last_version" = "已是最新";
"versioning_update_check_failed" = "更新检查失败";
"versioning_unknown_error" = "未知错误";
"versioning_check_for_updates" = "检查更新";
"versioning_update_alert_title" = "更新检查";

/* Version Terms Of Use View */
"terms_of_use_title" = "SiRing使用条款";
"terms_of_use_last_updated_date_format" = "最后更新和生效日期：%@";
"terms_of_use_intro_title" = "引言";
"terms_of_use_intro_content" = "本使用条款（\"本协议\"）描述了适用于您访问和使用SiRing服务（包括SiRing智能戒指设备（\"产品\"）、应用程序（\"App\"）、软件、API、电子邮件、新闻通讯和服务）的条款和条件。";
"terms_of_use_privacy_title" = "数据隐私";
"terms_of_use_privacy_content" = "本使用条款（\"本协议\"）描述了适用于您访问和使用SiRing服务（包括SiRing智能戒指设备（\"产品\"）、应用程序（\"App\"）、软件、API、电子邮件、新闻通讯和服务）的条款和条件。";

/* Version Privacy Policy View */
"privacy_policy_view_title" = "SiRing隐私政策";
"privacy_policy_last_updated_date_format" = "最后更新和生效日期：%@";
"privacy_policy_info_collection_title" = "信息收集";
"privacy_policy_info_collection_content" = "我们收集多种类型的信息，包括：个人信息（如姓名、电子邮件地址）、健康和健身数据（如心率、睡眠模式、活动水平）、设备信息（设备类型、操作系统）以及使用数据（您如何与应用互动）。\n\n我们仅在提供和改进我们的服务、处理交易、发送技术通知和支持消息、回应您的评论和问题以及开发新产品和服务所必需时收集您的个人数据。";
"privacy_policy_data_security_title" = "数据安全";
"privacy_policy_data_security_content" = "我们采取适当的技术和组织措施来保护您的个人信息，防止未经授权或非法的处理、意外丢失、破坏或损坏。您的数据是端到端加密的，只有您明确共享的人才能访问您的信息。\n\n我们可能与以下各方共享您的个人信息：代表我们执行服务的服务提供商、经您同意的业务合作伙伴以及法律要求的法律机构。";

/* Version Information List View */
"info_list_health_title" = "SiRing健康";
"info_list_health_description" = "SiRing健康（以下简称\"我们\"）将严格遵守国家法律法规，将合法合规融入产品设计和业务流程，并使用行业领先的安全技术来确保数据安全。同时，我们高度重视保护您的个人信息和隐私，并致力于保障每个人的隐私权。";
"info_list_register_login_title" = "注册/登录";
"info_list_collect_personal_info" = "收集的个人信息";
"info_list_register_login_info_content" = "电子邮件地址；电话号码；微信账号；QQ（Tim）账号；用户填写的信息：头像、昵称、出生日期、性别、身高、体重；";
"info_list_business_scene" = "业务场景";
"info_list_register_login_scene_content" = "创建/登录账户";
"info_list_bind_device_title" = "绑定设备";
"info_list_bind_device_info_content" = "设备信息：唯一标识符、固件版本等；位置信息；";
"info_list_bind_device_scene_content" = "搜索设备并绑定、激活、更新、修复错误、优化产品或解绑设备";
"info_list_monitoring_title" = "监控";
"info_list_monitoring_info_content" = "用户账户信息、设备记录的信息（步数）；";
"info_list_monitoring_scene_content" = "计算/分析运动和健康数据";
"info_list_monitoring_reminder_title" = "监控提醒功能";
"info_list_monitoring_reminder_info_content" = "用户账户信息、心率信息、久坐信息、低电量信息；";
"info_list_monitoring_reminder_scene_content" = "佩戴指南提醒、高心率提醒、久坐提醒、戒指电量低提醒";

/* User Guide View */
"user_guide_title" = "使用指南";
"user_guide_app" = "App";
"user_guide_ring" = "戒指";
"user_guide_account_password" = "账户密码";

"app_guide_title" = "APP使用指南";
"app_guide_logout" = "用户登出";
"app_guide_logout_content" = "内容 1";
"app_guide_bind_ring" = "绑定戒指";
"app_guide_bind_ring_content" = "内容 2";
"app_guide_unbind_ring" = "解绑戒指";
"app_guide_unbind_ring_content" = "内容 3";

"ring_guide_title" = "戒指使用指南";
"ring_guide_getting_started" = "入门";
"ring_guide_getting_started_content" = "内容 1";
"ring_guide_button_operations" = "按键操作";
"ring_guide_button_operations_content" = "内容 2";
"ring_guide_function_menu" = "功能菜单";
"ring_guide_function_menu_content" = "内容 3";

"account_guide_title" = "账户密码指南";
"account_guide_register" = "注册账户";
"account_guide_register_content" = "内容 1";
"account_guide_login_methods" = "登录方式";
"account_guide_login_methods_content" = "内容 2";
"account_guide_password_security" = "密码安全";
"account_guide_password_security_content" = "内容 3";

"sales_support_title" = "反馈";
"sales_support_record_button" = "记录";
"sales_support_feedback_type_prompt" = "请勾选您要反馈的类型";
"sales_support_channel_prompt" = "请让我们知道您是通过哪个渠道购买的戒指";
"sales_support_channel_tip" = "请填写准确的渠道，以便我们更有效地解决您的问题";
"sales_support_content_prompt" = "反馈内容";
"sales_support_abnormal_start_time_prompt" = "异常开始时间";
"sales_support_start_time_placeholder" = "开始时间";
"sales_support_abnormal_end_time_prompt" = "异常结束时间";
"sales_support_end_time_placeholder" = "结束时间";
"sales_support_image_upload_prompt" = "上传图片（最多6张）";
"sales_support_submit_button" = "提交";
"sales_support_select_channel_placeholder" = "选择渠道";
"sales_support_feedback_content_placeholder" = "请详细描述您的问题或建议，我们会尽快通过邮件回复。";
"sales_support_feedback_type_placeholder" = "请选择反馈类型";
"sales_support_channel_official" = "官方网站";
"sales_support_channel_amazon" = "亚马逊";
"sales_support_channel_kickstarter" = "Kickstarter";
"sales_support_channel_offline" = "线下";
"sales_support_channel_indiegogo" = "Indiegogo";
"sales_support_channel_other" = "其他";
"sales_support_alert_title" = "信息不完整";
"sales_support_alert_type_missing" = "请选择反馈类型。";
"sales_support_alert_channel_missing" = "请选择购买渠道。";
"sales_support_alert_content_missing" = "请输入反馈内容。";
"sales_support_submit_success" = "反馈提交成功！";
"sales_support_submit_failure" = "反馈提交失败，请重试。";

"about_ring_title" = "关于 SiRing";
"about_ring_device_name" = "SiRing 设备";
"about_us_app_name" = "SiRing";
"about_us_version" = "当前版本 %@";
"about_us_terms_of_use" = "使用条款";
"about_us_privacy_policy" = "隐私政策";
"about_us_information_list" = "信息列表";
"about_us_contact_us" = "联系我们";
"about_us_email" = "<EMAIL>";
"about_us_icp" = "粤ICP备202402222290-4A";
"about_us_company" = "广州知网科技有限公司";
"about_us_copyright" = "Copyright ©2021-2024 juzhi ALL SiRing Reserved";
"about_us_title" = "关于我们";

"terms_title" = "条款和条件";
"terms_last_updated" = "最后更新：2024年3月25日";
"terms_intro" = "在使用SiRing应用前，请仔细阅读这些条款和条件。";
"terms_acceptance_title" = "1. 接受条款";
"terms_acceptance_content" = "下载、安装或使用SiRing应用，即表示您同意受这些条款和条件的约束。如果您不同意这些条款，则不应使用该应用。";
"terms_use_title" = "2. 应用的使用";
"terms_use_content_1" = "SiRing授予您有限、非排他性、不可转让、可撤销的许可，以将该应用用于您的个人非商业目的。您不得将该应用用于任何非法或未经授权的目的。";
"terms_use_content_2" = "您同意不修改、改编、翻译、逆向工程、反编译或反汇编该应用或其任何部分。";
"terms_privacy_title" = "3. 用户数据和隐私";
"terms_privacy_content" = "您对该应用的使用也受我们的隐私政策的约束，该政策通过引用并入本条款和条件。";
"terms_ip_title" = "4. 知识产权";
"terms_ip_content" = "该应用，包括所有内容、功能和特性，均为广州知网科技有限公司所有，并受国际版权、商标、专利、商业秘密和其他知识产权法的保护。";
"terms_liability_title" = "5. 责任限制";
"terms_liability_content" = "在法律允许的最大范围内，SiRing、其关联公司或其许可方、服务提供商、员工、代理、高级职员或董事在任何情况下均不对因您使用本应用或与之相关的任何形式的损害承担责任。";
"terms_changes_title" = "6. 条款变更";
"terms_changes_content" = "我们保留随时修改这些条款的权利。我们将通过更新这些条款顶部的\"最后更新\"日期来提供任何重大变更的通知。";
"terms_contact_title" = "7. 联系信息";
"terms_contact_content" = "如果您对这些条款有任何疑问，请通过*************与我们联系。";

"privacy_policy_title" = "隐私政策";
"privacy_policy_last_updated" = "最后更新：2024年3月25日";
"privacy_policy_intro" = "本隐私政策描述了当您使用我们的应用时，SiRing如何收集、使用和披露您的个人信息。";
"privacy_policy_collection_title" = "1. 我们收集的信息";
"privacy_policy_collection_content" = "我们从应用用户处收集多种类型的信息，包括：";
"privacy_policy_collection_item_1" = "个人信息：您注册时的姓名、电子邮件地址和联系方式";
"privacy_policy_collection_item_2" = "健康与健身数据：心率、睡眠模式、活动水平";
"privacy_policy_collection_item_3" = "设备信息：设备类型、操作系统、唯一设备标识符";
"privacy_policy_collection_item_4" = "使用数据：您如何与我们的应用互动";
"privacy_policy_use_title" = "2. 我们如何使用您的信息";
"privacy_policy_use_content" = "我们使用收集的信息来：";
"privacy_policy_use_item_1" = "提供、维护和改进我们的服务";
"privacy_policy_use_item_2" = "处理和完成交易";
"privacy_policy_use_item_3" = "向您发送技术通知和支持消息";
"privacy_policy_use_item_4" = "回应您的评论和问题";
"privacy_policy_use_item_5" = "开发新产品和服务";
"privacy_policy_storage_title" = "3. 数据存储和安全";
"privacy_policy_storage_content" = "我们采取适当的技术和组织措施来保护您的个人信息，防止未经授权或非法的处理、意外丢失、破坏或损坏。";
"privacy_policy_sharing_title" = "4. 数据共享和披露";
"privacy_policy_sharing_content" = "我们可能与以下各方共享您的个人信息：";
"privacy_policy_sharing_item_1" = "代表我们执行服务的服务提供商";
"privacy_policy_sharing_item_2" = "经您同意的业务合作伙伴";
"privacy_policy_sharing_item_3" = "法律要求的法律机构";
"privacy_policy_rights_title" = "5. 您的权利";
"privacy_policy_rights_content" = "根据您所在的位置，您可能对您的个人信息拥有某些权利，包括：";
"privacy_policy_rights_item_1" = "访问您的个人信息";
"privacy_policy_rights_item_2" = "更正不准确的信息";
"privacy_policy_rights_item_3" = "删除您的信息";
"privacy_policy_rights_item_4" = "限制处理";
"privacy_policy_rights_item_5" = "数据可移植性";
"privacy_policy_changes_title" = "6. 本隐私政策的变更";
"privacy_policy_changes_content" = "我们可能会不时更新我们的隐私政策。我们将通过在此页面上发布新的隐私政策并更新\"最后更新\"日期来通知您任何更改。";
"privacy_policy_contact_title" = "7. 联系我们";
"privacy_policy_contact_content" = "如果您对本隐私政策有任何疑问，请通过*************与我们联系。";

"info_list_version_history_title" = "版本历史";
"info_list_features_title" = "功能";
"info_list_specs_title" = "技术规格";
"info_list_version_format" = "版本 %@";
"info_list_feature_1_title" = "健康监测";
"info_list_feature_1_desc" = "追踪心率、睡眠质量和活动水平";
"info_list_feature_2_title" = "智能通知";
"info_list_feature_2_desc" = "接收来电、消息和应用的提醒";
"info_list_feature_3_title" = "锻炼追踪";
"info_list_feature_3_desc" = "通过详细指标监控各种锻炼类型";
"info_list_feature_4_title" = "防水";
"info_list_feature_4_desc" = "IP68级防水防尘";
"info_list_feature_5_title" = "电池寿命";
"info_list_feature_5_desc" = "单次充电正常使用长达14天";
"info_list_spec_display_title" = "显示屏";
"info_list_spec_display_value" = "1.3英寸AMOLED，360x360像素";
"info_list_spec_sensors_title" = "传感器";
"info_list_spec_sensors_value" = "心率、加速度计、陀螺仪";
"info_list_spec_connectivity_title" = "连接性";
"info_list_spec_connectivity_value" = "蓝牙5.0, NFC";
"info_list_spec_battery_title" = "电池";
"info_list_spec_battery_value" = "420mAh锂离子电池";
"info_list_spec_water_resistance_title" = "防水等级";
"info_list_spec_water_resistance_value" = "5 ATM / IP68";
"info_list_spec_dimensions_title" = "尺寸";
"info_list_spec_dimensions_value" = "45.4 x 45.4 x 11.8 毫米";
"info_list_spec_weight_title" = "重量";
"info_list_spec_weight_value" = "32克（不含表带）";

/* Sales Support New Fields */
"sales_support_contact_name_prompt" = "我该如何称呼您";
"sales_support_contact_name_placeholder" = "请输入您的姓名或昵称";
"sales_support_contact_email_prompt" = "您的电子邮箱地址";
"sales_support_contact_email_placeholder" = "请输入您的电子邮箱地址";
"sales_support_alert_email_missing" = "请输入您的邮箱地址。";
"sales_support_record_button_icon" = "list.bullet.rectangle";

/* Family Sharing */
"create_link_success_message" = "链接创建成功！您可以跳转到其他应用分享您的链接！";
"copy_the_link_button" = "复制链接";

/* Share Data Content View */
"share_data_content_title" = "共享数据内容";
"share_data_content_description" = "请选择您想共享的数据内容，您可以随时调整共享数据内容和结束共享";
"share_data_content_standard_data_header" = "指导性标准数据";
"share_data_content_sleep" = "睡眠";
"share_data_content_activity" = "活动";
"share_data_content_stress" = "压力";
"share_data_content_health_status" = "健康状态";
"share_data_content_report_header" = "报告";
"share_data_content_weekly_report" = "周报";
"share_data_content_link_notice" = "即将生成一次性邀请链接，有效期30分钟，任何获取此链接的用户都可以接受您的邀请，请注意保护链接安全。";

/* Date Selection View */
"date_selection_today" = "今天";

/* Toasts */
"factory_reset_success_toast" = "恢复出厂设置成功";
"factory_reset_failed_toast" = "恢复出厂设置失败，请检查戒指是否连接或稍后重试";
"shutdown_success_toast" = "关机成功";
"shutdown_failed_toast" = "关机失败，请检查戒指是否连接或稍后重试";

/* Local Notifications */
"notification_sedentary_title" = "久坐提醒";
"notification_sedentary_body" = "久坐伤身，养成活动好习惯";
"notification_battery_title" = "电量提醒";
"notification_battery_body" = "当前电量低于5%，请及时充电";

/* Feedback History */
"feedback_history_title" = "反馈历史";
"feedback_history_empty" = "暂无历史反馈";
"feedback_status_replied" = "已回复";
"feedback_status_pending" = "待回复";

/* Feedback Detail */
"feedback_detail_title" = "反馈详情";
"feedback_status_replied_long" = "已回复";
"feedback_status_pending_long" = "待回复";
"feedback_detail_time" = "反馈时间";
"feedback_detail_type" = "反馈类型";
"feedback_detail_channel" = "购买渠道";
"feedback_detail_content" = "反馈内容";
"feedback_detail_address" = "反馈地址";
"feedback_detail_images" = "图片";

/* Device Pairing */
"device_add_ring_title" = "添加戒指";
"device_select_prompt" = "请从下方列表中选择您的戒指";
"device_not_found_prompt" = "未能找到戒指，请尝试为其充电";
"loading_mac_address" = "正在加载MAC地址...";
"default_ring_name" = "风王戒指";
"mac_address_not_found" = "未获取到MAC地址";
"device_searching_prompt" = "正在搜索附近的戒指...";
"bluetooth_permission_required_title" = "需要蓝牙权限";
"bluetooth_permission_required_message" = "请在设置中允许蓝牙访问";
"go_to_settings" = "前往设置";
"error_connect_paired_device" = "无法连接到已配对设备。请确保设备已开机且在范围内，或尝试重启设备后再试。";
"error_connect_failed" = "连接设备失败，请重试";
"error_connect_failed_with_suggestion" = "连接设备失败。请确保设备已开机且在范围内，然后重试。";

/* Version Update */
"update_available_title" = "发现新版本";
"update_available_message" = "发现新版本 %@，建议您立即更新以获得更好的体验。";
"error_api_business_error" = "接口返回业务错误码: %@";
"error_no_version_info" = "服务器未返回版本信息";

/* Glossary */
"glossary_title_history_sleep_score" = "睡眠评分";
"glossary_content_history_sleep_score" = "睡眠分数以0到100分反映您的整体睡眠质量。它基于您的睡眠时长、睡眠效率、睡眠阶段以及夜间醒来次数。分数越高表示睡眠越好。";
"glossary_title_health_status" = "综合状况";
"glossary_content_health_status" = "综合评分用于评估当天的状况，基于您的每日睡眠表现和活动时长。";
