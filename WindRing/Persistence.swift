//
//  Persistence.swift
//  WindRing
//
//  Created by 1234 on 2025/3/3.
//

import CoreData
import Foundation

/// 持久化控制器 - 使用StorageManager作为底层持久化存储
struct PersistenceController {
    static let shared = PersistenceController()

    @MainActor
    static let preview: PersistenceController = {
        // 初始化预览环境时使用内存数据库
        // 注意：预览环境不应影响真实的数据库
        let result = PersistenceController(inMemory: true)
        return result
    }()

    // 持久化容器 - 从StorageManager获取
    var container: NSPersistentContainer {
        return StorageManager.shared.persistentContainerPublic
    }

    // 初始化方法
    init(inMemory: Bool = false) {
        // 如果是内存模式（预览模式），不做任何特殊处理
        // StorageManager已经正确配置持久化存储
        if inMemory {
            print("Persistence初始化为内存模式 - 仅供预览使用")
            // 注意：这里不再像之前那样设置/dev/null路径
            // 预览环境下的操作不会影响真实数据库
        }
    }
}
