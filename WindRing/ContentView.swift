//
//  ContentView.swift
//  WindRing
//
//  Created by 1234 on 2025/3/3.
//

import SwiftUI
import CoreData

/// 主内容视图
struct ContentView: View {
    // MARK: - 属性
    @Environment(\.managedObjectContext) private var viewContext
    
    // 添加服务
    @StateObject private var mqttService = MQTTService.shared
    @StateObject private var mqttSyncService = MQTTSyncService.shared
    @StateObject private var deviceService = WindRingDeviceService.shared
   
    @EnvironmentObject var deepLinkManager: DeepLinkManager
    // MARK: - 主视图
    var body: some View {
        MainTabView()
            .environmentObject(mqttService)
            .environmentObject(mqttSyncService)
            .environmentObject(deviceService)
//            .environmentObject(localizationService)
        if let deepLink = deepLinkManager.activeDeepLink {
            HealthDataShareAlert(
                type: deepLink.type, nickname: deepLink.nickname,
                onConfirm: {
                    print("✅ 同意分享，uuid: \(deepLink.uuid)")
                    deepLinkManager.activeDeepLink = nil
                },
                onCancel: {
                    print("❌ 拒绝分享")
                    deepLinkManager.activeDeepLink = nil
                }
            )
        }
    }
}

// MARK: - 预览
//struct ContentView_Previews: PreviewProvider {
//    static var previews: some View {
//        Group {
//            ContentView()
//                .environment(\.managedObjectContext, StorageManager.shared.viewContext())
//                .previewDisplayName("English")
//                .localizationPreview(.english)
//            
//            ContentView()
//                .environment(\.managedObjectContext, StorageManager.shared.viewContext())
//                .previewDisplayName("Chinese")
//                .localizationPreview(.chinese)
//        }
//    }
//} 
