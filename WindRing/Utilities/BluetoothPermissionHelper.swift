import Foundation
import CoreBluetooth
import SwiftUI

/// 蓝牙权限助手 - 管理蓝牙权限检查和请求
class BluetoothPermissionHelper: NSObject, ObservableObject {
    // MARK: - 单例
    static let shared = BluetoothPermissionHelper()
    
    // MARK: - 属性
    @Published var permissionGranted = false
    @Published var bluetoothState: CBManagerState = .unknown
    
    private var centralManager: CBCentralManager?
    private var permissionCallback: ((Bool) -> Void)?
    
    // MARK: - 初始化
    private override init() {
        super.init()
        centralManager = CBCentralManager(delegate: self, queue: nil)
    }
    
    // MARK: - 公共方法
    /// 检查蓝牙权限状态
    /// - Parameter completion: 权限状态回调
    func checkPermission(completion: @escaping (Bool) -> Void) {
        permissionCallback = completion
        
        let state = centralManager?.state ?? .unknown
        updateStateAndCallback(state)
    }
    
    /// 根据蓝牙状态获取详细描述
    /// - Parameter state: 蓝牙状态
    /// - Returns: 状态描述
    func getStateDescription(_ state: CBManagerState) -> String {
        switch state {
        case .poweredOn:
            return "蓝牙已开启"
        case .poweredOff:
            return "蓝牙已关闭，请开启蓝牙"
        case .resetting:
            return "蓝牙正在重置中"
        case .unauthorized:
            return "蓝牙权限未授权，请在系统设置中开启"
        case .unsupported:
            return "此设备不支持蓝牙"
        case .unknown:
            return "蓝牙状态未知"
        @unknown default:
            return "蓝牙状态未知"
        }
    }
    
    // MARK: - 私有方法
    /// 更新状态并执行回调
    /// - Parameter state: 蓝牙状态
    private func updateStateAndCallback(_ state: CBManagerState) {
        bluetoothState = state
        permissionGranted = (state == .poweredOn)
        permissionCallback?(permissionGranted)
    }
}

// MARK: - CBCentralManagerDelegate
extension BluetoothPermissionHelper: CBCentralManagerDelegate {
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        updateStateAndCallback(central.state)
    }
}

// MARK: - 视图修饰器
struct BluetoothPermissionAlert: ViewModifier {
    @StateObject private var permissionHelper = BluetoothPermissionHelper.shared
    @State private var showAlert = false
    
    func body(content: Content) -> some View {
        content
            .onAppear {
                permissionHelper.checkPermission { granted in
                    showAlert = !granted
                }
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text("需要蓝牙权限"),
                    message: Text(permissionHelper.getStateDescription(permissionHelper.bluetoothState)),
                    primaryButton: .default(Text("设置")) {
                        if let url = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(url)
                        }
                    },
                    secondaryButton: .cancel(Text("取消"))
                )
            }
    }
}

extension View {
    /// 添加蓝牙权限检查和提示
    func checkBluetoothPermission() -> some View {
        self.modifier(BluetoothPermissionAlert())
    }
} 