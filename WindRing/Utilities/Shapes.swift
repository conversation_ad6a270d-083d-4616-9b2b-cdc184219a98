import SwiftUI

// MARK: - Common Shapes
// 注意：Diamond 和 RadarShape 结构已经被移到各个使用它们的文件中，
// 以避免"Invalid redeclaration"编译错误。
// 如果需要在多个文件中使用这些形状，应该考虑创建一个独立的Swift包或模块。

/*
/// Diamond shape for radar charts
public struct Diamond: Shape {
    public func path(in rect: CGRect) -> Path {
        var path = Path()
        
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let topPoint = CGPoint(x: center.x, y: center.y - rect.height / 2)
        let rightPoint = CGPoint(x: center.x + rect.width / 2, y: center.y)
        let bottomPoint = CGPoint(x: center.x, y: center.y + rect.height / 2)
        let leftPoint = CGPoint(x: center.x - rect.width / 2, y: center.y)
        
        path.move(to: topPoint)
        path.addLine(to: rightPoint)
        path.addLine(to: bottomPoint)
        path.addLine(to: leftPoint)
        path.closeSubpath()
        
        return path
    }
}

/// Radar chart shape for health data visualization
public struct RadarShape: Shape {
    public var sleepValue: CGFloat
    public var stressValue: CGFloat
    public var vitalSignsValue: CGFloat
    public var activityValue: CGFloat
    
    public init(sleepValue: CGFloat, stressValue: CGFloat, vitalSignsValue: CGFloat, activityValue: CGFloat) {
        self.sleepValue = sleepValue
        self.stressValue = stressValue
        self.vitalSignsValue = vitalSignsValue
        self.activityValue = activityValue
    }
    
    public func path(in rect: CGRect) -> Path {
        var path = Path()
        
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2
        
        // Calculate points based on values
        let topPoint = CGPoint(x: center.x, y: center.y - radius * sleepValue)
        let leftPoint = CGPoint(x: center.x - radius * stressValue, y: center.y)
        let rightPoint = CGPoint(x: center.x + radius * vitalSignsValue, y: center.y)
        let bottomPoint = CGPoint(x: center.x, y: center.y + radius * activityValue)
        
        // Draw the path
        path.move(to: topPoint)
        path.addLine(to: rightPoint)
        path.addLine(to: bottomPoint)
        path.addLine(to: leftPoint)
        path.closeSubpath()
        
        return path
    }
    
    // Add animation support
    public var animatableData: AnimatablePair<AnimatablePair<CGFloat, CGFloat>, AnimatablePair<CGFloat, CGFloat>> {
        get {
            AnimatablePair(
                AnimatablePair(sleepValue, stressValue),
                AnimatablePair(vitalSignsValue, activityValue)
            )
        }
        set {
            sleepValue = newValue.first.first
            stressValue = newValue.first.second
            vitalSignsValue = newValue.second.first
            activityValue = newValue.second.second
        }
    }
}
*/ 