import Foundation
import SwiftUI
import Combine

/// 本地化服务类，用于管理应用的语言切换
class LanguageManager: ObservableObject {
    static let shared = LanguageManager()
    
    @AppStorage("selectedLanguage") var selectedLanguage: String = Locale.current.language.languageCode?.identifier ?? "en" {
        didSet {
            Bundle.setLanguage(selectedLanguage)
            objectWillChange.send()
        }
    }

    var currentLanguageDisplayName: String {
        selectedLanguage == "zh-Hans" ? "简体中文" : "English"
    }

    func toggleLanguage() {
        selectedLanguage = (selectedLanguage == "en") ? "zh-Hans" : "en"
        UserDefaults.standard.set(selectedLanguage, forKey: "selectedLanguage")
    }
    
    private init() {
        if let savedLanguage = UserDefaults.standard.string(forKey: "selectedLanguage") {
            selectedLanguage = savedLanguage
        } else {
            // If not found, use the current system language
            let systemLanguage = Locale.current.language.languageCode?.identifier ?? "en"
            selectedLanguage = systemLanguage.starts(with: "zh") ? "zh-<PERSON>" : "en"
        }
        // Apply the language for the current session
//        Bundle.setLanguage(selectedLanguage)
        
//        let newLanguage = (selectedLanguage == "en") ? "zh-Hans" : "en"
//        selectedLanguage = newLanguage
//        // Manually save the user's selection to UserDefaults
//        UserDefaults.standard.set(newLanguage, forKey: "selectedLanguage")
//        Bundle.setLanguage(newLanguage)
    }
    

}

private var bundleKey: UInt8 = 0

final class LocalizedBundle: Bundle {
    override func localizedString(forKey key: String, value: String?, table tableName: String?) -> String {
        let langBundle = objc_getAssociatedObject(self, &bundleKey) as? Bundle ?? self
        return langBundle.localizedString(forKey: key, value: value, table: tableName)
    }
}

extension Bundle {
    static func setLanguage(_ language: String) {
        guard let path = Bundle.main.path(forResource: language, ofType: "lproj"),
              let langBundle = Bundle(path: path) else {
            return
        }

        objc_setAssociatedObject(Bundle.main, &bundleKey, langBundle, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        object_setClass(Bundle.main, LocalizedBundle.self)
    }
}
