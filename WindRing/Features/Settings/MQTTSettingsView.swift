import SwiftUI

/// MQTT设置视图
struct MQTTSettingsView: View {
    @EnvironmentObject var mqttService: MQTTService
    @EnvironmentObject var mqttSyncService: MQTTSyncService
    
    @State private var showAdvancedSettings = false
    @State private var customHost = ""
    @State private var customPort = ""
    @State private var username = ""
    @State private var password = ""
    
    var body: some View {
        List {
            Section(header: Text("MQTT状态")) {
                MQTTStatusView()
                    .environmentObject(mqttService)
                    .environmentObject(mqttSyncService)
                    .listRowInsets(EdgeInsets())
                    .padding(.vertical, 8)
            }
            
            Section(header: Text("连接设置")) {
                Toggle("自动连接", isOn: $mqttService.autoConnect)
                    .onChange(of: mqttService.autoConnect) { newValue in
                        mqttService.toggleAutoConnect(newValue)
                    }
                
                Toggle("显示高级设置", isOn: $showAdvancedSettings)
                
                if showAdvancedSettings {
                    TextField("服务器地址", text: $customHost)
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                    
                    TextField("端口", text: $customPort)
                        .keyboardType(.numberPad)
                    
                    TextField("用户名", text: $username)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                    
                    SecureField("密码", text: $password)
                    
                    Button(action: {
                        applySettings()
                    }) {
                        Text("应用设置")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
            
            Section(header: Text("同步设置")) {
                Button(action: {
                    mqttSyncService.syncNow()
                }) {
                    HStack {
                        Text("立即同步")
                        Spacer()
                        Image(systemName: "arrow.clockwise")
                    }
                }
                .disabled(mqttSyncService.isSyncing || !mqttService.connectionStatus)
                
                if let lastSyncTime = mqttSyncService.lastSyncTime {
                    HStack {
                        Text("上次同步")
                        Spacer()
                        Text(formatDate(lastSyncTime))
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Section(header: Text("连接日志")) {
                if mqttService.connectionStatus {
                    Label("已连接到服务器", systemImage: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    Label("未连接到服务器", systemImage: "xmark.circle.fill")
                        .foregroundColor(.red)
                }
                
                if let error = mqttService.lastError {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("错误信息")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(error.localizedDescription)
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                }
            }
            
            // 高级睡眠数据测试入口
            Section(header: Text("睡眠数据测试")) {
                NavigationLink(destination: GoMoreSleepTestView()) {
                    HStack {
                        Image(systemName: "waveform.path.ecg")
                            .foregroundColor(.teal)
                            .frame(width: 24)
                        Text("GoMore高级睡眠数据测试")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                    .padding(.vertical, 4)
                }
            }
        }
        .navigationTitle("MQTT设置")
        .onAppear {
            // 从UserDefaults加载设置
            customHost = UserDefaults.standard.string(forKey: "mqtt_host") ?? "*************"
            customPort = UserDefaults.standard.string(forKey: "mqtt_port") ?? "12930"
            username = UserDefaults.standard.string(forKey: "mqtt_username") ?? ""
            password = UserDefaults.standard.string(forKey: "mqtt_password") ?? ""
        }
    }
    
    /// 应用设置
    private func applySettings() {
        // 保存设置到UserDefaults
        UserDefaults.standard.set(customHost, forKey: "mqtt_host")
        UserDefaults.standard.set(customPort, forKey: "mqtt_port")
        UserDefaults.standard.set(username, forKey: "mqtt_username")
        UserDefaults.standard.set(password, forKey: "mqtt_password")
        
        // 设置认证信息
        if !username.isEmpty && !password.isEmpty {
            mqttService.setCredentials(username: username, password: password)
        }
        
        // 重新连接
        mqttService.disconnect()
        
        // 延迟1秒后重新连接
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            mqttService.connect()
        }
    }
    
    /// 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }
}

#Preview {
    NavigationStack {
        MQTTSettingsView()
            .environmentObject(MQTTService.shared)
            .environmentObject(MQTTSyncService.shared)
    }
} 
