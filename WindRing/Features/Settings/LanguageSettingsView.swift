//import SwiftUI
//
//struct LanguageSettingsView: View {
//    @Environment(\.presentationMode) var presentationMode
//    
////    @State private var selectedLanguage: AppLanguage
//    @State private var showRestartAlert = false
//    
//    init() {
//        // 初始化选中的语言为当前语言
////        _selectedLanguage = State(initialValue: LanguageManager.shared.currentLanguage)
//    }
//    
//    var body: some View {
//        ZStack(alignment: .top) {
//            // 背景色
//            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
//            
//            // 光晕背景
//            CornerGlowBackground()
//            
//            VStack(spacing: 0) {
//                // 自定义导航栏
//                HStack(alignment: .center) {
//                    // 返回按钮
//                    Button(action: {
//                        presentationMode.wrappedValue.dismiss()
//                    }) {
//                        Image(systemName: "chevron.left")
//                            .font(.system(size: 17, weight: .semibold))
//                            .foregroundColor(.white)
//                    }
//                    
//                    // 标题
//                    Text("language_settings".localized)
//                        .font(.custom("PingFang-SC-Heavy", size: 19))
//                        .foregroundColor(.white)
//                        .padding(.leading, 5)
//                    
//                    Spacer()
//                }
//                .padding(.horizontal, 16)
//                .frame(height: 44)
//                .padding(.top, 8)
//                
//                // 语言选择卡片
//                VStack(spacing: 0) {
//                    ForEach(AppLanguage.allCases) { language in
//                        languageRow(language)
//                        
//                        if language != AppLanguage.allCases.last {
//                            Divider()
//                                .background(Color(white: 0.2))
//                                .padding(.vertical, 1)
//                                .padding(.horizontal, 16)
//                        }
//                    }
//                }
//                .padding(.vertical, 5)
//                .background(Color(red: 0.06, green: 0.07, blue: 0.1))
//                .cornerRadius(25)
//                .padding(.horizontal, 10)
//                .padding(.top, 20)
//                
//                // 说明文本
//                Text("set_language_restart".localized)
//                    .font(.system(size: 14))
//                    .foregroundColor(Color(white: 0.7))
//                    .multilineTextAlignment(.center)
//                    .padding(.horizontal, 20)
//                    .padding(.top, 20)
//                
//                Spacer()
//                
//                // 应用按钮
//                Button(action: {
//                    if selectedLanguage != localizationService.currentLanguage {
//                        localizationService.setLanguage(selectedLanguage)
//                        showRestartAlert = true
//                    } else {
//                        presentationMode.wrappedValue.dismiss()
//                    }
//                }) {
//                    Text("confirm".localized)
//                        .font(.system(size: 17, weight: .medium))
//                        .foregroundColor(.white)
//                        .frame(maxWidth: .infinity)
//                        .frame(height: 50)
//                        .background(
//                            LinearGradient(
//                                gradient: Gradient(colors: [Color(hex: "#0A2B73"), Color(hex: "#0A3B93")]),
//                                startPoint: .leading,
//                                endPoint: .trailing
//                            )
//                        )
//                        .cornerRadius(25)
//                        .padding(.horizontal, 20)
//                        .padding(.bottom, 30)
//                }
//            }
//            .padding(.top, 10)
//        }
//        .navigationBarHidden(true)
//        .alert(isPresented: $showRestartAlert) {
//            Alert(
//                title: Text("success".localized),
//                message: Text("set_language_restart".localized),
//                dismissButton: .default(Text("ok".localized)) {
//                    localizationService.reloadApp()
//                    presentationMode.wrappedValue.dismiss()
//                }
//            )
//        }
//    }
//    
//    // 语言选择行
//    private func languageRow(_ language: AppLanguage) -> some View {
//        Button(action: {
//            selectedLanguage = language
//        }) {
//            HStack {
//                // 国旗图标
//                Text(language.flag)
//                    .font(.system(size: 24))
//                    .frame(width: 30, height: 30)
//                    .padding(.leading, 16)
//                
//                // 语言名称
//                Text(language.displayName)
//                    .font(.system(size: 17))
//                    .foregroundColor(.white)
//                    .padding(.leading, 8)
//                
//                Spacer()
//                
//                // 选择指示器
//                if selectedLanguage == language {
//                    Image(systemName: "checkmark.circle.fill")
//                        .foregroundColor(Color(hex: "#0A7B83"))
//                        .padding(.trailing, 16)
//                }
//            }
//            .padding(.vertical, 16)
//        }
//    }
//}
//
//struct LanguageSettingsView_Previews: PreviewProvider {
//    static var previews: some View {
//        Group {
//            LanguageSettingsView()
//                .previewDisplayName("English")
//                .localizationPreview(.english)
//            
//            LanguageSettingsView()
//                .previewDisplayName("Chinese")
//                .localizationPreview(.chinese)
//        }
//    }
//} 
