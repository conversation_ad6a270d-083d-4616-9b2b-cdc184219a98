import SwiftUI
import Charts
import CRPSmartRing

/// 步数明细测试视图
struct StepDetailTestView: View {
    @State private var stepDetails: [ActivityDetailData] = []
    @State private var isLoading = false
    @State private var selectedDay = 0
    @State private var message = "请点击按钮获取步数明细数据"
    @State private var messageColor: Color = .gray
    @State private var totalSteps = 0
    @State private var totalDistance = 0
    @State private var totalCalories = 0
    @State private var activeMinutes = 0
    @State private var showActivityData = false
    @State private var isUploading = false
    @State private var uploadResult = ""
    @State private var uploadResultColor: Color = .gray
    
    // 自动上传开关
    @State private var autoUploadEnabled: Bool = true
    
    var body: some View {
        NavigationView {
            VStack {
                // 顶部信息栏
                HStack {
                    Text(message)
                        .foregroundColor(messageColor)
                        .padding(.vertical, 8)
                    
                    Spacer()
                    
                    // 显示/隐藏活动数据切换按钮
                    if !stepDetails.isEmpty {
                        Button(action: {
                            withAnimation {
                                showActivityData.toggle()
                            }
                        }) {
                            Image(systemName: showActivityData ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                                .foregroundColor(.blue)
                                .imageScale(.large)
                        }
                    }
                }
                .padding(.horizontal)
                .frame(maxWidth: .infinity, alignment: .leading)
                
                // 活动数据卡片
                if showActivityData && !isLoading {
                    activityDataSection
                        .transition(.move(edge: .top).combined(with: .opacity))
                }
                
                // 自动上传状态显示
                HStack {
                    Text("自动上传状态:")
                        .font(.subheadline)
                    
                    Text("已开启")
                        .font(.subheadline)
                        .foregroundColor(.green)
                        .animation(.easeInOut, value: autoUploadEnabled)
                    
                    Spacer()
                    
                    // 显示上次上传时间
                    if let lastUploadTime = RawDataUploadService.shared.lastActivityUploadTime {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.footnote)
                            
                            Text("上次上传: \(formatDate(lastUploadTime))")
                                .font(.footnote)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // 添加验证按钮
                    Button(action: checkUploadStatus) {
                        Image(systemName: "questionmark.circle")
                            .foregroundColor(.blue)
                            .font(.footnote)
                    }
                    .padding(.leading, 8)
                }
                .padding(.horizontal)
                .padding(.top, 4)
                .padding(.bottom, 8)
                .background(Color.gray.opacity(0.05))
                .cornerRadius(8)
                .padding(.horizontal)
                
                // 日期选择器和操作按钮
                VStack(spacing: 10) {
                    HStack {
                        Text("选择日期:")
                        Picker("选择日期", selection: $selectedDay) {
                            Text("今天").tag(0)
                            Text("昨天").tag(1)
                            Text("前天").tag(2)
                            ForEach(3..<7) { day in
                                Text("\(day)天前").tag(day)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .frame(width: 100)
                        
                        Spacer()
                        
                        // 刷新按钮
                        Button(action: fetchStepDetails) {
                            HStack {
                                Image(systemName: "arrow.clockwise")
                                Text("刷新")
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                        }
                        .disabled(isLoading)
                    }
                    
                    // 上传按钮行
                    HStack {
                        // 上传结果提示
                        if !uploadResult.isEmpty {
                            Text(uploadResult)
                                .foregroundColor(uploadResultColor)
                                .lineLimit(1)
                                .truncationMode(.tail)
                        } else {
                            Spacer()
                        }
                        
                        // 手动上传按钮
                        Button(action: uploadActivityData) {
                            HStack {
                                Image(systemName: "arrow.up.to.line")
                                Text(isUploading ? "上传中..." : "手动上传")
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .foregroundColor(.white)
                            .background(isUploading ? Color.gray : Color.blue)
                            .cornerRadius(8)
                        }
                        .disabled(isUploading || stepDetails.isEmpty)
                    }
                }
                .padding(.horizontal)
                
                // 步数图表
                if !stepDetails.isEmpty {
                    stepsChartSection
                } else if isLoading {
                    ProgressView("加载中...")
                        .padding()
                        .frame(maxHeight: .infinity, alignment: .center)
                } else {
                    Text("暂无步数明细数据")
                        .foregroundColor(.gray)
                        .padding()
                        .frame(maxHeight: .infinity, alignment: .center)
                }
                
                // 数据列表
                if !stepDetails.isEmpty {
                    stepsListSection
                }
            }
            .navigationTitle("步数明细测试")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        Text("总步数: \(stepDetails.map { $0.steps }.reduce(0, +))")
                            .font(.footnote)
                            .foregroundColor(.gray)
                        
                        if !stepDetails.isEmpty {
                            // 显示自动上传标志
                            Image(systemName: "arrow.triangle.2.circlepath.circle.fill")
                                .foregroundColor(.green)
                        }
                        
                        // 添加验证按钮
                        Button(action: checkUploadStatus) {
                            Image(systemName: "checkmark.shield")
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
            .onAppear {
                // 刷新自动上传状态，如果服务还未启用自动上传则主动启用
                autoUploadEnabled = RawDataUploadService.shared.isAutoUploadEnabled
                
                // 如果连接已就绪但尚未启用自动上传，则自动启用
                if !autoUploadEnabled && WindRingDeviceService.shared.connectionState.isConnected {
                    updateAutoUploadSetting(enabled: true)
                }
                
                // 自动获取最新数据
                fetchStepDetails()
            }
        }
    }
    
    // MARK: - 活动数据区域
    private var activityDataSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("活动汇总数据")
                    .font(.headline)
                
                Spacer()
                
                // 显示自动上传状态
                HStack(spacing: 4) {
                    Image(systemName: "clock.badge.checkmark.fill")
                    Text("自动上传")
                }
                .font(.system(size: 14, weight: .medium))
                .padding(.horizontal, 10)
                .padding(.vertical, 4)
                .foregroundColor(.green)
                .background(Color.green.opacity(0.1))
                .cornerRadius(15)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 15) {
                // 步数卡片
                activityDataCard(
                    title: "步数",
                    value: "\(totalSteps)",
                    icon: "figure.walk",
                    color: .blue
                )
                
                // 距离卡片
                activityDataCard(
                    title: "距离",
                    value: String(format: "%.2f", Double(totalDistance) / 100000) + " km", // 厘米转公里
                    icon: "arrow.left.and.right",
                    color: .green
                )
            }
            
            HStack(spacing: 15) {
                // 卡路里卡片
                activityDataCard(
                    title: "卡路里",
                    value: "\(Int(Double(totalCalories) / 10000.0)) kcal",
                    icon: "flame.fill",
                    color: .orange
                )
                
                // 活动时间卡片
                activityDataCard(
                    title: "活动时间",
                    value: "\(activeMinutes) 分钟",
                    icon: "clock.fill",
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    // 活动数据卡片
    private func activityDataCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            
            Text(value)
                .font(.headline)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color.white)
        .cornerRadius(8)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - 步数图表区域
    private var stepsChartSection: some View {
        VStack {
            Text("半小时步数分布")
                .font(.headline)
                .padding(.top)
            
            Chart {
                ForEach(stepDetails) { detail in
                    if let date = detail.date {
                        BarMark(
                            x: .value("时间", date, unit: .hour),
                            y: .value("步数", detail.steps)
                        )
                        .foregroundStyle(Color.blue.gradient)
                    }
                }
            }
            .frame(height: 250)
            .padding()
            .chartXAxis {
                AxisMarks(values: .stride(by: .hour, count: 3)) { value in
                    AxisGridLine()
                    AxisValueLabel(format: .dateTime.hour())
                }
            }
        }
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
        .padding(.horizontal)
    }
    
    // MARK: - 步数列表区域
    private var stepsListSection: some View {
        VStack {
            HStack {
                Text("时间明细")
                    .font(.headline)
                
                Spacer()
                
                Text("共 \(stepDetails.count) 条记录")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding(.horizontal)
            
            List {
                ForEach(stepDetails) { detail in
                    HStack {
                        Text(detail.formattedTime())
                            .font(.system(.body, design: .monospaced))
                        
                        Spacer()
                        
                        Text("\(detail.steps) 步")
                            .foregroundColor(.blue)
                            .font(.headline)
                    }
                    .padding(.vertical, 4)
                }
            }
            .frame(height: 200)
            .listStyle(PlainListStyle())
        }
    }
    
    /// 获取步数明细数据
    private func fetchStepDetails() {
        // 清空之前的数据和状态
        stepDetails = []
        totalSteps = 0
        totalDistance = 0
        totalCalories = 0
        activeMinutes = 0
        uploadResult = ""
        
        isLoading = true
        
        // 计算实际日期并显示
        let calendar = Calendar.current
        let today = Date()
        let targetDate = calendar.date(byAdding: .day, value: -selectedDay, to: today) ?? today
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: targetDate)
        
        message = "正在获取\(dateString)的数据..."
        messageColor = .blue
        
        print("🔍 开始获取日期 \(dateString) 的步数数据 (day参数: \(selectedDay))")
        
        // 使用RawDataUploadService获取步数明细
        RawDataUploadService.shared.fetchStepDetailData(day: selectedDay) { details, error in
            print("📊 步数明细数据获取结果: \(details?.count ?? 0)条记录, 错误: \(error?.localizedDescription ?? "无")")
            
            // 获取活动汇总数据
            CRPSmartRingSDK.sharedInstance.getTrainingData(selectedDay) { record, trainingError in
                DispatchQueue.main.async {
                    isLoading = false
                    
                    print("🏃‍♂️ 活动汇总数据获取结果: 步数\(record.step), 距离\(record.distance), 卡路里\(record.cal), 活动时间\(record.exerciseTime)分钟")
                    print("📅 数据日期确认: SDK返回day值: \(record.day), 请求的day参数: \(selectedDay)")
                    
                    if let error = error {
                        message = "获取\(dateString)步数明细失败: \(error.localizedDescription)"
                        messageColor = .red
                        return
                    }
                    
                    if let details = details {
                        if details.isEmpty {
                            message = "\(dateString) 没有步数明细数据"
                            messageColor = .orange
                        } else {
                            let summaryTimes = details.map { detail in
                                if let date = detail.date {
                                    let timeFormatter = DateFormatter()
                                    timeFormatter.dateFormat = "HH:mm"
                                    return timeFormatter.string(from: date)
                                } else {
                                    return "未知时间"
                                }
                            }.joined(separator: ", ")
                            print("⏰ 步数明细记录时间点: \(summaryTimes)")
                            
                            stepDetails = details
                            let totalStepsInDetail = details.map { $0.steps }.reduce(0, +)
                            message = "\(dateString) 获取成功，共\(details.count)条记录，总计\(totalStepsInDetail)步"
                            messageColor = .green
                            
                            // 自动显示活动数据
                            showActivityData = true
                            
                            // 由于已开启自动上传，获取数据后自动上传
                            uploadActivityData()
                        }
                    } else {
                        message = "\(dateString) 获取数据返回为空"
                        messageColor = .red
                    }
                    
                    // 处理活动汇总数据
                    if trainingError == .none {
                        totalSteps = record.step
                        totalDistance = record.distance
                        totalCalories = record.cal
                        activeMinutes = record.exerciseTime
                    } else {
                        message += "，但活动总数据获取失败: \(trainingError)"
                        print("❌ 活动数据获取错误: \(trainingError)")
                    }
                }
            }
        }
    }
    
    /// 上传活动数据到服务器
    private func uploadActivityData() {
        isUploading = true
        uploadResult = "正在上传活动数据..."
        uploadResultColor = .blue
        
        RawDataUploadService.shared.uploadActivityData(day: selectedDay) { success, error in
            DispatchQueue.main.async {
                isUploading = false
                
                if success {
                    uploadResult = "活动数据上传成功！"
                    uploadResultColor = .green
                } else if let error = error {
                    uploadResult = "上传失败: \(error.localizedDescription)"
                    uploadResultColor = .red
                } else {
                    uploadResult = "上传失败: 未知错误"
                    uploadResultColor = .red
                }
            }
        }
    }
    
    /// 更新自动上传设置
    private func updateAutoUploadSetting(enabled: Bool) {
        if enabled {
            // 启用自动上传
            let success = RawDataUploadService.shared.startAutoUpload(interval: 300) // 5分钟
            if !success {
                // 如果启动失败，恢复开关状态
                DispatchQueue.main.async {
                    self.autoUploadEnabled = false
                }
            }
        } else {
            // 禁用自动上传
            RawDataUploadService.shared.stopAutoUpload()
        }
    }
    
    /// 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter.string(from: date)
    }
    
    /// 验证自动上传状态
    private func checkUploadStatus() {
        // 调用服务验证状态
        RawDataUploadService.shared.checkAutoUploadStatus()
        
        // 刷新UI上的状态显示
        DispatchQueue.main.async {
            self.autoUploadEnabled = RawDataUploadService.shared.isAutoUploadEnabled
            
            // 显示验证消息
            self.message = "已检查自动上传状态，详情见控制台日志"
            self.messageColor = .blue
            
            // 如果上传服务未启用但应该启用，则尝试重新启动
            if !RawDataUploadService.shared.isAutoUploadEnabled && 
               UserDefaults.standard.bool(forKey: "auto_upload_enabled") {
                updateAutoUploadSetting(enabled: true)
                self.message = "已重新启动自动上传服务"
                self.messageColor = .green
            }
        }
    }
}

struct StepDetailTestView_Previews: PreviewProvider {
    static var previews: some View {
        StepDetailTestView()
    }
} 