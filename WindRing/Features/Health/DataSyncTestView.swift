import SwiftUI
import Combine
import CoreData
#if os(iOS)
import CRPSmartRing
#endif

struct DataSyncTestView: View {
    // MARK: - 服务
    private let heartRateUploadService = HeartRateUploadService.shared
    private let hrvUploadService = HRVUploadService.shared
    private let stressUploadService = StressUploadService.shared
    private let bloodOxygenUploadService = BloodOxygenUploadService.shared
    private let sleepUploadService = SleepUploadService.shared
    private let temperatureUploadService = TemperatureUploadService.shared
    private let activityUploadService = ActivityUploadService.shared
    private let deviceService = WindRingDeviceService.shared
    private let authService = AuthService.shared
    private let healthDataManager = HealthDataManager.shared
    private let goMoreService = GoMoreService.shared
    
    // MARK: - 状态
    @State private var isLoading = false
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var syncResults: [SyncAction] = []
    @State private var lastSyncTime: Date? = UserDefaults.standard.object(forKey: "lastHRUploadTime") as? Date
    @State private var isLoggedIn: Bool = false
    @State private var totalDataCount: Int = 0
    @State private var uploadedDataCount: Int = 0
    @State private var cancellables = Set<AnyCancellable>()
    @State private var isSyncing = false
    @State private var showJsonDetails = false
    @State private var jsonDetails = ""
    @State private var jsonTitle = ""
    @State private var showProgressView = false
    @State private var progressMessage = ""
    @State private var isUploading = false
    @State private var uploadProgress = ""
    
    // MARK: - 初始化
    init() {
        // 设置SDK代理
        #if os(iOS)
        CRPSmartRingSDK.sharedInstance.delegate = SDKDelegate.shared
        #endif
    }
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 标题与图标
                VStack(spacing: 16) {
                    Image(systemName: "heart.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(Color.red)
                        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 2)
                    
                    Text("健康数据上传")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("从智能戒指获取并上传健康数据到云端")
                        .font(.subheadline)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top, 25)
                
                // 设备连接状态
                deviceStatusCard
                
                // 用户登录状态
                userStatusCard
                
                // 操作按钮
                actionButtonsCard
                
                // 同步状态和历史
                if !syncResults.isEmpty {
                    syncHistoryCard
                }
                
                // 上传数据统计
                dataStatsCard
                
                // 数据管理部分
                dataManagementSection
            }
            .padding()
        }
        .navigationBarTitle("健康数据上传", displayMode: .inline)
        .disabled(isLoading || showProgressView)
        .overlay(
            ZStack {
                if isLoading {
                    Color.black.opacity(0.4)
                        .edgesIgnoringSafeArea(.all)
                    
                    VStack {
                        ProgressView()
                            .scaleEffect(1.5)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        
                        Text("正在处理...")
                                .font(.headline)
                            .foregroundColor(.white)
                            .padding(.top, 10)
                    }
                } else if showProgressView {
                    Color.black.opacity(0.4)
                        .edgesIgnoringSafeArea(.all)
                    
                    VStack {
                        ProgressView()
                            .scaleEffect(1.5)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        
                        Text(progressMessage)
                                .font(.headline)
                            .foregroundColor(.white)
                            .padding(.top, 10)
                    }
                }
            }
        )
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertMessage),
                dismissButton: .default(Text("确定"))
            )
        }
        .sheet(isPresented: $showJsonDetails) {
            VStack(spacing: 20) {
                Text(jsonTitle)
                    .font(.headline)
                    .padding(.top)
                
                ScrollView {
                    Text(jsonDetails)
                        .font(.body)
                        .padding()
                        .multilineTextAlignment(.leading)
                }
                
                Button("关闭") {
                    showJsonDetails = false
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                                .cornerRadius(8)
                .padding(.bottom)
            }
            .padding()
        }
        .onAppear {
            checkLoginStatus()
            updateDataCounts()
            setupSubscriptions()
        }
        .onDisappear {
            // 清理订阅，防止内存泄漏
            cancellables.removeAll()
        }
    }
    
    // MARK: - 设备状态卡片
    private var deviceStatusCard: some View {
        VStack(alignment: .leading, spacing: 8) {
                        HStack {
                Image(systemName: "ring")
                    .font(.title2)
                    .foregroundColor(deviceService.connectionState.isConnected ? .green : .gray)
                
                Text("设备状态")
                                .font(.headline)
                        
                Spacer()
                
                            Text(deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized)
                    .font(.subheadline)
                    .foregroundColor(deviceService.connectionState.isConnected ? .green : .red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        deviceService.connectionState.isConnected ? 
                            Color.green.opacity(0.2) : Color.red.opacity(0.2)
                    )
                    .cornerRadius(4)
            }
            
            if deviceService.connectionState.isConnected {
                        Divider()
                            
                            HStack {
                    Text("设备名称")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(deviceService.deviceInfo?.localName ?? "未知设备")
                        .font(.subheadline)
                }
                        
                                    HStack {
                    Text("电池电量")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Image(systemName: getBatteryIcon())
                            .foregroundColor(getBatteryColor())
                        
                        Text("\(deviceService.batteryLevel)%")
                            .font(.subheadline)
                    }
                }
            } else {
                            Divider()
                        
                Text("请先连接您的智能戒指设备")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
                        }
                                .padding()
        .background(Color(.systemBackground))
                        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                            }
                            
    // MARK: - 用户状态卡片
    private var userStatusCard: some View {
                            VStack(alignment: .leading, spacing: 8) {
                            HStack {
                Image(systemName: "person.circle")
                    .font(.title2)
                    .foregroundColor(isLoggedIn ? .blue : .gray)
                
                Text("用户状态")
                            .font(.headline)
                        
                Spacer()
                
                Text(isLoggedIn ? "已登录" : "未登录")
                    .font(.subheadline)
                    .foregroundColor(isLoggedIn ? .blue : .red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        isLoggedIn ? 
                            Color.blue.opacity(0.2) : Color.red.opacity(0.2)
                    )
                    .cornerRadius(4)
            }
            
            if isLoggedIn {
                            Divider()
                        
                        HStack {
                    Text("用户名")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(authService.currentUser?.nickname ?? "未知用户")
                        .font(.subheadline)
                }
            } else {
                Divider()
                
                Text("请先登录以便上传数据到云端")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Button(action: {
                    simulateLogin()
                            }) {
                    HStack {
                        Image(systemName: "person.badge.key.fill")
                        Text("快速测试登录")
                    }
                                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                                    .background(Color.blue)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
                            .padding(.top, 8)
                        }
                    }
                    .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 操作按钮卡片
    private var actionButtonsCard: some View {
        VStack(spacing: 16) {
            // 获取心率数据按钮
                            Button(action: {
                fetchHeartRateData()
            }) {
                HStack {
                    Image(systemName: "arrow.down.heart.fill")
                        .font(.headline)
                    
                    Text("从设备获取心率数据")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                .background(deviceService.connectionState.isConnected ? 
                            Color.orange : Color.gray)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected)
                            
            // 获取HRV数据按钮
                            Button(action: {
                fetchHRVData()
            }) {
                HStack {
                    Image(systemName: "arrow.down.heart.fill")
                        .font(.headline)
                    
                    Text("从设备获取HRV数据")
                        .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                .background(deviceService.connectionState.isConnected ? 
                            Color.purple : Color.gray)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected)
                            
            // 获取压力数据按钮
                            Button(action: {
                fetchStressData()
            }) {
                HStack {
                    Image(systemName: "arrow.down.heart.fill")
                        .font(.headline)
                    
                    Text("从设备获取压力数据")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                .background(deviceService.connectionState.isConnected ? 
                            Color.pink : Color.gray)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected)
                            
            // 获取血氧数据按钮
                            Button(action: {
                fetchBloodOxygenData()
            }) {
                HStack {
                    Image(systemName: "arrow.down.heart.fill")
                        .font(.headline)
                    
                    Text("从设备获取血氧数据")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                .background(deviceService.connectionState.isConnected ? 
                            Color.cyan : Color.gray)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected)
                            
            // 上传心率数据按钮
                            Button(action: {
                uploadHeartRateData()
            }) {
            HStack {
                    Image(systemName: "arrow.up.heart.fill")
                    .font(.headline)
                
                    Text("上传心率数据到云端")
                        .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.blue : Color.gray)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
                            
            // 上传HRV数据按钮
                            Button(action: {
                uploadHRVData()
            }) {
                HStack {
                    Image(systemName: "arrow.up.heart.fill")
                            .font(.headline)
                        
                    Text("上传HRV数据到云端")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.indigo : Color.gray)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
                            
            // 上传压力数据按钮
                            Button(action: {
                                uploadStressData()
                            }) {
                HStack {
                    Image(systemName: "arrow.up.heart.fill")
                                .font(.headline)
                    
                    Text("上传压力数据到云端")
                        .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.red : Color.gray)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
                            
            // 上传血氧数据按钮
                            Button(action: {
                uploadBloodOxygenData()
            }) {
                HStack {
                    Image(systemName: "arrow.up.heart.fill")
                            .font(.headline)
                        
                    Text("上传血氧数据到云端")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.blue.opacity(0.7) : Color.gray)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
                            
            // 全部获取并上传
                            Button(action: {
                syncAllHeartRateData()
            }) {
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                            .font(.headline)
                        
                    Text("获取并上传全部心率数据")
                        .font(.headline)
                        }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.green : Color.gray)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
                            
            // 全部获取并上传HRV数据
                            Button(action: {
                syncAllHRVData()
            }) {
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                                .font(.headline)
                    
                    Text("获取并上传全部HRV数据")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.teal : Color.gray)
                .foregroundColor(.white)
                            .cornerRadius(8)
                        }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
            
            // 全部获取并上传压力数据
                            Button(action: {
                syncAllStressData()
            }) {
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                                    .font(.headline)
                    
                    Text("获取并上传全部压力数据")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.orange : Color.gray)
                .foregroundColor(.white)
                                .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
                            
            // 全部获取并上传血氧数据
                            Button(action: {
                syncAllBloodOxygenData()
            }) {
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                            .font(.headline)
                        
                    Text("获取并上传全部血氧数据")
                        .font(.headline)
                        }
                                    .frame(maxWidth: .infinity)
                    .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.cyan.opacity(0.8) : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
                            
            // 获取睡眠数据按钮
                            Button(action: {
                fetchSleepData()
            }) {
                HStack {
                    Image(systemName: "moon.fill")
                                .font(.headline)
                    
                    Text("从设备获取睡眠数据")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                .padding()
                .background(deviceService.connectionState.isConnected ? 
                            Color.purple : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(!deviceService.connectionState.isConnected)
            
            // 获取体温数据按钮
            Button(action: {
                fetchTemperatureData()
            }) {
                HStack {
                    Image(systemName: "thermometer")
                                .font(.headline)
                    
                    Text("从设备获取体温数据")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                .padding()
                .background(deviceService.connectionState.isConnected ? 
                            Color.orange : Color.gray)
                .foregroundColor(.white)
                            .cornerRadius(8)
                        }
            .disabled(!deviceService.connectionState.isConnected)
            
            // 上传体温数据按钮
            Button(action: {
                uploadTemperatureData()
            }) {
                HStack {
                    Image(systemName: "arrow.up.doc")
                                    .font(.headline)
                    
                    Text("上传体温数据到云端")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.teal : Color.gray)
                .foregroundColor(.white)
                                .cornerRadius(8)
                            }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
            
            // 测试直接上传体温数据按钮
            Button(action: {
                testDirectTemperatureUpload()
            }) {
                HStack {
                    Image(systemName: "network")
                                .font(.headline)
                    
                    Text("测试体温数据上传")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                .padding()
                .background(isLoggedIn ? 
                            Color.green : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(!isLoggedIn)
            
            // 获取活动数据按钮
            Button(action: {
                fetchActivityData()
            }) {
                HStack {
                    Image(systemName: "figure.walk")
                                .font(.headline)
                    
                    Text("从设备获取活动数据")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                .padding()
                .background(deviceService.connectionState.isConnected ? 
                            Color.green.opacity(0.8) : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(!deviceService.connectionState.isConnected)
            
            // 上传活动数据按钮
            Button(action: {
                uploadActivityData()
            }) {
                HStack {
                    Image(systemName: "arrow.up.forward")
                                .font(.headline)
                    
                    Text("上传活动数据到云端")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.blue.opacity(0.8) : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
            
            // 获取并上传全部活动数据按钮
            Button(action: {
                syncAllActivityData()
            }) {
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                                .font(.headline)
                    
                    Text("获取并上传全部活动数据")
                            .font(.headline)
                }
                                    .frame(maxWidth: .infinity)
                .padding()
                .background(deviceService.connectionState.isConnected && isLoggedIn ? 
                            Color.green : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(!deviceService.connectionState.isConnected || !isLoggedIn)
            
            // 使用Postman设置直接上传睡眠数据
            Button(action: {
                uploadWithPostmanSettings()
            }) {
                HStack {
                    Image(systemName: "network")
                            .font(.headline)
                        
                    Text("使用Postman设置上传数据")
                        .font(.headline)
                        }
                .frame(maxWidth: .infinity)
                    .padding()
                .background(isLoggedIn ? Color.green : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(!isLoggedIn)
            
            // 上次同步时间
            if let lastSync = lastSyncTime {
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.secondary)
                    Text("上次同步: \(dateFormatter.string(from: lastSync))")
                        .font(.footnote)
                        .foregroundColor(.secondary)
                }
            }
                            }
                            .padding()
        .background(Color(.systemBackground))
                            .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 同步历史卡片
    private var syncHistoryCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "clock.arrow.circlepath")
                    .font(.title2)
                    .foregroundColor(.purple)
                
                Text("同步历史记录")
                    .font(.headline)
                
                Spacer()
                
                Button(action: {
                    syncResults = []
                }) {
                    Text("清除")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
            
                        Divider()
            
            if syncResults.isEmpty {
                Text("暂无同步记录")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 8)
                    } else {
                ForEach(syncResults) { action in
                    HStack(spacing: 12) {
                        Image(systemName: action.iconName)
                            .foregroundColor(action.color)
                            .frame(width: 24, height: 24)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(action.title)
                                .font(.subheadline)
                                .foregroundColor(.primary)
                            
                            Text(action.description)
                                .font(.caption)
                                    .foregroundColor(.secondary)
                        }
                        
                    Spacer()
                    
                        Text(timeFormatter.string(from: action.timestamp))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                    
                    if action.id != syncResults.last?.id {
                        Divider()
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 数据统计卡片
    private var dataStatsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .font(.title2)
                    .foregroundColor(.orange)
                
                Text("数据统计")
                    .font(.headline)
                
                Spacer()
                
                Button(action: {
                    updateDataCounts()
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }
            }
            
                        Divider()
                            
                HStack {
                VStack(alignment: .center, spacing: 4) {
                    Text("\(totalDataCount)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("本地健康数据")
                        .font(.caption)
                                    .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                
                        Divider()
                    .frame(height: 40)
                
                VStack(alignment: .center, spacing: 4) {
                    Text(isLoggedIn ? "\(uploadedDataCount)" : "-")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("已上传数据")
                        .font(.caption)
                                    .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                                }
                                .frame(maxWidth: .infinity)
                            }
                }
                .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 数据管理部分
    private var dataManagementSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("数据管理")
                .font(.headline)
                .padding(.bottom, 4)
            
            // 添加恢复自动上传按钮
            Button(action: {
                restoreAutoUpload()
            }) {
                HStack {
                    Image(systemName: "arrow.clockwise.circle.fill")
                    Text("恢复自动上传")
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.green)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .padding(.bottom, 8)
            
            // ... existing code ...
        }
    }
    
    // MARK: - 恢复自动上传
    private func restoreAutoUpload() {
        let wasEnabled = UserDefaults.standard.bool(forKey: "auto_upload_enabled")
        
        // 无论之前的设置如何，都启用自动上传
        RawDataUploadService.shared.startAutoUpload()
        
        // 显示结果
        alertTitle = "自动上传已恢复"
        alertMessage = wasEnabled ? "自动上传功能已重新启动" : "自动上传功能已启用"
        showAlert = true
        
        // 打印日志
        print("已恢复自动上传功能")
    }
    
    // MARK: - 功能方法
    /// 获取心率数据
    private func fetchHeartRateData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        isLoading = true
        
        // 将单次心率历史改为定时心率数据（3天）
        heartRateUploadService.syncTimingHeartRateData(days: 3) { count, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if let error = error {
                    showAlert(title: "获取失败", message: error.localizedDescription)
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取定时心率数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                                } else {
                    showAlert(
                        title: "获取成功",
                        message: "成功从设备获取\(count)条定时心率数据"
                    )
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取定时心率数据成功",
                            description: "成功获取\(count)条定时心率数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    updateDataCounts()
                }
            }
        }
    }
    
    /// 获取HRV数据
    private func fetchHRVData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        isLoading = true
        
        // 获取定时HRV数据（3天）
        hrvUploadService.syncTimingHRVData(days: 3) { count, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if let error = error {
                    showAlert(title: "获取失败", message: error.localizedDescription)
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取定时HRV数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else {
                    showAlert(
                        title: "获取成功",
                        message: "成功从设备获取\(count)条定时HRV数据"
                    )
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取定时HRV数据成功",
                            description: "成功获取\(count)条定时HRV数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    updateDataCounts()
                }
            }
        }
    }
    
    /// 获取压力数据
    private func fetchStressData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        isLoading = true
        
        // 获取压力数据（使用历史数据方法更可靠）
        stressUploadService.fetchAndSaveStressHistory { count, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if let error = error {
                    showAlert(title: "获取失败", message: error.localizedDescription)
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取压力数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else {
                    let successMessage = count > 0 
                        ? "成功从设备获取\(count)条压力数据"
                        : "成功连接设备，但未获取到压力数据。请确认设备上有压力数据记录。"
                    
                    showAlert(
                        title: "获取成功",
                        message: successMessage
                    )
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取压力数据" + (count > 0 ? "成功" : "完成"),
                            description: count > 0 ? "成功获取\(count)条压力数据" : "未获取到任何压力数据",
                            iconName: count > 0 ? "checkmark.circle.fill" : "exclamationmark.circle",
                            color: count > 0 ? .green : .orange
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    updateDataCounts()
                }
            }
        }
    }
    
    /// 获取血氧数据
    private func fetchBloodOxygenData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        isLoading = true
        
        // 获取血氧数据
        bloodOxygenUploadService.fetchAndSaveBloodOxygenHistory { count, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if let error = error {
                    showAlert(title: "获取失败", message: error.localizedDescription)
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取血氧数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                                } else {
                    let successMessage = count > 0 
                        ? "成功从设备获取\(count)条血氧数据"
                        : "成功连接设备，但未获取到血氧数据。请确认设备上有血氧数据记录。"
                    
                    showAlert(
                        title: "获取成功",
                        message: successMessage
                    )
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取血氧数据" + (count > 0 ? "成功" : "完成"),
                            description: count > 0 ? "成功获取\(count)条血氧数据" : "未获取到任何血氧数据",
                            iconName: count > 0 ? "checkmark.circle.fill" : "exclamationmark.circle",
                            color: count > 0 ? .green : .orange
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    updateDataCounts()
                }
            }
        }
    }
    
    /// 上传本地心率数据到云端
    private func uploadHeartRateData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        guard isLoggedIn else {
            showAlert(title: "错误", message: "请先登录后再上传数据")
            return
        }
        
        isLoading = true
        
        heartRateUploadService.uploadPendingHeartRateData { count, error in
            DispatchQueue.main.async {
                isLoading = false
                self.lastSyncTime = Date()
                UserDefaults.standard.set(self.lastSyncTime, forKey: "lastHRUploadTime")
                
                if let error = error {
                    showAlert(title: "上传失败", message: error.localizedDescription)
                    
                    syncResults.insert(
                        SyncAction(
                            title: "上传心率数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else {
                    showAlert(
                        title: "上传成功",
                        message: "成功上传\(count)条心率数据到云端"
                    )
                    
                    syncResults.insert(
                        SyncAction(
                            title: "上传心率数据成功",
                            description: "成功上传\(count)条心率数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    self.updateDataCounts()
                }
            }
        }
    }
    
    /// 上传本地HRV数据到云端
    private func uploadHRVData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        guard isLoggedIn else {
            showAlert(title: "错误", message: "请先登录后再上传数据")
            return
        }
        
        isLoading = true
        
        hrvUploadService.uploadPendingHRVData { count, error in
            DispatchQueue.main.async {
                isLoading = false
                self.lastSyncTime = Date()
                UserDefaults.standard.set(self.lastSyncTime, forKey: "lastHRVUploadTime")
                
                if let error = error {
                    showAlert(title: "上传失败", message: error.localizedDescription)
                    
                    syncResults.insert(
                        SyncAction(
                            title: "上传HRV数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else {
                    showAlert(
                        title: "上传成功",
                        message: "成功上传\(count)条HRV数据到云端"
                    )
                    
                    syncResults.insert(
                        SyncAction(
                            title: "上传HRV数据成功",
                            description: "成功上传\(count)条HRV数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    self.updateDataCounts()
                }
            }
        }
    }
    
    /// 上传本地压力数据到云端
    private func uploadStressData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        guard isLoggedIn else {
            showAlert(title: "错误", message: "请先登录后再上传数据")
            return
        }
        
        isLoading = true
        
        stressUploadService.uploadPendingStressData { count, error in
            DispatchQueue.main.async {
                isLoading = false
                self.lastSyncTime = Date()
                UserDefaults.standard.set(self.lastSyncTime, forKey: "lastStressUploadTime")
                
                if let error = error {
                    showAlert(title: "上传失败", message: error.localizedDescription)
                    
                    syncResults.insert(
                        SyncAction(
                            title: "上传压力数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else {
                    showAlert(
                        title: "上传成功",
                        message: "成功上传\(count)条压力数据到云端"
                    )
                    
                    syncResults.insert(
                        SyncAction(
                            title: "上传压力数据成功",
                            description: "成功上传\(count)条压力数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    self.updateDataCounts()
                }
            }
        }
    }
    
    /// 上传本地血氧数据到云端
    private func uploadBloodOxygenData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        guard isLoggedIn else {
            showAlert(title: "错误", message: "请先登录后再上传数据")
            return
        }
        
        // 在上传前添加控制台消息，提醒用户查看控制台输出
        print("\n==== 开始获取并上传血氧数据，请查看控制台输出 ====\n")
            
        isLoading = true
            
        // 获取血氧历史数据并上传
        bloodOxygenUploadService.fetchAndSaveBloodOxygenHistory { fetchCount, fetchError in
            if let fetchError = fetchError {
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.showAlert(title: "获取数据失败", message: fetchError.localizedDescription)
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "血氧数据同步失败",
                            description: fetchError.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                }
                return
            }
            
            print("成功获取\(fetchCount)条血氧数据，准备上传...")
            
            if fetchCount > 0 {
                // 上传获取到的血氧数据
                self.bloodOxygenUploadService.uploadPendingBloodOxygenData { uploadCount, uploadError in
                    DispatchQueue.main.async {
                        self.isLoading = false
                        self.lastSyncTime = Date()
                        UserDefaults.standard.set(self.lastSyncTime, forKey: "lastBloodOxygenUploadTime")
                        
                        if let uploadError = uploadError {
                            self.showAlert(title: "上传失败", message: uploadError.localizedDescription)
                            
                            self.syncResults.insert(
                                SyncAction(
                                    title: "血氧数据同步部分完成",
                                    description: "获取了\(fetchCount)条数据，但上传失败: \(uploadError.localizedDescription)",
                                    iconName: "exclamationmark.triangle",
                                    color: .orange
                                ),
                                at: 0
                            )
                } else {
                            // 显示JSON信息
                            self.jsonTitle = "血氧数据同步上传"
                            self.jsonDetails = "请在Xcode控制台中查看血氧数据上传的JSON格式和响应数据。\n\n获取：\(fetchCount)条记录\n上传成功：\(uploadCount)条记录"
                            self.showJsonDetails = true
                            
                            self.syncResults.insert(
                                SyncAction(
                                    title: "血氧数据同步成功",
                                    description: "获取并上传\(uploadCount)条数据",
                                    iconName: "checkmark.circle.fill",
                                    color: .green
                                ),
                                at: 0
                            )
                        }
                        
                        // 更新总数据数量
                        self.updateDataCounts()
                    }
                }
            } else {
                // 没有获取到任何数据
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastBloodOxygenUploadTime")
                    
                    self.showAlert(
                        title: "同步完成",
                        message: "未获取到任何血氧数据，请确认设备上有血氧数据记录"
                    )
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "血氧数据同步完成",
                            description: "未获取到血氧数据",
                            iconName: "exclamationmark.circle",
                            color: .orange
                        ),
                        at: 0
                    )
                }
            }
        }
    }
    
    /// 获取并上传所有心率数据
    private func syncAllHeartRateData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        guard isLoggedIn else {
            showAlert(title: "错误", message: "请先登录后再上传数据")
                return
            }
            
        isLoading = true
            
        // 改为获取定时心率数据
        heartRateUploadService.syncTimingHeartRateData(days: 3) { totalCount, error in
            DispatchQueue.main.async {
                isLoading = false
                self.lastSyncTime = Date()
                UserDefaults.standard.set(self.lastSyncTime, forKey: "lastHRUploadTime")
                
                if let error = error {
                    showAlert(title: "同步失败", message: error.localizedDescription)
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "定时心率数据同步失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else {
                    showAlert(
                        title: "同步成功",
                        message: "成功获取并上传\(totalCount)条定时心率数据"
                    )
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "定时心率数据同步成功",
                            description: "同步\(totalCount)条数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    self.updateDataCounts()
                }
            }
        }
    }
    
    /// 获取并上传所有HRV数据
    private func syncAllHRVData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        guard isLoggedIn else {
            showAlert(title: "错误", message: "请先登录后再上传数据")
                return
            }
            
        isLoading = true
            
        // 获取定时HRV数据
        hrvUploadService.syncTimingHRVData(days: 3) { totalCount, error in
            DispatchQueue.main.async {
                isLoading = false
                self.lastSyncTime = Date()
                UserDefaults.standard.set(self.lastSyncTime, forKey: "lastHRVUploadTime")
                
                if let error = error {
                    showAlert(title: "同步失败", message: error.localizedDescription)
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "定时HRV数据同步失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else {
                    showAlert(
                        title: "同步成功",
                        message: "成功获取并上传\(totalCount)条定时HRV数据"
                    )
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "定时HRV数据同步成功",
                            description: "同步\(totalCount)条数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    self.updateDataCounts()
                }
            }
        }
    }
    
    /// 获取并上传所有压力数据
    private func syncAllStressData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        guard isLoggedIn else {
            showAlert(title: "错误", message: "请先登录后再上传数据")
                return
            }
            
        isLoading = true
            
        // 获取压力历史数据并上传
        stressUploadService.fetchAndSaveStressHistory { fetchCount, fetchError in
            if let fetchError = fetchError {
            DispatchQueue.main.async {
                    self.isLoading = false
                    self.showAlert(title: "获取数据失败", message: fetchError.localizedDescription)
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "压力数据同步失败",
                            description: fetchError.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                }
                return
            }
            
            print("成功获取\(fetchCount)条压力数据，准备上传...")
            
            if fetchCount > 0 {
                // 上传获取到的压力数据
                self.stressUploadService.uploadPendingStressData { uploadCount, uploadError in
            DispatchQueue.main.async {
                        self.isLoading = false
                        self.lastSyncTime = Date()
                        UserDefaults.standard.set(self.lastSyncTime, forKey: "lastStressUploadTime")
                        
                        if let uploadError = uploadError {
                            self.showAlert(title: "上传失败", message: uploadError.localizedDescription)
                            
                            self.syncResults.insert(
                                SyncAction(
                                    title: "压力数据同步部分完成",
                                    description: "获取了\(fetchCount)条数据，但上传失败: \(uploadError.localizedDescription)",
                                    iconName: "exclamationmark.triangle",
                                    color: .orange
                                ),
                                at: 0
                            )
        } else {
                            self.showAlert(
                                title: "同步成功",
                                message: "成功获取\(fetchCount)条压力数据并上传\(uploadCount)条到云端"
                            )
                            
                            self.syncResults.insert(
                                SyncAction(
                                    title: "压力数据同步成功",
                                    description: "获取并上传\(uploadCount)条数据",
                                    iconName: "checkmark.circle.fill",
                                    color: .green
                                ),
                                at: 0
                            )
                        }
                        
                        // 更新总数据数量
                        self.updateDataCounts()
                    }
                }
            } else {
                // 没有获取到任何数据
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastStressUploadTime")
                    
                    self.showAlert(
                        title: "同步完成",
                        message: "未获取到任何压力数据，请确认设备上有压力数据记录"
                    )
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "压力数据同步完成",
                            description: "未获取到压力数据",
                            iconName: "exclamationmark.circle",
                            color: .orange
                        ),
                        at: 0
                    )
                }
            }
        }
    }
    
    /// 获取并上传所有血氧数据
    private func syncAllBloodOxygenData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        guard isLoggedIn else {
            showAlert(title: "错误", message: "请先登录后再上传数据")
            return
        }
            
        // 在上传前添加控制台消息，提醒用户查看控制台输出
        print("\n==== 开始获取并上传血氧数据，请查看控制台输出 ====\n")
        
        isLoading = true
            
        // 获取血氧历史数据并上传
        bloodOxygenUploadService.fetchAndSaveBloodOxygenHistory { fetchCount, fetchError in
            if let fetchError = fetchError {
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.showAlert(title: "获取数据失败", message: fetchError.localizedDescription)
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "血氧数据同步失败",
                            description: fetchError.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                }
                return
            }
            
            print("成功获取\(fetchCount)条血氧数据，准备上传...")
            
            if fetchCount > 0 {
                // 上传获取到的血氧数据
                self.bloodOxygenUploadService.uploadPendingBloodOxygenData { uploadCount, uploadError in
                DispatchQueue.main.async {
                        self.isLoading = false
                        self.lastSyncTime = Date()
                        UserDefaults.standard.set(self.lastSyncTime, forKey: "lastBloodOxygenUploadTime")
                        
                        if let uploadError = uploadError {
                            self.showAlert(title: "上传失败", message: uploadError.localizedDescription)
                            
                            self.syncResults.insert(
                                SyncAction(
                                    title: "血氧数据同步部分完成",
                                    description: "获取了\(fetchCount)条数据，但上传失败: \(uploadError.localizedDescription)",
                                    iconName: "exclamationmark.triangle",
                                    color: .orange
                                ),
                                at: 0
                            )
                    } else {
                            // 显示JSON信息
                            self.jsonTitle = "血氧数据同步上传"
                            self.jsonDetails = "请在Xcode控制台中查看血氧数据上传的JSON格式和响应数据。\n\n获取：\(fetchCount)条记录\n上传成功：\(uploadCount)条记录"
                            self.showJsonDetails = true
                            
                            self.syncResults.insert(
                                SyncAction(
                                    title: "血氧数据同步成功",
                                    description: "获取并上传\(uploadCount)条数据",
                                    iconName: "checkmark.circle.fill",
                                    color: .green
                                ),
                                at: 0
                            )
                        }
                        
                        // 更新总数据数量
                        self.updateDataCounts()
                    }
                }
            } else {
                // 没有获取到任何数据
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastBloodOxygenUploadTime")
                    
                    self.showAlert(
                        title: "同步完成",
                        message: "未获取到任何血氧数据，请确认设备上有血氧数据记录"
                    )
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "血氧数据同步完成",
                            description: "未获取到血氧数据",
                            iconName: "exclamationmark.circle",
                            color: .orange
                        ),
                        at: 0
                    )
                }
            }
        }
    }
    
    // 添加睡眠数据获取方法
    private func fetchSleepData() {
        guard deviceService.connectionState.isConnected else {
            syncResults.insert(
                SyncAction(
                    title: "睡眠数据获取失败",
                    description: "设备未连接",
                    iconName: "xmark.circle.fill",
                    color: .red
                ),
                at: 0
            )
            return
        }
        
        // 获取当前用户
//        let users = healthDataManager.getAllUsers()
//        print("📊 系统中的用户列表: \(users?.map { $0.id ?? "未知" } ?? ["无用户"])")
        
        // 确保有用户存在
//        guard let users = users, !users.isEmpty else {
            // 如果没有用户，尝试创建一个用户
//            do {
//                HealthDataManager.shared.createUser(id: "current_user", name: "默认用户", email: "<EMAIL>", completion: {success in
//                    if success {
//                        print("已创建新用户: current_user")
//                        
//                        // 创建用户成功后，递归调用同一个函数重新开始流程
//                        self.fetchSleepData()
//                        return
//                    } else {
//                        print("创建用户失败")
//                        DispatchQueue.main.async {
//                            self.syncResults.insert(
//                                SyncAction(
//                                    title: "睡眠数据获取失败",
//                                    description: "无法创建用户",
//                                    iconName: "xmark.circle.fill",
//                                    color: .red
//                                ),
//                                at: 0
//                            )
//                        }
//                        return
//                    }
//                })
//            } catch {
//                print("创建用户出错: \(error)")
//                DispatchQueue.main.async {
//                    self.syncResults.insert(
//                        SyncAction(
//                            title: "睡眠数据获取失败",
//                            description: "无法创建用户",
//                            iconName: "xmark.circle.fill",
//                            color: .red
//                        ),
//                        at: 0
//                    )
//                }
//                return
//            }
//        }
        
        // 使用第一个用户ID或默认的current_user
//        let userId = users?.first?.id ?? "current_user"
//        print("🔍 使用用户ID: \(userId) 获取睡眠数据")
        
        isSyncing = true
        
        // 添加提示信息
        print("\n============================================")
        print("开始从设备获取最近14天的睡眠数据，将以JSON格式输出结果...")
        print("============================================\n")

        // 先检查设备是否支持GoMore算法
        goMoreService.checkGoMoreSupport { isSupported, error in
            if isSupported {
                print("设备支持GoMore算法，使用GoMore睡眠数据获取")
                // 使用GoMore算法获取最近14天的睡眠数据，传入正确的用户ID
//                self.fetch14DaysGoMoreSleepData(userId: userId)
            } else {
                print("设备不支持GoMore算法，使用基础睡眠算法")
                // 使用基础睡眠算法获取最近14天的数据，传入正确的用户ID
//                self.fetch14DaysBasicSleepData(userId: userId)
            }
        }
    }
    
    // 使用基础睡眠算法获取最近14天的睡眠数据
    private func fetch14DaysBasicSleepData(userId: String) {
        var processedDays = 0
        var successDays = 0
        var allSleepDataJson: [[String: Any]] = []
        
        // 递归函数，逐天获取睡眠数据
        func fetchDayData(day: Int) {
            guard day < 14 else { // 最多获取14天
                // 所有天数已处理完毕
                DispatchQueue.main.async {
                    self.isSyncing = false
                    
                    if successDays > 0 {
                        // 显示获取成功的消息
                        self.syncResults.insert(
                            SyncAction(
                                title: "睡眠数据获取成功",
                                description: "成功获取\(successDays)天睡眠数据",
                                iconName: "checkmark.circle.fill",
                                color: .green
                            ),
                            at: 0
                        )
                        
                        // 显示JSON数据
                        let jsonString = self.formatAllSleepDataJson(allSleepDataJson)
                        self.jsonTitle = "最近\(allSleepDataJson.count)天睡眠数据"
                        self.jsonDetails = jsonString
                        self.showJsonDetails = true
                        
                        print("\n============= 最近14天睡眠数据输出 =============")
                        print("总共获取到\(allSleepDataJson.count)天的睡眠数据")
                        print(jsonString)
                        print("=============================================\n")
                    } else {
                        // 显示无数据的消息
                        self.syncResults.insert(
                            SyncAction(
                                title: "没有睡眠数据",
                                description: "在最近14天内未找到任何睡眠数据",
                                iconName: "info.circle",
                                color: .orange
                            ),
                            at: 0
                        )
                    }
                }
                return
            }
            
            // 获取指定天数的睡眠数据
            deviceService.getSleepData(day: day) { sleepData, error in
                processedDays += 1
                
                if let sleepData = sleepData, error == nil {
                    // 确保有有效的睡眠数据
                    let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
                    if totalMinutes > 0 {
                        successDays += 1
                        
                        // 生成当天的日期字符串
                        let calendar = Calendar.current
                        let today = Date()
                        let date = calendar.date(byAdding: .day, value: -day, to: today) ?? today
                        let dateFormatter = DateFormatter()
                        dateFormatter.dateFormat = "yyyy-MM-dd"
                        let dateString = dateFormatter.string(from: date)
                        
                        // 使用传入的用户ID保存数据
                        self.saveSleepDataToLocalDB(sleepData: sleepData, userId: userId)
                        
                        // 计算开始和结束时间戳（毫秒）
                        let startTime = sleepData.startTime
                        let endTime = sleepData.endTime
                        
                        // 计算睡眠时间（小时）和效率
                        let sleepTimeHours = Float(totalMinutes) / 60.0
                        let efficiency = Float(sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes) / Float(totalMinutes)
                        
                        // 计算睡眠评分
                        let score = Float(sleepData.sleepQuality > 0 ? sleepData.sleepQuality : 
                                        Int(Float(sleepData.deepSleepMinutes) / Float(totalMinutes) * 60 + 30))
                        
                        // 生成睡眠阶段记录
                        var records: [[String: Any]] = []
                        
                        // 深睡阶段
                        if sleepData.deepSleepMinutes > 0 {
                            let deepStartTime = Int(startTime.timeIntervalSince1970 * 1000)
                            let deepDurationMs = Int64(sleepData.deepSleepMinutes * 60 * 1000)
                            let deepEndTime = deepStartTime + Int(deepDurationMs)
                            
                            records.append([
                                "type": 2,
                                "total": sleepData.deepSleepMinutes,
                                "startTime": deepStartTime,
                                "endTime": deepEndTime
                            ])
                        }
                        
                        // 浅睡阶段
                        if sleepData.lightSleepMinutes > 0 {
                            let lightStartTime = records.last?["endTime"] as? Int ?? Int(startTime.timeIntervalSince1970 * 1000)
                            let lightDurationMs = Int64(sleepData.lightSleepMinutes * 60 * 1000)
                            let lightEndTime = lightStartTime + Int(lightDurationMs)
                            
                            records.append([
                                "type": 1,
                                "total": sleepData.lightSleepMinutes,
                                "startTime": lightStartTime,
                                "endTime": lightEndTime
                            ])
                        }
                        
                        // REM阶段
                        if sleepData.remSleepMinutes > 0 {
                            let remStartTime = records.last?["endTime"] as? Int ?? Int(startTime.timeIntervalSince1970 * 1000)
                            let remDurationMs = Int64(sleepData.remSleepMinutes * 60 * 1000)
                            let remEndTime = remStartTime + Int(remDurationMs)
                            
                            records.append([
                                "type": 3,
                                "total": sleepData.remSleepMinutes,
                                "startTime": remStartTime,
                                "endTime": remEndTime
                            ])
                        }
                        
                        // 创建睡眠数据JSON
                        let sleepDataJson: [String: Any] = [
                            "date": dateString,
                            "deep": sleepData.deepSleepMinutes,
                            "light": sleepData.lightSleepMinutes,
                            "rem": sleepData.remSleepMinutes,
                            "type": 0,
                            "startDate": Int(startTime.timeIntervalSince1970 * 1000), // 确保是毫秒时间戳
                            "endDate": Int(endTime.timeIntervalSince1970 * 1000), // 确保是毫秒时间戳
                            "sleepTime": sleepTimeHours,
                            "sleepEfficiency": efficiency,
                            "score": score,
                            "records": records
                        ]
                        
                        // 添加到数组
                        allSleepDataJson.append(sleepDataJson)
                        print("成功处理第\(day)天的睡眠数据: \(dateString)")
                    }
                }
                
                // 延迟获取下一天的数据，避免设备过载
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    fetchDayData(day: day + 1)
                }
            }
        }
        
        // 开始获取第0天的数据
        fetchDayData(day: 0)
    }
    
    // 使用GoMore算法获取最近14天的睡眠数据
    private func fetch14DaysGoMoreSleepData(userId: String) {
        print("使用GoMore算法获取最近14天的睡眠数据...")
        
        // 设置状态为同步中
        self.isSyncing = true
        
        // 使用内置的SDK方法获取GoMore睡眠数据列表
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataList()
        
        // 设置接收数据的回调，注册观察者
        NotificationCenter.default.addObserver(forName: .receivedGoMoreSleepIdsNotification, object: nil, queue: OperationQueue.main) { notification in
            
            // 取消之前的观察者
            NotificationCenter.default.removeObserver(self, name: .receivedGoMoreSleepIdsNotification, object: nil)
            
            // 检查是否有睡眠数据ID列表
            if !SDKDelegate.shared.gomoreSleepIds.isEmpty {
                print("获取到\(SDKDelegate.shared.gomoreSleepIds.count)个GoMore睡眠数据ID")
                
                // 处理最多14天数据
                let idsToProcess = Array(SDKDelegate.shared.gomoreSleepIds.prefix(14))
                var processedIds = 0
                var allSleepDataJson: [[String: Any]] = []
                
                // 递归函数处理每个ID
                func processNextId(index: Int) {
                    guard index < idsToProcess.count else {
                        // 所有ID处理完毕
                        DispatchQueue.main.async {
                            self.isSyncing = false
                            
                            if allSleepDataJson.isEmpty {
                                self.syncResults.insert(
                                    SyncAction(
                                        title: "GoMore睡眠数据获取",
                                        description: "未找到有效的睡眠数据",
                                        iconName: "info.circle",
                                        color: .orange
                                    ),
                                    at: 0
                                )
                } else {
                                self.syncResults.insert(
                                    SyncAction(
                                        title: "GoMore睡眠数据获取成功",
                                        description: "成功获取\(allSleepDataJson.count)天睡眠数据",
                                        iconName: "checkmark.circle.fill",
                                        color: .green
                                    ),
                                    at: 0
                                )
                                
                                // 显示JSON数据
                                let jsonString = self.formatAllSleepDataJson(allSleepDataJson)
                                self.jsonTitle = "最近\(allSleepDataJson.count)天GoMore睡眠数据"
                                self.jsonDetails = jsonString
                                self.showJsonDetails = true
                                
                                print("\n============= GoMore睡眠数据输出 =============")
                                print("总共获取到\(allSleepDataJson.count)天的睡眠数据")
                                print(jsonString)
                                print("=============================================\n")
                            }
                        }
                        return
                    }
                    
                    let sleepId = idsToProcess[index]
                    
                    // 获取详细睡眠数据和分段数据
                    CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataDetail(id: sleepId) { (sleepModel, slpError) in
                        if slpError == .none {
                            CRPSmartRingSDK.sharedInstance.getGoMoreSleepSegmentationData(id: sleepId) { (segmentModel, segError) in
                                processedIds += 1
                                
                                if segError == .none {
                                    // 转换时间戳为日期
                                    let startTimestamp = Double(sleepModel.startTime) / 1000.0
                                    let endTimestamp = Double(sleepModel.endTime) / 1000.0
                                    let startDate = Date(timeIntervalSince1970: startTimestamp)
                                    
                                    // 计算实际的睡眠日期
                                    let calendar = Calendar.current
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
                                    
                                    // 检查时间戳有效性并生成日期字符串
                                    // 如果时间戳是非常小的值（在1970年附近），则使用当前日期
                                    let dateString: String
                                    let referenceDate = Date(timeIntervalSince1970: 946684800) // 2000-01-01
                                    if startDate < referenceDate {
                                        // 时间戳可能不正确，使用当前日期减去索引天数
                                        if let adjustedDate = calendar.date(byAdding: .day, value: -index, to: Date()) {
                                            dateString = dateFormatter.string(from: adjustedDate)
                                            print("检测到无效的时间戳(\(startTimestamp))，使用当前日期调整: \(dateString)")
                                        } else {
                                            dateString = dateFormatter.string(from: Date())
                                        }
                                    } else {
                                        // 时间戳有效，直接使用
                                        dateString = dateFormatter.string(from: startDate)
                                    }
                                    
                                    // 生成睡眠阶段记录
                                    var records: [[String: Any]] = []
                                    
                                    // 深睡阶段
                                    if segmentModel.deep > 0 {
                                        let deepStartTime = Int(sleepModel.startTime) * 1000
                                        let deepDurationMs = Int64(segmentModel.deep * 60 * 1000)
                                        let deepEndTime = deepStartTime + Int(deepDurationMs)
                                        
                                        records.append([
                                            "type": 2,
                                            "total": Int(segmentModel.deep),
                                            "startTime": deepStartTime,
                                            "endTime": deepEndTime
                                        ])
                                    }
                                    
                                    // 浅睡阶段
                                    if segmentModel.light > 0 {
                                        let lightStartTime = records.last?["endTime"] as? Int ?? (Int(sleepModel.startTime) * 1000)
                                        let lightDurationMs = Int64(segmentModel.light * 60 * 1000)
                                        let lightEndTime = lightStartTime + Int(lightDurationMs)
                                        
                                        records.append([
                                            "type": 1,
                                            "total": Int(segmentModel.light),
                                            "startTime": lightStartTime,
                                            "endTime": lightEndTime
                                        ])
                                    }
                                    
                                    // REM阶段
                                    if segmentModel.rem > 0 {
                                        let remStartTime = records.last?["endTime"] as? Int ?? (Int(sleepModel.startTime) * 1000)
                                        let remDurationMs = Int64(segmentModel.rem * 60 * 1000)
                                        let remEndTime = remStartTime + Int(remDurationMs)
                                        
                                        records.append([
                                            "type": 3,
                                            "total": Int(segmentModel.rem),
                                            "startTime": remStartTime,
                                            "endTime": remEndTime
                                        ])
                                    }
                                    
                                    // 创建睡眠数据JSON
                                    let sleepDataJson: [String: Any] = [
                                        "date": dateString,
                                        "deep": Int(segmentModel.deep),
                                        "light": Int(segmentModel.light),
                                        "rem": Int(segmentModel.rem),
                                        "type": 0,
                                        "startDate": Int(sleepModel.startTime) * 1000, // 毫秒时间戳
                                        "endDate": Int(sleepModel.endTime) * 1000, // 毫秒时间戳
                                        "sleepTime": Float(segmentModel.deep + segmentModel.light + segmentModel.rem) / 60.0,
                                        "sleepEfficiency": Float(sleepModel.sleepEfficiency) / 100.0,
                                        "score": Float(sleepModel.sleepScore),
                                        "records": records
                                    ]
                                    
                                    // 添加到数组
                                    allSleepDataJson.append(sleepDataJson)
                                    
                                    // 保存睡眠数据到本地数据库
                                    self.saveCombinedSleepData(
                                        sleepDetail: sleepModel,
                                        sleepSegment: segmentModel,
                                        userId: userId
                                    )
                                }
                                
                                // 延迟处理下一个ID，避免设备过载
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    processNextId(index: index + 1)
                                }
                            }
                        } else {
                            processedIds += 1
                            
                            // 延迟处理下一个ID，避免设备过载
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                processNextId(index: index + 1)
                            }
                        }
                    }
                }
                
                // 开始处理第一个ID
                processNextId(index: 0)
                
            } else {
                print("没有找到任何GoMore睡眠数据ID")
                DispatchQueue.main.async {
                    self.isSyncing = false
                    self.syncResults.insert(
                        SyncAction(
                            title: "GoMore睡眠数据获取",
                            description: "未发现任何GoMore睡眠数据",
                            iconName: "info.circle",
                            color: .blue
                        ),
                        at: 0
                    )
                }
            }
        }
    }
    
    // 格式化所有睡眠数据为JSON字符串
    private func formatAllSleepDataJson(_ dataArray: [[String: Any]]) -> String {
        do {
            // 序列化整个数据数组
            let jsonData = try JSONSerialization.data(withJSONObject: dataArray, options: .prettyPrinted)
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                return jsonString
            }
            
            // 如果处理失败，返回空数组
            return "[]"
        } catch {
            print("JSON序列化错误: \(error.localizedDescription)")
            return "Error: \(error.localizedDescription)"
        }
    }
    
    // 这是一个解决编译错误的占位函数，实际上不会被调用
    private func fetchRecentGoMoreSleepData() {
        // 创建一个临时结构体来模拟sleepItem
        struct TempSleepItem {
            struct Detail {
                let startTime: Int = 0
            }
            
            struct Segments {
                let deep: Int = 0
                let light: Int = 0
            }
            
            let detail = Detail()
            let segments = Segments()
        }
        
        // 创建一个临时的实例
        let sleepItem = TempSleepItem()
        
        // 以下代码不会实际执行，只是为了解决编译错误
        if sleepItem.segments.deep > 0 {
            let deepStartTime = sleepItem.detail.startTime
            let deepEndTime = deepStartTime + Int(Int64(sleepItem.segments.deep) * 60 * 1000)
            print("深睡结束时间: \(deepEndTime)")
        }
        
        if sleepItem.segments.light > 0 {
            let lightStartTime = sleepItem.detail.startTime + 
                Int(Int64(sleepItem.segments.deep) * 60 * 1000)
            let lightEndTime = lightStartTime + Int(Int64(sleepItem.segments.light) * 60 * 1000)
            print("浅睡结束时间: \(lightEndTime)")
        }
    }
    
    // 添加睡眠数据上传方法
    private func uploadSleepData() {
        guard isLoggedIn else {
            syncResults.insert(
                SyncAction(
                    title: "睡眠数据上传失败",
                    description: "用户未登录",
                    iconName: "xmark.circle.fill",
                    color: .red
                ),
                at: 0
            )
                return
            }
            
        isSyncing = true
        
        // 打印上传JSON格式和响应
        print("\n======================================================")
        print("开始上传睡眠数据，将在上传前后打印完整的JSON格式数据")
        print("======================================================\n")
        
        // 获取要上传的睡眠数据
        guard let userId = authService.currentUser?.id else {
            DispatchQueue.main.async {
                self.isSyncing = false
                self.syncResults.insert(
                    SyncAction(
                        title: "睡眠数据上传失败",
                        description: "用户ID不可用",
                        iconName: "xmark.circle.fill",
                        color: .red
                    ),
                    at: 0
                )
            }
            return
        }
        
        // 获取过去90天的睡眠数据
        let calendar = Calendar.current
        let endDate = Date()
        guard let startDate = calendar.date(byAdding: .day, value: -90, to: endDate) else {
            DispatchQueue.main.async {
                self.isSyncing = false
                self.syncResults.insert(
                    SyncAction(
                        title: "睡眠数据上传失败",
                        description: "日期计算错误",
                        iconName: "xmark.circle.fill",
                        color: .red
                    ),
                    at: 0
                )
            }
            return
        }
        
        // 获取所有未上传的睡眠数据
        let sleepData = healthDataManager.getSleep(userId: userId, startDate: startDate, endDate: endDate)
        
        if sleepData.isEmpty {
            print("没有待上传的睡眠数据")
        DispatchQueue.main.async {
                self.isSyncing = false
                self.syncResults.insert(
                    SyncAction(
                        title: "睡眠数据上传",
                        description: "没有待上传的睡眠数据",
                        iconName: "info.circle",
                        color: .blue
                    ),
                    at: 0
                )
            }
            return
        }
        
        print("找到\(sleepData.count)条待上传的睡眠数据")
        
        // 按日期分组睡眠数据
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
        dateFormatter.timeZone = TimeZone.current
        
        // 按日期分组睡眠数据
        let groupedData = Dictionary(grouping: sleepData) { sleep -> String in
            return dateFormatter.string(from: sleep.startTime!)
        }
        
        print("\n================ 睡眠数据待上传列表（共\(groupedData.count)条） ================\n")
        
        var allUploadData: [[String: Any]] = []
        
        for (dateString, records) in groupedData {
            // 如果该日期有多条睡眠记录，选择最长的一条
            guard let record = records.max(by: { $0.totalMinutes < $1.totalMinutes }) else { continue }
            
            // 将时间转换为毫秒级时间戳
            let startTimeStamp = Int(record.startTime!.timeIntervalSince1970 * 1000)
            let endTimeStamp = Int(record.endTime!.timeIntervalSince1970 * 1000)
            let efficiency = Float(record.efficiency) / 100.0
            let score = Float(record.score)
            
            // 获取睡眠阶段数据
            let sleepStages = healthDataManager.getSleepStages(sleepId: record.id ?? "")
            var sleepRecords: [[String: Any]] = []
            
            if !sleepStages.isEmpty {
                // 将睡眠阶段数据转换为接口需要的格式
                for stage in sleepStages {
                    guard let stageStartTime = stage.startTime else { continue }
                    
                    // 计算阶段结束时间（开始时间 + 持续时间）
                    let stageEndTime = stageStartTime.addingTimeInterval(Double(stage.duration) * 60)
                    
                    // 确定阶段类型: 0:清醒 1:浅睡 2:深睡 3:快速眼动
                    let stageType: Int
                    if let typeString = stage.type {
                        switch typeString {
                        case "awake": stageType = 0 // 清醒
                        case "light": stageType = 1 // 浅睡
                        case "deep": stageType = 2 // 深睡
                        case "rem": stageType = 3 // REM (快速眼动)
                        default: stageType = 1 // 默认为浅睡
                        }
                    } else {
                        stageType = 1 // 默认为浅睡
                    }
                    
                    let stageRecord: [String: Any] = [
                        "type": stageType,
                        "total": Int(stage.duration),
                        "startTime": Int(stageStartTime.timeIntervalSince1970 * 1000),
                        "endTime": Int(stageEndTime.timeIntervalSince1970 * 1000)
                    ]
                    
                    sleepRecords.append(stageRecord)
                }
            } else {
                // 如果没有详细的睡眠阶段数据，创建简化版本
                let sleepStartTime = record.startTime!
                
                // 深睡阶段
                if record.deepMinutes > 0 {
                    let deepStartTime = sleepStartTime
                    let deepEndTime = sleepStartTime.addingTimeInterval(Double(record.deepMinutes) * 60)
                    
                    let deepRecord: [String: Any] = [
                        "type": 2, // 深睡
                        "total": Int(record.deepMinutes),
                        "startTime": Int(deepStartTime.timeIntervalSince1970 * 1000),
                        "endTime": Int(deepEndTime.timeIntervalSince1970 * 1000)
                    ]
                    sleepRecords.append(deepRecord)
                }
                
                // 浅睡阶段
                if record.lightMinutes > 0 {
                    let lightStartTime = sleepStartTime.addingTimeInterval(Double(record.deepMinutes) * 60)
                    let lightEndTime = lightStartTime.addingTimeInterval(Double(record.lightMinutes) * 60)
                    
                    let lightRecord: [String: Any] = [
                        "type": 1, // 浅睡
                        "total": Int(record.lightMinutes),
                        "startTime": Int(lightStartTime.timeIntervalSince1970 * 1000),
                        "endTime": Int(lightEndTime.timeIntervalSince1970 * 1000)
                    ]
                    sleepRecords.append(lightRecord)
                }
                
                // REM阶段
                if record.remMinutes > 0 {
                    let remStartTime = sleepStartTime.addingTimeInterval(Double(record.deepMinutes + record.lightMinutes) * 60)
                    let remEndTime = record.endTime!
                    
                    let remRecord: [String: Any] = [
                        "type": 3, // REM
                        "total": Int(record.remMinutes),
                        "startTime": Int(remStartTime.timeIntervalSince1970 * 1000),
                        "endTime": Int(remEndTime.timeIntervalSince1970 * 1000)
                    ]
                    sleepRecords.append(remRecord)
                }
            }
            
            // 创建上传数据模型
            let uploadData: [String: Any] = [
                "date": dateString,
                "deep": Int(record.deepMinutes),
                "light": Int(record.lightMinutes),
                "rem": Int(record.remMinutes),
                "type": 0,
                "startDate": startTimeStamp,
                "endDate": endTimeStamp,
                "sleepTime": Float(record.totalMinutes) / 60.0,
                "sleepEfficiency": efficiency,
                "score": score,
                "records": sleepRecords
            ]
            
            allUploadData.append(uploadData)
            
            // 打印JSON
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: uploadData, options: .prettyPrinted)
                if let jsonString = String(data: jsonData, encoding: .utf8) {
                    print("【睡眠数据 \(dateString)】")
                    print(jsonString)
                    print("--------------------------------------------------------")
                }
            } catch {
                print("JSON序列化错误: \(error.localizedDescription)")
            }
        }
        
        // 打印完整的上传数据集合
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: allUploadData, options: .prettyPrinted)
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("\n================ 完整的睡眠数据集合（即将上传） ================")
                print(jsonString)
                print("============================================================\n")
            }
        } catch {
            print("JSON序列化错误: \(error.localizedDescription)")
        }
        
        // 注册监听睡眠数据上传响应
        var uploadResponseObserver: Any? = nil
        uploadResponseObserver = NotificationCenter.default.addObserver(
            forName: .sleepDataUploadResponse,
            object: nil,
            queue: .main
        ) { notification in
            // 删除观察者
            if let observer = uploadResponseObserver {
                NotificationCenter.default.removeObserver(observer)
                uploadResponseObserver = nil
            }
            
            if let responseData = notification.userInfo?["response"] as? Data,
               let responseString = String(data: responseData, encoding: .utf8) {
                print("\n================ 睡眠数据上传响应 ================")
                print(responseString)
                print("================================================\n")
                
                // 显示JSON详情
                self.jsonTitle = "睡眠数据上传响应"
                self.jsonDetails = responseString
                self.showJsonDetails = true
            }
        }
        
        // 执行实际上传
        sleepUploadService.uploadPendingSleepData { count, error in
            DispatchQueue.main.async {
                self.isSyncing = false
                
                if let error = error {
                    self.syncResults.insert(
                        SyncAction(
                            title: "睡眠数据上传失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                    
                    print("\n================ 睡眠数据上传失败 ================")
                    print("错误信息: \(error.localizedDescription)")
                    print("===================================================\n")
                    
                } else {
                    // 显示JSON信息
                    self.jsonTitle = "睡眠数据上传"
                    self.jsonDetails = "请在Xcode控制台中查看睡眠数据上传的JSON格式和响应数据。\n\n上传成功：\(count)条记录"
                    self.showJsonDetails = true
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "睡眠数据上传成功",
                            description: "上传了\(count)天睡眠数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastSleepUploadTime")
                    self.updateDataCounts()
                    
                    print("\n================ 睡眠数据上传成功 ================")
                    print("成功上传 \(count) 条睡眠数据记录")
                    print("===================================================\n")
                }
            }
        }
    }

    // 强制重新上传所有睡眠数据方法
    private func forceUploadAllSleepData() {
        guard isLoggedIn else {
            syncResults.insert(
                SyncAction(
                    title: "睡眠数据上传失败",
                    description: "用户未登录",
                    iconName: "xmark.circle.fill",
                    color: .red
                ),
                at: 0
            )
            return
        }
            
        isSyncing = true
        
        // 打印上传JSON格式和响应
        print("\n======================================================")
        print("开始强制上传所有睡眠数据，将无视已上传标记")
        print("======================================================\n")
        
        // 首先执行清除历史记录
//        sleepUploadService.clearSleepUploadHistory()
        
        print("\n已清除所有睡眠数据上传历史记录，确保所有数据均可重新上传\n")
        
        // 使用强制上传方法
        guard let userId = authService.currentUser?.id else {
            DispatchQueue.main.async {
                self.isSyncing = false
                self.syncResults.insert(
                    SyncAction(
                        title: "睡眠数据上传失败",
                        description: "用户ID不可用",
                        iconName: "xmark.circle.fill",
                        color: .red
                    ),
                    at: 0
                )
            }
                return
            }
            
        // 使用更长的历史范围，确保能够获取到所有历史数据
        sleepUploadService.forceUploadAllSleepData { count, error in
                DispatchQueue.main.async {
                self.isSyncing = false
                
                if let error = error {
                    self.syncResults.insert(
                        SyncAction(
                            title: "强制上传睡眠数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                    
                    print("\n================ 强制上传睡眠数据失败 ================")
                    print("错误信息: \(error.localizedDescription)")
                    print("===================================================\n")
                    
                    } else {
                    // 显示JSON信息
                    self.jsonTitle = "强制上传睡眠数据"
                    self.jsonDetails = "已强制重新上传所有睡眠数据。\n\n上传成功：\(count)条记录"
                    self.showJsonDetails = true
                    
                    self.syncResults.insert(
                        SyncAction(
                            title: "强制上传睡眠数据成功",
                            description: "上传了\(count)天睡眠数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastSleepUploadTime")
                    self.updateDataCounts()
                    
                    print("\n================ 强制上传睡眠数据成功 ================")
                    print("成功上传 \(count) 条睡眠数据记录")
                    print("===================================================\n")
                }
            }
        }
    }
    
    private func syncAllSleepData() {
        guard deviceService.connectionState.isConnected else {
            syncResults.insert(
                SyncAction(
                    title: "睡眠数据同步失败",
                    description: "设备未连接",
                    iconName: "xmark.circle.fill",
                    color: .red
                ),
                at: 0
            )
            return
        }
        
        guard isLoggedIn else {
            syncResults.insert(
                SyncAction(
                    title: "睡眠数据同步失败",
                    description: "用户未登录",
                    iconName: "xmark.circle.fill",
                    color: .red
                ),
                at: 0
            )
            return
        }
        
        isSyncing = true
        syncResults.insert(
            SyncAction(
                title: "睡眠数据同步",
                description: "开始同步...",
                iconName: "arrow.triangle.2.circlepath",
                color: .blue
            ),
            at: 0
        )
        
        // 检查是否支持GoMore算法
        goMoreService.checkGoMoreSupport { isSupported, error in
            if isSupported {
                print("设备支持GoMore算法，使用GoMore算法同步睡眠数据")
                self.syncAllGoMoreSleepData()
                } else {
                print("设备不支持GoMore算法，使用基础睡眠算法")
                self.syncAllBasicSleepData()
            }
        }
    }

    // 使用基础睡眠算法同步所有数据
    private func syncAllBasicSleepData() {
        // 先获取睡眠数据
        sleepUploadService.fetchAndSaveSleepHistory { fetchCount, fetchError in
            if let fetchError = fetchError {
            DispatchQueue.main.async {
                    self.isSyncing = false
                    self.syncResults.insert(
                        SyncAction(
                            title: "基础睡眠数据获取失败",
                            description: fetchError.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                }
                return
            }
            
            if fetchCount == 0 {
                DispatchQueue.main.async {
                    self.isSyncing = false
                    self.syncResults.insert(
                        SyncAction(
                            title: "基础睡眠数据获取完成",
                            description: "没有新的睡眠数据",
                            iconName: "exclamationmark.circle",
                            color: .orange
                        ),
                        at: 0
                    )
                }
                return
            }
            
            // 获取成功后，上传数据
            DispatchQueue.main.async {
                self.syncResults.insert(
                    SyncAction(
                        title: "基础睡眠数据获取成功",
                        description: "获取到\(fetchCount)天睡眠数据",
                        iconName: "checkmark.circle.fill",
                        color: .green
                    ),
                    at: 0
                )
                
                // 上传数据
                self.sleepUploadService.uploadPendingSleepData { uploadCount, uploadError in
            DispatchQueue.main.async {
                        self.isSyncing = false
                        
                        if let uploadError = uploadError {
                            self.syncResults.insert(
                                SyncAction(
                                    title: "睡眠数据上传失败",
                                    description: uploadError.localizedDescription,
                                    iconName: "xmark.circle.fill",
                                    color: .red
                                ),
                                at: 0
                            )
                        } else {
                            // 显示JSON信息
                            self.jsonTitle = "睡眠数据同步上传"
                            self.jsonDetails = "请在Xcode控制台中查看睡眠数据上传的JSON格式和响应数据。\n\n获取：\(fetchCount)条记录\n上传成功：\(uploadCount)条记录"
                            self.showJsonDetails = true
                            
                            self.syncResults.insert(
                                SyncAction(
                                    title: "睡眠数据上传成功",
                                    description: "上传了\(uploadCount)天睡眠数据",
                                    iconName: "checkmark.circle.fill",
                                    color: .green
                                ),
                                at: 0
                            )
                            self.lastSyncTime = Date()
                            UserDefaults.standard.set(self.lastSyncTime, forKey: "lastSleepUploadTime")
                        }
                        
                        self.updateDataCounts()
                    }
                }
            }
        }
    }

    // 使用GoMore算法同步所有睡眠数据
    private func syncAllGoMoreSleepData() {
        // 获取GoMore睡眠数据列表 - 修复API调用
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataList()
        
        // 设置接收数据的回调，注册观察者
        NotificationCenter.default.addObserver(forName: .receivedGoMoreSleepIdsNotification, object: nil, queue: OperationQueue.main) { notification in
            
            // 检查是否有睡眠数据ID列表
            if !SDKDelegate.shared.gomoreSleepIds.isEmpty {
                print("获取到\(SDKDelegate.shared.gomoreSleepIds.count)个GoMore睡眠数据ID")
                print("睡眠ID列表：\(SDKDelegate.shared.gomoreSleepIds)")
                
                self.syncResults.insert(
                    SyncAction(
                        title: "GoMore睡眠数据获取中",
                        description: "发现\(SDKDelegate.shared.gomoreSleepIds.count)个睡眠记录",
                        iconName: "arrow.triangle.2.circlepath",
                        color: .blue
                    ),
                    at: 0
                )
                
                // 处理每个睡眠ID，获取详细数据
                var processedCount = 0
                var totalSavedCount = 0
                let serialQueue = DispatchQueue(label: "com.windring.sleepdata.serial")
                
                // 创建递归函数顺序处理ID
                func processNextId(index: Int) {
                    if index >= SDKDelegate.shared.gomoreSleepIds.count {
                        // 所有ID处理完成，准备上传数据
                        if totalSavedCount > 0 {
                            self.syncResults.insert(
                                SyncAction(
                                    title: "GoMore睡眠数据获取成功",
                                    description: "处理了\(processedCount)个睡眠记录，成功保存\(totalSavedCount)条数据",
                                    iconName: "checkmark.circle.fill",
                                    color: .green
                                ),
                                at: 0
                            )
                            
                            // 打印待上传的JSON数据
                            print("\n==== 准备上传GoMore睡眠数据，以JSON格式显示 ====\n")
                            
                            // 获取要上传的睡眠数据
                            guard let userId = self.authService.currentUser?.id else {
                                self.isSyncing = false
                return
            }
            
                            // 获取过去90天的睡眠数据
                            let calendar = Calendar.current
                            let endDate = Date()
                            guard let startDate = calendar.date(byAdding: .day, value: -90, to: endDate) else {
                                self.isSyncing = false
                                return
                            }
                            
                            // 获取所有未上传的睡眠数据
                            let sleepData = self.healthDataManager.getSleep(userId: userId, startDate: startDate, endDate: endDate)
                            
                            if sleepData.isEmpty {
                                print("没有待上传的GoMore睡眠数据")
                    } else {
                                print("找到\(sleepData.count)条待上传的GoMore睡眠数据")
                                
                                // 打印每条睡眠数据的JSON格式
                                let dateFormatter = DateFormatter()
                                dateFormatter.dateFormat = "yyyy-MM-dd"
                                dateFormatter.timeZone = TimeZone.current
                                
                                let fullDateFormatter = DateFormatter()
                                fullDateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
                                fullDateFormatter.timeZone = TimeZone(secondsFromGMT: 0)
                                
                                // 按日期分组睡眠数据
                                let groupedData = Dictionary(grouping: sleepData) { sleep -> String in
                                    return dateFormatter.string(from: sleep.startTime!)
                                }
                                
                                print("\n=== GoMore睡眠数据JSON格式预览（共\(groupedData.count)条） ===\n")
                                
                                for (dateString, records) in groupedData {
                                    // 如果该日期有多条睡眠记录，选择最长的一条
                                    guard let record = records.max(by: { $0.totalMinutes < $1.totalMinutes }) else { continue }
                                    
                                    // 将时间转换为毫秒级时间戳
                                    let startTimeStamp = Int(record.startTime!.timeIntervalSince1970 * 1000)
                                    let endTimeStamp = Int(record.endTime!.timeIntervalSince1970 * 1000)
                                    let efficiency = Float(record.efficiency) / 100.0
                                    let score = Float(record.score)
                                    
                                    // 创建上传数据模型
                                    let uploadData: [String: Any] = [
                                        "date": dateString,
                                        "deep": Int(record.deepMinutes),
                                        "light": Int(record.lightMinutes),
                                        "rem": Int(record.remMinutes),
                                        "type": 0,
                                        "startDate": startTimeStamp,
                                        "endDate": endTimeStamp,
                                        "sleepTime": Float(record.totalMinutes) / 60.0,
                                        "sleepEfficiency": efficiency,
                                        "score": score
                                    ]
                                    
                                    // 打印JSON
                                    do {
                                        let jsonData = try JSONSerialization.data(withJSONObject: uploadData, options: .prettyPrinted)
                                        if let jsonString = String(data: jsonData, encoding: .utf8) {
                                            print("【GoMore睡眠数据 \(dateString)】")
                                            print(jsonString)
                                            print("---------------------------------")
                                        }
                                    } catch {
                                        print("JSON序列化错误: \(error.localizedDescription)")
                                    }
                                }
                                
                                print("\n=== GoMore睡眠数据JSON预览结束 ===\n")
                            }
                            
                            // 上传睡眠数据 - 使用强制上传方法
                            self.sleepUploadService.forceUploadAllSleepData { uploadCount, uploadError in
        DispatchQueue.main.async {
                                    self.isSyncing = false
                                    
                                    if let uploadError = uploadError {
                                        self.syncResults.insert(
                                            SyncAction(
                                                title: "GoMore睡眠数据上传失败",
                                                description: uploadError.localizedDescription,
                                                iconName: "xmark.circle.fill",
                                                color: .red
                                            ),
                                            at: 0
                                        )
                                    } else {
                                        // 显示JSON信息
                                        self.jsonTitle = "GoMore睡眠数据同步上传"
                                        self.jsonDetails = "请在Xcode控制台中查看睡眠数据上传的JSON格式和响应数据。\n\n获取并保存：\(totalSavedCount)条记录\n上传成功：\(uploadCount)条记录"
                                        self.showJsonDetails = true
                                        
                                        self.syncResults.insert(
                                            SyncAction(
                                                title: "GoMore睡眠数据上传成功",
                                                description: "上传了\(uploadCount)条睡眠数据",
                                                iconName: "checkmark.circle.fill",
                                                color: .green
                                            ),
                                            at: 0
                                        )
                                        self.lastSyncTime = Date()
                                        UserDefaults.standard.set(self.lastSyncTime, forKey: "lastSleepUploadTime")
                                    }
                                    
                                    self.updateDataCounts()
                                    
                                    // 移除观察者
                                    NotificationCenter.default.removeObserver(self, name: .receivedGoMoreSleepIdsNotification, object: nil)
                                }
                            }
                        } else {
                            self.isSyncing = false
                            self.syncResults.insert(
                                SyncAction(
                                    title: "GoMore睡眠数据处理完成",
                                    description: "未能保存任何睡眠数据",
                                    iconName: "exclamationmark.triangle",
                                    color: .orange
                                ),
                                at: 0
                            )
                            
                            // 移除观察者
                            NotificationCenter.default.removeObserver(self, name: .receivedGoMoreSleepIdsNotification, object: nil)
                        }
                        return
                    }
                    
                    // 获取当前处理的ID
                    let currentId = SDKDelegate.shared.gomoreSleepIds[index]
                    print("\n============================================")
                    print("开始处理睡眠ID: \(currentId) (第 \(index+1)/\(SDKDelegate.shared.gomoreSleepIds.count) 个)")
                    print("============================================")
                    
                    // 添加延迟避免SDK冲突
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                        // 获取睡眠详情
                        CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataDetail(id: currentId) { (sleepDetail, slpError) in
                            print("睡眠详情获取结果 - ID \(currentId): 错误码 \(slpError.rawValue)")
                            
                            if slpError == .none {
                                // 如果获取详情成功，再获取分段数据
                                CRPSmartRingSDK.sharedInstance.getGoMoreSleepSegmentationData(id: currentId) { (segmentData, segError) in
                                    print("睡眠分段获取结果 - ID \(currentId): 错误码 \(segError.rawValue)")
                                    
                                    // 在串行队列中处理数据
                                    serialQueue.async {
                                        processedCount += 1
                                        
                                        if slpError == .none && segError == .none {
                                            print("\n============= 成功获取ID为 \(currentId) 的睡眠数据 =============")
                                            print("开始时间戳: \(sleepDetail.startTime), 结束时间戳: \(sleepDetail.endTime)")
                                            print("深睡: \(segmentData.deep)分钟, 浅睡: \(segmentData.light)分钟, REM: \(segmentData.rem)分钟")
                                            
                                            // 保存获取到的睡眠数据
                                            let startDate = Date(timeIntervalSince1970: Double(sleepDetail.startTime) / 1000.0)
                                            let endDate = Date(timeIntervalSince1970: Double(sleepDetail.endTime) / 1000.0)
                                            print("睡眠时间: 开始=\(startDate), 结束=\(endDate)")
                                            
                                            // 保存睡眠数据
                                            let saved = self.sleepUploadService.saveGoMoreSleepData(
                                                sleepDetail: sleepDetail,
                                                segmentData: segmentData,
                                                userId: self.authService.currentUser?.id ?? "",
                                                deviceId: self.deviceService.deviceInfo?.mac ?? ""
                                            )
                                            
                                            if saved {
                                                totalSavedCount += 1
                                                print("睡眠数据ID \(currentId) 保存成功, 已保存 \(totalSavedCount)/\(processedCount) 条数据")
                } else {
                                                print("睡眠数据ID \(currentId) 保存失败")
                                            }
                                        } else {
                                            print("获取睡眠数据失败 - ID \(currentId): 睡眠详情错误 \(slpError.rawValue), 分段数据错误 \(segError.rawValue)")
                                        }
                                        
                                        // 无论成功失败，都处理下一个ID
            DispatchQueue.main.async {
                                            processNextId(index: index + 1)
                                        }
                                    }
                                }
                } else {
                                // 睡眠详情获取失败，直接处理下一个ID
                                processedCount += 1
                                print("获取睡眠详情数据失败 - ID \(currentId): 错误码 \(slpError.rawValue)")
                                
                                // 处理下一个ID
            DispatchQueue.main.async {
                                    processNextId(index: index + 1)
                                }
                            }
                        }
                    }
                }
                
                // 开始处理第一个ID
                processNextId(index: 0)
            } else {
                // 没有获取到睡眠ID列表
                self.isSyncing = false
                self.syncResults.insert(
                    SyncAction(
                        title: "GoMore睡眠数据获取完成",
                        description: "未找到任何睡眠记录",
                        iconName: "exclamationmark.circle",
                        color: .orange
                    ),
                    at: 0
                )
                
                // 移除观察者
                NotificationCenter.default.removeObserver(self, name: .receivedGoMoreSleepIdsNotification, object: nil)
            }
        }
    }
    
    // 添加测试睡眠数据方法
    private func addTestSleepData() {
        guard isLoggedIn else {
            syncResults.insert(
                SyncAction(
                    title: "添加测试数据失败",
                    description: "用户未登录",
                    iconName: "xmark.circle.fill",
                    color: .red
                ),
                at: 0
            )
            return
        }
        
        guard let userId = authService.currentUser?.id else {
            syncResults.insert(
                SyncAction(
                    title: "添加测试数据失败",
                    description: "用户ID不可用",
                    iconName: "xmark.circle.fill",
                    color: .red
                ),
                at: 0
            )
            return
        }
        
        let deviceId = deviceService.deviceInfo?.mac ?? "unknown"
        
        // 调用服务添加测试数据
        let success = sleepUploadService.addTestSleepData(userId: userId, deviceId: deviceId)
        
                if success {
            syncResults.insert(
                SyncAction(
                    title: "添加测试睡眠数据成功",
                    description: "已添加模拟的睡眠数据，可尝试上传",
                    iconName: "checkmark.circle.fill",
                    color: .green
                ),
                at: 0
            )
                } else {
            syncResults.insert(
                SyncAction(
                    title: "添加测试数据失败",
                    description: "无法创建测试睡眠数据",
                    iconName: "xmark.circle.fill",
                    color: .red
                ),
                at: 0
            )
        }
    }
    
    // MARK: - 辅助方法
    /// 设置订阅
    private func setupSubscriptions() {
        // 先清除现有订阅，防止重复订阅
        cancellables.removeAll()
        
        // 监听心率数据上传通知
        NotificationCenter.default.publisher(for: .heartRateDataUploaded)
            .receive(on: RunLoop.main) // 确保在主线程上接收通知
            .sink { notification in
                let count = notification.userInfo?["count"] as? Int ?? 0
                let success = notification.userInfo?["success"] as? Bool ?? false
                
                if success {
                    print("收到心率数据上传成功通知，上传了 \(count) 条数据")
                    
                    // 更新UI状态
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastHRUploadTime")
                    self.updateDataCounts()
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "心率数据自动上传成功",
                            description: "成功上传\(count)条数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                } else {
                    print("收到心率数据上传失败通知")
                }
            }
            .store(in: &self.cancellables)
            
        // 监听HRV数据上传通知
        NotificationCenter.default.publisher(for: Notification.Name.hrvDataUploaded)
            .receive(on: RunLoop.main) // 确保在主线程上接收通知
            .sink { notification in
                let count = notification.userInfo?["count"] as? Int ?? 0
                let success = notification.userInfo?["success"] as? Bool ?? false
                
                if success {
                    print("收到HRV数据上传成功通知，上传了 \(count) 条数据")
                    
                    // 更新UI状态
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastHRVUploadTime")
                    self.updateDataCounts()
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "HRV数据自动上传成功",
                            description: "成功上传\(count)条数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                } else {
                    print("收到HRV数据上传失败通知")
                }
            }
            .store(in: &self.cancellables)
            
        // 监听压力数据上传通知
        NotificationCenter.default.publisher(for: .stressDataUploaded)
            .receive(on: RunLoop.main) // 确保在主线程上接收通知
            .sink { notification in
                let count = notification.userInfo?["count"] as? Int ?? 0
                let success = notification.userInfo?["success"] as? Bool ?? false
                
                if success {
                    print("收到压力数据上传成功通知，上传了 \(count) 条数据")
                    
                    // 更新UI状态
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastStressUploadTime")
                    self.updateDataCounts()
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "压力数据自动上传成功",
                            description: "成功上传\(count)条数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                } else {
                    print("收到压力数据上传失败通知")
                }
            }
            .store(in: &self.cancellables)
            
        // 监听血氧数据上传通知
        NotificationCenter.default.publisher(for: .bloodOxygenDataUploaded)
            .receive(on: RunLoop.main) // 确保在主线程上接收通知
            .sink { notification in
                let count = notification.userInfo?["count"] as? Int ?? 0
                let success = notification.userInfo?["success"] as? Bool ?? false
                
                if success {
                    print("收到血氧数据上传成功通知，上传了 \(count) 条数据")
                    
                    // 更新UI状态
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastBloodOxygenUploadTime")
                    self.updateDataCounts()
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "血氧数据自动上传成功",
                            description: "成功上传\(count)条数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                } else {
                    print("收到血氧数据上传失败通知")
                }
            }
            .store(in: &self.cancellables)
        
        // 监听睡眠数据上传通知
        NotificationCenter.default.publisher(for: .sleepDataUploaded)
            .receive(on: RunLoop.main) // 确保在主线程上接收通知
            .sink { notification in
                let count = notification.userInfo?["count"] as? Int ?? 0
                let success = notification.userInfo?["success"] as? Bool ?? false
                
                if success {
                    self.syncResults.insert(
                        SyncAction(
                            title: "睡眠数据上传成功",
                            description: "上传了\(count)天睡眠数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastSleepUploadTime")
                } else if let error = notification.userInfo?["error"] as? Error {
                    self.syncResults.insert(
                        SyncAction(
                            title: "睡眠数据上传失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                }
                
                self.updateDataCounts()
            }
            .store(in: &self.cancellables)
        
        // 监听温度数据上传通知
        NotificationCenter.default.publisher(for: .temperatureDataUploaded)
            .receive(on: RunLoop.main) // 确保在主线程上接收通知
            .sink { notification in
                let count = notification.userInfo?["count"] as? Int ?? 0
                let success = notification.userInfo?["success"] as? Bool ?? false
                
                if success {
                    print("收到温度数据上传成功通知，上传了 \(count) 条数据")
                    
                    // 更新UI状态
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastTemperatureUploadTime")
                    self.updateDataCounts()
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "温度数据自动上传成功",
                            description: "成功上传\(count)条数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                } else {
                    print("收到温度数据上传失败通知")
                }
            }
            .store(in: &self.cancellables)
    }
    
    /// 显示警告
    private func showAlert(title: String, message: String) {
        alertTitle = title
        alertMessage = message
        showAlert = true
    }
    
    /// 检查登录状态
    private func checkLoginStatus() {
        isLoggedIn = authService.currentUser != nil
    }
    
    /// 更新数据统计信息
    private func updateDataCounts() {
        guard let userId = authService.currentUser?.id else {
            totalDataCount = 0
            uploadedDataCount = 0
            return
        }
        
        // 设置查询范围为过去90天
        let calendar = Calendar.current
        let endDate = Date()
        guard let startDate = calendar.date(byAdding: .day, value: -90, to: endDate) else {
            return
        }
        
        // 查询各类型数据
        let heartRates = healthDataManager.getHeartRates(userId: userId, startDate: startDate, endDate: endDate)
        let hrvs = healthDataManager.getHRVs(userId: userId, startDate: startDate, endDate: endDate)
        let stresses = healthDataManager.getStress(userId: userId, startDate: startDate, endDate: endDate)
        let bloodOxygens = healthDataManager.getBloodOxygens(userId: userId, startDate: startDate, endDate: endDate)
        let sleeps = healthDataManager.getSleep(userId: userId, startDate: startDate, endDate: endDate)
        
        // 计算总数据量
        totalDataCount = heartRates.count + hrvs.count + stresses.count + bloodOxygens.count + sleeps.count
        
        // 计算已上传数据量
        // 注意：由于Stress和BloodOxygen模型可能没有isUploaded属性，我们假设它们全部需要上传
        let uploadedHeartRates = heartRates.filter { $0.isUploaded }.count
        let uploadedHRVs = hrvs.filter { $0.isUploaded }.count
        
        // 更新已上传数据计数
        uploadedDataCount = uploadedHeartRates + uploadedHRVs
    }
    
    /// 获取电池图标
    private func getBatteryIcon() -> String {
        let level = deviceService.batteryLevel
        if level <= 10 {
            return "battery.0"
        } else if level <= 25 {
            return "battery.25"
        } else if level <= 50 {
            return "battery.50"
        } else if level <= 75 {
            return "battery.75"
                } else {
            return "battery.100"
        }
    }
    
    /// 获取电池颜色
    private func getBatteryColor() -> Color {
        let level = deviceService.batteryLevel
        if level <= 20 {
            return .red
        } else if level <= 40 {
            return .orange
        } else {
            return .green
        }
    }
    
    // 模拟登录
    private func simulateLogin() {
        isLoading = true
        // 使用测试账号登录
        authService.login(email: "<EMAIL>", password: "password123") { result in
            DispatchQueue.main.async {
                self.isLoading = false
                switch result {
                case .success(_):
                    self.checkLoginStatus()
                    self.showAlert(title: "登录成功", message: "已使用测试账号登录")
                case .failure(let error):
                    self.showAlert(title: "登录失败", message: error.localizedDescription)
                }
            }
        }
    }
    
    // 获取并打印所有睡眠数据（不上传）
    private func fetchAndPrintAllSleepData() {
        guard deviceService.connectionState.isConnected else {
            syncResults.insert(
                SyncAction(
                    title: "睡眠数据获取失败",
                    description: "设备未连接",
                    iconName: "xmark.circle.fill",
                    color: .red
                ),
                at: 0
            )
            return
        }
        
        isSyncing = true
        syncResults.insert(
            SyncAction(
                title: "睡眠数据获取",
                description: "开始获取所有睡眠数据...",
                iconName: "arrow.triangle.2.circlepath",
                color: .blue
            ),
            at: 0
        )
        
        print("\n============================================")
        print("开始从戒指获取所有睡眠数据，将以JSON格式在控制台显示...")
        print("============================================\n")
        
        // 检查是否支持GoMore算法
        goMoreService.checkGoMoreSupport { isSupported, error in
            if isSupported {
                print("设备支持GoMore算法，使用GoMore算法获取睡眠数据")
                // 使用GoMore算法获取全部睡眠数据
                self.fetchAllGoMoreSleepData()
                } else {
                print("设备不支持GoMore算法，使用基础睡眠算法")
                // 使用基础睡眠算法获取数据
                self.fetchAllBasicSleepData()
            }
        }
    }
    
    // 使用基础睡眠算法获取所有数据并打印
    private func fetchAllBasicSleepData() {
        // 尝试获取最近30天的数据
        let totalDays = 30
        let dispatchGroup = DispatchGroup()
        var allSleepData: [WindRingDeviceService.SleepData] = []
        
        print("使用基础睡眠算法获取最近\(totalDays)天的睡眠数据...")
        
        for day in 0..<totalDays {
            dispatchGroup.enter()
            
            // 获取第day天的睡眠数据
            deviceService.getSleepData(day: day) { sleepData, error in
                defer { dispatchGroup.leave() }
                
                if let error = error {
                    print("获取第\(day)天睡眠数据失败: \(error.localizedDescription)")
                    return
                }
                
                if let sleepData = sleepData {
                    print("成功获取第\(day)天睡眠数据")
                    allSleepData.append(sleepData)
                } else {
                    print("第\(day)天没有睡眠数据")
                }
            }
            
            // 添加延迟，避免请求过快
            Thread.sleep(forTimeInterval: 0.2)
        }
        
        dispatchGroup.notify(queue: .main) {
            self.isSyncing = false
            
            // 打印获取到的所有数据
            print("\n============= 获取到 \(allSleepData.count) 天的睡眠数据 =============")
            
            if allSleepData.isEmpty {
                print("未获取到任何睡眠数据")
                self.syncResults.insert(
                    SyncAction(
                        title: "睡眠数据获取完成",
                        description: "未发现任何睡眠数据",
                        iconName: "exclamationmark.circle",
                        color: .orange
                    ),
                    at: 0
                )
                return
            }
            
            // 按日期整理数据
            let calendar = Calendar.current
            
            // 将所有数据转换为JSON并打印
            for (index, sleepData) in allSleepData.enumerated() {
                let date = calendar.date(byAdding: .day, value: -index, to: Date()) ?? Date()
                
                // 创建JSON数据
                let jsonData: [String: Any] = [
                    "date": self.formatDateOnly(date),
                    "deep": sleepData.deepSleepMinutes,
                    "light": sleepData.lightSleepMinutes,
                    "rem": sleepData.remSleepMinutes,
                    "type": 0,
                    "startDate": self.formatDateTime(sleepData.startTime),
                    "endDate": self.formatDateTime(sleepData.endTime),
                    "sleepTime": Float(sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes) / 60.0,
                    "sleepEfficiency": self.calculateSleepEfficiency(sleepData),
                    "score": self.calculateSleepScore(sleepData),
                    "records": self.createSleepStageRecords(sleepData)
                ]
                
                // 将数据转换为JSON字符串并打印
                do {
                    let jsonData = try JSONSerialization.data(withJSONObject: jsonData, options: .prettyPrinted)
                    if let jsonString = String(data: jsonData, encoding: .utf8) {
                        print("\n================ 第\(index)天睡眠数据（JSON格式） ================")
                        print(jsonString)
                        print("==========================================================\n")
                    }
                } catch {
                    print("JSON转换错误: \(error.localizedDescription)")
                }
            }
            
            // 完成后更新UI
            self.syncResults.insert(
                SyncAction(
                    title: "成功获取睡眠数据",
                    description: "获取到\(allSleepData.count)天睡眠数据，已在控制台打印",
                    iconName: "checkmark.circle.fill",
                    color: .green
                ),
                at: 0
            )
        }
    }
    
    // 使用GoMore算法获取所有睡眠数据并打印
    private func fetchAllGoMoreSleepData() {
        // 获取GoMore睡眠数据列表
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataList()
        
        // 设置接收数据的回调，注册观察者
        NotificationCenter.default.addObserver(forName: .receivedGoMoreSleepIdsNotification, object: nil, queue: OperationQueue.main) { notification in
            
            // 先取消之前的观察者，避免重复接收通知
            NotificationCenter.default.removeObserver(self, name: .receivedGoMoreSleepIdsNotification, object: nil)
            
            // 检查是否有睡眠数据ID列表
            if !SDKDelegate.shared.gomoreSleepIds.isEmpty {
                print("获取到\(SDKDelegate.shared.gomoreSleepIds.count)个GoMore睡眠ID")
                print("睡眠ID列表：\(SDKDelegate.shared.gomoreSleepIds)")
                
                self.syncResults.insert(
                    SyncAction(
                        title: "GoMore睡眠数据获取中",
                        description: "发现\(SDKDelegate.shared.gomoreSleepIds.count)个睡眠记录",
                        iconName: "arrow.triangle.2.circlepath",
                        color: .blue
                    ),
                    at: 0
                )
                
                // 创建数组存储所有睡眠数据
                var allSleepDataJson: [[String: Any]] = []
                var processedCount = 0
                var successCount = 0
                let serialQueue = DispatchQueue(label: "com.windring.sleepdata.serial")
                
                // 创建递归函数顺序处理ID
                func processNextId(index: Int) {
                    if index >= SDKDelegate.shared.gomoreSleepIds.count {
                        // 所有ID处理完成，打印总结
            DispatchQueue.main.async {
                            self.isSyncing = false
                            
                            print("\n============= 处理完成: 共处理\(processedCount)个ID，成功获取\(successCount)条睡眠数据 =============")
                            
                            if successCount > 0 {
                                // 打印所有睡眠数据的合集JSON
                                print("\n============= 所有睡眠数据的JSON格式 =============")
                                do {
                                    let jsonData = try JSONSerialization.data(withJSONObject: allSleepDataJson, options: .prettyPrinted)
                                    if let jsonString = String(data: jsonData, encoding: .utf8) {
                                        print(jsonString)
                                    }
                                } catch {
                                    print("JSON序列化错误: \(error.localizedDescription)")
                                }
                                print("=================================================\n")
                                
                                // 更新UI
                                self.syncResults.insert(
                                    SyncAction(
                                        title: "GoMore睡眠数据获取成功",
                                        description: "获取到\(successCount)条睡眠数据，已在控制台打印",
                                        iconName: "checkmark.circle.fill",
                                        color: .green
                                    ),
                                    at: 0
                                )
                            } else {
                                self.syncResults.insert(
                                    SyncAction(
                                        title: "GoMore睡眠数据获取完成",
                                        description: "未能成功获取任何睡眠数据",
                                        iconName: "exclamationmark.triangle",
                                        color: .orange
                                    ),
                                    at: 0
                                )
                            }
                        }
                        return
                    }
                    
                    // 获取当前处理的ID
                    let currentId = SDKDelegate.shared.gomoreSleepIds[index]
                    print("\n============================================")
                    print("开始处理睡眠ID: \(currentId) (第 \(index+1)/\(SDKDelegate.shared.gomoreSleepIds.count) 个)")
                    print("============================================")
                    
                    // 添加延迟避免SDK冲突
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                        // 获取睡眠详情
                        CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataDetail(id: currentId) { (sleepDetail, slpError) in
                            if slpError == .none {
                                // 如果获取详情成功，再获取分段数据
                                CRPSmartRingSDK.sharedInstance.getGoMoreSleepSegmentationData(id: currentId) { (segmentData, segError) in
                                    // 在串行队列中处理数据
                                    serialQueue.async {
                                        processedCount += 1
                                        
                                        if slpError == .none && segError == .none {
                                            // 转换时间戳为日期
                                            let startTimestamp = Double(sleepDetail.startTime) / 1000.0
                                            let endTimestamp = Double(sleepDetail.endTime) / 1000.0
                                            let startDate = Date(timeIntervalSince1970: startTimestamp)
                                            let endDate = Date(timeIntervalSince1970: endTimestamp)
                                            
                                            // 计算实际的睡眠日期（使用睡眠开始时间的日期部分）
        let calendar = Calendar.current
                                            let sleepDate = calendar.startOfDay(for: startDate)
                                            
                                            // 创建JSON数据
                                            let jsonData: [String: Any] = [
                                                "id": currentId,
                                                "date": self.formatDateOnly(sleepDate),
                                                "deep": Int(segmentData.deep),
                                                "light": Int(segmentData.light),
                                                "rem": Int(segmentData.rem),
                                                "type": 0,
                                                "startDate": Int(sleepDetail.startTime) * 1000, // 毫秒时间戳
                                                "endDate": Int(sleepDetail.endTime) * 1000, // 毫秒时间戳
                                                "sleepTime": Float(segmentData.deep + segmentData.light + segmentData.rem) / 60.0,
                                                "sleepEfficiency": 1.0, // GoMore没有直接的睡眠效率
                                                "score": sleepDetail.sleepScore,
                                                "detail": segmentData.detail.isEmpty ? [] : "包含\(segmentData.detail.count)条详细记录" // 避免JSON过大
                                            ]
                                            
                                            // 添加到总数据集
                                            allSleepDataJson.append(jsonData)
                                            successCount += 1
                                            
                                            // 打印当前项的JSON
                                            print("\n============= 睡眠数据 ID: \(currentId) =============")
                                            print("日期: \(self.formatDateOnly(sleepDate))")
                                            print("开始时间: \(startDate)")
                                            print("结束时间: \(endDate)")
                                            print("深睡: \(segmentData.deep)分钟, 浅睡: \(segmentData.light)分钟, REM: \(segmentData.rem)分钟")
                                            print("睡眠评分: \(sleepDetail.sleepScore)")
                                            if !segmentData.detail.isEmpty {
                                                print("含有 \(segmentData.detail.count) 条详细睡眠阶段记录")
                                            }
                                            print("================================================\n")
                                        } else {
                                            print("获取睡眠数据失败 - ID \(currentId): 睡眠详情错误 \(slpError.rawValue), 分段数据错误 \(segError.rawValue)")
                                        }
                                        
                                        // 无论成功失败，都处理下一个ID
                                        DispatchQueue.main.async {
                                            processNextId(index: index + 1)
                                        }
                                    }
                                }
                            } else {
                                // 睡眠详情获取失败，直接处理下一个ID
                                processedCount += 1
                                print("获取睡眠详情数据失败 - ID \(currentId): 错误码 \(slpError.rawValue)")
                                
                                // 处理下一个ID
                                DispatchQueue.main.async {
                                    processNextId(index: index + 1)
                                }
                            }
                        }
                    }
                }
                
                // 开始处理第一个ID
                processNextId(index: 0)
            } else {
                // 没有获取到睡眠ID列表
            DispatchQueue.main.async {
                    self.isSyncing = false
                    self.syncResults.insert(
                        SyncAction(
                            title: "GoMore睡眠数据获取完成",
                            description: "未找到任何睡眠记录",
                            iconName: "exclamationmark.circle",
                            color: .orange
                        ),
                        at: 0
                    )
                }
            }
        }
    }
    
    // 格式化日期为仅日期字符串 (yyyy-MM-dd)
    private func formatDateOnly(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    // 格式化日期时间为毫秒级时间戳
    private func formatDateTime(_ date: Date) -> Int {
        // 将Date转换为毫秒级时间戳
        return Int(date.timeIntervalSince1970 * 1000)
    }
    
    // 计算睡眠效率
    private func calculateSleepEfficiency(_ sleepData: WindRingDeviceService.SleepData) -> Float {
        let totalSleepMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
        let totalTime = totalSleepMinutes + sleepData.awakeMinutes
        return totalTime > 0 ? Float(totalSleepMinutes) / Float(totalTime) : 0
    }
    
    // 计算睡眠评分
    private func calculateSleepScore(_ sleepData: WindRingDeviceService.SleepData) -> Float {
        let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
        if totalMinutes == 0 { return 0 }
        
        // 如果有睡眠质量评分，直接使用
        if sleepData.sleepQuality > 0 {
            return Float(sleepData.sleepQuality)
        }
        
        // 否则计算一个评分
        // 深睡占比得分
        let deepRatio = Float(sleepData.deepSleepMinutes) / Float(totalMinutes)
        let deepScore = 40.0 * min(deepRatio / 0.3, 1.0)
        
        // REM睡眠占比得分
        let remRatio = Float(sleepData.remSleepMinutes) / Float(totalMinutes)
        let remScore = 30.0 * min(remRatio / 0.25, 1.0)
        
        // 总睡眠时间得分
        let totalHours = Float(totalMinutes) / 60.0
        let durationScore = totalHours >= 7.0 ? 30.0 : (totalHours / 7.0 * 30.0)
        
        return deepScore + remScore + durationScore
    }
    
    // 创建睡眠阶段记录
    private func createSleepStageRecords(_ sleepData: WindRingDeviceService.SleepData) -> [[String: Any]] {
        var records: [[String: Any]] = []
        
        // 如果有实际睡眠阶段数据，这里会更准确，但示例中我们通过睡眠时长估算
        
        // 先添加深睡阶段
        if sleepData.deepSleepMinutes > 0 {
            let startTime = sleepData.startTime
            let endTime = sleepData.startTime.addingTimeInterval(Double(sleepData.deepSleepMinutes * 60))
            
            records.append([
                "type": 2, // 深睡
                "total": sleepData.deepSleepMinutes,
                "startTime": formatDateTime(startTime),
                "endTime": formatDateTime(endTime)
            ])
        }
        
        // 添加浅睡阶段
        if sleepData.lightSleepMinutes > 0 {
            let startTime = sleepData.startTime.addingTimeInterval(Double(sleepData.deepSleepMinutes * 60))
            let endTime = startTime.addingTimeInterval(Double(sleepData.lightSleepMinutes * 60))
            
            records.append([
                "type": 1, // 浅睡
                "total": sleepData.lightSleepMinutes,
                "startTime": formatDateTime(startTime),
                "endTime": formatDateTime(endTime)
            ])
        }
        
        // 添加REM阶段
        if sleepData.remSleepMinutes > 0 {
            let startTime = sleepData.startTime.addingTimeInterval(Double((sleepData.deepSleepMinutes + sleepData.lightSleepMinutes) * 60))
            let endTime = startTime.addingTimeInterval(Double(sleepData.remSleepMinutes * 60))
            
            records.append([
                "type": 3, // REM
                "total": sleepData.remSleepMinutes,
                "startTime": formatDateTime(startTime),
                "endTime": formatDateTime(endTime)
            ])
        }
        
        // 添加清醒阶段
        if sleepData.awakeMinutes > 0 {
            let startTime = sleepData.startTime.addingTimeInterval(Double((sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes) * 60))
            let endTime = sleepData.endTime
            
            records.append([
                "type": 0, // 清醒
                "total": sleepData.awakeMinutes,
                "startTime": formatDateTime(startTime),
                "endTime": formatDateTime(endTime)
            ])
        }
        
        return records
    }
    
    /// 使用Postman请求格式上传真实睡眠数据
    private func uploadWithPostmanSettings() {
        withAnimation {
            isUploading = true
            uploadProgress = "使用Postman请求格式上传最近14天睡眠数据..."
        }
        
        // 使用新的批量上传方法
        SleepUploadService.shared.uploadLast14DaysWithPostmanSettings { successCount, error in
            DispatchQueue.main.async {
                withAnimation {
                    self.isUploading = false
                    if successCount > 0 {
                        self.showSuccessAlert(message: "成功上传 \(successCount)/14 天睡眠数据\n(没有数据的日期已上传空记录)")
                    } else if let error = error {
                        self.showErrorAlert(message: "上传失败: \(error.localizedDescription)")
                    } else {
                        self.showErrorAlert(message: "上传失败: 未能上传任何数据")
                    }
                }
            }
        }
    }

    /// 显示成功警告
    private func showSuccessAlert(message: String) {
        showAlert(title: "成功", message: message)
    }

    /// 显示错误警告
    private func showErrorAlert(message: String) {
        showAlert(title: "错误", message: message)
    }
    
    // MARK: - 温度数据方法
    /// 获取温度数据
    private func fetchTemperatureData() {
        guard deviceService.connectionState.isConnected else {
            showErrorAlert(message: "设备未连接，无法获取温度数据")
            return
        }
        
        // 添加同步记录
        syncResults.insert(
            SyncAction(
                title: "开始获取温度数据",
                description: "检查并启用睡眠体温监测",
                iconName: "thermometer",
                color: .blue
            ),
            at: 0
        )
        
        // 先检查并启用睡眠体温监测
        temperatureUploadService.checkAndEnableSleepTemperatureMonitoring { success, error in
            if !success {
                if let error = error {
                    self.showErrorAlert(message: "启用睡眠体温监测失败: \(error.localizedDescription)")
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "启用睡眠体温监测失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                }
                return
            }
            
            // 添加同步记录
            self.syncResults.insert(
                SyncAction(
                    title: "睡眠体温监测已启用",
                    description: "正在从设备读取最近3天的体温数据",
                    iconName: "checkmark.circle.fill",
                    color: .green
                ),
                at: 0
            )
            
            // 然后获取体温数据
            self.temperatureUploadService.fetchAndSaveTemperatureData(days: 3) { count, error in
                if let error = error {
                    self.showErrorAlert(message: "获取温度数据失败: \(error.localizedDescription)")
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "获取温度数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else {
                    self.showSuccessAlert(message: "成功获取\(count)条温度数据")
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "获取温度数据成功",
                            description: "已获取\(count)条温度数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新数据计数
                    self.updateDataCounts()
                }
            }
        }
    }
    
    /// 上传温度数据
    private func uploadTemperatureData() {
        guard deviceService.connectionState.isConnected else {
            showErrorAlert(message: "设备未连接，无法上传温度数据")
            return
        }
        
        guard let _ = authService.currentToken?.accessToken else {
            showErrorAlert(message: "用户未登录，无法上传温度数据")
            return
        }
        
        // 添加同步记录
        syncResults.insert(
            SyncAction(
                title: "开始上传温度数据",
                description: "检查并启用睡眠体温监测",
                iconName: "arrow.up.circle",
                color: .blue
            ),
            at: 0
        )
        
        // 先检查并启用睡眠体温监测
        temperatureUploadService.checkAndEnableSleepTemperatureMonitoring { success, error in
            if !success {
                if let error = error {
                    self.showErrorAlert(message: "启用睡眠体温监测失败: \(error.localizedDescription)")
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "启用睡眠体温监测失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                }
                return
            }
            
            // 添加同步记录
            self.syncResults.insert(
                SyncAction(
                    title: "睡眠体温监测已启用",
                    description: "准备上传最近14天温度数据到服务器",
                    iconName: "checkmark.circle.fill",
                    color: .green
                ),
                at: 0
            )
            
            // 然后上传温度数据
            self.temperatureUploadService.uploadLast14DaysSleepTemperatureData { count, error in
                if let error = error {
                    self.showErrorAlert(message: "上传温度数据失败: \(error.localizedDescription)")
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "上传温度数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else {
                    self.showSuccessAlert(message: "成功上传\(count)天的温度数据")
                    
                    // 添加同步记录
                    self.syncResults.insert(
                        SyncAction(
                            title: "上传温度数据成功",
                            description: "已上传\(count)天的温度数据",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新最后同步时间
                    self.lastSyncTime = Date()
                    UserDefaults.standard.set(self.lastSyncTime, forKey: "lastTemperatureUploadTime")
                }
            }
        }
    }
    
    /// 测试直接使用postman配置上传温度数据
    private func testDirectTemperatureUpload() {
        guard let _ = authService.currentToken?.accessToken else {
            showErrorAlert(message: "用户未登录，无法上传温度数据")
            return
        }
        
        // 添加同步记录
        syncResults.insert(
            SyncAction(
                title: "开始测试温度数据上传",
                description: "使用Postman配置直接上传",
                iconName: "arrow.up.circle",
                color: .blue
            ),
            at: 0
        )
        
        // 今天的日期字符串
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let today = dateFormatter.string(from: Date())
        
        temperatureUploadService.uploadTemperatureWithPostmanSettings(date: today) { success, error in
            if success {
                self.showSuccessAlert(message: "测试温度数据上传成功")
                
                // 添加同步记录
                self.syncResults.insert(
                    SyncAction(
                        title: "测试温度数据上传成功",
                        description: "使用日期: \(today)",
                        iconName: "checkmark.circle.fill",
                        color: .green
                    ),
                    at: 0
                )
            } else {
                let errorMessage = error?.localizedDescription ?? "未知错误"
                self.showErrorAlert(message: "测试温度数据上传失败: \(errorMessage)")
                
                // 添加同步记录
                self.syncResults.insert(
                    SyncAction(
                        title: "测试温度数据上传失败",
                        description: errorMessage,
                        iconName: "xmark.circle.fill",
                        color: .red
                    ),
                    at: 0
                )
            }
        }
    }
    
    /// 获取活动数据
    private func fetchActivityData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        isLoading = true
        
        // 使用公共方法获取基本步数数据作为活动数据
        activityUploadService.getBasicActivityData { stepModel, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if let error = error {
                    showAlert(title: "获取失败", message: error.localizedDescription)
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取活动数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else if let stepModel = stepModel {
                    showAlert(
                        title: "获取成功",
                        message: "成功获取活动数据：\(stepModel.steps)步，\(stepModel.distance)米，\(stepModel.calory)卡路里，\(stepModel.time)秒"
                    )
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取活动数据成功",
                            description: "步数：\(stepModel.steps)，距离：\(stepModel.distance)米",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    updateDataCounts()
                } else {
                    showAlert(title: "获取失败", message: "未能获取活动数据")
                    
                    syncResults.insert(
                        SyncAction(
                            title: "获取活动数据失败",
                            description: "未能获取活动数据",
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                }
            }
        }
    }
    
    /// 上传活动数据
    private func uploadActivityData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        guard isLoggedIn else {
            showAlert(title: "错误", message: "请先登录后再上传数据")
            return
        }
        
        isLoading = true
        
        activityUploadService.uploadCurrentActivityData { success, error in
            DispatchQueue.main.async {
                isLoading = false
                self.lastSyncTime = Date()
                UserDefaults.standard.set(self.lastSyncTime, forKey: "lastActivityUploadTime")
                
                if let error = error {
                    showAlert(title: "上传失败", message: error.localizedDescription)
                    
                    syncResults.insert(
                        SyncAction(
                            title: "上传活动数据失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else if success {
                    showAlert(
                        title: "上传成功",
                        message: "成功上传活动数据到云端"
                    )
                    
                    syncResults.insert(
                        SyncAction(
                            title: "上传活动数据成功",
                            description: "活动数据已成功上传到云端",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    updateDataCounts()
                } else {
                    showAlert(title: "上传失败", message: "未知错误")
                    
                    syncResults.insert(
                        SyncAction(
                            title: "上传活动数据失败",
                            description: "未知错误",
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                }
            }
        }
    }
    
    /// 获取并上传全部活动数据
    private func syncAllActivityData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "请先连接设备")
            return
        }
        
        guard isLoggedIn else {
            showAlert(title: "错误", message: "请先登录后再上传数据")
            return
        }
        
        isLoading = true
        
        activityUploadService.uploadCurrentActivityData { success, error in
            DispatchQueue.main.async {
                isLoading = false
                self.lastSyncTime = Date()
                UserDefaults.standard.set(self.lastSyncTime, forKey: "lastActivityUploadTime")
                
                if let error = error {
                    showAlert(title: "同步失败", message: error.localizedDescription)
                    
                    syncResults.insert(
                        SyncAction(
                            title: "活动数据同步失败",
                            description: error.localizedDescription,
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                } else if success {
                    showAlert(
                        title: "同步成功",
                        message: "成功获取并上传活动数据到云端"
                    )
                    
                    syncResults.insert(
                        SyncAction(
                            title: "活动数据同步成功",
                            description: "活动数据已成功获取并上传到云端",
                            iconName: "checkmark.circle.fill",
                            color: .green
                        ),
                        at: 0
                    )
                    
                    // 更新总数据数量
                    updateDataCounts()
                } else {
                    showAlert(title: "同步失败", message: "未知错误")
                    
                    syncResults.insert(
                        SyncAction(
                            title: "活动数据同步失败",
                            description: "未知错误",
                            iconName: "xmark.circle.fill",
                            color: .red
                        ),
                        at: 0
                    )
                }
            }
        }
    }
    
    // MARK: - 睡眠数据处理方法
    
    /// 保存睡眠数据到本地数据库
    private func saveSleepDataToLocalDB(sleepData: WindRingDeviceService.SleepData, userId: String) {
        print("🔍 开始保存睡眠数据: \(sleepData)")
        print("🔍 用户ID: \(userId)")
        
        // 检查用户是否存在
//        if healthDataManager.getUser(id: userId) == nil {
//            print("❌ 睡眠数据保存失败: 用户ID \(userId) 不存在")
//            print("👉 创建临时用户以保存数据")
//            
//            // 创建临时用户
//            healthDataManager.createUser(
//                id: userId,
//                name: "临时用户",
//                email: "temp_\(userId)@example.com", completion: {success in
//                    if success {
//                        print("✅ 临时用户创建成功")
//                    } else {
//                        print("❌ 临时用户创建失败，无法保存睡眠数据")
//                        return
//                    }
//                }
//            )
//            
//            
//        }
        
        // 计算睡眠评分
        let sleepScore = calculateSleepScore(sleepData: sleepData)
        print("🔍 计算的睡眠评分: \(sleepScore)")
        
        // 打印睡眠数据信息
        let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
        print("🔍 总睡眠时间(分钟): \(totalMinutes)")
        print("🔍 深睡时间(分钟): \(sleepData.deepSleepMinutes)")
        print("🔍 浅睡时间(分钟): \(sleepData.lightSleepMinutes)")
        print("🔍 REM时间(分钟): \(sleepData.remSleepMinutes)")
        
        // 保存到数据库
        let success = healthDataManager.addSleep(
            userId: userId,
            startTime: sleepData.startTime,
            endTime: sleepData.endTime,
            totalMinutes: Int16(totalMinutes),
            deepMinutes: Int16(sleepData.deepSleepMinutes),
            lightMinutes: Int16(sleepData.lightSleepMinutes),
            remMinutes: Int16(sleepData.remSleepMinutes),
            awakeMinutes: Int16(sleepData.awakeMinutes),
            score: Int16(sleepScore),
            efficiency: Int16(85), // 默认值
            deviceId: "ring_device",
            sleepStages: createSleepStages(from: sleepData)
        )
        
        if success {
            print("✅ 睡眠数据成功保存到本地数据库")
        } else {
            print("❌ 睡眠数据保存失败")
            print("👉 可能的原因: 用户ID不存在、参数无效或数据库错误")
            
            // 打印用户列表以验证
//            let users = healthDataManager.getAllUsers()
//            print("📊 系统中的用户列表: \(users?.map { $0.id ?? "未知" } ?? ["无用户"])")
        }
    }
    
    /// 保存合并的GoMore睡眠数据
    private func saveCombinedSleepData(sleepDetail: CRPSmartRing.CRPGoMoreSleepDataModel, sleepSegment: CRPSmartRing.CRPGoMoreSleepRecordModel, userId: String) {
        print("🔍 开始保存合并GoMore睡眠数据")
        print("🔍 用户ID: \(userId)")
        
        // 创建时间
        let startTime = Date(timeIntervalSince1970: Double(sleepDetail.startTime) / 1000.0)
        let endTime = Date(timeIntervalSince1970: Double(sleepDetail.endTime) / 1000.0)
        
        // 从分段数据中获取睡眠阶段
        let deepSleepMinutes = sleepSegment.deep
        let lightSleepMinutes = sleepSegment.light
        let remSleepMinutes = sleepSegment.rem
        let awakeMinutes = 0 // 可能需要从其他字段计算
        
        // 计算睡眠评分
        let sleepQuality = Int(sleepDetail.sleepScore)
        
        print("🔍 睡眠开始时间: \(startTime)")
        print("🔍 睡眠结束时间: \(endTime)")
        print("🔍 深睡时间(分钟): \(deepSleepMinutes)")
        print("🔍 浅睡时间(分钟): \(lightSleepMinutes)")
        print("🔍 REM时间(分钟): \(remSleepMinutes)")
        print("🔍 GoMore睡眠评分: \(sleepQuality)")
        
        // 创建睡眠数据对象
        let sleepData = WindRingDeviceService.SleepData(
            startTime: startTime,
            endTime: endTime,
            deepSleepMinutes: deepSleepMinutes,
            lightSleepMinutes: lightSleepMinutes,
            remSleepMinutes: remSleepMinutes,
            awakeMinutes: awakeMinutes,
            sleepQuality: sleepQuality
        )
        
        // 保存到本地数据库
        saveSleepDataToLocalDB(sleepData: sleepData, userId: userId)
    }
    
    /// 计算睡眠评分
    private func calculateSleepScore(sleepData: WindRingDeviceService.SleepData) -> Int {
        // 如果设备已提供睡眠质量评分，直接使用
        if sleepData.sleepQuality > 0 {
            return sleepData.sleepQuality
        }
        
        // 否则自行计算
        let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
        if totalMinutes == 0 { return 0 }
        
        // 深睡占比得分 (理想30%)
        let deepRatio = Double(sleepData.deepSleepMinutes) / Double(totalMinutes)
        let deepScore = 40.0 * min(deepRatio / 0.3, 1.0)
        
        // REM睡眠占比得分 (理想25%)
        let remRatio = Double(sleepData.remSleepMinutes) / Double(totalMinutes)
        let remScore = 30.0 * min(remRatio / 0.25, 1.0)
        
        // 总睡眠时间得分 (理想7-8小时)
        let totalHours = Double(totalMinutes) / 60.0
        let durationScore: Double
        if totalHours < 6.0 {
            durationScore = 30.0 * (totalHours / 6.0)
        } else if totalHours <= 9.0 {
            durationScore = 30.0
        } else {
            // 超过9小时睡眠，分数逐渐降低
            durationScore = 30.0 * max(0, (11.0 - totalHours) / 2.0)
        }
        
        return Int(deepScore + remScore + durationScore)
    }
    
    /// 创建睡眠阶段数据
    private func createSleepStages(from sleepData: WindRingDeviceService.SleepData) -> [(type: String, startTime: Date, duration: Int16)]? {
        let totalMinutes = sleepData.deepSleepMinutes + sleepData.lightSleepMinutes + sleepData.remSleepMinutes
        if totalMinutes == 0 { return nil }
        
        var stages: [(type: String, startTime: Date, duration: Int16)] = []
        var currentTime = sleepData.startTime
        
        // 添加深睡阶段
        if sleepData.deepSleepMinutes > 0 {
            stages.append((type: "deep", startTime: currentTime, duration: Int16(sleepData.deepSleepMinutes)))
            currentTime = currentTime.addingTimeInterval(Double(sleepData.deepSleepMinutes * 60))
        }
        
        // 添加浅睡阶段
        if sleepData.lightSleepMinutes > 0 {
            stages.append((type: "light", startTime: currentTime, duration: Int16(sleepData.lightSleepMinutes)))
            currentTime = currentTime.addingTimeInterval(Double(sleepData.lightSleepMinutes * 60))
        }
        
        // 添加REM阶段
        if sleepData.remSleepMinutes > 0 {
            stages.append((type: "rem", startTime: currentTime, duration: Int16(sleepData.remSleepMinutes)))
        }
        
        return stages
    }
}

// MARK: - 同步操作模型
struct SyncAction: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let timestamp: Date
    let iconName: String
    let color: Color
    
    init(title: String, description: String, iconName: String, color: Color) {
        self.title = title
        self.description = description
        self.timestamp = Date()
        self.iconName = iconName
        self.color = color
    }
}

// MARK: - 日期格式化
extension DataSyncTestView {
    var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }
    
    var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter
    }
}

// MARK: - 预览提供者
struct DataSyncTestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
        DataSyncTestView()
    }
    }
}

// MARK: - CRPManager代理实现
#if os(iOS)
// 创建一个类来实现CRPManagerDelegate协议
class SDKDelegate: NSObject, CRPManagerDelegate {
    static let shared = SDKDelegate()
    var gomoreSleepIds: [Int] = []
    
    // 当接收到睡眠数据列表时调用
    func receiveSleepList(_ sleepRecords: [CRPGoMoreSleepRecord]) {
        print("接收到睡眠记录列表: \(sleepRecords.count)条记录")
        
        // 提取ID列表并存储到我们的属性中
        self.gomoreSleepIds = sleepRecords.map { $0.id }
        
        // 发送通知，以便任何观察者能够响应数据
        NotificationCenter.default.post(
            name: .receivedGoMoreSleepIdsNotification,
            object: nil
        )
    }
    
    // 其他必需的代理方法（可以提供空实现）
    func didState(_ state: CRPState) {}
    func didBluetoothState(_ state: CRPBluetoothState) {}
    func receiveSteps(_ model: CRPStepModel) {}
    func receiveHeartRate(_ heartRate: Int) {}
    func receiveRealTimeHeartRate(_ heartRate: Int) {}
    func receiveHRV(_ hrv: Int) {}
    func receiveSpO2(_ o2: Int) {}
    func receiveOTA(_ state: CRPOTAState, _ progress: Int) {}
    func receiveStress(_ stress: Int) {}
}
#endif 

// MARK: - 通知名称扩展
// 注意：所有通知名称已移至 NotificationExtensions.swift 中统一管理，此段代码已被注释掉
/*
extension Notification.Name {
    /// 当GoMore睡眠ID列表数据接收完成时发送的通知
    static let receivedGoMoreSleepIdsNotification = Notification.Name("receivedGoMoreSleepIds")
}
*/
