import SwiftUI
import CRPSmartRing
import Combine

/// 卡路里API测试页面
class CalorieAPITestViewModel: ObservableObject {
    // MARK: - 属性
    @Published var results: [TestResult] = []
    @Published var isLoading: Bool = false
    @Published var calorieToday: Int = 0
    @Published var selectedDay: Int = 0
    @Published var trainingGoal: Int = 0
    @Published var showGoalEditSheet: Bool = false
    @Published var showShareSheet: Bool = false
    @Published var shareText: String = ""
    
    // MARK: - 初始化
    init() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleStepsUpdate),
            name: .stepsUpdated,
            object: nil
        )
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 通知处理
    @objc func handleStepsUpdate(_ notification: Notification) {
        if let stepModel = notification.object as? CRPStepModel {
            DispatchQueue.main.async {
                self.calorieToday = stepModel.calory
                self.addResult(message: "收到步数更新，当前卡路里: \(stepModel.calory)", color: .green)
            }
        }
    }
    
    // MARK: - 测试结果管理
    func addResult(message: String, color: Color = .primary) {
        let result = TestResult(message: message, color: color)
        DispatchQueue.main.async {
            self.results.insert(result, at: 0)
        }
    }
    
    func clearResults() {
        DispatchQueue.main.async {
            self.results.removeAll()
        }
    }
    
    func exportResults() {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        var text = "卡路里API测试结果 - \(dateFormatter.string(from: Date()))\n\n"
        
        for (index, result) in results.enumerated() {
            text += "\(index + 1). \(result.message) (\(dateFormatter.string(from: result.timestamp)))\n"
        }
        
        self.shareText = text
        self.showShareSheet = true
    }
    
    // MARK: - API调用
    /// 获取当前步数（包含卡路里）
    func getCurrentSteps() {
        guard WindRingDeviceService.shared.connectionState.isConnected else {
            addResult(message: "设备未连接", color: .red)
            return
        }
        
        isLoading = true
        addResult(message: "正在获取当前步数和卡路里...")
        
        // 调用SDK的getSteps方法
        CRPSmartRingSDK.sharedInstance.getSteps()
        
        // 由于结果通过通知回调，这里设置一个定时器结束加载状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.isLoading = false
        }
    }
    
    /// 获取历史训练数据（包含卡路里）
    func getTrainingData() {
        guard WindRingDeviceService.shared.connectionState.isConnected else {
            addResult(message: "设备未连接", color: .red)
            return
        }
        
        isLoading = true
        addResult(message: "正在获取第\(selectedDay)天的训练数据...")
        
        CRPSmartRingSDK.sharedInstance.getTrainingData(selectedDay) { [weak self] model, error in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.isLoading = false
                
                // 根据error判断是否成功，而不是检查model是否为nil
                if error == .none {
                    // 根据SDK文档，CRPTrainingRecordModel使用cal而不是kcal属性
                    self.calorieToday = model.cal
                    self.addResult(message: "获取成功: 第\(self.selectedDay)天卡路里消耗为\(model.cal)小卡", color: .green)
                } else {
                    // 处理CRPError
                    let errorMessage: String
                    switch error {
                    case .none:
                        errorMessage = "未知错误" // 这里不会执行到，保留以避免编译警告
                    case .disconnected:
                        errorMessage = "设备已断开连接"
                    case .busy:
                        errorMessage = "设备忙"
                    case .timeout:
                        errorMessage = "请求超时"
                    case .interrupted:
                        errorMessage = "操作被中断"
                    case .internalError:
                        errorMessage = "内部错误"
                    case .noCentralManagerSet:
                        errorMessage = "蓝牙管理器未设置"
                    case .other:
                        errorMessage = "其他错误"
                    @unknown default:
                        errorMessage = "未定义错误"
                    }
                    self.addResult(message: "获取训练数据失败: \(errorMessage)", color: .red)
                }
            }
        }
    }
    
    /// 获取卡路里目标
    func getCalorieGoal() {
        guard WindRingDeviceService.shared.connectionState.isConnected else {
            addResult(message: "设备未连接", color: .red)
            return
        }
        
        isLoading = true
        addResult(message: "正在获取卡路里目标...")
        
        CRPSmartRingSDK.sharedInstance.getNormalTrainingGoal { [weak self] model, error in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.isLoading = false
                
                // 根据error判断是否成功，而不是检查model是否为nil
                if error == .none {
                    self.trainingGoal = model.kcal
                    self.addResult(message: "获取成功: 卡路里目标为\(model.kcal)小卡", color: .green)
                } else {
                    // 处理CRPError
                    let errorMessage: String
                    switch error {
                    case .none:
                        errorMessage = "未知错误" // 这里不会执行到，保留以避免编译警告
                    case .disconnected:
                        errorMessage = "设备已断开连接"
                    case .busy:
                        errorMessage = "设备忙"
                    case .timeout:
                        errorMessage = "请求超时"
                    case .interrupted:
                        errorMessage = "操作被中断"
                    case .internalError:
                        errorMessage = "内部错误"
                    case .noCentralManagerSet:
                        errorMessage = "蓝牙管理器未设置"
                    case .other:
                        errorMessage = "其他错误"
                    @unknown default:
                        errorMessage = "未定义错误"
                    }
                    self.addResult(message: "获取卡路里目标失败: \(errorMessage)", color: .red)
                }
            }
        }
    }
    
    /// 设置卡路里目标
    func setCalorieGoal() {
        guard WindRingDeviceService.shared.connectionState.isConnected else {
            addResult(message: "设备未连接", color: .red)
            return
        }
        
        isLoading = true
        addResult(message: "正在设置卡路里目标为\(trainingGoal)小卡...")
        
        // 使用正确的构造函数创建CRPTrainingGoalsModel
        let goals = CRPTrainingGoalsModel(step: 8000, kcal: trainingGoal, distance: 5000, exerciseTime: 60)
        
        CRPSmartRingSDK.sharedInstance.setNormalTrainingGoal(goals)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            self.isLoading = false
            self.addResult(message: "卡路里目标设置成功: \(self.trainingGoal)小卡", color: .green)
        }
    }
}

// MARK: - 测试结果模型
struct TestResult: Identifiable {
    let id = UUID()
    let message: String
    let color: Color
    let timestamp = Date()
}

// MARK: - 视图
struct CalorieAPITestView: View {
    @StateObject private var viewModel = CalorieAPITestViewModel()
    @State private var editedGoal: Int = 0
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 卡路里显示
                VStack {
                    Text("当前卡路里消耗")
                        .font(.headline)
                    
                    Text("\(viewModel.calorieToday)")
                        .font(.system(size: 48, weight: .bold))
                        .foregroundColor(.orange)
                        .padding()
                    
                    Button(action: {
                        viewModel.getCurrentSteps()
                    }) {
                        Text("获取当前卡路里")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(Color.orange)
                            .cornerRadius(10)
                    }
                    .disabled(viewModel.isLoading)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(15)
                
                // 历史数据获取
                VStack {
                    Text("历史卡路里数据")
                        .font(.headline)
                    
                    Picker(selection: $viewModel.selectedDay, label: Text("选择日期")) {
                        Text("今天").tag(0)
                        Text("昨天").tag(1)
                        Text("前天").tag(2)
                        Text("大前天").tag(3)
                        Text("4天前").tag(4)
                        Text("5天前").tag(5)
                        Text("6天前").tag(6)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .padding(.vertical)
                    
                    Button(action: {
                        viewModel.getTrainingData()
                    }) {
                        Text("获取历史卡路里数据")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                    .disabled(viewModel.isLoading)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(15)
                
                // 卡路里目标设置
                VStack {
                    Text("卡路里目标设置")
                        .font(.headline)
                    
                    HStack {
                        Text("当前目标:")
                        Text("\(viewModel.trainingGoal) 小卡")
                            .bold()
                        Spacer()
                        Button(action: {
                            editedGoal = viewModel.trainingGoal
                            viewModel.showGoalEditSheet = true
                        }) {
                            Text("修改")
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.blue)
                                .foregroundColor(.white)
                                .cornerRadius(8)
                        }
                    }
                    .padding(.vertical)
                    
                    HStack(spacing: 10) {
                        Button(action: {
                            viewModel.getCalorieGoal()
                        }) {
                            Text("获取目标")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color.green)
                                .cornerRadius(10)
                        }
                        
                        Button(action: {
                            viewModel.setCalorieGoal()
                        }) {
                            Text("设置目标")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color.purple)
                                .cornerRadius(10)
                        }
                    }
                    .disabled(viewModel.isLoading)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(15)
                
                // 测试结果区域
                VStack(alignment: .leading) {
                    HStack {
                        Text("测试结果")
                            .font(.headline)
                        Spacer()
                        Button(action: {
                            viewModel.clearResults()
                        }) {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                        }
                        Button(action: {
                            viewModel.exportResults()
                        }) {
                            Image(systemName: "square.and.arrow.up")
                                .foregroundColor(.blue)
                        }
                    }
                    
                    if viewModel.results.isEmpty {
                        Text("无测试记录")
                            .foregroundColor(.gray)
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding()
                    } else {
                        ForEach(viewModel.results) { result in
                            VStack(alignment: .leading) {
                                Text(result.message)
                                    .foregroundColor(result.color)
                                
                                Text(result.timestamp, style: .time)
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.vertical, 4)
                            
                            Divider()
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(15)
            }
            .padding()
        }
        .navigationTitle("卡路里API测试")
        .overlay(
            ZStack {
                if viewModel.isLoading {
                    Color.black.opacity(0.2)
                        .ignoresSafeArea()
                    
                    ProgressView()
                        .scaleEffect(1.5)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .background(Color.black.opacity(0.6))
                        .cornerRadius(10)
                        .frame(width: 80, height: 80)
                }
            }
        )
        .sheet(isPresented: $viewModel.showGoalEditSheet) {
            NavigationView {
                VStack {
                    Stepper("卡路里目标: \(editedGoal) 小卡", value: $editedGoal, in: 100...10000, step: 100)
                        .padding()
                    
                    Spacer()
                }
                .navigationTitle("设置卡路里目标")
                .navigationBarItems(
                    leading: Button("取消") {
                        viewModel.showGoalEditSheet = false
                    },
                    trailing: Button("保存") {
                        viewModel.trainingGoal = editedGoal
                        viewModel.showGoalEditSheet = false
                    }
                )
            }
        }
        .sheet(isPresented: $viewModel.showShareSheet) {
            ActivityView(text: viewModel.shareText)
        }
        .onAppear {
            viewModel.getCurrentSteps()
            viewModel.getCalorieGoal()
        }
    }
}

// MARK: - 分享功能
struct ActivityView: UIViewControllerRepresentable {
    let text: String
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        return UIActivityViewController(
            activityItems: [text],
            applicationActivities: nil
        )
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - 预览
struct CalorieAPITestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            CalorieAPITestView()
        }
    }
} 