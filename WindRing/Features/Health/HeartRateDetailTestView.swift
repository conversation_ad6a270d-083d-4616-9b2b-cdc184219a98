import SwiftUI
import Charts
import CRPSmartRing

/// 心率明细测试视图
struct HeartRateDetailTestView: View {
    @State private var heartRateDetails: [HeartRateData] = []
    @State private var isLoading = false
    @State private var selectedDay = 0
    @State private var message = "请点击按钮获取心率明细数据"
    @State private var messageColor: Color = .gray
    @State private var averageHeartRate = 0
    @State private var maxHeartRate = 0
    @State private var minHeartRate = 0
    @State private var showHeartRateData = true
    @State private var isUploading = false
    @State private var uploadResult = ""
    @State private var uploadResultColor: Color = .gray
    
    // 自动上传开关
    @State private var autoUploadEnabled: Bool = true
    
    // 日期选项
    let dayOptions = ["今天", "昨天", "前天", "三天前", "四天前", "五天前", "六天前"]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 状态消息
                    Text(message)
                        .font(.headline)
                        .foregroundColor(messageColor)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                    
                    // 控制栏
                    controlBar
                    
                    // 心率数据显示
                    if showHeartRateData {
                        heartRateDataSection
                        
                        // 心率图表区域
                        heartRateChartSection
                        
                        // 心率列表区域
                        heartRateListSection
                    }
                    
                    // 上传按钮和状态
                    VStack {
                        Button(action: uploadHeartRateData) {
                            Text("手动上传心率数据")
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                        .disabled(isLoading || isUploading || heartRateDetails.isEmpty)
                        
                        if !uploadResult.isEmpty {
                            Text(uploadResult)
                                .font(.subheadline)
                                .foregroundColor(uploadResultColor)
                                .padding(.top, 5)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                    
                    // 加载指示器
                    if isLoading || isUploading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                            .padding()
                    }
                }
                .padding()
            }
            .navigationTitle("心率数据测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        Text("心率记录: \(heartRateDetails.count)")
                            .font(.footnote)
                            .foregroundColor(.gray)
                        
                        if !heartRateDetails.isEmpty {
                            // 显示自动上传标志
                            Image(systemName: "arrow.triangle.2.circlepath.circle.fill")
                                .foregroundColor(.green)
                        }
                        
                        // 添加验证按钮
                        Button(action: checkUploadStatus) {
                            Image(systemName: "checkmark.shield")
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
            .onAppear {
                // 确保自动上传已启用
                UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
                self.autoUploadEnabled = true
                
                // 如果连接已就绪但上传服务未启动，则启动上传服务
                if WindRingDeviceService.shared.connectionState.isConnected && 
                   !RawDataUploadService.shared.isAutoUploadEnabled {
                    updateAutoUploadSetting(enabled: true)
                }
                
                // 首次加载时获取心率明细
                fetchHeartRateDetails()
                
                // 检查并更新自动上传状态
                checkUploadStatus()
                
                // 设置定时器，每5秒更新一次自动上传状态
                // 这样可以确保UI显示与服务实际状态保持同步
                Timer.scheduledTimer(withTimeInterval: 5, repeats: true) { _ in
                    self.autoUploadEnabled = RawDataUploadService.shared.isAutoUploadEnabled || 
                                           UserDefaults.standard.bool(forKey: "auto_upload_enabled")
                }
            }
            .onDisappear {
                // 恢复自动上传功能
                if UserDefaults.standard.bool(forKey: "auto_upload_enabled") {
                    RawDataUploadService.shared.startAutoUpload()
                }
            }
        }
    }
    
    // MARK: - 心率数据区域
    private var heartRateDataSection: some View {
        VStack {
            if heartRateDetails.isEmpty {
                Text("无心率数据")
                    .font(.title2)
                    .foregroundColor(.gray)
                    .padding()
                    .frame(maxWidth: .infinity)
            } else {
                VStack(spacing: 15) {
                    Text("心率统计")
                        .font(.headline)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    HStack(spacing: 15) {
                        // 平均心率卡片
                        heartRateDataCard(
                            title: "平均心率",
                            value: "\(averageHeartRate)",
                            icon: "heart.fill",
                            color: .red
                        )
                        
                        // 最高心率卡片
                        heartRateDataCard(
                            title: "最高心率",
                            value: "\(maxHeartRate)",
                            icon: "arrow.up.heart.fill",
                            color: .orange
                        )
                    }
                    
                    HStack(spacing: 15) {
                        // 最低心率卡片
                        heartRateDataCard(
                            title: "最低心率",
                            value: "\(minHeartRate)",
                            icon: "arrow.down.heart.fill",
                            color: .blue
                        )
                        
                        // 记录数量卡片
                        heartRateDataCard(
                            title: "记录数量",
                            value: "\(heartRateDetails.count)",
                            icon: "list.bullet.clipboard",
                            color: .green
                        )
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
            }
        }
    }
    
    // 心率数据卡片
    private func heartRateDataCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            
            Text(value)
                .font(.headline)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color.white)
        .cornerRadius(8)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - 心率图表区域
    private var heartRateChartSection: some View {
        VStack {
            Text("24小时心率趋势")
                .font(.headline)
                .padding(.top)
            
            Chart {
                ForEach(heartRateDetails) { detail in
                    if let date = detail.date {
                        LineMark(
                            x: .value("时间", date, unit: .hour),
                            y: .value("心率", detail.hearts)
                        )
                        .foregroundStyle(Color.red.gradient)
                    }
                }
            }
            .frame(height: 250)
            .padding()
            .chartXAxis {
                AxisMarks(values: .stride(by: .hour, count: 3)) { value in
                    AxisGridLine()
                    AxisValueLabel(format: .dateTime.hour())
                }
            }
        }
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .padding(.horizontal)
    }
    
    // MARK: - 心率列表区域
    private var heartRateListSection: some View {
        VStack {
            HStack {
                Text("心率明细")
                    .font(.headline)
                
                Spacer()
                
                Text("共 \(heartRateDetails.count) 条记录")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding(.horizontal)
            
            List {
                ForEach(heartRateDetails) { detail in
                    HStack {
                        Text(detail.formattedTime())
                            .font(.system(.body, design: .monospaced))
                        
                        Spacer()
                        
                        Text("\(detail.hearts) bpm")
                            .foregroundColor(.red)
                            .font(.headline)
                    }
                    .padding(.vertical, 4)
                }
            }
            .frame(height: 200)
            .listStyle(PlainListStyle())
        }
    }
    
    /// 获取心率明细数据
    private func fetchHeartRateDetails() {
        // 清空之前的数据和状态
        heartRateDetails = []
        averageHeartRate = 0
        maxHeartRate = 0
        minHeartRate = 0
        uploadResult = ""
        
        isLoading = true
        
        // 计算实际日期并显示
        let calendar = Calendar.current
        let today = Date()
        let targetDate = calendar.date(byAdding: .day, value: -selectedDay, to: today) ?? today
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: targetDate)
        
        message = "正在获取\(dateString)的心率数据..."
        messageColor = .blue
        
        print("🔍 开始获取日期 \(dateString) 的心率数据 (day参数: \(selectedDay))")
        
        // 使用RawDataUploadService获取心率明细
        RawDataUploadService.shared.fetchHeartRateDetailData(day: selectedDay) { details, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if let error = error {
                    message = "获取\(dateString)心率明细失败: \(error.localizedDescription)"
                    messageColor = .red
                    return
                }
                
                if let details = details, !details.isEmpty {
                    heartRateDetails = details
                    
                    // 计算统计数据
                    let heartRates = details.map { $0.hearts }
                    maxHeartRate = heartRates.max() ?? 0
                    minHeartRate = heartRates.min() ?? 0
                    averageHeartRate = heartRates.reduce(0, +) / heartRates.count
                    
                    message = "\(dateString) 获取成功，共\(details.count)条记录，平均心率\(averageHeartRate)bpm"
                    messageColor = .green
                    
                    // 自动显示心率数据
                    showHeartRateData = true
                    
                    // 如果启用了自动上传，获取数据后自动上传
                    if autoUploadEnabled {
                        uploadHeartRateData()
                    }
                } else {
                    message = "\(dateString) 没有心率明细数据"
                    messageColor = .orange
                }
            }
        }
    }
    
    /// 上传心率数据到服务器
    private func uploadHeartRateData() {
        isUploading = true
        uploadResult = "正在上传心率数据..."
        uploadResultColor = .blue
        
        RawDataUploadService.shared.uploadHeartRateData(day: selectedDay) { success, error in
            DispatchQueue.main.async {
                isUploading = false
                
                if success {
                    uploadResult = "心率数据上传成功！"
                    uploadResultColor = .green
                } else if let error = error {
                    uploadResult = "上传失败: \(error.localizedDescription)"
                    uploadResultColor = .red
                } else {
                    uploadResult = "上传失败: 未知错误"
                    uploadResultColor = .red
                }
            }
        }
    }
    
    /// 更新自动上传设置
    private func updateAutoUploadSetting(enabled: Bool) {
        if enabled {
            // 启用自动上传
            let success = RawDataUploadService.shared.startAutoUpload(interval: 300) // 5分钟
            if !success {
                // 如果启动失败，恢复开关状态
                DispatchQueue.main.async {
                    self.autoUploadEnabled = false
                }
            }
        } else {
            // 禁用自动上传
            RawDataUploadService.shared.stopAutoUpload()
        }
    }
    
    /// 验证自动上传状态
    private func checkUploadStatus() {
        // 调用服务验证状态
        RawDataUploadService.shared.checkAutoUploadStatus()
        
        // 刷新UI上的状态显示
        DispatchQueue.main.async {
            self.autoUploadEnabled = RawDataUploadService.shared.isAutoUploadEnabled || UserDefaults.standard.bool(forKey: "auto_upload_enabled")
            
            // 显示验证消息
            self.message = "已检查自动上传状态"
            self.messageColor = .blue
        }
    }
    
    // MARK: - 控制栏
    private var controlBar: some View {
        VStack {
            HStack {
                // 日期选择器
                Picker("天数", selection: $selectedDay) {
                    Text("今天").tag(0)
                    Text("昨天").tag(1)
                    Text("前天").tag(2)
                    Text("3天前").tag(3)
                    Text("4天前").tag(4)
                    Text("5天前").tag(5)
                    Text("6天前").tag(6)
                }
                .pickerStyle(SegmentedPickerStyle())
                .onChange(of: selectedDay) { _ in
                    fetchHeartRateDetails()
                }
                
                Spacer()
                
                // 获取心率数据按钮
                Button(action: {
                    fetchHeartRateDetails()
                }) {
                    Image(systemName: "arrow.clockwise.circle")
                        .font(.title2)
                }
            }
            .padding(.bottom, 5)
            
            // 状态信息显示
            HStack {
                Text(message)
                    .font(.caption)
                    .foregroundColor(messageColor)
                
                Spacer()
                
                // 自动上传开关 - 显示当前状态
                HStack {
                    Text("自动上传数据")
                        .font(.caption)
                    
                    Toggle("", isOn: $autoUploadEnabled)
                        .labelsHidden()
                        .onChange(of: autoUploadEnabled) { enabled in
                            updateAutoUploadSetting(enabled: enabled)
                        }
                }
            }
            
            // 上传结果信息
            if !uploadResult.isEmpty {
                HStack {
                    Text(uploadResult)
                        .font(.caption)
                        .foregroundColor(uploadResultColor)
                    
                    Spacer()
                    
                    if isUploading {
                        ProgressView()
                            .progressViewStyle(.circular)
                            .scaleEffect(0.7)
                    }
                }
            }
        }
        .padding(.horizontal)
        .onAppear {
            // 立即确保开关状态与服务状态一致
            DispatchQueue.main.async {
                self.autoUploadEnabled = RawDataUploadService.shared.isAutoUploadEnabled || UserDefaults.standard.bool(forKey: "auto_upload_enabled")
                
                // 如果开关显示为开启但服务未启动，则启动服务
                if self.autoUploadEnabled && !RawDataUploadService.shared.isAutoUploadEnabled {
                    updateAutoUploadSetting(enabled: true)
                }
            }
        }
    }
}

struct HeartRateDetailTestView_Previews: PreviewProvider {
    static var previews: some View {
        HeartRateDetailTestView()
    }
} 