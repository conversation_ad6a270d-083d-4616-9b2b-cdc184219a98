import SwiftUI

/// 通用睡眠数据区块视图组件
struct SleepDataSectionView<Content: View>: View {
    // MARK: - 属性
    var title: String
    var glossaryKey: GlossaryKey
    var icon: String
    var maxValue: String
    var avgValue: String
    var minValue: String
    var showScaleLabel: Bool
    var timeLabels: [String]
    var onTap: () -> Void
    var useCustomIcon: Bool
    var showInfoButton: Bool
    var onInfoButtonTap: () -> Void
    @ViewBuilder var chartContent: () -> Content
    
    // MARK: - 构造函数
    init(
        title: String,
        glossaryKey: GlossaryKey,
        icon: String,
        maxValue: String,
        avgValue: String,
        minValue: String,
        showScaleLabel: Bool = false,
        timeLabels: [String],
        useCustomIcon: Bool = false,
        showInfoButton: Bool = false,
        onTap: @escaping () -> Void,
        onInfoButtonTap: @escaping () -> Void = {},
        @ViewBuilder chartContent: @escaping () -> Content
    ) {
        self.title = title
        self.glossaryKey = glossaryKey
        self.icon = icon
        self.maxValue = maxValue
        self.avgValue = avgValue
        self.minValue = minValue
        self.showScaleLabel = showScaleLabel
        self.timeLabels = timeLabels
        self.useCustomIcon = useCustomIcon
        self.showInfoButton = showInfoButton
        self.onTap = onTap
        self.onInfoButtonTap = onInfoButtonTap
        self.chartContent = chartContent
    }
    
    // MARK: - 视图
    var body: some View {
//        Button(action: onTap) {
            VStack(spacing: 0) {
                // 上边界线
                Rectangle()
                    .fill(Color(red: 0.2, green: 0.224, blue: 0.302))
                    .frame(height: 0.3)
                    .frame(maxWidth: .infinity)
                
                // 内容区域
                VStack(alignment: .leading, spacing: 0) {
                    // 标题行
                    HStack {
                        HStack(spacing: 0) {
//                            if useCustomIcon {
                                Image(icon)
                                    .resizable()
                                    .frame(width: 18, height: 18)
                                    .foregroundColor(.blue)
                                    .padding(.trailing, 5)
//                            } else {
//                                Image(systemName: icon)
//                                    .foregroundColor(.blue)
//                                    .font(.system(size: 12))
//                                    .padding(.trailing, 5)
//                            }
                            
                            Text(title)
                                .font(.custom("PingFang-SC-Bold", size: 15))
                                .foregroundColor(.white)
                            
                            InfoButtonWithGlossaryPopup(showKey: glossaryKey.rawValue)
//                            if showInfoButton {
//                                Button(action: onInfoButtonTap) {
//                                    Image("疑问")
//                                        .resizable()
//                                        .frame(width: 12, height: 12)
//                                }
//                                .padding(.leading, 5)
//                            }
                        }
                        
                        Spacer()
                    }
                    .padding(.vertical, 8)
                    .padding(.horizontal, 10)
                    
                    // 下边界线
                    Rectangle()
                        .fill(Color(red: 0.2, green: 0.224, blue: 0.302))
                        .frame(height: 0.3)
                    
                    // 其余内容
                    VStack(alignment: .leading, spacing: 0) {
                        // 数据展示行
                        HStack(spacing: 10) {
                            // 最大值
                            metricCard(title: "activity_history_metric_max".localized, value: maxValue)
                                .frame(maxWidth: .infinity)
                            
                            // 平均值
                            metricCard(title: "activity_history_metric_ave".localized, value: avgValue)
                                .frame(maxWidth: .infinity)
                            
                            // 最小值
                            metricCard(title: "activity_history_metric_min".localized, value: minValue)
                                .frame(maxWidth: .infinity)
                        }
                        .padding(.top, 10)
                        .padding(.horizontal, 10)
                        
                        // 数值刻度（可选）
                        if showScaleLabel {
                            HStack {
                                Spacer()
                                // Text("50")
                                //     .font(.system(size: 10))
                                //     .foregroundColor(.gray)
                            }
                        }
                        
                        // 图表内容（由调用者提供）
                        chartContent()
                            .frame(height: 200)
                            .padding(.top, 5)
                        
                        // 时间标签
                        // HStack(spacing: 0) {
                        //    ForEach(timeLabels, id: \.self) { label in
                        //        Text(label)
                        //            .font(.system(size: 10))
                        //            .foregroundColor(.gray)
                        //            .frame(maxWidth: .infinity)
                        //    }
                        // }
                        // .padding(.top, 4)
                    }
                }
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        gradient: Gradient(
                            colors: [
                                Color(hex: "#0126401") //后面调整
                            ]
                        ),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            }
            .frame(maxWidth: .infinity)
            .background(
                LinearGradient(
                    gradient: Gradient(
                        colors: [
                            Color(hex: "#070708")
                        ]
                    ),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
//        }
//        .buttonStyle(DataSectionButtonStyle())
    }
    
    // MARK: - 辅助视图
    // 数据卡片
    private func metricCard(title: String, value: String) -> some View {
        // 分离数值和单位
        let components = value.split(separator: " ", maxSplits: 1).map(String.init)
        let valueText = components.first ?? value
        let unitText = components.count > 1 ? components[1] : ""
        
        return VStack(alignment: .center, spacing: 4) {
            Text(title)
                .font(.custom("PingFang-SC-Medium", size: 12))
                .foregroundColor(.white.opacity(0.6))
            
            HStack(alignment: .lastTextBaseline, spacing: 2) {
                Text(valueText)
                    .font(.custom("DIN-Bold", size: 18))
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text(unitText)
                    .font(.custom("PingFang-SC-Medium", size: 12))
                    .foregroundColor(.white)
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 60)
        .padding(7)
        .background(
            LinearGradient(
                gradient: Gradient(
                    colors: [
                        Color(red: 0.16, green: 0.19, blue: 0.25),
                        Color(red: 0.08, green: 0.09, blue: 0.13)
                    ]
                ),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(10)
    }
}

// 缩放按钮样式
struct DataSectionButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.98 : 1)
            .opacity(configuration.isPressed ? 0.9 : 1)
            .animation(.easeInOut(duration: 0.2), value: configuration.isPressed)
    }
}

// MARK: - 预览
struct SleepDataSectionView_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            SleepDataSectionView(
                title: "sleep_history_metric_score".localized, glossaryKey: .historySleepScore,
                icon: "questionmark.circle",
                maxValue: "47 bpm",
                avgValue: "43 bpm",
                minValue: "40 bpm",
                showScaleLabel: true,
                timeLabels: ["sleep_history_weekday_sun".localized, "sleep_history_weekday_mon".localized, "sleep_history_weekday_tue".localized, "sleep_history_weekday_wed".localized, "sleep_history_weekday_thu".localized, "sleep_history_weekday_fri".localized, "sleep_history_weekday_sat".localized],
                useCustomIcon: false,
                showInfoButton: true,
                onTap: { },
                onInfoButtonTap: { }
            ) {
                // 示例柱状图
                HStack(alignment: .bottom, spacing: 0) {
                    ForEach([42, 44, 46, 49, 47, 45, 43], id: \.self) { value in
                        Rectangle()
                            .fill(Color.blue.opacity(value == 49 ? 1.0 : 0.3))
                            .frame(width: 8, height: CGFloat(value) / 50 * 40)
                            .cornerRadius(2)
                            .frame(maxWidth: .infinity)
                    }
                }
                .frame(height: 50, alignment: .bottom)
            }
            .padding()
            .background(Color.black)
        }
        .background(Color.black)
        .previewLayout(.sizeThatFits)
    }
} 
 
