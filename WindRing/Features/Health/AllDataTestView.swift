import SwiftUI

/// 综合数据测试视图 - 提供各种健康数据测试功能
struct AllDataTestView: View {
    // MARK: - 服务
    @StateObject private var deviceService = WindRingDeviceService.shared
    @State private var selectedTab = 0
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 连接状态显示
                deviceStatusCard
                
                // 测试功能区
                VStack(spacing: 16) {
                    Text("数据测试功能")
                        .font(.headline)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // 活动数据测试
                    NavigationLink(destination: ActivityAPITestView()) {
                        testOptionCard(
                            title: "活动数据测试",
                            icon: "figure.walk.circle.fill",
                            color: .green,
                            description: "测试步数、距离、卡路里等活动数据"
                        )
                    }
                    
                    // 睡眠数据测试
                    NavigationLink(destination: SleepDataTestView()) {
                        testOptionCard(
                            title: "睡眠数据测试",
                            icon: "moon.zzz.fill",
                            color: .purple,
                            description: "测试基础和高级睡眠数据获取"
                        )
                    }
                    
                    // 数据同步测试
                    NavigationLink(destination: DataSyncTestView()) {
                        testOptionCard(
                            title: "数据同步测试",
                            icon: "arrow.triangle.2.circlepath.circle.fill",
                            color: .blue,
                            description: "测试健康数据同步和上传功能"
                        )
                    }
                    
                    // 压力数据测试
                    NavigationLink(destination: StressAPITestView()) {
                        testOptionCard(
                            title: "压力数据测试",
                            icon: "waveform.path.ecg.rectangle.fill",
                            color: .orange,
                            description: "测试压力数据获取与分析"
                        )
                    }
                    
                    // 数据导出功能
                    Button(action: {
                        // 这里可以实现导出所有测试数据的功能
                    }) {
                        testOptionCard(
                            title: "测试数据导出",
                            icon: "square.and.arrow.up.circle.fill",
                            color: .pink,
                            description: "导出所有测试数据用于分析"
                        )
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
            }
            .padding()
        }
        .navigationTitle("综合数据测试")
    }
    
    // MARK: - 设备状态卡片
    private var deviceStatusCard: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "ring")
                    .font(.title2)
                    .foregroundColor(deviceService.connectionState.isConnected ? .green : .gray)
                
                Text("设备状态")
                    .font(.headline)
                
                Spacer()
                
                Text(deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized)
                    .font(.subheadline)
                    .foregroundColor(deviceService.connectionState.isConnected ? .green : .red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        deviceService.connectionState.isConnected ? 
                            Color.green.opacity(0.2) : Color.red.opacity(0.2)
                    )
                    .cornerRadius(4)
            }
            
            if deviceService.connectionState.isConnected {
                Divider()
                
                HStack {
                    Text("设备名称")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(deviceService.deviceInfo?.localName ?? "未知设备")
                        .font(.subheadline)
                }
                
                HStack {
                    Text("电池电量")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Image(systemName: getBatteryIcon())
                            .foregroundColor(getBatteryColor())
                        
                        Text("\(deviceService.batteryLevel)%")
                            .font(.subheadline)
                    }
                }
            } else {
                Divider()
                
                Text("请先连接您的智能戒指设备")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 测试选项卡片
    private func testOptionCard(title: String, icon: String, color: Color, description: String) -> some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(color.opacity(0.2))
                .cornerRadius(8)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .foregroundColor(.gray)
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(10)
    }
    
    // MARK: - 辅助方法
    private func getBatteryIcon() -> String {
        let batteryLevel = deviceService.batteryLevel
        if batteryLevel <= 20 {
            return "battery.25"
        } else if batteryLevel <= 40 {
            return "battery.50"
        } else if batteryLevel <= 60 {
            return "battery.75"
        } else {
            return "battery.100"
        }
    }
    
    private func getBatteryColor() -> Color {
        let batteryLevel = deviceService.batteryLevel
        if batteryLevel <= 20 {
            return .red
        } else if batteryLevel <= 40 {
            return .orange
        } else {
            return .green
        }
    }
}

#Preview {
    AllDataTestView()
} 
