import SwiftUI

/// 健康标签页视图
struct HealthTabView: View {
    // MARK: - 属性
    @State private var selectedSubtab: HealthSubtab = .overview
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 健康子标签栏
                subtabBar
                
                // 主内容
                contentView
            }
            .navigationTitle("健康")
            .navigationBarTitleDisplayMode(.large)
            .background(Color.appBackground)
        }
    }
    
    // MARK: - 健康子标签栏
    private var subtabBar: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 20) {
                ForEach(HealthSubtab.allCases, id: \.self) { tab in
                    subtabButton(tab)
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
        }
        .background(Color.moduleBackground)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - 子标签按钮
    private func subtabButton(_ tab: HealthSubtab) -> some View {
        Button(action: {
            withAnimation {
                selectedSubtab = tab
            }
        }) {
            VStack(spacing: 6) {
                Text(tab.title)
                    .font(.headline)
                    .foregroundColor(selectedSubtab == tab ? .blue : .gray)
                
                // 选中指示器
                if selectedSubtab == tab {
                    Rectangle()
                        .fill(Color.blue)
                        .frame(width: 40, height: 3)
                        .cornerRadius(1.5)
                } else {
                    Rectangle()
                        .fill(Color.clear)
                        .frame(width: 40, height: 3)
                }
            }
        }
    }
    
    // MARK: - 内容视图
    private var contentView: some View {
        TabView(selection: $selectedSubtab) {
            // 健康概览
            Text("健康概览")
                .tag(HealthSubtab.overview)
            
            // 实时测量
            //RealtimeHealthView()
                //.tag(HealthSubtab.realtime)
            
            // 健康报告
            HealthReportHomeView()
                .tag(HealthSubtab.reports)
            
            // 活动
            Text("活动")
                .tag(HealthSubtab.activity)
            
            // 睡眠
            Text("睡眠")
                .tag(HealthSubtab.sleep)
            
            // 心率
            Text("心率")
                .tag(HealthSubtab.heartRate)
            
            // 血压
            Text("血压")
                .tag(HealthSubtab.bloodPressure)
        }
        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
    }
}

// MARK: - 健康子标签
enum HealthSubtab: String, CaseIterable {
    case overview
    case realtime
    case reports
    case activity
    case sleep
    case heartRate
    case bloodPressure
    
    var title: String {
        switch self {
        case .overview:
            return "概览"
        case .realtime:
            return "实时测量"
        case .reports:
            return "报告"
        case .activity:
            return "活动"
        case .sleep:
            return "睡眠"
        case .heartRate:
            return "心率"
        case .bloodPressure:
            return "血压"
        }
    }
}

// MARK: - 预览
struct HealthTabView_Previews: PreviewProvider {
    static var previews: some View {
        HealthTabView()
    }
} 
