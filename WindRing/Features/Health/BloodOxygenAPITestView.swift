import SwiftUI
import Combine
import CRPSmartRing

/// 血氧API测试视图模型
class BloodOxygenAPITestViewModel: NSObject, ObservableObject {
    // MARK: - 属性
    @Published var results: [BOTestResult] = []
    @Published var isLoading: Bool = false
    @Published var bloodOxygen: Int = 0
    @Published var isMeasuringBloodOxygen: Bool = false
    @Published var selectedDay: Int = 0
    @Published var showShareSheet: Bool = false
    @Published var shareText: String = ""
    @Published var receivedNotification: Bool = false
    @Published var bloodOxygenInterval: Int = 0
    @Published var sleepBloodOxygenSupported: Bool? = nil
    @Published var fullDayData: [Int] = []
    
    // 设备服务
    let deviceService = WindRingDeviceService.shared
    
    // 通知中心
    private var cancellables = Set<AnyCancellable>()
    
    // 单例
    static let shared = BloodOxygenAPITestViewModel()
    
    // MARK: - 初始化方法
    override init() {
        super.init()
        // 设置通知监听
        setupNotifications()
    }
    
    // 设置通知监听
    private func setupNotifications() {
        // 监听血氧测量通知
        NotificationCenter.default.publisher(for: .bloodOxygenMeasured)
            .sink { [weak self] notification in
                print("【调试】收到血氧通知: \(notification)")
                
                if let value = notification.userInfo?["value"] as? Int {
                    print("【调试】从userInfo中获取血氧值: \(value)%")
                    DispatchQueue.main.async {
                        self?.handleBloodOxygenUpdate(value)
                    }
                } else if let value = notification.object as? Int {
                    print("【调试】从object中获取血氧值: \(value)%")
                    DispatchQueue.main.async {
                        self?.handleBloodOxygenUpdate(value)
                    }
                } else {
                    print("【调试】收到血氧通知但无法获取血氧值: userInfo=\(String(describing: notification.userInfo)), object=\(String(describing: notification.object))")
                }
            }
            .store(in: &cancellables)
            
        print("【调试】已设置血氧通知监听")
    }
    
    // 处理血氧更新
    private func handleBloodOxygenUpdate(_ value: Int) {
        self.isLoading = false
        self.isMeasuringBloodOxygen = false
        self.bloodOxygen = value
        self.receivedNotification = true
        
        if value == 0 || value == 255 {
            addResult("血氧测量已中断", color: .orange)
        } else {
            addResult("接收到血氧测量结果: \(value)%", color: .green)
        }
    }
    
    // MARK: - 结果管理方法
    
    /// 添加测试结果
    func addResult(_ message: String, color: Color = .primary) {
        let result = BOTestResult(message: message, color: color)
        DispatchQueue.main.async {
            self.results.insert(result, at: 0)
        }
    }
    
    /// 清除结果
    func clearResults() {
        DispatchQueue.main.async {
            self.results.removeAll()
        }
    }
    
    /// 导出测试结果
    func exportResults() {
        var text = "血氧API测试结果\n"
        text += "测试时间: \(Date())\n"
        text += "设备连接状态: \(deviceService.connectionState.description)\n\n"
        text += "测试记录:\n"
        
        for (index, result) in results.enumerated() {
            text += "\(index + 1). \(result.message)\n"
        }
        
        shareText = text
        showShareSheet = true
    }
    
    // MARK: - 血氧测量方法
    
    /// 开始单次血氧测量
    func startSingleBloodOxygenMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法进行血氧测量", color: .red)
            return
        }
        
        clearResults()
        addResult("开始单次血氧测量...", color: .blue)
        isLoading = true
        isMeasuringBloodOxygen = true
        receivedNotification = false
        
        print("【调试】发送开始血氧测量请求")
        CRPSmartRingSDK.sharedInstance.setStartSpO2()
        
        // 设置超时检查，因为血氧测量可能耗时较长
        DispatchQueue.main.asyncAfter(deadline: .now() + 60) { [weak self] in
            if self?.isLoading == true && self?.receivedNotification == false {
                self?.isLoading = false
                self?.isMeasuringBloodOxygen = false
                self?.addResult("血氧测量超时，请重试", color: .red)
            }
        }
    }
    
    /// 停止单次血氧测量
    func stopSingleBloodOxygenMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法停止血氧测量", color: .red)
            return
        }
        
        addResult("停止血氧测量...", color: .blue)
        CRPSmartRingSDK.sharedInstance.setStopSpO2()
        isMeasuringBloodOxygen = false
        isLoading = false
    }
    
    /// 获取血氧历史记录
    func getBloodOxygenHistory() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取血氧历史", color: .red)
            return
        }
        
        addResult("正在获取血氧历史记录...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getO2RecordData { [weak self] records, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    if records.isEmpty {
                        self?.addResult("未找到血氧历史记录", color: .orange)
                    } else {
                        let dateFormatter = DateFormatter()
                        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                        
                        self?.addResult("获取到\(records.count)条血氧记录:", color: .green)
                        
                        // 显示最近5条记录
                        for (index, record) in records.prefix(5).enumerated() {
                            let date = Date(timeIntervalSince1970: TimeInterval(record.time))
                            self?.addResult("记录\(index + 1): 血氧值 \(record.value)%, 时间: \(dateFormatter.string(from: date))")
                        }
                    }
                } else {
                    self?.addResult("获取血氧历史记录失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 获取定时血氧测量状态
    func getBloodOxygenInterval() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取定时血氧状态", color: .red)
            return
        }
        
        addResult("正在获取定时血氧测量状态...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getTimingO2Interval { [weak self] interval, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    self?.bloodOxygenInterval = interval
                    
                    if interval == 0 {
                        self?.addResult("定时血氧测量已关闭", color: .orange)
                    } else {
                        self?.addResult("定时血氧测量已开启，间隔为\(interval * 5)分钟", color: .green)
                    }
                } else {
                    self?.addResult("获取定时血氧测量状态失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 设置定时血氧测量
    func setBloodOxygenInterval(_ interval: Int) {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法设置定时血氧", color: .red)
            return
        }
        
        let minutes = interval * 5
        addResult("正在设置定时血氧测量，间隔为\(minutes)分钟...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.setTimingO2(interval)
        
        // 由于设置命令没有回调，我们等待一段时间后再获取当前状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [weak self] in
            self?.isLoading = false
            if interval == 0 {
                self?.addResult("已关闭定时血氧测量", color: .orange)
            } else {
                self?.addResult("定时血氧测量设置成功，间隔为\(minutes)分钟", color: .green)
            }
            
            // 更新当前状态
            self?.getBloodOxygenInterval()
        }
    }
    
    /// 检查睡眠血氧支持状态
    func checkSleepBloodOxygenSupport() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法检查睡眠血氧支持状态", color: .red)
            return
        }
        
        addResult("正在检查睡眠血氧支持状态...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getSleepO2SupportState { [weak self] state, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    let supported = state == 1
                    self?.sleepBloodOxygenSupported = supported
                    
                    if supported {
                        self?.addResult("设备支持睡眠血氧测量", color: .green)
                    } else {
                        self?.addResult("设备不支持睡眠血氧测量", color: .orange)
                    }
                } else {
                    self?.addResult("检查睡眠血氧支持状态失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 获取全天血氧数据
    func getFullDayBloodOxygenData() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取全天血氧数据", color: .red)
            return
        }
        
        addResult("正在获取第\(selectedDay)天的血氧数据...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getTimingO2(selectedDay) { [weak self] model, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    let o2s = model.o2s
                    self?.fullDayData = o2s
                    
                    // 计算有效数据点数量（排除0值）
                    let validDataPoints = o2s.filter { $0 > 0 }
                    
                    if validDataPoints.isEmpty {
                        self?.addResult("第\(self?.selectedDay ?? 0)天没有血氧数据记录", color: .orange)
                    } else {
                        // 计算平均值、最高值和最低值
                        let avgO2 = validDataPoints.reduce(0, +) / validDataPoints.count
                        let minO2 = validDataPoints.min() ?? 0
                        let maxO2 = validDataPoints.max() ?? 0
                        
                        self?.addResult("获取到第\(self?.selectedDay ?? 0)天的血氧数据：共\(validDataPoints.count)条记录", color: .green)
                        self?.addResult("最低血氧: \(minO2)%", color: .blue)
                        self?.addResult("最高血氧: \(maxO2)%", color: .blue)
                        self?.addResult("平均血氧: \(avgO2)%", color: .blue)
                        
                        // 显示部分时间点的数据
                        self?.addResult("数据示例（每5分钟一个点）:", color: .gray)
                        
                        for hour in 0..<24 where hour % 6 == 0 {
                            let index = hour * 12 // 每小时12个数据点（5分钟一个）
                            let time = String(format: "%02d:00", hour)
                            let value = o2s[index]
                            
                            if value > 0 {
                                self?.addResult("\(time): \(value)%")
                            }
                        }
                    }
                } else {
                    self?.addResult("获取全天血氧数据失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 测试通知处理
    func testBloodOxygenNotification() {
        let value = 98 // 模拟正常血氧值
        
        // 构建通知
        let userInfo = ["value": value]
        NotificationCenter.default.post(
            name: .bloodOxygenMeasured,
            object: nil,
            userInfo: userInfo
        )
        
        addResult("已发送测试血氧通知: \(value)%", color: .blue)
    }
    
    /// 解释血氧值
    func interpretBloodOxygen(_ value: Int) -> (String, Color) {
        if value == 0 {
            return ("无数据", .gray)
        }
        
        // 血氧正常值范围通常是95-100%
        if value >= 95 && value <= 100 {
            return ("正常", .green)
        }
        // 轻度缺氧 90-94%
        else if value >= 90 && value < 95 {
            return ("轻度缺氧", .yellow)
        }
        // 中度缺氧 85-89%
        else if value >= 85 && value < 90 {
            return ("中度缺氧", .orange)
        }
        // 重度缺氧 < 85%
        else if value < 85 {
            return ("重度缺氧", .red)
        }
        // 超过100%是不正常的
        else if value > 100 {
            return ("数值异常", .red)
        }
        
        return ("未知", .gray)
    }
}

// MARK: - 测试结果数据模型
struct BOTestResult: Identifiable {
    let id = UUID()
    let message: String
    let color: Color
    let timestamp = Date()
}

// MARK: - 血氧API测试视图
struct BloodOxygenAPITestView: View {
    @StateObject private var viewModel = BloodOxygenAPITestViewModel.shared
    @State private var selectedIntervalIndex = 0
    
    // 定时测量间隔选项
    private let intervalOptions = [
        (label: "关闭", value: 0),
        (label: "5分钟", value: 1),
        (label: "10分钟", value: 2),
        (label: "15分钟", value: 3),
        (label: "30分钟", value: 6),
        (label: "1小时", value: 12)
    ]
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 血氧显示区域
                VStack(spacing: 16) {
                    Text("血氧测量")
                        .font(.headline)
                    
                    Text("\(viewModel.bloodOxygen)%")
                        .font(.system(size: 48, weight: .bold))
                        .foregroundColor(.blue)
                        .padding()
                    
                    // 血氧状态解释
                    if viewModel.bloodOxygen > 0 {
                        let (interpretText, interpretColor) = viewModel.interpretBloodOxygen(viewModel.bloodOxygen)
                        
                        Text("血氧状态: \(interpretText)")
                            .font(.headline)
                            .foregroundColor(interpretColor)
                            .padding(.vertical, 4)
                    }
                    
                    // 控制按钮
                    HStack(spacing: 16) {
                        Button(action: {
                            viewModel.startSingleBloodOxygenMeasurement()
                        }) {
                            Text("开始测量")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                        .disabled(viewModel.isMeasuringBloodOxygen || viewModel.isLoading)
                        
                        Button(action: {
                            viewModel.stopSingleBloodOxygenMeasurement()
                        }) {
                            Text("停止测量")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(viewModel.isMeasuringBloodOxygen ? Color.red : Color.gray)
                                .cornerRadius(10)
                        }
                        .disabled(!viewModel.isMeasuringBloodOxygen)
                    }
                    
                    Button(action: {
                        viewModel.getBloodOxygenHistory()
                    }) {
                        Text("获取血氧历史记录")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .cornerRadius(10)
                    }
                    .disabled(viewModel.isLoading)
                    
                    // 测试通知按钮（用于调试）
                    Button(action: {
                        viewModel.testBloodOxygenNotification()
                    }) {
                        HStack {
                            Image(systemName: "bell.fill")
                            Text("测试通知接收")
                        }
                        .font(.footnote)
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.purple)
                        .cornerRadius(10)
                    }
                }
                .padding()
                .background(Color.moduleBackground)
                .cornerRadius(15)
                
                // 定时血氧设置
                VStack(spacing: 16) {
                    Text("定时血氧设置")
                        .font(.headline)
                    
                    HStack {
                        Text("当前状态:")
                        Spacer()
                        Button(action: {
                            viewModel.getBloodOxygenInterval()
                        }) {
                            Image(systemName: "arrow.clockwise")
                                .foregroundColor(.blue)
                        }
                    }
                    
                    if viewModel.bloodOxygenInterval == 0 {
                        Text("已关闭")
                            .foregroundColor(.orange)
                            .padding(.vertical, 4)
                    } else {
                        Text("每\(viewModel.bloodOxygenInterval * 5)分钟测量一次")
                            .foregroundColor(.green)
                            .padding(.vertical, 4)
                    }
                    
                    Divider()
                    
                    VStack(alignment: .leading) {
                        Text("设置测量间隔:")
                            .font(.subheadline)
                        
                        Picker("间隔", selection: $selectedIntervalIndex) {
                            ForEach(0..<intervalOptions.count, id: \.self) { index in
                                Text(intervalOptions[index].label).tag(index)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .padding(.vertical, 4)
                        
                        Button(action: {
                            let selectedInterval = intervalOptions[selectedIntervalIndex].value
                            viewModel.setBloodOxygenInterval(selectedInterval)
                        }) {
                            Text("应用设置")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                        .disabled(viewModel.isLoading)
                    }
                }
                .padding()
                .background(Color.moduleBackground)
                .cornerRadius(15)
                
                // 全天血氧数据
                VStack(spacing: 16) {
                    Text("血氧历史数据")
                        .font(.headline)
                    
                    Picker(selection: $viewModel.selectedDay, label: Text("选择日期")) {
                        Text("今天").tag(0)
                        Text("昨天").tag(1)
                        Text("前天").tag(2)
                        Text("大前天").tag(3)
                        Text("4天前").tag(4)
                        Text("5天前").tag(5)
                        Text("6天前").tag(6)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .padding(.vertical)
                    
                    Button(action: {
                        viewModel.getFullDayBloodOxygenData()
                    }) {
                        Text("获取全天血氧数据")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                    .disabled(viewModel.isLoading)
                    
                    Button(action: {
                        viewModel.checkSleepBloodOxygenSupport()
                    }) {
                        Text("检查睡眠血氧支持")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(Color.orange)
                            .cornerRadius(10)
                    }
                    .disabled(viewModel.isLoading)
                    
                    if let supported = viewModel.sleepBloodOxygenSupported {
                        HStack {
                            Image(systemName: supported ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(supported ? .green : .orange)
                            Text(supported ? "设备支持睡眠血氧测量" : "设备不支持睡眠血氧测量")
                                .foregroundColor(supported ? .green : .orange)
                        }
                        .padding(.vertical, 4)
                    }
                }
                .padding()
                .background(Color.moduleBackground)
                .cornerRadius(15)
                
                // 测试结果区域
                VStack(alignment: .leading) {
                    HStack {
                        Text("测试结果")
                            .font(.headline)
                        
                        Spacer()
                        
                        Button(action: {
                            viewModel.clearResults()
                        }) {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                        }
                        
                        Button(action: {
                            viewModel.exportResults()
                        }) {
                            Image(systemName: "square.and.arrow.up")
                                .foregroundColor(.blue)
                        }
                    }
                    
                    Divider()
                    
                    if viewModel.results.isEmpty {
                        Text("暂无测试结果")
                            .foregroundColor(.gray)
                            .padding()
                            .frame(maxWidth: .infinity)
                    } else {
                        ForEach(viewModel.results) { result in
                            VStack(alignment: .leading, spacing: 4) {
                                Text(result.message)
                                    .foregroundColor(result.color)
                                
                                Text(result.timestamp, style: .time)
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                            .padding(.vertical, 4)
                            
                            Divider()
                        }
                    }
                }
                .padding()
                .background(Color.moduleBackground)
                .cornerRadius(15)
                
                // 说明区域
                VStack(alignment: .leading, spacing: 8) {
                    Text("血氧测量说明")
                        .font(.headline)
                    
                    Divider()
                    
                    Text("• 血氧测量需要保持戒指佩戴稳定")
                        .font(.caption)
                    
                    Text("• 血氧饱和度正常范围为95%-100%")
                        .font(.caption)
                    
                    Text("• 低于90%可能表示缺氧状况")
                        .font(.caption)
                    
                    Text("• 血氧数值仅供参考，如有异常请咨询医生")
                        .font(.caption)
                        .foregroundColor(.red)
                }
                .padding()
                .background(Color.moduleBackground)
                .cornerRadius(15)
            }
            .padding()
        }
        .navigationTitle("血氧API测试")
        .overlay(
            ZStack {
                if viewModel.isLoading {
                    Color.black.opacity(0.3)
                        .edgesIgnoringSafeArea(.all)
                    
                    VStack {
                        ProgressView()
                            .scaleEffect(1.5)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        
                        Text(viewModel.isMeasuringBloodOxygen ? "血氧测量中..." : "加载中...")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.top, 10)
                    }
                    .padding(20)
                    .background(Color.black.opacity(0.7))
                    .cornerRadius(10)
                }
            }
        )
        .sheet(isPresented: $viewModel.showShareSheet) {
            BloodOxygenActivityView(text: viewModel.shareText)
        }
        .onAppear {
            // 页面加载时获取当前的定时血氧状态
            if viewModel.deviceService.connectionState.isConnected {
                viewModel.getBloodOxygenInterval()
            }
        }
    }
}

// MARK: - 活动视图（用于分享）
struct BloodOxygenActivityView: UIViewControllerRepresentable {
    let text: String
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: [text],
            applicationActivities: nil
        )
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - 预览
struct BloodOxygenAPITestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            BloodOxygenAPITestView()
        }
    }
} 