import SwiftUI
import Combine
import CRPSmartRing

/// 体温API测试视图模型
class TemperatureAPITestViewModel: NSObject, ObservableObject {
    // MARK: - 属性
    @Published var results: [TemperatureTestResult] = []
    @Published var isLoading: Bool = false
    @Published var temperature: Double = 0.0
    @Published var isMeasuringTemperature: Bool = false
    @Published var selectedDay: Int = 0
    @Published var showShareSheet: Bool = false
    @Published var shareText: String = ""
    
    // 设备服务
    let deviceService = WindRingDeviceService.shared
    
    // 通知中心
    private var cancellables = Set<AnyCancellable>()
    
    // 单例
    static let shared = TemperatureAPITestViewModel()
    
    // MARK: - 初始化方法
    override init() {
        super.init()
        // 监听体温变化的通知
        setupNotifications()
        
        // 从设备服务获取最新体温值
        self.temperature = deviceService.lastTemperature
    }
    
    // 设置通知监听
    private func setupNotifications() {
        // 监听体温测量通知
        NotificationCenter.default.publisher(for: .temperatureMeasured)
            .sink { [weak self] notification in
                print("【调试】收到体温通知: \(notification)")
                
                if let temperature = notification.userInfo?["value"] as? Double {
                    print("【调试】从userInfo中获取体温值: \(temperature)°C")
                    DispatchQueue.main.async {
                        self?.handleTemperatureUpdate(temperature)
                    }
                } else {
                    print("【调试】收到体温通知但无法获取体温值: userInfo=\(String(describing: notification.userInfo))")
                }
            }
            .store(in: &cancellables)
            
        print("【调试】已设置体温通知监听")
    }
    
    // 处理体温更新
    private func handleTemperatureUpdate(_ temperature: Double) {
        self.isLoading = false
        self.isMeasuringTemperature = false
        self.temperature = temperature
        
        // 同时更新设备服务中的值（以防万一）
        if temperature > 0 && self.deviceService.lastTemperature != temperature {
            self.deviceService.lastTemperature = temperature
        }
        
        if temperature <= 0 {
            addResult("体温测量已中断", color: .orange)
        } else {
            addResult("接收到体温测量结果: \(String(format: "%.1f", temperature))°C", color: .green)
        }
        
        print("【调试】体温更新为: \(temperature)°C")
    }
    
    // MARK: - 结果管理方法
    
    /// 添加测试结果
    func addResult(_ message: String, color: Color = .primary) {
        let result = TemperatureTestResult(message: message, color: color)
        DispatchQueue.main.async {
            self.results.append(result)
        }
    }
    
    /// 清除结果
    func clearResults() {
        DispatchQueue.main.async {
            self.results.removeAll()
        }
    }
    
    /// 导出测试结果
    func exportResults() {
        var text = "体温API测试结果\n"
        text += "测试时间: \(Date())\n"
        text += "设备连接状态: \(deviceService.connectionState.description)\n\n"
        text += "测试记录:\n"
        
        for (index, result) in results.enumerated() {
            text += "\(index + 1). \(result.message)\n"
        }
        
        shareText = text
        showShareSheet = true
    }
    
    // MARK: - 体温测量方法
    
    /// 开始体温测量
    func startTemperatureMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法进行体温测量", color: .red)
            return
        }
        
        clearResults()
        addResult("开始体温测量...", color: .blue)
        isLoading = true
        isMeasuringTemperature = true
        
        print("【调试】发送开始体温测量请求")
        deviceService.startTemperatureMeasurement()
    }
    
    /// 停止体温测量
    func stopTemperatureMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法停止体温测量", color: .red)
            return
        }
        
        addResult("停止体温测量...", color: .blue)
        deviceService.stopTemperatureMeasurement()
        isMeasuringTemperature = false
        isLoading = false
        
        // 确保获取最新的体温值
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.temperature = self?.deviceService.lastTemperature ?? 0.0
            print("【调试】停止测量后更新体温值: \(self?.temperature ?? 0.0)°C")
        }
    }
    
    /// 获取睡眠体温状态
    func getSleepTemperatureState() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取睡眠体温状态", color: .red)
            return
        }
        
        clearResults()
        addResult("获取睡眠体温监测状态...", color: .blue)
        isLoading = true
//        CRPSmartRingSDK.sharedInstance.gette
//        CRPSmartRingSDK.sharedInstance.getSleepTemperatureState { [weak self] isEnabled, error in
//            DispatchQueue.main.async {
//                self?.isLoading = false
//                
//                if error == .none {
//                    if isEnabled {
//                        self?.addResult("睡眠体温监测已启用", color: .green)
//                    } else {
//                        self?.addResult("睡眠体温监测未启用", color: .orange)
//                    }
//                } else {
//                    self?.addResult("获取睡眠体温监测状态失败: \(error)", color: .red)
//                }
//            }
//        }
    }
    
    /// 设置睡眠体温状态
    func setSleepTemperatureState(open: Bool) {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法设置睡眠体温状态", color: .red)
            return
        }
        
        clearResults()
        addResult("\(open ? "开启" : "关闭")睡眠体温监测...", color: .blue)
        isLoading = true
        
//        CRPSmartRingSDK.sharedInstance.setSleepTemperatureState(open: open)
        
        // 延迟一小段时间再获取状态，确保设置已生效
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.getSleepTemperatureState()
        }
    }
    
    /// 获取睡眠体温数据
    func getSleepTemperatureData(day: Int) {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取睡眠体温数据", color: .red)
            return
        }
        
        clearResults()
        addResult("获取第\(day)天的睡眠体温数据...", color: .blue)
        isLoading = true
        
//        CRPSmartRingSDK.sharedInstance.getSleepTemperatureData(day: day) { [weak self] model, error in
//            DispatchQueue.main.async {
//                self?.isLoading = false
//                
//                if error == .none {
//                    self?.addResult("成功获取睡眠体温数据", color: .green)
//                    
//                    if !model.tempeartures.isEmpty {
//                        // 统计基本信息
//                        let validTemps = model.tempeartures.filter { $0 > 0 }
//                        let count = validTemps.count
//                        
//                        if count == 0 {
//                            self?.addResult("没有有效的体温数据", color: .orange)
//                            return
//                        }
//                        
//                        let minTemp = validTemps.min() ?? 0
//                        let maxTemp = validTemps.max() ?? 0
//                        let avgTemp = validTemps.reduce(0, +) / Double(count)
//                        
//                        self?.addResult("有效体温数据点: \(count)", color: .blue)
//                        self?.addResult("最低体温: \(String(format: "%.1f", minTemp))°C", color: .blue)
//                        self?.addResult("最高体温: \(String(format: "%.1f", maxTemp))°C", color: .blue)
//                        self?.addResult("平均体温: \(String(format: "%.1f", avgTemp))°C", color: .blue)
//                        
//                        // 显示部分数据点
//                        self?.addResult("体温数据示例:", color: .gray)
//                        let sampleCount = min(5, model.tempeartures.count)
//                        for i in 0..<sampleCount {
//                            let value = model.tempeartures[i]
//                            if value > 0 {
//                                self?.addResult("样本\(i+1): \(String(format: "%.1f", value))°C")
//                            }
//                        }
//                        
//                        if model.tempeartures.count > 5 {
//                            self?.addResult("... 更多数据未显示 ...", color: .gray)
//                        }
//                    } else {
//                        self?.addResult("没有睡眠体温数据", color: .orange)
//                    }
//                } else {
//                    self?.addResult("获取睡眠体温数据失败: \(error)", color: .red)
//                }
//            }
//        }
    }
}

/// 测试结果模型
struct TemperatureTestResult: Identifiable {
    let id = UUID()
    let message: String
    let color: Color
    let timestamp = Date()
}

/// 体温API测试视图
struct TemperatureAPITestView: View {
    // MARK: - 属性
    @StateObject private var viewModel = TemperatureAPITestViewModel.shared
    @Environment(\.colorScheme) private var colorScheme
    
    // MARK: - 主视图
    var body: some View {
        List {
            // 当前体温显示
            temperatureSection
            
            // 体温测量
            temperatureMeasurementSection
            
            // 睡眠体温
            sleepTemperatureSection
            
            // 测试结果
            resultSection
        }
        .navigationTitle("体温API测试")
        .navigationBarItems(trailing: exportButton)
        .sheet(isPresented: $viewModel.showShareSheet) {
            TemperatureActivityViewController(activityItems: [viewModel.shareText])
        }
    }
    
    // MARK: - 视图组件
    
    /// 体温显示部分
    private var temperatureSection: some View {
        Section(header: Text("当前体温")) {
            HStack {
                Spacer()
                VStack {
                    Image(systemName: "thermometer")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)
                        .padding(.bottom, 5)
                    
                    Text(viewModel.temperature > 0 ? String(format: "%.1f", viewModel.temperature) : "0.0")
                        .font(.system(size: 48, weight: .bold))
                    
                    Text("°C")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        
                    Text("通知状态: \(viewModel.temperature > 0 ? "已接收" : "未接收")")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                Spacer()
            }
            .padding(.vertical, 20)
            
            // 调试按钮
            Button(action: {
                print("【调试】通知中心测试开始")
                // 使用与WindRingDeviceService一致的方式发送通知
                NotificationCenter.default.post(
                    name: .temperatureMeasured,
                    object: nil,
                    userInfo: ["value": 36.5]
                )
                print("【调试】已发送测试体温通知，值为36.5°C")
            }) {
                Text("测试通知 (设置体温为36.5°C)")
                    .font(.footnote)
                    .foregroundColor(.gray)
            }
        }
    }
    
    /// 体温测量部分
    private var temperatureMeasurementSection: some View {
        Section(header: Text("体温测量")) {
            if viewModel.isMeasuringTemperature {
                Button(action: {
                    viewModel.stopTemperatureMeasurement()
                }) {
                    HStack {
                        Spacer()
                        if viewModel.isLoading {
                            ProgressView()
                                .padding(.trailing, 10)
                        }
                        Text("停止测量")
                            .fontWeight(.semibold)
                            .foregroundColor(.red)
                        Spacer()
                    }
                }
                .padding(.vertical, 8)
            } else {
                Button(action: {
                    viewModel.startTemperatureMeasurement()
                }) {
                    HStack {
                        Spacer()
                        Text("开始测量")
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                        Spacer()
                    }
                }
                .padding(.vertical, 8)
            }
        }
    }
    
    /// 睡眠体温部分
    private var sleepTemperatureSection: some View {
        Section(header: Text("睡眠体温")) {
            Button(action: {
                viewModel.getSleepTemperatureState()
            }) {
                HStack {
                    Spacer()
                    Text("获取睡眠体温监测状态")
                        .fontWeight(.semibold)
                    Spacer()
                }
            }
            .padding(.vertical, 8)
            
            HStack {
                Spacer()
                Button(action: {
                    viewModel.setSleepTemperatureState(open: true)
                }) {
                    Text("开启睡眠体温监测")
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                }
                Spacer()
                Button(action: {
                    viewModel.setSleepTemperatureState(open: false)
                }) {
                    Text("关闭睡眠体温监测")
                        .fontWeight(.semibold)
                        .foregroundColor(.red)
                }
                Spacer()
            }
            .padding(.vertical, 8)
            
            HStack {
                Text("选择日期:")
                Picker("", selection: $viewModel.selectedDay) {
                    Text("今天").tag(0)
                    Text("昨天").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            Button(action: {
                viewModel.getSleepTemperatureData(day: viewModel.selectedDay)
            }) {
                HStack {
                    Spacer()
                    Text("获取睡眠体温数据")
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    Spacer()
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    /// 测试结果部分
    private var resultSection: some View {
        Section(header: Text("测试结果")) {
            if viewModel.isLoading {
                HStack {
                    Spacer()
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                    Text("加载中...")
                        .padding(.leading, 10)
                    Spacer()
                }
                .padding(.vertical, 10)
            }
            
            if viewModel.results.isEmpty {
                Text("尚未执行任何测试")
                    .foregroundColor(.gray)
                    .italic()
                    .padding(.vertical, 8)
            } else {
                ForEach(viewModel.results) { result in
                    Text(result.message)
                        .foregroundColor(result.color)
                        .padding(.vertical, 2)
                }
                
                Button(action: {
                    viewModel.clearResults()
                }) {
                    HStack {
                        Spacer()
                        Text("清除结果")
                            .foregroundColor(.red)
                        Spacer()
                    }
                }
                .padding(.top, 8)
            }
        }
    }
    
    /// 导出按钮
    private var exportButton: some View {
        Button(action: {
            viewModel.exportResults()
        }) {
            Image(systemName: "square.and.arrow.up")
        }
        .disabled(viewModel.results.isEmpty)
    }
}

// MARK: - ActivityViewController
struct TemperatureActivityViewController: UIViewControllerRepresentable {
    var activityItems: [Any]
    var applicationActivities: [UIActivity]? = nil
    
    func makeUIViewController(context: UIViewControllerRepresentableContext<TemperatureActivityViewController>) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: applicationActivities)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: UIViewControllerRepresentableContext<TemperatureActivityViewController>) {}
}

// MARK: - 预览
struct TemperatureAPITestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            TemperatureAPITestView()
        }
    }
} 
