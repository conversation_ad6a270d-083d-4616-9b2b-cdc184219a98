import SwiftUI
import Charts
import CRPSmartRing

/// HRV明细测试视图
struct HRVDetailTestView: View {
    @State private var hrvDetails: [HRVData] = []
    @State private var isLoading = false
    @State private var selectedDay = 0
    @State private var message = "请点击按钮获取HRV明细数据"
    @State private var messageColor: Color = .gray
    @State private var averageHRV = 0
    @State private var maxHRV = 0
    @State private var minHRV = 0
    @State private var showHRVData = false
    @State private var isUploading = false
    @State private var uploadResult = ""
    @State private var uploadResultColor: Color = .gray
    
    // 自动上传开关
    @State private var autoUploadEnabled: Bool = true
    
    // 日期选项
    let dayOptions = ["今天", "昨天", "前天", "三天前", "四天前", "五天前", "六天前"]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 状态消息
                    Text(message)
                        .font(.headline)
                        .foregroundColor(messageColor)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                    
                    // 日期选择器和刷新按钮
                    HStack {
                        Picker("选择日期", selection: $selectedDay) {
                            ForEach(0..<dayOptions.count) { index in
                                Text(dayOptions[index]).tag(index)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .padding(.horizontal)
                        
                        Button(action: fetchHRVDetails) {
                            Image(systemName: "arrow.clockwise")
                                .font(.title2)
                        }
                        .disabled(isLoading)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                    
                    // 自动上传开关
                    Toggle("自动上传数据", isOn: $autoUploadEnabled)
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                        .onChange(of: autoUploadEnabled) { newValue in
                            updateAutoUploadSetting(enabled: newValue)
                        }
                    
                    // HRV数据显示
                    if showHRVData {
                        hrvDataSection
                        
                        // HRV图表区域
                        hrvChartSection
                        
                        // HRV列表区域
                        hrvListSection
                    }
                    
                    // 上传按钮和状态
                    VStack {
                        Button(action: uploadHRVData) {
                            Text("手动上传HRV数据")
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                        .disabled(isLoading || isUploading || hrvDetails.isEmpty)
                        
                        if !uploadResult.isEmpty {
                            Text(uploadResult)
                                .font(.subheadline)
                                .foregroundColor(uploadResultColor)
                                .padding(.top, 5)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                    
                    // 加载指示器
                    if isLoading || isUploading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                            .padding()
                    }
                }
                .padding()
            }
            .navigationTitle("HRV数据测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        Text("HRV记录: \(hrvDetails.count)")
                            .font(.footnote)
                            .foregroundColor(.gray)
                        
                        if !hrvDetails.isEmpty {
                            // 显示自动上传标志
                            Image(systemName: "arrow.triangle.2.circlepath.circle.fill")
                                .foregroundColor(.green)
                        }
                        
                        // 添加验证按钮
                        Button(action: checkUploadStatus) {
                            Image(systemName: "checkmark.shield")
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
            .onAppear {
                // 刷新自动上传状态，如果服务还未启用自动上传则主动启用
                autoUploadEnabled = RawDataUploadService.shared.isAutoUploadEnabled
                
                // 如果连接已就绪但尚未启用自动上传，则自动启用
                if !autoUploadEnabled && WindRingDeviceService.shared.connectionState.isConnected {
                    updateAutoUploadSetting(enabled: true)
                }
                
                // 自动获取最新数据
                fetchHRVDetails()
            }
        }
    }
    
    // MARK: - HRV数据区域
    private var hrvDataSection: some View {
        VStack {
            if hrvDetails.isEmpty {
                Text("无HRV数据")
                    .font(.title2)
                    .foregroundColor(.gray)
                    .padding()
                    .frame(maxWidth: .infinity)
            } else {
                VStack(spacing: 15) {
                    Text("HRV统计")
                        .font(.headline)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    HStack(spacing: 15) {
                        // 平均HRV卡片
                        hrvDataCard(
                            title: "平均HRV",
                            value: "\(averageHRV)",
                            icon: "waveform.path.ecg",
                            color: .purple
                        )
                        
                        // 最高HRV卡片
                        hrvDataCard(
                            title: "最高HRV",
                            value: "\(maxHRV)",
                            icon: "arrow.up.heart",
                            color: .green
                        )
                    }
                    
                    HStack(spacing: 15) {
                        // 最低HRV卡片
                        hrvDataCard(
                            title: "最低HRV",
                            value: "\(minHRV)",
                            icon: "arrow.down.heart",
                            color: .orange
                        )
                        
                        // 记录数量卡片
                        hrvDataCard(
                            title: "记录数量",
                            value: "\(hrvDetails.count)",
                            icon: "list.bullet.clipboard",
                            color: .blue
                        )
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
            }
        }
    }
    
    // HRV数据卡片
    private func hrvDataCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            
            Text(value)
                .font(.headline)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color.white)
        .cornerRadius(8)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - HRV图表区域
    private var hrvChartSection: some View {
        VStack {
            Text("24小时HRV趋势")
                .font(.headline)
                .padding(.top)
            
            Chart {
                ForEach(hrvDetails) { detail in
                    if let date = detail.date {
                        LineMark(
                            x: .value("时间", date, unit: .hour),
                            y: .value("HRV", detail.hrv)
                        )
                        .foregroundStyle(Color.purple.gradient)
                    }
                }
            }
            .frame(height: 250)
            .padding()
            .chartXAxis {
                AxisMarks(values: .stride(by: .hour, count: 3)) { value in
                    AxisGridLine()
                    AxisValueLabel(format: .dateTime.hour())
                }
            }
        }
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .padding(.horizontal)
    }
    
    // MARK: - HRV列表区域
    private var hrvListSection: some View {
        VStack {
            HStack {
                Text("HRV明细")
                    .font(.headline)
                
                Spacer()
                
                Text("共 \(hrvDetails.count) 条记录")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding(.horizontal)
            
            List {
                ForEach(hrvDetails) { detail in
                    HStack {
                        Text(detail.formattedTime())
                            .font(.system(.body, design: .monospaced))
                        
                        Spacer()
                        
                        Text("\(detail.hrv) ms")
                            .foregroundColor(.purple)
                            .font(.headline)
                    }
                    .padding(.vertical, 4)
                }
            }
            .frame(height: 200)
            .listStyle(PlainListStyle())
        }
    }
    
    /// 获取HRV明细数据
    private func fetchHRVDetails() {
        // 清空之前的数据和状态
        hrvDetails = []
        averageHRV = 0
        maxHRV = 0
        minHRV = 0
        uploadResult = ""
        
        isLoading = true
        
        // 计算实际日期并显示
        let calendar = Calendar.current
        let today = Date()
        let targetDate = calendar.date(byAdding: .day, value: -selectedDay, to: today) ?? today
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: targetDate)
        
        message = "正在获取\(dateString)的HRV数据..."
        messageColor = .blue
        
        print("🔍 开始获取日期 \(dateString) 的HRV数据 (day参数: \(selectedDay))")
        
        // 使用RawDataUploadService获取HRV明细
        RawDataUploadService.shared.fetchHRVDetailData(day: selectedDay) { details, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if let error = error {
                    message = "获取\(dateString)HRV明细失败: \(error.localizedDescription)"
                    messageColor = .red
                    return
                }
                
                if let details = details, !details.isEmpty {
                    hrvDetails = details
                    
                    // 计算统计数据
                    let hrvValues = details.map { $0.hrv }
                    maxHRV = hrvValues.max() ?? 0
                    minHRV = hrvValues.min() ?? 0
                    averageHRV = hrvValues.reduce(0, +) / hrvValues.count
                    
                    message = "\(dateString) 获取成功，共\(details.count)条记录，平均HRV\(averageHRV)ms"
                    messageColor = .green
                    
                    // 自动显示HRV数据
                    showHRVData = true
                    
                    // 如果启用了自动上传，获取数据后自动上传
                    if autoUploadEnabled {
                        uploadHRVData()
                    }
                } else {
                    message = "\(dateString) 没有HRV明细数据"
                    messageColor = .orange
                }
            }
        }
    }
    
    /// 上传HRV数据到服务器
    private func uploadHRVData() {
        isUploading = true
        uploadResult = "正在上传HRV数据..."
        uploadResultColor = .blue
        
        RawDataUploadService.shared.uploadHRVData(day: selectedDay) { success, error in
            DispatchQueue.main.async {
                isUploading = false
                
                if success {
                    uploadResult = "HRV数据上传成功！"
                    uploadResultColor = .green
                } else if let error = error {
                    uploadResult = "上传失败: \(error.localizedDescription)"
                    uploadResultColor = .red
                } else {
                    uploadResult = "上传失败: 未知错误"
                    uploadResultColor = .red
                }
            }
        }
    }
    
    /// 更新自动上传设置
    private func updateAutoUploadSetting(enabled: Bool) {
        if enabled {
            // 启用自动上传
            let success = RawDataUploadService.shared.startAutoUpload(interval: 300) // 5分钟
            if !success {
                // 如果启动失败，恢复开关状态
                DispatchQueue.main.async {
                    self.autoUploadEnabled = false
                }
            }
        } else {
            // 禁用自动上传
            RawDataUploadService.shared.stopAutoUpload()
        }
    }
    
    /// 验证自动上传状态
    private func checkUploadStatus() {
        // 调用服务验证状态
        RawDataUploadService.shared.checkAutoUploadStatus()
        
        // 刷新UI上的状态显示
        DispatchQueue.main.async {
            self.autoUploadEnabled = RawDataUploadService.shared.isAutoUploadEnabled
            
            // 显示验证消息
            self.message = "已检查自动上传状态，详情见控制台日志"
            self.messageColor = .blue
            
            // 如果上传服务未启用但应该启用，则尝试重新启动
            if !RawDataUploadService.shared.isAutoUploadEnabled && 
               UserDefaults.standard.bool(forKey: "auto_upload_enabled") {
                updateAutoUploadSetting(enabled: true)
                self.message = "已重新启动自动上传服务"
                self.messageColor = .green
            }
        }
    }
}

struct HRVDetailTestView_Previews: PreviewProvider {
    static var previews: some View {
        HRVDetailTestView()
    }
}
