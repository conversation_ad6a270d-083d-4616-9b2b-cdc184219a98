import SwiftUI

/// 数据流程测试视图
/// 用于测试和演示数据流程的实现
struct DataFlowTestView: View {
    // MARK: - 属性
    @ObservedObject private var dataSyncService = DataSyncService.shared
    @ObservedObject private var deviceService = WindRingDeviceService.shared
    
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    
    @State private var autoUploadEnabled = false
    
    // MARK: - 视图
    var body: some View {
        NavigationView {
            List {
                // 设备连接状态
                Section(header: Text("设备状态")) {
                    HStack {
                        Text("连接状态")
                        Spacer()
                        Text(deviceService.connectionState.description)
                            .foregroundColor(deviceService.connectionState.isConnected ? .green : .red)
                    }
                    
                    if let deviceInfo = deviceService.deviceInfo {
                        HStack {
                            Text("设备名称")
                            Spacer()
                            Text(deviceInfo.localName ?? "")
                        }
                        
                        HStack {
                            Text("MAC地址")
                            Spacer()
                            Text(deviceInfo.mac ?? "")
                        }
                        
                        HStack {
                            Text("固件版本")
                            Spacer()
                            Text(deviceInfo.firmwareVersion ?? "")
                        }
                    }
                }
                
                // 同步状态
                Section(header: Text("同步状态")) {
                    HStack {
                        Text("状态")
                        Spacer()
                        Text(dataSyncService.isSyncing ? "同步中..." : "空闲")
                            .foregroundColor(dataSyncService.isSyncing ? .blue : .primary)
                    }
                    
                    HStack {
                        Text("进度")
                        Spacer()
                        Text("\(dataSyncService.syncProgress)%")
                    }
                    
                    HStack {
                        Text("状态消息")
                        Spacer()
                        Text(dataSyncService.statusMessage)
                    }
                    
                    if let lastSyncTime = dataSyncService.lastSyncTime {
                        HStack {
                            Text("最后同步时间")
                            Spacer()
                            Text(formatDate(lastSyncTime))
                        }
                    }
                }
                
                // 自动上传设置
                Section(header: Text("自动上传设置")) {
                    Toggle("启用自动上传", isOn: $autoUploadEnabled)
                        .onChange(of: autoUploadEnabled) { newValue in
                            dataSyncService.setAutoUpload(enabled: newValue)
                        }
                }
                
                // 操作按钮
                Section {
                    Button(action: startSync) {
                        HStack {
                            Image(systemName: "arrow.triangle.2.circlepath")
                            Text("开始同步")
                        }
                    }
                    .disabled(dataSyncService.isSyncing || !deviceService.connectionState.isConnected)
                    
                    Button(action: connectDevice) {
                        HStack {
                            Image(systemName: "antenna.radiowaves.left.and.right")
                            Text(deviceService.connectionState.isConnected ? "断开连接" : "连接设备")
                        }
                    }
                    .disabled(dataSyncService.isSyncing)
                }
                
                // 数据流程说明
                Section(header: Text("数据流程说明")) {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("1. 获取戒指的原始数据")
                        Text("2. 将原始数据保存到本地数据库（未处理数据）")
                        Text("3. 将原始数据上传到后台服务器进行处理")
                        Text("4. 从后台下载处理完成的数据")
                        Text("5. 将处理后的数据保存到本地的另一个数据库（处理后数据）")
                        Text("6. 在前端界面展示处理后的数据")
                        Text("7. 数据展示优先级：本地处理后数据库 > 后台获取")
                        Text("8. 设置戒指自动上传数据")
                    }
                    .font(.footnote)
                    .foregroundColor(.secondary)
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("数据流程测试")
            .alert(isPresented: $showingAlert) {
                Alert(title: Text(alertTitle), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
        }
    }
    
    // MARK: - 方法
    
    /// 开始同步
    private func startSync() {
        dataSyncService.startSync { success, error in
            if !success {
                showAlert(title: "同步失败", message: error?.localizedDescription ?? "未知错误")
            } else {
                showAlert(title: "同步成功", message: "数据已成功同步")
            }
        }
    }
    
    /// 连接或断开设备
    private func connectDevice() {
        if deviceService.connectionState.isConnected {
            // 断开连接
            deviceService.disconnectDevice()
        } else {
            // 连接设备
            deviceService.startScan()
            
            // 模拟自动连接到第一个发现的设备
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                if !deviceService.discoveredDevices.isEmpty {
                    let device = deviceService.discoveredDevices.first!
                    deviceService.connectDevice(discovery: device)
                } else {
                    showAlert(title: "未发现设备", message: "请确保戒指已开启并在附近")
                }
            }
        }
    }
    
    /// 显示警告
    private func showAlert(title: String, message: String) {
        self.alertTitle = title
        self.alertMessage = message
        self.showingAlert = true
    }
    
    /// 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }
}
