import SwiftUI
// 确保颜色扩展被导入
import CoreData

/// API测试导航页面 - 集中所有API测试入口
struct APITestNavigationView: View {
    // MARK: - 状态属性
    @StateObject private var deviceService = WindRingDeviceService.shared
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            List {
                // 设备连接状态
                Section(header: Text("设备状态")) {
                    HStack {
                        Circle()
                            .fill(deviceService.connectionState.isConnected ? Color.green : Color.red)
                            .frame(width: 12, height: 12)
                        
                        Text(deviceService.connectionState.isConnected ? "已连接: \(deviceService.deviceInfo?.localName ?? "")" : "disconnected".localized)
                            .font(.subheadline)
                        
                        Spacer()
                        
                        if !deviceService.connectionState.isConnected {
                            NavigationLink(destination: DeviceManagerView()) {
                                Text("连接设备")
                                    .font(.footnote)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(Color.blue)
                                    .cornerRadius(8)
                            }
                        }
                    }
                }
                
                // 数据同步测试
                Section(header: Text("数据同步测试")) {
                    NavigationLink(destination: DataSyncTestView()) {
                        HStack {
                            Image(systemName: "arrow.triangle.2.circlepath.circle.fill")
                                .font(.title2)
                                .foregroundColor(Color(hex: "#FA6C2D"))
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("数据同步测试")
                                    .font(.headline)
                                Text("测试数据在本地设备与服务器之间的同步功能")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                }
                
                // API测试入口
                Section(header: Text("API测试")) {
                    // 步数API测试入口
                    NavigationLink(destination: StepAPITestView()) {
                        HStack {
                            Image(systemName: "figure.walk")
                                .font(.title2)
                                .foregroundColor(.blue)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("步数API测试")
                                    .font(.headline)
                                Text("测试获取步数、运动统计等功能")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    
                    // 原始数据上传入口
                    NavigationLink(destination: RawDataUploadView()) {
                        HStack {
                            Image(systemName: "arrow.up.doc.fill")
                                .font(.title2)
                                .foregroundColor(.purple)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("原始数据上传")
                                    .font(.headline)
                                Text("上传戒指原始数据到云端")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    
                    // 卡路里API测试入口
                    NavigationLink(destination: CalorieAPITestView()) {
                        HStack {
                            Image(systemName: "flame.fill")
                                .font(.title2)
                                .foregroundColor(.orange)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("卡路里API测试")
                                    .font(.headline)
                                Text("测试获取卡路里消耗、设置卡路里目标等功能")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    
                    // 心率API测试入口
                    NavigationLink(destination: HeartRateAPITestView()) {
                        HStack {
                            Image(systemName: "heart.fill")
                                .font(.title2)
                                .foregroundColor(.red)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("心率API测试")
                                    .font(.headline)
                                Text("测试单次心率测量、定时心率监测等功能")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    
                    // 体温API测试入口
                    NavigationLink(destination: TemperatureAPITestView()) {
                        HStack {
                            Image(systemName: "thermometer")
                                .font(.title2)
                                .foregroundColor(.orange)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("体温API测试")
                                    .font(.headline)
                                Text("测试体温测量、睡眠体温监测等功能")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    
                    // 血压API测试入口
                    NavigationLink(destination: BloodPressureAPITestView()) {
                        HStack {
                            Image(systemName: "heart.text.square.fill")
                                .font(.title2)
                                .foregroundColor(.red)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("血压API测试")
                                    .font(.headline)
                                Text("测试单次血压测量、数据解析等功能")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    
                    // 血氧API测试入口
                    NavigationLink(destination: BloodOxygenAPITestView()) {
                        HStack {
                            Image(systemName: "lungs.fill")
                                .font(.title2)
                                .foregroundColor(.blue)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("血氧API测试")
                                    .font(.headline)
                                Text("测试血氧测量、定时血氧监测等功能")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    
                    // 心率变异性API测试入口
                    NavigationLink(destination: HRVAPITestView()) {
                        HStack {
                            Image(systemName: "waveform.path.ecg")
                                .font(.title2)
                                .foregroundColor(.purple)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("心率变异性(HRV)API测试")
                                    .font(.headline)
                                Text("测试HRV测量、疲劳度监测等功能")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    
                    // 睡眠API测试入口
                    NavigationLink(destination: SleepAPITestView()) {
                        HStack {
                            Image(systemName: "moon.zzz.fill")
                                .font(.title2)
                                .foregroundColor(.purple)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("睡眠API测试")
                                    .font(.headline)
                                Text("测试获取睡眠数据、睡眠阶段等功能")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    
                    // 压力API测试入口
                    NavigationLink(destination: StressAPITestView()) {
                        HStack {
                            Image(systemName: "brain.head.profile")
                                .font(.title2)
                                .foregroundColor(.teal)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("压力API测试")
                                    .font(.headline)
                                Text("测试压力测量、压力历史记录等功能")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    
                    // 活动API测试入口
                    NavigationLink(destination: ActivityAPITestView()) {
                        HStack {
                            Image(systemName: "chart.bar.fill")
                                .font(.title2)
                                .foregroundColor(.green)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("活动API测试")
                                    .font(.headline)
                                Text("测试获取步数、距离、卡路里和运动时间等数据")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                }
                
                // 说明
                Section(header: Text("说明")) {
                    Text("本页面提供对智能戒指API的测试功能，包括步数、卡路里、心率、体温、血压、血氧、心率变异性和睡眠数据的获取与处理。需要先连接设备才能正常使用这些功能。数据同步测试部分可测试数据在本地和服务器之间的同步机制。")
                        .font(.footnote)
                        .foregroundColor(.gray)
                }
            }
            .navigationTitle("API测试中心")
            .navigationBarItems(trailing: Button("关闭") {
                dismiss()
            })
        }
    }
}

// MARK: - 预览
struct APITestNavigationView_Previews: PreviewProvider {
    static var previews: some View {
        APITestNavigationView()
    }
} 
