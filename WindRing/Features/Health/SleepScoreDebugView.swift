import SwiftUI

struct SleepScoreDebugView: View {
    private let sleepScoreService = SleepScoreService.shared
    
    @State private var sleepDuration: Double = 480 // 8小时
    @State private var bedDuration: Double = 510 // 8.5小时
    @State private var deepSleepDuration: Double = 120 // 2小时
    @State private var wakeCount: Double = 2
    
    @State private var calculatedScore: Int = 0
    @State private var scoreLevel: String = ""
    @State private var scoreColor: Color = .white
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("睡眠评分调试工具")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top, 20)
                
                // 评分显示
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 10)
                        .frame(width: 150, height: 150)
                    
                    Circle()
                        .trim(from: 0, to: CGFloat(calculatedScore) / 100.0)
                        .stroke(
                            scoreColor,
                            style: StrokeStyle(lineWidth: 10, lineCap: .round)
                        )
                        .frame(width: 150, height: 150)
                        .rotationEffect(.degrees(-90))
                    
                    VStack(spacing: 0) {
                        Text("\(calculatedScore)")
                            .font(.system(size: 42, weight: .bold))
                            .foregroundColor(.white)
                        
                        Text(scoreLevel)
                            .font(.system(size: 14))
                            .foregroundColor(scoreColor)
                    }
                }
                .padding(20)
                
                // 滑块控制
                VStack(spacing: 15) {
                    // 睡眠时长
                    VStack(alignment: .leading, spacing: 5) {
                        Text("睡眠时长: \(Int(sleepDuration))分钟 (\(Int(sleepDuration)/60)小时\(Int(sleepDuration)%60)分钟)")
                            .font(.headline)
                        
                        Slider(value: $sleepDuration, in: 300...600, step: 30)
                            .accentColor(.blue)
                            .onChange(of: sleepDuration) { _ in calculateScore() }
                    }
                    .padding(.horizontal)
                    
                    // 卧床时长
                    VStack(alignment: .leading, spacing: 5) {
                        Text("卧床时长: \(Int(bedDuration))分钟 (\(Int(bedDuration)/60)小时\(Int(bedDuration)%60)分钟)")
                            .font(.headline)
                        
                        Slider(value: $bedDuration, in: sleepDuration...(sleepDuration+120), step: 30)
                            .accentColor(.orange)
                            .onChange(of: bedDuration) { _ in calculateScore() }
                    }
                    .padding(.horizontal)
                    
                    // 深睡时长
                    VStack(alignment: .leading, spacing: 5) {
                        Text("深睡时长: \(Int(deepSleepDuration))分钟 (\(Int(deepSleepDuration)/60)小时\(Int(deepSleepDuration)%60)分钟)")
                            .font(.headline)
                        
                        Slider(value: $deepSleepDuration, in: 0...min(240, sleepDuration), step: 15)
                            .accentColor(.purple)
                            .onChange(of: deepSleepDuration) { _ in calculateScore() }
                    }
                    .padding(.horizontal)
                    
                    // 醒来次数
                    VStack(alignment: .leading, spacing: 5) {
                        Text("醒来次数: \(Int(wakeCount))次")
                            .font(.headline)
                        
                        Slider(value: $wakeCount, in: 0...10, step: 1)
                            .accentColor(.red)
                            .onChange(of: wakeCount) { _ in calculateScore() }
                    }
                    .padding(.horizontal)
                }
                
                // 计算详情
                Button("显示计算详情") {
                    sleepScoreService.debugCalculation(
                        sleepDuration: Int(sleepDuration),
                        bedDuration: Int(bedDuration),
                        deepSleepDuration: Int(deepSleepDuration),
                        wakeCount: Int(wakeCount)
                    )
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
                
                // 检查UI显示
                Text("确认UI显示值与计算值一致: \(calculatedScore)")
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(10)
            }
            .padding()
        }
        .onAppear {
            calculateScore()
        }
    }
    
    private func calculateScore() {
        calculatedScore = sleepScoreService.calculateSleepScore(
            sleepDuration: Int(sleepDuration),
            bedDuration: Int(bedDuration),
            deepSleepDuration: Int(deepSleepDuration),
            wakeCount: Int(wakeCount)
        )
        
        scoreLevel = sleepScoreService.getSleepScoreLevel(score: calculatedScore)
        scoreColor = Color(hex: sleepScoreService.getSleepScoreColor(score: calculatedScore))
        
        // 打印评分信息
        print("计算评分: \(calculatedScore), 等级: \(scoreLevel)")
    }
}

struct SleepScoreDebugView_Previews: PreviewProvider {
    static var previews: some View {
        SleepScoreDebugView()
            .preferredColorScheme(.dark)
    }
} 