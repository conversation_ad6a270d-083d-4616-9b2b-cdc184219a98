import SwiftUI
import CRPSmartRing

/// 实时健康数据视图
struct RealtimeHealthView: View {
    // MARK: - 属性
    @StateObject private var deviceService = WindRingDeviceService.shared
    @State private var selectedTab = 0
    @State private var showingDeviceSheet = false
    @State private var isMeasuring = false
    @State private var measurementType = ""
    @State private var showAlert = false
    @State private var alertMessage = ""
    
    // 测量数据
    @State private var heartRate = 0
    @State private var bloodOxygen = 0
    @State private var temperature = 0.0
    @State private var bloodPressure = (systolic: 0, diastolic: 0)
    
    // MARK: - 主视图
    var body: some View {
        VStack(spacing: 0) {
            // 顶部标签栏
            tabBar
            
            // 内容区域
            ScrollView {
                VStack(spacing: 24) {
                    // 设备状态卡片
                    deviceStatusCard
                    
                    // 测量卡片
                    switch selectedTab {
                    case 0:
                        heartRateCard
                    case 1:
                        bloodOxygenCard
                    case 2:
                        temperatureCard
                    case 3:
                        bloodPressureCard
                    default:
                        EmptyView()
                    }
                    
                    // 测量说明
                    measurementInstructions
                }
                .padding(.vertical)
            }
            .background(Color.appBackground)
        }
        .sheet(isPresented: $showingDeviceSheet) {
            DevicePairingView()
        }
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text("提示"),
                message: Text(alertMessage),
                dismissButton: .default(Text("确定"))
            )
        }
        .onAppear {
            // 注册通知观察者
            setupNotificationObservers()
            
            // 更新数据
            updateMeasurementData()
        }
        .onDisappear {
            // 移除通知观察者
            removeNotificationObservers()
            
            // 停止所有测量
            stopAllMeasurements()
        }
    }
    
    // MARK: - 顶部标签栏
    private var tabBar: some View {
        HStack(spacing: 0) {
            ForEach(0..<4) { index in
                Button(action: {
                    selectedTab = index
                    stopAllMeasurements()
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: tabIcon(for: index))
                            .font(.system(size: 20))
                        
                        Text(tabTitle(for: index))
                            .font(.caption)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .foregroundColor(selectedTab == index ? .blue : .gray)
                    .background(
                        selectedTab == index ?
                        Color.blue.opacity(0.1) :
                        Color.clear
                    )
                }
            }
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - 设备状态卡片
    private var deviceStatusCard: some View {
        VStack(spacing: 16) {
            HStack {
                Text("设备状态")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if deviceService.connectionState.isConnected {
                    HStack {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                        
                        Text("connected".localized)
                            .font(.subheadline)
                            .foregroundColor(.green)
                    }
                } else {
                    Button(action: {
                        showingDeviceSheet = true
                    }) {
                        Text("连接设备")
                            .font(.subheadline)
                            .foregroundColor(.blue)
                    }
                }
            }
            
            if deviceService.connectionState.isConnected {
                HStack(spacing: 20) {
                    // 设备图像
                    DeviceImageView(isActive: true)
                        .frame(width: 60, height: 60)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        // 设备名称
                        Text(deviceService.deviceInfo?.localName ?? "WindRing")
                            .font(.headline)
                        
                        // 佩戴状态
                        HStack {
                            Image(systemName: deviceService.wearingState == 1 ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(deviceService.wearingState == 1 ? .green : .orange)
                            
                            Text(deviceService.wearingState == 1 ? "已佩戴" : "未佩戴")
                                .font(.subheadline)
                                .foregroundColor(deviceService.wearingState == 1 ? .green : .orange)
                        }
                    }
                    
                    Spacer()
                    
                    // 电池状态
                    VStack(alignment: .center, spacing: 4) {
                        Image(systemName: batteryIconName)
                            .foregroundColor(batteryColor)
                        
                        Text("\(deviceService.batteryLevel)%")
                            .font(.caption)
                            .foregroundColor(batteryColor)
                    }
                }
            } else {
                HStack {
                    Image(systemName: "exclamationmark.circle")
                        .foregroundColor(.orange)
                    
                    Text("请先连接设备以开始测量")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
    
    // MARK: - 心率卡片
    private var heartRateCard: some View {
        VStack(spacing: 16) {
            HStack {
                Text("心率")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: {
                    startMeasurement(type: "心率")
                }) {
                    Text(isMeasuring && measurementType == "心率" ? "停止测量" : "开始测量")
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }
                .disabled(!deviceService.connectionState.isConnected)
            }
            
            ZStack {
                Circle()
                    .stroke(Color.red.opacity(0.2), lineWidth: 20)
                    .frame(width: 200, height: 200)
                
                if isMeasuring && measurementType == "心率" {
                    Circle()
                        .trim(from: 0, to: 0.75)
                        .stroke(Color.red, lineWidth: 20)
                        .frame(width: 200, height: 200)
                        .rotationEffect(Angle(degrees: -90))
                        .animation(Animation.linear(duration: 1).repeatForever(autoreverses: false), value: UUID())
                    
                    Text("测量中...")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.red)
                } else {
                    VStack(spacing: 8) {
                        Text("\(heartRate)")
                            .font(.system(size: 60, weight: .bold))
                            .foregroundColor(.red)
                        
                        Text("BPM")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.vertical, 20)
            
            HStack(spacing: 0) {
                healthStatusBox(title: "偏低", range: "< 60", isActive: heartRate > 0 && heartRate < 60)
                healthStatusBox(title: "正常", range: "60-100", isActive: heartRate >= 60 && heartRate <= 100)
                healthStatusBox(title: "偏高", range: "> 100", isActive: heartRate > 100)
            }
            .background(Color(.systemBackground))
            .cornerRadius(8)
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
    
    // MARK: - 血氧卡片
    private var bloodOxygenCard: some View {
        VStack(spacing: 16) {
            HStack {
                Text("血氧")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: {
                    startMeasurement(type: "血氧")
                }) {
                    Text(isMeasuring && measurementType == "血氧" ? "停止测量" : "开始测量")
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }
                .disabled(!deviceService.connectionState.isConnected)
            }
            
            ZStack {
                Circle()
                    .stroke(Color.blue.opacity(0.2), lineWidth: 20)
                    .frame(width: 200, height: 200)
                
                if isMeasuring && measurementType == "血氧" {
                    Circle()
                        .trim(from: 0, to: 0.75)
                        .stroke(Color.blue, lineWidth: 20)
                        .frame(width: 200, height: 200)
                        .rotationEffect(Angle(degrees: -90))
                        .animation(Animation.linear(duration: 1).repeatForever(autoreverses: false), value: UUID())
                    
                    Text("测量中...")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                } else {
                    VStack(spacing: 8) {
                        Text("\(bloodOxygen)")
                            .font(.system(size: 60, weight: .bold))
                            .foregroundColor(.blue)
                        
                        Text("%")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.vertical, 20)
            
            HStack(spacing: 0) {
                healthStatusBox(title: "偏低", range: "< 95", isActive: bloodOxygen > 0 && bloodOxygen < 95)
                healthStatusBox(title: "正常", range: "95-100", isActive: bloodOxygen >= 95 && bloodOxygen <= 100)
            }
            .background(Color(.systemBackground))
            .cornerRadius(8)
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
    
    // MARK: - 体温卡片
    private var temperatureCard: some View {
        VStack(spacing: 16) {
            HStack {
                Text("体温")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: {
                    startMeasurement(type: "体温")
                }) {
                    Text(isMeasuring && measurementType == "体温" ? "停止测量" : "开始测量")
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }
                .disabled(!deviceService.connectionState.isConnected)
            }
            
            ZStack {
                Circle()
                    .stroke(Color.orange.opacity(0.2), lineWidth: 20)
                    .frame(width: 200, height: 200)
                
                if isMeasuring && measurementType == "体温" {
                    Circle()
                        .trim(from: 0, to: 0.75)
                        .stroke(Color.orange, lineWidth: 20)
                        .frame(width: 200, height: 200)
                        .rotationEffect(Angle(degrees: -90))
                        .animation(Animation.linear(duration: 1).repeatForever(autoreverses: false), value: UUID())
                    
                    Text("测量中...")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                } else {
                    VStack(spacing: 8) {
                        Text(temperature > 0 ? String(format: "%.1f", temperature) : "0.0")
                            .font(.system(size: 60, weight: .bold))
                            .foregroundColor(.orange)
                        
                        Text("°C")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.vertical, 20)
            
            HStack(spacing: 0) {
                healthStatusBox(title: "偏低", range: "< 36.3", isActive: temperature > 0 && temperature < 36.3)
                healthStatusBox(title: "正常", range: "36.3-37.2", isActive: temperature >= 36.3 && temperature <= 37.2)
                healthStatusBox(title: "偏高", range: "> 37.2", isActive: temperature > 37.2)
            }
            .background(Color(.systemBackground))
            .cornerRadius(8)
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
    
    // MARK: - 血压卡片
    private var bloodPressureCard: some View {
        VStack(spacing: 16) {
            HStack {
                Text("血压")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: {
                    startMeasurement(type: "血压")
                }) {
                    Text(isMeasuring && measurementType == "血压" ? "停止测量" : "开始测量")
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }
                .disabled(!deviceService.connectionState.isConnected)
            }
            
            ZStack {
                Circle()
                    .stroke(Color.purple.opacity(0.2), lineWidth: 20)
                    .frame(width: 200, height: 200)
                
                if isMeasuring && measurementType == "血压" {
                    Circle()
                        .trim(from: 0, to: 0.75)
                        .stroke(Color.purple, lineWidth: 20)
                        .frame(width: 200, height: 200)
                        .rotationEffect(Angle(degrees: -90))
                        .animation(Animation.linear(duration: 1).repeatForever(autoreverses: false), value: UUID())
                    
                    Text("测量中...")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                } else if bloodPressure.systolic > 0 && bloodPressure.diastolic > 0 {
                    VStack(spacing: 8) {
                        Text("\(bloodPressure.systolic)/\(bloodPressure.diastolic)")
                            .font(.system(size: 50, weight: .bold))
                            .foregroundColor(.purple)
                        
                        Text("mmHg")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                } else {
                    VStack(spacing: 8) {
                        Text("0/0")
                            .font(.system(size: 50, weight: .bold))
                            .foregroundColor(.purple)
                        
                        Text("mmHg")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.vertical, 20)
            
            VStack(spacing: 0) {
                HStack {
                    Text("收缩压")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                    
                    Divider()
                    
                    Text("舒张压")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                }
                .padding(.vertical, 8)
                
                Divider()
                
                HStack(spacing: 0) {
                    bloodPressureStatusBox(title: "偏低", systolicRange: "< 90", diastolicRange: "< 60", 
                                          isActive: bloodPressure.systolic > 0 && bloodPressure.systolic < 90 && bloodPressure.diastolic < 60)
                    
                    Divider()
                    
                    bloodPressureStatusBox(title: "正常", systolicRange: "90-139", diastolicRange: "60-89", 
                                          isActive: bloodPressure.systolic >= 90 && bloodPressure.systolic <= 139 && 
                                                   bloodPressure.diastolic >= 60 && bloodPressure.diastolic <= 89)
                    
                    Divider()
                    
                    bloodPressureStatusBox(title: "偏高", systolicRange: "> 139", diastolicRange: "> 89", 
                                          isActive: bloodPressure.systolic > 139 || bloodPressure.diastolic > 89)
                }
            }
            .background(Color(.systemBackground))
            .cornerRadius(8)
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
    
    // MARK: - 测量说明
    private var measurementInstructions: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测量说明")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 8) {
                instructionRow(icon: "1.circle.fill", text: "确保戒指已正确佩戴，并保持手指静止")
                instructionRow(icon: "2.circle.fill", text: "测量过程中请保持放松，避免剧烈运动")
                instructionRow(icon: "3.circle.fill", text: "测量完成后，数据将自动保存到健康记录")
                instructionRow(icon: "exclamationmark.triangle.fill", text: "本设备测量结果仅供参考，不作为医疗诊断依据")
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
    
    // MARK: - 辅助视图
    
    // 健康状态框
    private func healthStatusBox(title: String, range: String, isActive: Bool) -> some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.subheadline)
                .fontWeight(isActive ? .bold : .regular)
                .foregroundColor(isActive ? .primary : .secondary)
            
            Text(range)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(isActive ? Color.blue.opacity(0.1) : Color.clear)
    }
    
    // 血压状态框
    private func bloodPressureStatusBox(title: String, systolicRange: String, diastolicRange: String, isActive: Bool) -> some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.subheadline)
                .fontWeight(isActive ? .bold : .regular)
                .foregroundColor(isActive ? .primary : .secondary)
            
            Text(systolicRange)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(diastolicRange)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(isActive ? Color.blue.opacity(0.1) : Color.clear)
    }
    
    // 说明行
    private func instructionRow(icon: String, text: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
        }
    }
    
    // 电池图标名称
    private var batteryIconName: String {
        let level = deviceService.batteryLevel
        if level <= 20 {
            return "battery.0"
        } else if level <= 40 {
            return "battery.25"
        } else if level <= 60 {
            return "battery.50"
        } else if level <= 80 {
            return "battery.75"
        } else {
            return "battery.100"
        }
    }
    
    // 电池颜色
    private var batteryColor: Color {
        let level = deviceService.batteryLevel
        if level <= 20 {
            return .red
        } else if level <= 40 {
            return .orange
        } else {
            return .green
        }
    }
    
    // 标签图标
    private func tabIcon(for index: Int) -> String {
        switch index {
        case 0: return "heart.fill"
        case 1: return "drop.fill"
        case 2: return "thermometer"
        case 3: return "waveform.path.ecg"
        default: return ""
        }
    }
    
    // 标签标题
    private func tabTitle(for index: Int) -> String {
        switch index {
        case 0: return "心率"
        case 1: return "血氧"
        case 2: return "体温"
        case 3: return "血压"
        default: return ""
        }
    }
    
    // MARK: - 通知处理
    
    // 设置通知观察者
    private func setupNotificationObservers() {
        // 心率通知
        NotificationCenter.default.addObserver(
            forName: .heartRateMeasured,
            object: nil,
            queue: .main
        ) { notification in
            if let value = notification.userInfo?["value"] as? Int {
                self.heartRate = value
                if self.measurementType == "心率" {
                    self.isMeasuring = false
                }
            }
        }
        
        // 血氧通知
        NotificationCenter.default.addObserver(
            forName: .bloodOxygenMeasured,
            object: nil,
            queue: .main
        ) { notification in
            if let value = notification.userInfo?["value"] as? Int {
                self.bloodOxygen = value
                if self.measurementType == "血氧" {
                    self.isMeasuring = false
                }
            }
        }
        
        // 体温通知
        NotificationCenter.default.addObserver(
            forName: .temperatureMeasured,
            object: nil,
            queue: .main
        ) { notification in
            if let value = notification.userInfo?["value"] as? Double {
                self.temperature = value
                if self.measurementType == "体温" {
                    self.isMeasuring = false
                }
            }
        }
        
        // 血压通知
        NotificationCenter.default.addObserver(
            forName: .bloodPressureMeasured,
            object: nil,
            queue: .main
        ) { notification in
            if let systolic = notification.userInfo?["systolic"] as? Int,
               let diastolic = notification.userInfo?["diastolic"] as? Int {
                self.bloodPressure = (systolic: systolic, diastolic: diastolic)
                if self.measurementType == "血压" {
                    self.isMeasuring = false
                }
            }
        }
    }
    
    // 移除通知观察者
    private func removeNotificationObservers() {
        NotificationCenter.default.removeObserver(self, name: .heartRateMeasured, object: nil)
        NotificationCenter.default.removeObserver(self, name: .bloodOxygenMeasured, object: nil)
        NotificationCenter.default.removeObserver(self, name: .temperatureMeasured, object: nil)
        NotificationCenter.default.removeObserver(self, name: .bloodPressureMeasured, object: nil)
    }
    
    // MARK: - 辅助方法
    
    // 更新测量数据
    private func updateMeasurementData() {
        heartRate = deviceService.lastHeartRate
        bloodOxygen = deviceService.lastBloodOxygen
        temperature = deviceService.lastTemperature
        bloodPressure = deviceService.lastBloodPressure
    }
    
    // 开始测量
    private func startMeasurement(type: String) {
        if !deviceService.connectionState.isConnected {
            alertMessage = "请先连接设备"
            showAlert = true
            return
        }
        
        if deviceService.wearingState != 1 {
            alertMessage = "请先佩戴设备"
            showAlert = true
            return
        }
        
        if isMeasuring {
            stopMeasurement()
            return
        }
        
        measurementType = type
        isMeasuring = true
        
        switch type {
        case "心率":
            deviceService.startHeartRateMeasurement()
        case "血氧":
            deviceService.startSpO2Measurement()
        case "体温":
            deviceService.startTemperatureMeasurement()
        case "血压":
            deviceService.startBloodPressureMeasurement()
        default:
            break
        }
    }
    
    // 停止测量
    private func stopMeasurement() {
        isMeasuring = false
        
        switch measurementType {
        case "心率":
            deviceService.stopHeartRateMeasurement()
        case "血氧":
            deviceService.stopSpO2Measurement()
        case "体温":
            deviceService.stopTemperatureMeasurement()
        case "血压":
            deviceService.stopBloodPressureMeasurement()
        default:
            break
        }
        
        measurementType = ""
    }
    
    // 停止所有测量
    private func stopAllMeasurements() {
        deviceService.stopHeartRateMeasurement()
        deviceService.stopSpO2Measurement()
        deviceService.stopTemperatureMeasurement()
        deviceService.stopBloodPressureMeasurement()
        
        isMeasuring = false
        measurementType = ""
    }
}

// MARK: - 预览
struct RealtimeHealthView_Previews: PreviewProvider {
    static var previews: some View {
        RealtimeHealthView()
    }
} 
