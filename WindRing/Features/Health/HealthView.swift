import SwiftUI

struct HealthView: View {
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("身体指标")) {
                    NavigationLink(destination: SleepHistoryView()) {
                        HStack {
                            Image(systemName: "bed.double")
                                .foregroundColor(.blue)
                                .frame(width: 30, height: 30)
                            
                            VStack(alignment: .leading) {
                                Text("睡眠")
                                    .font(.headline)
                                Text("查看您的睡眠质量和模式")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                    
                    // 可以添加更多健康指标入口
                    HStack {
                        Image(systemName: "heart")
                            .foregroundColor(.red)
                            .frame(width: 30, height: 30)
                        
                        VStack(alignment: .leading) {
                            Text("心率")
                                .font(.headline)
                            Text("查看您的心率和心脏健康")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.vertical, 4)
                    
                    HStack {
                        Image(systemName: "flame")
                            .foregroundColor(.orange)
                            .frame(width: 30, height: 30)
                        
                        VStack(alignment: .leading) {
                            Text("活动")
                                .font(.headline)
                            Text("查看您的活动和锻炼记录")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.vertical, 4)
                }
                
                Section(header: Text("健康洞察")) {
                    HStack {
                        Image(systemName: "chart.bar")
                            .foregroundColor(.purple)
                            .frame(width: 30, height: 30)
                        
                        VStack(alignment: .leading) {
                            Text("健康报告")
                                .font(.headline)
                            Text("获取每周健康状况总结")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.vertical, 4)
                }
            }
            .navigationTitle("健康")
        }
    }
}

struct HealthView_Previews: PreviewProvider {
    static var previews: some View {
        HealthView()
    }
} 