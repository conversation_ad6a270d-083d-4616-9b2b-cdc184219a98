import SwiftUI
import Combine

// MARK: - 体温视图模型
class TemperatureViewModel: ObservableObject {
    // 数据状态
    @Published var temperatureData: TemperatureDetailsData?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // 服务和取消存储
    private let apiService = APIService.shared.health
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化方法
    init() {
        // 初始化时可以添加其他设置
    }
    
    // MARK: - 数据加载
    
    /// 加载体温数据
    /// - Parameter date: 日期
    func loadTemperatureData(for date: Date) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("加载体温详情数据，日期字符串格式: \(dateString)")
        
        isLoading = true
        errorMessage = nil
        
        apiService.getTemperatureToVital(date: dateString)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                
                if case .failure(let error) = completion {
                    self?.errorMessage = error.localizedDescription
                    print("加载体温详情数据失败: \(error)")
                }
            } receiveValue: { [weak self] response in
//                if response.code == 0 {
                    self?.temperatureData = response//response.data
                    print("成功获取体温详情数据")
//                } else {
//                    self?.errorMessage = response.msg
//                    print("加载体温详情数据失败: \(response.msg)")
//                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 计算属性
    
    /// 平均体温
    var averageTemperature: Double {
        return temperatureData?.average ?? 0.0
    }
    
    /// 基准体温/当前读数
    var baselineTemperature: Double {
        return temperatureData?.baseline ?? 0.0
    }
    
    /// 温差
    var temperatureDifference: Double {
        return temperatureData?.differ ?? 0.0
    }
    
    /// 格式化的平均体温（保留一位小数）
    var formattedAverageTemperature: String {
        return String(format: "%.1f", averageTemperature)
    }
    
    /// 格式化的基准体温（保留一位小数）
    var formattedBaselineTemperature: String {
        return String(format: "%.1f", baselineTemperature)
    }
    
    /// 格式化的温差（保留一位小数）
    var formattedTemperatureDifference: String {
        return String(format: "%.1f", temperatureDifference)
    }
    
    /// 体温是否在正常范围内（36.3-37.3℃）
    var isTemperatureNormal: Bool {
        let temp = averageTemperature
        return temp >= 36.3 && temp <= 37.3
    }
    
    /// 获取体温状态描述
    var temperatureStatusDescription: String {
        if averageTemperature == 0 {
            return "No temperature data available."
        }
        
        if isTemperatureNormal {
            return "Body temperature is within the normal range!"
        } else if averageTemperature < 36.3 {
            return "Body temperature is low."
        } else {
            return "Body temperature is high."
        }
    }
    
    /// 体温状态颜色
    var temperatureStatusColor: Color {
        if averageTemperature < 36.3 {
            return .blue
        } else if averageTemperature > 37.3 {
            return .red
        } else {
            return .green
        }
    }
    
    // MARK: - 清理资源
    deinit {
        cancellables.forEach { $0.cancel() }
    }
} 
