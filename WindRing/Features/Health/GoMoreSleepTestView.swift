import SwiftUI
import CRPSmartRing

/// GoMore高级睡眠数据测试视图 - 用于测试获取和展示GoMore高级睡眠分析功能
struct GoMoreSleepTestView: View {
    // MARK: - 状态属性
    @StateObject private var viewModel = GoMoreSleepTestViewModel()
    @State private var selectedTab = 0
    
    // MARK: - 主视图
    var body: some View {
        VStack {
            // 顶部连接状态
            connectionStatusView
            
            // 标签选择器
            Picker("测试类别", selection: $selectedTab) {
                Text("数据列表").tag(0)
                Text("详细数据").tag(1)
                Text("分段数据").tag(2)
                Text("类型分析").tag(3)
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.horizontal)
            
            // 根据选择的标签显示不同内容
            ScrollView {
                VStack(spacing: 20) {
                    if selectedTab == 0 {
                        sleepDataListView
                    } else if selectedTab == 1 {
                        sleepDetailView
                    } else if selectedTab == 2 {
                        sleepSegmentationView
                    } else {
                        sleepTypeView
                    }
                    
                    Divider()
                        .padding(.vertical)
                    
                    // 结果显示区域
                    resultsView
                }
                .padding()
            }
        }
        .navigationTitle("GoMore睡眠测试")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { viewModel.clearResults() }) {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                }
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 连接状态视图
    private var connectionStatusView: some View {
        HStack {
            Circle()
                .fill(viewModel.deviceService.connectionState.isConnected ? Color.green : Color.red)
                .frame(width: 12, height: 12)
            
            Text(viewModel.deviceService.connectionState.isConnected ? "已连接: \(viewModel.deviceService.deviceInfo?.localName ?? "")" : "disconnected".localized)
                .font(.footnote)
            
            Spacer()
            
            if !viewModel.deviceService.connectionState.isConnected {
                NavigationLink(destination: DeviceManagerView()) {
                    Text("连接设备")
                        .font(.footnote)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue)
                        .cornerRadius(8)
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
    
    /// 睡眠数据列表视图
    private var sleepDataListView: some View {
        VStack(spacing: 12) {
            Text("睡眠数据列表")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            testButton(
                title: "获取睡眠数据列表",
                systemImage: "list.bullet",
                action: { viewModel.fetchSleepDataList() }
            )
            
            if !viewModel.sleepRecords.isEmpty {
                Text("数据列表 (\(viewModel.sleepRecords.count)条记录)")
                    .font(.subheadline)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.top, 8)
                
                // 显示睡眠记录列表
                ForEach(viewModel.sleepRecords, id: \.id) { record in
                    sleepRecordRow(record: record)
                }
            }
        }
    }
    
    /// 睡眠详细信息视图
    private var sleepDetailView: some View {
        VStack(spacing: 12) {
            Text("睡眠详细数据")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 选择睡眠ID下拉菜单
            if !viewModel.sleepRecords.isEmpty {
                Menu {
                    ForEach(viewModel.sleepRecords, id: \.id) { record in
                        Button("ID: \(record.id) (时间: \(viewModel.formatDate(timestamp: record.startTime)))") {
                            viewModel.selectedSleepId = record.id
                        }
                    }
                } label: {
                    HStack {
                        Text("选择睡眠记录: \(viewModel.selectedSleepId > 0 ? String(viewModel.selectedSleepId) : "未选择")")
                        Spacer()
                        Image(systemName: "chevron.down")
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
            }
            
            testButton(
                title: "获取详细睡眠数据",
                systemImage: "doc.text.magnifyingglass",
                action: { viewModel.fetchSleepDetail() }
            )
            .disabled(viewModel.selectedSleepId == 0)
            
            // 显示详细数据
            if let detail = viewModel.sleepDetail {
                VStack(alignment: .leading, spacing: 8) {
                    Text("详细数据结果:")
                        .font(.subheadline)
                        .padding(.top, 8)
                    
                    VStack(spacing: 8) {
                        dataRow(label: "开始时间", value: viewModel.formatDate(timestamp: detail.startTime))
                        dataRow(label: "结束时间", value: viewModel.formatDate(timestamp: detail.endTime))
                        dataRow(label: "总睡眠时间", value: "\(detail.totalTime)分钟")
                        dataRow(label: "睡眠效率", value: "\(Int(detail.sleepEfficiency * 100))%")
                        dataRow(label: "睡眠得分", value: "\(Int(detail.sleepScore))")
                        dataRow(label: "睡眠类型", value: detail.type == 1 ? "长睡眠" : "短睡眠")
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
        }
    }
    
    /// 睡眠分段数据视图
    private var sleepSegmentationView: some View {
        VStack(spacing: 12) {
            Text("睡眠分段数据")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 选择睡眠ID下拉菜单
            if !viewModel.sleepRecords.isEmpty {
                Menu {
                    ForEach(viewModel.sleepRecords, id: \.id) { record in
                        Button("ID: \(record.id) (时间: \(viewModel.formatDate(timestamp: record.startTime)))") {
                            viewModel.selectedSleepId = record.id
                        }
                    }
                } label: {
                    HStack {
                        Text("选择睡眠记录: \(viewModel.selectedSleepId > 0 ? String(viewModel.selectedSleepId) : "未选择")")
                        Spacer()
                        Image(systemName: "chevron.down")
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
            }
            
            testButton(
                title: "获取睡眠分段数据",
                systemImage: "chart.bar.xaxis",
                action: { viewModel.fetchSleepSegmentation() }
            )
            .disabled(viewModel.selectedSleepId == 0)
            
            // 显示分段数据
            if let segment = viewModel.sleepSegmentation {
                VStack(alignment: .leading, spacing: 8) {
                    Text("分段数据结果:")
                        .font(.subheadline)
                        .padding(.top, 8)
                    
                    VStack(spacing: 8) {
                        dataRow(label: "深睡", value: "\(segment.deep)分钟")
                        dataRow(label: "浅睡", value: "\(segment.light)分钟")
                        dataRow(label: "REM", value: "\(segment.rem)分钟")
                        dataRow(label: "总睡眠", value: "\(segment.deep + segment.light + segment.rem)分钟")
                        
                        // 睡眠阶段百分比
                        let total = segment.deep + segment.light + segment.rem
                        if total > 0 {
                            dataRow(label: "深睡占比", value: "\(Int(Double(segment.deep) / Double(total) * 100))%")
                            dataRow(label: "浅睡占比", value: "\(Int(Double(segment.light) / Double(total) * 100))%")
                            dataRow(label: "REM占比", value: "\(Int(Double(segment.rem) / Double(total) * 100))%")
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
                
                // 睡眠阶段图表
                sleepStagesChart(deep: segment.deep, light: segment.light, rem: segment.rem)
                    .frame(height: 60)
                    .padding(.vertical)
            }
        }
    }
    
    /// 睡眠类型视图
    private var sleepTypeView: some View {
        VStack(spacing: 12) {
            Text("睡眠类型分析")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            testButton(
                title: "获取睡眠类型分析",
                systemImage: "chart.xyaxis.line",
                action: { viewModel.fetchSleepType() }
            )
            
            // 显示类型数据
            if let type = viewModel.sleepType {
                VStack(alignment: .leading, spacing: 8) {
                    Text("睡眠类型分析结果:")
                        .font(.subheadline)
                        .padding(.top, 8)
                    
                    // 预先计算所有显示文本
                    let stateText: String = {
                        switch type.state {
                        case 0: return "长睡眠数据收集进行中"
                        case 1: return "已收集一天长睡眠数据，有基本时间类型指标"
                        case 2: return "在一天内收集的长睡眠数据，但有睡眠片段"
                        case 3: return "连续7天收集4天或更多天的长睡眠数据，具有较高置信度"
                        case 4: return "连续7天收集长睡眠数据，具有较高置信度"
                        case 5: return "连续7天收集长睡眠数据，但置信度较低"
                        case 71: return "睡眠时间超过15小时"
                        case 72: return "连续7天内未收集长期睡眠数据"
                        default: return "未知状态(\(type.state))"
                        }
                    }()
                    
                    let typeText: String = {
                        switch type.type {
                        case 1: return "蜂鸟型"
                        case 2: return "猫头鹰型"
                        case 3: return "云雀型"
                        default: return "未知类型(\(type.type))"
                        }
                    }()
                    
                    let reliabilityText = type.reliability == 1 ? "高" : "低"
                    
                    let bedtimeText: String = {
                        if type.bedtime > 0 {
                            let bedtimeHour = type.bedtime / 60
                            let bedtimeMinute = type.bedtime % 60
                            return "\(bedtimeHour):\(String(format: "%02d", bedtimeMinute))"
                        } else {
                            return ""
                        }
                    }()
                    
                    // 使用VStack包装视图组件
                    VStack(alignment: .leading, spacing: 8) {
                        dataRow(label: "睡眠状态", value: stateText)
                        dataRow(label: "睡眠类型", value: typeText)
                        dataRow(label: "数据可靠性", value: reliabilityText)
                        if !bedtimeText.isEmpty {
                            dataRow(label: "通常就寝时间", value: bedtimeText)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
                
                // 睡眠类型图标和说明
                sleepTypeDescription(type: type.type)
            }
        }
    }
    
    /// 结果视图
    private var resultsView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("操作日志")
                .font(.headline)
                .padding(.vertical, 4)
            
            ScrollView {
                ForEach(viewModel.results.indices, id: \.self) { index in
                    let result = viewModel.results[index]
                    Text(result.text)
                        .foregroundColor(result.color)
                        .font(.system(.footnote, design: .monospaced))
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.vertical, 2)
                }
            }
            .frame(height: 200)
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
    }
    
    // MARK: - 辅助视图函数
    
    /// 睡眠记录行
    private func sleepRecordRow(record: CRPGoMoreSleepRecord) -> some View {
        Button(action: {
            viewModel.selectedSleepId = record.id
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("ID: \(record.id)")
                        .font(.subheadline)
                    Text("开始: \(viewModel.formatDate(timestamp: record.startTime))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.gray)
            }
            .padding()
            .background(viewModel.selectedSleepId == record.id ? Color.blue.opacity(0.1) : Color(.systemGray6))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /// 测试按钮
    private func testButton(title: String, systemImage: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Image(systemName: systemImage)
                Text(title)
                Spacer()
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
        .disabled(!viewModel.deviceService.connectionState.isConnected)
    }
    
    /// 数据行
    private func dataRow(label: String, value: String) -> some View {
        HStack(alignment: .top) {
            Text("\(label):")
                .fontWeight(.medium)
                .frame(width: 100, alignment: .leading)
            Text(value)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
    
    /// 睡眠阶段图表
    private func sleepStagesChart(deep: Int, light: Int, rem: Int) -> some View {
        let total = deep + light + rem
        
        return GeometryReader { geometry in
            HStack(spacing: 0) {
                // 深睡部分
                if total > 0 && deep > 0 {
                    Rectangle()
                        .fill(Color.blue)
                        .frame(width: CGFloat(deep) / CGFloat(total) * geometry.size.width)
                }
                
                // 浅睡部分
                if total > 0 && light > 0 {
                    Rectangle()
                        .fill(Color.teal)
                        .frame(width: CGFloat(light) / CGFloat(total) * geometry.size.width)
                }
                
                // REM部分
                if total > 0 && rem > 0 {
                    Rectangle()
                        .fill(Color.purple)
                        .frame(width: CGFloat(rem) / CGFloat(total) * geometry.size.width)
                }
            }
            .cornerRadius(8)
            .overlay(
                VStack {
                    if total > 0 {
                        Text("深睡: \(Int(Double(deep) / Double(total) * 100))% | 浅睡: \(Int(Double(light) / Double(total) * 100))% | REM: \(Int(Double(rem) / Double(total) * 100))%")
                            .font(.caption)
                            .foregroundColor(.white)
                            .shadow(radius: 1)
                    }
                }
            )
        }
    }
    
    /// 睡眠类型说明
    private func sleepTypeDescription(type: Int) -> some View {
        VStack(spacing: 12) {
            HStack(spacing: 30) {
                Image(systemName: type == 1 ? "hare" : (type == 2 ? "owl" : "bird"))
                    .font(.largeTitle)
                    .foregroundColor(type == 1 ? .orange : (type == 2 ? .purple : .blue))
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(type == 1 ? "蜂鸟型" : (type == 2 ? "猫头鹰型" : "云雀型"))
                        .font(.headline)
                    
                    Text(type == 1 ? "您属于短睡眠型，睡眠时间较短但睡眠质量高。" : 
                         (type == 2 ? "您属于晚睡型，晚上精力充沛，早上不易起床。" : 
                          "您属于早起型，早睡早起，早晨精力充沛。"))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            
            if type == 1 {
                sleepTypeRecommendation("保持规律作息，确保睡眠质量", "避免午间咖啡因摄入", "睡前30分钟避免使用电子设备")
            } else if type == 2 {
                sleepTypeRecommendation("尽量避免早晨重要会议", "考虑调整工作时间", "下午适当运动提升夜间睡眠质量")
            } else {
                sleepTypeRecommendation("利用早晨精力充沛时段处理复杂任务", "晚上提前准备第二天事项", "午后小憩提升下午工作效率")
            }
        }
    }
    
    /// 睡眠类型建议
    private func sleepTypeRecommendation(_ tips: String...) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("睡眠建议:")
                .font(.subheadline)
                .fontWeight(.semibold)
            
            ForEach(0..<tips.count, id: \.self) { index in
                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                    
                    Text(tips[index])
                        .font(.callout)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - ViewModel

/// GoMore睡眠测试视图模型
class GoMoreSleepTestViewModel: ObservableObject {
    // MARK: - 服务和状态属性
    @Published var deviceService = WindRingDeviceService.shared
    @Published var results: [(text: String, color: Color)] = []
    
    // 睡眠数据
    @Published var sleepRecords: [CRPGoMoreSleepRecord] = []
    @Published var selectedSleepId: Int = 0
    @Published var sleepDetail: CRPGoMoreSleepDataModel? = nil
    @Published var sleepSegmentation: CRPSleepModel? = nil
    @Published var sleepType: CRPGoMoreSleepType? = nil
    
    // MARK: - 初始化
    init() {
        addResult("GoMore睡眠测试页面初始化完成", color: .green)
        addResult("请连接设备并选择测试功能", color: .blue)
        
        // 设置SDK代理
        CRPSmartRingSDK.sharedInstance.delegate = self
    }
    
    // MARK: - 操作方法
    
    /// 获取睡眠数据列表
    func fetchSleepDataList() {
        addResult("正在获取睡眠数据列表...", color: .blue)
        
        // 清空现有记录
        sleepRecords = []
        
        // 调用SDK获取睡眠数据列表
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataList()
    }
    
    /// 获取睡眠详细数据
    func fetchSleepDetail() {
        guard selectedSleepId > 0 else {
            addResult("请先选择一个睡眠记录", color: .red)
            return
        }
        
        addResult("正在获取睡眠ID \(selectedSleepId) 的详细数据...", color: .blue)
        
        // 调用SDK获取睡眠详细数据
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataDetail(id: selectedSleepId) { [weak self] (detail, error) in
            guard let self = self else { return }
            
            if error == .none {
                DispatchQueue.main.async {
                    self.sleepDetail = detail
                    self.addResult("✅ 成功获取睡眠详细数据", color: .green)
                    self.addResult("开始时间: \(self.formatDate(timestamp: detail.startTime))", color: .green)
                    self.addResult("结束时间: \(self.formatDate(timestamp: detail.endTime))", color: .green)
                    self.addResult("总睡眠时间: \(detail.totalTime)分钟", color: .green)
                    self.addResult("睡眠效率: \(Int(detail.sleepEfficiency * 100))%", color: .green)
                    self.addResult("睡眠得分: \(Int(detail.sleepScore))", color: .green)
                    self.addResult("睡眠类型: \(detail.type == 1 ? "长睡眠" : "短睡眠")", color: .green)
                }
            } else {
                DispatchQueue.main.async {
                    self.addResult("❌ 获取睡眠详细数据失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 获取睡眠分段数据
    func fetchSleepSegmentation() {
        guard selectedSleepId > 0 else {
            addResult("请先选择一个睡眠记录", color: .red)
            return
        }
        
        addResult("正在获取睡眠ID \(selectedSleepId) 的分段数据...", color: .blue)
        
        // 调用SDK获取睡眠分段数据
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepSegmentationData(id: selectedSleepId) { [weak self] (segment, error) in
            guard let self = self else { return }
            
            if error == .none {
                DispatchQueue.main.async {
                    // 创建一个CRPSleepModel实例来存储数据
                    let sleepModel = CRPSleepModel(deep: segment.deep, light: segment.light, rem: segment.rem, detail: [])
                    self.sleepSegmentation = sleepModel
                    
                    self.addResult("✅ 成功获取睡眠分段数据", color: .green)
                    self.addResult("深睡: \(segment.deep)分钟", color: .green)
                    self.addResult("浅睡: \(segment.light)分钟", color: .green)
                    self.addResult("REM: \(segment.rem)分钟", color: .green)
                    self.addResult("总睡眠: \(segment.deep + segment.light + segment.rem)分钟", color: .green)
                }
            } else {
                DispatchQueue.main.async {
                    self.addResult("❌ 获取睡眠分段数据失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 获取睡眠类型
    func fetchSleepType() {
        addResult("正在获取睡眠类型分析...", color: .blue)
        
        // 调用SDK获取睡眠类型
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepType { [weak self] (type, error) in
            guard let self = self else { return }
            
            if error == .none {
                DispatchQueue.main.async {
                    self.sleepType = type
                    self.addResult("✅ 成功获取睡眠类型分析", color: .green)
                    
                    // 睡眠状态
                    let stateText: String
                    switch type.state {
                    case 0: stateText = "长睡眠数据收集进行中"
                    case 1: stateText = "已收集一天长睡眠数据，有基本时间类型指标"
                    case 2: stateText = "在一天内收集的长睡眠数据，但有睡眠片段"
                    case 3: stateText = "连续7天收集4天或更多天的长睡眠数据，具有较高置信度"
                    case 4: stateText = "连续7天收集长睡眠数据，具有较高置信度"
                    case 5: stateText = "连续7天收集长睡眠数据，但置信度较低"
                    case 71: stateText = "睡眠时间超过15小时"
                    case 72: stateText = "连续7天内未收集长期睡眠数据"
                    default: stateText = "未知状态(\(type.state))"
                    }
                    self.addResult("睡眠状态: \(stateText)", color: .green)
                    
                    // 睡眠类型
                    let typeText: String
                    switch type.type {
                    case 1: typeText = "蜂鸟型"
                    case 2: typeText = "猫头鹰型"
                    case 3: typeText = "云雀型"
                    default: typeText = "未知类型(\(type.type))"
                    }
                    self.addResult("睡眠类型: \(typeText)", color: .green)
                    
                    // 可靠性
                    let reliabilityText = type.reliability == 1 ? "高" : "低"
                    self.addResult("数据可靠性: \(reliabilityText)", color: .green)
                    
                    // 就寝时间
                    if type.bedtime > 0 {
                        let bedtimeHour = type.bedtime / 60
                        let bedtimeMinute = type.bedtime % 60
                        self.addResult("通常就寝时间: \(bedtimeHour):\(String(format: "%02d", bedtimeMinute))", color: .green)
                    }
                }
            } else {
                DispatchQueue.main.async {
                    self.addResult("❌ 获取睡眠类型失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 清空结果
    func clearResults() {
        results = []
        addResult("结果已清空", color: .blue)
    }
    
    /// 添加结果
    func addResult(_ text: String, color: Color = .primary) {
        DispatchQueue.main.async {
            self.results.insert((text: text, color: color), at: 0)
            
            // 限制保存的结果数量
            if self.results.count > 100 {
                self.results = Array(self.results.prefix(100))
            }
        }
    }
    
    /// 格式化时间戳
    func formatDate(timestamp: Int) -> String {
        let date = Date(timeIntervalSince1970: TimeInterval(timestamp))
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }
}

// MARK: - SDK 代理

extension GoMoreSleepTestViewModel: CRPManagerDelegate {
    func didState(_ state: CRPState) {}
    func didBluetoothState(_ state: CRPBluetoothState) {}
    func receiveSteps(_ model: CRPStepModel) {}
    func receiveHeartRate(_ heartRate: Int) {}
    func receiveRealTimeHeartRate(_ heartRate: Int) {}
    func receiveHRV(_ hrv: Int) {}
    func receiveSpO2(_ o2: Int) {}
    func receiveOTA(_ state: CRPOTAState, _ progress: Int) {}
    func receiveStress(_ stress: Int) {}
    
    // 接收睡眠数据列表回调
    func receiveSleepList(_ list: [CRPGoMoreSleepRecord]) {
        DispatchQueue.main.async {
            self.sleepRecords = list
            self.addResult("✅ 收到睡眠数据列表回调", color: .green)
            self.addResult("共获取 \(list.count) 条睡眠记录", color: .green)
            
            if !list.isEmpty {
                // 默认选择第一条记录
                self.selectedSleepId = list.first?.id ?? 0
                
                // 显示前五条数据的基本信息
                let displayCount = min(5, list.count)
                self.addResult("显示前 \(displayCount) 条数据:", color: .blue)
                
                for i in 0..<displayCount {
                    let record = list[i]
                    self.addResult("记录 #\(i+1): ID=\(record.id), 时间=\(self.formatDate(timestamp: record.startTime))", color: .secondary)
                }
                
                if list.count > 5 {
                    self.addResult("... 还有 \(list.count - 5) 条记录", color: .secondary)
                }
            } else {
                self.addResult("⚠️ 没有找到睡眠记录", color: .orange)
            }
        }
    }
}

// MARK: - 预览
struct GoMoreSleepTestView_Previews: PreviewProvider {
    static var previews: some View {
        GoMoreSleepTestView()
    }
} 
