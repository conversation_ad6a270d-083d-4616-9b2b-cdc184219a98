import SwiftUI
import Combine
import CRPSmartRing

/// 压力API测试视图模型
class StressAPITestViewModel: NSObject, ObservableObject {
    // MARK: - 属性
    @Published var results: [StressTestResult] = []
    @Published var isLoading: Bool = false
    @Published var stressValue: Int = 0
    @Published var isMeasuringStress: Bool = false
    @Published var showShareSheet: Bool = false
    @Published var shareText: String = ""
    @Published var receivedNotification: Bool = false
    
    // 设备服务
    let deviceService = WindRingDeviceService.shared
    
    // 通知中心
    private var cancellables = Set<AnyCancellable>()
    
    // 单例
    static let shared = StressAPITestViewModel()
    
    // MARK: - 初始化方法
    override init() {
        super.init()
        // 设置通知监听
        setupNotifications()
    }
    
    // 设置通知监听
    private func setupNotifications() {
        // 监听压力测量通知
        NotificationCenter.default.publisher(for: .stressMeasured)
            .sink { [weak self] notification in
                print("【调试】收到压力通知: \(notification)")
                
                if let value = notification.userInfo?["value"] as? Int {
                    print("【调试】从userInfo中获取压力值: \(value)")
                    DispatchQueue.main.async {
                        self?.handleStressUpdate(value)
                    }
                } else if let value = notification.object as? Int {
                    print("【调试】从object中获取压力值: \(value)")
                    DispatchQueue.main.async {
                        self?.handleStressUpdate(value)
                    }
                } else {
                    print("【调试】收到压力通知但无法获取压力值: userInfo=\(String(describing: notification.userInfo)), object=\(String(describing: notification.object))")
                }
            }
            .store(in: &cancellables)
        
        // 监听压力测量超时通知
        NotificationCenter.default.publisher(for: .stressMeasurementTimeout)
            .sink { [weak self] _ in
                DispatchQueue.main.async {
                    self?.isLoading = false
                    self?.isMeasuringStress = false
                    self?.addResult("压力测量超时，请重试", color: .red)
                }
            }
            .store(in: &cancellables)
            
        print("【调试】已设置压力通知监听")
    }
    
    // 处理压力更新
    private func handleStressUpdate(_ value: Int) {
        self.isLoading = false
        self.isMeasuringStress = false
        self.stressValue = value
        self.receivedNotification = true
        
        if value == 0 || value == 255 {
            addResult("压力测量已中断", color: .orange)
        } else {
            let stressLevel = interpretStress(value)
            addResult("接收到压力测量结果: \(value) (\(stressLevel.0))", color: stressLevel.1)
        }
    }
    
    // MARK: - 结果管理方法
    
    /// 添加测试结果
    func addResult(_ message: String, color: Color = .primary) {
        let result = StressTestResult(message: message, color: color)
        DispatchQueue.main.async {
            self.results.insert(result, at: 0)
        }
    }
    
    /// 清除结果
    func clearResults() {
        DispatchQueue.main.async {
            self.results.removeAll()
        }
    }
    
    /// 导出测试结果
    func exportResults() {
        var text = "压力API测试结果\n"
        text += "测试时间: \(Date())\n"
        text += "设备连接状态: \(deviceService.connectionState.description)\n\n"
        text += "测试记录:\n"
        
        for (index, result) in results.enumerated() {
            text += "\(index + 1). \(result.message)\n"
        }
        
        shareText = text
        showShareSheet = true
    }
    
    // MARK: - 压力测量方法
    
    /// 开始单次压力测量
    func startSingleStressMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法进行压力测量", color: .red)
            return
        }
        
        clearResults()
        addResult("开始单次压力测量...", color: .blue)
        isLoading = true
        isMeasuringStress = true
        receivedNotification = false
        
        print("【调试】发送开始压力测量请求")
        deviceService.startStressMeasurement()
    }
    
    /// 停止单次压力测量
    func stopSingleStressMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法停止压力测量", color: .red)
            return
        }
        
        addResult("停止压力测量...", color: .blue)
        deviceService.stopStressMeasurement()
        isMeasuringStress = false
        isLoading = false
    }
    
    /// 获取压力历史记录
    func getStressHistory() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取压力历史", color: .red)
            return
        }
        
        addResult("正在获取压力历史记录...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getStressRecord { [weak self] records, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    if records.isEmpty {
                        self?.addResult("未找到压力历史记录", color: .orange)
                    } else {
                        let dateFormatter = DateFormatter()
                        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                        
                        self?.addResult("获取到\(records.count)条压力记录:", color: .green)
                        
                        // 显示最近5条记录
                        for (index, record) in records.prefix(5).enumerated() {
                            let date = Date(timeIntervalSince1970: TimeInterval(record.time))
                            let stressLevel = self?.interpretStress(record.stress).0 ?? "未知"
                            self?.addResult("记录\(index + 1): 压力值 \(record.stress) (\(stressLevel)), 时间: \(dateFormatter.string(from: date))")
                        }
                    }
                } else {
                    self?.addResult("获取压力历史记录失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 测试通知处理
    func testStressNotification() {
        let value = 60 // 模拟适中压力值
        
        // 构建通知
        let userInfo = ["value": value]
        NotificationCenter.default.post(
            name: .stressMeasured,
            object: nil,
            userInfo: userInfo
        )
        
        addResult("已发送测试压力通知: \(value)", color: .blue)
    }
    
    /// 解释压力值
    func interpretStress(_ value: Int) -> (String, Color) {
        if value == 0 || value == 255 {
            return ("无数据", .gray)
        }
        
        switch value {
        case 0...30:
            return ("放松", .green)
        case 31...60:
            return ("适中", .blue)
        case 61...80:
            return ("轻度压力", .yellow)
        case 81...100:
            return ("压力较大", .orange)
        case 101...255:
            return ("压力严重", .red)
        default:
            return ("未知", .gray)
        }
    }
}

// MARK: - 测试结果数据模型
struct StressTestResult: Identifiable {
    let id = UUID()
    let message: String
    let color: Color
    let timestamp = Date()
}

// MARK: - 压力API测试视图
struct StressAPITestView: View {
    @StateObject private var viewModel = StressAPITestViewModel.shared
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 压力显示区域
                VStack(spacing: 16) {
                    Text("压力测量")
                        .font(.headline)
                    
                    Text("\(viewModel.stressValue)")
                        .font(.system(size: 48, weight: .bold))
                        .foregroundColor(viewModel.interpretStress(viewModel.stressValue).1)
                        .padding()
                    
                    // 压力状态解释
                    if viewModel.stressValue > 0 && viewModel.stressValue != 255 {
                        let (interpretText, interpretColor) = viewModel.interpretStress(viewModel.stressValue)
                        
                        Text("压力状态: \(interpretText)")
                            .font(.headline)
                            .foregroundColor(interpretColor)
                            .padding(.vertical, 4)
                    }
                    
                    // 控制按钮
                    HStack(spacing: 16) {
                        Button(action: {
                            viewModel.startSingleStressMeasurement()
                        }) {
                            Text("开始测量")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                        .disabled(viewModel.isMeasuringStress || viewModel.isLoading)
                        
                        Button(action: {
                            viewModel.stopSingleStressMeasurement()
                        }) {
                            Text("停止测量")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(viewModel.isMeasuringStress ? Color.red : Color.gray)
                                .cornerRadius(10)
                        }
                        .disabled(!viewModel.isMeasuringStress)
                    }
                    
                    Button(action: {
                        viewModel.getStressHistory()
                    }) {
                        Text("获取压力历史记录")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .cornerRadius(10)
                    }
                    .disabled(viewModel.isLoading)
                    
                    // 测试通知按钮（用于调试）
                    Button(action: {
                        viewModel.testStressNotification()
                    }) {
                        HStack {
                            Image(systemName: "bell.fill")
                            Text("测试通知接收")
                        }
                        .font(.footnote)
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.purple)
                        .cornerRadius(10)
                    }
                }
                .padding()
                .background(Color(.systemGray5).opacity(0.5))
                .cornerRadius(15)
                
                // 压力说明区域
                VStack(alignment: .leading, spacing: 8) {
                    Text("压力评估标准")
                        .font(.headline)
                    
                    Divider()
                    
                    HStack {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.green)
                            .frame(width: 16, height: 16)
                        Text("0-30: 放松")
                            .font(.subheadline)
                    }
                    
                    HStack {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.blue)
                            .frame(width: 16, height: 16)
                        Text("31-60: 适中")
                            .font(.subheadline)
                    }
                    
                    HStack {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.yellow)
                            .frame(width: 16, height: 16)
                        Text("61-80: 轻度压力")
                            .font(.subheadline)
                    }
                    
                    HStack {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.orange)
                            .frame(width: 16, height: 16)
                        Text("81-100: 压力较大")
                            .font(.subheadline)
                    }
                    
                    HStack {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.red)
                            .frame(width: 16, height: 16)
                        Text("101+: 压力严重")
                            .font(.subheadline)
                    }
                }
                .padding()
                .background(Color(.systemGray5).opacity(0.5))
                .cornerRadius(15)
                
                // 测试结果区域
                VStack(alignment: .leading) {
                    HStack {
                        Text("测试结果")
                            .font(.headline)
                        
                        Spacer()
                        
                        Button(action: {
                            viewModel.clearResults()
                        }) {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                        }
                        
                        Button(action: {
                            viewModel.exportResults()
                        }) {
                            Image(systemName: "square.and.arrow.up")
                                .foregroundColor(.blue)
                        }
                    }
                    
                    Divider()
                    
                    if viewModel.results.isEmpty {
                        Text("暂无测试结果")
                            .foregroundColor(.gray)
                            .padding()
                            .frame(maxWidth: .infinity)
                    } else {
                        ForEach(viewModel.results) { result in
                            VStack(alignment: .leading, spacing: 4) {
                                Text(result.message)
                                    .foregroundColor(result.color)
                                
                                Text(result.timestamp, style: .time)
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                            .padding(.vertical, 4)
                            
                            Divider()
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray5).opacity(0.5))
                .cornerRadius(15)
                
                // 说明区域
                VStack(alignment: .leading, spacing: 8) {
                    Text("压力测量说明")
                        .font(.headline)
                    
                    Divider()
                    
                    Text("• 压力测量需要保持戒指佩戴稳定")
                        .font(.caption)
                    
                    Text("• 测量过程中尽量保持安静和放松")
                        .font(.caption)
                    
                    Text("• 压力测量结果仅供参考，不作为医疗诊断依据")
                        .font(.caption)
                        .foregroundColor(.red)
                    
                    Text("• 若压力长期处于较高水平，建议适当休息和调整")
                        .font(.caption)
                }
                .padding()
                .background(Color(.systemGray5).opacity(0.5))
                .cornerRadius(15)
            }
            .padding()
        }
        .navigationTitle("压力API测试")
        .overlay(
            ZStack {
                if viewModel.isLoading {
                    Color.black.opacity(0.3)
                        .edgesIgnoringSafeArea(.all)
                    
                    VStack {
                        ProgressView()
                            .scaleEffect(1.5)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        
                        Text(viewModel.isMeasuringStress ? "压力测量中..." : "加载中...")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.top, 10)
                    }
                    .padding(20)
                    .background(Color.black.opacity(0.7))
                    .cornerRadius(10)
                }
            }
        )
        .sheet(isPresented: $viewModel.showShareSheet) {
            StressActivityView(text: viewModel.shareText)
        }
        .onAppear {
            // 页面加载时
        }
    }
}

// MARK: - 活动视图（用于分享）
struct StressActivityView: UIViewControllerRepresentable {
    let text: String
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: [text],
            applicationActivities: nil
        )
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - 预览
struct StressAPITestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            StressAPITestView()
        }
    }
} 