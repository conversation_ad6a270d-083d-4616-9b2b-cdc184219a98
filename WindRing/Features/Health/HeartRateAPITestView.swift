import SwiftUI
import Combine
import CRPSmartRing

/// 心率API测试视图模型
class HeartRateAPITestViewModel: NSObject, ObservableObject {
    // MARK: - 属性
    @Published var results: [HeartRateTestResult] = []
    @Published var isLoading: Bool = false
    @Published var heartRate: Int = 0
    @Published var isMeasuringHeartRate: Bool = false
    @Published var selectedDay: Int = 0
    @Published var showShareSheet: Bool = false
    @Published var shareText: String = ""
    @Published var heartRateInterval: Int = 0
    
    // 设备服务
    let deviceService = WindRingDeviceService.shared
    
    // 通知中心
    private var cancellables = Set<AnyCancellable>()
    
    // 单例
    static let shared = HeartRateAPITestViewModel()
    
    // MARK: - 初始化方法
    override init() {
        super.init()
        // 监听心率变化的通知
        setupNotifications()
    }
    
    // 设置通知监听
    private func setupNotifications() {
        // 监听心率测量通知
        NotificationCenter.default.publisher(for: .heartRateMeasured)
            .sink { [weak self] notification in
                print("【调试】收到心率通知: \(notification)")
                
                if let heartRate = notification.userInfo?["value"] as? Int {
                    print("【调试】从userInfo中获取心率值: \(heartRate) bpm")
                    DispatchQueue.main.async {
                        self?.handleHeartRateUpdate(heartRate)
                    }
                } else if let heartRate = notification.object as? Int {
                    print("【调试】从object中获取心率值: \(heartRate) bpm")
                    DispatchQueue.main.async {
                        self?.handleHeartRateUpdate(heartRate)
                    }
                } else {
                    print("【调试】收到心率通知但无法获取心率值: userInfo=\(String(describing: notification.userInfo)), object=\(String(describing: notification.object))")
                }
            }
            .store(in: &cancellables)
            
        print("【调试】已设置心率通知监听")
    }
    
    // 处理心率更新
    private func handleHeartRateUpdate(_ heartRate: Int) {
        self.isLoading = false
        self.isMeasuringHeartRate = false
        self.heartRate = heartRate
        
        if heartRate == 0 || heartRate == 255 {
            addResult("心率测量已中断", color: .orange)
        } else {
            addResult("接收到心率测量结果: \(heartRate) bpm", color: .green)
        }
    }
    
    // MARK: - 结果管理方法
    
    /// 添加测试结果
    func addResult(_ message: String, color: Color = .primary) {
        let result = HeartRateTestResult(message: message, color: color)
        DispatchQueue.main.async {
            self.results.append(result)
        }
    }
    
    /// 清除结果
    func clearResults() {
        DispatchQueue.main.async {
            self.results.removeAll()
        }
    }
    
    /// 导出测试结果
    func exportResults() {
        var text = "心率API测试结果\n"
        text += "测试时间: \(Date())\n"
        text += "设备连接状态: \(deviceService.connectionState.description)\n\n"
        text += "测试记录:\n"
        
        for (index, result) in results.enumerated() {
            text += "\(index + 1). \(result.message)\n"
        }
        
        shareText = text
        showShareSheet = true
    }
    
    // MARK: - 心率测量方法
    
    /// 开始单次心率测量
    func startSingleHeartRateMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法进行心率测量", color: .red)
            return
        }
        
        clearResults()
        addResult("开始单次心率测量...", color: .blue)
        isLoading = true
        isMeasuringHeartRate = true
        
        print("【调试】发送开始心率测量请求")
        CRPSmartRingSDK.sharedInstance.setStartSingleHR()
    }
    
    /// 停止单次心率测量
    func stopSingleHeartRateMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法停止心率测量", color: .red)
            return
        }
        
        addResult("停止心率测量...", color: .blue)
        CRPSmartRingSDK.sharedInstance.setStopSingleHR()
        isMeasuringHeartRate = false
        isLoading = false
    }
    
    /// 获取心率测量历史
    func getHeartRateHistory() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取心率历史", color: .red)
            return
        }
        
        clearResults()
        addResult("获取心率测量历史...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getHeartRecordData { [weak self] records, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    self?.addResult("成功获取心率历史记录", color: .green)
                    self?.addResult("共获取到 \(records.count) 条记录", color: .blue)
                    
                    if records.isEmpty {
                        self?.addResult("没有心率历史记录", color: .orange)
                    } else {
                        // 最多显示10条记录，防止界面过长
                        let displayRecords = records.prefix(10)
                        for (index, record) in displayRecords.enumerated() {
                            let date = Date(timeIntervalSince1970: TimeInterval(record.time))
                            let dateFormatter = DateFormatter()
                            dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                            
                            self?.addResult("记录\(index + 1): 心率值 \(record.value) bpm, 时间: \(dateFormatter.string(from: date))")
                        }
                        
                        if records.count > 10 {
                            self?.addResult("... 更多记录未显示 ...", color: .gray)
                        }
                    }
                } else {
                    self?.addResult("获取心率历史记录失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 获取定时心率测量状态
    func getTimingHeartRateStatus() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取定时心率测量状态", color: .red)
            return
        }
        
        clearResults()
        addResult("获取定时心率测量状态...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getTimingHeartRateInterval { [weak self] interval, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    self?.heartRateInterval = interval
                    if interval == 0 {
                        self?.addResult("定时心率测量已关闭", color: .orange)
                    } else {
                        self?.addResult("定时心率测量已开启", color: .green)
                        self?.addResult("测量间隔: 每 \(interval * 5) 分钟", color: .blue)
                    }
                } else {
                    self?.addResult("获取定时心率测量状态失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 设置定时心率测量
    func setTimingHeartRate(interval: Int) {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法设置定时心率测量", color: .red)
            return
        }
        
        clearResults()
        
        if interval == 0 {
            addResult("正在关闭定时心率测量...", color: .blue)
        } else {
            addResult("正在设置定时心率测量: 每 \(interval * 5) 分钟", color: .blue)
        }
        
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.setTimingHeartRate(interval)
        
        // 延迟一小段时间再获取状态，确保设置已生效
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.getTimingHeartRateStatus()
        }
    }
    
    /// 获取指定日期的定时心率数据
    func getTimingHeartRateData(day: Int) {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取定时心率数据", color: .red)
            return
        }
        
        clearResults()
        addResult("获取第\(day)天的定时心率数据...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getTimingHeartRate(day) { [weak self] model, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    self?.addResult("成功获取定时心率数据", color: .green)
                    
                    // 检查是否有心率数据
                    let heartRates = model.hearts
                    if heartRates.isEmpty {
                        self?.addResult("没有定时心率数据记录", color: .orange)
                        return
                    }
                    
                    // 统计基本信息
                    let validHeartRates = heartRates.filter { $0 > 0 && $0 < 255 }
                    let count = validHeartRates.count
                    
                    if count == 0 {
                        self?.addResult("没有有效的心率数据", color: .orange)
                        return
                    }
                    
                    let minHR = validHeartRates.min() ?? 0
                    let maxHR = validHeartRates.max() ?? 0
                    let avgHR = validHeartRates.reduce(0, +) / count
                    
                    self?.addResult("有效心率数据点: \(count)/288", color: .blue)
                    self?.addResult("最低心率: \(minHR) bpm", color: .blue)
                    self?.addResult("最高心率: \(maxHR) bpm", color: .blue)
                    self?.addResult("平均心率: \(avgHR) bpm", color: .blue)
                    
                    // 显示部分数据点
                    self?.addResult("心率数据示例 (每隔1小时取一个点):", color: .gray)
                    for i in stride(from: 0, to: heartRates.count, by: 12) {
                        if i < heartRates.count {
                            let time = String(format: "%02d:%02d", (i / 12) % 24, (i % 12) * 5)
                            let value = heartRates[i]
                            if value > 0 && value < 255 {
                                self?.addResult("\(time): \(value) bpm")
                            } else {
                                self?.addResult("\(time): 无数据", color: .gray)
                            }
                        }
                    }
                } else {
                    self?.addResult("获取定时心率数据失败: \(error)", color: .red)
                }
            }
        }
    }
}

/// 测试结果模型
struct HeartRateTestResult: Identifiable {
    let id = UUID()
    let message: String
    let color: Color
    let timestamp = Date()
}

/// 心率API测试视图
struct HeartRateAPITestView: View {
    // MARK: - 属性
    @StateObject private var viewModel = HeartRateAPITestViewModel.shared
    @Environment(\.colorScheme) private var colorScheme
    
    // MARK: - 主视图
    var body: some View {
        List {
            // 当前心率显示
            heartRateSection
            
            // 单次心率测量
            singleHeartRateSection
            
            // 定时心率测量
            timingHeartRateSection
            
            // 历史数据
            historicalDataSection
            
            // 测试结果
            resultSection
        }
        .navigationTitle("心率API测试")
        .navigationBarItems(trailing: exportButton)
        .sheet(isPresented: $viewModel.showShareSheet) {
            HeartRateActivityViewController(activityItems: [viewModel.shareText])
        }
    }
    
    // MARK: - 视图组件
    
    /// 心率显示部分
    private var heartRateSection: some View {
        Section(header: Text("当前心率")) {
            HStack {
                Spacer()
                VStack {
                    Image(systemName: "heart.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.red)
                        .padding(.bottom, 5)
                    
                    Text("\(viewModel.heartRate)")
                        .font(.system(size: 48, weight: .bold))
                    
                    Text("BPM")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        
                    Text("通知状态: \(viewModel.heartRate > 0 ? "已接收" : "未接收")")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                Spacer()
            }
            .padding(.vertical, 20)
            
            // 调试按钮
            Button(action: {
                print("【调试】通知中心测试开始")
                // 使用与WindRingDeviceService一致的方式发送通知
                NotificationCenter.default.post(
                    name: .heartRateMeasured,
                    object: nil,
                    userInfo: ["value": 88]
                )
                print("【调试】已发送测试心率通知，值为88")
            }) {
                Text("测试通知 (设置心率为88)")
                    .font(.footnote)
                    .foregroundColor(.gray)
            }
        }
    }
    
    /// 单次心率测量部分
    private var singleHeartRateSection: some View {
        Section(header: Text("单次心率测量")) {
            if viewModel.isMeasuringHeartRate {
                Button(action: {
                    viewModel.stopSingleHeartRateMeasurement()
                }) {
                    HStack {
                        Spacer()
                        if viewModel.isLoading {
                            ProgressView()
                                .padding(.trailing, 10)
                        }
                        Text("停止测量")
                            .fontWeight(.semibold)
                            .foregroundColor(.red)
                        Spacer()
                    }
                }
                .padding(.vertical, 8)
            } else {
                Button(action: {
                    viewModel.startSingleHeartRateMeasurement()
                }) {
                    HStack {
                        Spacer()
                        Text("开始测量")
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                        Spacer()
                    }
                }
                .padding(.vertical, 8)
            }
            
            Button(action: {
                viewModel.getHeartRateHistory()
            }) {
                HStack {
                    Spacer()
                    Text("获取心率历史记录")
                        .fontWeight(.semibold)
                    Spacer()
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    /// 定时心率测量部分
    private var timingHeartRateSection: some View {
        Section(header: Text("定时心率测量")) {
            Button(action: {
                viewModel.getTimingHeartRateStatus()
            }) {
                HStack {
                    Spacer()
                    Text("获取定时心率测量状态")
                        .fontWeight(.semibold)
                    Spacer()
                }
            }
            .padding(.vertical, 8)
            
            HStack {
                Text("间隔设置:")
                Picker("", selection: $viewModel.heartRateInterval) {
                    Text("关闭").tag(0)
                    Text("5分钟").tag(1)
                    Text("10分钟").tag(2)
                    Text("15分钟").tag(3)
                    Text("30分钟").tag(6)
                    Text("1小时").tag(12)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            Button(action: {
                viewModel.setTimingHeartRate(interval: viewModel.heartRateInterval)
            }) {
                HStack {
                    Spacer()
                    Text("保存定时测量设置")
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    Spacer()
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    /// 历史数据部分
    private var historicalDataSection: some View {
        Section(header: Text("历史定时数据")) {
            HStack {
                Text("选择日期:")
                Picker("", selection: $viewModel.selectedDay) {
                    Text("今天").tag(0)
                    Text("昨天").tag(1)
                    Text("前天").tag(2)
                    Text("大前天").tag(3)
                    Text("4天前").tag(4)
                    Text("5天前").tag(5)
                    Text("6天前").tag(6)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            Button(action: {
                viewModel.getTimingHeartRateData(day: viewModel.selectedDay)
            }) {
                HStack {
                    Spacer()
                    Text("获取定时心率数据")
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    Spacer()
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    /// 测试结果部分
    private var resultSection: some View {
        Section(header: Text("测试结果")) {
            if viewModel.isLoading {
                HStack {
                    Spacer()
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                    Text("加载中...")
                        .padding(.leading, 10)
                    Spacer()
                }
                .padding(.vertical, 10)
            }
            
            if viewModel.results.isEmpty {
                Text("尚未执行任何测试")
                    .foregroundColor(.gray)
                    .italic()
                    .padding(.vertical, 8)
            } else {
                ForEach(viewModel.results) { result in
                    Text(result.message)
                        .foregroundColor(result.color)
                        .padding(.vertical, 2)
                }
                
                Button(action: {
                    viewModel.clearResults()
                }) {
                    HStack {
                        Spacer()
                        Text("清除结果")
                            .foregroundColor(.red)
                        Spacer()
                    }
                }
                .padding(.top, 8)
            }
        }
    }
    
    /// 导出按钮
    private var exportButton: some View {
        Button(action: {
            viewModel.exportResults()
        }) {
            Image(systemName: "square.and.arrow.up")
        }
        .disabled(viewModel.results.isEmpty)
    }
}

// MARK: - ActivityViewController
struct HeartRateActivityViewController: UIViewControllerRepresentable {
    var activityItems: [Any]
    var applicationActivities: [UIActivity]? = nil
    
    func makeUIViewController(context: UIViewControllerRepresentableContext<HeartRateActivityViewController>) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: applicationActivities)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: UIViewControllerRepresentableContext<HeartRateActivityViewController>) {}
}

// MARK: - 预览
struct HeartRateAPITestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            HeartRateAPITestView()
        }
    }
} 