import SwiftUI
import Combine
import Charts

// MARK: - ViewModel
class VitalSignsViewModel: ObservableObject {
    
    // 数据状态
    @Published var vitalsData: VitalsStatusData?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // 服务和取消存储
    private let apiService = APIService.shared.health
    private var cancellables = Set<AnyCancellable>()
    
    // 加载生命体征数据
    func loadVitalsData(for date: Date) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("加载生命体征数据，日期字符串格式: \(dateString)")
        
        isLoading = true
        errorMessage = nil
        
        apiService.getVitalsStatus(date: dateString)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                
                if case .failure(let error) = completion {
                    self?.errorMessage = error.localizedDescription
                    print("加载生命体征数据失败: \(error)")
                }
            } receiveValue: { [weak self] response in
//                if response.code == 0 {
                    self?.vitalsData = response
                    print("成功获取生命体征数据")
//                } else {
//                    self?.errorMessage = response.msg
//                    print("加载生命体征数据失败: \(response.msg)")
//                }
            }
            .store(in: &cancellables)
    }
    
    // 返回指定类型的数据记录
    func record(for type: Int) -> VitalRecord? {
        return vitalsData?.records.first { $0.type == type }
    }
    
    // 安全获取Int值，处理null情况
    func safeInt(_ value: Int?) -> Int {
        return value ?? 0
    }
    
    // 获取显著异常次数
    var significantTimes: Int {
        return vitalsData?.significantTimes ?? 0
    }
    
    // 获取轻微异常次数
    var minorTimes: Int {
        return vitalsData?.minorTimes ?? 0
    }
    
    // 清理资源
    deinit {
        cancellables.forEach { $0.cancel() }
    }
}

// 新的测量状态机
enum HeartRateMeasurementState {
    case idle
    case measuring
    case completed(value: Int, time: Date)
    
    static func == (lhs: HeartRateMeasurementState, rhs: HeartRateMeasurementState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.measuring, .measuring):
            return true
        case let (.completed(lv, lt), .completed(rv, rt)):
            return lv == rv && lt == rt
        default:
            return false
        }
    }
    var isIdle: Bool {
        if case .idle = self { return true }
        return false
    }
}

//struct HeartRatePoint: Identifiable {
//    let id = UUID()
//    let time: Date
//    let min: Int
//    let max: Int
//}

// MARK: - 心率ViewModel
class HeartRateViewModel: ObservableObject {
    // 数据状态
    @Published var heartRateData: HrVitalDetailsData?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // 心率范围和时间段信息
    @Published var heartRateRange: String = ""
    @Published var timeRange: String = ""
    
    // 曲线图数据点
    @Published var chartData: [HrVitalRecord] = []
    
    // Y-axis scale
    @Published var yAxisMax: Double = 90.0
    @Published var yAxisMin: Double = 0.0
    @Published var dataCenter: Double = 60.0
    
    // X-axis scale
    @Published var xAxisStart: Date = Date()
    @Published var xAxisEnd: Date = Date()
    
    @Published var measurementState: HeartRateMeasurementState = .idle
    @Published var selectedPoint: HrVitalRecord?
    
    // 测量状态
    @Published var measurementValue: Int = 0
    
    // 服务和取消存储
    private let apiService = APIService.shared.health
    private let deviceService = WindRingDeviceService.shared
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        // 监听心率测量通知
        setupNotifications()
    }
    
    // 设置通知监听
    private func setupNotifications() {
        // 监听实时心率值
        NotificationCenter.default.publisher(for: .heartRateMeasured)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notification in
                guard let self = self else { return }
                
                if let value = notification.userInfo?["value"] as? Int {
                    self.measurementValue = value
                }
            }
            .store(in: &cancellables)

        // 监听测量完成通知
        NotificationCenter.default.publisher(for: .heartRateDataUploaded)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.completeMeasurement()
            }
            .store(in: &cancellables)
    }
    
    // 加载心率数据
    func loadHeartRateData(for date: Date) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        // Set up the 24-hour window for the X-axis
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        self.xAxisStart = startOfDay
        self.xAxisEnd = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? startOfDay
        
        print("加载心率详情数据，日期字符串格式: \(dateString)")
        
        isLoading = true
        errorMessage = nil
        
        apiService.getHrToVitalDetails(date: dateString)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                
                if case .failure(let error) = completion {
                    self?.errorMessage = error.localizedDescription
                    print("加载心率详情数据失败: \(error)")
                }
            } receiveValue: { [weak self] response in
//                if response.code == 0 {
                    self?.heartRateData = response//.data
                    self?.processHeartRateData()
                    print("成功获取心率详情数据")
//                } else {
//                    self?.errorMessage = response.msg
//                    print("加载心率详情数据失败: \(response.msg)")
//                }
            }
            .store(in: &cancellables)
    }
    
    // 处理心率数据，计算范围和准备图表数据
    private func processHeartRateData() {
        guard let data = heartRateData, !data.records.isEmpty else {
            chartData = []
            heartRateRange = "N/A"
            timeRange = "N/A"
            yAxisMax = 90.0
            yAxisMin = 0.0
            dataCenter = 60.0
            return
        }
        
        // Calculate Y-axis scale
        let hearts = data.records.map { $0.hearts }
        
        let validHearts = hearts.filter { $0 > 0 }

        if let maxHR = validHearts.max(), let minHR = validHearts.min(), maxHR != minHR {
            let maxVal = Double(maxHR)
            let minVal = Double(minHR)
            
            let range = maxVal - minVal
            let padding = range * 0.1 // 可调整间距
            
            self.yAxisMax = maxVal + padding
            self.yAxisMin = minVal - padding
            self.dataCenter = (minVal + maxVal) / 2.0
        } else if let hr = validHearts.first {
            let val = Double(hr)
            self.yAxisMax = val + 10
            self.yAxisMin = max(0, val - 10)
            self.dataCenter = val
        } else {
            // 默认值
            self.yAxisMax = 90.0
            self.yAxisMin = 50.0
            self.dataCenter = 70.0
        }
        
        // 计算心率范围
        if let min = hearts.min(), let max = hearts.max() {
            heartRateRange = "\(min)-\(max) bpm"
        } else {
            heartRateRange = "N/A"
        }
        
        // 计算时间范围
        if let firstRecord = data.records.first, let lastRecord = data.records.last {
            // 转换毫秒时间戳为Date对象
            let firstDate = Date(timeIntervalSince1970: TimeInterval(firstRecord.time) / 1000.0)
            let lastDate = Date(timeIntervalSince1970: TimeInterval(lastRecord.time) / 1000.0)
            
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "HH:mm"
            timeRange = "\(displayFormatter.string(from: firstDate))-\(displayFormatter.string(from: lastDate))"
        } else {
            timeRange = "--"
        }
        
        // 准备图表数据
        chartData = data.records
//        prepareChartData()
    }
    
    // 准备心率曲线图表数据
    private func prepareChartData() {
        guard let data = heartRateData, !data.records.isEmpty else {
            self.chartData = []
            return
        }
        
        let calendar = Calendar.current
        let startOfDay = self.xAxisStart
        
        // Create 48 buckets for every 30 minutes in a day
        var buckets: [[Int]] = Array(repeating: [], count: 48)
        
        for record in data.records {
            let recordDate = Date(timeIntervalSince1970: TimeInterval(record.time) / 1000.0)
            let hour = calendar.component(.hour, from: recordDate)
            let minute = calendar.component(.minute, from: recordDate)
            
            if minute < 0 || minute > 59 { continue }
            
            let index = hour * 2 + (minute / 30)
            
            if index >= 0 && index < 48 {
                buckets[index].append(record.hearts)
            }
        }
        
        var aggregatedData: [HrVitalRecord] = []
//        for (index, bucket) in buckets.enumerated() {
//            if !bucket.isEmpty, let minVal = bucket.min(), let maxVal = bucket.max() {
//                if let bucketDate = calendar.date(byAdding: .minute, value: index * 30, to: startOfDay) {
//                    aggregatedData.append(HrVitalRecord(time: bucketDate, min: minVal, max: maxVal))
//                }
//            }
//        }
        
//        self.chartData = aggregatedData
    }
    
    // 获取最新心率值
    var latestHeartRate: Int {
        return heartRateData?.latestHearts ?? 0
    }
    
    var latestHeartRateTime: Int64? {
        return heartRateData?.latestTime
    }
    
    // 获取平均心率值
    var averageHeartRate: Int {
        return heartRateData?.avg ?? 0
    }
    
    // 开始或停止心率测量
    func toggleHeartRateMeasurement() {
        if case .measuring = measurementState {
            stopMeasurement()
        } else {
            startMeasurement()
        }
    }
    
    // 开始心率测量
    func startMeasurement() {
        // 确保当前是空闲状态
        guard case .idle = measurementState else { return }
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            errorMessage = "error_connect_device_first".localized
            return
        }
        
        // 检查佩戴状态
        guard deviceService.wearingState == 1 else {
            errorMessage = "error_wear_device_first".localized
            return
        }
        
        // 开始测量前清除错误信息
        errorMessage = nil
        
        // 开始测量
        deviceService.startHeartRateMeasurement()
        measurementState = .measuring
        measurementValue = 0 // 重置测量值
        
        // 设置超时保护（30秒）
        DispatchQueue.main.asyncAfter(deadline: .now() + 30) { [weak self] in
            // 如果30秒后仍在测量，自动取消
            if case .measuring = self?.measurementState {
                self?.cancelMeasurement(reason: "error_hr_measurement_timeout".localized)
            }
        }
    }
    
    // 完成测量
    private func completeMeasurement() {
        guard case .measuring = measurementState else { return }
        
        if measurementValue > 0 {
            measurementState = .completed(value: measurementValue, time: Date())
        } else {
            // 如果值无效，则视为取消
            cancelMeasurement(reason: "error_invalid_hr_reading".localized)
        }
        
        // 无论如何都停止设备测量
        deviceService.stopHeartRateMeasurement()
    }
    
    // 停止心率测量
    func stopMeasurement() {
        guard case .measuring = measurementState else { return }
        
        deviceService.stopHeartRateMeasurement()
        measurementState = .idle
        
        // 如果测量值为0，可能是测量失败
        if measurementValue == 0 {
            errorMessage = "error_invalid_hr_reading".localized
        }
    }
    
    // 取消或关闭弹窗
    func cancelMeasurement(reason: String? = nil) {
        if case .measuring = measurementState {
            deviceService.stopHeartRateMeasurement()
        }
        measurementState = .idle
        
        if let reason = reason {
            errorMessage = reason
        }
    }
    
    // 清理资源
    deinit {
        if case .measuring = measurementState {
            deviceService.stopHeartRateMeasurement()
        }
        cancellables.forEach { $0.cancel() }
    }
}



struct VitalSignsDetailView: View {
    // API基础URL
//    private let apiBaseURL = "http://ring-api-dev.weaving-park.com"
    // 状态变量
    @Environment(\.presentationMode) var presentationMode
    // 添加日期状态管理
    @State private var selectedDate = Date()
    @State private var showDatePicker = false
    @State private var selectedDay: Int = 0 // 当前选中的日偏移量（0表示今天）
    
    @StateObject private var vitalSignsViewModel = VitalSignsViewModel()
    @StateObject private var heartRateViewModel = HeartRateViewModel()
    @StateObject private var spo2ViewModel = SpO2ViewModel() // 使用已有的SpO2ViewModel
    @StateObject private var hrvViewModel = HRVViewModel() // 添加HRV视图模型
    @StateObject private var temperatureViewModel = TemperatureViewModel() // 添加体温视图模型
    @State private var selectedMetricType: Int? = nil
    // 添加设备服务访问
    private let deviceService = WindRingDeviceService.shared
    
    // 蓝牙连接状态
    @State private var bluetoothState: BluetoothConnectionState = .disconnected
    @State private var isShowingBluetoothAlert = false // 控制蓝牙操作提示框
    @State private var shouldCancelConnection = false // 取消连接标志
    @State private var connectionCheckTimer: AnyCancellable? // 连接状态检查计时器
    @State private var connectedDeviceName = "" // 连接的设备名称
    @State private var showConnectedToast = false // 显示连接成功提示
    
    // 添加用于动画的状态变量
    @State private var animationValue: Double = 0
    
    // 添加HRV健康建议弹窗状态
    @State private var isShowingHRVAdvice: Bool = false
    
    // 使用AppStorage直接读取与AuthService相同的token
    @AppStorage("auth_token") private var authToken: String?
    // 添加日历数据状态
    @State private var calendarDatesWithData: [Date] = []
    @State private var isLoadingCalendarData = false
    
    // 计算文字的透明度 - 根据与中心日期的距离
    private func calculateOpacity(dayOffset: Int) -> Double {
        let distance = abs(dayOffset - selectedDay)
        if distance == 0 {
            return 1.0 // 选中项保持完全不透明
        } else if distance == 1 {
            return 0.8 // 相邻项稍微透明
        } else if distance == 2 {
            return 0.6 // 再远一点的项更透明
        } else {
            return 0.4 // 最远的项最透明
        }
    }
    
    // 计算文字的缩放 - 根据与中心日期的距离
    private func calculateScale(dayOffset: Int) -> CGFloat {
        let distance = abs(dayOffset - selectedDay)
        if distance == 0 {
            return 1.0 // 选中项不再放大，保持原始大小
        } else if distance == 1 {
            return 1.0 // 相邻项保持原始大小
        } else if distance == 2 {
            return 0.9 // 再远一点的项缩小
        } else {
            return 0.8 // 最远的项最小
        }
    }
    
    // 常量定义
    let cardBackground = Color.moduleBackground // 使用系统定义的模块背景色 #10131A
    let primaryBlue = Color(red: 0.0, green: 0.4, blue: 1.0)
    let highlightCyan = Color(red: 0.0, green: 0.9, blue: 0.9)
    let pageBackground = Color.black
    
    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏
            HStack {
                // 返回按钮
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.white)
                            .font(.system(size: 16))
                        
                        Text("vital_signs_detail_nav_title".localized)
                            .foregroundColor(.white)
                            .font(.system(size: 16, weight: .bold))
                    }
                }
                
                Spacer()
                
                // 右侧图标按钮
                HStack(spacing: 16) {
                    // 日历选择器
                    Button(action: {
                        withAnimation {
                            showDatePicker.toggle()
                            
                            // 当打开日历时获取日历数据
                            if showDatePicker {
                                fetchCalendarData()
                            }
                        }
                    }) {
                        HStack(spacing: 6) {
                            // 使用项目中的日历图标
                            Image(systemName: "calendar")
                                .foregroundColor(.white)
                                .font(.system(size: 16))
                            
                            // 删除日期文本，只保留日历图标
                        }
                        .padding(.vertical, 5)
                        .padding(.horizontal, 8)
                    }
                    .padding(.leading, 10)
                    
                    // 蓝牙按钮
                    Button(action: {
                        // 蓝牙按钮点击逻辑
                        handleBluetoothButtonTap()
                    }) {
                        ZStack {
                            // 根据连接状态显示不同图标
                            if bluetoothState == .disconnected {
                                // 断开连接时显示断连图标
                                Image("断连")
                                    .resizable()
                                    .frame(width: 16, height: 16)
                            } else if bluetoothState == .connected {
                                // 已连接状态显示连接状态图标 - 使用蓝色圆环
                                Image("连联")
                                    .resizable()
                                    .frame(width: 16, height: 16)
                            } else if bluetoothState == .connecting {
                                // 连接中状态的子视图
                                ConnectingStateView()
                                    .frame(width: 16, height: 16)
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 12)
            .padding(.bottom, 8)
            
            // 日期选择器
            DateSelectionView(
//                selectedDay: $selectedDay,
                selectedDate: $selectedDate,
                onDateSelected: { day, date in
                    // 处理日期选择事件
                    print("选择了日期：\(date), 偏移量：\(day)")
                    vitalSignsViewModel.loadVitalsData(for: date)
                    heartRateViewModel.loadHeartRateData(for: date)
                    spo2ViewModel.loadSpO2Data(for: date)
                    hrvViewModel.loadHRVData(for: date)
                    temperatureViewModel.loadTemperatureData(for: date)
                }
            )
            .frame(height: 50)
            .padding(.bottom, 8)
    
            // 可滚动内容区域
            ScrollView {
                VStack(spacing: 16) {
                    // 加载状态指示器
                    if vitalSignsViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(1.2)
                            .frame(maxWidth: .infinity, minHeight: 100)
                    }
                    
                    // 错误信息
                    if let errorMessage = vitalSignsViewModel.errorMessage {
                        VStack(spacing: 8) {
                            Image(systemName: "exclamationmark.triangle")
                                .foregroundColor(.red)
                                .font(.system(size: 20))
                            
                            Text(errorMessage)
                                .foregroundColor(.red)
                                .font(.system(size: 14))
                                .multilineTextAlignment(.center)
                            
                            Button(action: {
                                // 重试按钮
                                vitalSignsViewModel.loadVitalsData(for: selectedDate)
                                heartRateViewModel.loadHeartRateData(for: selectedDate)
                                spo2ViewModel.loadSpO2Data(for: selectedDate)
                                hrvViewModel.loadHRVData(for: selectedDate)
                                temperatureViewModel.loadTemperatureData(for: selectedDate)
                            }) {
                                Text("button_retry".localized)
                                    .foregroundColor(.white)
                                    .font(.system(size: 14, weight: .medium))
                                    .padding(.horizontal, 20)
                                    .padding(.vertical, 8)
                                    .background(Color.blue)
                                    .cornerRadius(8)
                            }
                            .padding(.top, 8)
                        }
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.black.opacity(0.5))
                        .cornerRadius(10)
                    }
                    
                    // 数据为空的全局提示
                    if !vitalSignsViewModel.isLoading && vitalSignsViewModel.errorMessage == nil && vitalSignsViewModel.vitalsData == nil {
                        VStack(spacing: 12) {
                            Image(systemName: "heart.text.square")
                                .foregroundColor(.gray)
                                .font(.system(size: 40))
                            
                            Text("vital_signs_no_data_today".localized)
                                .foregroundColor(.gray)
                                .font(.system(size: 16))
                            
                            Text("vital_signs_wear_device_prompt".localized)
                                .foregroundColor(.gray.opacity(0.8))
                                .font(.system(size: 14))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 20)
                        }
                        .padding(.vertical, 40)
                        .frame(maxWidth: .infinity)
                    }
                    
                    // Vitals Status卡片
                    vitalsStatusCard
                    
                    // Heart Rate卡片
                    heartRateCard
                    //暂时隐藏
                    // SpO2卡片
//                    spo2Card
                    //
                    // HRV卡片
//                    hrvCard
                    
                    // 体温卡片
                    temperatureCard
                    
                    // 底部留白
                    Spacer().frame(height: 20)
                }
                .padding(.horizontal, 16)
            }
        }
        .background(pageBackground.edgesIgnoringSafeArea(.all))
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
        .sheet(isPresented: $isShowingHRVAdvice) {
            // HRV健康建议弹窗
            VStack(spacing: 20) {
                // 标题
                Text("hrv_advice_title".localized)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                
                // 当前状态
                HStack {
                    Text("hrv_advice_current_status".localized)
                        .font(.system(size: 16))
                        .foregroundColor(.gray)
                    
                    Text(hrvViewModel.healthStatusDescription)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(hrvViewModel.healthStatusColor)
                }
                
                // 分隔线
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: 1)
                
                // 健康建议
                VStack(alignment: .leading, spacing: 12) {
                    Text("hrv_advice_suggestion".localized)
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                    
                    Text(hrvViewModel.healthAdvice)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.leading)
                    
                    // HRV解释
                    VStack(alignment: .leading, spacing: 8) {
                        Text("hrv_advice_what_is_hrv".localized)
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                        
                        Text("hrv_advice_explanation_1".localized)
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.leading)
                        
                        Text("hrv_advice_explanation_2".localized)
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.leading)
                    }
                    .padding(.top, 16)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                
                Spacer()
                
                // 关闭按钮
                Button(action: {
                    isShowingHRVAdvice = false
                }) {
                    Text("sleep_close_button".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.blue)
                        .cornerRadius(12)
                }
            }
            .padding(20)
            .background(Color.black.edgesIgnoringSafeArea(.all))
        }
        .onAppear {
            // 页面出现时加载数据
            vitalSignsViewModel.loadVitalsData(for: selectedDate)
            heartRateViewModel.loadHeartRateData(for: selectedDate)
            spo2ViewModel.loadSpO2Data(for: selectedDate)
            hrvViewModel.loadHRVData(for: selectedDate)
            temperatureViewModel.loadTemperatureData(for: selectedDate)
            
            // 同步蓝牙状态
            syncBluetoothState()
            startConnectionStateChecking()
        }
        .onDisappear {
            // 离开页面时停止所有测量

            if !heartRateViewModel.measurementState.isIdle {
                heartRateViewModel.stopMeasurement()
            }
            if !spo2ViewModel.measurementState.isIdle {
                spo2ViewModel.cancelMeasurement()
            }
            if hrvViewModel.isMeasuring {
                hrvViewModel.stopMeasurement()
            }
            
            // 取消计时器
            connectionCheckTimer?.cancel()
        }
        // 日历选择器浮层
        .overlay(
                ZStack {
                if showDatePicker {
                    // 磨砂半透明背景
                    Color.black.opacity(0.8)
                        .ignoresSafeArea()
                        .blur(radius: 5)
                                .onTapGesture {
                            closeCalendarPicker()
                        }
                    
                    // 自定义日历视图
                    CustomCalendarView(
                        selectedDate: $selectedDate,
//                        selectedDay: $selectedDay,
                        onClose: closeCalendarPicker
                    )
                    .frame(width: UIScreen.main.bounds.width * 0.85)
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: showDatePicker)
        )
        // 添加连接成功提示
        .overlay(
            VStack {
                if showConnectedToast {
                    Spacer().frame(height: 80)
                    
                    HStack(spacing: 10) {
                        Image("连联")
                            .resizable()
                            .frame(width: 18, height: 18)
                        
                        Text(String(format: "insight_connected_to_device".localized, connectedDeviceName))
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                    }
                    .padding(.vertical, 8)
                    .padding(.horizontal, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.black.opacity(0.7))
                    )
                    .transition(.move(edge: .top).combined(with: .opacity))
                    
                    Spacer()
                }
            }
            .animation(.easeInOut, value: showConnectedToast)
        )
        .alert(isPresented: $isShowingBluetoothAlert) {
            Alert(
                title: Text("insight_disconnect_alert_title".localized),
                message: Text("insight_disconnect_alert_message".localized),
                primaryButton: .destructive(Text("insight_disconnect_button_title".localized)) {
                    disconnectBluetooth()
                },
                secondaryButton: .cancel(Text("cancel".localized))
            )
        }
        .overlay(
            ZStack {
                // 根据状态显示不同内容
                if case .idle = heartRateViewModel.measurementState {
                    // 空闲时不显示任何内容
                    EmptyView()
                } else {
                    // 测量中或完成后显示遮罩和弹窗
                    Color.black.opacity(0.6).ignoresSafeArea()
                        .onTapGesture {
                            heartRateViewModel.cancelMeasurement()
                        }
                    
                    HeartRateMeasurementPopup(viewModel: heartRateViewModel)
                        .transition(.scale.combined(with: .opacity))
                }
            }
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: !heartRateViewModel.measurementState.isIdle)
        )
        .overlay(
            ZStack {
                if !spo2ViewModel.measurementState.isIdle {
                    Color.black.opacity(0.6).ignoresSafeArea()
                        .onTapGesture {
                            spo2ViewModel.cancelMeasurement()
                        }
                    
                    SpO2MeasurementPopup(viewModel: spo2ViewModel)
                        .transition(.scale.combined(with: .opacity))
                }
            }
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: !spo2ViewModel.measurementState.isIdle)
        )
        
    }
    
    // MARK: - 组件视图
    
    // Vitals Status卡片
    var vitalsStatusCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 动态标题
            VStack(alignment: .leading, spacing: 12) {
                if let metricType = selectedMetricType {
                    let metrics: [(type: Int, icon: String, labelKey: String)] = [
                        (1, "heart", "heart_rate"),
                        (2, "drop", "vital_signs_metric_spo2"),
                        (3, "waveform.path.ecg", "hrv"),
                        (4, "thermometer", "body_temperature")
                    ]
                    let selectedMetric = metrics.first { $0.type == metricType }!
                    HStack {
                        Image(systemName: selectedMetric.icon)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                        Text(selectedMetric.labelKey.localized)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                        Spacer()
                    }
                } else {
                    HStack {
                        Text("vital_signs_status_title".localized)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                        Button(action: {}) {
                            Image(systemName: "questionmark.circle")
                                .font(.system(size: 16))
                                .foregroundColor(.gray)
                        }
                        Spacer()
                    }
                }
            }
            .padding(.top, 16)
            .padding(.horizontal, 16)

            // 动态显示数据
            if let metricType = selectedMetricType, let record = vitalSignsViewModel.record(for: metricType) {
                HStack(spacing: 10) {
                    metricValueDisplay(title: "vital_signs_minor_outlier".localized, count: record.low ?? 0)
                    metricValueDisplay(title: "vital_signs_significant_outlier".localized, count: record.high ?? 0)
                    metricValueDisplay(title: "vital_signs_level_normal".localized, count: record.normal ?? 0)
                }
                .padding(.horizontal, 16)
            } else {
                // 异常指标 - 原始视图
                HStack(spacing: 10) {
                    metricValueDisplay(title: "vital_signs_significant_outlier".localized, count: vitalSignsViewModel.significantTimes)
                    metricValueDisplay(title: "vital_signs_minor_outlier".localized, count: vitalSignsViewModel.minorTimes)
                }
                .padding(.horizontal, 16)
            }
            
            // 等级指标条和底部图标 - 新设计
            VStack(spacing: 0) {
                let indicatorGrid = VStack(spacing: 0) {
                    Divider().background(Color.gray.opacity(0.3))
                    
                    levelIndicatorRow(
                        level: "vital_signs_level_high".localized,
                        color: Color.blue,
                        recordProvider: { vitalSignsViewModel.record(for: $0)?.high },
                        selectedMetricType: selectedMetricType
                    )
                    
                    Divider().background(Color.gray.opacity(0.3))
                    
                    levelIndicatorRow(
                        level: "vital_signs_level_normal".localized,
                        color: Color.cyan,
                        recordProvider: { vitalSignsViewModel.record(for: $0)?.normal },
                        selectedMetricType: selectedMetricType
                    )
                    
                    Divider().background(Color.gray.opacity(0.3))
                    
                    levelIndicatorRow(
                        level: "vital_signs_level_low".localized,
                        color: Color.green,
                        recordProvider: { vitalSignsViewModel.record(for: $0)?.low },
                        selectedMetricType: selectedMetricType
                    )
                }
                
                indicatorGrid
                    .background(
                        GeometryReader { geometry in
                            Color.clear
                                .contentShape(Rectangle())
                                .gesture(
                                    DragGesture(minimumDistance: 0, coordinateSpace: .local)
                                        .onChanged { value in
                                            // 指标区域从x=50开始
                                            let indicatorAreaX = value.location.x - 50
                                            let indicatorAreaWidth = geometry.size.width - 50
                                            
                                            // 如果在标签区域，则清除选择
                                            guard indicatorAreaX >= 0 else {
                                                if selectedMetricType != nil {
                                                    selectedMetricType = nil
                                                }
                                                return
                                            }
                                            
                                            let columnWidth = indicatorAreaWidth / 4
                                            let columnIndex = Int(indicatorAreaX / columnWidth)
                                            
                                            if (0..<4).contains(columnIndex) {
                                                let metricType = columnIndex + 1 // 指标类型从1开始
                                                if selectedMetricType != metricType {
                                                    withAnimation(.easeInOut(duration: 0.1)) {
                                                        selectedMetricType = metricType
                                                    }
                                                }
                                            }
                                        }
                                        .onEnded { _ in
                                            withAnimation(.easeInOut(duration: 0.2)) {
                                                selectedMetricType = nil
                                            }
                                        }
                                )
                        }
                    )
                
                Divider().background(Color.gray.opacity(0.3))
                
                // 底部图标和标签 - 移除手势
                let metrics: [(type: Int, icon: String, labelKey: String)] = [
                    (1, "heart", "heart_rate"),
                    (2, "drop", "vital_signs_metric_spo2"),
                    (3, "waveform.path.ecg", "hrv"),
                    (4, "thermometer", "body_temperature")
                ]

                HStack(alignment: .top, spacing: 0) {
                    Spacer().frame(width: 50)
                    
                    HStack(alignment: .top, spacing: 0) {
                        ForEach(metrics, id: \.type) { metric in
                            metricIconLabel(
                                icon: metric.icon,
                                label: metric.labelKey.localized,
                                isSelected: selectedMetricType == metric.type
                            )
                        }
                    }
                }
                .padding(.top, 16)
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(cardBackground)
        .cornerRadius(24)
    }
    
    // MARK: - Vitals Status Card Helpers
    
    @ViewBuilder
    private func levelIndicatorRow(level: String, color: Color, recordProvider: (Int) -> Int?, selectedMetricType: Int?) -> some View {
        HStack(spacing: 0) {
            Text(level)
                .font(.system(size: 12))
                .foregroundColor(.gray)
                .frame(width: 50, alignment: .leading)
            
            HStack(spacing: 0) {
                indicator(metric: 1, color: color, recordProvider: recordProvider, selectedMetricType: selectedMetricType).frame(maxWidth: .infinity)
                indicator(metric: 2, color: color, recordProvider: recordProvider, selectedMetricType: selectedMetricType).frame(maxWidth: .infinity)
                indicator(metric: 3, color: color, recordProvider: recordProvider, selectedMetricType: selectedMetricType).frame(maxWidth: .infinity)
                indicator(metric: 4, color: color, recordProvider: recordProvider, selectedMetricType: selectedMetricType).frame(maxWidth: .infinity)
            }
        }
        .frame(height: 60)
    }
    
    @ViewBuilder
    private func indicator(
        metric: Int,
        color: Color,
        recordProvider: (Int) -> Int?,
        selectedMetricType: Int?
    ) -> some View {
        let count = min(recordProvider(metric) ?? 0, 7)
        let isDimmed = selectedMetricType != nil && selectedMetricType != metric

        VStack {
            Spacer()
            
            VStack(spacing: 2) {
                ForEach(0..<count, id: \.self) { _ in
                    Capsule()
                        .fill(color)
                        .frame(width: 28, height: 4)
                        .cornerRadius(1)
                }
            }
            .frame(width: 28)
            .opacity(isDimmed ? 0.3 : 1.0)

            Spacer()
        }
    }
    
    @ViewBuilder
    private func metricIconLabel(icon: String, label: String, isSelected: Bool) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 16))
                .foregroundColor(isSelected ? .cyan : .white)
            
            Text(label)
                .font(.system(size: 10))
                .foregroundColor(isSelected ? .cyan : .gray)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(isSelected ? Color.cyan.opacity(0.15) : Color.clear)
        .cornerRadius(8)
    }

    @ViewBuilder
    private func metricValueDisplay(title: String, count: Int) -> some View {
        ZStack {
            LinearGradient(
                gradient: Gradient(colors: [Color(hex: "#141821"), Color(hex: "#2A3040")]),
                startPoint: .leading,
                endPoint: .trailing
            )
            .cornerRadius(10)

            VStack(spacing: 8) {
                Text(title)
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                
                HStack(spacing: 4) {
                    Text("\(count)")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                    
                    Text("vital_signs_unit_times".localized)
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                }
            }
        }
        .frame(height: 56)
        .frame(maxWidth: .infinity)
    }

    @ViewBuilder
    private func temperatureDetailBox(
        title: String,
        value: String,
        bgColor: Color,
        icon: String
    ) -> some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 16))
                .foregroundColor(.white)
                .padding(6)
                .background(Circle().fill(Color.white.opacity(0.15)))
            
            Text(title)
                .font(.system(size: 12))
                .foregroundColor(.white.opacity(0.7))
            
            Text(value)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(.white)
        }
        .frame(maxWidth: .infinity, maxHeight: 75)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(bgColor)
        )
    }

    
    // Heart Rate卡片
    var heartRateCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和信息图标
            HStack {
                Text("heart_rate".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.vitalHeartRate.rawValue)
                
//                Button(action: {}) {
//                    Image(systemName: "info.circle")
//                        .font(.system(size: 12))
//                        .foregroundColor(.gray)
//                }
                
                Spacer()
            }
            .padding(.top, 16)
            .padding(.horizontal, 16)
            
            // 最新值和平均值
            HStack(spacing: 10) {
                // Latest卡片
                ZStack {
                    // 渐变背景
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#141821"),
                            Color(hex: "#2A3040")
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    .cornerRadius(10)
                    
                    // 内容
                    VStack(alignment: .center, spacing: 4) {
                        Text(heartRateViewModel.selectedPoint == nil ? "vital_signs_latest".localized : "vital_signs_selected".localized)
                            .font(.system(size: 12))
                            .foregroundColor(.gray)
                        
                        Text("\(heartRateViewModel.selectedPoint?.maxHearts ?? heartRateViewModel.latestHeartRate) bpm")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.white)
                        if let timeInt = heartRateViewModel.selectedPoint?.time ?? heartRateViewModel.latestHeartRateTime, let time = Date(timeIntervalSince1970: TimeInterval(timeInt) / 1000) as Date? {
                            Text(time, style: .time)
                                .font(.system(size: 10))
                                .foregroundColor(.gray)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal, 0)
                }
                .frame(height: 56)
                .frame(maxWidth: .infinity)
                
                // Average卡片 - Heart Rate
                ZStack {
                    // 渐变背景
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#141821"),
                            Color(hex: "#2A3040")
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    .cornerRadius(10)
                    
                    // 内容
                    VStack(alignment: .center, spacing: 4) {
                        Text("vitals_history_detail_sheet_average".localized)
                            .font(.system(size: 12))
                            .foregroundColor(.gray)
                        
                        Text("\(heartRateViewModel.averageHeartRate) bpm")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal, 0)
                }
                .frame(height: 56)
                .frame(maxWidth: .infinity)
            }
            .padding(.horizontal, 16)
            
            // 范围标签
            HStack {
                Spacer()
                Text("\(heartRateViewModel.heartRateRange)  \(heartRateViewModel.timeRange)")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .padding(.vertical, 6)
                    .padding(.horizontal, 12)
                    .background(Color(hex: "#1A1D26"))
                    .cornerRadius(8)
                    .fixedSize(horizontal: true, vertical: false)
                Spacer()
            }
            .padding(.top, 16)
            
            // 心率图表
            if heartRateViewModel.isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)
                    .frame(maxWidth: .infinity)
                    .padding(.top, 8)
                    .padding(.bottom, 8)
            } else if let error = heartRateViewModel.errorMessage {
                Text(error)
                    .foregroundColor(.red)
                    .font(.system(size: 12))
                    .frame(maxWidth: .infinity)
                    .padding(.top, 8)
                    .padding(.bottom, 8)
            } else if heartRateViewModel.chartData.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "heart.slash")
                        .foregroundColor(.gray)
                        .font(.system(size: 24))
                    Text("vitals_no_hr_data".localized)
                        .foregroundColor(.gray)
                        .font(.system(size: 14))
                }
                .frame(maxWidth: .infinity)
                .frame(height: 100)
                .padding(.top, 8)
                .padding(.bottom, 8)
            } else {
                Chart {
                    ForEach(heartRateViewModel.chartData){point in
                        let date = Date(timeIntervalSince1970: TimeInterval(point.time) / 1000.0)
//                        let height = point.minHearts - point.maxHearts
                        if point.maxHearts > 0 {
                            let minY = point.minHearts
                            let maxY = point.maxHearts
                            let adjustedMaxY = (minY == maxY) ? (maxY + 1) : maxY
                            RectangleMark(
                                x: .value("Time", date),
                                yStart: .value("min", minY),
                                yEnd: .value("max", adjustedMaxY),
                                width: .fixed(4)
                            )
                            .foregroundStyle(Color(hex: "#C8B6F2"))
                            .cornerRadius(2)
                        }
//                        RectangleMark(
//                            x: .value("Time", date, unit: .minute),
//                            y: .value("BPM", point.min),
//                            width: .fixed(4),
//                            height: .value("Range", point.minHearts - point.maxHearts)
//                        )
//                            RectangleMark(yStart: .value("Min", point.minHearts),
//                                          yEnd: .value("Max", point.maxHearts))
                    }
                }
                .chartYScale(domain: heartRateViewModel.yAxisMin...heartRateViewModel.yAxisMax)
                .chartXScale(domain: heartRateViewModel.xAxisStart...heartRateViewModel.xAxisEnd)
                .chartYAxis {
                    AxisMarks(position: .trailing, values: [heartRateViewModel.yAxisMin, heartRateViewModel.dataCenter, heartRateViewModel.yAxisMax]) { value in
                        AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                        AxisValueLabel {
                            if let val = value.as(Double.self) {
                                Text("\(Int(val.rounded()))")
                                .font(.system(size: 10))
                                .foregroundColor(.gray)
                        }
                        }
                    }
                }
                .chartXAxis {
                    AxisMarks(values: .stride(by: .hour, count: 1)) { value in
                        if let date = value.as(Date.self) {
                            let hour = Calendar.current.component(.hour, from: date)
                            
                            // 判断是否为结束时间（模拟 24:00）
                            let isLastTick = Calendar.current.isDate(date, equalTo: heartRateViewModel.xAxisEnd, toGranularity: .minute)
                            
                            if [0, 6, 12, 18].contains(hour) || isLastTick {
                                AxisValueLabel {
                                    Text(isLastTick ? "24:00" : "\(String(format: "%02d", hour)):00")
                                .font(.system(size: 10))
                                        .foregroundColor(.gray.opacity(0.8))
                                }
                            }
                        }
                    }
                }
                .chartOverlay { proxy in
                    GeometryReader { geometry in
                        Rectangle().fill(.clear).contentShape(Rectangle())
                            .gesture(
                                DragGesture(minimumDistance: 0)
                                    .onChanged { value in
                                        updateSelectedHeartRatePoint(at: value.location, proxy: proxy, geometry: geometry)
                                    }
                                    .onEnded { _ in
                                        heartRateViewModel.selectedPoint = nil
                                    }
                            )
                    }
                }
                .overlay(alignment: .top) {
                    if let selectedPoint = heartRateViewModel.selectedPoint {
                        heartRateScrubberView(point: selectedPoint)
                            .padding(.top, -30) // Position above chart
                    }
                }
                .frame(height: 150)
            .padding(.top, 8)
            }
            
            // 高强度活动标注
            HStack {
                Spacer()
                HStack(spacing: 4) {
                    Circle()
                        .fill(Color.gray)
                        .frame(width: 8, height: 8)
                    Text("vital_signs_high_intensity_activity".localized)
                        .font(.system(size: 12))
                    .foregroundColor(.gray)
                }
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.top, 4)
            
            // 测量按钮
            Button(action: {
                heartRateViewModel.startMeasurement()
            }) {
                HStack {
                    Image(systemName: "heart.fill")
                        .foregroundColor(.white)
                        .font(.system(size: 14))
                    
                    Text("vital_signs_start_hr_measurement".localized)
                        .font(.system(size: 12))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.3))
                .cornerRadius(8)
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 12)
            
            // 错误消息
            if let errorMessage = heartRateViewModel.errorMessage, !heartRateViewModel.isLoading {
                Text(errorMessage)
                    .font(.system(size: 12))
                    .foregroundColor(.red)
                    .padding(.horizontal, 16)
                    .padding(.bottom, 12)
                    .frame(maxWidth: .infinity, alignment: .center)
                
                // 5秒后自动清除错误消息
                .onAppear {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                        if heartRateViewModel.errorMessage == errorMessage {
                            heartRateViewModel.errorMessage = nil
                        }
                    }
                }
            }
        }
        .background(cardBackground)
        .cornerRadius(24)
    }

    private func updateSelectedHeartRatePoint(at location: CGPoint, proxy: ChartProxy, geometry: GeometryProxy) {
        let xPosition = location.x - geometry[proxy.plotAreaFrame].origin.x
        guard let date: Date = proxy.value(atX: xPosition) else {
            return
        }

        // Find the closest data point
        let closestPoint = heartRateViewModel.chartData.min { recordOne, recordTwo in
            let time1 = TimeInterval(recordOne.time) / 1000
            let time2 = TimeInterval(recordTwo.time) / 1000
            return abs(time1 - date.timeIntervalSince1970) < abs(time2 - date.timeIntervalSince1970)
        }
//        let closestPoint = heartRateViewModel.chartData.min(by: { recordOne, recordTwo in
//            let time1 = Int(recordOne.time / 1000)
//            let time2 = Int(recordTwo.time / 1000)
//            return  abs(time1.timeIntervalSince(date)) < abs(time2.timeIntervalSince(date))
//        })
//            .min(by: { abs($0.time.timeIntervalSince(date)) < abs($1.time.timeIntervalSince(date)) })

        // Update the selected point
        if heartRateViewModel.selectedPoint?.id != closestPoint?.id ?? 0{
            heartRateViewModel.selectedPoint = closestPoint
        }
    }

    @ViewBuilder
    private func heartRateScrubberView(point: HrVitalRecord) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("HR: \(point.minHearts)-\(point.maxHearts) bpm")
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(.white)
            if let time = Date(timeIntervalSince1970: TimeInterval(point.time) / 1000) as Date? {
                Text(time, style: .time)
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
            }
            
            
        }
        .padding(8)
        .background(Color.black.opacity(0.6))
        .cornerRadius(8)
        .shadow(radius: 4)
    }
    
    // SpO2卡片
    var spo2Card: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和信息图标
            HStack {
                Text("vital_signs_metric_spo2".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.vitalSpO2.rawValue)
//                Button(action: {}) {
//                    Image(systemName: "info.circle")
//                        .font(.system(size: 12))
//                        .foregroundColor(.gray)
//                }
                
                Spacer()
                
                // 添加设备连接状态指示器
                HStack(spacing: 4) {
                    Circle()
                        .fill(deviceService.connectionState.isConnected ? Color.green : Color.red)
                        .frame(width: 8, height: 8)
                    
                    Text(deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized)
                        .font(.system(size: 10))
                        .foregroundColor(deviceService.connectionState.isConnected ? .green : .red)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.black.opacity(0.3))
                .cornerRadius(12)
            }
            .padding(.top, 16)
            .padding(.horizontal, 16)
            
            // 最新值和平均值
            HStack(spacing: 10) {
                // Latest卡片
                ZStack {
                    // 渐变背景
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#141821"),
                            Color(hex: "#2A3040")
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    .cornerRadius(10)
                    
                    // 内容
                    VStack(alignment: .center, spacing: 4) {
                        Text("vital_signs_latest_percent".localized)
                            .font(.system(size: 12))
                            .foregroundColor(.gray)
                        
                        Text("\(spo2ViewModel.latestSpO2)")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.white)
                        if let latestSpO2Time = spo2ViewModel.latestSpO2Time{
                            Text(latestSpO2Time)
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .frame(height: 14)
                        }
                        

                    }
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal, 0)
                }
                .frame(height: 56)
                .frame(maxWidth: .infinity)
                
                // Average卡片
                ZStack {
                    // 渐变背景
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#141821"),
                            Color(hex: "#2A3040")
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    .cornerRadius(10)
                    
                    // 内容
                    VStack(alignment: .center, spacing: 4) {
                        Text("vital_signs_average_percent".localized)
                            .font(.system(size: 12))
                            .foregroundColor(.gray)
                        
                        Text("\(spo2ViewModel.averageSpO2)")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal, 0)
                }
                .frame(height: 56)
                .frame(maxWidth: .infinity)
            }
            .padding(.horizontal, 16)
            
            // 范围标签
            HStack {
                Spacer()
                Text("\(spo2ViewModel.spO2Range)  \(spo2ViewModel.timeRange)")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .padding(.vertical, 6)
                    .padding(.horizontal, 12)
                    .background(Color(hex: "#1A1D26"))
                    .cornerRadius(8)
                    .fixedSize(horizontal: true, vertical: false)
                Spacer()
            }
            .padding(.top, 16)
            
            // SpO2图表
            if spo2ViewModel.isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)
                    .frame(maxWidth: .infinity)
                    .frame(height: 100)
                    .padding(.top, 8)
            } else if let error = spo2ViewModel.errorMessage {
                Text(error)
                    .foregroundColor(.red)
                    .font(.system(size: 12))
                    .frame(maxWidth: .infinity)
                    .padding(.top, 8)
                    .padding(.bottom, 8)
            } else if spo2ViewModel.chartData.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "lungs.slash")
                        .foregroundColor(.gray)
                        .font(.system(size: 24))
                    Text("vitals_no_spo2_data".localized)
                        .foregroundColor(.gray)
                        .font(.system(size: 14))
                }
                .frame(maxWidth: .infinity)
                .frame(height: 100)
                .padding(.top, 8)
            } else {
                Chart {
                    RuleMark(y: .value("Center", 95))
                        .lineStyle(StrokeStyle(lineWidth: 1, dash: [2, 3]))
                        .foregroundStyle(.gray.opacity(0.8))

                            ForEach(spo2ViewModel.chartData) { point in
                                    if point.hasData {
                            BarMark(
                                x: .value("Time", point.time, unit: .minute),
                                yStart: .value("SpO2", min(point.value, 95)),
                                yEnd: .value("SpO2", max(point.value, 95)),
                                width: .fixed(4)
                            )
                            .foregroundStyle(Color(hex: "#FF5252"))
                            .cornerRadius(2)
                                    } else {
                            PointMark(
                                x: .value("Time", point.time, unit: .minute),
                                y: .value("SpO2", 95)
                            )
                            .foregroundStyle(.gray)
                            .symbolSize(CGSize(width: 4, height: 4))
                        }
                    }
                }
                .chartYScale(domain: 88...102)
                .chartYAxis {
                    AxisMarks(position: .trailing, values: [90, 95, 100]) { value in
                        AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                        AxisValueLabel {
                            if let v = value.as(Int.self) {
                                Text("\(v)%")
                                    .font(.system(size: 10))
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                }
                .chartXAxis(.hidden)
                .frame(height: 100)
                .padding(.top, 8)
                .padding(.horizontal, 16)
            }
            
            // 时间轴
            HStack {
                Text("00:00")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
                
                Spacer()
                
                Text("6:00")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
                
                Spacer()
                
                Text("12:00")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
                
                Spacer()
                
                Text("18:00")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
                
                Spacer()
                
                Text("24:00")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            .padding(.top, 8)
            
            // 健康范围提示
            if spo2ViewModel.measurementState.isIdle && spo2ViewModel.latestSpO2 > 0 {
                HStack {
                    Circle()
                        .fill(healthStatusColor(for: spo2ViewModel.latestSpO2))
                        .frame(width: 8, height: 8)
                    
                    Text(healthStatusText(for: spo2ViewModel.latestSpO2))
                        .font(.system(size: 12))
                        .foregroundColor(healthStatusColor(for: spo2ViewModel.latestSpO2))
                }
                .padding(.horizontal, 16)
                .padding(.top, 4)
            }
            
            // 测量按钮
            Button(action: {
                spo2ViewModel.startMeasurement()
            }) {
                HStack {
                    Image(systemName: "lungs.fill")
                        .foregroundColor(.white)
                        .font(.system(size: 14))
                    
                    Text("vital_signs_start_spo2_measurement".localized)
                        .font(.system(size: 12))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.3))
                .cornerRadius(8)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .disabled(!deviceService.connectionState.isConnected)
            .opacity(deviceService.connectionState.isConnected ? 1.0 : 0.5)
            
            // 测量未连接提示
            if !deviceService.connectionState.isConnected {
                Text("error_connect_device_first".localized)
                    .font(.system(size: 12))
                    .foregroundColor(.orange)
                    .padding(.horizontal, 16)
                    .padding(.top, -8)
                    .padding(.bottom, 8)
                    .frame(maxWidth: .infinity, alignment: .center)
            }
            
            // 错误消息
            if let errorMessage = spo2ViewModel.errorMessage, !spo2ViewModel.isLoading {
                Text(errorMessage)
                    .font(.system(size: 12))
                    .foregroundColor(.red)
                    .padding(.horizontal, 16)
                    .padding(.bottom, 12)
                    .frame(maxWidth: .infinity, alignment: .center)
                
                // 5秒后自动清除错误消息
                .onAppear {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                        if spo2ViewModel.errorMessage == errorMessage {
                            spo2ViewModel.errorMessage = nil
                        }
                    }
                }
            }
        }
        .background(cardBackground)
        .cornerRadius(24)
    }
    
    // HRV卡片
    var hrvCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和信息图标
            HStack {
                Text("hrv".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.vitalHRV.rawValue)
                
//                Button(action: {}) {
//                    Image(systemName: "info.circle")
//                        .font(.system(size: 12))
//                        .foregroundColor(.gray)
//                }
                
                Spacer()
                
                // 添加设备连接状态指示器
//                HStack(spacing: 4) {
//                    Circle()
//                        .fill(deviceService.connectionState.isConnected ? Color.green : Color.red)
//                        .frame(width: 8, height: 8)
//
//                    Text(deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized)
//                        .font(.system(size: 10))
//                        .foregroundColor(deviceService.connectionState.isConnected ? .green : .red)
//                }
//                .padding(.horizontal, 8)
//                .padding(.vertical, 4)
//                .background(Color.black.opacity(0.3))
//                .cornerRadius(12)
            }
            .padding(.top, 16)
            .padding(.horizontal, 16)
            
            // 最新值和平均值
            HStack(spacing: 10) {
                // Latest卡片 - HRV
                ZStack {
                    // 渐变背景
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#141821"),
                            Color(hex: "#2A3040")
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    .cornerRadius(10)
                    
                    // 内容
                    VStack(alignment: .center, spacing: 4) {
                        Text(hrvViewModel.selectedPoint == nil ? "vital_signs_latest_ms".localized : "vital_signs_range_max_ms".localized)
                            .font(.system(size: 12))
                            .foregroundColor(.gray)
                        
                        Text("\(hrvViewModel.selectedPoint?.maxHrv ?? hrvViewModel.latestHRV)")
                            .font(.system(size: 28, weight: .bold))
                            .foregroundColor(.white)
                        
                        Text(hrvViewModel.selectedPoint == nil ? hrvViewModel.latestHRVTime : "")
                            .font(.system(size: 12))
                            .foregroundColor(.gray)
                            .frame(height: 14)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal, 0)
                }
                .frame(height: 80)
                .frame(maxWidth: .infinity)
                
                // Average卡片 - HRV
                ZStack {
                    // 渐变背景
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#141821"),
                            Color(hex: "#2A3040")
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    .cornerRadius(10)
                    
                    // 内容
                    VStack(alignment: .center, spacing: 4) {
                        Text(hrvViewModel.selectedPoint == nil ? "vital_signs_average_ms".localized : "vital_signs_range_min_ms".localized)
                            .font(.system(size: 12))
                            .foregroundColor(.gray)
                        
                        Text("\(hrvViewModel.selectedPoint?.minHrv ?? hrvViewModel.averageHRV)")
                            .font(.system(size: 28, weight: .bold))
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal, 0)
                }
                .frame(height: 80)
                .frame(maxWidth: .infinity)
            }
            .padding(.horizontal, 16)
            
            // 范围标签
            HStack {
                Spacer()
                Text("\(hrvViewModel.hrvRange)  \(hrvViewModel.timeRange)")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .padding(.vertical, 6)
                    .padding(.horizontal, 12)
                    .background(Color(hex: "#1A1D26"))
                    .cornerRadius(8)
                    .fixedSize(horizontal: true, vertical: false)
                Spacer()
            }
            .padding(.top, 16)
            
            // HRV图表
            if hrvViewModel.isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)
                    .frame(maxWidth: .infinity)
                    .frame(height: 150)
                    .padding(.top, 8)
            } else if let error = hrvViewModel.errorMessage {
                Text(error)
                    .foregroundColor(.red)
                    .font(.system(size: 12))
                    .frame(maxWidth: .infinity)
                    .padding(.top, 8)
                    .padding(.bottom, 8)
            } else if hrvViewModel.chartData.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "waveform.path.ecg.rectangle.slash")
                        .foregroundColor(.gray)
                        .font(.system(size: 24))
                    Text("vitals_no_hrv_data".localized)
                        .foregroundColor(.gray)
                        .font(.system(size: 14))
                }
                .frame(maxWidth: .infinity)
                .frame(height: 150)
                .padding(.top, 8)
            } else {
                Chart {
                    ForEach(hrvViewModel.chartData) { point in
                        if point.minHrv > 0 || point.maxHrv > 0 {
                            let minY = point.minHrv
                            let maxY = point.maxHrv
                            let adjustedMaxY = (minY == maxY) ? (maxY + 1) : maxY
                            BarMark(
                                x: .value("Time", point.time, unit: .minute),
                                yStart: .value("Min HRV", minY),
                                yEnd: .value("Max HRV", adjustedMaxY),
                                width: .fixed(4)
                            )
                            .foregroundStyle(Color.purple.opacity(0.6))
                            .cornerRadius(2)
                        }
                    }
                }
                .chartYScale(domain: hrvViewModel.yAxisMin...hrvViewModel.yAxisMax)
                .chartXScale(domain: heartRateViewModel.xAxisStart...heartRateViewModel.xAxisEnd)
                .chartYAxis {
                    AxisMarks(position: .trailing, values: .automatic(desiredCount: 4)) { value in
                        AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                        AxisValueLabel {
                            if let val = value.as(Double.self) {
                                Text("\(Int(val.rounded()))")
                                .font(.system(size: 10))
                                .foregroundColor(.gray)
                        }
                        }
                    }
                }
                .chartXAxis(.hidden)
                .chartOverlay { proxy in
                    GeometryReader { geometry in
                        Rectangle().fill(.clear).contentShape(Rectangle())
                            .gesture(
                                DragGesture(minimumDistance: 0)
                                    .onChanged { value in
                                        updateSelectedHRVPoint(at: value.location, proxy: proxy, geometry: geometry)
                                    }
                                    .onEnded { _ in
                                        hrvViewModel.selectedPoint = nil
                                    }
                            )
                    }
                }
                .overlay(alignment: .top) {
                    if let selectedPoint = hrvViewModel.selectedPoint {
                        hrvScrubberView(point: selectedPoint)
                            .padding(.top, -30) // Position above chart
                    }
                }
                .frame(height: 150)
                    .padding(.horizontal, 16)
                .padding(.top, 8)
            }
            
            // 时间轴
            HStack {
                Text("00:00")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
                
                Spacer()
                
                Text("6:00")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
                
                Spacer()
                
                Text("12:00")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
                
                Spacer()
                
                Text("18:00")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
                
                Spacer()
                
                Text("24:00")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            .padding(.top, 8)
            
            // 高强度活动标注
            HStack {
                Spacer()
                
                HStack(spacing: 6){
                    Rectangle()
                        .fill(Color.purple.opacity(0.6))
                        .frame(width: 12, height: 12)
                        .cornerRadius(2)
                    Text("vital_signs_hrv_range".localized)
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                }
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.top, 4)
            
            Spacer()
            // 健康状态显示 - 新增
//            if !hrvViewModel.isMeasuring && hrvViewModel.latestHRV > 0 {
//                HStack {
//                    Circle()
//                        .fill(hrvViewModel.healthStatusColor)
//                        .frame(width: 10, height: 10)
//                    
//                    Text(String(format: "vital_signs_heart_health_status_format".localized, hrvViewModel.healthStatusDescription))
//                        .font(.system(size: 14))
//                        .foregroundColor(hrvViewModel.healthStatusColor)
//                    
//                    Spacer()
//                    
//                    // 详情按钮
//                    Button(action: {
//                        // 显示健康建议弹窗
//                        isShowingHRVAdvice = true
//                    }) {
//                        Image(systemName: "info.circle")
//                            .foregroundColor(.blue)
//                            .font(.system(size: 14))
//                    }
//                }
//                .padding(.horizontal, 16)
//                .padding(.top, 8)
//                .padding(.bottom, 8)
//            }
//            
//            // 测量按钮
//            Button(action: {
//                hrvViewModel.toggleHRVMeasurement()
//            }) {
//                HStack {
//                    Image(systemName: hrvViewModel.isMeasuring ? "stop.fill" : "waveform.path.ecg")
//                        .foregroundColor(hrvViewModel.isMeasuring ? .purple : .white)
//                        .font(.system(size: 14))
//                    
//                    Text(hrvViewModel.isMeasuring ? "vital_signs_stop_measurement".localized : "vital_signs_start_hrv_measurement".localized)
//                        .font(.system(size: 12))
//                        .foregroundColor(.white)
//                }
//                .frame(maxWidth: .infinity)
//                .padding(.vertical, 12)
//                .background(hrvViewModel.isMeasuring ? Color.purple.opacity(0.3) : Color.gray.opacity(0.3))
//                .cornerRadius(8)
//            }
//            .padding(.horizontal, 16)
//            .padding(.vertical, 12)
//            .disabled(!deviceService.connectionState.isConnected && !hrvViewModel.isMeasuring)
//            .opacity(deviceService.connectionState.isConnected || hrvViewModel.isMeasuring ? 1.0 : 0.5)
//            
//            // 测量未连接提示
//            if !deviceService.connectionState.isConnected && !hrvViewModel.isMeasuring {
//                Text("error_connect_device_first".localized)
//                    .font(.system(size: 12))
//                    .foregroundColor(.orange)
//                    .padding(.horizontal, 16)
//                    .padding(.top, -8)
//                    .padding(.bottom, 8)
//                    .frame(maxWidth: .infinity, alignment: .center)
//            }
            
            // 显示当前测量值
//            if hrvViewModel.isMeasuring {
//                VStack(spacing: 16) {
//                    // 测量进度动画
//                    ZStack {
//                        Circle()
//                            .stroke(Color.purple.opacity(0.2), lineWidth: 6)
//                            .frame(width: 80, height: 80)
//                        
//                        Circle()
//                            .trim(from: 0, to: 0.75)
//                            .stroke(Color.purple, style: StrokeStyle(lineWidth: 6, lineCap: .round))
//                            .frame(width: 80, height: 80)
//                            .rotationEffect(Angle(degrees: 270))
//                            .rotationEffect(Angle(degrees: animationValue))
//                            .animation(Animation.linear(duration: 1).repeatForever(autoreverses: false), value: animationValue)
//                            .onAppear {
//                                animationValue = 360
//                            }
//                        
//                        // 显示测量值或提示
//                        if hrvViewModel.measurementValue > 0 {
//                            Text("\(hrvViewModel.measurementValue) times")
//                                .font(.system(size: 20, weight: .bold))
//                                .foregroundColor(.white)
//                        } else {
//                            Text("vital_signs_measuring".localized)
//                                .font(.system(size: 14))
//                                .foregroundColor(.gray)
//                        }
//                    }
//                    
//                    // 测量指导提示
//                    Text("vital_signs_keep_still_prompt".localized)
//                        .font(.system(size: 12))
//                        .foregroundColor(.gray)
//                        .multilineTextAlignment(.center)
//                }
//                .frame(maxWidth: .infinity)
//                .padding(.vertical, 16)
//                .background(Color.black.opacity(0.3))
//                .cornerRadius(12)
//                .padding(.horizontal, 16)
//                .padding(.bottom, 16)
//            }
//            
//            // 错误消息
//            if let errorMessage = hrvViewModel.errorMessage, !hrvViewModel.isLoading {
//                Text(errorMessage)
//                    .font(.system(size: 12))
//                    .foregroundColor(.red)
//                    .padding(.horizontal, 16)
//                    .padding(.bottom, 12)
//                    .frame(maxWidth: .infinity, alignment: .center)
//                
//                // 5秒后自动清除错误消息
//                .onAppear {
//                    DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
//                        if hrvViewModel.errorMessage == errorMessage {
//                            hrvViewModel.errorMessage = nil
//                        }
//                    }
//                }
//            }
        }
        .background(cardBackground)
        .cornerRadius(24)
    }
    // hrvScrubberView definition
    @ViewBuilder
    private func hrvScrubberView(point: HRVChartPoint) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("HRV: \(point.minHrv)-\(point.maxHrv) ms")
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(.white)
            
            Text(point.time, style: .time)
                .font(.system(size: 10))
                .foregroundColor(.gray)
        }
        .padding(8)
        .background(Color.black.opacity(0.6))
        .cornerRadius(8)
        .shadow(radius: 4)
    }
    
    // 体温卡片
    var temperatureCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和信息图标
            HStack {
                Text("vital_signs_temperature_celsius".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
            }
            .padding(.top, 16)
            .padding(.horizontal, 16)
            
            // 体温数据卡片
            if temperatureViewModel.isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 30)
            } else if let error = temperatureViewModel.errorMessage {
                VStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle")
                        .foregroundColor(.red)
                        .font(.system(size: 20))
                    
                    Text(error)
                        .foregroundColor(.red)
                        .font(.system(size: 14))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 30)
            } else if temperatureViewModel.averageTemperature == 0 && temperatureViewModel.baselineTemperature == 0 && temperatureViewModel.temperatureDifference == 0 {
                VStack(spacing: 12) {
                    Image(systemName: "thermometer.slash")
                        .foregroundColor(.gray)
                        .font(.system(size: 24))
                    Text("vitals_no_temp_data".localized)
                        .foregroundColor(.gray)
                        .font(.system(size: 14))
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 30)
            } else {
                HStack(spacing: 12) {
                    temperatureDetailBox(
                        title: "vitals_history_detail_sheet_average".localized,
                        value: temperatureViewModel.formattedAverageTemperature,
                        bgColor: Color(hex: "#2E7D8A"),
                        icon: "thermometer"
                    )
                    temperatureDetailBox(
                        title: "vitals_history_detail_sheet_baseline".localized,
                        value: temperatureViewModel.formattedBaselineTemperature,
                        bgColor: Color(hex: "#3B588A"),
                        icon: "thermometer"
                    )
                    temperatureDetailBox(
                        title: "vitals_history_detail_sheet_difference".localized,
                        value: temperatureViewModel.formattedTemperatureDifference,
                        bgColor: Color(hex: "#5A3B8A"),
                        icon: "thermometer"
                    )
                }
                .padding(.horizontal, 16)
            }
            
            // 健康提示
            if !temperatureViewModel.isLoading && temperatureViewModel.errorMessage == nil && temperatureViewModel.averageTemperature > 0 {
                HStack(spacing: 8) {
                    Image(systemName: "bell.fill")
                        .font(.system(size: 14))
                        .foregroundColor(temperatureViewModel.temperatureStatusColor)
                    
                    Text(temperatureViewModel.temperatureStatusDescription)
                        .font(.system(size: 14))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                .padding()
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(temperatureViewModel.temperatureStatusColor, lineWidth: 1)
                )
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
        }
        .onAppear {
            if let errorMessage = temperatureViewModel.errorMessage {
                DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                    if temperatureViewModel.errorMessage == errorMessage {
                        temperatureViewModel.errorMessage = nil
                    }
                }
            }
        }
        .background(cardBackground)
        .cornerRadius(24)
    }
    
    // 关闭日历选择器
    private func closeCalendarPicker() {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                showDatePicker = false
        }
    }
    
    // 添加心率图表视图
    struct HeartRateChartView: Shape {
        var points: [CGPoint]
        
        func path(in rect: CGRect) -> Path {
            var path = Path()
            
            guard !points.isEmpty else {
                return path
            }
            
            // 调整点以适应绘制区域
            let scaledPoints = points.map { point in
                CGPoint(
                    x: rect.width * (point.x / 300.0),
                    y: rect.height * (point.y / 100.0)
                )
            }
            
            // 开始绘制路径
            path.move(to: scaledPoints[0])
            
            for i in 1..<scaledPoints.count {
                path.addLine(to: scaledPoints[i])
            }
            
            return path
        }
    }
    
    // 根据血氧值获取健康状态颜色
    private func healthStatusColor(for spo2Value: Int) -> Color {
        if spo2Value >= 95 {
            return .green
        } else if spo2Value >= 90 {
            return .yellow
        } else {
            return .red
        }
    }
    
    // 根据血氧值获取健康状态文本
    private func healthStatusText(for spo2Value: Int) -> String {
        if spo2Value >= 95 {
            return "vital_signs_spo2_normal".localized
        } else if spo2Value >= 90 {
            return "vital_signs_spo2_low".localized
        } else {
            return "vital_signs_spo2_very_low".localized
        }
    }
    
    // 添加获取日历数据的方法
    private func fetchCalendarData() {
        isLoadingCalendarData = true
        
        // 格式化日期为yyyy-MM-dd格式
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: selectedDate)
        
        print("正在获取日历数据，请求日期: \(dateString)")
        
        // 构建URL
        guard let url = URL(string: "\(AppGlobals.apiBaseURL)/app-api/iot/common/getCalenderData?date=\(dateString)") else {
            print("日历数据：无效的URL")
            isLoadingCalendarData = false
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 添加认证令牌
        if let token = authToken, !token.isEmpty {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("使用用户访问令牌: \(token)")
        } else {
            // 使用测试令牌作为备用
            print("警告: 没有可用的访问令牌，使用测试令牌")
            request.addValue("Bearer test1", forHTTPHeaderField: "Authorization")
        }
        
        request.addValue("application/json", forHTTPHeaderField: "Accept")
        
        print("发送日历数据请求: \(url)")
        
        // 执行网络请求
        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                self.isLoadingCalendarData = false
                
                if let error = error {
                    print("日历数据网络错误: \(error.localizedDescription)")
                    return
                }
                
                // 检查HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("HTTP状态码: \(httpResponse.statusCode)")
                    if !(200...299).contains(httpResponse.statusCode) {
                        print("日历数据HTTP错误: \(httpResponse.statusCode)")
                        return
                    }
                }
                
                guard let data = data else {
                    print("日历数据：没有返回数据")
                    return
                }
                
                // 打印原始响应数据，用于调试
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("日历数据原始响应: \(jsonString)")
                }
                
                do {
                    // 解析响应数据
                    let response = try CleanJSONDecoder().decode(CalendarDataResponse.self, from: data)
                    
                    if response.code == 200 || response.code == 0 {
                        // 清空现有数据
                        self.calendarDatesWithData.removeAll()
                        
                        // 处理解析到的日历数据
                        if let calendarData = response.data {
                            // 将返回的日期字符串转换为Date对象
                            let dateFormatter = DateFormatter()
                            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                            
                            for item in calendarData {
                                if item.flag, let date = dateFormatter.date(from: item.time) {
                                    self.calendarDatesWithData.append(date)
                                }
                            }
                            
                            print("成功获取日历数据，共\(self.calendarDatesWithData.count)个日期有数据")
                        }
                    } else {
                        print("日历数据API错误: \(response.msg)")
                    }
                } catch {
                    print("日历数据解析错误: \(error.localizedDescription)")
                }
            }
        }.resume()
    }
    
    // MARK: - 蓝牙相关函数
    // 蓝牙按钮点击逻辑
    private func handleBluetoothButtonTap() {
        switch bluetoothState {
        case .disconnected:
            // 断开状态下点击，尝试连接之前的设备
            connectLastDevice()
        case .connecting:
            // 连接中状态下点击，取消连接
            cancelConnecting()
        case .connected:
            // 已连接状态下点击，显示断开连接提示
            isShowingBluetoothAlert = true
        }
    }
    
    // 尝试连接上次的设备
    private func connectLastDevice() {
        bluetoothState = .connecting
        shouldCancelConnection = false
        
        print("尝试连接设备...")
        
        // 首先检查设备服务是否有上次连接的设备
        if let lastMac = deviceService.lastConnectedDeviceMAC, !lastMac.isEmpty {
            print("尝试连接上次设备: \(lastMac)")
            
            // 先断开当前连接（如果有）
            if deviceService.connectionState.isConnected {
                deviceService.disconnectDevice()
            }
            
            // 开始扫描
            deviceService.startScan(duration: 10)
            
            // 设置10秒超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [self] in
                guard bluetoothState == .connecting else { return }
                
                // 检查是否已连接
                if !deviceService.connectionState.isConnected && !shouldCancelConnection {
                    bluetoothState = .disconnected
                    print("连接超时")
                }
            }
            
            // 尝试连接
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [self] in
                guard !shouldCancelConnection else { return }
                
                if let discovery = deviceService.discoveredDevices.first(where: {
                    $0.mac?.uppercased() == lastMac.uppercased()
                }) {
                    print("找到之前设备，开始连接")
                    deviceService.connectDevice(discovery: discovery)
                    
                    // 连接成功后记录设备名称，用于显示提示
                    if let deviceName = discovery.localName, !deviceName.isEmpty {
                        connectedDeviceName = deviceName
                    } else {
                        connectedDeviceName = "设备"
                    }
                }
            }
        } else {
            print("无之前连接的设备记录")
            
            // 开始扫描，连接任何发现的设备
            deviceService.startScan(duration: 5)
            
            // 设置扫描超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 6) { [self] in
                guard bluetoothState == .connecting else { return }
                
                if !deviceService.discoveredDevices.isEmpty && !shouldCancelConnection {
                    let firstDevice = deviceService.discoveredDevices.first!
                    print("尝试连接发现的设备: \(firstDevice.localName ?? "未知设备")")
                    deviceService.connectDevice(discovery: firstDevice)
                    
                    // 连接超时
                    DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [self] in
                        guard bluetoothState == .connecting else { return }
                        
                        if !deviceService.connectionState.isConnected && !shouldCancelConnection {
                            bluetoothState = .disconnected
                            print("连接超时")
                        }
                    }
                } else {
                    // 没有发现设备
                    bluetoothState = .disconnected
                    print("未发现设备")
                }
            }
        }
    }
    
    // 取消连接过程
    private func cancelConnecting() {
        shouldCancelConnection = true
        bluetoothState = .disconnected
        
        // 取消设备服务的连接操作
        if deviceService.connectionState == .connecting {
            deviceService.disconnectDevice()
        }
        
        if deviceService.isScanning {
            deviceService.stopScan()
        }
    }
    
    // 断开蓝牙连接
    private func disconnectBluetooth() {
        // 执行断开连接逻辑
        bluetoothState = .disconnected
        deviceService.disconnectDevice()
    }
    
    // 开始定期检查连接状态
    private func startConnectionStateChecking() {
        // 取消可能存在的计时器
        connectionCheckTimer?.cancel()
        
        // 创建新计时器，每1秒检查一次
        connectionCheckTimer = Timer.publish(every: 1, on: .main, in: .common)
            .autoconnect()
            .sink { [self] _ in
                syncBluetoothState()
                
                // 如果设备已连接但UI未显示连接状态，立即更新
                if deviceService.connectionState == .connected && bluetoothState != .connected {
                    bluetoothState = .connected
                    print("发现设备已连接，更新UI状态")
                    
                    // 显示连接成功提示
                    if !showConnectedToast {
                        if let deviceName = deviceService.currentDiscovery?.localName, !deviceName.isEmpty {
                            connectedDeviceName = deviceName
                        } else {
                            connectedDeviceName = "设备"
                        }
                        showConnectedToast = true
                        
                        // 3秒后隐藏提示
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                            withAnimation {
                                showConnectedToast = false
                            }
                        }
                    }
                }
                
                // 如果设备已断开但UI仍显示连接状态，立即更新
                if deviceService.connectionState != .connected && bluetoothState == .connected {
                    bluetoothState = .disconnected
                    print("发现设备已断开，更新UI状态")
                }
            }
    }
    
    // 同步蓝牙状态
    private func syncBluetoothState() {
        let previousState = bluetoothState
        
        // 先检查设备服务的连接状态
        if deviceService.connectionState == .connected {
            // 如果设备已连接，直接更新UI状态
            if bluetoothState != .connected {
                bluetoothState = .connected
                
                // 如果状态从非连接变为连接，更新设备名称但不显示提示
                if previousState != .connected {
                    connectedDeviceName = deviceService.currentDiscovery?.localName ?? "设备"
                }
            }
            return
        }
        
        // 连接中状态的特殊处理
        if bluetoothState == .connecting {
            if deviceService.connectionState == .connected {
                bluetoothState = .connected
            } else if deviceService.connectionState == .disconnected {
                if !shouldCancelConnection {
                    bluetoothState = .disconnected
                }
            }
        } else {
            // 常规状态同步
            if deviceService.connectionState == .connected && bluetoothState != .connected {
                bluetoothState = .connected
            } else if deviceService.connectionState != .connected && bluetoothState == .connected {
                bluetoothState = .disconnected
            }
        }
    }

    private func updateSelectedHRVPoint(at location: CGPoint, proxy: ChartProxy, geometry: GeometryProxy) {
        let xPosition = location.x - geometry[proxy.plotAreaFrame].origin.x
        guard let date: Date = proxy.value(atX: xPosition) else {
            return
        }
        
        // Find the closest data point in hrvViewModel.chartData
        // We assume the point type is HRVChartPoint and it is Identifiable.
        let closestPoint = hrvViewModel.chartData
            .min(by: { abs($0.time.timeIntervalSince(date)) < abs($1.time.timeIntervalSince(date)) })

        // Update the selected point
        if hrvViewModel.selectedPoint?.id != closestPoint?.id {
            hrvViewModel.selectedPoint = closestPoint
        }
    }
}

// MARK: - 新增测量弹窗
struct HeartRateMeasurementPopup: View {
    @ObservedObject var viewModel: HeartRateViewModel
    
    var body: some View {
        Group {
            switch viewModel.measurementState {
            case .idle:
                EmptyView() // Should not be visible in this state
            case .measuring:
                MeasurementAnimationView(viewModel: viewModel)
            case .completed(let value, let time):
                MeasurementResultView(viewModel: viewModel, value: value, time: time)
            }
        }
        .padding(24)
        .background(Color(red: 44/255, green: 49/255, blue: 56/255))
        .cornerRadius(24)
        .shadow(color: .black.opacity(0.4), radius: 20)
        .padding(.horizontal, 30)
    }
}

// MARK: - 结果视图
struct MeasurementResultView: View {
    @ObservedObject var viewModel: HeartRateViewModel
    let value: Int
    let time: Date
    
    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter
    }

    var body: some View {
        VStack(spacing: 24) {
            // Header
            HStack {
                Text("hr_measurement_popup_title".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                Spacer()
                Button(action: {
                    viewModel.cancelMeasurement() // cancelMeasurement also dismisses
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.gray)
                        .font(.body.weight(.bold))
                }
            }

            // Result View
            VStack(spacing: 12) {
                HalftoneHeartView()
                    .frame(width: 150, height: 150)
                    .overlay(
                        HStack(alignment: .lastTextBaseline, spacing: 2) {
                            Text("\(value)")
                                .font(.system(size: 52, weight: .bold))
                            Text("bpm")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.gray)
                                .padding(.bottom, 8)
                        }
                    )
                
                Text(String(format: "hr_measurement_latest_time".localized, timeFormatter.string(from: time)))
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
            }
            
            // Action Button
            Button(action: {
                viewModel.cancelMeasurement() // Dismiss
            }) {
                Text("hr_measurement_done".localized)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(Color.blue)
                    .cornerRadius(16)
            }
        }
    }
}
// MARK: - 半色调心形
struct HalftoneHeartView: View {
    var body: some View {
        Canvas { context, size in
            let dotSize: CGFloat = 2.5
            let spacing: CGFloat = 6
            let numCols = Int(size.width / spacing)
            let numRows = Int(size.height / spacing)
            let center = CGPoint(x: size.width / 2, y: size.height / 2)
            // Use a slightly larger max distance to make the falloff smoother
            let maxDist = size.width * 0.7

            for row in 0..<numRows {
                for col in 0..<numCols {
                    let pos = CGPoint(x: CGFloat(col) * spacing, y: CGFloat(row) * spacing)
                    let distFromCenter = sqrt(pow(pos.x - center.x, 2) + pow(pos.y - center.y, 2))
                    
                    // Use a non-linear falloff for a better visual effect
                    let opacity = max(0, 1.0 - pow(distFromCenter / maxDist, 2))
                    
                    context.fill(
                        Path(ellipseIn: CGRect(origin: pos, size: CGSize(width: dotSize, height: dotSize))),
                        with: .color(.white.opacity(opacity * 0.8))
                    )
                }
            }
        }
        .mask(
            Image(systemName: "heart.fill")
                .resizable()
                .scaledToFit()
        )
    }
}

extension Notification.Name {
    static let spo2Measured = Notification.Name("spo2Measured")
    static let spo2DataUploaded = Notification.Name("spo2DataUploaded")
//    static let heartRateDataUploaded = Notification.Name("heartRateDataUploaded")
}


// MARK: - 预览
struct VitalSignsDetailView_Previews: PreviewProvider {
    static var previews: some View {
        VitalSignsDetailView()
            .preferredColorScheme(.dark)
    }
} 
