import Foundation

// MARK: - 睡眠体温详情响应
public struct TemperatureSleepResponse: Codable {
    public var code: Int
    public var msg: String
    public var data: TemperatureSleepData?
    
    enum CodingKeys: String, CodingKey {
        case code, msg, data
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        code = try container.decodeIfPresent(Int.self, forKey: .code) ?? 0
        msg = try container.decodeIfPresent(String.self, forKey: .msg) ?? ""
        data = try container.decodeIfPresent(TemperatureSleepData.self, forKey: .data)
    }
    
    // 提供默认初始化方法
    public init(code: Int = 0, msg: String = "", data: TemperatureSleepData? = nil) {
        self.code = code
        self.msg = msg
        self.data = data
    }
}

// MARK: - 睡眠体温数据
public struct TemperatureSleepData: Codable {
    public var average: Double    // 平均体温
    public var baseLine: Double   // 基准线温度
    public var differ: Double     // 温差
    
    enum CodingKeys: String, CodingKey {
        case average, baseLine, differ
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 处理可能为null的数值，提供默认值
        average = try container.decodeIfPresent(Double.self, forKey: .average) ?? 0.0
        baseLine = try container.decodeIfPresent(Double.self, forKey: .baseLine) ?? 0.0
        differ = try container.decodeIfPresent(Double.self, forKey: .differ) ?? 0.0
    }
    
    // 提供默认初始化方法
    public init(average: Double = 0.0, baseLine: Double = 0.0, differ: Double = 0.0) {
        self.average = average
        self.baseLine = baseLine
        self.differ = differ
    }
    
    // 判断是否有有效数据
    public var hasValidData: Bool {
        return average > 0 || baseLine > 0 || differ != 0
    }
    
    // 获取体温状态描述
    public var temperatureStatus: String {
        if !hasValidData {
            return "无数据"
        }
        
        let tempDiff = differ
        if tempDiff > 0.5 {
            return "体温偏高"
        } else if tempDiff < -0.5 {
            return "体温偏低"
        } else {
            return "体温正常"
        }
    }
    
    // 格式化体温显示（保留1位小数）
    public func formattedTemperature(_ value: Double) -> String {
        if value <= 0 {
            return "--"
        }
        return String(format: "%.1f", value)
    }
    
    // 获取格式化的平均体温
    public var formattedAverage: String {
        return formattedTemperature(average)
    }
    
    // 获取格式化的基准体温
    public var formattedBaseLine: String {
        return formattedTemperature(baseLine)
    }
    
    // 获取格式化的温差（可能为正或负）
    public var formattedDiffer: String {
        if differ == 0 && (average <= 0 || baseLine <= 0) {
            return "--"
        }
        
        let sign = differ > 0 ? "+" : ""
        return sign + String(format: "%.1f", differ)
    }
} 