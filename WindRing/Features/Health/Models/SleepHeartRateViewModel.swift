import Foundation
import SwiftUI
import Combine

/// 睡眠心率视图模型 - 负责处理和管理睡眠心率数据
class SleepHeartRateViewModel: ObservableObject {
    // MARK: - 发布属性
    @Published var sleepHeartRateData: [SleepHeartRatePoint] = [] // 心率数据点
    @Published var isLoading: Bool = false // 是否正在加载
    @Published var errorMessage: String? = nil // 错误信息
    @Published var averageHeartRate: Int = 0 // 平均心率
    @Published var recent7DayAverage: Int = 0 // 最近7天平均心率
    @Published var timeRange: String = "" // 时间范围
    @Published var highlightPeriod: String = "" // 高亮区间
    
    // MARK: - 私有属性
    private let apiService = APIService.shared // API服务
    private var cancellables = Set<AnyCancellable>() // 用于存储和管理订阅
    
    // MARK: - 初始化
    init() {
        print("SleepHeartRateViewModel初始化")
    }
    
    // MARK: - 公共方法
    
    /// 加载指定日期的睡眠心率数据
    /// - Parameter date: 日期
    func loadSleepHeartRateData(for date: Date) {
        guard !isLoading else { return } // 防止重复加载
        
        // 设置状态
        isLoading = true
        errorMessage = nil
        
        // 格式化日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("开始加载日期\(dateString)的睡眠心率数据")
        
        // 使用Combine API
        apiService.health.getHrToSleepDetailsCombine(date: dateString)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (completion: Subscribers.Completion<NetworkError>) in
                guard let self = self else { return }
                
                self.isLoading = false
                
                switch completion {
                case .finished:
                    print("睡眠心率数据请求成功完成")
                case .failure(let error):
                    self.errorMessage = "获取睡眠心率数据失败: \(error.localizedDescription)"
                    print("睡眠心率数据请求失败: \(error.localizedDescription)")
                }
            } receiveValue: { [weak self] (response: HrSleepDetailsData) in
                guard let self = self else { return }
                
                print("收到睡眠心率数据响应")
                
                // 处理响应
//                if let data = response {
                    self.processHeartRateData(response)
//                } else {
//                    self.errorMessage = "没有睡眠心率数据"
//                    print("睡眠心率数据为空")
//                }
            }
            .store(in: &cancellables)
    }
    
    /// 异步版本加载指定日期的睡眠心率数据
    /// - Parameter date: 日期
    func loadSleepHeartRateDataAsync(for date: Date) async {
        guard !isLoading else { return } // 防止重复加载
        
        // 在主线程上更新UI状态
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        // 格式化日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("开始异步加载日期\(dateString)的睡眠心率数据")
        
        do {
            let response = try await apiService.health.getHrToSleepDetails(date: dateString)
            
            // 在主线程上更新UI
            await MainActor.run {
                isLoading = false
                
                if let data = response.data {
                    processHeartRateData(data)
                } else {
                    errorMessage = "没有睡眠心率数据"
                    print("睡眠心率数据为空")
                }
            }
        } catch {
            // 在主线程上更新UI错误状态
            await MainActor.run {
                isLoading = false
                errorMessage = "获取睡眠心率数据失败: \(error.localizedDescription)"
                print("睡眠心率数据请求失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 清除数据和错误状态
    func clearData() {
        sleepHeartRateData = []
        errorMessage = nil
        averageHeartRate = 0
        recent7DayAverage = 0
        timeRange = ""
        highlightPeriod = ""
    }
    
    // MARK: - 私有方法
    
    /// 处理心率数据
    /// - Parameter data: 心率数据
    private func processHeartRateData(_ data: HrSleepDetailsData) {
        print("处理睡眠心率数据: \(data.records.count)条记录")
        
        // 更新平均值
        averageHeartRate = data.avg
        recent7DayAverage = data.avg7
        
        // 没有记录时直接返回
        if data.records.isEmpty {
            sleepHeartRateData = []
            return
        }
        
        // 将记录转换为图表数据点
        var chartPoints: [SleepHeartRatePoint] = []
        
        // 创建一个索引对照表，以便在没有数据的时间点上生成空数据点
        var timeIndicesMap: [Int: SleepHeartRatePoint] = [:]
        
        // 首先对记录按时间排序
        let sortedRecords = data.records.sorted { $0.time < $1.time }
        
        // 计算时间范围
        if let firstRecord = sortedRecords.first, let lastRecord = sortedRecords.last {
            // 创建时间格式化器
            let timeFormatter = DateFormatter()
            timeFormatter.dateFormat = "HH:mm"
            
            // 转换时间
            let startTime = timeFormatter.string(from: firstRecord.date)
            let endTime = timeFormatter.string(from: lastRecord.date)
            
            // 设置时间范围
            timeRange = "\(startTime) - \(endTime)"
        }
        
        // 处理每条记录
        for record in sortedRecords {
            // 创建数据点
            let timeIndex = SleepHeartRatePoint.calculateTimeIndex(from: record.date)
            let point = SleepHeartRatePoint(from: record, timeIndex: timeIndex)
            
            // 添加到集合
            chartPoints.append(point)
            timeIndicesMap[timeIndex] = point
        }
        
        // 查找最高心率区间
        if let highestHeartRateRecord = sortedRecords.max(by: { $0.hearts < $1.hearts }) {
            // 查找相近的高心率点形成区间
            let highThreshold = highestHeartRateRecord.hearts * 9 / 10 // 90%的最高值作为阈值
            let highHeartRateRecords = sortedRecords.filter { $0.hearts >= highThreshold }
            
            if let firstHigh = highHeartRateRecords.first, let lastHigh = highHeartRateRecords.last {
                // 创建时间格式化器
                let timeFormatter = DateFormatter()
                timeFormatter.dateFormat = "HH:mm"
                
                // 转换时间
                let startTime = timeFormatter.string(from: firstHigh.date)
                let endTime = timeFormatter.string(from: lastHigh.date)
                
                // 设置高亮区间
                highlightPeriod = "\(highestHeartRateRecord.hearts)bpm \(startTime)-\(endTime)"
                
                // 标记高亮点
                for i in 0..<chartPoints.count {
                    if chartPoints[i].hearts >= highThreshold {
                        chartPoints[i].isHighlightPoint = true
                    }
                }
            } else {
                // 单个高点
                let timeFormatter = DateFormatter()
                timeFormatter.dateFormat = "HH:mm"
                let timeStr = timeFormatter.string(from: highestHeartRateRecord.date)
                highlightPeriod = "\(highestHeartRateRecord.hearts)bpm \(timeStr)"
                
                // 标记单个高亮点
                for i in 0..<chartPoints.count {
                    if chartPoints[i].time == highestHeartRateRecord.time {
                        chartPoints[i].isHighlightPoint = true
                        break
                    }
                }
            }
        }
        
        // 保存处理后的数据
        sleepHeartRateData = chartPoints
        
        print("睡眠心率数据处理完成: \(sleepHeartRateData.count)个数据点")
    }
} 
