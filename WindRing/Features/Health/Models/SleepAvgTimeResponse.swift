import Foundation

/// 7天平均睡眠时间响应模型
public struct SleepAvgTimeResponse: Codable {
    public var code: Int
    public var msg: String?
    public var data: SleepAvgTimeData?
    
    public init(code: Int, msg: String? = nil, data: SleepAvgTimeData? = nil) {
        self.code = code
        self.msg = msg
        self.data = data
    }
}

/// 7天平均睡眠时间数据
public struct SleepAvgTimeData: Codable {
    public var hours: Int
    public var minutes: Int
    
    public init(hours: Int = 0, minutes: Int = 0) {
        self.hours = hours
        self.minutes = minutes
    }
    
    /// 获取总分钟数
    public var totalMinutes: Int {
        return hours * 60 + minutes
    }
    
    /// 格式化输出
    public func formattedTime() -> String {
        return "\(hours) hr \(minutes) min"
    }
} 