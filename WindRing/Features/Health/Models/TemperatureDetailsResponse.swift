import Foundation

// MARK: - 体温详情响应
struct TemperatureDetailsResponse: Codable {
    var code: Int
    var msg: String
    var data: TemperatureDetailsData?
}

// MARK: - 体温数据模型
struct TemperatureDetailsData: Codable {
    var average: Double?     // 平均体温
    var baseline: Double?    // 基准体温/Reading
    var differ: Double?      // 温差
    
    // 处理可能为null的字段
    enum CodingKeys: String, CodingKey {
        case average, baseline, differ
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 处理可能为null的数值
        average = try container.decodeIfPresent(Double.self, forKey: .average) ?? 0.0
        baseline = try container.decodeIfPresent(Double.self, forKey: .baseline) ?? 0.0
        differ = try container.decodeIfPresent(Double.self, forKey: .differ) ?? 0.0
    }
    
    // 提供默认初始化方法
    init(average: Double? = 0.0, baseline: Double? = 0.0, differ: Double? = 0.0) {
        self.average = average
        self.baseline = baseline
        self.differ = differ
    }
} 