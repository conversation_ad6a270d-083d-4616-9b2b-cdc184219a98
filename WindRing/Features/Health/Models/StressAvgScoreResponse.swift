import Foundation

/// 7天平均压力分数响应模型
public struct StressAvgScoreResponse: Codable {
    public var code: Int
    public var msg: String?
    public var data: StressAvgScoreData?
    
    public init(code: Int, msg: String? = nil, data: StressAvgScoreData? = nil) {
        self.code = code
        self.msg = msg
        self.data = data
    }
}

/// 7天平均压力分数数据
public struct StressAvgScoreData: Codable {
    public var score: Int
    
    public init(score: Int = 0) {
        self.score = score
    }
} 