import Foundation

// MARK: - 睡眠心率详情响应
public struct HrSleepDetailsResponse: Codable {
    public var code: Int
    public var msg: String
    public var data: HrSleepDetailsData?
    
    // MARK: - 心率记录
    public struct HeartRateRecord: Codable, Identifiable {
        public var hearts: Int       // 心率值
        public var time: Int64       // 时间戳
        public var minHearts: Int    // 最小心率
        public var maxHearts: Int    // 最大心率
        
        // 提供ID以符合Identifiable协议
        public var id: String { "\(time)" }
        
        // 处理可能为null的字段
        enum CodingKeys: String, CodingKey {
            case hearts, time, minHearts, maxHearts
        }
        
        public init(from decoder: Decoder) throws {
            let container = try decoder.container(keyedBy: CodingKeys.self)
            
            // 处理可能为null的数值
            hearts = try container.decodeIfPresent(Int.self, forKey: .hearts) ?? 0
            time = try container.decodeIfPresent(Int64.self, forKey: .time) ?? 0
            minHearts = try container.decodeIfPresent(Int.self, forKey: .minHearts) ?? 0
            maxHearts = try container.decodeIfPresent(Int.self, forKey: .maxHearts) ?? 0
        }
        
        // 提供默认初始化方法
        public init(hearts: Int = 0, time: Int64 = 0, minHearts: Int = 0, maxHearts: Int = 0) {
            self.hearts = hearts
            self.time = time
            self.minHearts = minHearts
            self.maxHearts = maxHearts
        }
        
        // 解析时间，转换为Date对象
        public var date: Date {
            // API返回的是毫秒时间戳，直接转换
            return Date(timeIntervalSince1970: Double(time) / 1000.0)
        }
        
        // 获取格式化的时间字符串（HH:mm格式）
        public func formattedTime() -> String {
            let formatter = DateFormatter()
            formatter.dateFormat = "HH:mm"
            return formatter.string(from: date)
        }
    }
}

// MARK: - 睡眠心率详情数据
public struct HrSleepDetailsData: Codable {
    public var records: [HrSleepDetailsResponse.HeartRateRecord]
    public var avg: Int
    public var avg7: Int
    
    // 处理可能为null的字段
    enum CodingKeys: String, CodingKey {
        case records, avg, avg7
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 处理可能为null的数值
        records = try container.decodeIfPresent([HrSleepDetailsResponse.HeartRateRecord].self, forKey: .records) ?? []
        avg = try container.decodeIfPresent(Int.self, forKey: .avg) ?? 0
        avg7 = try container.decodeIfPresent(Int.self, forKey: .avg7) ?? 0
    }
    
    // 提供默认初始化方法
    public init(records: [HrSleepDetailsResponse.HeartRateRecord] = [], avg: Int = 0, avg7: Int = 0) {
        self.records = records
        self.avg = avg
        self.avg7 = avg7
    }
} 