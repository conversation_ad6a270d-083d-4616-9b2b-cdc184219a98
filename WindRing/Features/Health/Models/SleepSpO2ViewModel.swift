import Foundation
import SwiftUI
import Combine

// MARK: - Data Models for SpO2

struct SleepSpO2SleepDetailsResponse: Codable {
    let code: Int
    let msg: String?
    let data: SpO2SleepDetailsData?
}

public struct SpO2SleepDetailsData: Codable {
    let records: [SleepSpO2Record]
    let avg: Int        // Average SpO2 for the sleep period
    let avg7: Int       // 7-day average SpO2
    // Other summary fields from API can be added here, e.g., min_spo2, time_below_threshold, etc.
}

struct SleepSpO2Record: Codable, Identifiable {
    let id = UUID() // For Identifiable conformance
    let time: String  // Timestamp string from API, e.g., "yyyy-MM-dd HH:mm:ss"
    let spo2: Int     // SpO2 value, ensure key matches API (e.g., "value", "level")

    // Computed property to parse the time string into a Date object
    var date: Date {
        let formatter = DateFormatter()
        // Attempt common date formats; adjust to actual API output
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        if let d = formatter.date(from: time) { return d }
        
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ" // ISO8601
        if let d = formatter.date(from: time) { return d }

        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ" // ISO8601 with milliseconds
        if let d = formatter.date(from: time) { return d }
        
        // Fallback if parsing fails - logs an error or returns a default.
        // print("Warning: Failed to parse date string: \(time)")
        return Date() 
    }

    // Define CodingKeys if API field names differ from struct property names
    // enum CodingKeys: String, CodingKey {
    //     case time = "measureTime" // Example: if API uses "measureTime"
    //     case spo2 = "oxygenLevel" // Example: if API uses "oxygenLevel"
    // }
}

// Data point structure for use in charts
struct SleepSpO2Point: Identifiable {
    let id = UUID()
    let timeIndex: Int          // X-axis index for chart plotting
    let spo2: Int               // Y-axis value
    var isHighlightPoint: Bool = false // To mark specific points on the chart
    let time: String            // Original time "HH:mm" for display purposes

    // Calculates a numerical index from a Date for chart positioning.
    // Consistent with SleepHRVPoint's logic if charts share X-axis.
    static func calculateTimeIndex(from date: Date) -> Int {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        var index = 0
        // Assuming chart spans from 20:00 (index 0) to 10:00 next day (index 28)
        // This is 14 hours * 2 intervals/hour = 28 intervals.
        if hour >= 20 { // Current day: 20:00 - 23:59
            index = (hour - 20) * 2 + (minute >= 30 ? 1 : 0)
        } else { // Next day: 00:00 - 10:00 (or end of sleep period)
            // Offset for the 4 hours from 20:00 to 00:00 (4 hours * 2 indices/hour = 8)
            index = ( (hour * 2) + (minute >= 30 ? 1 : 0) ) + (4 * 2)
        }
        // Clamp index to the expected chart domain (e.g., 0-28)
        return max(0, min(28, index))
    }

    // Initializer to create an SpO2Point from an SpO2Record
    init(record: SleepSpO2Record, timeIndex: Int) {
        self.timeIndex = timeIndex
        self.spo2 = record.spo2
        self.isHighlightPoint = false // Default, can be updated by processing logic
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "HH:mm" // Format for display
        self.time = dateFormatter.string(from: record.date)
    }
}

/// ViewModel for managing sleep SpO2 data.
class SleepSpO2ViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var sleepSpO2Data: [SleepSpO2Point] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var averageSpO2: Int = 0
    @Published var recent7DayAverageSpO2: Int = 0
    @Published var timeRange: String = ""       // e.g., "22:05 - 06:30"
    @Published var highlightPeriod: String = "" // e.g., "92% at 03:15" or "88% 03:15-03:45"

    // MARK: - Private Properties
    private let apiService = APIService.shared // Assumes APIService.shared is available and configured
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init() {
        print("SleepSpO2ViewModel initialized.")
    }

    // MARK: - Public Methods

    /// Loads sleep SpO2 data for a specific date using Combine.
    func loadSleepSpO2Data(for date: Date) {
        guard !isLoading else { return }

        isLoading = true
        errorMessage = nil
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("SleepSpO2ViewModel: Loading SpO2 data for \(dateString)...")
        
        // Assumes an APIService structure like: APIService.shared.blood.getSpO2ToSleepDetailsCombine(...)
        // Replace with actual API call structure if different.
        apiService.health.getSpO2ToSleepDetailsCombine(date: dateString)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                guard let self = self else { return }
                self.isLoading = false
                switch completion {
                case .finished:
                    print("SleepSpO2ViewModel: SpO2 data request successfully completed.")
                case .failure(let error):
                    self.errorMessage = "Failed to load SpO2 data: \(error.localizedDescription)"
                    print("SleepSpO2ViewModel: Error - \(self.errorMessage ?? "Unknown error")")
                    self.resetDataFields() // Clear data on failure
                }
            } receiveValue: { [weak self] (response: SpO2SleepDetailsData) in
                guard let self = self else { return }
//                print("SleepSpO2ViewModel: Received SpO2 response. Code: \(response.code)")
//                if response.code == 200 || response.code == 0 { // Common success codes
//                    if let data = response.data {
                        self.processSpO2Data(response)
//                    } else {
//                        self.errorMessage = response.msg ?? "No SpO2 data available for this date."
//                        print("SleepSpO2ViewModel: \(self.errorMessage ?? "Data is nil")")
//                        self.resetDataFields()
//                    }
//                } else {
//                    self.errorMessage = response.msg ?? "API returned error code: \(response.code)."
//                    print("SleepSpO2ViewModel: \(self.errorMessage ?? "Unknown API error")")
//                    self.resetDataFields()
//                }
            }
            .store(in: &cancellables)
    }

//    /// Asynchronously loads sleep SpO2 data for a specific date.
//    func loadSleepSpO2DataAsync(for date: Date) async {
//        guard !isLoading else { return }
//
//        await MainActor.run {
//            isLoading = true
//            errorMessage = nil
//        }
//        
//        let dateFormatter = DateFormatter()
//        dateFormatter.dateFormat = "yyyy-MM-dd"
//        let dateString = dateFormatter.string(from: date)
//        
//        print("SleepSpO2ViewModel: Asynchronously loading SpO2 data for \(dateString)...")
//        
//        do {
//            // Assumes an APIService structure like: APIService.shared.blood.getSpO2ToSleepDetails(...)
//            let response = try await apiService.health.getSpO2ToSleepDetails(date: dateString)
//            
//            await MainActor.run {
//                isLoading = false
//                print("SleepSpO2ViewModel: Received async SpO2 response. Code: \(response.code)")
//                if response.code == 200 || response.code == 0 {
//                    if let data = response.data {
//                        processSpO2Data(data)
//                    } else {
//                        errorMessage = response.msg ?? "No SpO2 data available for this date."
//                        print("SleepSpO2ViewModel: \(errorMessage ?? "Data is nil")")
//                        resetDataFields()
//                    }
//                } else {
//                    errorMessage = response.msg ?? "API returned error code: \(response.code)."
//                    print("SleepSpO2ViewModel: \(errorMessage ?? "Unknown API error")")
//                    resetDataFields()
//                }
//            }
//        } catch {
//            await MainActor.run {
//                isLoading = false
//                errorMessage = "Failed to load SpO2 data: \(error.localizedDescription)"
//                print("SleepSpO2ViewModel: Async Error - \(errorMessage ?? "Unknown error")")
//                resetDataFields()
//            }
//        }
//    }

    /// Clears all published data fields and error messages.
    func clearData() {
        resetDataFields()
        errorMessage = nil
        if isLoading { // If clearing while loading, set isLoading to false
            isLoading = false
        }
        print("SleepSpO2ViewModel: Data cleared.")
    }

    // MARK: - Private Methods

    /// Resets all data-holding published properties to their default/empty state.
    private func resetDataFields() {
        sleepSpO2Data = []
        averageSpO2 = 0
        recent7DayAverageSpO2 = 0
        timeRange = ""
        highlightPeriod = ""
    }

    /// Processes the fetched SpO2 data and updates published properties.
    private func processSpO2Data(_ data: SpO2SleepDetailsData) {
        print("SleepSpO2ViewModel: Processing \(data.records.count) SpO2 records.")
        
        averageSpO2 = data.avg
        recent7DayAverageSpO2 = data.avg7
        
        if data.records.isEmpty {
            resetDataFields() // Ensure UI consistency
            print("SleepSpO2ViewModel: No records to process. Data fields have been reset.")
            return
        }
        
        let sortedRecords = data.records.sorted { $0.date < $1.date }
        
        var chartPoints: [SleepSpO2Point] = []
        let timeDisplayFormatter = DateFormatter()
        timeDisplayFormatter.dateFormat = "HH:mm"

        if let firstRecordDate = sortedRecords.first?.date, let lastRecordDate = sortedRecords.last?.date {
            let startTimeStr = timeDisplayFormatter.string(from: firstRecordDate)
            let endTimeStr = timeDisplayFormatter.string(from: lastRecordDate)
            timeRange = "\(startTimeStr) - \(endTimeStr)"
        } else {
            timeRange = ""
        }
        
        for record in sortedRecords {
            let timeIndex = SleepSpO2Point.calculateTimeIndex(from: record.date)
            chartPoints.append(SleepSpO2Point(record: record, timeIndex: timeIndex))
        }
        
        // Example highlight logic: find the lowest SpO2 value and its time.
        // More sophisticated logic could identify periods below a threshold.
        if let lowestSpO2Point = chartPoints.min(by: { $0.spo2 < $1.spo2 }) {
            if let index = chartPoints.firstIndex(where: { $0.id == lowestSpO2Point.id }) {
                chartPoints[index].isHighlightPoint = true // Mark for special rendering on chart
                highlightPeriod = "\(lowestSpO2Point.spo2)% at \(lowestSpO2Point.time)"
            }
        } else {
            highlightPeriod = ""
        }
        
        self.sleepSpO2Data = chartPoints
        print("SleepSpO2ViewModel: Processing complete. Generated \(chartPoints.count) chart points.")
        if chartPoints.isEmpty && !data.records.isEmpty {
             print("SleepSpO2ViewModel: WARNING - Records were present, but no chart points generated. Review time parsing, date construction, and timeIndex calculation.")
        }
    }
} 
