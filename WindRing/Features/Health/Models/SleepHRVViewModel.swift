import Foundation
import SwiftUI
import Combine

/// 睡眠HRV视图模型 - 负责处理和管理睡眠HRV数据
class SleepHRVViewModel: ObservableObject {
    // MARK: - 发布属性
    @Published var sleepHRVData: [SleepHRVPoint] = [] // HRV数据点
    @Published var isLoading: Bool = false // 是否正在加载
    @Published var errorMessage: String? = nil // 错误信息
    @Published var averageHRV: Int = 0 // 平均HRV
    @Published var recent7DayAverage: Int = 0 // 最近7天平均HRV
    @Published var timeRange: String = "" // 时间范围
    @Published var highlightPeriod: String = "" // 高亮区间
    @Published var healthStatus: HRVHealthStatus = .unknown // 健康状态
    
    // MARK: - 私有属性
    private let apiService = APIService.shared // API服务
    private var cancellables = Set<AnyCancellable>() // 用于存储和管理订阅
    
    // MARK: - 初始化
    init() {
        print("SleepHRVViewModel初始化")
    }
    
    // MARK: - 公共方法
    
    /// 加载指定日期的睡眠HRV数据
    /// - Parameter date: 日期
    func loadSleepHRVData(for date: Date) {
        guard !isLoading else { return } // 防止重复加载
        
        // 设置状态
        isLoading = true
        errorMessage = nil
        
        // 格式化日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("开始加载日期\(dateString)的睡眠HRV数据")
        
        // 使用Combine API
        apiService.health.getHRVToSleepDetailsCombine(date: dateString)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (completion: Subscribers.Completion<NetworkError>) in
                guard let self = self else { return }
                
                self.isLoading = false
                
                switch completion {
                case .finished:
                    print("睡眠HRV数据请求成功完成")
                case .failure(let error):
                    self.errorMessage = "获取睡眠HRV数据失败: \(error.localizedDescription)"
                    print("睡眠HRV数据请求失败: \(error.localizedDescription)")
                }
            } receiveValue: { [weak self] (response: HRVSleepDetailsData) in
                guard let self = self else { return }
                
                print("收到睡眠HRV数据响应")
                
                // 处理响应
//                if let data = response.data {
                    self.processHRVData(response)
//                } else {
//                    self.errorMessage = "没有睡眠HRV数据"
//                    print("睡眠HRV数据为空")
//                }
            }
            .store(in: &cancellables)
    }
    
    /// 异步版本加载指定日期的睡眠HRV数据
    /// - Parameter date: 日期
    func loadSleepHRVDataAsync(for date: Date) async {
        guard !isLoading else { return } // 防止重复加载
        
        // 在主线程上更新UI状态
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        // 格式化日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("开始异步加载日期\(dateString)的睡眠HRV数据")
        
        do {
            let response = try await apiService.health.getHRVToSleepDetails(date: dateString)
            
            // 在主线程上更新UI
            await MainActor.run {
                isLoading = false
                
                if let data = response.data {
                    processHRVData(data)
                } else {
                    errorMessage = "没有睡眠HRV数据"
                    print("睡眠HRV数据为空")
                }
            }
        } catch {
            // 在主线程上更新UI错误状态
            await MainActor.run {
                isLoading = false
                errorMessage = "获取睡眠HRV数据失败: \(error.localizedDescription)"
                print("睡眠HRV数据请求失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 清除数据和错误状态
    func clearData() {
        sleepHRVData = []
        errorMessage = nil
        averageHRV = 0
        recent7DayAverage = 0
        timeRange = ""
        highlightPeriod = ""
        healthStatus = .unknown
    }
    
    // MARK: - 私有方法
    
    /// 处理HRV数据
    /// - Parameter data: HRV数据
    private func processHRVData(_ data: HRVSleepDetailsData) {
        print("处理睡眠HRV数据: \(data.records.count)条记录")
        
        // 更新平均值
        averageHRV = data.avg
        recent7DayAverage = data.avg7
        
        // 设置健康状态
        healthStatus = HRVHealthStatus.statusFor(hrv: averageHRV)
        
        // 没有记录时直接返回
        if data.records.isEmpty {
            sleepHRVData = []
            return
        }
        
        // 将记录转换为图表数据点
        var chartPoints: [SleepHRVPoint] = []
        
        // 创建一个索引对照表，以便在没有数据的时间点上生成空数据点
        var timeIndicesMap: [Int: SleepHRVPoint] = [:]
        
        // 首先对记录按时间排序
        let sortedRecords = data.records.sorted { $0.time < $1.time }
        
        // 计算时间范围
        if let firstRecord = sortedRecords.first, let lastRecord = sortedRecords.last {
            // 创建时间格式化器
            let timeFormatter = DateFormatter()
            timeFormatter.dateFormat = "HH:mm"
            
            // 转换时间
            let startTime = timeFormatter.string(from: firstRecord.date)
            let endTime = timeFormatter.string(from: lastRecord.date)
            
            // 设置时间范围
            timeRange = "\(startTime) - \(endTime)"
        }
        
        // 处理每条记录
        for record in sortedRecords {
            // 创建数据点
            let timeIndex = SleepHRVPoint.calculateTimeIndex(from: record.date)
            let point = SleepHRVPoint(from: record, timeIndex: timeIndex)
            
            // 添加到集合
            chartPoints.append(point)
            timeIndicesMap[timeIndex] = point
        }
        
        // 查找最高HRV区间
        if let highestHRVRecord = sortedRecords.max(by: { $0.hrv < $1.hrv }) {
            // 查找相近的高HRV点形成区间
            let highThreshold = highestHRVRecord.hrv * 9 / 10 // 90%的最高值作为阈值
            let highHRVRecords = sortedRecords.filter { $0.hrv >= highThreshold }
            
            if let firstHigh = highHRVRecords.first, let lastHigh = highHRVRecords.last {
                // 创建时间格式化器
                let timeFormatter = DateFormatter()
                timeFormatter.dateFormat = "HH:mm"
                
                // 转换时间
                let startTime = timeFormatter.string(from: firstHigh.date)
                let endTime = timeFormatter.string(from: lastHigh.date)
                
                // 设置高亮区间
                highlightPeriod = "\(highestHRVRecord.hrv)ms \(startTime)-\(endTime)"
                
                // 标记高亮点
                for i in 0..<chartPoints.count {
                    if chartPoints[i].hrv >= highThreshold {
                        chartPoints[i].isHighlightPoint = true
                    }
                }
            } else {
                // 单个高点
                let timeFormatter = DateFormatter()
                timeFormatter.dateFormat = "HH:mm"
                let timeStr = timeFormatter.string(from: highestHRVRecord.date)
                highlightPeriod = "\(highestHRVRecord.hrv)ms \(timeStr)"
                
                // 标记单个高亮点
                for i in 0..<chartPoints.count {
                    if chartPoints[i].time == highestHRVRecord.time {
                        chartPoints[i].isHighlightPoint = true
                        break
                    }
                }
            }
        }
        
        // 保存处理后的数据
        sleepHRVData = chartPoints
        
        print("睡眠HRV数据处理完成: \(sleepHRVData.count)个数据点")
    }
    
    // MARK: - 健康状态相关
    
    /// 获取健康状态颜色
    var healthStatusColor: Color {
        return healthStatus.color
    }
    
    /// 获取健康状态描述
    var healthStatusDescription: String {
        return healthStatus.description
    }
    
    /// 获取健康建议
    var healthAdvice: String {
        return healthStatus.advice
    }
} 
