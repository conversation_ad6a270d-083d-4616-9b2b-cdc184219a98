import Foundation

// MARK: - 睡眠HRV详情响应
public struct HRVSleepDetailsResponse: Codable {
    public var code: Int
    public var msg: String
    public var data: HRVSleepDetailsData?
    
    // MARK: - HRV记录
    public struct HRVRecord: Codable, Identifiable {
        public var hrv: Int         // HRV值
        public var time: Int64       // 时间戳
        public var minHRV: Int      // 最小HRV
        public var maxHRV: Int      // 最大HRV
        
        // 提供ID以符合Identifiable协议
        public var id: String { "\(time)" }
        
        // 处理可能为null的字段
        enum CodingKeys: String, CodingKey {
            case hrv, time, minHRV, maxHRV
        }
        
        public init(from decoder: Decoder) throws {
            let container = try decoder.container(keyedBy: CodingKeys.self)
            
            // 处理可能为null的数值
            hrv = try container.decodeIfPresent(Int.self, forKey: .hrv) ?? 0
            time = try container.decodeIfPresent(Int64.self, forKey: .time) ?? 0
            minHRV = try container.decodeIfPresent(Int.self, forKey: .minHRV) ?? 0
            maxHRV = try container.decodeIfPresent(Int.self, forKey: .maxHRV) ?? 0
        }
        
        // 提供默认初始化方法
        public init(hrv: Int = 0, time: Int64 = 0, minHRV: Int = 0, maxHRV: Int = 0) {
            self.hrv = hrv
            self.time = time
            self.minHRV = minHRV
            self.maxHRV = maxHRV
        }
        
        // 解析时间，转换为Date对象
        public var date: Date {
            // API返回的是毫秒时间戳，直接转换
            return Date(timeIntervalSince1970: Double(time) / 1000.0)
        }
        
        // 获取格式化的时间字符串（HH:mm格式）
        public func formattedTime() -> String {
            let formatter = DateFormatter()
            formatter.dateFormat = "HH:mm"
            return formatter.string(from: date)
        }
    }
}

// MARK: - 睡眠HRV详情数据
public struct HRVSleepDetailsData: Codable {
    public var records: [HRVSleepDetailsResponse.HRVRecord]
    public var avg: Int
    public var avg7: Int
    
    // 处理可能为null的字段
    enum CodingKeys: String, CodingKey {
        case records, avg, avg7
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 处理可能为null的数值
        records = try container.decodeIfPresent([HRVSleepDetailsResponse.HRVRecord].self, forKey: .records) ?? []
        avg = try container.decodeIfPresent(Int.self, forKey: .avg) ?? 0
        avg7 = try container.decodeIfPresent(Int.self, forKey: .avg7) ?? 0
    }
    
    // 提供默认初始化方法
    public init(records: [HRVSleepDetailsResponse.HRVRecord] = [], avg: Int = 0, avg7: Int = 0) {
        self.records = records
        self.avg = avg
        self.avg7 = avg7
    }
} 