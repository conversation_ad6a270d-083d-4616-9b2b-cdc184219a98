import SwiftUI
import Foundation

/// 睡眠HRV图表数据点模型
struct SleepHRVPoint: Identifiable {
    var id = UUID()
    var hrv: Int           // HRV值
    var time: Int64        // 时间戳
    var timeIndex: Int     // 时间索引(0-47，对应一天中的48个半小时)
    var hasData: Bool      // 是否有数据
    var isHighlightPoint: Bool = false // 是否为高亮点
    
    // 坐标值 - 用于绘制图表
    var x: CGFloat = 0
    var y: CGFloat = 0
    
    // 初始化方法
    init(hrv: Int, time: Int64, timeIndex: Int, hasData: Bool = true, isHighlightPoint: Bool = false) {
        self.hrv = hrv
        self.time = time
        self.timeIndex = timeIndex
        self.hasData = hasData
        self.isHighlightPoint = isHighlightPoint
    }
    
    // 通过HRVRecord创建
    init(from record: HRVSleepDetailsResponse.HRVRecord, timeIndex: Int? = nil, isHighlightPoint: Bool = false) {
        self.hrv = record.hrv
        self.time = record.time
        self.hasData = record.hrv > 0
        self.isHighlightPoint = isHighlightPoint
        
        // 如果外部提供了timeIndex，就使用外部提供的
        if let providedIndex = timeIndex {
            self.timeIndex = providedIndex
        } else {
            // 否则根据时间计算timeIndex
            self.timeIndex = Self.calculateTimeIndex(from: record.date)
        }
    }
    
    // 获取时间点(小时)
    var hourString: String {
        let date = Date(timeIntervalSince1970: Double(time) / 1000.0)
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    // 计算时间索引(0-47)
//    static func calculateTimeIndex(from date: Date) -> Int {
//        let calendar = Calendar.current
//        let hour = calendar.component(.hour, from: date)
//        let minute = calendar.component(.minute, from: date)
//        
//        return hour * 2 + (minute >= 30 ? 1 : 0)
//    }
    
    static func calculateTimeIndex(from date: Date) -> Int {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)

        let referenceHour = 20 // 20:00 is our baseline

        var totalMinutesSinceReference: Int
        
        if hour >= referenceHour { // Current day, from 20:00 up to 23:59
            totalMinutesSinceReference = (hour - referenceHour) * 60 + minute
        } else { // Next day, from 00:00 up to 09:59 (or 10:00 exclusive for upper bound of data)
            // Add 4 hours (from 20:00 to 24:00 of the previous day)
            totalMinutesSinceReference = (hour + (24 - referenceHour)) * 60 + minute
        }

        // Calculate the number of 30-minute intervals
        let timeIndex = totalMinutesSinceReference / 30
        
        // The maximum index for the range 20:00 to 10:00 (next day) is 28.
        // (14 hours * 2 intervals/hour = 28 intervals)
        // Example: 10:00 next day is (10 + 4) * 60 / 30 = 14 * 2 = 28.
        // If the time is exactly 10:00, it would be the start of index 28.
        // If your data for 10:00 means "up to 10:00", then index 27 might be the last relevant one.
        // Let's cap it for safety if needed, or you can adjust based on how 10:00 is handled.
        // For now, let's assume up to index 28 (inclusive of start of 10:00 slot) is valid.
        // Max index for 20:00 to 09:30 is 27. Max index covering up to 10:00 is 28.
        
        return timeIndex
    }
    
    // 检查是否为睡眠期间的数据点
    var isSleepPeriod: Bool {
        // 睡眠时间段通常为晚上10点到早上8点
        let hour = timeIndex / 2
        return hour >= 22 || hour < 8
    }
    
    // 获取HRV健康状态
    var hrvHealthStatus: HRVHealthStatus {
        return HRVHealthStatus.statusFor(hrv: hrv)
    }
} 
