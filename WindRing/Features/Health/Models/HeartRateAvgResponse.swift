import Foundation

/// 7天平均心率响应模型
public struct HeartRateAvgResponse: Codable {
    public var code: Int
    public var msg: String?
    public var data: HeartRateAvgData?
    
    public init(code: Int, msg: String? = nil, data: HeartRateAvgData? = nil) {
        self.code = code
        self.msg = msg
        self.data = data
    }
}

/// 7天平均心率数据
public struct HeartRateAvgData: Codable {
    public var bpm: Int
    
    public init(bpm: Int = 0) {
        self.bpm = bpm
    }
} 