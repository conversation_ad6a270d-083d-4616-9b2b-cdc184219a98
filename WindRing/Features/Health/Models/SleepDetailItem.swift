import SwiftUI

// MARK: - Sleep Detail Item Struct
struct SleepDetailItem: Identifiable {
    let id: Int
    let type: Int // 0: 清醒, 1: 浅睡, 2: 深睡, 3: REM
    let startTime: Date
    let endTime: Date
    let durationMinutes: Int
    
    var typeText: String {
        switch type {
        case 0: return "清醒"
        case 1: return "浅睡"
        case 2: return "深睡"
        case 3: return "REM"
        default: return "未知"
        }
    }
    
    var icon: String {
        switch type {
        case 0: return "sun.max.fill"
        case 1: return "moon.fill"
        case 2: return "moon.stars.fill"
        case 3: return "eye.fill"
        default: return "questionmark.circle.fill"
        }
    }
    
    var color: Color {
        switch type {
        case 0: return .orange
        case 1: return .purple
        case 2: return .indigo
        case 3: return .pink
        default: return .gray
        }
    }
    
    var startTimeText: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: startTime)
    }
    
    var endTimeText: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: endTime)
    }
    
    var durationText: String {
        let hours = durationMinutes / 60
        let minutes = durationMinutes % 60
        
        if hours > 0 {
            return "\(hours)h\(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
} 