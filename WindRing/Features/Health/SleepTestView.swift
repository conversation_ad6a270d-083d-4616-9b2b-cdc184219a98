import SwiftUI
import CRPSmartRing
import UIKit
import UniformTypeIdentifiers

/// 睡眠测试视图 - 用于测试从智能戒指获取睡眠数据
struct SleepTestView: View {
    // MARK: - 状态属性
    @StateObject private var deviceService = WindRingDeviceService.shared
    @State private var selectedDay = 0 // 0=今天，1=昨天，2=前天...
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var showShareSheet = false
    @State private var csvData: String = ""
    
    // 睡眠数据
    @State private var sleepData: CRPSleepRecordModel? = nil
    @State private var sleepDetails: [SleepDetailItem] = []
    
    // 添加历史数据相关状态
    @State private var isLoadingHistory = false
    @State private var historySleepData: [Int: CRPSleepRecordModel] = [:] // 键是天数，值是睡眠数据
    @State private var historyLoadProgress: Double = 0
    @State private var showHistoryData = false
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            ScrollView {
                VStack {
                    // 连接状态
                    connectionStatusView
                    
                    // 日期选择
                    daySelectionView
                    
                    // 获取数据按钮
                    fetchDataButton
                    
                    // 添加获取历史数据按钮
                    fetchHistoryDataButton
                    
                    if isLoading {
                        ProgressView("获取数据中...")
                            .padding()
                    } else if let errorMsg = errorMessage {
                        Text(errorMsg)
                            .foregroundColor(.red)
                            .padding()
                    } else if let data = sleepData {
                        // 睡眠数据总结
                        sleepSummaryView(data: data)
                        
                        // 睡眠阶段图表
                        if !sleepDetails.isEmpty {
                            sleepChartView
                                .padding()
                                .background(Color.moduleBackground)
                                .cornerRadius(12)
                                .padding(.top)
                                
                            // 导出按钮
                            exportButton
                        }
                        
                        // 如果有历史数据，显示导出历史数据按钮
                        if !historySleepData.isEmpty {
                            exportHistoryButton
                        }
                        
                        // 睡眠阶段详情
                        sleepDetailsView
                    } else {
                        Text("请点击《获取睡眠数据》按钮")
                            .foregroundColor(.gray)
                            .padding()
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("睡眠数据测试")
            .sheet(isPresented: $showShareSheet) {
                ShareSheet(activityItems: [csvData])
            }
        }
        .background(Color.moduleBackground)
        .cornerRadius(12)
    }
    
    // MARK: - 子视图
    
    /// 连接状态视图
    private var connectionStatusView: some View {
        HStack {
            Circle()
                .fill(deviceService.connectionState.isConnected ? Color.green : Color.red)
                .frame(width: 12, height: 12)
            
            Text(deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized)
                .font(.footnote)
            
            Spacer()
            
            if !deviceService.connectionState.isConnected {
                Button("连接设备") {
                    // 这里应该打开设备管理页面，但现在我们只显示一个提示
                    errorMessage = "请前往设备页面连接智能戒指"
                }
                .font(.footnote)
                .buttonStyle(.bordered)
            }
        }
        .padding(.bottom)
    }
    
    /// 日期选择视图
    private var daySelectionView: some View {
        VStack(alignment: .leading) {
            Text("选择日期：")
                .font(.headline)
            
            Picker("选择日期", selection: $selectedDay) {
                Text("今天").tag(0)
                Text("昨天").tag(1)
                Text("前天").tag(2)
                Text("3天前").tag(3)
                Text("4天前").tag(4)
                Text("5天前").tag(5)
                Text("6天前").tag(6)
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.bottom)
        }
    }
    
    /// 获取数据按钮
    private var fetchDataButton: some View {
        Button(action: fetchSleepData) {
            HStack {
                Image(systemName: "arrow.down.circle")
                Text("获取睡眠数据")
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(.borderedProminent)
        .padding(.bottom)
        .disabled(!deviceService.connectionState.isConnected || isLoading || isLoadingHistory)
    }
    
    /// 获取历史数据按钮
    private var fetchHistoryDataButton: some View {
        Button(action: fetchAllHistorySleepData) {
            HStack {
                Image(systemName: "clock.arrow.circlepath")
                Text("获取所有历史睡眠数据")
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(.bordered)
        .padding(.bottom)
        .disabled(!deviceService.connectionState.isConnected || isLoading || isLoadingHistory)
        .overlay(
            Group {
                if isLoadingHistory {
                    ProgressView(value: historyLoadProgress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle())
                        .frame(height: 2)
                        .padding(.horizontal)
                        .animation(.linear, value: historyLoadProgress)
                }
            },
            alignment: .bottom
        )
    }
    
    /// 导出按钮
    private var exportButton: some View {
        Button(action: exportData) {
            HStack {
                Image(systemName: "square.and.arrow.up")
                Text("导出睡眠数据")
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(.bordered)
        .padding(.vertical)
    }
    
    /// 导出历史数据按钮
    private var exportHistoryButton: some View {
        Button(action: exportHistoryData) {
            HStack {
                Image(systemName: "square.and.arrow.up.on.square")
                Text("导出所有历史睡眠数据")
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(.bordered)
        .foregroundColor(.blue)
        .padding(.vertical)
    }
    
    /// 睡眠数据总结视图
    private func sleepSummaryView(data: CRPSleepRecordModel) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("睡眠总结")
                .font(.headline)
            
            // 睡眠时间总计
            let totalMinutes = data.deep + data.light + data.rem
            let hours = totalMinutes / 60
            let minutes = totalMinutes % 60
            
            HStack {
                SleepStatCard(
                    title: "总睡眠时间",
                    value: "\(hours)小时\(minutes)分钟",
                    icon: "bed.double",
                    color: .blue
                )
            }
            
            HStack {
                SleepStatCard(
                    title: "深睡",
                    value: "\(data.deep)分钟",
                    icon: "moon.stars",
                    color: .indigo
                )
                
                SleepStatCard(
                    title: "浅睡",
                    value: "\(data.light)分钟",
                    icon: "moon",
                    color: .purple
                )
                
                SleepStatCard(
                    title: "REM",
                    value: "\(data.rem)分钟",
                    icon: "eye",
                    color: .pink
                )
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(12)
    }
    
    /// 睡眠阶段图表视图
    private var sleepChartView: some View {
        VStack(alignment: .leading) {
            Text("睡眠阶段图表")
                .font(.headline)
                .padding(.bottom, 8)
            
            // 睡眠阶段类型说明
            HStack {
                ForEach([
                    (type: "清醒", color: Color.orange),
                    (type: "浅睡", color: Color.purple),
                    (type: "深睡", color: Color.indigo),
                    (type: "REM", color: Color.pink)
                ], id: \.type) { item in
                    HStack {
                        Rectangle()
                            .fill(item.color)
                            .frame(width: 12, height: 12)
                        Text(item.type)
                            .font(.caption)
                    }
                    .padding(.trailing, 8)
                }
                Spacer()
            }
            .padding(.bottom, 8)
            
            // 睡眠图表
            if let firstDetail = sleepDetails.first, let lastDetail = sleepDetails.last {
                let startTime = firstDetail.startTime
                let endTime = lastDetail.endTime
                let totalDuration = endTime.timeIntervalSince(startTime)
                
                VStack(alignment: .leading, spacing: 0) {
                    // 添加时间轴
                    timeAxisView(startTime: startTime, endTime: endTime)
                        .padding(.bottom, 4)
                    
                    // 睡眠阶段图表
                    ZStack(alignment: .leading) {
                        // 背景网格
                        VStack(spacing: 0) {
                            ForEach(0..<3) { _ in
                                Divider()
                                Spacer()
                            }
                            Divider()
                        }
                        
                        // 睡眠阶段条形图
                        HStack(spacing: 0) {
                            ForEach(sleepDetails) { detail in
                                let width = CGFloat(detail.endTime.timeIntervalSince(detail.startTime) / totalDuration) * UIScreen.main.bounds.width * 0.8
                                
                                Rectangle()
                                    .fill(detail.color)
                                    .frame(width: max(1, width), height: 60)
                                    .opacity(0.8)
                            }
                        }
                    }
                    .frame(height: 60)
                    .padding(.bottom, 4)
                }
            }
        }
    }
    
    /// 时间轴视图
    private func timeAxisView(startTime: Date, endTime: Date) -> some View {
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm"
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour], from: startTime, to: endTime)
        let hours = max(1, components.hour ?? 1)
        
        return HStack {
            // 小时标记
            ForEach(0...hours, id: \.self) { hour in
                if let hourDate = calendar.date(byAdding: .hour, value: hour, to: startTime) {
                    if hourDate <= endTime {
                        Text(timeFormatter.string(from: hourDate))
                            .font(.caption2)
                            .foregroundColor(.gray)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
            }
        }
    }
    
    /// 睡眠阶段详情视图
    private var sleepDetailsView: some View {
        VStack(alignment: .leading) {
            Text("睡眠阶段详情")
                .font(.headline)
                .padding(.top)
            
            if sleepDetails.isEmpty {
                Text("没有详细的睡眠阶段数据")
                    .foregroundColor(.gray)
                    .padding()
            } else {
                List {
                    ForEach(sleepDetails) { detail in
                        HStack {
                            Image(systemName: detail.icon)
                                .foregroundColor(detail.color)
                            
                            VStack(alignment: .leading) {
                                Text(detail.typeText)
                                    .font(.headline)
                                Text("\(detail.startTimeText) - \(detail.endTimeText)")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                            }
                            
                            Spacer()
                            
                            Text(detail.durationText)
                                .font(.callout)
                        }
                        .padding(.vertical, 4)
                    }
                }
                .listStyle(PlainListStyle())
                .frame(height: 300)
            }
        }
    }
    
    // MARK: - 功能方法
    
    /// 获取睡眠数据
    private func fetchSleepData() {
        guard deviceService.connectionState.isConnected else {
            errorMessage = "设备未连接，请先连接设备"
            return
        }
        
        isLoading = true
        errorMessage = nil
        sleepData = nil
        sleepDetails = []
        
        CRPSmartRingSDK.sharedInstance.getSleepData(selectedDay) { model, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if error == .none {
                    sleepData = model
                    parseSleepDetails(from: model.detail)
                    errorMessage = nil
                    
                    // 在调试日志中打印睡眠数据详情
                    print("\n============ 睡眠数据详情 ============")
                    print("日期: \(Calendar.current.date(byAdding: .day, value: -self.selectedDay, to: Date()) ?? Date())")
                    print("深睡时间: \(model.deep) 分钟")
                    print("浅睡时间: \(model.light) 分钟")
                    print("REM时间: \(model.rem) 分钟")
                    print("总睡眠时间: \(model.deep + model.light + model.rem) 分钟")
                    
                    // 打印详细的睡眠阶段信息
                    if model.detail.isEmpty {
                        print("没有详细的睡眠阶段数据")
                    } else {
                        print("\n----- 睡眠阶段详情 -----")
                        let dateFormatter = DateFormatter()
                        dateFormatter.dateFormat = "HH:mm:ss"
                        
                        for (index, detail) in model.detail.enumerated() {
                            if let typeString = detail["type"],
                               let startTimeString = detail["start"],
                               let endTimeString = detail["end"],
                               let totalString = detail["total"],
                               let type = Int(typeString),
                               let startTime = TimeInterval(startTimeString),
                               let endTime = TimeInterval(endTimeString),
                               let total = Int(totalString) {
                                
                                let startDate = Date(timeIntervalSince1970: startTime)
                                let endDate = Date(timeIntervalSince1970: endTime)
                                
                                let typeText: String
                                switch type {
                                case 0: typeText = "清醒"
                                case 1: typeText = "浅睡"
                                case 2: typeText = "深睡"
                                case 3: typeText = "REM"
                                default: typeText = "未知"
                                }
                                
                                print("阶段 #\(index+1): \(typeText)")
                                print("  开始时间: \(dateFormatter.string(from: startDate))")
                                print("  结束时间: \(dateFormatter.string(from: endDate))")
                                print("  持续时间: \(total) 分钟")
                            }
                        }
                    }
                    
                    // 打印JSON格式的睡眠数据（格式与截图中类似）
                    let now = Date()
                    let calendar = Calendar.current
                    let selectedDate = calendar.date(byAdding: .day, value: -self.selectedDay, to: now) ?? now
                    
                    let sleepJsonData: [String: Any] = [
                        "total_minutes": model.deep + model.light + model.rem,
                        "awake_minutes": 0, // SDK模型中可能没有此字段，显示为0
                        "end_time": ISO8601DateFormatter().string(from: selectedDate),
                        "total_sleep_minutes": model.deep + model.light + model.rem,
                        "start_time": ISO8601DateFormatter().string(from: selectedDate.addingTimeInterval(-Double(model.deep + model.light + model.rem) * 60)),
                        "deep_sleep_minutes": model.deep,
                        "light_sleep_minutes": model.light,
                        "rem_sleep_minutes": model.rem,
                        "sleep_quality": 0 // SDK模型中可能没有此字段，显示为0
                    ]
                    
                    // 将字典转换为JSON格式的字符串
                    if let jsonData = try? JSONSerialization.data(withJSONObject: sleepJsonData, options: .prettyPrinted),
                       let jsonString = String(data: jsonData, encoding: .utf8) {
                        print("\n----- 睡眠数据JSON格式 -----")
                        print(jsonString)
                    }
                    
                    print("====================================\n")
                } else {
                    errorMessage = "获取睡眠数据失败: \(error)"
                    sleepData = nil
                }
            }
        }
        
        // 添加超时处理，确保UI不会一直处于加载状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 15.0) {
            if self.isLoading {
                self.isLoading = false
                self.errorMessage = "获取数据超时，请重试"
            }
        }
    }
    
    /// 解析睡眠阶段详情
    private func parseSleepDetails(from details: [Dictionary<String, String>]) {
        var parsedDetails: [SleepDetailItem] = []
        
        for (index, detail) in details.enumerated() {
            if let typeString = detail["type"],
               let startTimeString = detail["start"],
               let endTimeString = detail["end"],
               let type = Int(typeString),
               let startTime = TimeInterval(startTimeString),
               let endTime = TimeInterval(endTimeString) {
                
                let startDate = Date(timeIntervalSince1970: startTime)
                let endDate = Date(timeIntervalSince1970: endTime)
                let durationMinutes = Int(endTime - startTime) / 60
                
                let item = SleepDetailItem(
                    id: index,
                    type: type,
                    startTime: startDate,
                    endTime: endDate,
                    durationMinutes: durationMinutes
                )
                
                parsedDetails.append(item)
            }
        }
        
        // 按时间排序
        sleepDetails = parsedDetails.sorted { $0.startTime < $1.startTime }
    }
    
    /// 获取所有历史睡眠数据
    private func fetchAllHistorySleepData() {
        guard deviceService.connectionState.isConnected else {
            errorMessage = "设备未连接，请先连接设备"
            return
        }
        
        isLoadingHistory = true
        historyLoadProgress = 0
        historySleepData.removeAll()
        errorMessage = nil
        
        // 定义要获取的天数范围（例如过去7天）
        let daysToFetch = 7
        let totalDays = daysToFetch
        var completedDays = 0
        
        // 使用递归函数逐个获取数据，避免阻塞主线程
        func fetchNextDay(day: Int) {
            guard day < daysToFetch else {
                // 所有天数已获取完成
                if historySleepData.isEmpty {
                    self.errorMessage = "未能获取任何历史睡眠数据"
                } else {
                    // 处理获取到的历史数据
                    self.processHistorySleepData()
                }
                self.isLoadingHistory = false
                return
            }
            
            CRPSmartRingSDK.sharedInstance.getSleepData(day) { model, error in
                DispatchQueue.main.async {
                    completedDays += 1
                    self.historyLoadProgress = Double(completedDays) / Double(totalDays)
                    
                    if error == .none {
                        self.historySleepData[day] = model
                    } else {
                        print("获取第\(day)天睡眠数据失败: \(error)")
                    }
                    
                    // 延迟0.5秒后获取下一天的数据
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        fetchNextDay(day: day + 1)
                    }
                }
            }
        }
        
        // 开始获取第一天的数据
        fetchNextDay(day: 0)
        
        // 添加超时处理
        DispatchQueue.main.asyncAfter(deadline: .now() + 30.0) {
            if self.isLoadingHistory {
                self.isLoadingHistory = false
                self.errorMessage = "获取历史数据超时，请重试"
            }
        }
    }
    
    /// 处理历史睡眠数据
    private func processHistorySleepData() {
        // 如果有数据，默认显示最近一天的数据
        if let mostRecentData = historySleepData.sorted(by: { $0.key < $1.key }).first?.value {
            sleepData = mostRecentData
            parseSleepDetails(from: mostRecentData.detail)
            
            // 准备导出所有历史数据的CSV
            prepareHistoryDataCSV()
        }
    }
    
    /// 准备历史数据CSV
    private func prepareHistoryDataCSV() {
        var csv = "日期,深睡(分钟),浅睡(分钟),REM(分钟),总睡眠(分钟)\n"
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        // 按日期排序（从最近到最远）
        let sortedData = historySleepData.sorted { $0.key < $1.key }
        
        for (day, data) in sortedData {
            let date = Date().addingTimeInterval(-Double(day) * 24 * 60 * 60)
            let dateString = dateFormatter.string(from: date)
            let totalSleep = data.deep + data.light + data.rem
            
            csv += "\(dateString),\(data.deep),\(data.light),\(data.rem),\(totalSleep)\n"
        }
        
        // 保存CSV数据
        csvData = csv
    }
    
    /// 导出所有历史睡眠数据
    private func exportHistoryData() {
        guard !historySleepData.isEmpty else {
            errorMessage = "没有历史数据可导出"
            return
        }
        
        // 显示分享表单
        showShareSheet = true
    }
    
    /// 导出睡眠数据为CSV
    private func exportData() {
        guard let data = sleepData, !sleepDetails.isEmpty else {
            errorMessage = "没有数据可导出"
            return
        }
        
        // 创建CSV数据
        var csv = "日期,类型,开始时间,结束时间,持续时间(分钟)\n"
        
        // 获取日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let date = dateFormatter.string(from: Date().addingTimeInterval(-Double(selectedDay) * 24 * 60 * 60))
        
        // 添加总结数据
        csv += "\(date),总睡眠,,,(深睡: \(data.deep)分钟, 浅睡: \(data.light)分钟, REM: \(data.rem)分钟)\n"
        
        // 添加详细数据
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm:ss"
        
        for detail in sleepDetails {
            let typeStr = detail.typeText
            let startTimeStr = timeFormatter.string(from: detail.startTime)
            let endTimeStr = timeFormatter.string(from: detail.endTime)
            
            csv += "\(date),\(typeStr),\(startTimeStr),\(endTimeStr),\(detail.durationMinutes)\n"
        }
        
        // 保存CSV数据
        csvData = csv
        
        // 显示分享表单
        showShareSheet = true
    }
}

// MARK: - 辅助视图和模型

/// 睡眠统计卡片
struct SleepStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack {
            Image(systemName: icon)
                .font(.title)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
            
            Text(value)
                .font(.headline)
                .padding(.top, 2)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(color.opacity(0.1))
        .cornerRadius(10)
    }
}

// MARK: - 预览
struct SleepTestView_Previews: PreviewProvider {
    static var previews: some View {
        SleepTestView()
    }
} 
