import SwiftUI
import Combine

// MARK: - 测量状态机
enum SpO2MeasurementState {
    case idle
    case measuring
    case completed(value: Int, time: Date)

    var isIdle: Bool {
        if case .idle = self { return true }
        return false
    }

    static func == (lhs: SpO2MeasurementState, rhs: SpO2MeasurementState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.measuring, .measuring):
            return true
        case let (.completed(lv, lt), .completed(rv, rt)):
            return lv == rv && lt == rt
        default:
            return false
        }
    }
}

// MARK: - 血氧视图模型
class SpO2ViewModel: ObservableObject {
    @Published var measurementState: SpO2MeasurementState = .idle
    // 数据状态
    @Published var spO2Data: SpO2DetailsData?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // 血氧范围和时间段信息
    @Published var spO2Range: String = ""
    @Published var timeRange: String = ""
    @Published var latestSpO2Time: String?
    
    // 柱状图数据点
    @Published var chartData: [SpO2ChartPoint] = []
    
    // 测量状态
    @Published var measurementValue: Int = 0
    
    // 服务和取消存储
    private let apiService = APIService.shared.health
    private let deviceService = WindRingDeviceService.shared
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        // 监听血氧测量通知
        setupNotifications()
    }
    
    // 设置通知监听
    private func setupNotifications() {
        // 监听实时血氧值
        NotificationCenter.default.publisher(for: .bloodOxygenMeasured)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notification in
                guard let self = self else { return }
                
                if let value = notification.userInfo?["value"] as? Int {
                    self.measurementValue = value
                }
            }
            .store(in: &cancellables)

        // 监听测量完成通知
        NotificationCenter.default.publisher(for: .spo2DataUploaded)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.completeMeasurement()
            }
            .store(in: &cancellables)
    }
    
    // 加载血氧数据
    func loadSpO2Data(for date: Date) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("加载血氧详情数据，日期字符串格式: \(dateString)")
        
        isLoading = true
        errorMessage = nil
        
        apiService.getSpO2ToVitalDetails(date: dateString)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                
                if case .failure(let error) = completion {
                    self?.errorMessage = error.localizedDescription
                    print("加载血氧详情数据失败: \(error)")
                }
            } receiveValue: { [weak self] response in
//                if response.code == 0 {
                    self?.spO2Data = response//.data
                    self?.processSpO2Data(for: date)
                    print("成功获取血氧详情数据")
//                } else {
//                    self?.errorMessage = response.msg
//                    print("加载血氧详情数据失败: \(response.msg)")
//                }
            }
            .store(in: &cancellables)
    }
    
    // 处理血氧数据，计算范围和准备图表数据
    private func processSpO2Data(for date: Date) {
        guard let data = spO2Data, !data.records.isEmpty else {
            chartData = []
            spO2Range = "N/A"
            timeRange = "N/A"
            latestSpO2Time = ""
            return
        }
        
        // 计算血氧范围
        let spO2Values = data.records.filter { $0.spO2 > 0 }.map { $0.spO2 }
        if let min = spO2Values.min(), let max = spO2Values.max() {
            spO2Range = "\(min)-\(max) %"
        } else {
            spO2Range = "N/A"
        }
        
        // 计算时间范围和最新时间
        let validRecords = data.records.filter { $0.spO2 > 0 }
        if let firstRecord = validRecords.min(by: { $0.time < $1.time }),
           let lastRecord = validRecords.max(by: { $0.time < $1.time }) {
            
            let firstDate = Date(timeIntervalSince1970: TimeInterval(firstRecord.time) / 1000.0)
            let lastDate = Date(timeIntervalSince1970: TimeInterval(lastRecord.time) / 1000.0)
            
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "HH:mm"
            timeRange = "\(displayFormatter.string(from: firstDate))-\(displayFormatter.string(from: lastDate))"

            if let latestRecordTime = data.records.filter({$0.spO2 > 0}).max(by: {$0.time < $1.time})?.time {
                let latestDate = Date(timeIntervalSince1970: TimeInterval(latestRecordTime) / 1000.0)
                self.latestSpO2Time = displayFormatter.string(from: latestDate)
            } else {
                 self.latestSpO2Time = ""
            }

        } else {
            timeRange = "N/A"
            latestSpO2Time = ""
        }
        
        // 准备图表数据
        prepareChartData(for: date)
    }
    
    // 准备血氧柱状图数据
    private func prepareChartData(for date: Date) {
        guard let data = spO2Data else {
            chartData = []
            return
        }
        
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        
        var buckets: [[Int]] = Array(repeating: [], count: 48)
        
        for record in data.records.filter({ $0.spO2 > 0 }) {
            let recordDate = Date(timeIntervalSince1970: TimeInterval(record.time) / 1000.0)
            let hour = calendar.component(.hour, from: recordDate)
            let minute = calendar.component(.minute, from: recordDate)
            let index = hour * 2 + (minute / 30)
            
            if index >= 0 && index < 48 {
                buckets[index].append(record.spO2)
            }
        }
        
        var aggregatedData: [SpO2ChartPoint] = []
        for index in 0..<48 {
            if let bucketDate = calendar.date(byAdding: .minute, value: index * 30, to: startOfDay) {
                let bucket = buckets[index]
                if !bucket.isEmpty {
                    let averageValue = bucket.reduce(0, +) / bucket.count
                    aggregatedData.append(SpO2ChartPoint(time: bucketDate, value: averageValue, hasData: true))
                } else {
                    aggregatedData.append(SpO2ChartPoint(time: bucketDate, value: 0, hasData: false))
                }
            }
        }
        self.chartData = aggregatedData
    }
    
    // 获取最新血氧值
    var latestSpO2: Int {
        return spO2Data?.latestSpO2 ?? 0
    }
    
    // 获取平均血氧值
    var averageSpO2: Int {
        return spO2Data?.avg ?? 0
    }
    
    // 开始血氧测量
    func startMeasurement() {
        // 确保当前是空闲状态
        guard case .idle = measurementState else { return }
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            errorMessage = "请先连接设备"
            return
        }
        
        // 检查佩戴状态
        guard deviceService.wearingState == 1 else {
            errorMessage = "请先佩戴设备"
            return
        }
        
        // 开始测量前清除错误信息
        errorMessage = nil
        
        // 开始测量
        deviceService.startSpO2Measurement()
        measurementState = .measuring
        measurementValue = 0 // 重置测量值
        
        // 设置超时保护（30秒）
        DispatchQueue.main.asyncAfter(deadline: .now() + 30) { [weak self] in
            // 如果30秒后仍在测量，自动取消
            guard let self = self else { return }
            if self.measurementState == .measuring {
                self.cancelMeasurement(reason: "血氧测量超时，请重试")
            }
        }
    }

    // 完成测量
    private func completeMeasurement() {
        guard case .measuring = measurementState else { return }

        if measurementValue > 0 {
            measurementState = .completed(value: measurementValue, time: Date())
        } else {
            // 如果值无效，则视为取消
            cancelMeasurement(reason: "无法获取有效血氧值，请确保设备正确佩戴并重试")
        }

        // 无论如何都停止设备测量
        deviceService.stopSpO2Measurement()
    }

    // 取消或关闭弹窗
    func cancelMeasurement(reason: String? = nil) {
        if case .measuring = measurementState {
            deviceService.stopSpO2Measurement()
        }
        measurementState = .idle
        
        if let reason = reason {
            errorMessage = reason
        }
    }
    
    // 清理资源
    deinit {
        if case .measuring = measurementState {
            deviceService.stopSpO2Measurement()
        }
        cancellables.forEach { $0.cancel() }
    }
}

// MARK: - 血氧图表数据点
struct SpO2ChartPoint: Identifiable {
    let id = UUID()
    let time: Date
    var value: Int
    var hasData: Bool
}
