//
//  WavingBloodView.swift
//  WindRing
//
//  Created by zx on 2025/6/9.
//
import SwiftUI
struct WavingBloodView: View {
    var body: some View {
        TimelineView(.animation) { timeline in
            Canvas { context, size in
                let now = timeline.date.timeIntervalSinceReferenceDate
                let waveHeight: CGFloat = 10
                let waveLength: CGFloat = size.width / 1.5
                
                let liquidLevel = size.height * 0.5
                
                var path = Path()
                path.move(to: CGPoint(x: 0, y: liquidLevel))
                
                for i in 0...Int(size.width) {
                    let x = CGFloat(i)
                    let angle = (x / waveLength) * Double.pi * 2 + (now * 3)
                    let y = sin(angle) * waveHeight + liquidLevel
                    path.addLine(to: CGPoint(x: x, y: y))
                }
                
                path.addLine(to: CGPoint(x: size.width, y: size.height))
                path.addLine(to: CGPoint(x: 0, y: size.height))
                path.closeSubpath()
                
                context.fill(path, with: .color(Color.red.opacity(0.7)))

                // Bubbles
                for i in 0..<5 {
                    let seed = Double(i) / 5.0
                    let bubbleTime = fmod(now * 0.5 + seed * 2, 2)
                    
                    let x = (sin(seed * .pi * 2 * 2.3 + now * 0.2) * 0.4 + 0.5) * size.width
                    let y = size.height - (bubbleTime / 2 * size.height)
                    
                    if y > liquidLevel {
                        let radius = (1 - (bubbleTime / 2)) * 5 + 2
                        let alpha = 1 - (bubbleTime / 2)
                        
                        let bubblePath = Path(ellipseIn: CGRect(x: x - radius, y: y - radius, width: radius * 2, height: radius * 2))
                        context.fill(bubblePath, with: .color(.white.opacity(alpha * 0.8)))
                    }
                }
            }
            .clipShape(Circle())
        }
    }
}
