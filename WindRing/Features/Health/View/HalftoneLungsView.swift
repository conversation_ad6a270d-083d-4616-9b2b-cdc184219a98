//
//  HalftoneLungsView.swift
//  WindRing
//
//  Created by zx on 2025/6/9.
//
import SwiftUI

struct HalftoneLungsView: View {
    var body: some View {
        Canvas { context, size in
            let dotSize: CGFloat = 2.5
            let spacing: CGFloat = 6
            let numCols = Int(size.width / spacing)
            let numRows = Int(size.height / spacing)
            let center = CGPoint(x: size.width / 2, y: size.height / 2)
            let maxDist = size.width * 0.7

            for row in 0..<numRows {
                for col in 0..<numCols {
                    let pos = CGPoint(x: CGFloat(col) * spacing, y: CGFloat(row) * spacing)
                    let distFromCenter = sqrt(pow(pos.x - center.x, 2) + pow(pos.y - center.y, 2))
                    let opacity = max(0, 1.0 - pow(distFromCenter / maxDist, 2))
                    context.fill(
                        Path(ellipseIn: CGRect(origin: pos, size: CGSize(width: dotSize, height: dotSize))),
                        with: .color(.white.opacity(opacity * 0.8))
                    )
                }
            }
        }
        .mask(
            Image(systemName: "lungs.fill")
                .resizable()
                .scaledToFit()
        )
    }
}
