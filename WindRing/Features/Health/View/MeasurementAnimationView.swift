//
//  MeasurementAnimationView.swift
//  WindRing
//
//  Created by zx on 2025/6/9.
//
import SwiftUI
// MARK: - 动画视图
struct MeasurementAnimationView: View {
    @ObservedObject var viewModel: HeartRateViewModel
    
    // New states for the two-stage animation
    @State private var drawingProgress: CGFloat = 0.0
    @State private var scrollingPhase: CGFloat = 0.0
    @State private var hasFinishedDrawing = false

    var body: some View {
        VStack(spacing: 24) {
            // Header
            HStack {
                Text("Heart Rate Measurement")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                Spacer()
                Button(action: {
                    viewModel.cancelMeasurement()
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.gray)
                        .font(.body.weight(.bold))
                }
            }

            // Animation View
            ZStack {
                // Background grid
                HStack(spacing: 6) {
                    ForEach(0..<45) { i in
                        Rectangle()
                            .fill(Color.white.opacity( i % 5 == 0 ? 0.2 : 0.1))
                            .frame(width: 1.5, height: 100)
                    }
                }

                // Animated wave using the new two-stage logic
                HeartbeatWaveShape(
                    drawingProgress: drawingProgress,
                    scrollingPhase: scrollingPhase,
                    hasFinishedDrawing: hasFinishedDrawing
                )
                .stroke(Color.red, style: StrokeStyle(lineWidth: 2, lineCap: .round, lineJoin: .round))
                .frame(height: 100)
                .clipped()
            }
            .frame(height: 120)
            .onAppear {
                // Reset state every time the view appears
                hasFinishedDrawing = false
                drawingProgress = 0.0
                scrollingPhase = 0.0

                // Start Phase 1: Drawing animation
                withAnimation(.linear(duration: 2.0).delay(0.1)) {
                    drawingProgress = 1.0
                }
            }
            .onChange(of: drawingProgress) { newValue in
                // Once drawing is complete, trigger Phase 2: Scrolling animation
                if newValue >= 1.0 && !hasFinishedDrawing {
                    hasFinishedDrawing = true
                    // This kicks off the forever-repeating scrolling animation
                    withAnimation(.linear(duration: 1.5).repeatForever(autoreverses: false)) {
                        scrollingPhase = 1.0
                    }
                }
            }

            Text("Measuring...")
                .font(.system(size: 16))
                .foregroundColor(.gray)

            // "Done" button during measurement can act as a cancel button
            Button(action: {
                viewModel.cancelMeasurement()
            }) {
                Text("Cancel")
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.3))
                    .foregroundColor(.white)
                    .cornerRadius(16)
            }
        }
    }
}
// MARK: - 心电图形状
struct HeartbeatWaveShape: Shape {
    var drawingProgress: CGFloat
    var scrollingPhase: CGFloat
    var hasFinishedDrawing: Bool
    
    private let patternWidth: CGFloat = 150

    var animatableData: AnimatablePair<CGFloat, CGFloat> {
        get { AnimatablePair(drawingProgress, scrollingPhase) }
        set {
            drawingProgress = newValue.first
            scrollingPhase = newValue.second
        }
    }

    func path(in rect: CGRect) -> Path {
        if !hasFinishedDrawing {
            // Phase 1: Draw the wave from left to right
            let fullPath = createWavePath(length: rect.width, in: rect)
            return fullPath.trimmedPath(from: 0, to: drawingProgress)
        } else {
            // Phase 2: Scroll the wave infinitely
            // Create a path that's wider than the view to ensure seamless scrolling
            let longPath = createWavePath(length: rect.width + patternWidth, in: rect)
            // Calculate the offset based on the scrolling phase
            let offset = -scrollingPhase * patternWidth
            let transform = CGAffineTransform(translationX: offset, y: 0)
            return longPath.applying(transform)
        }
    }
    
    private func createWavePath(length: CGFloat, in rect: CGRect) -> Path {
        var path = Path()
        let height = rect.height
        
        let pattern: [CGPoint] = [
            CGPoint(x: 0.0, y: 0.5),   // Baseline
            CGPoint(x: 0.1, y: 0.5),   // P-wave start
            CGPoint(x: 0.15, y: 0.45), // P-wave peak
            CGPoint(x: 0.2, y: 0.5),   // P-wave end
            CGPoint(x: 0.25, y: 0.5),  // PR segment
            CGPoint(x: 0.28, y: 0.52), // Q-wave
            CGPoint(x: 0.35, y: 0.1),  // R-peak
            CGPoint(x: 0.42, y: 0.8),  // S-wave
            CGPoint(x: 0.5, y: 0.5),   // ST segment start
            CGPoint(x: 0.7, y: 0.5),   // T-wave start
            CGPoint(x: 0.8, y: 0.4),   // T-wave peak
            CGPoint(x: 0.9, y: 0.5),   // T-wave end
            CGPoint(x: 1.0, y: 0.5)    // Baseline
        ]
        
        let scaledPattern = pattern.map { CGPoint(x: $0.x * patternWidth, y: $0.y * height) }
        
        path.move(to: CGPoint(x: 0, y: height * 0.5))
        
        let patternCount = Int(ceil(length / patternWidth))
        
        for i in 0...patternCount {
            let patternStartX = CGFloat(i) * patternWidth
            for point in scaledPattern.dropFirst() { // dropFirst to avoid re-adding the previous point
                path.addLine(to: CGPoint(x: patternStartX + point.x, y: point.y))
            }
        }
        
        return path
    }
}
