//
//  SpO2MeasurementAnimationView.swift
//  WindRing
//
//  Created by zx on 2025/6/9.
//
import SwiftUI
struct SpO2MeasurementAnimationView: View {
    @ObservedObject var viewModel: SpO2ViewModel

    var body: some View {
        VStack(spacing: 24) {
            HStack {
                Text("SpO2 Measurement")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                Spacer()
                But<PERSON>(action: {
                    viewModel.cancelMeasurement()
                }) {
                    Image(systemName: "xmark").foregroundColor(.gray).font(.body.weight(.bold))
                }
            }

            WavingBloodView()
                .frame(width: 150, height: 150)
                .overlay(
                    Text(viewModel.measurementValue > 0 ? "\(viewModel.measurementValue)%" : "Measuring...")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                )

            Text("Please keep your finger still.")
                .font(.system(size: 14))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            Button(action: { viewModel.cancelMeasurement() }) {
                Text("Cancel")
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.3))
                    .foregroundColor(.white)
                    .cornerRadius(16)
            }
        }
    }
}
