//
//  SpO2MeasurementResultView.swift
//  WindRing
//
//  Created by zx on 2025/6/9.
//
import SwiftUI
struct SpO2MeasurementResultView: View {
    @ObservedObject var viewModel: SpO2ViewModel
    let value: Int
    let time: Date
    
    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter
    }

    var body: some View {
        VStack(spacing: 24) {
            // Header
            HStack {
                Text("SpO2 Measurement")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                Spacer()
                Button(action: {
                    viewModel.cancelMeasurement()
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.gray)
                        .font(.body.weight(.bold))
                }
            }

            // Result View
            VStack(spacing: 12) {
                HStack(alignment: .lastTextBaseline, spacing: 2) {
                    Text("\(value)")
                        .font(.system(size: 64, weight: .bold))
                    Text("%")
                        .font(.system(size: 24, weight: .medium))
                        .offset(y: -8)
                }
                .foregroundColor(.white)
                
                Text("Latest \(timeFormatter.string(from: time))")
                    .font(.system(size: 16))
                    .foregroundColor(.gray)
            }
            .frame(height: 150, alignment: .center)


            // Done button
            Button(action: {
                viewModel.cancelMeasurement()
            }) {
                Text("Done")
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(16)
            }
        }
    }
}
