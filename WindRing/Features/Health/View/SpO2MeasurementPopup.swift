//
//  SpO2MeasurementPopup.swift
//  WindRing
//
//  Created by zx on 2025/6/9.
//
import SwiftUI
// MARK: - SpO2 Measurement Popup
struct SpO2MeasurementPopup: View {
    @ObservedObject var viewModel: SpO2ViewModel

    var body: some View {
        Group {
            switch viewModel.measurementState {
            case .idle:
                EmptyView()
            case .measuring:
                SpO2MeasurementAnimationView(viewModel: viewModel)
            case .completed(let value, let time):
                SpO2MeasurementResultView(viewModel: viewModel, value: value, time: time)
            }
        }
        .padding(24)
        .background(Color(red: 44/255, green: 49/255, blue: 56/255))
        .cornerRadius(24)
        .shadow(color: .black.opacity(0.4), radius: 20)
        .padding(.horizontal, 30)
    }
}
