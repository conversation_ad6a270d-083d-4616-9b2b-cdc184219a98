import SwiftUI
import Combine

// MARK: - HRV视图模型
class HRVViewModel: ObservableObject {
    // 数据状态
    @Published var hrvData: HRVDetailsData?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // HRV范围和时间段信息
    @Published var hrvRange: String = ""
    @Published var timeRange: String = ""
    
    // 柱状图数据点
    @Published var chartData: [HRVChartPoint] = []
    
    // 测量状态
    @Published var isMeasuring: Bool = false
    @Published var measurementValue: Int = 0
    @Published var latestHRVTime: String = ""

    // Y-axis scale
    @Published var yAxisMax: Double = 90.0
    @Published var yAxisMin: Double = 0.0
    @Published var dataCenter: Double = 60.0

    // For gesture interaction
    @Published var selectedPoint: HRVChartPoint?

    // 服务和取消存储
    private let apiService = APIService.shared.health
    private let deviceService = WindRingDeviceService.shared
    private var cancellables = Set<AnyCancellable>()
    private var selectedDate: Date = Date()
    
    init() {
        // 监听HRV测量通知
        setupNotifications()
    }
    
    // 设置通知监听
    private func setupNotifications() {
        NotificationCenter.default.publisher(for: .hrvMeasured)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notification in
                guard let self = self else { return }
                
                if let value = notification.userInfo?["value"] as? Int {
                    self.measurementValue = value
                    
                    // 如果正在测量，更新UI
                    if self.isMeasuring {
                        // 可以在这里记录测量值，如果需要的话
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    // 加载HRV数据
    func loadHRVData(for date: Date) {
        self.selectedDate = date
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("加载HRV详情数据，日期字符串格式: \(dateString)")
        
        isLoading = true
        errorMessage = nil
        
        apiService.getHRVToVitalDetails(date: dateString)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                
                if case .failure(let error) = completion {
                    self?.errorMessage = error.localizedDescription
                    print("加载HRV详情数据失败: \(error)")
                }
            } receiveValue: { [weak self] response in
//                if response.code == 0 {
                    self?.hrvData = response//.data
                    self?.processHRVData()
                    print("成功获取HRV详情数据")
//                } else {
//                    self?.errorMessage = response.msg
//                    print("加载HRV详情数据失败: \(response.msg)")
//                }
            }
            .store(in: &cancellables)
    }
    
    // 处理HRV数据，计算范围和准备图表数据
    private func processHRVData() {
        guard let data = hrvData, !data.records.isEmpty else {
            chartData = []
            hrvRange = "N/A"
            timeRange = "N/A"
            latestHRVTime = ""
            yAxisMax = 90.0
            yAxisMin = 0.0
            dataCenter = 60.0
            return
        }
        
        // 准备图表数据
        prepareChartData()
        
//        let allMins = chartData.map { Double($0.minHrv) }
//        let allMaxs = chartData.map { Double($0.maxHrv) }

        let allMins = chartData.map { Double($0.minHrv) }.filter { $0 > 0 }
        let allMaxs = chartData.map { Double($0.maxHrv) }.filter { $0 > 0 }

        if let overallMin = allMins.min(), let overallMax = allMaxs.max(), overallMax > overallMin {
            let range = overallMax - overallMin
            let padding = max(range * 0.1, 5) // 最少 padding 为 5，避免视觉“贴边”
            
            self.yAxisMax = overallMax + padding
            self.yAxisMin = max(0, overallMin - padding)
            self.dataCenter = (overallMin + overallMax) / 2.0
        } else if let singleValue = (allMins + allMaxs).first {
            self.yAxisMax = singleValue + 10
            self.yAxisMin = max(0, singleValue - 10)
            self.dataCenter = singleValue
        } else {
            self.yAxisMax = 90.0
            self.yAxisMin = 0.0
            self.dataCenter = 60.0
        }

        // 计算HRV范围
        let hrvValues = data.records.map { $0.hrv }
        if let min = hrvValues.min(), let max = hrvValues.max() {
            hrvRange = "\(min)-\(max) ms"
        } else {
            hrvRange = "N/A"
        }
        
        // 计算时间范围
        if let firstRecord = data.records.first, let lastRecord = data.records.last {
            // 转换毫秒时间戳为Date对象
            let firstDate = Date(timeIntervalSince1970: TimeInterval(firstRecord.time) / 1000.0)
            let lastDate = Date(timeIntervalSince1970: TimeInterval(lastRecord.time) / 1000.0)
            
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "HH:mm"
            timeRange = "\(displayFormatter.string(from: firstDate))-\(displayFormatter.string(from: lastDate))"
            
            // 格式化最新时间
            let timeFormatter = DateFormatter()
            timeFormatter.dateFormat = "HH:mm"
            latestHRVTime = timeFormatter.string(from: lastDate)
            
        } else {
            timeRange = "N/A"
            latestHRVTime = ""
        }
    }
    
    // 准备HRV柱状图数据
    private func prepareChartData() {
        guard let data = hrvData, !data.records.isEmpty else {
            self.chartData = []
            return
        }

        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: self.selectedDate)
        
        // Create 48 buckets for every 30 minutes in a day
        var buckets: [[HRVDetailData]] = Array(repeating: [], count: 48)
        
        for record in data.records {
            let recordDate = Date(timeIntervalSince1970: TimeInterval(record.time) / 1000.0)
            let hour = calendar.component(.hour, from: recordDate)
            let minute = calendar.component(.minute, from: recordDate)
            
            // Ensure minute is within a valid range before calculation
            guard (0...59).contains(minute) else { continue }
            
            let index = hour * 2 + (minute / 30)
            
            if index >= 0 && index < 48 && (record.hrv > 0 || record.minHrv > 0 || record.maxHrv > 0) {
                buckets[index].append(record)
            }
        }
        
        var aggregatedData: [HRVChartPoint] = []
        for (index, bucket) in buckets.enumerated() {
            if !bucket.isEmpty {
                let minHrv = bucket.map { $0.minHrv }.filter{ $0 > 0 }.min() ?? 0
                let maxHrv = bucket.map { $0.maxHrv }.filter{ $0 > 0 }.max() ?? 0
                
                if let bucketDate = calendar.date(byAdding: .minute, value: index * 30, to: startOfDay) {
                    aggregatedData.append(HRVChartPoint(time: bucketDate, minHrv: minHrv, maxHrv: maxHrv))
                }
            }
        }
        
        self.chartData = aggregatedData
    }
    
    // 获取最新HRV值
    var latestHRV: Int {
        return hrvData?.latestHRV ?? 0
    }
    
    // 获取平均HRV值
    var averageHRV: Int {
        return hrvData?.avg ?? 0
    }
    
    // 开始或停止HRV测量
    func toggleHRVMeasurement() {
        if isMeasuring {
            stopMeasurement()
        } else {
            startMeasurement()
        }
    }
    
    // 开始HRV测量
    func startMeasurement() {
        guard !isMeasuring else { return }
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            errorMessage = "请先连接设备"
            return
        }
        
        // 检查佩戴状态
        guard deviceService.wearingState == 1 else {
            errorMessage = "请先佩戴设备"
            return
        }
        
        // 开始测量前清除错误信息
        errorMessage = nil
        
        // 开始测量
        deviceService.startHRVMeasurement()
        isMeasuring = true
        measurementValue = 0 // 重置测量值
        
        // 设置超时保护（30秒）
        DispatchQueue.main.asyncAfter(deadline: .now() + 30) { [weak self] in
            guard let self = self, self.isMeasuring else { return }
            
            // 如果30秒后仍在测量，自动停止
            self.stopMeasurement()
            self.errorMessage = "HRV测量超时，请重试"
        }
    }
    
    // 停止HRV测量
    func stopMeasurement() {
        guard isMeasuring else { return }
        
        deviceService.stopHRVMeasurement()
        isMeasuring = false
        
        // 如果测量值为0，可能是测量失败
        if measurementValue == 0 {
            errorMessage = "无法获取有效HRV值，请确保设备正确佩戴并重试"
        }
    }
    
    // 清理资源
    deinit {
        stopMeasurement()
        cancellables.forEach { $0.cancel() }
    }
}

// MARK: - HRV图表数据点
struct HRVChartPoint: Identifiable, Equatable {
    let id = UUID()
    let time: Date
    let minHrv: Int
    let maxHrv: Int
}

// MARK: - HRV健康状态
enum HRVHealthStatus {
    case excellent // 优秀
    case good      // 良好
    case normal    // 正常
    case fair      // 一般
    case poor      // 较差
    case unknown   // 未知
    
    // 根据HRV值判断健康状态
    static func statusFor(hrv: Int, age: Int = 35) -> HRVHealthStatus {
        // 不同年龄段有不同的参考值，这里使用简化的判断逻辑
        // 实际应用中应该基于年龄、性别等更多因素
        if hrv == 0 {
            return .unknown
        } else if hrv > 100 {
            return .excellent
        } else if hrv > 80 {
            return .good
        } else if hrv > 60 {
            return .normal
        } else if hrv > 40 {
            return .fair
        } else {
            return .poor
        }
    }
    
    // 返回对应的颜色
    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return Color(red: 0.0, green: 0.7, blue: 0.3)
        case .normal: return .blue
        case .fair: return .orange
        case .poor: return .red
        case .unknown: return .gray
        }
    }
    
    // 返回状态描述
    var description: String {
        switch self {
        case .excellent: return "优秀"
        case .good: return "良好"
        case .normal: return "正常"
        case .fair: return "一般"
        case .poor: return "较差"
        case .unknown: return "未知"
        }
    }
    
    // 返回健康建议
    var advice: String {
        switch self {
        case .excellent:
            return "心脏健康状况优秀，压力管理良好。继续保持良好的生活习惯。"
        case .good:
            return "心脏健康状况良好，能够有效应对压力。保持规律运动和充足睡眠。"
        case .normal:
            return "心脏健康状况正常，适当增加有氧运动可以进一步提高HRV。"
        case .fair:
            return "心脏健康状况一般，注意压力管理，增加休息和放松时间。"
        case .poor:
            return "心脏健康状况较差，建议减少压力，增加放松活动，如有不适请咨询医生。"
        case .unknown:
            return "数据不足，无法提供健康建议。"
        }
    }
}

extension HRVViewModel {
    // 获取当前健康状态
    var healthStatus: HRVHealthStatus {
        return HRVHealthStatus.statusFor(hrv: latestHRV)
    }
    
    // 获取健康状态颜色
    var healthStatusColor: Color {
        return healthStatus.color
    }
    
    // 获取健康状态描述
    var healthStatusDescription: String {
        return healthStatus.description
    }
    
    // 获取健康建议
    var healthAdvice: String {
        return healthStatus.advice
    }
}

// MARK: - HRV数据模型
struct HRVDetailsData: Codable {
    var latestHRV: Int       // 最新HRV值
    var avg: Int             // 平均HRV值
    var records: [HRVDetailData]
    
    enum CodingKeys: String, CodingKey {
        case latestHRV = "latestHrv"
        case avg
        case records
    }
}

struct HRVDetailData: Codable {
    let hrv: Int
    let time: Int64
    let minHrv: Int
    let maxHrv: Int
}

// MARK: - HRV详情响应
struct HRVDetailsResponse: Codable {
    var code: Int
    var msg: String
    var data: HRVDetailsData?
} 
