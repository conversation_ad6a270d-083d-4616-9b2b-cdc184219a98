import SwiftUI
import Combine
import CRPSmartRing

/// 血压API测试视图模型
class BloodPressureAPITestViewModel: NSObject, ObservableObject {
    // MARK: - 属性
    @Published var results: [BPTestResult] = []
    @Published var isLoading: Bool = false
    @Published var systolicBP: Int = 0  // 收缩压
    @Published var diastolicBP: Int = 0  // 舒张压
    @Published var isMeasuringBP: Bool = false
    @Published var showShareSheet: Bool = false
    @Published var shareText: String = ""
    @Published var receivedNotification: Bool = false
    
    // 设备服务
    let deviceService = WindRingDeviceService.shared
    
    // 通知中心
    private var cancellables = Set<AnyCancellable>()
    
    // 单例
    static let shared = BloodPressureAPITestViewModel()
    
    // MARK: - 初始化方法
    override init() {
        super.init()
        // 设置代理和通知监听
        setupNotifications()
    }
    
    // 设置通知监听
    private func setupNotifications() {
        // 监听血压测量通知 (如果有的话)
        NotificationCenter.default.publisher(for: .bloodPressureMeasured)
            .sink { [weak self] notification in
                print("【调试】收到血压通知: \(notification)")
                
                if let systolic = notification.userInfo?["systolic"] as? Int,
                   let diastolic = notification.userInfo?["diastolic"] as? Int {
                    print("【调试】从userInfo中获取血压值: 收缩压\(systolic)，舒张压\(diastolic)")
                    DispatchQueue.main.async {
                        self?.handleBPUpdate(sbp: systolic, dbp: diastolic)
                    }
                } else {
                    print("【调试】收到血压通知但无法获取血压值: userInfo=\(String(describing: notification.userInfo)), object=\(String(describing: notification.object))")
                }
            }
            .store(in: &cancellables)
            
        print("【调试】已设置血压通知监听")
    }
    
    // 处理血压更新
    private func handleBPUpdate(sbp: Int, dbp: Int) {
        self.isLoading = false
        self.isMeasuringBP = false
        self.systolicBP = sbp
        self.diastolicBP = dbp
        self.receivedNotification = true
        
        if sbp == 0 || sbp == 255 || dbp == 0 || dbp == 255 {
            addResult("血压测量已中断", color: .orange)
        } else {
            addResult("接收到血压测量结果: 收缩压 \(sbp) mmHg, 舒张压 \(dbp) mmHg", color: .green)
        }
    }
    
    // MARK: - 结果管理方法
    
    /// 添加测试结果
    func addResult(_ message: String, color: Color = .primary) {
        let result = BPTestResult(message: message, color: color)
        DispatchQueue.main.async {
            self.results.insert(result, at: 0)
        }
    }
    
    /// 清除结果
    func clearResults() {
        DispatchQueue.main.async {
            self.results.removeAll()
        }
    }
    
    /// 导出测试结果
    func exportResults() {
        var text = "血压API测试结果\n"
        text += "测试时间: \(Date())\n"
        text += "设备连接状态: \(deviceService.connectionState.description)\n\n"
        text += "测试记录:\n"
        
        for (index, result) in results.enumerated() {
            text += "\(index + 1). \(result.message)\n"
        }
        
        shareText = text
        showShareSheet = true
    }
    
    // MARK: - 血压测量方法
    
    /// 开始单次血压测量
    func startSingleBPMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法进行血压测量", color: .red)
            return
        }
        
        clearResults()
        addResult("开始单次血压测量...", color: .blue)
        isLoading = true
        isMeasuringBP = true
        receivedNotification = false
        
        print("【调试】发送开始血压测量请求")
        CRPSmartRingSDK.sharedInstance.setStartBP()
        
        // 设置超时检查，因为血压测量可能耗时较长
        DispatchQueue.main.asyncAfter(deadline: .now() + 60) { [weak self] in
            if self?.isLoading == true && self?.receivedNotification == false {
                self?.isLoading = false
                self?.isMeasuringBP = false
                self?.addResult("血压测量超时，请重试", color: .red)
            }
        }
    }
    
    /// 停止单次血压测量
    func stopSingleBPMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法停止血压测量", color: .red)
            return
        }
        
        addResult("停止血压测量...", color: .blue)
        CRPSmartRingSDK.sharedInstance.setStopBP()
        isMeasuringBP = false
        isLoading = false
    }
    
    /// 测试通知处理 - 模拟发送一个血压通知
    func testBPNotification() {
        let sbp = 120 // 模拟正常收缩压值
        let dbp = 80  // 模拟正常舒张压值
        
        // 构建通知
        let userInfo = ["systolic": sbp, "diastolic": dbp]
        NotificationCenter.default.post(
            name: .bloodPressureMeasured,
            object: nil,
            userInfo: userInfo
        )
        
        addResult("已发送测试血压通知: 收缩压\(sbp), 舒张压\(dbp)", color: .blue)
    }
    
    /// 解释血压值
    func interpretBP(systolic: Int, diastolic: Int) -> (String, Color) {
        if systolic == 0 || diastolic == 0 {
            return ("无数据", .gray)
        }
        
        // 正常: 收缩压 < 120 且 舒张压 < 80
        if systolic < 120 && diastolic < 80 {
            return ("正常", .green)
        }
        // 偏高: 收缩压 120-129 且 舒张压 < 80
        else if (systolic >= 120 && systolic <= 129) && diastolic < 80 {
            return ("偏高", .yellow)
        }
        // 高血压I期: 收缩压 130-139 或 舒张压 80-89
        else if (systolic >= 130 && systolic <= 139) || (diastolic >= 80 && diastolic <= 89) {
            return ("高血压I期", .orange)
        }
        // 高血压II期: 收缩压 ≥ 140 或 舒张压 ≥ 90
        else if systolic >= 140 || diastolic >= 90 {
            return ("高血压II期", .red)
        }
        // 高血压危象: 收缩压 > 180 或 舒张压 > 120
        else if systolic > 180 || diastolic > 120 {
            return ("高血压危象", .red)
        }
        
        return ("未知", .gray)
    }
}

// MARK: - 测试结果数据模型
struct BPTestResult: Identifiable {
    let id = UUID()
    let message: String
    let color: Color
    let timestamp = Date()
}

// MARK: - 血压API测试视图
struct BloodPressureAPITestView: View {
    @StateObject private var viewModel = BloodPressureAPITestViewModel.shared
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 血压显示区域
                VStack(spacing: 16) {
                    Text("血压测量")
                        .font(.headline)
                    
                    HStack(spacing: 15) {
                        // 收缩压
                        VStack {
                            Text("收缩压")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                            
                            Text("\(viewModel.systolicBP)")
                                .font(.system(size: 36, weight: .bold))
                                .foregroundColor(.red)
                            
                            Text("mmHg")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                        .frame(minWidth: 0, maxWidth: .infinity)
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                        
                        // 舒张压
                        VStack {
                            Text("舒张压")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                            
                            Text("\(viewModel.diastolicBP)")
                                .font(.system(size: 36, weight: .bold))
                                .foregroundColor(.blue)
                            
                            Text("mmHg")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                        .frame(minWidth: 0, maxWidth: .infinity)
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }
                    
                    // 血压状态解释
                    if viewModel.systolicBP > 0 && viewModel.diastolicBP > 0 {
                        let (interpretText, interpretColor) = viewModel.interpretBP(
                            systolic: viewModel.systolicBP, 
                            diastolic: viewModel.diastolicBP
                        )
                        
                        Text("血压状态: \(interpretText)")
                            .font(.headline)
                            .foregroundColor(interpretColor)
                            .padding(.vertical, 4)
                    }
                    
                    // 控制按钮
                    HStack(spacing: 16) {
                        Button(action: {
                            viewModel.startSingleBPMeasurement()
                        }) {
                            Text("开始测量")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                        .disabled(viewModel.isMeasuringBP || viewModel.isLoading)
                        
                        Button(action: {
                            viewModel.stopSingleBPMeasurement()
                        }) {
                            Text("停止测量")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(viewModel.isMeasuringBP ? Color.red : Color.gray)
                                .cornerRadius(10)
                        }
                        .disabled(!viewModel.isMeasuringBP)
                    }
                    
                    // 测试通知按钮（用于调试）
                    Button(action: {
                        viewModel.testBPNotification()
                    }) {
                        HStack {
                            Image(systemName: "bell.fill")
                            Text("测试通知接收")
                        }
                        .font(.footnote)
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.purple)
                        .cornerRadius(10)
                    }
                }
                .padding()
                .background(Color(.systemGray5).opacity(0.5))
                .cornerRadius(15)
                
                // 测试结果区域
                VStack(alignment: .leading) {
                    HStack {
                        Text("测试结果")
                            .font(.headline)
                        
                        Spacer()
                        
                        Button(action: {
                            viewModel.clearResults()
                        }) {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                        }
                        
                        Button(action: {
                            viewModel.exportResults()
                        }) {
                            Image(systemName: "square.and.arrow.up")
                                .foregroundColor(.blue)
                        }
                    }
                    
                    Divider()
                    
                    if viewModel.results.isEmpty {
                        Text("暂无测试结果")
                            .foregroundColor(.gray)
                            .padding()
                            .frame(maxWidth: .infinity)
                    } else {
                        ForEach(viewModel.results) { result in
                            VStack(alignment: .leading, spacing: 4) {
                                Text(result.message)
                                    .foregroundColor(result.color)
                                
                                Text(result.timestamp, style: .time)
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                            .padding(.vertical, 4)
                            
                            Divider()
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray5).opacity(0.5))
                .cornerRadius(15)
                
                // 说明区域
                VStack(alignment: .leading, spacing: 8) {
                    Text("血压测量说明")
                        .font(.headline)
                    
                    Divider()
                    
                    Text("• 血压测量需要保持戒指佩戴稳定")
                        .font(.caption)
                    
                    Text("• 测量过程中请保持手臂放松自然")
                        .font(.caption)
                    
                    Text("• 连续测量需要间隔5分钟以上")
                        .font(.caption)
                    
                    Text("• 血压数值仅供参考，如有异常请咨询医生")
                        .font(.caption)
                        .foregroundColor(.red)
                }
                .padding()
                .background(Color(.systemGray5).opacity(0.5))
                .cornerRadius(15)
            }
            .padding()
        }
        .navigationTitle("血压API测试")
        .overlay(
            ZStack {
                if viewModel.isLoading {
                    Color.black.opacity(0.3)
                        .edgesIgnoringSafeArea(.all)
                    
                    VStack {
                        ProgressView()
                            .scaleEffect(1.5)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        
                        Text(viewModel.isMeasuringBP ? "血压测量中..." : "加载中...")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.top, 10)
                    }
                    .padding(20)
                    .background(Color.black.opacity(0.7))
                    .cornerRadius(10)
                }
            }
        )
        .sheet(isPresented: $viewModel.showShareSheet) {
            BloodPressureActivityView(text: viewModel.shareText)
        }
    }
}

// MARK: - 活动视图（用于分享）
struct BloodPressureActivityView: UIViewControllerRepresentable {
    let text: String
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: [text],
            applicationActivities: nil
        )
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - 预览
struct BloodPressureAPITestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            BloodPressureAPITestView()
        }
    }
} 