import SwiftUI
import Combine
import CRPSmartRing

/// 心率变异性(HRV)API测试视图模型
class HRVAPITestViewModel: NSObject, ObservableObject {
    // MARK: - 属性
    @Published var results: [HRVTestResult] = []
    @Published var isLoading: Bool = false
    @Published var hrv: Int = 0
    @Published var isMeasuringHRV: Bool = false
    @Published var selectedDay: Int = 0
    @Published var showShareSheet: Bool = false
    @Published var shareText: String = ""
    @Published var hrvInterval: Int = 0
    
    // 设备服务
    let deviceService = WindRingDeviceService.shared
    
    // 通知中心
    private var cancellables = Set<AnyCancellable>()
    
    // 单例
    static let shared = HRVAPITestViewModel()
    
    // MARK: - 初始化方法
    override init() {
        super.init()
        // 监听HRV变化的通知
        setupNotifications()
    }
    
    // 设置通知监听
    private func setupNotifications() {
        // 监听HRV测量通知
        NotificationCenter.default.publisher(for: .hrvMeasured)
            .sink { [weak self] notification in
                print("【调试】收到HRV通知: \(notification)")
                
                if let hrv = notification.userInfo?["value"] as? Int {
                    print("【调试】从userInfo中获取HRV值: \(hrv)")
                    DispatchQueue.main.async {
                        self?.handleHRVUpdate(hrv)
                    }
                } else if let hrv = notification.object as? Int {
                    print("【调试】从object中获取HRV值: \(hrv)")
                    DispatchQueue.main.async {
                        self?.handleHRVUpdate(hrv)
                    }
                } else {
                    print("【调试】收到HRV通知但无法获取HRV值: userInfo=\(String(describing: notification.userInfo)), object=\(String(describing: notification.object))")
                }
            }
            .store(in: &cancellables)
            
        print("【调试】已设置HRV通知监听")
    }
    
    // 处理HRV更新
    private func handleHRVUpdate(_ hrv: Int) {
        self.isLoading = false
        self.isMeasuringHRV = false
        self.hrv = hrv
        
        if hrv == 0 || hrv == 255 {
            addResult("HRV测量已中断", color: .orange)
        } else {
            addResult("接收到HRV测量结果: \(hrv)", color: .green)
            addResult("HRV评估: \(interpretHRV(hrv: hrv))", color: .blue)
        }
    }
    
    // MARK: - 结果管理方法
    
    /// 添加测试结果
    func addResult(_ message: String, color: Color = .primary) {
        let result = HRVTestResult(message: message, color: color)
        DispatchQueue.main.async {
            self.results.append(result)
        }
    }
    
    /// 清除结果
    func clearResults() {
        DispatchQueue.main.async {
            self.results.removeAll()
        }
    }
    
    /// 导出测试结果
    func exportResults() {
        var text = "心率变异性(HRV)API测试结果\n"
        text += "测试时间: \(Date())\n"
        text += "设备连接状态: \(deviceService.connectionState.description)\n\n"
        text += "测试记录:\n"
        
        for (index, result) in results.enumerated() {
            text += "\(index + 1). \(result.message)\n"
        }
        
        shareText = text
        showShareSheet = true
    }
    
    // MARK: - HRV测量方法
    
    /// 开始单次HRV测量
    func startSingleHRVMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法进行HRV测量", color: .red)
            return
        }
        
        clearResults()
        addResult("开始单次HRV测量...", color: .blue)
        addResult("请保持手指静止，测量大约需要30秒", color: .orange)
        isLoading = true
        isMeasuringHRV = true
        
        CRPSmartRingSDK.sharedInstance.setStartHRV()
    }
    
    /// 停止单次HRV测量
    func stopSingleHRVMeasurement() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法停止HRV测量", color: .red)
            return
        }
        
        addResult("停止HRV测量...", color: .blue)
        CRPSmartRingSDK.sharedInstance.setStopHRV()
        isMeasuringHRV = false
        isLoading = false
    }
    
    /// 获取HRV测量历史
    func getHRVHistory() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取HRV历史", color: .red)
            return
        }
        
        clearResults()
        addResult("获取HRV测量历史...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getHRVRecord { [weak self] records, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    self?.addResult("成功获取HRV历史记录", color: .green)
                    self?.addResult("共获取到 \(records.count) 条记录", color: .blue)
                    
                    if records.isEmpty {
                        self?.addResult("没有HRV历史记录", color: .orange)
                    } else {
                        // 最多显示10条记录，防止界面过长
                        let displayRecords = records.prefix(10)
                        for (index, record) in displayRecords.enumerated() {
                            let date = Date(timeIntervalSince1970: TimeInterval(record.time))
                            let dateFormatter = DateFormatter()
                            dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                            
                            self?.addResult("记录\(index + 1): HRV值 \(record.hrv), 时间: \(dateFormatter.string(from: date))")
                        }
                        
                        if records.count > 10 {
                            self?.addResult("... 更多记录未显示 ...", color: .gray)
                        }
                    }
                } else {
                    self?.addResult("获取HRV历史记录失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 获取定时HRV测量状态
    func getTimingHRVStatus() {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取定时HRV测量状态", color: .red)
            return
        }
        
        clearResults()
        addResult("获取定时HRV测量状态...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getTimingHRVInterval { [weak self] interval, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    self?.hrvInterval = interval
                    if interval == 0 {
                        self?.addResult("定时HRV测量已关闭", color: .orange)
                    } else {
                        self?.addResult("定时HRV测量已开启", color: .green)
                        self?.addResult("测量间隔: 每 \(interval * 5) 分钟", color: .blue)
                    }
                } else {
                    self?.addResult("获取定时HRV测量状态失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 设置定时HRV测量
    func setTimingHRV(interval: Int) {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法设置定时HRV测量", color: .red)
            return
        }
        
        clearResults()
        
        if interval == 0 {
            addResult("正在关闭定时HRV测量...", color: .blue)
        } else {
            addResult("正在设置定时HRV测量: 每 \(interval * 5) 分钟", color: .blue)
        }
        
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.setTimingHRV(interval)
        
        // 延迟一小段时间再获取状态，确保设置已生效
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.getTimingHRVStatus()
        }
    }
    
    /// 获取指定日期的定时HRV数据
    func getTimingHRVData(day: Int) {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取定时HRV数据", color: .red)
            return
        }
        
        clearResults()
        addResult("获取第\(day)天的定时HRV数据...", color: .blue)
        isLoading = true
        
        CRPSmartRingSDK.sharedInstance.getTimingHRV(day) { [weak self] model, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    self?.addResult("成功获取定时HRV数据", color: .green)
                    
                    // 检查是否有HRV数据
                    let hrvs = model.hrvs
                    if hrvs.isEmpty {
                        self?.addResult("没有定时HRV数据记录", color: .orange)
                        return
                    }
                    
                    // 统计基本信息
                    let validHRVs = hrvs.filter { $0 > 0 && $0 < 255 }
                    let count = validHRVs.count
                    
                    if count == 0 {
                        self?.addResult("没有有效的HRV数据", color: .orange)
                        return
                    }
                    
                    let minHRV = validHRVs.min() ?? 0
                    let maxHRV = validHRVs.max() ?? 0
                    let avgHRV = validHRVs.reduce(0, +) / count
                    
                    self?.addResult("有效HRV数据点: \(count)/288", color: .blue)
                    self?.addResult("最低HRV: \(minHRV)", color: .blue)
                    self?.addResult("最高HRV: \(maxHRV)", color: .blue)
                    self?.addResult("平均HRV: \(avgHRV)", color: .blue)
                    
                    // 显示部分数据点
                    self?.addResult("HRV数据示例 (每隔1小时取一个点):", color: .gray)
                    for i in stride(from: 0, to: hrvs.count, by: 12) {
                        if i < hrvs.count {
                            let time = String(format: "%02d:%02d", (i / 12) % 24, (i % 12) * 5)
                            let value = hrvs[i]
                            if value > 0 && value < 255 {
                                self?.addResult("\(time): \(value)")
                            } else {
                                self?.addResult("\(time): 无数据", color: .gray)
                            }
                        }
                    }
                    
                    // 添加HRV评估标准
                    self?.addResult("\n心率变异性(HRV)评估标准:", color: .blue)
                    self?.addResult("优秀: > 70", color: .green)
                    self?.addResult("良好: 50-70", color: .green)
                    self?.addResult("一般: 30-50", color: .orange)
                    self?.addResult("较差: < 30", color: .red)
                    self?.addResult("注: HRV值受多种因素影响，包括年龄、健康状况、运动水平等", color: .gray)
                } else {
                    self?.addResult("获取定时HRV数据失败: \(error)", color: .red)
                }
            }
        }
    }
    
    /// 解释HRV值
    func interpretHRV(hrv: Int) -> String {
        if hrv > 70 {
            return "优秀 - 表明恢复良好，压力水平低"
        } else if hrv >= 50 {
            return "良好 - 恢复正常，压力水平适中"
        } else if hrv >= 30 {
            return "一般 - 可能存在疲劳，压力水平较高"
        } else {
            return "较差 - 恢复不足，压力水平高"
        }
    }
}

/// 测试结果模型
struct HRVTestResult: Identifiable {
    let id = UUID()
    let message: String
    let color: Color
    let timestamp = Date()
}

/// 心率变异性API测试视图
struct HRVAPITestView: View {
    // MARK: - 属性
    @StateObject private var viewModel = HRVAPITestViewModel.shared
    @Environment(\.colorScheme) private var colorScheme
    
    // MARK: - 主视图
    var body: some View {
        List {
            // 当前HRV显示
            hrvSection
            
            // 单次HRV测量
            singleHRVSection
            
            // 定时HRV测量
            timingHRVSection
            
            // 历史数据
            historicalDataSection
            
            // HRV说明
            hrvExplanationSection
            
            // 测试结果
            resultSection
        }
        .navigationTitle("HRV API测试")
        .navigationBarItems(trailing: exportButton)
        .sheet(isPresented: $viewModel.showShareSheet) {
            HRVActivityViewController(activityItems: [viewModel.shareText])
        }
    }
    
    // MARK: - 视图组件
    
    /// HRV显示部分
    private var hrvSection: some View {
        Section(header: Text("当前HRV")) {
            HStack {
                Spacer()
                VStack {
                    Image(systemName: "waveform.path.ecg")
                        .font(.system(size: 50))
                        .foregroundColor(.purple)
                        .padding(.bottom, 5)
                    
                    Text("\(viewModel.hrv)")
                        .font(.system(size: 48, weight: .bold))
                    
                    Text("HRV")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        
                    Text("通知状态: \(viewModel.hrv > 0 ? "已接收" : "未接收")")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                Spacer()
            }
            .padding(.vertical, 20)
            
            // 调试按钮
            Button(action: {
                print("【调试】HRV通知中心测试开始")
                // 使用与WindRingDeviceService一致的方式发送通知
                NotificationCenter.default.post(
                    name: .hrvMeasured,
                    object: nil,
                    userInfo: ["value": 65]
                )
                print("【调试】已发送测试HRV通知，值为65")
            }) {
                Text("测试通知 (设置HRV为65)")
                    .font(.footnote)
                    .foregroundColor(.gray)
            }
        }
    }
    
    /// 单次HRV测量部分
    private var singleHRVSection: some View {
        Section(header: Text("单次HRV测量")) {
            if viewModel.isMeasuringHRV {
                Button(action: {
                    viewModel.stopSingleHRVMeasurement()
                }) {
                    HStack {
                        Spacer()
                        if viewModel.isLoading {
                            ProgressView()
                                .padding(.trailing, 10)
                        }
                        Text("停止测量")
                            .fontWeight(.semibold)
                            .foregroundColor(.red)
                        Spacer()
                    }
                }
                .padding(.vertical, 8)
            } else {
                Button(action: {
                    viewModel.startSingleHRVMeasurement()
                }) {
                    HStack {
                        Spacer()
                        Text("开始测量")
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                        Spacer()
                    }
                }
                .padding(.vertical, 8)
            }
            
            Button(action: {
                viewModel.getHRVHistory()
            }) {
                HStack {
                    Spacer()
                    Text("获取HRV历史记录")
                        .fontWeight(.semibold)
                    Spacer()
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    /// 定时HRV测量部分
    private var timingHRVSection: some View {
        Section(header: Text("定时HRV测量")) {
            Button(action: {
                viewModel.getTimingHRVStatus()
            }) {
                HStack {
                    Spacer()
                    Text("获取定时HRV测量状态")
                        .fontWeight(.semibold)
                    Spacer()
                }
            }
            .padding(.vertical, 8)
            
            HStack {
                Text("间隔设置:")
                Picker("", selection: $viewModel.hrvInterval) {
                    Text("关闭").tag(0)
                    Text("5分钟").tag(1)
                    Text("10分钟").tag(2)
                    Text("15分钟").tag(3)
                    Text("30分钟").tag(6)
                    Text("1小时").tag(12)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            Button(action: {
                viewModel.setTimingHRV(interval: viewModel.hrvInterval)
            }) {
                HStack {
                    Spacer()
                    Text("保存定时测量设置")
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    Spacer()
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    /// 历史数据部分
    private var historicalDataSection: some View {
        Section(header: Text("历史定时数据")) {
            HStack {
                Text("选择日期:")
                Picker("", selection: $viewModel.selectedDay) {
                    Text("今天").tag(0)
                    Text("昨天").tag(1)
                    Text("前天").tag(2)
                    Text("大前天").tag(3)
                    Text("4天前").tag(4)
                    Text("5天前").tag(5)
                    Text("6天前").tag(6)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            Button(action: {
                viewModel.getTimingHRVData(day: viewModel.selectedDay)
            }) {
                HStack {
                    Spacer()
                    Text("获取定时HRV数据")
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    Spacer()
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    /// HRV说明部分
    private var hrvExplanationSection: some View {
        Section(header: Text("什么是HRV?")) {
            Text("心率变异性(HRV)是相邻心跳之间时间间隔的变化，反映了自主神经系统的调节能力。高HRV值通常表示身体恢复良好，压力水平低，而低HRV值可能表示疲劳、压力过大或健康问题。")
                .font(.footnote)
                .foregroundColor(.secondary)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("HRV评估标准:")
                    .font(.footnote)
                    .fontWeight(.bold)
                
                HStack {
                    Text("优秀: > 70")
                        .foregroundColor(.green)
                    Spacer()
                    Text("良好: 50-70")
                        .foregroundColor(.green)
                }
                .font(.footnote)
                
                HStack {
                    Text("一般: 30-50")
                        .foregroundColor(.orange)
                    Spacer()
                    Text("较差: < 30")
                        .foregroundColor(.red)
                }
                .font(.footnote)
            }
            .padding(.top, 4)
        }
    }
    
    /// 测试结果部分
    private var resultSection: some View {
        Section(header: Text("测试结果")) {
            if viewModel.isLoading {
                HStack {
                    Spacer()
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                    Text("加载中...")
                        .padding(.leading, 10)
                    Spacer()
                }
                .padding(.vertical, 10)
            }
            
            if viewModel.results.isEmpty {
                Text("尚未执行任何测试")
                    .foregroundColor(.gray)
                    .italic()
                    .padding(.vertical, 8)
            } else {
                ForEach(viewModel.results) { result in
                    Text(result.message)
                        .foregroundColor(result.color)
                        .padding(.vertical, 2)
                }
                
                Button(action: {
                    viewModel.clearResults()
                }) {
                    HStack {
                        Spacer()
                        Text("清除结果")
                            .foregroundColor(.red)
                        Spacer()
                    }
                }
                .padding(.top, 8)
            }
        }
    }
    
    /// 导出按钮
    private var exportButton: some View {
        Button(action: {
            viewModel.exportResults()
        }) {
            Image(systemName: "square.and.arrow.up")
        }
        .disabled(viewModel.results.isEmpty)
    }
}

// MARK: - ActivityViewController
struct HRVActivityViewController: UIViewControllerRepresentable {
    var activityItems: [Any]
    var applicationActivities: [UIActivity]? = nil
    
    func makeUIViewController(context: UIViewControllerRepresentableContext<HRVActivityViewController>) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: applicationActivities)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: UIViewControllerRepresentableContext<HRVActivityViewController>) {}
}

// MARK: - 预览
struct HRVAPITestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            HRVAPITestView()
        }
    }
} 