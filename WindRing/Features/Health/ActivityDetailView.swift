import SwiftUI
#if os(iOS)
//import CRPSmartRing
import UIKit // 添加UIKit框架导入，用于提供震动反馈
#endif
import Combine
import HealthKit

// 导入项目中的公共组件，这里我们不需要特别的import语句，因为同一个模块内的组件自动可用
// 由于CalendarPickerView已放在项目的Components目录下，Swift可以自动访问它

// 创建一个协议，去掉AnyObject约束，使其可以被struct实现
protocol ActivityDetailViewDelegate {
    var stepCount: Int { get set }
    var isRealStepCountData: Bool { get set }
    var calories: Double { get set }
    var isRealCaloriesData: Bool { get set }
    var distance: Double { get set }
    var isRealDistanceData: Bool { get set }
    var formattedDistanceString: String { get set } // 添加格式化后的距离字符串属性
    var untilTime: String { get set } // 添加时间戳属性
    
    func updateActivityDataBasedOnSteps(_ steps: Int)
    func updateActivityDataBasedOnCalories(_ calories: Int)
}

// 创建一个类包装器来保存对ActivityDetailView的引用
class ActivityDetailViewWrapper: NSObject {
    var delegate: ActivityDetailViewDelegate
    
    init(delegate: ActivityDetailViewDelegate) {
        self.delegate = delegate
        super.init()
    }
}

// 添加一个辅助类来处理Objective-C交互
class ActivityDataManager: NSObject {
    weak var wrapper: ActivityDetailViewWrapper?
    
    init(wrapper: ActivityDetailViewWrapper) {
        self.wrapper = wrapper
        super.init()
    }
    
    // 实时步数监听相关
    var isListeningRealTimeSteps: Bool = false
    var realTimeStepsUpdateTimer: Timer?
    
    // 开始监听实时步数数据
    func startListeningRealTimeStepsData() {
        // 如果已经在监听，则不需要重复启动
        if isListeningRealTimeSteps {
            return
        }
        
        // 设置标志
        isListeningRealTimeSteps = true
        
        // 监听步数更新通知
//        NotificationCenter.default.addObserver(
//            self,
//            selector: #selector(handleStepsUpdate(_:)),
//            name: .stepsUpdated,
//            object: nil
//        )
        
        // 启动定时器，每3秒获取一次步数
//        startStepsUpdateTimer()
        
        print("活动详情页开始监听戒指实时步数数据")
    }
    
    // 停止监听实时步数数据
    func stopListeningRealTimeStepsData() {
        // 如果没有在监听，则不需要停止
        if !isListeningRealTimeSteps {
            return
        }
        
        // 重置标志
        isListeningRealTimeSteps = false
        
        // 移除观察者
        NotificationCenter.default.removeObserver(self, name: .stepsUpdated, object: nil)
        
        // 停止定时器
//        stopStepsUpdateTimer()
        
        print("活动详情页停止监听戒指实时步数数据")
    }
    
    // 启动步数更新定时器
//    func startStepsUpdateTimer() {
//        // 先停止可能存在的定时器
//        stopStepsUpdateTimer()
//        
//        // 创建新定时器，每3秒执行一次
//        realTimeStepsUpdateTimer = Timer.scheduledTimer(
//            timeInterval: 30.0,
//            target: self,
//            selector: #selector(fetchRealTimeSteps),
//            userInfo: nil,
//            repeats: true
//        )
//        
//        // 保证定时器在UI滚动时也能运行
//        RunLoop.current.add(realTimeStepsUpdateTimer!, forMode: .common)
//        
//        // 立即执行一次获取步数
//        fetchRealTimeSteps()
//        
//        print("活动详情页步数更新定时器已启动，间隔：3秒")
//    }
    
    // 停止定时器
//    func stopStepsUpdateTimer() {
//        realTimeStepsUpdateTimer?.invalidate()
//        realTimeStepsUpdateTimer = nil
//    }
    
    // 获取实时步数
//    @objc func fetchRealTimeSteps() {
//        // 检查设备是否已连接
//        if WindRingDeviceService.shared.connectionState == .connected {
//            // 使用getTrainingData获取今天(day=0)的步数数据
//            CRPSmartRingSDK.sharedInstance.getTrainingData(0) { [weak self] record, error in
//                guard let self = self, let wrapper = self.wrapper else { return }
//                
//                if error != .none {
//                    print("获取步数失败: \(error)")
//                    return
//                }
//                
//                // 在主线程更新步数
//                DispatchQueue.main.async {
//                    // 更新实时步数
//                    let realTimeSteps = record.step
//                    let realTimeCalories = record.cal // 获取实时卡路里
//                    let realTimeDistance = Double(record.distance) / 100000.0 // 获取实时距离(厘米转公里)
//                    
//                    // 只处理有意义的步数数据
//                    if realTimeSteps > 0 {
//                        print("===活动详情页实时步数数据===")
//                        print("获取到设备实时步数: \(realTimeSteps) 步")
//                        print("当前UI显示步数值: \(wrapper.delegate.stepCount)")
//                        print("===========================")
//                    
//                        // 数据有更新时，更新时间戳
//                        var dataChanged = false
//                        
//                        // 只有当步数真正发生变化时才更新
//                        let oldSteps = wrapper.delegate.stepCount
//                        if oldSteps != realTimeSteps {
//                            // 添加动画效果
//                            withAnimation(.easeInOut(duration: 0.3)) {
//                                wrapper.delegate.stepCount = realTimeSteps
//                            }
//                            
//                            // 更新相关的其他数据 - 添加动画
//                            withAnimation(.easeInOut(duration: 0.3)) {
//                                wrapper.delegate.updateActivityDataBasedOnSteps(realTimeSteps)
//                            }
//                            
//                            // 标记步数来源为实时数据
//                            wrapper.delegate.isRealStepCountData = true
//                            
//                            print("活动详情页更新实时步数: 从\(oldSteps)步 更新为\(realTimeSteps)步")
//                            
//                            // 标记数据已更新
//                            dataChanged = true
//                        } else {
//                            print("实时步数未变化，保持当前值: \(oldSteps)步")
//                        }
//                        
//                        // 更新卡路里 - 直接使用设备返回的卡路里数据
//                        if realTimeCalories > 0 {
//                            let newCalories = Double(realTimeCalories) / 10000.0
//                            let oldCalories = wrapper.delegate.calories
//                            // 更新卡路里数据 - 添加动画
//                            withAnimation(.easeInOut(duration: 0.3)) {
//                                wrapper.delegate.updateActivityDataBasedOnCalories(realTimeCalories)
//                            }
//                            print("活动详情页更新实时卡路里: 从\(oldCalories)千卡 更新为\(newCalories)千卡 (原始值: \(realTimeCalories))")
//                            
//                            // 标记数据已更新
//                            dataChanged = true
//                        }
//                        
//                        // 更新距离 - 直接使用格式化后的距离字符串值
//                        if realTimeDistance > 0 {
//                            // 计算格式化后的距离值(保留2位小数)
//                            let formattedDistanceStr = String(format: "%.2f", realTimeDistance)
//                            print("格式化后距离值(2位小数): \(formattedDistanceStr)")
//                            
//                            let formattedDistance = Double(formattedDistanceStr) ?? realTimeDistance
//                            let oldDistance = wrapper.delegate.distance
//                            // 直接使用格式化后的字符串值转为Double - 添加动画
//                            withAnimation(.easeInOut(duration: 0.3)) {
//                                wrapper.delegate.distance = formattedDistance
//                                wrapper.delegate.formattedDistanceString = formattedDistanceStr // 更新格式化字符串
//                            }
//                            wrapper.delegate.isRealDistanceData = true
//                            print("活动详情页更新实时距离: 从\(oldDistance)公里 更新为\(formattedDistanceStr)公里 (原始值: \(realTimeDistance))")
//                            
//                            // 标记数据已更新
//                            dataChanged = true
//                        }
//                        
//                        // 如果数据有更新，才更新时间戳
//                        if dataChanged {
//                            // 只在初始时间为空或查看非今天数据时更新时间戳
//                            let calendar = Calendar.current
//                            let today = Date()
//                            // 没有date变量，我们使用today，表示实时数据总是今天的数据
//                            let isToday = true
//                            
//                            // 如果是非今天数据，或者时间戳为空，则初始化时间戳
//                            if !isToday || wrapper.delegate.untilTime.isEmpty {
//                                let formatter = DateFormatter()
//                                formatter.dateFormat = "HH:mm"
//                                wrapper.delegate.untilTime = formatter.string(from: Date())
//                                print("初始化或历史数据，设置时间戳为: \(wrapper.delegate.untilTime)")
//                            } else {
//                                print("保持现有时间戳: \(wrapper.delegate.untilTime)，等待实时数据更新时再更新")
//                            }
//                        } else {
//                            print("数据未变化，时间戳保持不变: \(wrapper.delegate.untilTime)")
//                        }
//                    } else {
//                        print("设备返回的步数为0，不进行实时数据更新")
//                    }
//                }
//            }
//        } else {
//            print("设备未连接，无法获取实时步数数据")
//        }
//    }
    
    // 处理步数更新通知
    @objc func handleStepsUpdate(_ notification: Notification) {
        guard let wrapper = wrapper else { return }
        
        if let userInfo = notification.userInfo,
           let steps = userInfo["steps"] as? Int {
            
            // 尝试获取卡路里值，如果存在的话
            let calories = userInfo["calories"] as? Int ?? userInfo["cal"] as? Int
            
            DispatchQueue.main.async {
                // 标记数据是否有更新
                var dataChanged = false
                
                // 只有当步数真正发生变化时才更新
                let oldSteps = wrapper.delegate.stepCount
                if oldSteps != steps {
                    // 添加动画效果
                    withAnimation(.easeInOut(duration: 0.3)) {
                        wrapper.delegate.stepCount = steps
                    }
                    
                    // 更新相关的其他数据 - 添加动画
                    withAnimation(.easeInOut(duration: 0.3)) {
                        wrapper.delegate.updateActivityDataBasedOnSteps(steps)
                    }
                    
                    // 标记步数来源为实时数据
                    wrapper.delegate.isRealStepCountData = true
                    
                    print("活动详情页收到步数更新通知: 从\(oldSteps)步 更新为\(steps)步")
                    
                    // 标记数据已更新
                    dataChanged = true
                } else {
                    print("步数更新通知：步数未变化，保持当前值: \(oldSteps)步")
                }
                
                // 如果通知中包含卡路里数据，检查并更新
                if let calories = calories {
                    let newCalories = Double(calories)
                    let oldCalories = wrapper.delegate.calories
                    // 添加动画效果
                    withAnimation(.easeInOut(duration: 0.3)) {
                        wrapper.delegate.updateActivityDataBasedOnCalories(calories)
                    }
                    print("活动详情页收到卡路里更新通知: 从\(oldCalories)千卡 更新为\(newCalories)千卡")
                    
                    // 标记数据已更新
                    dataChanged = true
                }
                
                // 如果数据有更新，才更新时间戳
                if dataChanged {
                    // 只在初始时间为空或查看非今天数据时更新时间戳
                    let calendar = Calendar.current
                    let today = Date()
                    // 没有date变量，我们使用today，表示实时数据总是今天的数据
                    let isToday = true
                    
                    // 如果是非今天数据，或者时间戳为空，则初始化时间戳
                    if !isToday || wrapper.delegate.untilTime.isEmpty {
                        let formatter = DateFormatter()
                        formatter.dateFormat = "HH:mm"
                        wrapper.delegate.untilTime = formatter.string(from: Date())
                        print("初始化或历史数据，设置时间戳为: \(wrapper.delegate.untilTime)")
                    } else {
                        print("保持现有时间戳: \(wrapper.delegate.untilTime)，等待实时数据更新时再更新")
                    }
                } else {
                    print("数据未变化，时间戳保持不变: \(wrapper.delegate.untilTime)")
                }
            }
        }
    }
}

/// 活动详情页面
struct ActivityDetailView: View {//ActivityDetailViewDelegate
    // MARK: - 属性
    @Environment(\.presentationMode) var presentationMode
//    @State private var selectedDay: Int = 0 // 默认选中"今天"
//    @State private var selectedDate = Date()
    @State private var rawActivityJson: String = "" // 存储原始活动数据的JSON
    @State private var showJsonData: Bool = false // 控制是否显示JSON数据
    @ObservedObject private var sharedDateViewModel = SharedDateViewModel.shared
    // 活动数据
    @State var activityData: ActivityScoreData?
//    @State var activityScore: Int = 0
//    @State var stepCount: Int = 0 // 将默认值改为0，而不是1870
//    @State var hours: Int = 0
//    @State var minutes: Int = 0
//    @State var calories: Double = 0.0
//    @State var distance: Double = 0.0
//    @State var formattedDistanceString: String = "0.00" // 存储格式化后的距离字符串
//    @State internal var untilTime: String = ""
    
    // 实时步数监听相关
    // 使用包装器和数据管理器
//    @State private var wrapper: ActivityDetailViewWrapper?
//    @State private var dataManager: ActivityDataManager?
    
    // 数据源标志 - 区分真实数据和模拟数据
    @State var isRealActivityScoreData: Bool = false  // 标记活动评分是否为真实数据
    @State var isRealStepCountData: Bool = false      // 标记步数是否为真实数据
    @State var isRealTimeData: Bool = false           // 标记活动时间是否为真实数据
    @State var isRealCaloriesData: Bool = false       // 标记卡路里是否为真实数据
    @State var isRealDistanceData: Bool = false       // 标记距离是否为真实数据
    
    // 日历选择器状态
    @State private var showCalendarPicker: Bool = false
    @State private var animateCalendar: Bool = false  // 添加animateCalendar状态变量
    
    // 蓝牙连接状态
    @State private var bluetoothState: BluetoothConnectionState = .disconnected
    @State private var isShowingBluetoothAlert = false // 控制蓝牙操作提示框
    @State private var shouldCancelConnection = false // 取消连接标志
    @State private var connectionCheckTimer: AnyCancellable? // 连接状态检查计时器
    @State private var connectedDeviceName = "" // 连接的设备名称
    @State private var showConnectedToast = false // 显示连接成功提示
    
    // 添加设备服务实例
    @StateObject private var deviceService = WindRingDeviceService.shared
    
    // 加载状态
    @State private var isLoading: Bool = false
    @State private var errorMessage: String?
    
    // 用于存储Combine的Cancellables
    @State private var cancellables: Set<AnyCancellable> = []
    
    // 活动明细相关状态
    @State private var activityDetails: [ActivityDetailData] = []
    @State private var isLoadingActivityDetails: Bool = false
    @State private var activityDetailsError: String?
    
    // 活动状态和明细相关状态
    @State private var isLoadingActivityStates: Bool = false
    @State private var activityStatesError: String?
    @State private var isLoadingActivityCalories: Bool = false
    @State private var activityCaloriesError: String?
    
    // 自动上传状态
    @State private var autoUploadEnabled: Bool = false
    
    @State private var activityAvgStateData: ActivityAvgStateData?
    
    
    // 将计算属性改为方法，避免在初始化阶段的编译错误
    private func getDatesWithData() -> [Date] {
        let calendar = Calendar.current
        let today = Date()
        var dates: [Date] = [today]
        
        // 在过去的30天中随机选择一些日期标记为有数据
        for day in 1...30 {
            if let pastDate = calendar.date(byAdding: .day, value: -day, to: today) {
                if Int.random(in: 1...10) <= 3 { // 约30%的几率
                    dates.append(pastDate)
                }
            }
        }
        return dates
    }
    
    // MARK: - 主视图
    var body: some View {
        VStack(spacing: 0) {
            // 顶部状态栏
            VStack(spacing: 12) {
                // 设备连接状态 - 已隐藏
                // deviceStatusSection
                
                // 自动上传控制 - 已隐藏
                /*
                HStack {
                    Text("自动上传数据:")
                        .font(.subheadline)
                    
                    Toggle("", isOn: $autoUploadEnabled) { newValue in
                            updateAutoUploadSetting(enabled: newValue)
                        }
                    
                    Text(autoUploadEnabled ? "已开启" : "已关闭")
                        .font(.subheadline)
                        .foregroundColor(autoUploadEnabled ? .green : .gray)
                        .animation(.easeInOut, value: autoUploadEnabled)
                    
                    Spacer()
                    
                    // 显示上次上传时间
                    if let lastUploadTime = RawDataUploadService.shared.lastActivityUploadTime {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.footnote)
                            
                            Text("上次: \(formatDate(lastUploadTime))")
                                .font(.footnote)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.gray.opacity(0.05))
                .cornerRadius(8)
                .padding(.horizontal, 16)
                */
                
            // 自定义导航栏
            HStack {
                // 返回按钮
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.white)
                            .font(.system(size: 16))
                        
                        Text("activity_detail_nav_title".localized)
                            .foregroundColor(.white)
                            .font(.system(size: 16, weight: .bold))
                    }
                }
                            
                            Spacer()
                            
                // 右侧图标按钮
                HStack(spacing: 16) {
                        // 日历按钮 - Insight风格
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            showCalendarPicker = true
                        }
                    }) {
                        Image("日期 (2)")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                    }
                    
                        // 蓝牙按钮 - 与Insight页面相同的实现
                        Button(action: {
                            // 蓝牙按钮点击逻辑
                            handleBluetoothButtonTap()
                        }) {
                            ZStack {
                                // 根据连接状态显示不同图标
                                if bluetoothState == .disconnected {
                                    // 断开连接时显示断连图标
                                    Image("断连")
                            .resizable()
                                        .frame(width: 16, height: 16)
                                } else if bluetoothState == .connected {
                                    // 已连接状态显示连接状态图标 - 使用蓝色圆环
                                    Image("连联")
                                        .resizable()
                                        .frame(width: 16, height: 16)
                                } else if bluetoothState == .connecting {
                                    // 连接中状态的子视图
                                    ConnectingStateView()
                            .frame(width: 16, height: 16)
                                }
                            }
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 12) // 为状态栏留出更多空间
            .padding(.bottom, 8) // 增加底部间距，给日期选择器留出更多空间
            }
            
            // 日期选择器 - 作为固定的顶部元素
            DateSelectionView(
//                selectedDay: $sharedDateViewModel.selectedDay,
                selectedDate: $sharedDateViewModel.selectedDate,
                onDateSelected: { day, date in
                    // 加载数据 - 不需要强制刷新
                    loadActivityData(for: date, forceRefresh: false)
                    // 获取原始活动数据
                    fetchDeviceActivityData(day: day)
                }
            )
            .background(Color(hex: "#070708")) // 确保背景色与页面一致
            .zIndex(1) // 确保日期选择器始终位于顶层
            .padding(.bottom, 10) // 修改底部间距为10，与内容区域保持一致
            
            // 可滚动内容区域
            ScrollView {
                VStack(spacing: 10) {
                    // 移除单独的加载状态显示
                    /*
                    if isLoading {
                        VStack(spacing: 10) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.2)
                                .padding()
                            
                            Text("正在加载活动数据...")
                                .font(.system(size: 14))
                                .foregroundColor(.gray)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 40)
                    }
                    */
                    
                    // 活动评分卡片 - 添加加载状态覆盖层
                    ZStack {
                        activityScoreCard
                        
                        if isLoading {
                            loadingOverlay
                        }
                    }
                    
                    // 步数统计图表 - 添加加载状态覆盖层
                    ZStack {
                        stepCountCard
                        
                        if isLoading {
                            loadingOverlay
                        }
                    }
                    
                    // 活动卡路里图表 - 添加加载状态覆盖层
                    ZStack {
                        activityCaloriesCard
                        
                        if isLoading {
                            loadingOverlay
                        }
                    }
                    
                    // 全天卡路里消耗分析 - 添加加载状态覆盖层
                    ZStack {
                        allDayCaloriesCard
                        
                        if isLoading {
                            loadingOverlay
                        }
                    }
                        
                        // 活动明细列表 - 根据用户要求删除该卡片
                        // activityDetailListCard
                    
                    // 其他活动统计信息卡片
                    // 可以添加更多卡片...
                    
                    Spacer(minLength: 40)
                }
                .padding(.horizontal, 10)
                .padding(.vertical, 10) // 修改为同时设置顶部和底部的内边距为10
            }
        }
        .background(Color(hex: "#070708").edgesIgnoringSafeArea(.all))
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true) // 完全隐藏系统导航栏
        .onAppear {
            // 确保wrapper和dataManager已初始化
//            if wrapper == nil {
//                wrapper = ActivityDetailViewWrapper(delegate: self)
//            }
            
//            if dataManager == nil {
//                dataManager = ActivityDataManager(wrapper: wrapper!)
//            }
            
            // 确保自动上传功能始终开启
            autoUploadEnabled = true
            updateAutoUploadSetting(enabled: true)
            
            // 初始加载数据
            loadActivityData(for: sharedDateViewModel.selectedDate, forceRefresh: false)
            
            // 连接管理
            syncBluetoothState()
            startConnectionStateChecking()
            
            // 额外添加一个后台任务，检查卡路里数据是否被错误地设置为0
//            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
//                // 如果calories为0但API返回的活动评分不为0，则恢复正确的值
//                if self.calories == 0.0 && self.isRealCaloriesData {
//                    print("检测到卡路里值被错误地设置为0，尝试恢复...")
//                    
//                    // 重新加载活动数据
//                    loadActivityData(for: sharedDateViewModel.selectedDate, forceRefresh: true)
//                }
//            }
        }
        .onDisappear {
            // 取消计时器
            connectionCheckTimer?.cancel()
            
            // 停止监听实时步数
            stopListeningRealTimeStepsData()
        }
        // 监听selectedDate的变化
        .onChange(of: sharedDateViewModel.selectedDate) { newDate in
            // 加载新日期的数据
            loadActivityData(for: newDate, forceRefresh: false)
            // 获取设备活动数据
            fetchDeviceActivityData(day: sharedDateViewModel.selectedDay)
        }
        // 日历选择器浮层
        .overlay(
                ZStack {
                if showCalendarPicker {
                    // 磨砂半透明背景
                    Color.black.opacity(0.8)
                        .ignoresSafeArea()
                        .blur(radius: 5)
                                .onTapGesture {
                            closeCalendarPicker()
                        }
                    
                    // 自定义日历视图
                    CustomCalendarView(
                        selectedDate: $sharedDateViewModel.selectedDate,
//                        selectedDay: $sharedDateViewModel.selectedDay,
                        onClose: closeCalendarPicker
                    )
                    .frame(width: UIScreen.main.bounds.width * 0.85)
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .animation(.spring(response: 0.35, dampingFraction: 0.8), value: showCalendarPicker)
        )
        // 添加连接成功提示
        .overlay(
            VStack {
                if showConnectedToast {
                    Spacer().frame(height: 80)
                    
                    HStack(spacing: 10) {
                        Image("连联")
                            .resizable()
                            .frame(width: 18, height: 18)
                        
                        Text(String(format: "insight_connected_to_device".localized, connectedDeviceName))
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                    }
                    .padding(.vertical, 8)
                    .padding(.horizontal, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.black.opacity(0.7))
                    )
                    .transition(.move(edge: .top).combined(with: .opacity))
                    
                    Spacer()
                }
            }
            .animation(.easeInOut, value: showConnectedToast)
        )
        .alert(isPresented: $isShowingBluetoothAlert) {
            Alert(
                title: Text("insight_disconnect_alert_title".localized),
                message: Text("insight_disconnect_alert_message".localized),
                primaryButton: .destructive(Text("insight_disconnect_button_title".localized)) {
                    disconnectBluetooth()
                },
                secondaryButton: .cancel(Text("cancel".localized))
            )
        }
//        .onChange(of: calories) { newValue in
//            // 记录卡路里值的变化，帮助调试
//            print("卡路里值变化: \(newValue) kcal")
//        }
    }
    
    // MARK: - 活动评分卡片
    private var activityScoreCard: some View {
        VStack(spacing: 0) {
            // 标题行
            HStack {
                Text("activity_detail_score_title".localized)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white)
                
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.activityScore.rawValue)
                
                Spacer()
                if sharedDateViewModel.selectedDate.isInToday{
                    Text("Until: \(Date().string(withFormat: "hh:mm"))")
                        .font(.system(size: 13))
                        .foregroundColor(.gray)
                }
                
            }
            .padding(.top, 22)
            .padding(.horizontal, 21.5)
            .padding(.bottom, 10)
            
            
            // 添加额外的间距，确保半圆形指示器与卡片顶部间距为70pt
            // 70pt减去标题行的高度和已有的底部间距10pt
            Spacer()
                .frame(height: 90)
            
            // 活动评分仪表盘 - 三层彩色半圆进度指示器
            ZStack {
                // 外层蓝色弧形 - 基于总体活动得分
                let overallProgress = min(1.0, Double(activityData?.score ?? 0) / 100.0)
                ActivityArcView(progress: overallProgress, thickness: 10, color: Color(hex: "#007AFF"), radius: 210)
                
                // 中间青色弧形 - 基于步数得分
                let stepsProgress = min(1.0, Double(activityData?.steps ?? 0) / AppGlobals.defaultSteps)
                ActivityArcView(progress: stepsProgress, thickness: 10, color: Color(hex: "#00C8FF"), radius: 155)
                
                // 内层紫色弧形 - 基于活动时间
                let timeProgress = min(1.0, Double((activityData?.time ?? 0)) / AppGlobals.defaultActivityStepTime)
                ActivityArcView(progress: timeProgress, thickness: 10, color: Color(hex: "#A152E4"), radius: 100)
                
                // 黑色背景圆形，增强中间数字的可读性
                Circle()
                    .fill(Color(hex: "#10131A"))
                    .frame(width: 60, height: 60)
                
                // 中间评分数字
                Text(activityData?.score ?? 0 <= 0 ? "--" : "\(activityData?.score ?? 0)")
                    .font(.system(size: 30, weight: .bold))
                    .foregroundColor(.white)
            }
            .frame(height: 130)
            .padding(.bottom, 0)
            
            // 四个主要指标卡片 - 2x2 布局
            VStack(spacing: 10) {
                HStack(spacing: 10) {
                    // 步数卡片
                    let steps = activityData?.steps ?? 0
                    ActivityDataCardView(
                        icon: "组 1_slices",
                        iconColor: .cyan,
                        value: steps > 0 ? "\(steps.description)" : "--",
                        unit: "activity_detail_unit_steps".localized,
                        isRealData: isRealStepCountData && steps > 0
                    )
                    
                    // 时间卡片 - 使用基于步数的预估时间
                    let time = activityData?.time ?? 0
                    let estimatedTime = estimateActivityTimeFromSteps(time)
                    let usingEstimatedTime = estimatedTime.hours <= 0 && estimatedTime.minutes <= 0
                    ActivityTimeCardView(
                        icon: "组 1_slices (1)",
                        iconColor: .blue,
                        hours: estimatedTime.hours,
                        minutes: estimatedTime.minutes,
                        isRealData: isRealTimeData && (estimatedTime.hours > 0 || estimatedTime.minutes > 0),
                        hasData: true, // 始终显示数据，因为我们现在可以基于步数预估
                        isEstimated: usingEstimatedTime // 标记是否使用了估算时间
                    )
                }
                
                HStack(spacing: 10) {
                    // 卡路里卡片
                    let calories = activityData?.calories ?? 0
                    ActivityDataCardView(
                        icon: "组 1_slices (2)",
                        iconColor: .purple,
                        value: calories > 0 ? "\(Int(calories))" : "--",
                        unit: "insight_unit_kcal".localized,
                        isRealData: isRealCaloriesData && calories > 0,
                        isCaloriesCard: true // 标记为卡路里卡片
                    )
                    
                    // 距离卡片
                    let distance = (activityData?.distance ?? 0.0) / 1000.0
                    let distanceString = distance > 0 ? String(format: "%.1f", distance) : "--"
                    ActivityDataCardView(
                        icon: "组 1_slices (3)",
                        iconColor: .cyan,
                        value: distanceString,
                        unit: "activity_detail_unit_km".localized,
                        isRealData: isRealDistanceData && distance > 0
                    )
                }
            }
            .padding(.horizontal, 10)
            .padding(.bottom, 20)
        }
        .background(Color(hex: "#10131A"))
        .cornerRadius(24)
    }
    
    // MARK: - 数据加载函数
    private func loadActivityData(for date: Date, forceRefresh: Bool = false) {
        // 设置加载状态
        isLoading = true
        // 错误消息保持为nil，即使发生错误也能显示页面
        // errorMessage = nil
        isLoadingActivityStates = true
        activityStatesError = nil
        isLoadingActivityDetails = true
        activityDetailsError = nil
        isLoadingActivityCalories = true
        activityCaloriesError = nil
        
        // 只在查看历史数据或时间戳为空时更新时间戳
        let calendar = Calendar.current
        let today = Date()
        let isToday = calendar.isDateInToday(date)
        
//        if !isToday || untilTime.isEmpty {
//            let formatter = DateFormatter()
//            formatter.dateFormat = "HH:mm"
//            untilTime = formatter.string(from: Date())
//            print("加载页面：初始化或历史数据，设置时间戳为: \(untilTime)")
//        } else {
//            print("加载页面：保持现有时间戳: \(untilTime)，等待实时数据更新时再更新")
//        }
        
        // 计算与今天的日期差
        let components = calendar.dateComponents([.day], from: calendar.startOfDay(for: date), to: calendar.startOfDay(for: today))
        let dayDifference = -(components.day ?? 0)
        
        // 如果是今天，开始监听实时步数
        if isToday {
            startListeningRealTimeStepsData()
        } else {
            // 如果不是今天，停止监听实时步数
            stopListeningRealTimeStepsData()
        }
        
        // 从设备获取活动数据 - 保留这个调用以兼容现有功能
        fetchDeviceActivityData(day: dayDifference)
        
        // 从API获取活动评分数据
        ActivityService.shared.getActivityScore(for: date, forceRefresh: forceRefresh)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoading = false
                
                switch completion {
                case .failure(let error):
                    // 不设置错误信息，只打印错误日志
                    print("加载活动数据失败: \(error.localizedDescription)")
                    
                    // 使用默认数据并标记为非真实数据
//                    self.activityScore = 0 // 将模拟活动评分改为0
                    self.activityData = nil
                    self.isRealActivityScoreData = false
                    self.isRealStepCountData = false
                    self.isRealTimeData = false
                    self.isRealCaloriesData = false
                    self.isRealDistanceData = false
                case .finished:
                    break
                }
            }, receiveValue: { scoreData in
                // 更新UI数据
                print("成功获取活动评分数据: \(scoreData.score)分")
                self.activityData = scoreData
//                self.activityScore = scoreData.score
                
                // 只有在步数为0或者SDK直接获取的步数小于API步数时，才使用API返回的步数
//                if self.stepCount == 0 || !self.isRealStepCountData || self.stepCount < scoreData.steps {
//                    print("使用API返回的步数: 从\(self.stepCount)步 更新为\(scoreData.steps)步")
//                    self.stepCount = scoreData.steps
//                } else {
//                    print("保留SDK获取的步数: \(self.stepCount)步 (API返回\(scoreData.steps)步)")
//                }
                
                // 计算活动时间 - 秒数转换为小时和分钟
//                let hours = scoreData.time / 3600
//                let minutes = (scoreData.time % 3600) / 60
//                
//                // 设置活动时间
//                self.hours = hours
//                self.minutes = minutes
                
                // 设置数据来源标记
                self.isRealActivityScoreData = true
                self.isRealStepCountData = true
                self.isRealTimeData = true
                
//                if scoreData.distance > 0 {
//                    // 将米转换为公里并保留2位小数
//                    let distanceKm = Double(scoreData.distance) / 1000.0
//                    self.distance = distanceKm
//                    self.formattedDistanceString = String(format: "%.2f", distanceKm)
//                    self.isRealDistanceData = true
//                }
                
                // 根据API响应数据更新scoreData中的卡路里值，处理API返回的数据
//                if scoreData.calories > 0 {
//                    // 将卡路里显示为千卡，统一除以10000并取整
//                    self.calories = Double(Int(Double(scoreData.calories) / 10000.0))
//                    self.isRealCaloriesData = true
//                }
            })
            .store(in: &cancellables)
        
        // 从API获取活动状态数据
        ActivityService.shared.getActivityStates(for: date, forceRefresh: forceRefresh)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoadingActivityStates = false
                
                switch completion {
                case .failure(let error):
                    // 不设置错误信息，只打印错误日志
                    print("加载活动状态数据失败: \(error.localizedDescription)")
                    
                    // 使用备用数据
                    self.activityLevels = self.generateMockActivityLevels(for: date)
                case .finished:
                    break
                }
            }, receiveValue: { stateDataArray in
                // 更新活动状态数据
                if stateDataArray.isEmpty {
                    print("警告: 获取到的活动状态数据为空")
                    self.activityLevels = self.generateMockActivityLevels(for: date)
                } else {
                    print("成功获取活动状态数据 - 条目数: \(stateDataArray.count)")
                    
                    // 转换为UI需要的格式
//                    let formattedData = stateDataArray.map { (level: $0.title, halfHourIndex: $0.halfHourIndex) }
                    let formattedData = stateDataArray.map { state in
                        let localizedTitle: String
                        switch state.title {
                        case "Vigorous":
                            localizedTitle = "activity_intensity_vigorous".localized
                        case "Moderate":
                            localizedTitle = "activity_intensity_moderate".localized
                        case "Low":
                            localizedTitle = "activity_intensity_low".localized
                        case "Inactive":
                            localizedTitle = "activity_intensity_inactive".localized
                        default:
                            localizedTitle = ""
                        }
                        return (level: localizedTitle, halfHourIndex: state.halfHourIndex)
                    }
                    self.activityLevels = formattedData
                    
                    // 打印部分数据用于调试
                    let sampleCount = min(3, stateDataArray.count)
                    for (index, state) in stateDataArray.prefix(sampleCount).enumerated() {
                        print("活动状态数据样本[\(index+1)]: 级别=\(state.title), 时间=\(state.formattedTime()), 半小时索引=\(state.halfHourIndex)")
                    }
                }
            })
            .store(in: &cancellables)
            
        // 从API获取活动明细数据
        ActivityService.shared.getActivityDetails(for: date, forceRefresh: forceRefresh)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoadingActivityDetails = false
                
                switch completion {
                case .failure(let error):
                    // 不设置错误信息，只打印错误日志
                    print("加载活动明细数据失败: \(error.localizedDescription)")
                    
                    // 清空原有数据
                    self.activityDetails = []
                    // 更新图表数据 - 失败时使用模拟数据
                    self.updateHourlyData(date: date)
                    
                    // 在明细数据获取完成后加载活动状态数据
                    self.loadActivityStates(for: date, forceRefresh: forceRefresh)
                case .finished:
                    break
                }
            }, receiveValue: { detailDataArray in
                // 更新活动明细数据
                if detailDataArray.isEmpty {
                    print("警告: 获取到的活动明细数据为空")
                    // 如果没有明细数据，使用模拟数据
                    self.activityDetails = []
                    self.updateHourlyData(date: date)
                } else {
                    print("成功获取活动明细数据 - 共\(detailDataArray.count)条")
                    self.activityDetails = detailDataArray
                    
                    // 根据活动明细数据更新图表
                    self.updateHourlyDataFromDetails(detailDataArray)
                    
                    // 打印部分数据用于调试
                    let sampleCount = min(3, detailDataArray.count)
                    for (index, detail) in detailDataArray.prefix(sampleCount).enumerated() {
                        print("活动明细数据样本[\(index+1)]: 时间=\(detail.formattedTime()), 步数=\(detail.steps)")
                    }
                }
                
                // 在明细数据获取完成后加载活动状态数据
                self.loadActivityStates(for: date, forceRefresh: forceRefresh)
            })
            .store(in: &cancellables)
        
        // 从API获取活动卡路里数据
        ActivityService.shared.getActivityCalories(for: date, forceRefresh: forceRefresh)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoadingActivityCalories = false
                
                switch completion {
                case .failure(let error):
                    // 不设置错误信息，只打印错误日志
                    print("加载活动卡路里数据失败: \(error.localizedDescription)")
                    
                    // 使用空数据代替模拟数据
                    self.hourlyCalorieData = Array(repeating: 0, count: 48)
                    print("使用空数据代替模拟卡路里数据")
                case .finished:
                    break
                }
            }, receiveValue: { caloriesData in
                // 成功获取数据
                print("成功获取活动卡路里数据，数据点数量: \(caloriesData.count)")
                
                // 将原始数据以JSON格式打印到控制台
                self.printCaloriesDataAsJSON(caloriesData)
                
                // 清空现有数据
                var newCalorieData = Array(repeating: 0, count: 48)
                
                // 遍历活动卡路里数据，更新对应半小时索引的卡路里值
                for calorie in caloriesData {
                    if let halfHourIndex = calorie.halfHourIndex, halfHourIndex >= 0 && halfHourIndex < 48 {
                        // 更新卡路里数据
                        newCalorieData[halfHourIndex] = Int(calorie.calories)
                    }
                }
                
                // 更新UI数据
                DispatchQueue.main.async {
                    self.hourlyCalorieData = newCalorieData
                    
                    // 计算总卡路里消耗
                    let totalCalories = newCalorieData.reduce(0, +)
                    
                    // 记录当前的卡路里值，用于后续比较
                    let oldCaloriesValue = (activityData?.calories ?? 0).description.double() ?? 0.0 //self.calories
                    
                    // 只有在当前卡路里值为0或小于新计算的值时才更新
                    if totalCalories > 0 && (oldCaloriesValue <= 0 || Double(totalCalories) / 10000.0 > oldCaloriesValue) {
                        // 显示时将原始卡路里值除以10000并取整
                        activityData?.calories = totalCalories
//                        self.calories = Double(Int(Double(totalCalories) / 10000.0))
                        print("使用活动卡路里图表数据更新: 总计\(totalCalories) -> \(activityData?.calories.description ?? "") kcal")
                    } else if totalCalories > 0 {
                        print("活动卡路里图表数据计算值(\(Double(totalCalories) / 10000.0))小于当前值(\(oldCaloriesValue))，保留当前值")
                    } else if oldCaloriesValue <= 0 {
                        print("活动卡路里图表数据为空且当前卡路里值为0")
                    } else {
                        print("活动卡路里图表数据为空，保留当前卡路里值: \(oldCaloriesValue) kcal")
                    }
                }
            })
            .store(in: &cancellables)
        
        ActivityService.shared.getActivityAvgState(for: date).receive(on: DispatchQueue.main)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                
            }, receiveValue: { data in
                self.activityAvgStateData = data
            }).store(in: &cancellables)
    }
    
    // 将卡路里数据以JSON格式打印到控制台
    private func printCaloriesDataAsJSON(_ caloriesData: [ActivityCalorieData]) {
        let encoder = JSONEncoder()
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        
        do {
            // 创建简化的数据结构，便于阅读
            let simplifiedData = caloriesData.map { data -> [String: Any] in
                let timeString = data.formattedTime()
                let halfHourIndex = data.halfHourIndex ?? -1
                
                return [
                    "calories": data.calories,
                    "time": data.time,
                    "formattedTime": timeString,
                    "halfHourIndex": halfHourIndex
                ]
            }
            
            // 转换为JSON可序列化的字典
            let jsonObject: [String: Any] = [
                "count": caloriesData.count,
                "timestamp": Date().timeIntervalSince1970,
                "data": simplifiedData
            ]
            
            // 转换为Data
            let jsonData = try JSONSerialization.data(withJSONObject: jsonObject, options: [.prettyPrinted, .sortedKeys])
            
            // 转换为字符串并打印
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("====== 活动卡路里原始数据(JSON格式) ======")
                print(jsonString)
                print("=======================================")
            }
        } catch {
            print("转换卡路里数据为JSON失败: \(error.localizedDescription)")
        }
    }
    
    // 根据活动明细数据更新图表数据
    private func updateHourlyDataFromDetails(_ details: [ActivityDetailData]) {
        // 清空现有数据
        var newStepData = Array(repeating: 0, count: 48)
        var newCalorieData = Array(repeating: 0, count: 48)
        
        // 遍历活动明细数据，更新对应半小时索引的步数
        for detail in details {
            if let halfHourIndex = detail.halfHourIndex, halfHourIndex >= 0 && halfHourIndex < 48 {
                // 更新步数数据
                newStepData[halfHourIndex] = detail.steps
                
                // 估算卡路里数据（假设每1000步消耗40卡路里）
                newCalorieData[halfHourIndex] = Int(Double(detail.steps) * 0.04)
            }
        }
        
        // 更新图表数据
        DispatchQueue.main.async {
            self.hourlyStepData = newStepData
            self.hourlyCalorieData = newCalorieData
        }
    }
    
    // 生成模拟活动水平数据
    private func generateMockActivityLevels() {
        // 调用新的带参数版本，使用createDefaultInactiveActivityLevels替代
        self.activityLevels = createDefaultInactiveActivityLevels()
    }
    
    // MARK: - 获取设备原始活动数据并转换为JSON
    private func fetchDeviceActivityData(day: Int) {
        // 检查设备连接状态
        guard WindRingDeviceService.shared.connectionState.isConnected else {
            rawActivityJson = """
            {
              "error": "\("activity_detail_json_error_disconnected".localized)",
              "message": "\("activity_detail_json_error_connect_first".localized)",
              "timestamp": "\(Date())"
            }
            """
            return
        }
        
        // 显示加载中状态
        rawActivityJson = "activity_detail_json_loading".localized
        
        // 获取活动数据
//        CRPSmartRingSDK.sharedInstance.getTrainingData(day) { record, error in
//            DispatchQueue.main.async {
//                if error == .none {
//                    // 添加更多元数据
//                    let dateString = self.formattedDate(daysAgo: day)
//                    let calendar = Calendar.current
//                    let today = Date()
//                    let date = calendar.date(byAdding: .day, value: day, to: today) ?? today
//                    
//                    // 转换为JSON对象
//                    let jsonObject: [String: Any] = [
//                        "data": [
//                            "day": record.day,
//                            "step": record.step,
//                            "cal": record.cal,
//                            "exerciseTime": record.exerciseTime,
//                            "distance": record.distance
//                        ],
//                        "metadata": [
//                            "deviceConnected": true,
//                            "requestDay": day,
//                            "date": dateString,
//                            "timestamp": Int(Date().timeIntervalSince1970),
//                            "weekday": calendar.component(.weekday, from: date),
//                            "source": "CRPSmartRingSDK.getTrainingData"
//                        ],
//                        "status": "success"
//                    ]
//                    
//                    // 转换为JSON字符串
//                    if let jsonData = try? JSONSerialization.data(withJSONObject: jsonObject, options: [.prettyPrinted, .sortedKeys]),
//                       let jsonString = String(data: jsonData, encoding: .utf8) {
//                        self.rawActivityJson = jsonString
//                    } else {
//                        self.rawActivityJson = """
//                        {
//                          "error": "\("activity_detail_json_error_conversion".localized)",
//                          "message": "\("activity_detail_json_error_format".localized)",
//                          "timestamp": "\(Date())"
//                        }
//                        """
//                    }
//                } else {
//                    // 处理特定错误
//                    var errorCode = "unknown"
//                    var errorMessage = "activity_detail_sdk_error_other".localized
//                    
//                    switch error {
//                    case .disconnected:
//                        errorCode = "disconnected"
//                        errorMessage = "activity_detail_sdk_error_disconnected".localized
//                    case .timeout:
//                        errorCode = "timeout"
//                        errorMessage = "activity_detail_sdk_error_timeout".localized
//                    case .busy:
//                        errorCode = "busy"
//                        errorMessage = "activity_detail_sdk_error_busy".localized
//                    case .interrupted:
//                        errorCode = "interrupted"
//                        errorMessage = "activity_detail_sdk_error_interrupted".localized
//                    case .internalError:
//                        errorCode = "internal_error"
//                        errorMessage = "activity_detail_sdk_error_internal".localized
//                    default:
//                        errorCode = "other"
//                        errorMessage = String(format: "activity_detail_sdk_error_other".localized, "\(error)")
//                    }
//                    
//                    // 生成错误JSON
//                    self.rawActivityJson = """
//                    {
//                      "error": "\(errorCode)",
//                      "message": "\(errorMessage)",
//                      "timestamp": "\(Date())",
//                      "requestDay": \(day)
//                    }
//                    """
//                }
//            }
//        }
    }
    
    // 格式化日期
    private func formattedDate(daysAgo: Int) -> String {
        let date = Calendar.current.date(byAdding: .day, value: daysAgo, to: Date()) ?? Date()
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    // MARK: - 每小时数据
    @State private var hourlyStepData: [Int] = Array(repeating: 0, count: 48) // 48个半小时数据点
    @State private var hourlyCalorieData: [Int] = Array(repeating: 0, count: 48) // 48个半小时数据点
    @State private var activityLevels: [(level: String, halfHourIndex: Int)] = []
    
    private func updateHourlyData(date: Date) {
        // 不再生成模拟数据，只显示空数据
        let newStepData = Array(repeating: 0, count: 48)
        let newCalorieData = Array(repeating: 0, count: 48)
        
        // 使用默认的Inactive状态
        let activityLevelData = createDefaultInactiveActivityLevels()
        
        // 更新状态
        DispatchQueue.main.async {
            self.hourlyStepData = newStepData
            self.hourlyCalorieData = newCalorieData
            self.activityLevels = activityLevelData
            
            // 记录日志，表明我们显示的是空数据而不是模拟数据
            print("使用空数据代替模拟数据")
        }
    }
    
    // 生成模拟活动水平数据
    private func generateMockActivityLevels(for date: Date) -> [(level: String, halfHourIndex: Int)] {
        // 直接调用创建默认Inactive活动级别的方法替代随机生成
        return createDefaultInactiveActivityLevels()
    }
    
    // MARK: - 步数统计卡片
    @State private var selectedHalfHourIndex: Int? = nil // 记录用户选中的半小时索引
    @State private var stepGoal: Int = 6000 // 步数目标
    
    private var stepCountCard: some View {
        VStack(spacing: 0) {
            // 标题行
            HStack {
                Text("activity_detail_steps_card_title".localized)
                    .font(.custom("PingFang-SC-Bold", size: 18))
                    .foregroundColor(.white)
                
                Spacer()
                
                if sharedDateViewModel.selectedDate.isInToday{
                    Text("Until: \(Date().string(withFormat: "hh:mm"))")
                        .font(.system(size: 13))
                        .foregroundColor(.gray)
                }
            }
            .padding(.top, 22) // 保持标题的上边距不变
            .padding(.horizontal, 22) // 保持标题的水平边距不变
            .padding(.bottom, 12)
            
            // 步数和时间段信息 - 根据是否选中柱子显示不同内容
                HStack {
                Spacer()
                
                // 紧凑布局的步数显示
                if let selectedIndex = selectedHalfHourIndex {
                    // 显示选中时段的步数和时间范围（图1）
                    HStack(spacing: 2) {
                        Text("\(hourlyStepData[selectedIndex])")
                            .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                    
                        Text("activity_detail_unit_step".localized)
                            .font(.system(size: 10))
                            .foregroundColor(.gray.opacity(0.8))
                        
                        Text(" \(formatTimeRange(for: selectedIndex))")
                            .font(.system(size: 10))
                            .foregroundColor(.gray.opacity(0.8))
                    }
                    .padding(.horizontal, 10)
                    .padding(.vertical, 3)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color(hex: "#1A2029"))
                    )
                } else {
                    // 显示总步数和目标步数（图2）
                    HStack(spacing: 2) {
                        // 当步数为0时显示--
                        let stepCount = activityData?.steps ?? 0
                        Text(stepCount > 0 ? "\(stepCount)" : "--")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                        
                        Text("/ \(stepGoal)")
                            .font(.system(size: 12))
                            .foregroundColor(.gray.opacity(0.8))
                        
                        Text("activity_detail_unit_step".localized)
                            .font(.system(size: 10))
                            .foregroundColor(.gray.opacity(0.8))
                    }
                    .padding(.horizontal, 10)
                    .padding(.vertical, 3)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color(hex: "#1A2029"))
                    )
                }
                
                Spacer()
            }
            .frame(height: 20)
            .padding(.bottom, 12)
            
            // 步数柱状图
            HourlyBarChartView(
                data: hourlyStepData,
                maxValue: nil,
                barColor: Color(hex: "#1DA1F2"),
                showLabels: true,
                selectedIndex: $selectedHalfHourIndex // 传递选中索引的绑定
            )
            .frame(height: 200) // 增加图表高度，从180增加到200
            .padding(.horizontal, 2)
            .padding(.bottom, 10) // 减小底部间距
            .padding(.top, 4)
        }
        .frame(height: 280) // 增加整个卡片的高度，从260增加到280
        .background(Color(hex: "#10131A"))
        .cornerRadius(24)
    }
    
    // MARK: - 活动卡路里图表
    @State private var selectedCalorieHalfHourIndex: Int? = nil // 记录用户选中的卡路里半小时索引
    @State private var calorieGoal: Int = 50 // 卡路里目标
    
    private var activityCaloriesCard: some View {
        VStack(spacing: 0) {
            // 标题行
            HStack {
                Text("activity_detail_calories_card_title".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                if sharedDateViewModel.selectedDate.isInToday{
                    Text("Until: \(Date().string(withFormat: "hh:mm"))")
                        .font(.system(size: 13))
                        .foregroundColor(.gray)
                }
            }
            .padding(.top, 22) // 保持标题的上边距不变
            .padding(.horizontal, 22) // 保持标题的水平边距不变
            .padding(.bottom, 12)
            
            // 卡路里和时间段信息 - 根据是否选中柱子显示不同内容
                HStack {
                Spacer()
                
                if let selectedIndex = selectedCalorieHalfHourIndex {
                    // 显示选中时段的卡路里和时间范围
                    HStack(spacing: 2) {
                        // 显示时将原始卡路里值除以10000
                        let displayCalories = Double(hourlyCalorieData[selectedIndex]) / 10000.0
                        Text(String(format: "%.1f", displayCalories))
                            .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                    
                        Text("insight_unit_kcal".localized)
                            .font(.system(size: 10))
                            .foregroundColor(.gray.opacity(0.8))
                        
                        Text(" \(formatTimeRange(for: selectedIndex))")
                            .font(.system(size: 10))
                            .foregroundColor(.gray.opacity(0.8))
                    }
                    .padding(.horizontal, 10)
                    .padding(.vertical, 3)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color(hex: "#1A2029"))
                    )
                } else {
                    // 显示总卡路里和目标卡路里
                    let calories = activityData?.calories ?? 0
                    HStack(spacing: 2) {
                        Text("\(Int(calories))")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                        
                        Text("/ \(calorieGoal)")
                            .font(.system(size: 12))
                            .foregroundColor(.gray.opacity(0.8))
                        
                        Text("insight_unit_kcal".localized)
                            .font(.system(size: 10))
                            .foregroundColor(.gray.opacity(0.8))
                    }
                    .padding(.horizontal, 10)
                    .padding(.vertical, 3)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color(hex: "#1A2029"))
                    )
                }
                
                Spacer()
            }
            .frame(height: 20)
            .padding(.bottom, 12)
            
            // 卡路里柱状图
            HourlyBarChartView(
                data: hourlyCalorieData,
                maxValue: nil,
                barColor: Color.purple,
                showLabels: true,
                selectedIndex: $selectedCalorieHalfHourIndex // 传递选中索引的绑定
            )
            .frame(height: 200) // 增加图表高度，从180增加到200
            .padding(.horizontal, 2)
            .padding(.bottom, 10) // 减小底部间距
            .padding(.top, 4)
        }
        .frame(height: 280) // 增加整个卡片的高度，从260增加到280，与步数卡片保持一致
        .background(Color(hex: "#10131A"))
        .cornerRadius(24)
    }
    
    // MARK: - 全天卡路里消耗分析
    @State var selectedActivityHalfHourIndex: Int? = nil
    
    private var allDayCaloriesCard: some View {
        VStack(spacing: 0) {
            // 标题行
            HStack {
                Text("activity_detail_intensity_card_title".localized)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
                
                if sharedDateViewModel.selectedDate.isInToday{
                    Text("Until: \(Date().string(withFormat: "hh:mm"))")
                        .font(.system(size: 13))
                        .foregroundColor(.gray)
                }
            }
            .padding(.top, 22)
            .padding(.horizontal, 21.5)
            .padding(.bottom, 10)
            
            // 活动状态信息 - 使用深色背景卡片
            VStack(spacing: 2) {
                if let selectedIndex = selectedActivityHalfHourIndex,
                   let selectedLevel = activityLevels.first(where: { $0.halfHourIndex == selectedIndex }) {
                    // 显示选中矩形的信息
                    Text(selectedLevel.level)
                        .font(.system(size: 15, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Text(formatTimeRange(for: selectedIndex))
                        .font(.system(size: 13))
                        .foregroundColor(.white)
                } else {
                    // 默认状态显示 - 现在使用API数据来确定平均活动级别
                    let defaultLevel = getAverageActivityLevel()
                    Text(defaultLevel)
                        .font(.system(size: 15, weight: .semibold))
                        .foregroundColor(.white)
                
                    Text("activity_detail_intensity_average".localized)
                        .font(.system(size: 13))
                        .foregroundColor(.white)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 10)
            .background(
                Color(hex: "#52F9BF")
                    .opacity(0.16)  // 不透明度16%
            )
            .cornerRadius(10)  // 圆角10pt
            .padding(.horizontal, 12)
            .padding(.top, 4)
            .padding(.bottom, 14)
            .animation(.easeInOut(duration: 0.2), value: selectedActivityHalfHourIndex)
            
            // 活动状态数据加载中或错误状态
            if isLoadingActivityStates {
                VStack {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                    Text("activity_detail_loading_intensity".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                        .padding(.top, 10)
                }
                .frame(height: 180)
                .frame(maxWidth: .infinity)
            } else {
                // 活动水平热力图 - 使用CustomActivityHeatMapView
                CustomActivityHeatMapView(
                    activityLevels: activityLevels,
                    selectedHalfHourIndex: $selectedActivityHalfHourIndex
                )
                .frame(height: 180)
                .padding(.horizontal, 16)
                .padding(.bottom, 20)
            }
        }
        .background(Color(hex: "#10131A"))
        .cornerRadius(24)
    }
    
    // 获取平均活动级别
    private func getAverageActivityLevel() -> String {
        // 如果有API数据，计算真实的平均活动级别
        if let title = activityAvgStateData?.title,title.count > 0 {
            switch title {
            case "Vigorous":
                return "activity_intensity_vigorous".localized
            case "Moderate":
                return "activity_intensity_moderate".localized
            case "Low":
                return "activity_intensity_low".localized
            case "Inactive":
                return "activity_intensity_inactive".localized
            default:
                return "activity_intensity_inactive".localized
            }
        }

        
        return "activity_intensity_inactive".localized
//        if !activityLevels.isEmpty {
//            // 只计算6:00-22:00之间的活动（醒着的时间）
//            let awakeStates = activityLevels.filter { state in
//                let halfHourIndex = state.halfHourIndex
//                let hour = halfHourIndex / 2
//                return hour >= 6 && hour <= 22
//            }
//            
//            if !awakeStates.isEmpty {
//                // 统计各级别活动的数量
//                var levelCounts: [String: Int] = [
//                    "Inactive": 0,
//                    "Low": 0,
//                    "Moderate": 0,
//                    "Vigorous": 0
//                ]
//                
//                for state in awakeStates {
//                    levelCounts[state.level, default: 0] += 1
//                }
//                
//                // 找出出现次数最多的活动级别
//                if let (mostCommonLevel, _) = levelCounts.max(by: { $0.value < $1.value }),mostCommonLevel.count == 0 {
//                    return mostCommonLevel.localized
//                }else{
//                    return "activity_intensity_inactive".localized
//                }
//            }else{
//                return "activity_intensity_inactive".localized
//            }
//        }
//        
//        // 默认返回 Inactive
//        return "activity_intensity_inactive".localized
    }
    
    // 加载活动状态数据 - 单独加载活动状态的方法，用于重试功能
    private func loadActivityStates(for date: Date, forceRefresh: Bool = false) {
        isLoadingActivityStates = true
        
        // 判断是否为当天
        let calendar = Calendar.current
        let isToday = calendar.isDateInToday(date)
        
        // 如果是当天且有步数数据，根据步数计算活动强度
//        if isToday && !activityDetails.isEmpty {
//            self.activityLevels = calculateActivityLevelsFromSteps(activityDetails)
//            self.isLoadingActivityStates = false
//            print("根据当天步数数据计算活动强度，共\(self.activityLevels.count)条记录")
//            return
//        }
        
        // 如果不是当天或者没有步数数据，从服务器获取活动强度数据
        ActivityService.shared.getActivityStates(for: date, forceRefresh: forceRefresh)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoadingActivityStates = false
                
                switch completion {
                case .failure(let error):
                    // 不设置错误信息，只打印错误日志
                    print("获取活动状态数据失败: \(error.localizedDescription)")
                    
                    // 尝试使用步数数据计算
                    if !self.activityDetails.isEmpty {
                        print("使用步数数据计算活动强度")
                        self.activityLevels = self.calculateActivityLevelsFromSteps(self.activityDetails)
                    } else {
                        // 没有步数数据时，设置所有时间段为Inactive而不是使用随机数据
                        print("没有步数数据，将所有活动强度设为Inactive")
                        self.activityLevels = self.createDefaultInactiveActivityLevels()
                    }
                case .finished:
                    break
                }
            }, receiveValue: { stateData in
                if !stateData.isEmpty {
                    self.activityLevels = stateData.map { state in
                        let localizedTitle: String
                        switch state.title {
                        case "Vigorous":
                            localizedTitle = "activity_intensity_vigorous".localized
                        case "Moderate":
                            localizedTitle = "activity_intensity_moderate".localized
                        case "Low":
                            localizedTitle = "activity_intensity_low".localized
                        case "Inactive":
                            localizedTitle = "activity_intensity_inactive".localized
                        default:
                            localizedTitle = ""
                        }
                        return (level: localizedTitle, halfHourIndex: state.halfHourIndex)
                    }
                    print("成功获取活动状态数据 - 共\(stateData.count)条")
                } else {
                    print("活动状态数据为空")
                    
                    // 尝试使用步数数据计算
                    if !self.activityDetails.isEmpty {
                        print("使用步数数据计算活动强度")
                        self.activityLevels = self.calculateActivityLevelsFromSteps(self.activityDetails)
                    } else {
                        // 没有步数数据时，设置所有时间段为Inactive而不是使用随机数据
                        print("没有步数数据，将所有活动强度设为Inactive")
                        self.activityLevels = self.createDefaultInactiveActivityLevels()
                    }
                }
            })
            .store(in: &cancellables)
    }
    
    // 添加一个新函数，创建默认的全部Inactive活动级别数据，而不是随机数据
    private func createDefaultInactiveActivityLevels() -> [(level: String, halfHourIndex: Int)] {
        var defaultLevels: [(level: String, halfHourIndex: Int)] = []
        
        // 获取当前半小时索引
        let calendar = Calendar.current
        let now = Date()
        let currentHour = calendar.component(.hour, from: now)
        let currentMinute = calendar.component(.minute, from: now)
        let currentHalfHourIndex = currentHour * 2 + (currentMinute >= 30 ? 1 : 0)
        
        // 检查是否是今天的数据
        let isToday = calendar.isDateInToday(sharedDateViewModel.selectedDate)
        let maxIndex = isToday ? currentHalfHourIndex : 47
        
        // 为每个半小时创建Inactive状态
        for index in 0...maxIndex {
            defaultLevels.append((level: "Inactive", halfHourIndex: index))
        }
        
        return defaultLevels
    }
    
    // MARK: - 根据步数数据计算活动强度
    /// 根据步数数据计算活动强度级别
    private func calculateActivityLevelsFromSteps(_ details: [ActivityDetailData]) -> [(level: String, halfHourIndex: Int)] {
        var activityLevels: [(level: String, halfHourIndex: Int)] = []
        
        // 获取当前半小时索引
        let calendar = Calendar.current
        let now = Date()
        let currentHour = calendar.component(.hour, from: now)
        let currentMinute = calendar.component(.minute, from: now)
        let currentHalfHourIndex = currentHour * 2 + (currentMinute >= 30 ? 1 : 0)
        
        // 步数阈值定义 - 用于判断活动强度
        let inactiveThreshold = 20    // 0-20步：Inactive
        let lowThreshold = 120        // 21-120步：Low
        let moderateThreshold = 300   // 121-300步：Moderate
                                      // >300步：Vigorous
        
        // 准备48个半小时的步数数据
        var halfHourlySteps = Array(repeating: 0, count: 48)
        
        // 先将所有步数数据按半小时索引进行聚合
        for detail in details {
            if let index = detail.halfHourIndex, index >= 0 && index < 48 {
                halfHourlySteps[index] += detail.steps
            }
        }
        
        // 根据步数判断活动强度，只处理当前时间之前的数据
        for (index, steps) in halfHourlySteps.enumerated() {
            // 检查日期是否为今天，如果是今天则只处理到当前半小时
            let isToday = calendar.isDateInToday(sharedDateViewModel.selectedDate)
            let shouldIncludeIndex = !isToday || index <= currentHalfHourIndex
            
            if shouldIncludeIndex {
                let level: String
                
                if steps <= inactiveThreshold {
                    level = "activity_intensity_inactive".localized
                } else if steps <= lowThreshold {
                    level = "activity_intensity_low".localized
                } else if steps <= moderateThreshold {
                    level = "activity_intensity_moderate".localized
                } else {
                    level = "activity_intensity_vigorous".localized
                }
                
                activityLevels.append((level: level, halfHourIndex: index))
            }
        }
        
        return activityLevels
    }
    
    // 格式化时间段
    private func formatTimeRange(for halfHourIndex: Int) -> String {
        let startHour = halfHourIndex / 2
        let startMinute = (halfHourIndex % 2) * 30
        let endHour = (halfHourIndex + 1) / 2
        let endMinute = ((halfHourIndex + 1) % 2) * 30
        
        let startTimeString = String(format: "%02d:%02d", startHour, startMinute)
        let endTimeString = String(format: "%02d:%02d", endHour, endMinute)
        
        return "\(startTimeString)-\(endTimeString)"
    }
    
    // 关闭日历选择器
    private func closeCalendarPicker() {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                showCalendarPicker = false
        }
    }
    
    // MARK: - 活动明细列表卡片
    private var activityDetailListCard: some View {
        VStack(spacing: 0) {
            // 标题行
            HStack {
                Text("activity_detail_details_card_title".localized)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
                
//                Text("Until: \(untilTime)")
//                    .font(.system(size: 13))
//                    .foregroundColor(.gray)
            }
            .padding(.top, 22)
            .padding(.horizontal, 21.5)
            .padding(.bottom, 16)
            
            // 活动明细数据加载中或错误状态
            if isLoadingActivityDetails {
                VStack {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                    Text("activity_detail_loading_details".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                        .padding(.top, 10)
                }
                .frame(height: 120)
                .frame(maxWidth: .infinity)
            } else if let error = activityDetailsError {
                VStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle")
                        .foregroundColor(.orange)
                        .font(.system(size: 24))
                    
                    Text("activity_detail_error_loading_details".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text(error)
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    Button(action: {
                        // 重试加载活动明细
                        loadActivityDetails(for: sharedDateViewModel.selectedDate, forceRefresh: true)
                    }) {
                        Text("activity_detail_retry_button".localized)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 6)
                            .background(Color.blue)
                            .cornerRadius(8)
                    }
                    .padding(.top, 8)
                }
                .frame(height: 160)
                .frame(maxWidth: .infinity)
            } else if activityDetails.isEmpty {
                // 没有数据的情况
                VStack(spacing: 12) {
                    Image(systemName: "chart.bar.xaxis")
                        .font(.system(size: 32))
                        .foregroundColor(.gray.opacity(0.7))
                    
                    Text("activity_detail_no_details_title".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text("activity_detail_no_details_message".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                }
                .frame(height: 120)
                .padding(.vertical, 20)
            } else {
                // 有数据时显示活动明细列表
                ScrollView {
                    VStack(spacing: 0) {
                        // 列表标题行
                        HStack {
                            Text("activity_detail_details_header_time".localized)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.gray)
                                .frame(width: 70, alignment: .leading)
                            
                            Spacer()
                            
                            Text("activity_detail_details_header_steps".localized)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.gray)
                                .frame(width: 60, alignment: .trailing)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        
                        Divider()
                            .background(Color.gray.opacity(0.2))
                        
                        // 数据行
                        ForEach(activityDetails.sorted {
                            // 按时间排序
                            guard let date1 = $0.date, let date2 = $1.date else { return false }
                            return date1 < date2
                        }) { detail in
                            VStack(spacing: 0) {
                                HStack(alignment: .center) {
                                    Text(detail.formattedTime())
                                        .font(.system(size: 15))
                                        .foregroundColor(.white)
                                        .frame(width: 70, alignment: .leading)
                                    
                                    Spacer()
                                    
                                    Text("\(detail.steps)")
                                        .font(.system(size: 15, weight: .medium))
                                        .foregroundColor(.white)
                                        .frame(width: 60, alignment: .trailing)
                                }
                                .padding(.horizontal, 20)
                                .padding(.vertical, 12)
                                
                                Divider()
                                    .background(Color.gray.opacity(0.1))
                            }
                        }
                    }
                }
                .frame(height: min(CGFloat(activityDetails.count) * 44 + 44, 300))
            }
        }
        .background(Color(hex: "#10131A"))
        .cornerRadius(24)
        .padding(.bottom, 10)
    }
    
    // 加载活动明细数据 - 单独加载活动明细的方法，用于重试功能
    private func loadActivityDetails(for date: Date, forceRefresh: Bool = false) {
        isLoadingActivityDetails = true
        // 不再设置错误状态
        
        ActivityService.shared.getActivityDetails(for: date, forceRefresh: forceRefresh)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoadingActivityDetails = false
                
                switch completion {
                case .failure(let error):
                    // 不设置错误信息，只打印错误日志
                    print("获取活动明细数据失败: \(error.localizedDescription)")
                    self.activityDetails = []
                case .finished:
                    break
                }
            }, receiveValue: { detailDataArray in
                if detailDataArray.isEmpty {
                    print("活动明细数据为空")
                    self.activityDetails = []
                } else {
                    print("成功获取活动明细数据 - 共\(detailDataArray.count)条")
                    self.activityDetails = detailDataArray
                    
                    // 更新图表数据
                    self.updateHourlyDataFromDetails(detailDataArray)
                    
                    // 打印部分数据用于调试
                    let sampleCount = min(3, detailDataArray.count)
                    for (index, detail) in detailDataArray.prefix(sampleCount).enumerated() {
                        print("活动明细数据样本[\(index+1)]: 时间=\(detail.formattedTime()), 步数=\(detail.steps)")
                    }
                }
                
                // 在明细数据获取完成后加载活动状态数据
                self.loadActivityStates(for: date, forceRefresh: forceRefresh)
            })
            .store(in: &cancellables)
    }
    
    // 生成模拟卡路里数据
    private func generateMockHourlyCalorieData() {
        // 不再生成模拟数据，只使用全部为0的数组
        let emptyData = Array(repeating: 0, count: 48)
        
        // 更新UI数据
        DispatchQueue.main.async {
            // 更新小时数据图表为空数据
            self.hourlyCalorieData = emptyData
            
            // 记录日志
            print("使用空卡路里数据代替模拟数据")
        }
    }
    
    // MARK: - 辅助视图和计算属性
    
    // 加载中覆盖层视图
    private var loadingOverlay: some View {
        Color.black.opacity(0.6)
            .cornerRadius(24)
            .overlay(
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)
            )
    }
    
    // MARK: - 蓝牙相关函数
    
    // 蓝牙按钮点击逻辑
    private func handleBluetoothButtonTap() {
        switch bluetoothState {
        case .disconnected:
            // 断开状态下点击，尝试连接之前的设备
            connectLastDevice()
        case .connecting:
            // 连接中状态下点击，取消连接
            cancelConnecting()
        case .connected:
            // 已连接状态下点击，显示断开连接提示
            isShowingBluetoothAlert = true
        }
    }
    
    // 尝试连接上次的设备
    private func connectLastDevice() {
        bluetoothState = .connecting
        shouldCancelConnection = false
        
        print("尝试连接设备...")
        
        // 首先检查设备服务是否有上次连接的设备
        if let lastMac = deviceService.lastConnectedDeviceMAC, !lastMac.isEmpty {
            print("尝试连接上次设备: \(lastMac)")
            
            // 先断开当前连接（如果有）
            if deviceService.connectionState.isConnected {
                deviceService.disconnectDevice()
            }
            
            // 开始扫描
            deviceService.startScan(duration: 10)
            
            // 设置10秒超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [self] in
                guard bluetoothState == .connecting else { return }
                
                // 检查是否已连接
                if !deviceService.connectionState.isConnected && !shouldCancelConnection {
                    bluetoothState = .disconnected
                    print("连接超时")
                }
            }
            
            // 尝试连接
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [self] in
                guard !shouldCancelConnection else { return }
                
                if let discovery = deviceService.discoveredDevices.first(where: {
                    $0.mac?.uppercased() == lastMac.uppercased()
                }) {
                    print("找到之前设备，开始连接")
                    deviceService.connectDevice(discovery: discovery)
                    
                    // 连接成功后记录设备名称，用于显示提示
                    if let deviceName = discovery.localName, !deviceName.isEmpty {
                        connectedDeviceName = deviceName
                    } else {
                        connectedDeviceName = "设备"
                    }
                }
            }
        } else {
            print("无之前连接的设备记录")
            
            // 开始扫描，连接任何发现的设备
            deviceService.startScan(duration: 5)
            
            // 设置扫描超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 6) { [self] in
                guard bluetoothState == .connecting else { return }
                
                if !deviceService.discoveredDevices.isEmpty && !shouldCancelConnection {
                    let firstDevice = deviceService.discoveredDevices.first!
                    print("尝试连接发现的设备: \(firstDevice.localName ?? "未知设备")")
                    deviceService.connectDevice(discovery: firstDevice)
                    
                    // 连接超时
                    DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [self] in
                        guard bluetoothState == .connecting else { return }
                        
                        if !deviceService.connectionState.isConnected && !shouldCancelConnection {
                            bluetoothState = .disconnected
                            print("连接超时")
                        }
                    }
                } else {
                    // 没有发现设备
                    bluetoothState = .disconnected
                    print("未发现设备")
                }
            }
        }
    }
    
    // 取消连接过程
    private func cancelConnecting() {
        shouldCancelConnection = true
        bluetoothState = .disconnected
        
        // 取消设备服务的连接操作
        if deviceService.connectionState == .connecting {
            deviceService.disconnectDevice()
        }
        
        if deviceService.isScanning {
            deviceService.stopScan()
        }
    }
    
    // 断开蓝牙连接
    private func disconnectBluetooth() {
        // 执行断开连接逻辑
        bluetoothState = .disconnected
        deviceService.disconnectDevice()
    }
    
    // 开始定期检查连接状态
    private func startConnectionStateChecking() {
        // 取消可能存在的计时器
        connectionCheckTimer?.cancel()
        
        // 创建新计时器，每1秒检查一次
        connectionCheckTimer = Timer.publish(every: 1, on: .main, in: .common)
            .autoconnect()
            .sink { [self] _ in
                syncBluetoothState()
                
                // 如果设备已连接但UI未显示连接状态，立即更新
                if deviceService.connectionState == .connected && bluetoothState != .connected {
                    bluetoothState = .connected
                    print("发现设备已连接，更新UI状态")
                    
                    // 显示连接成功提示
                    if !showConnectedToast {
                        if let deviceName = deviceService.currentDiscovery?.localName, !deviceName.isEmpty {
                            connectedDeviceName = deviceName
                        } else {
                            connectedDeviceName = "设备"
                        }
                        showConnectedToast = true
                        
                        // 3秒后隐藏提示
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                            withAnimation {
                                showConnectedToast = false
                            }
                        }
                    }
                }
                
                // 如果设备已断开但UI仍显示连接状态，立即更新
                if deviceService.connectionState != .connected && bluetoothState == .connected {
                    bluetoothState = .disconnected
                    print("发现设备已断开，更新UI状态")
                }
            }
    }
    
    // 同步蓝牙状态
    private func syncBluetoothState() {
        let previousState = bluetoothState
        
        // 先检查设备服务的连接状态
        if deviceService.connectionState == .connected {
            // 如果设备已连接，直接更新UI状态
            if bluetoothState != .connected {
                bluetoothState = .connected
                
                // 如果状态从非连接变为连接，更新设备名称但不显示提示
                if previousState != .connected {
                    connectedDeviceName = deviceService.currentDiscovery?.localName ?? "设备"
                }
            }
            return
        }
        
        // 连接中状态的特殊处理
        if bluetoothState == .connecting {
            if deviceService.connectionState == .connected {
                bluetoothState = .connected
            } else if deviceService.connectionState == .disconnected {
                if !shouldCancelConnection {
                    bluetoothState = .disconnected
                }
            }
        } else {
            // 常规状态同步
            if deviceService.connectionState == .connected && bluetoothState != .connected {
                bluetoothState = .connected
            } else if deviceService.connectionState != .connected && bluetoothState == .connected {
                bluetoothState = .disconnected
            }
        }
    }
    
    /// 更新自动上传设置
    private func updateAutoUploadSetting(enabled: Bool) {
        if enabled {
            // 启用自动上传
            let success = RawDataUploadService.shared.startAutoUpload(interval: 300) // 5分钟
            if !success {
                // 如果启动失败，在1秒后重试
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                    let retrySuccess = RawDataUploadService.shared.startAutoUpload(interval: 300)
                    if !retrySuccess {
                        // 如果重试仍然失败，5秒后再次尝试
                        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                            _ = RawDataUploadService.shared.startAutoUpload(interval: 300)
                            // 无论成功与否，都保持状态为开启
                            self.autoUploadEnabled = true
                        }
                    }
                }
            }
        } else {
            // 禁用自动上传 - 由于我们希望保持开启，这部分代码不会被执行
            // RawDataUploadService.shared.stopAutoUpload()
            
            // 强制重新启用自动上传
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.autoUploadEnabled = true
                _ = RawDataUploadService.shared.startAutoUpload(interval: 300)
            }
        }
    }
    
    /// 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter.string(from: date)
    }
    
    // MARK: - 设备状态部分
    private var deviceStatusSection: some View {
        HStack {
            // 设备连接状态图标
            Image(systemName: bluetoothState == .connected ? "checkmark.circle.fill" : "exclamationmark.circle.fill")
                .foregroundColor(bluetoothState == .connected ? .green : .orange)
                .font(.system(size: 14))
            
            // 设备连接状态文本
            Text(bluetoothState == .connected ? String(format: "activity_detail_device_status_connected".localized, connectedDeviceName) : "activity_detail_device_status_unconnected".localized)
                .font(.subheadline)
                .foregroundColor(bluetoothState == .connected ? .green : .orange)
            
            Spacer()
            
            // 连接状态指示器
            if bluetoothState == .connecting {
                HStack(spacing: 4) {
                    Text("activity_detail_device_status_connecting".localized)
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                        .scaleEffect(0.7)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
        .padding(.horizontal, 16)
    }
    
    // MARK: - 基于步数预估活动时间
    private func estimateActivityTimeFromSteps(_ time: Int) -> (hours: Int, minutes: Int) {
        // 预估活动时间 - 假设平均每分钟走120步
        // 普通人每分钟约走80-120步，跑步每分钟约150-200步
        // 使用每分钟120步作为保守估计，与MainInsightView保持一致
//        let averageStepsPerMinute: Double = 120.0
        
        // 计算总分钟数
//        let totalMinutes = Int(Double(steps) / averageStepsPerMinute)
        
        // 转换为小时和分钟
        let hours = time / 60
        let minutes = time % 60
        
        return (hours, minutes)
    }
    
    // 开始监听实时步数数据的代理方法
    private func startListeningRealTimeStepsData() {
        // 确保dataManager已初始化
//        if dataManager == nil {
//            dataManager = ActivityDataManager(wrapper: ActivityDetailViewWrapper(delegate: self))
//        }
        
        // 调用dataManager的方法
//        dataManager?.startListeningRealTimeStepsData()
    }
    
    // 停止监听实时步数数据的代理方法
    private func stopListeningRealTimeStepsData() {
        // 调用dataManager的方法
//        dataManager?.stopListeningRealTimeStepsData()
    }
    
    
    // 根据步数更新其他活动数据
    func updateActivityDataBasedOnSteps(_ steps: Int) {
        // 计算卡路里 (简单估算：每25步消耗1卡路里)
//        let calories = activityData?.calories.description.double() ?? 0.0
//        let estimatedCalories = Double(steps) / 25.0
//        if estimatedCalories > calories {
//            activityData?.calories = Int(estimatedCalories)
//            self.isRealCaloriesData = true
//        }
        
        // 计算距离 (简单估算：每步0.7米)
//        let estimatedDistance = Double(steps) * 0.7 / 1000.0 // 转换为公里
//        let formattedEstimatedDistanceStr = String(format: "%.2f", estimatedDistance)
//        let formattedEstimatedDistance = Double(formattedEstimatedDistanceStr) ?? estimatedDistance
//        if formattedEstimatedDistance > self.distance {
//            self.distance = formattedEstimatedDistance
//            self.formattedDistanceString = formattedEstimatedDistanceStr // 更新格式化字符串
//            self.isRealDistanceData = true
//        }
//        
//        // 根据步数估算活动时间
//        let estimatedTime = estimateActivityTimeFromSteps(steps)
//        if estimatedTime.hours > self.hours ||
//           (estimatedTime.hours == self.hours && estimatedTime.minutes > self.minutes) {
//            self.hours = estimatedTime.hours
//            self.minutes = estimatedTime.minutes
//            self.isRealTimeData = true
//        }
    }
    
    // 根据卡路里更新活动数据
    func updateActivityDataBasedOnCalories(_ caloriesValue: Int) {
        // 显示时将原始卡路里值除以10000并取整
//        self.calories = Double(Int(Double(caloriesValue) / 10000.0))
        self.isRealCaloriesData = true
        
//        print("已更新活动卡路里为设备实时值: \(caloriesValue) 原始值, 显示值: \(self.calories) kcal")
    }
} // 闭合ActivityDetailView结构体

// MARK: - 辅助视图
// 活动弧形视图（半圆进度指示器）
struct ActivityArcView: View {
    let progress: Double  // 0.0-1.0
    let thickness: CGFloat
    let color: Color
    let radius: CGFloat
    
    var body: some View {
        ZStack {
            // 背景弧
            Circle()
                .trim(from: 0.0, to: 0.5)
                .stroke(
                    color.opacity(0.15),
                    style: StrokeStyle(lineWidth: 28, lineCap: .round)
                )
                .rotationEffect(.degrees(180))
                .frame(width: radius, height: radius)
            
            // 进度弧
            Circle()
                .trim(from: 0.0, to: progress * 0.5)
                .stroke(
                    color,
                    style: StrokeStyle(lineWidth: thickness, lineCap: .round)
                )
                .rotationEffect(.degrees(180))
                .frame(width: radius, height: radius)
        }
    }
}

// 活动数据卡片视图
struct ActivityDataCardView: View {
    let icon: String
    let iconColor: Color
    let value: String
    let unit: String
    var isRealData: Bool = false // 添加控制是否为真实数据的属性
    var isCaloriesCard: Bool = false // 标记是否为卡路里卡片
    
    var body: some View {
        HStack(spacing: 10) {
            // 图标
            Image(icon)
                .foregroundColor(iconColor)
            
            // 数值与单位在同一行
            HStack(alignment: .bottom, spacing: 4) {
                // 特殊处理卡路里卡片 - 如果是卡路里且值为"0.0"但isRealData为true，显示"--"表示加载中
                Text(isCaloriesCard && value == "0.0" && isRealData ? "--" : value)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(value == "--" ? .white : .white)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
                
                if !unit.isEmpty {
                    Text(unit)
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                        .padding(.bottom, 2)
                }
            }
            
            Spacer(minLength: 0)
        }
        .padding(12)
        .frame(height: 62)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color(hex: "#141821"), Color(hex: "#2A3040")]),
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        .cornerRadius(10)
        .frame(maxWidth: .infinity)
    }
}

// 活动时间卡片视图 - 专门用于显示小时和分钟
struct ActivityTimeCardView: View {
    let icon: String
    let iconColor: Color
    let hours: Int
    let minutes: Int
    var isRealData: Bool = false // 添加控制是否为真实数据的属性
    var hasData: Bool = true // 添加控制是否有数据的属性
    var isEstimated: Bool = false // 添加标记是否为估算数据的属性
    
    var body: some View {
        HStack(spacing: 10) {
            // 图标
            Image(icon)
                .foregroundColor(iconColor)
            
            if hasData {
                // 小时和分钟显示
                VStack(alignment: .leading, spacing: 2) {
                    HStack(alignment: .bottom, spacing: 8) {
                        // 小时部分
                        if hours > 0 {
                            HStack(alignment: .bottom, spacing: 2) {
                                Text("\(hours)")
                                    .font(.system(size: 20, weight: .bold))
                                    .foregroundColor(isRealData ? .white : .white) // 实时数据显示紫色
                                
                                Text("hr".localized)
                                    .font(.system(size: 12))
                                    .foregroundColor(.gray)
                                    .padding(.bottom, 2)
                            }
                        }
                        
                        // 分钟部分
                        HStack(alignment: .bottom, spacing: 2) {
                            Text("\(minutes)")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(isRealData ? .white : .white) // 实时数据显示紫色
                            
                            Text("min".localized)
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .padding(.bottom, 2)
                        }
                    }
                    
                    // 删除"基于步数估算"标签代码
                }
            } else {
                // 没有数据时显示占位符
                HStack(alignment: .bottom, spacing: 4) {
                    Text("--")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                    
                    Text(hours > 0 ? "hr".localized : "min".localized)
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                        .padding(.bottom, 2)
                }
            }
            
            Spacer(minLength: 0)
        }
        .padding(12)
        .frame(height: 62)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color(hex: "#141821"), Color(hex: "#2A3040")]),
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        .cornerRadius(10)
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 小时柱状图视图
struct HourlyBarChartView: View {
    let data: [Int]
    let maxValue: Int? // 可选参数，如果为nil则根据数据自动计算最大值
    let barColor: Color
    let showLabels: Bool
    @Binding var selectedIndex: Int? // 用于跟踪用户选择的半小时索引
    
    // 内部状态用于控制是否正在按住
    @State private var isHolding: Bool = false
    // 用于脉动动画效果
    @State private var isPulsing: Bool = false
    
    // 计算实际使用的最大值
    private var effectiveMaxValue: Int {
        if let maxValue = maxValue {
            return maxValue
        } else {
            // 如果没有提供最大值，则计算数据中的最大值并向上取整
            let maxDataValue = data.max() ?? 0
            
            // 根据最大值确定一个恰当的刻度范围
            if maxDataValue <= 100 {
                return (maxDataValue / 50 + 1) * 50 // 50的倍数
            } else if maxDataValue <= 500 {
                return (maxDataValue / 100 + 1) * 100 // 100的倍数
            } else if maxDataValue <= 1000 {
                return (maxDataValue / 250 + 1) * 250 // 250的倍数
            } else if maxDataValue <= 2000 {
                return (maxDataValue / 500 + 1) * 500 // 500的倍数
            } else {
                return (maxDataValue / 1000 + 1) * 1000 // 1000的倍数
            }
        }
    }
    
    // 获取分段值（最大值，中间值，最小值）
    private var yAxisValues: [Int] {
        let maxYValue = effectiveMaxValue
        return [maxYValue, maxYValue / 2, 0]
    }
    
    // 震动反馈功能
    private func triggerHapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.prepare()
        generator.impactOccurred()
    }
    
    var body: some View {
        GeometryReader { geometry in
            // Define slotWidth based on chartGeometry.size.width available after ZStack padding
            let slotWidth = (geometry.size.width - 40) / 48 // Assuming chartGeometry.size.width will be geometry.size.width

            VStack(spacing: 0) {
                // 主图表区域
                ZStack(alignment: .leading) {
                    // 水平参考线
                    VStack(spacing: 0) {
                        ForEach(yAxisValues.indices, id: \.self) { index in
                            if index > 0 { Spacer() }
                            
                            // 水平参考线 - 0刻度为实线，其他为虚线
                            Rectangle()
                                .fill(Color.gray.opacity(0.15))
                                .frame(height: 1)
                                // 如果是最下方的线(0刻度)是实线，上面的是虚线
                                .opacity(index == 2 ? 1.0 : 0.0) // 隐藏虚线位置的实线
                            
                            // 添加虚线(除了0刻度)
                            if index < 2 {
                                DashedLine()
                                    .stroke(Color.gray.opacity(0.15), style: StrokeStyle(lineWidth: 1, dash: [3, 3]))
                                    .frame(height: 1)
                            }
                        }
                    }
                    .padding(.bottom, 25) // 为时间轴留出空间 - MODIFIED FROM 40 to 25
                    .padding(.leading, 20) // 保持左侧内边距与水平刻度对齐
                    .padding(.trailing, 60) // 右侧Y轴标签空间
                    
                    // 柱状图和时间轴
                    VStack(spacing: 0) {
                        // 柱状图主体
                        GeometryReader { chartGeometry in
                            ZStack(alignment: .bottom) {
                                // 背景区域用于捕获整个图表区域的手势
                                Rectangle()
                                    .fill(Color.clear)
                                    .contentShape(Rectangle())
                                    .gesture(
                                        DragGesture(minimumDistance: 0)
                                            .onChanged { value in
                                                // Calculate index based on touch position within the padded ZStack content area
                                                // slotWidth is now calculated based on the ZStack's content area for bars.
                                                // value.location.x is relative to the gesture rectangle,
                                                // which aligns with the bars' HStack within the padded ZStack.
                                                let touchXInBarsArea = value.location.x
                                                let calculatedIndex = Int(touchXInBarsArea / slotWidth)
                                                
                                                // Ensure index is within valid bounds
                                                let index = min(max(0, calculatedIndex), 47)

                                                    // 只有在刚开始按下或切换到不同柱子时才触发震动
                                                    if selectedIndex != index {
                                                        triggerHapticFeedback()
                                                        selectedIndex = index
                                                        isHolding = true
                                                        // 开始脉动动画
                                                        withAnimation(Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                                                            isPulsing = true
                                                    }
                                                }
                                            }
                                            .onEnded { _ in
                                                // 当用户松开手指时，延迟后取消选择
                                                isHolding = false
                                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                                    // 只有在不再按住的情况下才清除选择
                                                    if !isHolding {
                                                        selectedIndex = nil
                                                        // 停止脉动动画
                                                        withAnimation {
                                                            isPulsing = false
                                                        }
                                                    }
                                                }
                                            }
                                    )
                                
                                // 柱状图
                                HStack(alignment: .bottom, spacing: 0) {
                                    ForEach(0..<48) { halfHourIndex in
                                        VStack(spacing: 0) {
                                            // 移除Spacer，改为直接放置柱子
                                            
                                            ZStack(alignment: .bottom) {
                                                // 底层柱子
                                                if data[halfHourIndex] > 0 {
                                                    // 使用圆角上部矩形并确保底部平整
                                                    RoundedRectangle(cornerRadius: 1.5, style: .continuous)
                                                            .fill(barColor)
                                                            .frame(
                                                                width: max(slotWidth * 0.7, 2.5), // Use correct slotWidth
                                                            height: max(CGFloat(data[halfHourIndex]) / CGFloat(effectiveMaxValue) * chartGeometry.size.height, 3)
                                                        )
                                                        // 确保底部平整，只有顶部是圆角
                                                        .mask(
                                                            VStack(spacing: 0) {
                                                                RoundedRectangle(cornerRadius: 1.5)
                                                                    .frame(height: max(CGFloat(data[halfHourIndex]) / CGFloat(effectiveMaxValue) * chartGeometry.size.height - 1, 2))
                                                        Rectangle()
                                                                    .frame(height: 1)
                                                            }
                                                        )
                                                }
                                                
                                                // 选中状态高亮效果
                                                if selectedIndex == halfHourIndex && data[halfHourIndex] > 0 {
                                                    RoundedRectangle(cornerRadius: 1.5, style: .continuous)
                                                            .fill(Color.white.opacity(isPulsing ? 0.4 : 0.3))
                                                            .frame(
                                                                width: max(slotWidth * 0.7, 2.5), // Use correct slotWidth
                                                            height: max(CGFloat(data[halfHourIndex]) / CGFloat(effectiveMaxValue) * chartGeometry.size.height, 3)
                                                            )
                                                            .overlay(
                                                                RoundedRectangle(cornerRadius: 1.5)
                                                                    .stroke(Color.white, lineWidth: 1)
                                                            )
                                                            .shadow(color: barColor.opacity(0.8), radius: 3, x: 0, y: 0)
                                                            .scaleEffect(isPulsing ? 1.1 : 1.0)
                                                        // 确保底部平整，只有顶部是圆角
                                                        .mask(
                                                            VStack(spacing: 0) {
                                                                RoundedRectangle(cornerRadius: 1.5)
                                                                    .frame(height: max(CGFloat(data[halfHourIndex]) / CGFloat(effectiveMaxValue) * chartGeometry.size.height - 1, 2))
                                                        Rectangle()
                                                                    .frame(height: 1)
                                                            }
                                                        )
                                                }
                                            }
                                            .animation(.easeInOut(duration: 0.2), value: selectedIndex)
                                        }
                                        .frame(width: slotWidth) // Use correct slotWidth for the slot's frame
                                    }
                                }
                            }
                            .padding(.leading, 20) // 确保左侧对齐
                            .padding(.trailing, 20) // 确保右侧与时间轴对齐
                        }
                        .padding(.trailing, 60) // 右侧Y轴标签空间
                        .padding(.bottom, 0) // 确保没有底部额外的内边距
                        .frame(height: geometry.size.height - 25) // 留出时间轴空间 - MODIFIED FROM 40 to 25
                        
                        // 时间轴 - 重新设计，按照红色箭头位置对齐
                        GeometryReader { timeAxisGeometry in
                            ZStack(alignment: .leading) {
                                // 第一个时间标签（0:00）- 移到最左侧
                                Text("0:00")
                                    .font(.system(size: 10))
                                    .foregroundColor(.gray)
                                    .position(x: 10, y: 12.5) // 接近左边缘
                                
                                // 第二个时间标签（6:00）- 在0:00和24:00之间均匀分布
                                Text("6:00")
                                    .font(.system(size: 10))
                                    .foregroundColor(.gray)
                                    .position(x: 10 + (timeAxisGeometry.size.width - 20) * 0.25, y: 12.5)
                                
                                // 第三个时间标签（12:00）- 在0:00和24:00之间均匀分布
                                Text("12:00")
                                    .font(.system(size: 10))
                                    .foregroundColor(.gray)
                                    .position(x: 10 + (timeAxisGeometry.size.width - 20) * 0.5, y: 12.5)
                                
                                // 第四个时间标签（18:00）- 在0:00和24:00之间均匀分布
                                Text("18:00")
                                    .font(.system(size: 10))
                                    .foregroundColor(.gray)
                                    .position(x: 10 + (timeAxisGeometry.size.width - 20) * 0.75, y: 12.5)
                                
                                // 第五个时间标签（24:00）- 向左移动，确保完全显示在屏幕内
                                Text("24:00")
                                    .font(.system(size: 10))
                                    .foregroundColor(.gray)
                                    .position(x: timeAxisGeometry.size.width - 20, y: 12.5) // 更靠左一点，确保完全可见
                            }
                        }
                        .frame(height: 25)
                    }
                    
                    // Y轴刻度标签（放在最上层）
                    VStack(spacing: 0) {
                        ForEach(yAxisValues.indices, id: \.self) { index in
                            if index > 0 { Spacer() }
                            
                            HStack {
                                Spacer()
                                
                                // Y轴标签
                                if showLabels {
                                    Text("\(yAxisValues[index])")
                                        .font(.system(size: 11))
                                        .foregroundColor(.white)
                                        .frame(width: 45) // 保持标签宽度
                                        .padding(.trailing, 10)
                                        .offset(y: -8) // 保持向上偏移
                                }
                            }
                        }
                    }
                    .padding(.bottom, 25) // 与时间轴保持一致 - MODIFIED FROM 40 to 25
                    .padding(.trailing, 5) // 调整额外右侧内边距
                }
            }
        }
    }
}

// MARK: - 自定义活动热力图视图
struct CustomActivityHeatMapView: View {
    let activityLevels: [(level: String, halfHourIndex: Int)]
    @Binding var selectedHalfHourIndex: Int? // 用于跟踪用户选择的半小时索引
    
    // 活动级别行标签
    private var levelLabels: [String] {
        ["activity_intensity_vigorous".localized, "activity_intensity_moderate".localized, "activity_intensity_low".localized, "activity_intensity_inactive".localized]
    }
    
    // 内部状态用于控制是否正在按住
    @State private var isHolding: Bool = false
    // 用于脉动动画效果
    @State private var isPulsing: Bool = false
    
    // 活动水平颜色映射 - 按照效果图中的蓝绿色调整
    private func colorForLevel(_ level: String) -> Color {
        switch level {
        case "activity_intensity_inactive".localized: return Color(hex: "#00B7B0")  // 浅青色
        case "activity_intensity_low".localized: return Color(hex: "#00A6CE")       // 浅蓝色
        case "activity_intensity_moderate".localized: return Color(hex: "#0085CE")  // 中蓝色
        case "activity_intensity_vigorous".localized: return Color(hex: "#0062BB")  // 深蓝色
        default: return Color.gray
        }
    }
    
    // 震动反馈功能
    private func triggerHapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.prepare()
        generator.impactOccurred()
    }
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 热力图区域
                ZStack(alignment: .topLeading) {
                    // 网格背景
                    GridBackground()
                    
                    // 活动级别标签 - 使用精确定位以确保每个标签垂直居中于水平线之间
                    // 计算每个水平区域的高度
                    let sectionHeight = (geometry.size.height - 25) / 4
                    
                    // 为每个活动级别标签创建文本，并精确定位在水平线之间的中间
                    ForEach(0..<levelLabels.count, id: \.self) { index in
                        Text(levelLabels[index])
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                            .position(
                                x: 35, // 距离左侧固定距离
                                y: sectionHeight * (CGFloat(index) + 0.5) // 每个区间的中点
                            )
                    }
                    
                    // 手势区域 - 用于捕获整个区域的手势
                    Rectangle()
                        .fill(Color.clear)
                        .contentShape(Rectangle())
                        .gesture(
                            DragGesture(minimumDistance: 0)
                                .onChanged { value in
                                    // 计算当前触摸位置对应的半小时索引
                                    let timeAxisStart = geometry.size.width / 5
                                    let timeAxisEnd = geometry.size.width
                                    let timeAxisWidth = timeAxisEnd - timeAxisStart
                                    
                                    // 确保触摸点在有效区域内
                                    if value.location.x >= timeAxisStart && value.location.x <= timeAxisEnd {
                                        let touchX = value.location.x - timeAxisStart
                                        let halfHourWidth = timeAxisWidth / 48
                                        let index = Int(touchX / halfHourWidth)
                                        
                                        // 确保索引在有效范围内
                                        if index >= 0 && index < 48 {
                                            // 只有在索引发生变化时才触发震动
                                            if selectedHalfHourIndex != index {
                                                triggerHapticFeedback()
                                                selectedHalfHourIndex = index
                                                isHolding = true
                                                // 开始脉动动画
                                                withAnimation(Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                                                    isPulsing = true
                                                }
                                            }
                                        }
                                    }
                                }
                                .onEnded { _ in
                                    // 当用户松开手指时，延迟后取消选择
                                    isHolding = false
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                        // 只有在不再按住的情况下才清除选择
                                        if !isHolding {
                                            selectedHalfHourIndex = nil
                                            // 停止脉动动画
                                            withAnimation {
                                                isPulsing = false
                                            }
                                        }
                                    }
                                }
                        )
                    
                    // 活动块
                    ForEach(activityLevels.indices, id: \.self) { index in
                        let currentLevelTuple = activityLevels[index]
                        // 仅当活动级别是已知的标签时才渲染
                        if levelLabels.contains(currentLevelTuple.level) {
                            let isSelected = selectedHalfHourIndex == currentLevelTuple.halfHourIndex
                            ActivityBlock(
                                    level: currentLevelTuple,
                                colorForLevel: colorForLevel,
                                totalWidth: geometry.size.width,
                                totalHeight: geometry.size.height - 25, // 减去底部时间轴的高度
                                isSelected: isSelected,
                                isPulsing: isPulsing
                            )
                        }
                    }
                }
                .frame(height: geometry.size.height - 25)
                
                // 时间轴 - 重新设计，对齐网格
                GeometryReader { timeAxisGeometry in
                    ZStack(alignment: .leading) {
                        // 每个网格线的位置坐标参考 - 共6根竖线（第0到第5根）
                        ForEach(0..<6) { index in
                            Rectangle()
                                .fill(Color.clear) // 透明，仅用于参考位置
                                .frame(width: 1)
                                .position(x: CGFloat(index) * timeAxisGeometry.size.width / 5, y: 12.5)
                        }
                        
                        // 第一个时间标签（0:00）- 与第1根线对齐
                        Text("0:00")
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                            .position(x: timeAxisGeometry.size.width / 5, y: 12.5)
                        
                        // 第二个时间标签（6:00）- 与第2根线对齐
                        Text("6:00")
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                            .position(x: 2 * timeAxisGeometry.size.width / 5, y: 12.5)
                        
                        // 第三个时间标签（12:00）- 与第3根线对齐
                        Text("12:00")
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                            .position(x: 3 * timeAxisGeometry.size.width / 5, y: 12.5)
                        
                        // 第四个时间标签（18:00）- 与第4根线对齐
                        Text("18:00")
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                            .position(x: 4 * timeAxisGeometry.size.width / 5, y: 12.5)
                        
                        // 第五个时间标签（24:00）- 精确定位在红色箭头所指位置
                        Text("24:00")
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                            .position(x: timeAxisGeometry.size.width, y: 12.5)
                    }
                }
                .frame(height: 25)
            }
        }
    }
}

// 网格背景
struct GridBackground: View {
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 水平线
                ForEach(0..<5) { row in
                    Path { path in
                        path.move(to: CGPoint(x: 0, y: CGFloat(row) * geometry.size.height / 4))
                        path.addLine(to: CGPoint(x: geometry.size.width, y: CGFloat(row) * geometry.size.height / 4))
                    }
                    .stroke(Color.gray.opacity(0.15), lineWidth: 1)
                }
                
                // 添加特殊的0:00时间点竖直虚线
                    Path { path in
                    // 从最底部水平线到最顶部水平线的竖直虚线
                    path.move(to: CGPoint(x: geometry.size.width / 5, y: 0))
                    path.addLine(to: CGPoint(x: geometry.size.width / 5, y: geometry.size.height))
                }
                .stroke(style: StrokeStyle(lineWidth: 1, dash: [3, 3])) // 使用虚线样式
                .foregroundColor(Color.gray.opacity(0.15))
            }
        }
    }
}

// 活动块 - 修改为支持半小时粒度和交互
struct ActivityBlock: View {
    let level: (level: String, halfHourIndex: Int)
    let colorForLevel: (String) -> Color
    let totalWidth: CGFloat
    let totalHeight: CGFloat
    let isSelected: Bool
    let isPulsing: Bool
    
    var body: some View {
        let rowIndex: CGFloat
        
        switch level.level {
        case "activity_intensity_vigorous".localized: rowIndex = 0
        case "activity_intensity_moderate".localized: rowIndex = 1
        case "activity_intensity_low".localized: rowIndex = 2
        case "activity_intensity_inactive".localized: rowIndex = 3
        default: rowIndex = 4
        }
        
        // 计算半小时区块的位置
        let halfHourIndex = level.halfHourIndex
        
        // 计算有效时间区域的起始和结束点（对应0:00和24:00的刻度）
        let timeAxisStart = totalWidth / 5  // 0:00刻度位置
        let timeAxisEnd = totalWidth  // 24:00刻度位置（红色箭头位置）
        let timeAxisWidth = timeAxisEnd - timeAxisStart  // 有效时间区域宽度
        
        // 计算块在X轴上的位置，确保落在0:00和24:00之间
        let blockWidth = timeAxisWidth / 48 * 0.9  // 留出10%的间隙
        let xPosition = timeAxisStart + (CGFloat(halfHourIndex) + 0.5) * (timeAxisWidth / 48)
        
        // 计算行区域高度（每个活动级别的区域高度）
        let rowHeight = totalHeight / 4
        
        // 计算块在Y轴上的位置 - 居中显示在行区域中间
        let yPosition = rowIndex * rowHeight + (rowHeight / 2)
        
        return ZStack {
            // 底层矩形
            RoundedRectangle(cornerRadius: 3)
            .fill(colorForLevel(level.level))
            .frame(
                    width: blockWidth,
                    height: rowHeight
                )
                
            // 选中状态高亮效果
            if isSelected {
                RoundedRectangle(cornerRadius: 3)
                    .fill(Color.white.opacity(isPulsing ? 0.4 : 0.3))
                    .frame(
                        width: blockWidth,
                        height: rowHeight
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 3)
                            .stroke(Color.white, lineWidth: 1)
                    )
                    .shadow(color: colorForLevel(level.level).opacity(0.8), radius: 3, x: 0, y: 0)
                    .scaleEffect(isPulsing ? 1.1 : 1.0)
            }
        }
        .position(x: xPosition, y: yPosition)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// DashedLine结构体
struct DashedLine: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: 0, y: rect.midY))
        path.addLine(to: CGPoint(x: rect.width, y: rect.midY))
        return path
    }
}

// MARK: - 预览
struct ActivityDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ActivityDetailView()
        }
        .preferredColorScheme(.dark)
    }
}

// MARK: - 连接中状态子视图
