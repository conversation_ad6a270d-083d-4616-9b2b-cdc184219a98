import SwiftUI

/// 健康数据详情类型
enum HealthDetailType: String, CaseIterable {
    case sleep = "Sleep"
    case activity = "Activity"
    case heartRate = "Heart Rate"
    case stress = "Stress"
    case steps = "Steps"
    case calories = "Calories"
    
    var iconName: String {
        switch self {
        case .sleep:
            return "moon.zzz.fill"
        case .activity:
            return "figure.walk"
        case .heartRate:
            return "heart.fill"
        case .stress:
            return "brain.head.profile"
        case .steps:
            return "figure.walk"
        case .calories:
            return "flame.fill"
        }
    }
    
    var iconColor: Color {
        switch self {
        case .sleep:
            return .purple
        case .activity:
            return .blue
        case .heartRate:
            return .pink
        case .stress:
            return .orange
        case .steps:
            return .green
        case .calories:
            return .red
        }
    }
    
    var unit: String {
        switch self {
        case .sleep:
            return "hr"
        case .activity:
            return "score"
        case .heartRate:
            return "bpm"
        case .stress:
            return "score"
        case .steps:
            return "steps"
        case .calories:
            return "kcal"
        }
    }
}

/// 时间段枚举
enum TimePeriod: String, CaseIterable {
    case day = "Day"
    case week = "Week" 
    case month = "Month"
    case year = "Year"
    
    var title: String {
        return self.rawValue
    }
}

/// 健康数据详情视图
struct HealthDetailView: View {
    // MARK: - 属性
    let type: HealthDetailType
    @State private var selectedPeriod: TimePeriod = .week
    @State private var chartData: [ChartDataPoint] = []
    @Environment(\.presentationMode) var presentationMode
    
    // MARK: - 初始化
    init(type: HealthDetailType) {
        self.type = type
        _chartData = State(initialValue: generateMockData(for: type))
    }
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 顶部标题和时间选择器
                headerView
                
                // 数据概览卡片
                dataOverviewCard
                
                // 图表卡片
                chartCard
                
                // 数据记录列表
                dataRecordsList
                
                // 底部说明
                Text("Data Source: WindRing Smart Ring")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 8)
                    .padding(.bottom, 32)
            }
            .padding(.vertical)
        }
        .background(Color.appBackground.edgesIgnoringSafeArea(.all))
        .navigationBarHidden(true)
    }
    
    // MARK: - 顶部标题和时间选择器
    private var headerView: some View {
        VStack(spacing: 16) {
            // 标题和返回按钮
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                        .foregroundColor(.primary)
                }
                
                Text(type.rawValue)
                    .font(.title)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button(action: {
                    // 分享数据
                }) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.title2)
                        .foregroundColor(.primary)
                }
            }
            .padding(.horizontal)
            
            // 时间选择器
            HStack {
                ForEach(TimePeriod.allCases, id: \.self) { period in
                    Button(action: {
                        selectedPeriod = period
                        // 更新图表数据
                        chartData = generateMockData(for: type)
                    }) {
                        Text(period.title)
                            .font(.subheadline)
                            .fontWeight(selectedPeriod == period ? .bold : .regular)
                            .padding(.vertical, 8)
                            .padding(.horizontal, 12)
                            .background(
                                selectedPeriod == period ?
                                    type.iconColor.opacity(0.1) :
                                    Color.clear
                            )
                            .foregroundColor(selectedPeriod == period ? type.iconColor : .secondary)
                            .cornerRadius(8)
                    }
                }
            }
            .padding(.horizontal)
        }
    }
    
    // MARK: - 数据概览卡片
    private var dataOverviewCard: some View {
        HStack(spacing: 16) {
            // 图标
            ZStack {
                Circle()
                    .fill(type.iconColor.opacity(0.1))
                    .frame(width: 60, height: 60)
                
                Image(systemName: type.iconName)
                    .font(.title)
                    .foregroundColor(type.iconColor)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                // 标题
                Text("Average \(type.rawValue)")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                // 数值
                HStack(alignment: .lastTextBaseline, spacing: 4) {
                    Text(averageValue)
                        .font(.system(size: 28, weight: .bold, design: .rounded))
                    
                    Text(type.unit)
                        .font(.headline)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 趋势
            VStack(alignment: .trailing, spacing: 4) {
                Text("Compared to last \(selectedPeriod.title.lowercased())")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                HStack(spacing: 4) {
                    Image(systemName: "arrow.up")
                        .font(.caption)
                        .foregroundColor(.green)
                    
                    Text("+8%")
                        .font(.headline)
                        .foregroundColor(.green)
                }
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
    
    // MARK: - 图表卡片
    private var chartCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text("\(selectedPeriod.title) Trend")
                .font(.headline)
                .fontWeight(.semibold)
                .padding(.horizontal)
            
            // 图表
            HealthDetailChartView(data: chartData, color: type.iconColor)
                .frame(height: 200)
                .padding(.horizontal, 8)
            
            // 图例
            HStack(spacing: 16) {
                legendItem(color: type.iconColor, text: "Actual")
                
                if type != .steps && type != .calories {
                    legendItem(color: .gray.opacity(0.5), text: "Target")
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 8)
        }
        .padding(.vertical)
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
    
    // MARK: - 图例项
    private func legendItem(color: Color, text: String) -> some View {
        HStack(spacing: 8) {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - 数据记录列表
    private var dataRecordsList: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text("History")
                .font(.headline)
                .fontWeight(.semibold)
                .padding(.horizontal)
            
            // 列表
            VStack(spacing: 0) {
                ForEach(0..<5) { index in
                    dataRecordRow(
                        date: "2025/3/\(4-index)",
                        value: recordValue(for: index),
                        change: index % 2 == 0 ? "+3%" : "-2%",
                        isPositive: index % 2 == 0
                    )
                    
                    if index < 4 {
                        Divider()
                            .padding(.leading, 16)
                    }
                }
            }
            .background(Color.moduleBackground)
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        }
        .padding(.horizontal)
    }
    
    // MARK: - 数据记录行
    private func dataRecordRow(date: String, value: String, change: String, isPositive: Bool) -> some View {
        HStack {
            // 日期
            Text(date)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
            
            // 数值
            Text(value)
                .font(.headline)
                .foregroundColor(.primary)
            
            // 变化
            Text(change)
                .font(.caption)
                .foregroundColor(isPositive ? .green : .red)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    (isPositive ? Color.green : Color.red)
                        .opacity(0.1)
                )
                .cornerRadius(8)
        }
        .padding()
    }
    
    // MARK: - 辅助方法
    
    // 获取平均值
    private var averageValue: String {
        switch type {
        case .sleep:
            return "7.5"
        case .activity:
            return "82"
        case .heartRate:
            return "68"
        case .stress:
            return "72"
        case .steps:
            return "9,876"
        case .calories:
            return "2,345"
        }
    }
    
    // 获取记录值
    private func recordValue(for index: Int) -> String {
        switch type {
        case .sleep:
            let values = ["7.2", "7.8", "7.4", "7.6", "7.3"]
            return values[index] + " " + type.unit
        case .activity:
            let values = ["84", "80", "83", "81", "85"]
            return values[index] + " " + type.unit
        case .heartRate:
            let values = ["70", "67", "69", "66", "71"]
            return values[index] + " " + type.unit
        case .stress:
            let values = ["74", "70", "73", "71", "75"]
            return values[index] + " " + type.unit
        case .steps:
            let values = ["10,234", "9,876", "10,543", "9,654", "10,123"]
            return values[index] + " " + type.unit
        case .calories:
            let values = ["2,456", "2,345", "2,567", "2,234", "2,432"]
            return values[index] + " " + type.unit
        }
    }
    
    // 生成模拟数据
    private func generateMockData(for type: HealthDetailType) -> [ChartDataPoint] {
        let count: Int
        switch selectedPeriod {
        case .day:
            count = 24
        case .week:
            count = 7
        case .month:
            count = 30
        case .year:
            count = 12
        }
        
        var data: [ChartDataPoint] = []
        for i in 0..<count {
            let value = Double.random(in: 0.4...1.0)
            let target = Double.random(in: 0.6...0.9)
            let label = String(i + 1)
            data.append(ChartDataPoint(value: value, target: target, label: label))
        }
        return data
    }
}

// MARK: - 图表数据点
struct ChartDataPoint: Identifiable {
    let id = UUID()
    let value: Double
    let target: Double
    let label: String
}

// MARK: - 图表视图
struct HealthDetailChartView: View {
    let data: [ChartDataPoint]
    let color: Color
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 网格线
                VStack(spacing: 0) {
                    ForEach(0..<5) { i in
                        Divider()
                        Spacer()
                    }
                }
                
                // 目标线
                Path { path in
                    for (index, point) in data.enumerated() {
                        let x = geometry.size.width * CGFloat(index) / CGFloat(data.count - 1)
                        let y = geometry.size.height * (1 - CGFloat(point.target))
                        
                        if index == 0 {
                            path.move(to: CGPoint(x: x, y: y))
                        } else {
                            path.addLine(to: CGPoint(x: x, y: y))
                        }
                    }
                }
                .stroke(Color.gray.opacity(0.5), style: StrokeStyle(lineWidth: 2, dash: [5, 5]))
                
                // 数据线
                Path { path in
                    for (index, point) in data.enumerated() {
                        let x = geometry.size.width * CGFloat(index) / CGFloat(data.count - 1)
                        let y = geometry.size.height * (1 - CGFloat(point.value))
                        
                        if index == 0 {
                            path.move(to: CGPoint(x: x, y: y))
                        } else {
                            path.addLine(to: CGPoint(x: x, y: y))
                        }
                    }
                }
                .stroke(color, lineWidth: 3)
                
                // 数据点
                ForEach(data.indices, id: \.self) { index in
                    let point = data[index]
                    let x = geometry.size.width * CGFloat(index) / CGFloat(data.count - 1)
                    let y = geometry.size.height * (1 - CGFloat(point.value))
                    
                    Circle()
                        .fill(color)
                        .frame(width: 8, height: 8)
                        .position(x: x, y: y)
                }
                
                // X轴标签
                HStack(spacing: 0) {
                    ForEach(data.indices, id: \.self) { index in
                        if index % (data.count > 10 ? 4 : 1) == 0 {
                            Text(data[index].label)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .frame(width: geometry.size.width / CGFloat(data.count > 10 ? 4 : data.count))
                        } else {
                            Spacer()
                                .frame(width: geometry.size.width / CGFloat(data.count))
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .position(x: geometry.size.width / 2, y: geometry.size.height + 15)
            }
        }
    }
}

// MARK: - 预览
struct HealthDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            HealthDetailView(type: .sleep)
        }
    }
} 
