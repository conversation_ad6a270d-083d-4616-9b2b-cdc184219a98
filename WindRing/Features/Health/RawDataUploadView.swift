import SwiftUI
import Combine

struct RawDataUploadView: View {
    // MARK: - 服务
    private let rawDataService = RawDataUploadService.shared
    private let deviceService = WindRingDeviceService.shared
    private let authService = AuthService.shared
    private let autoDataSyncService = AutoDataSyncService.shared
    
    // MARK: - 状态
    @State private var isAutoUploadEnabled = false
    @State private var uploadInterval: Double = 600 // 默认10分钟
    @State private var lastUploadTime: Date? = nil
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var isUploading = false
    @State private var uploadProgress: Double = 0.0
    @State private var logs: [LogEntry] = []
    @State private var isIntervalPickerVisible = false
    
    // MARK: - 初始化
    private let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    private var intervalOptions: [Double] = [60, 300, 600, 1800, 3600, 7200]
    
    // MARK: - 视图体
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                headerView
                connectionStatusCard
                uploadControlCard
                uploadStatsCard
                logCard
            }
            .padding()
        }
        .navigationBarTitle("原始数据上传", displayMode: .inline)
        .onAppear {
            checkUploadStatus()
            addLog("视图已加载")
        }
        .onReceive(timer) { _ in
            // 如果正在上传，更新进度
            if isUploading {
                uploadProgress = rawDataService.uploadProgress
                if uploadProgress >= 1.0 {
                    isUploading = false
                }
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertMessage),
                dismissButton: .default(Text("确定"))
            )
        }
        .actionSheet(isPresented: $isIntervalPickerVisible) {
            ActionSheet(
                title: Text("选择上传间隔"),
                message: Text("自动上传将按照此间隔定期执行"),
                buttons: intervalOptions.map { interval in
                    .default(Text(formatInterval(interval))) {
                        uploadInterval = interval
                    }
                } + [.cancel()]
            )
        }
    }
    
    // MARK: - 子视图
    
    // 头部视图
    private var headerView: some View {
        VStack(spacing: 16) {
            Image(systemName: "arrow.up.doc")
                .font(.system(size: 60))
                .foregroundColor(.blue)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 2)
            
            Text("原始数据上传")
                .font(.title)
                .fontWeight(.bold)
            
            Text("实时上传戒指原始数据到云端")
                .font(.subheadline)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal)
        }
        .padding(.top, 20)
    }
    
    // 连接状态卡片
    private var connectionStatusCard: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "ring")
                    .font(.title2)
                    .foregroundColor(deviceService.connectionState.isConnected ? .green : .gray)
                
                Text("设备状态")
                    .font(.headline)
                
                Spacer()
                
                Text(deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized)
                    .font(.subheadline)
                    .foregroundColor(deviceService.connectionState.isConnected ? .green : .red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        deviceService.connectionState.isConnected ? 
                            Color.green.opacity(0.2) : Color.red.opacity(0.2)
                    )
                    .cornerRadius(4)
            }
            
            Divider()
            
            if deviceService.connectionState.isConnected {
                HStack {
                    Text("设备名称")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(deviceService.deviceInfo?.localName ?? "未知设备")
                        .font(.subheadline)
                }
                
                HStack {
                    Text("电池电量")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Image(systemName: getBatteryIcon())
                            .foregroundColor(getBatteryColor())
                        
                        Text("\(deviceService.batteryLevel)%")
                            .font(.subheadline)
                    }
                }
                
                HStack {
                    Text("固件版本")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(deviceService.deviceInfo?.firmwareVersion ?? "未知")
                        .font(.subheadline)
                }
            } else {
                Text("请先连接您的智能戒指设备")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // 上传控制卡片
    private var uploadControlCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("上传控制")
                .font(.headline)
            
            Divider()
            
            // 自动上传开关
            Toggle(isOn: $isAutoUploadEnabled.onChange(handleAutoUploadToggle)) {
                HStack {
                    Image(systemName: "clock.arrow.circlepath")
                    Text("自动上传")
                }
            }
            .disabled(!deviceService.connectionState.isConnected || (authService.currentToken == nil))
            
            if isAutoUploadEnabled {
                HStack {
                    Text("上传间隔")
                    Spacer()
                    Button(action: { isIntervalPickerVisible = true }) {
                        HStack {
                            Text(formatInterval(uploadInterval))
                            Image(systemName: "chevron.down")
                        }
                        .foregroundColor(.blue)
                    }
                }
                .padding(.leading, 24)
            }
            
            // 手动上传按钮
            Button(action: manualUpload) {
                HStack {
                    Image(systemName: "arrow.up.to.line")
                    Text("立即上传")
                        .fontWeight(.semibold)
                    
                    if isUploading {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(0.7)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(deviceService.connectionState.isConnected && (authService.currentToken == nil) ? Color.blue : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(!deviceService.connectionState.isConnected || !(authService.currentToken == nil) || isUploading)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // 上传统计卡片
    private var uploadStatsCard: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("上传统计")
                .font(.headline)
            
            Divider()
            
            HStack {
                Text("最后上传时间")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text(lastUploadTime != nil ? formatDate(lastUploadTime!) : "尚未上传")
                    .font(.subheadline)
            }
            
            if isUploading {
                VStack(alignment: .leading, spacing: 4) {
                    Text("上传进度")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    ProgressView(value: uploadProgress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle())
                    
                    Text("\(Int(uploadProgress * 100))%")
                        .font(.caption)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                }
                .padding(.top, 4)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // 日志卡片
    private var logCard: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("操作日志")
                    .font(.headline)
                
                Spacer()
                
                Button(action: clearLogs) {
                    Text("清除")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
            
            Divider()
            
            if logs.isEmpty {
                Text("暂无操作日志")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ForEach(logs) { log in
                    VStack(alignment: .leading, spacing: 2) {
                        Text(log.message)
                            .font(.subheadline)
                            .lineLimit(1)
                        
                        Text(formatDate(log.timestamp))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                    
                    if logs.last?.id != log.id {
                        Divider()
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
        .frame(minHeight: 200)
    }
    
    // MARK: - 方法
    
    // 处理自动上传开关切换
    private func handleAutoUploadToggle(newValue: Bool) {
        if newValue {
            // 启动自动上传
            let success = rawDataService.startAutoUpload(interval: uploadInterval)
            if success {
                addLog("已启动自动上传，间隔\(formatInterval(uploadInterval))")
            } else {
                isAutoUploadEnabled = false
                showAlert(title: "启动失败", message: "无法启动自动上传，请检查设备连接和登录状态")
                addLog("启动自动上传失败")
            }
        } else {
            // 停止自动上传
            rawDataService.stopAutoUpload()
            addLog("已停止自动上传")
        }
    }
    
    // 手动上传
    private func manualUpload() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "设备未连接", message: "请先连接您的智能戒指")
            return
        }
        
        guard (authService.currentToken == nil) else {
            showAlert(title: "未登录", message: "请先登录您的账号")
            return
        }
        
        isUploading = true
        uploadProgress = 0.0
        addLog("开始手动上传原始数据")
        
        rawDataService.uploadRawData { success, error in
            DispatchQueue.main.async {
                isUploading = false
                
                if success {
                    self.lastUploadTime = Date()
                    addLog("原始数据上传成功")
                    showAlert(title: "上传成功", message: "原始数据已成功上传到云端")
                } else if let error = error {
                    addLog("原始数据上传失败：\(error.localizedDescription)")
                    showAlert(title: "上传失败", message: error.localizedDescription)
                } else {
                    addLog("原始数据上传失败")
                    showAlert(title: "上传失败", message: "未知错误")
                }
            }
        }
    }
    
    // 检查上传状态
    private func checkUploadStatus() {
        isAutoUploadEnabled = rawDataService.isUploading
        lastUploadTime = rawDataService.lastUploadTime
        uploadProgress = rawDataService.uploadProgress
        isUploading = rawDataService.isUploading
    }
    
    // 添加日志
    private func addLog(_ message: String) {
        logs.insert(LogEntry(message: message), at: 0)
        if logs.count > 20 {
            logs.removeLast()
        }
    }
    
    // 清除日志
    private func clearLogs() {
        logs.removeAll()
    }
    
    // 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }
    
    // 格式化时间间隔
    private func formatInterval(_ seconds: Double) -> String {
        if seconds < 60 {
            return "\(Int(seconds))秒"
        } else if seconds < 3600 {
            return "\(Int(seconds / 60))分钟"
        } else if seconds < 86400 {
            return "\(Int(seconds / 3600))小时"
        } else {
            return "\(Int(seconds / 86400))天"
        }
    }
    
    // 获取电池图标
    private func getBatteryIcon() -> String {
        let level = deviceService.batteryLevel
        
        if level <= 10 {
            return "battery.0"
        } else if level <= 25 {
            return "battery.25"
        } else if level <= 50 {
            return "battery.50"
        } else if level <= 75 {
            return "battery.75"
        } else {
            return "battery.100"
        }
    }
    
    // 获取电池颜色
    private func getBatteryColor() -> Color {
        let level = deviceService.batteryLevel
        
        if level <= 20 {
            return .red
        } else if level <= 40 {
            return .orange
        } else {
            return .green
        }
    }
    
    // 显示提示框
    private func showAlert(title: String, message: String) {
        alertTitle = title
        alertMessage = message
        showAlert = true
    }
}

// MARK: - 辅助类型

// 日志条目
struct LogEntry: Identifiable {
    let id = UUID()
    let timestamp = Date()
    let message: String
}

// Toggle扩展，用于在值改变时调用回调
extension Binding {
    func onChange(_ handler: @escaping (Value) -> Void) -> Binding<Value> {
        Binding(
            get: { self.wrappedValue },
            set: { newValue in
                self.wrappedValue = newValue
                handler(newValue)
            }
        )
    }
}

// MARK: - 预览
struct RawDataUploadView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            RawDataUploadView()
        }
    }
} 
