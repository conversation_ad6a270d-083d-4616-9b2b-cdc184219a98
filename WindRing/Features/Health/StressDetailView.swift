import SwiftUI
import Charts
#if os(iOS)
import CRPSmartRing
import UIKit // 添加UIKit框架导入，用于提供震动反馈
#endif
import Combine
import HealthKit
// 导入包含StressScoreResponse和StressScoreData模型的文件
import Foundation
// 尝试导入我们创建的API模型文件
// 注意：如果models目录不在main bundle中，可能需要调整导入路径

/// 压力详情页面
struct StressDetailView: View {
    // MARK: - 属性
    // API基础URL
//    private let apiBaseURL = "http://ring-api-dev.weaving-park.com"
    // 状态变量
    @Environment(\.presentationMode) var presentationMode
    // 添加日期状态管理
    @State private var selectedDate = Date()
    @State private var showCalendarPicker: Bool = false
    @State private var selectedDay: Int = 0 // 当前选中的日偏移量（0表示今天）
    @State private var animateCalendar: Bool = false  // 添加animateCalendar状态变量

    // 使用AppStorage直接读取与AuthService相同的token
//    @AppStorage("auth_token") private var authToken: String?
    // 添加日历数据状态
    @State private var calendarDatesWithData: [Date] = []
    @State private var isLoadingCalendarData = false
    
    
    // 蓝牙连接状态
    @State private var bluetoothState: BluetoothConnectionState = .disconnected
    @State private var isShowingBluetoothAlert = false // 控制蓝牙操作提示框
    @State private var shouldCancelConnection = false // 取消连接标志
    @State private var connectionCheckTimer: AnyCancellable? // 连接状态检查计时器
    @State private var connectedDeviceName = "" // 连接的设备名称
    @State private var showConnectedToast = false // 显示连接成功提示
    // 添加设备服务实例
    @StateObject private var deviceService = WindRingDeviceService.shared
    // 自动上传状态
    @State private var autoUploadEnabled: Bool = false
    
    // 添加加载状态
    @State private var isLoading = false
    @State private var errorMessage: String?
    
    // 压力数据
    @State private var stressScore: Int? = 48
    @State private var latestHRV: Int? = 25
    @State private var dailyAverageHRV: Int? = 27
    @State private var allDayStressIndex: Int? = 49
    @State private var specificStressValue: Int? = 67
    @State private var specificTimeRange: String = "12:30-4:00"
    
    // 时段压力数据
    @State private var morningStress: Double? = 53
    @State private var afternoonStress: Double? = 47
    @State private var eveningStress: Double? = 37.5
    
    // 压力比率数据
    @State private var stressRatios = [
        StressRatioData(state: "Awake", value: 0.4, duration: 45, color: .cyan),
        StressRatioData(state: "REM", value: 0.7, duration: 45, color: Color(red: 0.0, green: 0.8, blue: 0.6)),
        StressRatioData(state: "core", value: 0.3, duration: 45, color: .green),
        StressRatioData(state: "Deep", value: 0.2, duration: 45, color: .yellow)
    ]
    
    // 最近7条记录
    @State private var lastRecords: [APIStressRecordItem] = []
    @State private var isLoadingRecords: Bool = false
    @State private var recordsError: String? = nil
    @State private var selectedRecordIndex: Int? = nil // 用于高亮显示选中的记录
    
    @State private var isMeasuringStress = false // 新增：用于跟踪压力测量状态
    
    // 为图表准备的数据，始终包含7条记录
    private var chartRecords: [APIStressRecordItem] {
        var records = self.lastRecords
        if records.count > 7 {
            records = Array(records.prefix(7))
        }
        let paddingCount = 7 - records.count
        if paddingCount > 0 {
            for _ in 0..<paddingCount {
                records.append(APIStressRecordItem(id: UUID(), stress: 0, time: "", date: Date(), formattedDateString: "--"))
            }
        }
        return records
    }
    
    // 计算Y轴的上限
    private var yAxisUpperBound: Double {
        let maxStress = lastRecords.map { $0.stress }.max() ?? 0
        if maxStress > 100 {
            return Double(maxStress) * 1.2
        } else {
            return 100.0
        }
    }

    // 计算Y轴的刻度值
    private var yAxisTickValues: [Double] {
        if yAxisUpperBound <= 100.0 {
            return [30, 60, 80, 100]
        } else {
            // 生成4个动态刻度
            return [yAxisUpperBound * 0.3, yAxisUpperBound * 0.6, yAxisUpperBound * 0.8, yAxisUpperBound]
        }
    }
    
    
    
    // 计算文字的透明度 - 根据与中心日期的距离
    private func calculateOpacity(dayOffset: Int) -> Double {
        let distance = abs(dayOffset - selectedDay)
        if distance == 0 {
            return 1.0 // 选中项保持完全不透明
        } else if distance == 1 {
            return 0.8 // 相邻项稍微透明
        } else if distance == 2 {
            return 0.6 // 再远一点的项更透明
        } else {
            return 0.4 // 最远的项最透明
        }
    }
    
    // 计算文字的缩放 - 根据与中心日期的距离
    private func calculateScale(dayOffset: Int) -> CGFloat {
        let distance = abs(dayOffset - selectedDay)
        if distance == 0 {
            return 1.0 // 选中项不再放大，保持原始大小
        } else if distance == 1 {
            return 1.0 // 相邻项保持原始大小
        } else if distance == 2 {
            return 0.9 // 再远一点的项缩小
        } else {
            return 0.8 // 最远的项最小
        }
    }
    
    // 根据压力分数获取状态描述
    private func getStressStateText(score: Int?) -> String {
        guard let score = score else { return "--" }
        
        if score < 30 {
            return "stress_state_relaxed".localized
        } else if score < 60 {
            return "stress_state_normal".localized
        } else if score < 80 {
            return "stress_state_mild".localized
        } else {
            return "stress_state_high".localized
        }
    }
    
    // 根据压力分数获取状态文本颜色
    private func getStressStateColor(score: Int?) -> Color {
        guard let score = score else { return .white }
        
        if score < 30 {
            return .cyan
        } else if score < 60 {
            return .green
        } else if score < 80 {
            return .yellow
        } else {
            return .red
        }
    }
    
    // MARK: - 主视图
    var body: some View {
        VStack(spacing: 0) {
            // 顶部状态栏
            VStack(spacing: 12) {
                // 自定义导航栏
                HStack {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .foregroundColor(.white)
                                .font(.system(size: 16))
                            
                            Text("sleep_detail_nav_title".localized)
                                .foregroundColor(.white)
                                .font(.system(size: 16, weight: .bold))
                        }
                    }
                                
                                Spacer()
                                
                    // 右侧图标按钮
                    HStack(spacing: 16) {
                        // 蓝牙按钮 - 与Insight页面相同的实现
                        Button(action: {
                            // 蓝牙按钮点击逻辑
                            handleBluetoothButtonTap()
                        }) {
                            ZStack {
                                // 根据连接状态显示不同图标
                                if bluetoothState == .disconnected {
                                    // 断开连接时显示断连图标
                                    Image("断连")
                            .resizable()
                                        .frame(width: 16, height: 16)
                                } else if bluetoothState == .connected {
                                    // 已连接状态显示连接状态图标 - 使用蓝色圆环
                                    Image("连联")
                                        .resizable()
                                        .frame(width: 16, height: 16)
                                } else if bluetoothState == .connecting {
                                    // 连接中状态的子视图
                                    ConnectingStateView()
                            .frame(width: 16, height: 16)
                                }
                            }
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 12) // 为状态栏留出更多空间
                .padding(.bottom, 8) // 增加底部间距，给日期选择器留出更多空间
            }
//            // 自定义导航栏
//            HStack {
//                // 返回按钮
//                Button(action: {
//                    presentationMode.wrappedValue.dismiss()
//                }) {
//                    HStack(spacing: 4) {
//                        Image(systemName: "chevron.left")
//                            .foregroundColor(.white)
//                            .font(.system(size: 16))
//
//                        Text("stress_detail_nav_title".localized)
//                            .foregroundColor(.white)
//                            .font(.system(size: 16, weight: .bold))
//                    }
//                }
//
//                Spacer()
//
//                // 右侧图标按钮
//                HStack(spacing: 16) {
//                    // 蓝牙按钮 - 与Insight页面相同的实现
//                    Button(action: {
//                        // 蓝牙按钮点击逻辑
//                        handleBluetoothButtonTap()
//                    }) {
//                        ZStack {
//                            // 根据连接状态显示不同图标
//                            if bluetoothState == .disconnected {
//                                // 断开连接时显示断连图标
//                                Image("断连")
//                                    .resizable()
//                                    .frame(width: 16, height: 16)
//                            } else if bluetoothState == .connected {
//                                // 已连接状态显示连接状态图标 - 使用蓝色圆环
//                                Image("连联")
//                                    .resizable()
//                                    .frame(width: 16, height: 16)
//                            } else if bluetoothState == .connecting {
//                                // 连接中状态的子视图
//                                ConnectingStateView()
//                                    .frame(width: 16, height: 16)
//                            }
//                        }
//                    }
//                }
//                .padding(.horizontal, 16)
//                .padding(.top, 12) // 为状态栏留出更多空间
//                .padding(.bottom, 8) // 增加底部间距，给日期选择器留出更多空间
//            }
                // 日期选择器 - 作为固定的顶部元素
//                DateSelectionView(
//                    selectedDay: $selectedDay,
//                    selectedDate: $selectedDate,
//                    onDateSelected: { day, date in
//                        // 加载数据
//                        loadStressData(for: date)
//                    }
//                )
//                .background(Color.black) // 确保背景色与页面一致
//                .zIndex(1) // 确保日期选择器始终位于顶层
//                .padding(.bottom, 4) // 添加底部间距，与内容区域分隔
                
                // 可滚动内容区域
                ScrollView {
                    VStack(spacing: 16) {
                        // 压力指数卡片 - 重新设计以匹配参考图
                        stressScoreCard
                        
                        // 最近记录卡片 - 新增卡片
                        lastRecordsCard
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 6) // 添加顶部内边距，增加与日期选择器的分隔
                }
            }
            .background(Color.black.edgesIgnoringSafeArea(.all))
            .navigationBarBackButtonHidden(true)
            .navigationBarHidden(true) // 完全隐藏系统导航栏
            .onAppear {
                // 初始加载数据
                print("StressDetailView 出现，加载数据...")
                loadStressData(for: selectedDate)
                
                // 同步蓝牙状态
                syncBluetoothState()
                startConnectionStateChecking()
            }
            .onDisappear {
                // 取消所有未完成的网络请求
                print("StressDetailView 消失，取消订阅...")
                connectionCheckTimer?.cancel()
            }
            .onReceive(NotificationCenter.default.publisher(for: .stressDataUploaded)) { _ in
                print("收到压力数据上传通知，刷新数据...")
                isMeasuringStress = false
                loadStressData(for: selectedDate)
            }
            // 监听selectedDate的变化
            .onChange(of: selectedDate) { newDate in
                // 加载新日期的数据
                //loadActivityData(for: newDate, forceRefresh: false)
                // 获取设备活动数据
                //fetchDeviceActivityData(day: selectedDay)
            }
            // 添加连接成功提示
            .overlay(
                VStack {
                    if showConnectedToast {
                        Spacer().frame(height: 80)
                        
                        HStack(spacing: 10) {
                            Image("连联")
                                .resizable()
                                .frame(width: 18, height: 18)
                            
                            Text(String(format: "insight_connected_to_device".localized, connectedDeviceName))
                                .font(.system(size: 14))
                                .foregroundColor(.white)
                        }
                        .padding(.vertical, 8)
                        .padding(.horizontal, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color.black.opacity(0.7))
                        )
                        .transition(.move(edge: .top).combined(with: .opacity))
                        
                        Spacer()
                    }
                }
                .animation(.easeInOut, value: showConnectedToast)
            )
            .alert(isPresented: $isShowingBluetoothAlert) {
                Alert(
                    title: Text("insight_disconnect_alert_title".localized),
                    message: Text("insight_disconnect_alert_message".localized),
                    primaryButton: .destructive(Text("insight_disconnect_button_title".localized)) {
                        disconnectBluetooth()
                    },
                    secondaryButton: .cancel(Text("cancel".localized))
                )
            }
    }
    
    // MARK: - 压力指数卡片
    private var stressScoreCard: some View {
        VStack(spacing: 16) {
            // 标题行
            HStack {
                HStack(spacing: 4) {
                    Text("stress_detail_nav_title".localized)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                    
                    InfoButtonWithGlossaryPopup(showKey: GlossaryKey.stress.rawValue)

                }
                
                Spacer()
                
                if let timeRange = lastRecords.first?.date.string(withFormat: "HH:mm:ss") {
                    Text(timeRange)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.5))
                }
            }
            .padding(.top, 20)
            .padding(.horizontal, 16)
            
            // 加载状态指示器
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.5)
                    .frame(height: 50)
                    .padding(.vertical, 20)
            }
            
            // 错误消息显示
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .font(.system(size: 14))
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
            }
            
            // 压力指数仪表盘 - 调整样式以匹配参考图
            if !isLoading {
                ZStack {
                    // 半圆背景 - 使用渐变色
                    Circle()
                        .trim(from: 0, to: 0.5)
                        .stroke(
                            AngularGradient(
                                gradient: Gradient(colors: [
                                    .cyan,
                                    .green,
                                    Color(hex: "#FFD700"), // 金黄色
                                    .orange,
                                    .red
                                ]),
                                center: .center,
                                startAngle: .degrees(180),
                                endAngle: .degrees(0)
                            ),
                            style: StrokeStyle(lineWidth: 18, lineCap: .round)
                        )
                        .frame(width: 220, height: 220)
                        .rotationEffect(.degrees(180))
                        .opacity(0.3)
                        .offset(y: 30)
                    
                    // 压力指数进度 - 当没有数据时不显示进度
                    if let score = stressScore {
                        let progress = min(Double(score) / 100.0, 1.0)
                        Circle()
                            .trim(from: 0, to: 0.5 * progress)
                            .stroke(
                                AngularGradient(
                                    gradient: Gradient(colors: [
                                        .cyan,
                                        .green
                                    ]),
                                    center: .center,
                                    startAngle: .degrees(180),
                                    endAngle: .degrees(180 - 180 * progress)
                                ),
                                style: StrokeStyle(lineWidth: 18, lineCap: .round)
                            )
                            .frame(width: 220, height: 220)
                            .rotationEffect(.degrees(180))
                            .offset(y: 30)
                    }
                    
                    // 使用GeometryReader精确定位刻度标签
                    GeometryReader { geometry in
                        let width = geometry.size.width
                        let radius = width / 2 - 5 // 更贴近环
                        let center = CGPoint(x: width / 2, y: width / 2)
                        
                        ZStack {
                            // 删除所有刻度点标签
                        }
                        .offset(y: 30) // 添加和半圆环一样的偏移量
                    }
                    .frame(width: 220, height: 220)
                    
                    // 压力指数值和状态
                    VStack(spacing: 2) { // 减小间距
                        Text(stressScore != nil ? "\(stressScore!)" : "--")
                            .font(.system(size: 48, weight: .bold)) // 增大字体
                            .foregroundColor(.white)
                        
                        Text(stressScore != nil ? getStressStateText(score: stressScore) : "--")
                            .font(.system(size: 18))
                            .foregroundColor(getStressStateColor(score: stressScore))
                    }
                    .offset(y: 15) // 微调位置
                    
                    // Stress Value 文本
                    Text("stress_detail_stress_value".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.6))
                        .offset(y: 70) // 放在半圆环底部中央
                }
                .frame(height: 180)
                .padding(.vertical, 10)
            }
            ///是否隐藏
            if VersionUpdateService.shared.status == 1{
                // HRV指标 - 重新布局匹配参考图
                HStack(spacing: 20) {
                    // 最新HRV
                    hrvMetricView(
                        title: "stress_detail_latest_hrv".localized,
                        value: latestHRV != nil ? "\(latestHRV!)" : "--",
                        unit: "stress_detail_unit_ms".localized,
                        icon: "clock",
                        iconColor: .cyan
                    )
                    
                    // 日均HRV
                    hrvMetricView(
                        title: "stress_detail_daily_average_hrv".localized,
                        value: dailyAverageHRV != nil ? "\(dailyAverageHRV!)" : "--",
                        unit: "stress_detail_unit_ms".localized,
                        icon: "chart.bar",
                        iconColor: .blue
                    )
                }
                .padding(.horizontal, 12)
                .padding(.top, 8)
                
                // HRV解释提示
                infoBannerView(message: "stress_detail_hrv_explanation".localized)
                    .padding(.horizontal, 12)
                    
                // 测量按钮
                Button(action: {
                    // 开始测量压力逻辑
                    isMeasuringStress = true
                    CRPSmartRingManage.shared.startStressMeasurement()
                }) {
                    HStack {
                        if isMeasuringStress {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                            Text("stress_detail_measuring".localized)
                                .foregroundColor(.white)
                                .font(.system(size: 14))
                        } else {
                            Image(systemName: "play.circle")
                                .foregroundColor(.white)
                            Text("stress_detail_start_measuring".localized)
                                .foregroundColor(.white)
                                .font(.system(size: 14))
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(isMeasuringStress ? Color.gray : Color.blue)
                    .cornerRadius(12)
                }
                .disabled(isMeasuringStress)
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
            }
            
        }
        .background(Color.moduleBackground)
        .cornerRadius(24)
    }
    
    // MARK: - 最近记录卡片
    private var lastRecordsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("stress_detail_last_7_records".localized)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.white)
                .padding(.top, 20)
                .padding(.horizontal, 16)

            if isLoadingRecords {
                HStack {
                    Spacer()
                    ProgressView().progressViewStyle(CircularProgressViewStyle(tint: .white))
                    Spacer()
                }.padding(.vertical, 30)
            } else if let error = recordsError {
                Text(error)
                    .foregroundColor(.red)
                    .padding()
            } else if lastRecords.isEmpty {
                 HStack {
                    Spacer()
                    Text("stress_detail_no_records_available".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.7))
                    Spacer()
                }
                .padding(.vertical, 30)
            } else {
                // 成功获取数据后显示图表
                successView
            }
        }
        .background(Color.moduleBackground)
        .cornerRadius(24)
    }
    
    private var successView: some View {
        VStack(spacing: 16) {
            // 日期显示
            Text(selectedRecordIndex.flatMap { index in chartRecords.indices.contains(index) ? chartRecords[index].formattedDate : nil } ?? "stress_detail_select_record_prompt".localized)
                .font(.system(size: 14))
                .foregroundColor(.white)
                .padding(10)
                .frame(maxWidth: .infinity)
                .background(Color.green.opacity(0.8))
                .cornerRadius(16)
                .padding(.horizontal, 16)

            // 分隔线
            Rectangle()
                .fill(Color.gray.opacity(0.2))
                .frame(height: 1)
                .padding(.horizontal, 16)

            // 图表区域
            HStack(spacing: 8) {
                Chart {
                    ForEach(Array(chartRecords.enumerated()), id: \.offset) { index, record in
                        BarMark(
                            x: .value("Index", String(index)),
                            y: .value("Stress", record.stress)
                        )
                        .foregroundStyle(selectedRecordIndex == index ? Color.green : Color.gray.opacity(0.5))
                        .cornerRadius(4)
                    }
                }
                .chartYScale(domain: 0...yAxisUpperBound)
                .chartYAxis {
                    AxisMarks(position: .trailing, values: yAxisTickValues) { value in
                        AxisGridLine().foregroundStyle(Color.gray.opacity(0.15))
                        AxisValueLabel {
                            if let val = value.as(Double.self) {
                                Text("\(Int(val))")
                                    .font(.system(size: 10))
                                    .foregroundColor(.white.opacity(0.6))
                            }
                        }
                    }
                }
                .chartXAxis {
                    AxisMarks(values: .automatic) { value in
                        if let indexString = value.as(String.self), let index = Int(indexString) {
                            if chartRecords.indices.contains(index) {
                                let record = chartRecords[index]
                                AxisValueLabel {
                                    Text(record.stress > 0 ? "\(record.stress)" : "")
                                        .font(.system(size: 14))
                                        .foregroundColor(.white.opacity(0.8))
                                }
                            }
                        }
                    }
                }
                .chartOverlay { proxy in
                    GeometryReader { geometry in
                        Rectangle().fill(Color.clear).contentShape(Rectangle())
                            .gesture(
                                DragGesture(minimumDistance: 0)
                                    .onChanged { value in
                                        let xPosition = value.location.x
                                        if let indexString: String = proxy.value(atX: xPosition), let index = Int(indexString) {
                                            if chartRecords.indices.contains(index) && selectedRecordIndex != index {
                                                selectedRecordIndex = index
                                            }
                                        }
                                    }
                            )
                    }
                }

                stressScaleBar.frame(width: 4)
            }
            .frame(height: 150)
            .padding(.horizontal, 16)
            .padding(.bottom, 20)
        }
    }
    
    private var stressScaleBar: some View {
        let gradientStops: [Gradient.Stop] = [
            .init(color: .cyan, location: 0),
            .init(color: .green, location: 0.3),
            .init(color: .yellow, location: 0.6),
            .init(color: .orange, location: 0.8),
            .init(color: .red, location: 1.0)
        ]
        
        return Rectangle()
            .fill(LinearGradient(gradient: Gradient(stops: gradientStops), startPoint: .bottom, endPoint: .top))
            .cornerRadius(2)
    }
    
    // MARK: - 辅助视图
    
    // HRV指标视图 - 简化样式以匹配参考图
    private func hrvMetricView(title: String, value: String, unit: String, icon: String, iconColor: Color) -> some View {
        HStack(spacing: 10) {
            // 图标
            Image(systemName: icon)
                .foregroundColor(iconColor)
                .font(.system(size: 16))
                .frame(width: 28, height: 28)
                .background(Color.black.opacity(0.3))
                .clipShape(Circle())
            
            // 数值和标题
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.7))
                
                HStack(alignment: .firstTextBaseline, spacing: 2) {
                    Text(value)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text(unit)
                        .font(.system(size: 10))
                        .foregroundColor(.white.opacity(0.7))
                        .offset(y: -2)
                }
            }
            
            Spacer()
        }
        .frame(height: 56) // 固定高度为56
        .padding(.horizontal, 12)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#141821"),
                    Color(hex: "#2A3040")
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        .cornerRadius(12)
    }
    
    // 信息提示条视图 - 调整样式匹配参考图
    private func infoBannerView(message: String) -> some View {
        HStack(spacing: 10) {
            Image(systemName: "bell.fill")
                .foregroundColor(.green)
                .font(.system(size: 14))
            
            Text(message)
                .font(.system(size: 12))
                .foregroundColor(.green)
                .lineLimit(2)
            
            Spacer()
        }
        .padding(12)
        .background(Color(red: 0.0, green: 0.2, blue: 0.1))
        .cornerRadius(10)
    }
    
    // MARK: - 数据加载函数
    private func loadStressData(for date: Date) {
        // 清除旧错误信息
        errorMessage = nil
        
        // 设置加载状态
        isLoading = true
        
        // 将日期格式化为API需要的格式：yyyy-MM-dd
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        print("StressDetailView: 尝试获取压力数据，日期: \(dateString)")
        
        // 获取用户认证令牌 - 从 UserDefaults 获取，这样与 MainInsightView 中的实现区分开
        let token = AuthService.shared.currentToken?.accessToken//UserDefaults.standard.string(forKey: "auth_token")
        
        // 创建自定义请求，而不是直接调用共享的 APIService
        // 这样可以为压力详情页面定制请求行为
//        let apiBaseURL = "http://ring-api-dev.weaving-park.com"
        
        guard let url = URL(string: "\(AppGlobals.apiBaseURL)/app-api/iot/stress/getScore?date=\(dateString)") else {
            self.isLoading = false
            self.errorMessage = "error_invalid_url".localized
            self.resetDataToEmpty()
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 添加认证令牌
        if let token = token, !token.isEmpty {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("StressDetailView: 使用用户访问令牌")
        } else {
            request.addValue("Bearer test1", forHTTPHeaderField: "Authorization")
            print("StressDetailView: 使用测试令牌")
        }
        
        request.addValue("application/json", forHTTPHeaderField: "Accept")
        
        // 发送请求 - 使用 URLSession 而不是 Combine，与 MainInsightView 区分
        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                self.isLoading = false
                
                if let error = error {
                    print("StressDetailView: 网络错误: \(error.localizedDescription)")
                    self.errorMessage = String(format: "error_load_data_format".localized, error.localizedDescription)
                    self.resetDataToEmpty()
                    return
                }
                
                // 检查HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("StressDetailView: HTTP状态码: \(httpResponse.statusCode)")
                    if !(200...299).contains(httpResponse.statusCode) {
                        self.errorMessage = String(format: "error_http_code_format".localized, httpResponse.statusCode)
                        self.resetDataToEmpty()
                        return
                    }
                }
                
                guard let data = data else {
                    self.errorMessage = "error_no_data_returned".localized
                    self.resetDataToEmpty()
                    return
                }
                
                // 调试输出响应数据
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("StressDetailView: 压力评分原始响应: \(jsonString)")
                }
                
                do {
                    let response = try CleanJSONDecoder().decode(StressScoreResponse.self, from: data)
                    
                    if response.code == 0, let data = response.data {
                        print("StressDetailView: 数据解析成功: score=\(data.score)")
                        
                        // 成功获取到数据，更新UI
                        self.stressScore = data.score
                        
                        // 更新HRV数据
                        if let lastAvgHrv = data.lastAvg30Hrv {
                            print("StressDetailView: 更新最新HRV: \(lastAvgHrv)")
                            self.latestHRV = lastAvgHrv
                        } else if let lastAvgHRV = data.lastAvgHRV {
                            print("StressDetailView: 更新最新HRV(备选字段): \(lastAvgHRV)")
                            self.latestHRV = lastAvgHRV
                        } else {
                            self.latestHRV = nil
                        }
                        
                        if let avgHrv = data.avgHrv {
                            print("StressDetailView: 更新日均HRV: \(avgHrv)")
                            self.dailyAverageHRV = avgHrv
                        } else {
                            self.dailyAverageHRV = nil
                        }
                        
                        // 加载压力数据的同时，也加载最近记录
                        self.loadLastRecords()
                    } else {
                        print("StressDetailView: API返回错误: \(response.msg)")
                        self.errorMessage = String(format: "error_api_format".localized, response.msg)
                        self.resetDataToEmpty()
                    }
                } catch {
                    print("StressDetailView: 解析错误: \(error.localizedDescription)")
                    self.errorMessage = String(format: "error_parsing_format".localized, error.localizedDescription)
                    self.resetDataToEmpty()
                }
            }
        }.resume()
    }
    
    // MARK: - 蓝牙相关函数
    // 蓝牙按钮点击逻辑
    private func handleBluetoothButtonTap() {
        switch bluetoothState {
        case .disconnected:
            // 断开状态下点击，尝试连接之前的设备
            connectLastDevice()
        case .connecting:
            // 连接中状态下点击，取消连接
            cancelConnecting()
        case .connected:
            // 已连接状态下点击，显示断开连接提示
            isShowingBluetoothAlert = true
        }
    }
    
    // 尝试连接上次的设备
    private func connectLastDevice() {
        bluetoothState = .connecting
        shouldCancelConnection = false
        
        print("尝试连接设备...")
        
        // 首先检查设备服务是否有上次连接的设备
        if let lastMac = deviceService.lastConnectedDeviceMAC, !lastMac.isEmpty {
            print("尝试连接上次设备: \(lastMac)")
            
            // 先断开当前连接（如果有）
            if deviceService.connectionState.isConnected {
                deviceService.disconnectDevice()
            }
            
            // 开始扫描
            deviceService.startScan(duration: 10)
            
            // 设置10秒超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [self] in
                guard bluetoothState == .connecting else { return }
                
                // 检查是否已连接
                if !deviceService.connectionState.isConnected && !shouldCancelConnection {
                    bluetoothState = .disconnected
                    print("连接超时")
                }
            }
            
            // 尝试连接
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [self] in
                guard !shouldCancelConnection else { return }
                
                if let discovery = deviceService.discoveredDevices.first(where: {
                    $0.mac?.uppercased() == lastMac.uppercased()
                }) {
                    print("找到之前设备，开始连接")
                    deviceService.connectDevice(discovery: discovery)
                    
                    // 连接成功后记录设备名称，用于显示提示
                    if let deviceName = discovery.localName, !deviceName.isEmpty {
                        connectedDeviceName = deviceName
                    } else {
                        connectedDeviceName = "device".localized
                    }
                }
            }
        } else {
            print("无之前连接的设备记录")
            
            // 开始扫描，连接任何发现的设备
            deviceService.startScan(duration: 5)
            
            // 设置扫描超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 6) { [self] in
                guard bluetoothState == .connecting else { return }
                
                if !deviceService.discoveredDevices.isEmpty && !shouldCancelConnection {
                    let firstDevice = deviceService.discoveredDevices.first!
                    print("尝试连接发现的设备: \(firstDevice.localName ?? "unknown_device".localized)")
                    deviceService.connectDevice(discovery: firstDevice)
                    
                    // 连接超时
                    DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [self] in
                        guard bluetoothState == .connecting else { return }
                        
                        if !deviceService.connectionState.isConnected && !shouldCancelConnection {
                            bluetoothState = .disconnected
                            print("连接超时")
                        }
                    }
                } else {
                    // 没有发现设备
                    bluetoothState = .disconnected
                    print("未发现设备")
                }
            }
        }
    }
    
    // 取消连接过程
    private func cancelConnecting() {
        shouldCancelConnection = true
        bluetoothState = .disconnected
        
        // 取消设备服务的连接操作
        if deviceService.connectionState == .connecting {
            deviceService.disconnectDevice()
        }
        
        if deviceService.isScanning {
            deviceService.stopScan()
        }
    }
    
    // 断开蓝牙连接
    private func disconnectBluetooth() {
        // 执行断开连接逻辑
        bluetoothState = .disconnected
        deviceService.disconnectDevice()
    }
    
    // 开始定期检查连接状态
    private func startConnectionStateChecking() {
        // 取消可能存在的计时器
        connectionCheckTimer?.cancel()
        
        // 创建新计时器，每1秒检查一次
        connectionCheckTimer = Timer.publish(every: 1, on: .main, in: .common)
            .autoconnect()
            .sink { [self] _ in
                syncBluetoothState()
                
                // 如果设备已连接但UI未显示连接状态，立即更新
                if deviceService.connectionState == .connected && bluetoothState != .connected {
                    bluetoothState = .connected
                    print("发现设备已连接，更新UI状态")
                    
                    // 显示连接成功提示
                    if !showConnectedToast {
                        if let deviceName = deviceService.currentDiscovery?.localName, !deviceName.isEmpty {
                            connectedDeviceName = deviceName
                        } else {
                            connectedDeviceName = "device".localized
                        }
                        showConnectedToast = true
                        
                        // 3秒后隐藏提示
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                            withAnimation {
                                showConnectedToast = false
                            }
                        }
                    }
                }
                
                // 如果设备已断开但UI仍显示连接状态，立即更新
                if deviceService.connectionState != .connected && bluetoothState == .connected {
                    bluetoothState = .disconnected
                    print("发现设备已断开，更新UI状态")
                }
            }
    }
    
    // 同步蓝牙状态
    private func syncBluetoothState() {
        let previousState = bluetoothState
        
        // 先检查设备服务的连接状态
        if deviceService.connectionState == .connected {
            // 如果设备已连接，直接更新UI状态
            if bluetoothState != .connected {
                bluetoothState = .connected
                
                // 如果状态从非连接变为连接，更新设备名称但不显示提示
                if previousState != .connected {
                    connectedDeviceName = deviceService.currentDiscovery?.localName ?? "device".localized
                }
            }
            return
        }
        
        // 连接中状态的特殊处理
        if bluetoothState == .connecting {
            if deviceService.connectionState == .connected {
                bluetoothState = .connected
            } else if deviceService.connectionState == .disconnected {
                if !shouldCancelConnection {
                    bluetoothState = .disconnected
                }
            }
        } else {
            // 常规状态同步
            if deviceService.connectionState == .connected && bluetoothState != .connected {
                bluetoothState = .connected
            } else if deviceService.connectionState != .connected && bluetoothState == .connected {
                bluetoothState = .disconnected
            }
        }
    }
    
    /// 更新自动上传设置
    private func updateAutoUploadSetting(enabled: Bool) {
        if enabled {
            // 启用自动上传
            let success = RawDataUploadService.shared.startAutoUpload(interval: 300) // 5分钟
            if !success {
                // 如果启动失败，在1秒后重试
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                    let retrySuccess = RawDataUploadService.shared.startAutoUpload(interval: 300)
                    if !retrySuccess {
                        // 如果重试仍然失败，5秒后再次尝试
                        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                            _ = RawDataUploadService.shared.startAutoUpload(interval: 300)
                            // 无论成功与否，都保持状态为开启
                            self.autoUploadEnabled = true
                        }
                    }
                }
            }
        } else {
            // 禁用自动上传 - 由于我们希望保持开启，这部分代码不会被执行
            // RawDataUploadService.shared.stopAutoUpload()
            
            // 强制重新启用自动上传
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.autoUploadEnabled = true
                _ = RawDataUploadService.shared.startAutoUpload(interval: 300)
            }
        }
    }
    
    
    
    // MARK: - 加载最近压力记录
    private func loadLastRecords() {
        // 清除旧错误信息
        recordsError = nil
        
        // 设置加载状态
        isLoadingRecords = true
        
        // 创建自定义请求
//        let apiBaseURL = "http://ring-api-dev.weaving-park.com"
        guard let url = URL(string: "\(AppGlobals.apiBaseURL)/app-api/iot/stress/getLastRecords?number=7") else {
            self.isLoadingRecords = false
            self.recordsError = "error_invalid_url".localized
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        // 添加认证令牌
        
//        let token = UserDefaults.standard.string(forKey: "auth_token")
        if let token = AuthService.shared.currentToken?.accessToken, !token.isEmpty {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("StressDetailView: 使用用户访问令牌获取最近记录")
        } else {
            request.addValue("Bearer test1", forHTTPHeaderField: "Authorization")
            print("StressDetailView: 使用测试令牌获取最近记录")
        }
        
        request.addValue("application/json", forHTTPHeaderField: "Accept")
        
        // 发送请求
        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                self.isLoadingRecords = false
                
                if let error = error {
                    print("StressDetailView: 获取最近记录网络错误: \(error.localizedDescription)")
                    self.recordsError = String(format: "error_load_records_format".localized, error.localizedDescription)
                    return
                }
                
                // 检查HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("StressDetailView: 获取最近记录HTTP状态码: \(httpResponse.statusCode)")
                    if !(200...299).contains(httpResponse.statusCode) {
                        self.recordsError = String(format: "error_http_code_format".localized, httpResponse.statusCode)
                        return
                    }
                }
                
                guard let data = data else {
                    self.recordsError = "error_no_data_returned".localized
                    return
                }
                
                // 调试输出响应数据
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("StressDetailView: 最近记录原始响应: \(jsonString)")
                }
                
                do {
                    // 解析JSON
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let code = json["code"] as? Int {
                        
                        print("StressDetailView: 响应代码 \(code)")
                        
                        if code == 0 {
                            // 清空旧数据
                            self.lastRecords.removeAll()
                            
                            // 检查data字段是否为数组
                            if let dataArray = json["data"] as? [[String: Any]] {
                                print("StressDetailView: 找到 \(dataArray.count) 条记录")
                                
                                // 解析记录数据
                                for (index, recordData) in dataArray.enumerated() {
                                    print("StressDetailView: 解析记录 \(index): \(recordData)")
                                    
                                    if let stress = recordData["stress"] as? Int,
                                       let timeMillis = recordData["time"] as? Int64 {
                                        // 修正：毫秒时间戳，除以1000转换为秒
                                        let date = Date(timeIntervalSince1970: Double(timeMillis) / 1000.0)
                                        let formattedDate = formatDateForDisplay(date)
                                        
                                        print("StressDetailView: 时间戳 \(timeMillis) 解析为日期: \(formattedDate)")
                                        
                                        // 创建记录对象
                                        let record = APIStressRecordItem(
                                            id: UUID(),
                                            stress: stress,
                                            time: "\(timeMillis)",
                                            date: date,
                                            formattedDateString: formattedDate
                                        )
                                        self.lastRecords.append(record)
                                        
                                        print("StressDetailView: 解析记录 - stress: \(stress), date: \(formatDateForDisplay(date))")
                                    } else {
                                        print("StressDetailView: 记录 \(index) 格式不正确: \(recordData)")
                                    }
                                }
                                
                                // 如果解析后没有记录，可能是格式问题
                                if self.lastRecords.isEmpty {
                                    print("StressDetailView: 解析后没有可用记录")
//                                    self.recordsError = "解析后没有可用记录"
                                } else {
                                    // 如果有记录，默认选中第一条
                                    self.selectedRecordIndex = 0
                                    print("StressDetailView: 成功获取最近记录，共\(self.lastRecords.count)条")
                                }
                            } else if let dataNull = json["data"], dataNull is NSNull {
                                // 处理data字段为null的情况
                                print("StressDetailView: 数据字段为null")
                                self.recordsError = "stress_detail_no_records_available".localized
                            } else {
                                print("StressDetailView: 数据字段格式不正确: \(String(describing: json["data"]))")
                                self.recordsError = "error_data_format".localized
                            }
                        } else {
                            let msg = json["msg"] as? String ?? "unknown_error".localized
                            print("StressDetailView: API返回错误: \(msg)")
                            self.recordsError = String(format: "error_api_format".localized, msg)
                        }
                    } else {
                        print("StressDetailView: JSON格式错误")
                        self.recordsError = "error_data_format".localized
                    }
                } catch {
                    print("StressDetailView: 解析错误: \(error.localizedDescription)")
                    self.recordsError = String(format: "error_parsing_format".localized, error.localizedDescription)
                }
            }
        }.resume()
    }
    
    // 格式化日期为显示格式
    private func formatDateForDisplay(_ date: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.locale = Locale(identifier: "zh_CN")
        dateFormatter.dateFormat = "yyyy年MM月dd日，EEEE，HH:mm"
        return dateFormatter.string(from: date)
    }
    
    // 重置数据为空，使显示为"--"
    private func resetDataToEmpty() {
        stressScore = nil
        latestHRV = nil
        dailyAverageHRV = nil
        allDayStressIndex = nil
        specificStressValue = nil
        lastRecords.removeAll()
        selectedRecordIndex = nil
    }
    
    // 加载备用模拟数据的方法
    private func loadFallbackData(for date: Date) {
        // 模拟数据加载，当API失败时使用
        let calendar = Calendar.current
        let day = calendar.component(.day, from: date)
        let seed = day % 10 // 使用日期作为随机种子
        
        // 更新压力指数
        stressScore = 40 + (seed * 5)
        
        // 更新HRV数据
        latestHRV = 20 + (seed * 2)
        dailyAverageHRV = 22 + (seed * 2)
        
        // 更新压力指数
        allDayStressIndex = 40 + (seed * 3)
        specificStressValue = 60 + (seed * 3)
        
        // 更新时段压力
        morningStress = 50.0 + Double(seed * 2)
        afternoonStress = 45.0 + Double(seed * 2)
        eveningStress = 35.0 + Double(seed * 2)
        
        // 更新压力比率
        stressRatios = [
            StressRatioData(state: "sleep_stage_awake".localized, value: 0.3 + Double(seed) / 100, duration: 40 + seed * 2, color: .cyan),
            StressRatioData(state: "sleep_stage_rem".localized, value: 0.6 + Double(seed) / 100, duration: 40 + seed * 2, color: Color(red: 0.0, green: 0.8, blue: 0.6)),
            StressRatioData(state: "sleep_stage_core".localized, value: 0.2 + Double(seed) / 100, duration: 40 + seed * 2, color: .green),
            StressRatioData(state: "sleep_stage_deep".localized, value: 0.1 + Double(seed) / 100, duration: 40 + seed * 2, color: .yellow)
        ]
    }
}

// MARK: - 压力比率数据模型
struct StressRatioData: Identifiable {
    let id = UUID()
    let state: String
    let value: Double
    let duration: Int
    let color: Color
}

// MARK: - 压力记录数据模型
struct APIStressRecordItem: Identifiable {
    let id: UUID
    let stress: Int
    let time: String
    let date: Date
    let formattedDateString: String
    
    var formattedDate: String {
        return formattedDateString
    }
}

// MARK: - 预览
struct StressDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            StressDetailView()
        }
        .preferredColorScheme(.dark)
    }
}
