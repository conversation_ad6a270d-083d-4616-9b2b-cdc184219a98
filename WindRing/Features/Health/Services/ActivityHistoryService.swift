import Foundation
import Combine

// 删除重复定义的模型，使用已有的模型

class ActivityHistoryService {
    static let shared = ActivityHistoryService()
    
    // 是否使用模拟数据（开发调试用）
    private let useMockData = false
    
    /// 获取历史7天活动平均分数
    /// - Returns: 包含分数的Publisher
    func getHistory7AvgScore() -> AnyPublisher<ActivityAvgScore, NetworkError> {
        // 使用模拟数据模式
//        if useMockData {
//            return Just(ActivityAvgScore(score: 65))  // 模拟返回65分
//                .delay(for: .seconds(1), scheduler: RunLoop.main) // 模拟1秒网络延迟
//                .setFailureType(to: NetworkError.self)
//                .eraseToAnyPublisher()
//        }
        
        // 实际API请求实现
        return NetworkManager.shared.request(
            endpoint: "/app-api/iot/activity/getHistory7AvgScore",
            responseType: Int.self
        )
        .map {
            ActivityAvgScore(score: $0)
        }
        .eraseToAnyPublisher()
    }
    
    
    /// 获取历史活动分数
    /// - Parameters:
    ///   - startDate: 开始日期（格式：yyyy-MM-dd）
    ///   - endDate: 结束日期（格式：yyyy-MM-dd）
    ///   - type: 时间范围类型（1=天, 2=周, 3=年）
    /// - Returns: 包含历史分数数据的Publisher
    func getHistoryScore(startDate: String, endDate: String, type: Int) -> AnyPublisher<ActivityScoreHistoryData, NetworkError> {
        // 使用模拟数据模式
        if useMockData {
            // 创建模拟响应数据
            let records: [ActivityScoreHistoryRecord]
            
            switch type {
            case 1: // 天
                records = [
                    ActivityScoreHistoryRecord(score: 82, time: "2025-04-15T01:14:32.710Z"), 
                    ActivityScoreHistoryRecord(score: 86, time: "2025-04-15T05:24:12.323Z"),
                    ActivityScoreHistoryRecord(score: 90, time: "2025-04-15T09:34:45.110Z"),
                    ActivityScoreHistoryRecord(score: 87, time: "2025-04-15T13:47:32.520Z"),
                    ActivityScoreHistoryRecord(score: 84, time: "2025-04-15T17:54:22.310Z"),
                    ActivityScoreHistoryRecord(score: 81, time: "2025-04-15T21:14:55.420Z"),
                    ActivityScoreHistoryRecord(score: 78, time: "2025-04-15T23:44:12.710Z")
                ]
            case 2: // 周
                records = [
                    ActivityScoreHistoryRecord(score: 82, time: "2025-03-10T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 86, time: "2025-03-11T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 90, time: "2025-03-12T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 87, time: "2025-03-13T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 84, time: "2025-03-14T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 81, time: "2025-03-15T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 78, time: "2025-03-16T12:00:00.000Z")
                ]
            case 3: // 年
                records = [
                    ActivityScoreHistoryRecord(score: 81, time: "2025-01-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 83, time: "2025-02-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 86, time: "2025-03-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 88, time: "2025-04-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 89, time: "2025-05-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 87, time: "2025-06-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 85, time: "2025-07-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 83, time: "2025-08-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 81, time: "2025-09-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 84, time: "2025-10-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 86, time: "2025-11-01T12:00:00.000Z"),
                    ActivityScoreHistoryRecord(score: 88, time: "2025-12-01T12:00:00.000Z")
                ]
            default:
                records = []
            }
            
            // 计算最大、最小和平均值
            let scores = records.map { $0.score }
            let maxScore = scores.max() ?? 0
            let minScore = scores.min() ?? 0
            let avgScore = scores.isEmpty ? 0 : scores.reduce(0, +) / scores.count
            
            let mockData = ActivityScoreHistoryData(
                maxScore: maxScore,
                avgScore: avgScore,
                minScore: minScore,
                records: records
            )
            
            return Just(mockData)
                .delay(for: .seconds(1), scheduler: RunLoop.main)
                .setFailureType(to: NetworkError.self)
                .eraseToAnyPublisher()
        }
        
        let parameters: [String: Any] = ["startDate": startDate, "endDate": endDate, "type": type]
        return NetworkManager.shared.request(
            endpoint: "/app-api/iot/activity/getHistoryScore",
            parameters: parameters,
            responseType: ActivityScoreHistoryData.self
        ).eraseToAnyPublisher()
    }
    
    /// 获取历史步数数据
    /// - Parameters:
    ///   - startDate: 开始日期（格式：yyyy-MM-dd）
    ///   - endDate: 结束日期（格式：yyyy-MM-dd）
    ///   - type: 时间范围类型（1=天, 2=周, 3=年）
    /// - Returns: 包含历史步数数据的Publisher
    func getHistorySteps(startDate: String, endDate: String, type: Int) -> AnyPublisher<ActivityStepsHistoryData, NetworkError> {
        // 使用模拟数据模式
        if useMockData {
            // 创建模拟响应数据
            let records: [ActivityStepsRecord]
            
            switch type {
            case 1: // 天
                records = [
                    ActivityStepsRecord(steps: 9500, time: "2025-04-14T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 11200, time: "2025-04-15T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 12500, time: "2025-04-16T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 10800, time: "2025-04-17T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 9600, time: "2025-04-18T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 8800, time: "2025-04-19T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 9100, time: "2025-04-20T01:30:00.000Z")
                ]
            case 2: // 周
                records = [
                    ActivityStepsRecord(steps: 9500, time: "2025-04-14T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 11200, time: "2025-04-15T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 12500, time: "2025-04-16T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 10800, time: "2025-04-17T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 9600, time: "2025-04-18T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 8800, time: "2025-04-19T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 9100, time: "2025-04-20T01:30:00.000Z")
                ]
            case 3: // 年
                records = [
                    ActivityStepsRecord(steps: 9200, time: "2025-01-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 9800, time: "2025-02-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 10400, time: "2025-03-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 11000, time: "2025-04-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 11600, time: "2025-05-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 12200, time: "2025-06-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 11800, time: "2025-07-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 11200, time: "2025-08-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 10600, time: "2025-09-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 10000, time: "2025-10-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 9400, time: "2025-11-01T01:30:00.000Z"),
                    ActivityStepsRecord(steps: 10000, time: "2025-12-01T01:30:00.000Z")
                ]
            default:
                records = []
            }
            
            let stepsValues = records.map { $0.steps }
            let maxSteps = stepsValues.max() ?? 0
            let minSteps = stepsValues.min() ?? 0
            let avgSteps = stepsValues.isEmpty ? 0 : stepsValues.reduce(0, +) / stepsValues.count
            
            let mockData = ActivityStepsHistoryData(
                maxSteps: maxSteps,
                avgSteps: avgSteps,
                minSteps: minSteps,
                records: records
            )
            
            return Just(mockData)
                .delay(for: .seconds(1), scheduler: RunLoop.main)
                .setFailureType(to: NetworkError.self)
                .eraseToAnyPublisher()
        }
        
        let parameters: [String: Any] = ["startDate": startDate, "endDate": endDate, "type": type]
        return NetworkManager.shared.request(
            endpoint: "/app-api/iot/activity/getHistorySteps",
            parameters: parameters,
            responseType: ActivityStepsHistoryData.self
        ).eraseToAnyPublisher()
    }
    
    /// 获取历史卡路里数据
    /// - Parameters:
    ///   - startDate: 开始日期（格式：yyyy-MM-dd）
    ///   - endDate: 结束日期（格式：yyyy-MM-dd）
    ///   - type: 时间范围类型（1=天, 2=周, 3=年）
    /// - Returns: 包含历史卡路里数据的Publisher
    func getHistoryCalories(startDate: String, endDate: String, type: Int) -> AnyPublisher<ActivityCaloriesHistoryData, NetworkError> {
        // 使用模拟数据模式
        if useMockData {
            // 创建模拟响应数据
            let records: [ActivityCaloriesRecord]
            
            switch type {
            case 1: // 天
                records = [
                    ActivityCaloriesRecord(calories: 480, time: "2025-04-14T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 520, time: "2025-04-15T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 460, time: "2025-04-16T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 490, time: "2025-04-17T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 450, time: "2025-04-18T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 430, time: "2025-04-19T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 400, time: "2025-04-20T01:30:00.000Z")
                ]
            case 2: // 周
                records = [
                    ActivityCaloriesRecord(calories: 480, time: "2025-04-14T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 520, time: "2025-04-15T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 460, time: "2025-04-16T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 490, time: "2025-04-17T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 450, time: "2025-04-18T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 430, time: "2025-04-19T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 400, time: "2025-04-20T01:30:00.000Z")
                ]
            case 3: // 年
                records = [
                    ActivityCaloriesRecord(calories: 470, time: "2025-01-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 500, time: "2025-02-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 530, time: "2025-03-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 560, time: "2025-04-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 590, time: "2025-05-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 620, time: "2025-06-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 600, time: "2025-07-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 570, time: "2025-08-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 540, time: "2025-09-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 510, time: "2025-10-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 480, time: "2025-11-01T01:30:00.000Z"),
                    ActivityCaloriesRecord(calories: 510, time: "2025-12-01T01:30:00.000Z")
                ]
            default:
                records = []
            }
            
            let caloriesValues = records.map { $0.calories }
            let maxCalories = caloriesValues.max() ?? 0
            let minCalories = caloriesValues.min() ?? 0
            let avgCalories = caloriesValues.isEmpty ? 0 : caloriesValues.reduce(0, +) / caloriesValues.count
            
            let mockData = ActivityCaloriesHistoryData(
                maxCalories: maxCalories,
                avgCalories: avgCalories,
                minCalories: minCalories,
                records: records
            )
            
            return Just(mockData)
                .delay(for: .seconds(1), scheduler: RunLoop.main)
                .setFailureType(to: NetworkError.self)
                .eraseToAnyPublisher()
        }
        
        let parameters: [String: Any] = ["startDate": startDate, "endDate": endDate, "type": type]
        return NetworkManager.shared.request(
            endpoint: "/app-api/iot/activity/getHistoryCalories",
            parameters: parameters,
            responseType: ActivityCaloriesHistoryData.self
        ).eraseToAnyPublisher()
    }
    
    /// 获取历史站立时长数据
    /// - Parameters:
    ///   - startDate: 开始日期（格式：yyyy-MM-dd）
    ///   - endDate: 结束日期（格式：yyyy-MM-dd）
    ///   - type: 时间范围类型（1=天, 2=周, 3=年）
    /// - Returns: 包含历史站立时长数据的Publisher
    func getHistoryStandingDuration(startDate: String, endDate: String, type: Int) -> AnyPublisher<ActivityStandingDurationHistoryData, NetworkError> {
        // 使用模拟数据模式
        if useMockData {
            // 创建模拟响应数据
            let records: [ActivityStandingDurationRecord]
            
            switch type {
            case 1: // 天
                records = [
                    ActivityStandingDurationRecord(duration: 312, time: "2025-04-14T01:30:00.000Z"), // 5小时12分钟
                    ActivityStandingDurationRecord(duration: 468, time: "2025-04-15T01:30:00.000Z"), // 7小时48分钟
                    ActivityStandingDurationRecord(duration: 390, time: "2025-04-16T01:30:00.000Z"), // 6小时30分钟
                    ActivityStandingDurationRecord(duration: 330, time: "2025-04-17T01:30:00.000Z"), // 5小时30分钟
                    ActivityStandingDurationRecord(duration: 228, time: "2025-04-18T01:30:00.000Z"), // 3小时48分钟
                    ActivityStandingDurationRecord(duration: 192, time: "2025-04-19T01:30:00.000Z"), // 3小时12分钟
                    ActivityStandingDurationRecord(duration: 180, time: "2025-04-20T01:30:00.000Z")  // 3小时0分钟
                ]
            case 2: // 周
                records = [
                    ActivityStandingDurationRecord(duration: 312, time: "2025-04-14T01:30:00.000Z"), // 5小时12分钟
                    ActivityStandingDurationRecord(duration: 468, time: "2025-04-15T01:30:00.000Z"), // 7小时48分钟
                    ActivityStandingDurationRecord(duration: 390, time: "2025-04-16T01:30:00.000Z"), // 6小时30分钟
                    ActivityStandingDurationRecord(duration: 330, time: "2025-04-17T01:30:00.000Z"), // 5小时30分钟
                    ActivityStandingDurationRecord(duration: 228, time: "2025-04-18T01:30:00.000Z"), // 3小时48分钟
                    ActivityStandingDurationRecord(duration: 192, time: "2025-04-19T01:30:00.000Z"), // 3小时12分钟
                    ActivityStandingDurationRecord(duration: 180, time: "2025-04-20T01:30:00.000Z")  // 3小时0分钟
                ]
            case 3: // 年
                records = [
                    ActivityStandingDurationRecord(duration: 270, time: "2025-01-01T01:30:00.000Z"), // 4小时30分钟
                    ActivityStandingDurationRecord(duration: 282, time: "2025-02-01T01:30:00.000Z"), // 4小时42分钟
                    ActivityStandingDurationRecord(duration: 294, time: "2025-03-01T01:30:00.000Z"), // 4小时54分钟
                    ActivityStandingDurationRecord(duration: 306, time: "2025-04-01T01:30:00.000Z"), // 5小时6分钟
                    ActivityStandingDurationRecord(duration: 318, time: "2025-05-01T01:30:00.000Z"), // 5小时18分钟
                    ActivityStandingDurationRecord(duration: 330, time: "2025-06-01T01:30:00.000Z"), // 5小时30分钟
                    ActivityStandingDurationRecord(duration: 312, time: "2025-07-01T01:30:00.000Z"), // 5小时12分钟
                    ActivityStandingDurationRecord(duration: 300, time: "2025-08-01T01:30:00.000Z"), // 5小时0分钟
                    ActivityStandingDurationRecord(duration: 288, time: "2025-09-01T01:30:00.000Z"), // 4小时48分钟
                    ActivityStandingDurationRecord(duration: 276, time: "2025-10-01T01:30:00.000Z"), // 4小时36分钟
                    ActivityStandingDurationRecord(duration: 264, time: "2025-11-01T01:30:00.000Z"), // 4小时24分钟
                    ActivityStandingDurationRecord(duration: 288, time: "2025-12-01T01:30:00.000Z")  // 4小时48分钟
                ]
            default:
                records = []
            }
            
            let durationValues = records.map { $0.duration }
            let maxDuration = durationValues.max() ?? 0
            let minDuration = durationValues.min() ?? 0
            let avgDuration = durationValues.isEmpty ? 0 : durationValues.reduce(0, +) / durationValues.count
            
            let mockData = ActivityStandingDurationHistoryData(
                maxDuration: maxDuration,
                avgDuration: avgDuration,
                minDuration: minDuration,
                records: records
            )
            
            return Just(mockData)
                .delay(for: .seconds(1), scheduler: RunLoop.main)
                .setFailureType(to: NetworkError.self)
                .eraseToAnyPublisher()
        }
        
        let parameters: [String: Any] = ["startDate": startDate, "endDate": endDate, "type": type]
        return NetworkManager.shared.request(
            endpoint: "/app-api/iot/activity/getHistoryStandingDuration",
            parameters: parameters,
            responseType: ActivityStandingDurationHistoryData.self
        ).eraseToAnyPublisher()
    }
} 
