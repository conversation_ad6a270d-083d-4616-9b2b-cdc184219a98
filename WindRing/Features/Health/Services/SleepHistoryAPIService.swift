//
//  SleepAPIService.swift
//  WindRing
//
//  Created by zx on 2025/6/24.
//

import Combine
import UIKit

// MARK: - 睡眠得分API数据模型
/// 睡眠得分API响应模型
struct SleepHistoryScoreResponse: Codable {
    let code: Int
    let data: SleepHistoryScoreData
    let msg: String
}

/// 睡眠得分数据
struct SleepHistoryScoreData: Codable {
    let maxScore: Int
    let avgScore: Int
    let minScore: Int
    let records: [SleepHistoryScoreRecord]
}

/// 睡眠得分记录
struct SleepHistoryScoreRecord: Codable, Identifiable {
    let score: Int
    let time: String
    
    var id: String { time }
}
// MARK: - 睡眠效率API数据模型
/// 睡眠效率API响应模型
//    struct SleepEfficiencyResponse: Codable {
//        let code: Int
//        let data: SleepEfficiencyData
//        let msg: String
//    }

/// 睡眠效率数据
struct SleepHistoryEfficiencyData: Codable {
    let maxEfficiency: Int
    let avgEfficiency: Int
    let minEfficiency: Int
    let records: [SleepHistoryEfficiencyRecord]
}

/// 睡眠效率记录
struct SleepHistoryEfficiencyRecord: Codable, Identifiable {
    let efficiency: Int
    let time: String
    
    var id: String { time }
}

// MARK: - 睡眠时长API数据模型
/// 睡眠时长API响应模型
//    struct SleepTimeAsleepResponse: Codable {
//        let code: Int
//        let data: SleepTimeAsleepData
//        let msg: String
//    }

/// 睡眠时长数据
struct SleepHistoryTimeAsleepData: Codable {
    let maxTimeAsleep: Int
    let avgTimeAsleep: Int
    let minTimeAsleep: Int
    let records: [SleepHistoryTimeAsleepRecord]
    
    /// 获取格式化的最大睡眠时长
    var formattedMaxTime: String {
        formatMinutesToHoursAndMinutes(maxTimeAsleep)
    }
    
    /// 获取格式化的平均睡眠时长
    var formattedAvgTime: String {
        formatMinutesToHoursAndMinutes(avgTimeAsleep)
    }
    
    /// 获取格式化的最小睡眠时长
    var formattedMinTime: String {
        formatMinutesToHoursAndMinutes(minTimeAsleep)
    }
    
    /// 将分钟转换为小时和分钟格式
    private func formatMinutesToHoursAndMinutes(_ minutes: Int) -> String {
        let hours = minutes / 60
        let remainingMinutes = minutes % 60
        return "\(hours) \("hr".localized) \(remainingMinutes) \("min".localized)"
    }
}

/// 睡眠时长记录
struct SleepHistoryTimeAsleepRecord: Codable, Identifiable {
    let timeAsleep: Int
    let time: String
    
    var id: String { time }
}

// MARK: - 睡眠心率API数据模型
/// 睡眠心率API响应模型
struct SleepHistoryHeartRateResponse: Codable {
    let code: Int
    let data: SleepHistoryHeartRateData
    let msg: String
}

/// 睡眠心率数据
struct SleepHistoryHeartRateData: Codable  {
    let maxHeartRate: Int
    let avgHeartRate: Int
    let minHeartRate: Int
    var records: [SleepHistoryHeartRateRecord]
}

/// 睡眠心率记录
struct SleepHistoryHeartRateRecord: Codable, Identifiable {
    var id: Int = 0
    let heartRate: Int
    let time: String
    let maxHeartRate: Int
    let minHeartRate: Int
    
    enum CodingKeys: String, CodingKey {
        case heartRate, time, maxHeartRate, minHeartRate
    }
}
/// 睡眠皮肤温度记录
struct SleepHistorySkinTempRecord: Codable, Identifiable {
    let skinTemp: Double
    let time: String
    
    var id: String { time }
}




// MARK: - 睡眠HRV API数据模型
/// 睡眠HRV API响应模型
struct SleepHistoryHRVResponse: Codable {
    let code: Int
    let data: SleepHistoryHRVData
    let msg: String
}

/// 睡眠HRV数据
struct SleepHistoryHRVData: Codable {
    let maxHRV: Int
    let avgHRV: Int
    let minHRV: Int
    let records: [SleepHRVRecord]
}

struct SleepHRVRecord: Codable, Identifiable,Equatable {
    let hrv: Int
    let time: String
    let maxHRV: Int
    let minHRV: Int
    
    var id: String { time }
}


// MARK: - 睡眠SpO2 API数据模型
/// 睡眠SpO2 API响应模型
struct SleepHistorySpO2Response: Codable {
    let code: Int
    let data: SleepHistorySpO2Data
    let msg: String
}

/// 睡眠SpO2数据
struct SleepHistorySpO2Data: Codable {
    let maxSpO2: Int
    let avgSpO2: Int
    let minSpO2: Int
    let records: [SleepHistorySpO2Record]
}

/// 睡眠SpO2记录
struct SleepHistorySpO2Record: Codable, Identifiable {
    let spO2: Int
    let time: String
    let maxSpO2: Int
    let minSpO2: Int
    
    var id: String { time }
}

// MARK: - 睡眠皮肤温度 API数据模型
/// 睡眠皮肤温度 API响应模型
struct SleepHistorySkinTempResponse: Codable {
    let code: Int
    let data: SleepHistorySkinTempData
    let msg: String
}

/// 睡眠皮肤温度数据
struct SleepHistorySkinTempData: Codable {
    let avgSkinTemp: Double
    let records: [SleepHistorySkinTempRecord]
}

// MARK: - 网络服务
/// 睡眠API服务
class SleepHistoryAPIService {
    /// API基础URL
//        private let baseURL = "https://api.example.com"
    
    /// 默认租户ID
    private let tenantId = 1
    
    /// 默认认证令牌
    private let authToken = AuthService.shared.currentToken?.accessToken ?? ""
    
    /// 共享实例
    static let shared = SleepHistoryAPIService()
    
    /// Cancellables for Combine subscriptions
    private var cancellables = Set<AnyCancellable>()
    
    /// 数据缓存
    private var cache: [String: SleepScoreData] = [:]
    
    /// 睡眠效率数据缓存
    private var efficiencyCache: [String: SleepHistoryEfficiencyData] = [:]

    /// 睡眠时长数据缓存
    private var timeAsleepCache: [String: SleepHistoryTimeAsleepData] = [:]
    
    /// 睡眠心率数据缓存
    private var heartRateCache: [String: SleepHistoryHeartRateData] = [:]
    
    /// 睡眠HRV数据缓存
    private var hrvCache: [String: SleepHistoryHRVData] = [:]
    
    /// 睡眠SpO2数据缓存
    private var spO2Cache: [String: SleepHistorySpO2Data] = [:]
    
    /// 睡眠皮肤温度数据缓存
    private var skinTempCache: [String: SleepHistorySkinTempData] = [:]
    
    /// 私有初始化方法
    private init() {}
    
    /// 获取缓存键
    private func getCacheKey(startDate: String, endDate: String, type: Int) -> String {
        return "\(startDate)_\(endDate)_\(type)"
    }
    
    /// 获取睡眠历史得分
    /// - Parameters:
    ///   - startDate: 开始日期 (格式: yyyy-MM-dd)
    ///   - endDate: 结束日期 (格式: yyyy-MM-dd)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - forceRefresh: 是否强制刷新数据
    ///   - completion: 完成回调，返回结果或错误
    func getSleepHistoryScore(startDate: String, endDate: String, type: Int, forceRefresh: Bool = false, completion: @escaping (Result<SleepHistoryScoreData, Error>) -> Void) {
        // 检查缓存
//            let cacheKey = getCacheKey(startDate: startDate, endDate: endDate, type: type)
//            if !forceRefresh, let cachedData = cache[cacheKey] {
//                completion(.success(cachedData))
//                return
//            }
        
        let parameters: [String: Any] = [
            "startDate": startDate,
            "endDate": endDate,
            "type": type
        ]

        NetworkManager.shared.request(
            endpoint: "/app-api/iot/sleep/getHistoryScore",
            method: .get,
            parameters: parameters,
            responseType: SleepHistoryScoreData.self
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { result in
            if case .failure(let error) = result {
                completion(.failure(error))
            }
        }, receiveValue: { [weak self] data in
//                self?.cache[cacheKey] = data
            completion(.success(data))
        })
        .store(in: &cancellables)
    }
    
    /// 获取睡眠历史效率
    /// - Parameters:
    ///   - startDate: 开始日期 (格式: yyyy-MM-dd)
    ///   - endDate: 结束日期 (格式: yyyy-MM-dd)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - forceRefresh: 是否强制刷新数据
    ///   - completion: 完成回调，返回结果或错误
    func getSleepHistoryEfficiency(startDate: String, endDate: String, type: Int, forceRefresh: Bool = false, completion: @escaping (Result<SleepHistoryEfficiencyData, Error>) -> Void) {
        // 检查缓存
//            let cacheKey = getCacheKey(startDate: startDate, endDate: endDate, type: type)
//            if !forceRefresh, let cachedData = efficiencyCache[cacheKey] {
//                completion(.success(cachedData))
//                return
//            }
        
        let parameters: [String: Any] = [
            "startDate": startDate,
            "endDate": endDate,
            "type": type
        ]

        NetworkManager.shared.request(
            endpoint: "/app-api/iot/sleep/getHistoryEfficiency",
            method: .get,
            parameters: parameters,
            responseType: SleepHistoryEfficiencyData.self
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { result in
            if case .failure(let error) = result {
                completion(.failure(error))
            }
        }, receiveValue: { [weak self] data in
//                self?.efficiencyCache[cacheKey] = data
            completion(.success(data))
        })
        .store(in: &cancellables)
    }
    
    /// 获取睡眠时长
    /// - Parameters:
    ///   - startDate: 开始日期 (格式: yyyy-MM-dd)
    ///   - endDate: 结束日期 (格式: yyyy-MM-dd)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - forceRefresh: 是否强制刷新数据
    ///   - completion: 完成回调，返回结果或错误
    func getSleepTimeAsleep(startDate: String, endDate: String, type: Int, forceRefresh: Bool = false, completion: @escaping (Result<SleepHistoryTimeAsleepData, Error>) -> Void) {
//            let cacheKey = getCacheKey(startDate: startDate, endDate: endDate, type: type)
//            if !forceRefresh, let cachedData = timeAsleepCache[cacheKey] {
//                completion(.success(cachedData))
//                return
//            }
        
        let parameters: [String: Any] = [
            "startDate": startDate,
            "endDate": endDate,
            "type": type
        ]

        NetworkManager.shared.request(
            endpoint: "/app-api/iot/sleep/getHistoryTimeAsleep",
            method: .get,
            parameters: parameters,
            responseType: SleepHistoryTimeAsleepData.self
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { result in
            if case .failure(let error) = result {
                completion(.failure(error))
            }
        }, receiveValue: { [weak self] data in
//                self?.timeAsleepCache[cacheKey] = data
            completion(.success(data))
        })
        .store(in: &cancellables)
    }
    
    /// 获取睡眠心率
    /// - Parameters:
    ///   - startDate: 开始日期 (格式: yyyy-MM-dd)
    ///   - endDate: 结束日期 (格式: yyyy-MM-dd)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - forceRefresh: 是否强制刷新数据
    ///   - completion: 完成回调，返回结果或错误
    func getSleepHeartRate(startDate: String, endDate: String, type: Int, forceRefresh: Bool = false, completion: @escaping (Result<SleepHistoryHeartRateData, Error>) -> Void) {
        // 检查缓存
//            let cacheKey = getCacheKey(startDate: startDate, endDate: endDate, type: type)
//            if !forceRefresh, let cachedData = heartRateCache[cacheKey] {
//                completion(.success(cachedData))
//                return
//            }
        
        let parameters: [String: Any] = [
            "startDate": startDate,
            "endDate": endDate,
            "type": type
        ]

        NetworkManager.shared.request(
            endpoint: "/app-api/iot/hr/sleep/getHistorySleepHeartRate",
            method: .get,
            parameters: parameters,
            responseType: SleepHistoryHeartRateData.self
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { result in
            if case .failure(let error) = result {
                completion(.failure(error))
            }
        }, receiveValue: { [weak self] data in
//                self?.heartRateCache[cacheKey] = data
            completion(.success(data))
        })
        .store(in: &cancellables)
    }
    
    /// 获取睡眠HRV
    /// - Parameters:
    ///   - startDate: 开始日期 (格式: yyyy-MM-dd)
    ///   - endDate: 结束日期 (格式: yyyy-MM-dd)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - forceRefresh: 是否强制刷新数据
    ///   - completion: 完成回调，返回结果或错误
    func getSleepHRV(startDate: String, endDate: String, type: Int, forceRefresh: Bool = false, completion: @escaping (Result<SleepHistoryHRVData, Error>) -> Void) {
        // 检查缓存
//            let cacheKey = getCacheKey(startDate: startDate, endDate: endDate, type: type)
//            if !forceRefresh, let cachedData = hrvCache[cacheKey] {
//                completion(.success(cachedData))
//                return
//            }
        
        let parameters: [String: Any] = [
            "startDate": startDate,
            "endDate": endDate,
            "type": type
        ]

        NetworkManager.shared.request(
            endpoint: "/app-api/iot/hrv/sleep/getHistorySleepHRV",
            method: .get,
            parameters: parameters,
            responseType: SleepHistoryHRVData.self
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { result in
            if case .failure(let error) = result {
                completion(.failure(error))
            }
        }, receiveValue: { [weak self] data in
//                self?.hrvCache[cacheKey] = data
            completion(.success(data))
        })
        .store(in: &cancellables)
    }
    
    /// 获取睡眠SpO2
    /// - Parameters:
    ///   - startDate: 开始日期 (格式: yyyy-MM-dd)
    ///   - endDate: 结束日期 (格式: yyyy-MM-dd)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - forceRefresh: 是否强制刷新数据
    ///   - completion: 完成回调，返回结果或错误
    func getSleepSpO2(startDate: String, endDate: String, type: Int, forceRefresh: Bool = false, completion: @escaping (Result<SleepHistorySpO2Data, Error>) -> Void) {
        // 检查缓存
//            let cacheKey = getCacheKey(startDate: startDate, endDate: endDate, type: type)
//            if !forceRefresh, let cachedData = spO2Cache[cacheKey] {
//                completion(.success(cachedData))
//                return
//            }
        
        let parameters: [String: Any] = [
            "startDate": startDate,
            "endDate": endDate,
            "type": type
        ]
        
        NetworkManager.shared.request(
            endpoint: "/app-api/iot/blood/sleep/getHistorySleepSpO2",
            method: .get,
            parameters: parameters,
            responseType: SleepHistorySpO2Data.self
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { result in
            if case .failure(let error) = result {
                completion(.failure(error))
            }
        }, receiveValue: { [weak self] data in
//                self?.spO2Cache[cacheKey] = data
            completion(.success(data))
        })
        .store(in: &cancellables)
    }
    
    /// 获取睡眠皮肤温度
    /// - Parameters:
    ///   - startDate: 开始日期 (格式: yyyy-MM-dd)
    ///   - endDate: 结束日期 (格式: yyyy-MM-dd)
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    ///   - forceRefresh: 是否强制刷新数据
    ///   - completion: 完成回调，返回结果或错误
    func getSleepSkinTemp(startDate: String, endDate: String, type: Int, forceRefresh: Bool = false, completion: @escaping (Result<SleepHistorySkinTempData, Error>) -> Void) {
        // 检查缓存
//            let cacheKey = getCacheKey(startDate: startDate, endDate: endDate, type: type)
//            if !forceRefresh, let cachedData = skinTempCache[cacheKey] {
//                completion(.success(cachedData))
//                return
//            }
        
        let parameters: [String: Any] = [
            "startDate": startDate,
            "endDate": endDate,
            "type": type
        ]
        
        NetworkManager.shared.request(
            endpoint: "/app-api/iot/sleep/getHistorySkinTemp",
            method: .get,
            parameters: parameters,
            responseType: SleepHistorySkinTempData.self
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { result in
            if case .failure(let error) = result {
                completion(.failure(error))
            }
        }, receiveValue: { [weak self] data in
//                self?.skinTempCache[cacheKey] = data
            completion(.success(data))
        })
        .store(in: &cancellables)
    }
    
    /// 清除缓存
    func clearCache() {
        cache.removeAll()
        efficiencyCache.removeAll()
        timeAsleepCache.removeAll()
        heartRateCache.removeAll()
        hrvCache.removeAll()
        spO2Cache.removeAll()
        skinTempCache.removeAll()
    }
}
