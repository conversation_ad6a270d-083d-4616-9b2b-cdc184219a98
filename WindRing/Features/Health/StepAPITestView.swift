import SwiftUI
import Combine

struct StepAPITestView: View {
    // MARK: - 状态属性
    @State private var isLoading = false
    @State private var stepCount: Int = 0
    @State private var distance: Int = 0
    @State private var calories: Int = 0
    @State private var activeTime: Int = 0
    @State private var errorMessage: String? = nil
    @State private var lastUpdateTime: Date? = nil
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    
    // MARK: - 服务
    private let deviceService = WindRingDeviceService.shared
    private let activityUploadService = ActivityUploadService.shared
    
    // MARK: - 视图体
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 标题
                Text("步数API测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // 设备连接状态
                connectionStatusCard
                
                // 当前活动数据
                activityDataCard
                
                // 操作按钮
                actionButtonsCard
                
                // 日志
                logCard
                
                Spacer()
            }
            .padding()
        }
        .navigationBarTitle("步数API测试", displayMode: .inline)
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertMessage),
                dismissButton: .default(Text("确定"))
            )
        }
        .overlay(
            ZStack {
                if isLoading {
                    Color.black.opacity(0.3)
                        .edgesIgnoringSafeArea(.all)
                    
                    ProgressView()
                        .scaleEffect(1.5)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                }
            }
        )
    }
    
    // MARK: - 子视图
    
    // 设备连接状态卡片
    private var connectionStatusCard: some View {
        VStack {
            HStack {
                Image(systemName: deviceService.connectionState.isConnected ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(deviceService.connectionState.isConnected ? .green : .red)
                    .font(.title)
                
                Text(deviceService.connectionState.isConnected ? "设备connected".localized : "设备未连接")
                    .font(.headline)
                
                Spacer()
                
                if deviceService.connectionState.isConnected {
                    Text(deviceService.deviceInfo?.localName ?? "未知设备")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        }
    }
    
    // 活动数据卡片
    private var activityDataCard: some View {
        VStack {
            HStack {
                Text("当前活动数据")
                    .font(.headline)
                Spacer()
                Text(lastUpdateTime != nil ? formattedTime(lastUpdateTime!) : "未更新")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Divider()
            
            // 步数
            HStack {
                Image(systemName: "figure.walk")
                    .frame(width: 30, alignment: .center)
                Text("步数：")
                Spacer()
                Text("\(stepCount) 步")
                    .fontWeight(.semibold)
            }
            .padding(.vertical, 5)
            
            // 距离
            HStack {
                Image(systemName: "map")
                    .frame(width: 30, alignment: .center)
                Text("距离：")
                Spacer()
                Text("\(distance) 米")
                    .fontWeight(.semibold)
            }
            .padding(.vertical, 5)
            
            // 卡路里
            HStack {
                Image(systemName: "flame.fill")
                    .frame(width: 30, alignment: .center)
                Text("卡路里：")
                Spacer()
                Text("\(calories) 卡")
                    .fontWeight(.semibold)
            }
            .padding(.vertical, 5)
            
            // 活动时间
            HStack {
                Image(systemName: "clock")
                    .frame(width: 30, alignment: .center)
                Text("活动时间：")
                Spacer()
                Text("\(formatSeconds(activeTime))")
                    .fontWeight(.semibold)
            }
            .padding(.vertical, 5)
            
            if let error = errorMessage {
                Text(error)
                    .foregroundColor(.red)
                    .font(.caption)
                    .padding(.top, 5)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // 操作按钮卡片
    private var actionButtonsCard: some View {
        VStack {
            Text("操作")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Divider()
            
            Button(action: getStepData) {
                HStack {
                    Image(systemName: "arrow.down.doc.fill")
                    Text("获取步数数据")
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(isLoading || !deviceService.connectionState.isConnected)
            
            Button(action: syncStepData) {
                HStack {
                    Image(systemName: "arrow.up.arrow.down")
                    Text("同步步数数据")
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.green)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(isLoading || !deviceService.connectionState.isConnected)
            .padding(.top, 8)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // 日志卡片
    private var logCard: some View {
        VStack {
            Text("设备信息")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Divider()
            
            if deviceService.connectionState.isConnected {
                VStack(alignment: .leading, spacing: 8) {
                    Text("设备名称: \(deviceService.deviceInfo?.localName ?? "未知")")
                    Text("设备ID: \(deviceService.deviceInfo?.mac ?? "未知")")
                    Text("固件版本: \(deviceService.deviceInfo?.firmwareVersion ?? "未知")")
                    Text("电池电量: \(deviceService.batteryLevel)%")
                    Text("连接状态: \(deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized)")
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            } else {
                Text("设备未连接")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 方法
    
    // 获取步数数据
    private func getStepData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "设备未连接")
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        activityUploadService.getBasicActivityData { stepModel, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if let error = error {
                    errorMessage = "获取步数数据失败: \(error.localizedDescription)"
                    showAlert(title: "错误", message: errorMessage!)
                    return
                }
                
                if let model = stepModel {
                    stepCount = model.steps
                    distance = model.distance
                    calories = model.calory
                    activeTime = model.time
                    lastUpdateTime = Date()
                    
                    showAlert(title: "成功", message: "已成功获取最新步数数据")
                } else {
                    errorMessage = "获取数据为空"
                    showAlert(title: "错误", message: "获取的数据为空")
                }
            }
        }
    }
    
    // 同步步数数据
    private func syncStepData() {
        guard deviceService.connectionState.isConnected else {
            showAlert(title: "错误", message: "设备未连接")
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        activityUploadService.uploadCurrentActivityData { success, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if let error = error {
                    errorMessage = "同步步数数据失败: \(error.localizedDescription)"
                    showAlert(title: "错误", message: errorMessage!)
                    return
                }
                
                if success {
                    showAlert(title: "成功", message: "步数数据已成功同步到服务器")
                } else {
                    errorMessage = "同步失败"
                    showAlert(title: "错误", message: "同步失败，未知错误")
                }
            }
        }
    }
    
    // 显示提示框
    private func showAlert(title: String, message: String) {
        alertTitle = title
        alertMessage = message
        showAlert = true
    }
    
    // 格式化时间
    private func formattedTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }
    
    // 格式化秒数为时分秒
    private func formatSeconds(_ seconds: Int) -> String {
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        let secs = seconds % 60
        
        if hours > 0 {
            return "\(hours)小时 \(minutes)分钟 \(secs)秒"
        } else if minutes > 0 {
            return "\(minutes)分钟 \(secs)秒"
        } else {
            return "\(secs)秒"
        }
    }
} 
