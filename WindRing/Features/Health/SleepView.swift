import SwiftUI
import CRPSmartRing

/// 睡眠监测视图 - 用于查看睡眠质量和周期
struct SleepView: View {
    // MARK: - 状态属性
    @StateObject private var deviceService = WindRingDeviceService.shared
    @State private var selectedDate = Date()
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var sleepData: CRPSleepRecordModel? = nil
    @State private var selectedTab = 0 // 0: 概览, 1: 详细
    
    // MARK: - 主视图
    var body: some View {
        VStack {
            // 日期选择器
            DatePicker(
                "选择日期",
                selection: $selectedDate,
                displayedComponents: .date
            )
            .datePickerStyle(CompactDatePickerStyle())
            .padding(.horizontal)
            .onChange(of: selectedDate) { _ in
                fetchSleepData()
            }
            
            // 分段控制器
            Picker("数据查看模式", selection: $selectedTab) {
                Text("睡眠概览").tag(0)
                Text("详细数据").tag(1)
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.horizontal)
            
            if isLoading {
                // 加载中
                ProgressView("正在获取睡眠数据...")
                    .padding()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if let error = errorMessage {
                // 错误提示
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)
                    
                    Text(error)
                        .multilineTextAlignment(.center)
                    
                    Button("重试") {
                        fetchSleepData()
                    }
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if let data = sleepData {
                // 数据视图
                TabView(selection: $selectedTab) {
                    // 睡眠概览
                    sleepOverviewView(data: data)
                        .tag(0)
                    
                    // 详细数据
                    sleepDetailView(data: data)
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            } else {
                // 无数据提示
                VStack(spacing: 20) {
                    Image(systemName: "bed.double")
                        .font(.system(size: 60))
                        .foregroundColor(.blue.opacity(0.8))
                    
                    Text("没有睡眠数据")
                        .font(.title3)
                    
                    Text("请确保您的设备已连接并且在睡眠期间佩戴")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    if !deviceService.connectionState.isConnected {
                        NavigationLink(destination: DevicePairingView()) {
                            Text("连接设备")
                                .padding(.horizontal, 20)
                                .padding(.vertical, 10)
                                .background(Color.blue)
                                .foregroundColor(.white)
                                .cornerRadius(8)
                        }
                        .padding(.top, 10)
                    } else {
                        Button("刷新") {
                            fetchSleepData()
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .navigationTitle("睡眠监测")
        .onAppear {
            fetchSleepData()
        }
        .alert(item: Binding<AlertData?>(
            get: { errorMessage != nil ? AlertData(id: 0, message: errorMessage!) : nil },
            set: { if $0 == nil { errorMessage = nil } }
        )) { alertData in
            Alert(
                title: Text("获取数据失败"),
                message: Text(alertData.message),
                dismissButton: .default(Text("确定"))
            )
        }
    }
    
    // MARK: - 子视图
    
    // 睡眠概览视图
    private func sleepOverviewView(data: CRPSleepRecordModel) -> some View {
        ScrollView {
            VStack(spacing: 20) {
                // 睡眠总结卡片
                sleepSummaryCard(data: data)
                
                // 睡眠阶段分布
                sleepStagesCard(data: data)
                
                // 睡眠质量评分
                sleepQualityCard(data: data)
            }
            .padding()
        }
    }
    
    // 睡眠总结卡片
    private func sleepSummaryCard(data: CRPSleepRecordModel) -> some View {
        // 计算总睡眠时间（分钟）
        let totalSleepMinutes = data.deep + data.light + data.rem
        let hours = totalSleepMinutes / 60
        let minutes = totalSleepMinutes % 60
        
        return VStack(alignment: .leading, spacing: 15) {
            Text("睡眠总结")
                .font(.headline)
            
            HStack(spacing: 25) {
                VStack {
                    Text("\(hours)小时\(minutes)分钟")
                        .font(.title2)
                        .foregroundColor(.primary)
                    
                    Text("总睡眠时间")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                
                Divider()
                    .frame(height: 40)
                
                VStack {
                    // 根据深睡比例计算出0-100的睡眠质量分数
                    let sleepQuality = min(100, max(0, Int(Double(data.deep) / Double(max(1, totalSleepMinutes)) * 100) + 50))
                    Text("\(sleepQuality)")
                        .font(.title2)
                        .foregroundColor(.primary)
                    
                    Text("质量评分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
            }
            .padding()
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
        }
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // 睡眠阶段卡片
    private func sleepStagesCard(data: CRPSleepRecordModel) -> some View {
        let totalMinutes = max(1, data.deep + data.light + data.rem)
        let deepPercent = Double(data.deep) / Double(totalMinutes)
        let lightPercent = Double(data.light) / Double(totalMinutes)
        let remPercent = Double(data.rem) / Double(totalMinutes)
        
        return VStack(alignment: .leading, spacing: 15) {
            Text("睡眠阶段")
                .font(.headline)
            
            // 睡眠阶段图表
            HStack(spacing: 0) {
                Rectangle()
                    .fill(Color.indigo)
                    .frame(width: CGFloat(deepPercent) * UIScreen.main.bounds.width * 0.8)
                
                Rectangle()
                    .fill(Color.blue.opacity(0.7))
                    .frame(width: CGFloat(lightPercent) * UIScreen.main.bounds.width * 0.8)
                
                Rectangle()
                    .fill(Color.purple.opacity(0.7))
                    .frame(width: CGFloat(remPercent) * UIScreen.main.bounds.width * 0.8)
            }
            .frame(height: 24)
            .cornerRadius(6)
            
            // 图例和数据
            HStack(spacing: 20) {
                // 深睡
                sleepStageItem(
                    title: "深睡",
                    minutes: data.deep,
                    percent: Int(deepPercent * 100),
                    color: .indigo
                )
                
                // 浅睡
                sleepStageItem(
                    title: "浅睡",
                    minutes: data.light,
                    percent: Int(lightPercent * 100),
                    color: .blue.opacity(0.7)
                )
                
                // REM
                sleepStageItem(
                    title: "REM",
                    minutes: data.rem,
                    percent: Int(remPercent * 100),
                    color: .purple.opacity(0.7)
                )
            }
        }
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // 睡眠阶段项
    private func sleepStageItem(title: String, minutes: Int, percent: Int, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack(spacing: 8) {
                Circle()
                    .fill(color)
                    .frame(width: 12, height: 12)
                
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Text("\(minutes)分钟")
                .font(.headline)
            
            Text("\(percent)%")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // 睡眠质量卡片
    private func sleepQualityCard(data: CRPSleepRecordModel) -> some View {
        let totalMinutes = data.deep + data.light + data.rem
        let deepRatio = Double(data.deep) / Double(max(1, totalMinutes))
        let remRatio = Double(data.rem) / Double(max(1, totalMinutes))
        
        // 计算睡眠质量评分 (0-100)
        let qualityScore = min(100, max(0, Int(deepRatio * 100) + Int(remRatio * 30) + 40))
        
        // 睡眠质量等级
        let (qualityLevel, qualityColor) = sleepQualityLevel(score: qualityScore)
        
        return VStack(alignment: .leading, spacing: 15) {
            Text("睡眠质量分析")
                .font(.headline)
            
            HStack {
                // 分数圆环
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.2), lineWidth: 10)
                        .frame(width: 100, height: 100)
                    
                    Circle()
                        .trim(from: 0, to: CGFloat(qualityScore) / 100.0)
                        .stroke(qualityColor, lineWidth: 10)
                        .frame(width: 100, height: 100)
                        .rotationEffect(.degrees(-90))
                    
                    VStack {
                        Text("\(qualityScore)")
                            .font(.title)
                            .fontWeight(.bold)
                        
                        Text("分")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.trailing, 10)
                
                VStack(alignment: .leading, spacing: 6) {
                    Text(qualityLevel)
                        .font(.title3)
                        .foregroundColor(qualityColor)
                    
                    Text(sleepQualityDescription(score: qualityScore))
                        .font(.callout)
                        .foregroundColor(.secondary)
                }
            }
            
            // 睡眠建议
            VStack(alignment: .leading, spacing: 8) {
                Text("睡眠建议")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text(sleepAdvice(data: data))
                    .font(.callout)
                    .foregroundColor(.primary)
            }
            .padding(.top, 10)
        }
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    // 详细数据视图
    private func sleepDetailView(data: CRPSleepRecordModel) -> some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 详细睡眠记录
                if !data.detail.isEmpty {
                    Text("睡眠阶段记录")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    VStack(spacing: 0) {
                        // 表头
                        HStack {
                            Text("阶段")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(width: 80, alignment: .leading)
                            
                            Text("时长")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(width: 60, alignment: .leading)
                            
                            Spacer()
                            
                            Text("时间段")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 8)
                        .background(Color(UIColor.secondarySystemBackground))
                        
                        Divider()
                        
                        // 表内容
                        ForEach(0..<min(data.detail.count, 30), id: \.self) { index in
                            let record = data.detail[index]
                            sleepRecordRow(record: record)
                            
                            if index < min(data.detail.count, 30) - 1 {
                                Divider()
                            }
                        }
                        
                        if data.detail.count > 30 {
                            Divider()
                            Text("...等\(data.detail.count - 30)条记录")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(8)
                        }
                    }
                    .background(Color(UIColor.systemBackground))
                    .cornerRadius(12)
                    .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 1)
                    .padding(.horizontal)
                } else {
                    Text("无详细睡眠阶段记录")
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                }
            }
            .padding(.vertical)
        }
    }
    
    // 详细数据行
    private func sleepRecordRow(record: [String: String]) -> some View {
        HStack {
            // 睡眠类型
            if let typeStr = record["type"], let type = Int(typeStr) {
                HStack(spacing: 4) {
                    Circle()
                        .fill(sleepTypeColor(type))
                        .frame(width: 12, height: 12)
                    
                    Text(sleepTypeString(type))
                        .font(.subheadline)
                }
                .frame(width: 80, alignment: .leading)
            } else {
                Text("未知")
                    .font(.subheadline)
                    .frame(width: 80, alignment: .leading)
            }
            
            // 时长
            if let durationStr = record["duration"], let duration = Int(durationStr) {
                Text("\(duration)分钟")
                    .font(.subheadline)
                    .frame(width: 60, alignment: .leading)
            } else if let totalStr = record["total"], let total = Int(totalStr) {
                Text("\(total)分钟")
                    .font(.subheadline)
                    .frame(width: 60, alignment: .leading)
            } else {
                Text("--")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(width: 60, alignment: .leading)
            }
            
            Spacer()
            
            // 时间戳
            VStack(alignment: .trailing) {
                if let startStr = record["start"], let endStr = record["end"],
                   let startTs = TimeInterval(startStr), let endTs = TimeInterval(endStr) {
                    let startDate = Date(timeIntervalSince1970: startTs)
                    let endDate = Date(timeIntervalSince1970: endTs)
                    
                    Text("\(formatTime(startDate)) - \(formatTime(endDate))")
                        .font(.subheadline)
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 10)
    }
    
    // MARK: - 辅助方法
    
    // 获取睡眠数据
    private func fetchSleepData() {
        isLoading = true
        errorMessage = nil
        sleepData = nil
        
        // 计算当前日期与选择日期的天数差
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let selectedDay = calendar.startOfDay(for: selectedDate)
        let dayOffset = calendar.dateComponents([.day], from: selectedDay, to: today).day ?? 0
        
        // 确保dayOffset不为负数
        let positiveDayOffset = max(0, dayOffset)
        
        // 通过SDK获取睡眠数据
        CRPSmartRingSDK.sharedInstance.getSleepData(positiveDayOffset) { [self] model, error in
            DispatchQueue.main.async {
                isLoading = false
                
                if error == .none {
                    sleepData = model
                } else {
                    // 处理错误情况
                    switch error {
                    case .disconnected:
                        errorMessage = "设备未连接，请先连接您的设备"
                    case .timeout:
                        errorMessage = "获取数据超时，请重试"
                    default:
                        // 如果没有数据，显示空状态而不是错误
                        if model == nil {
                            errorMessage = nil // 无数据时不显示错误，只显示空状态
                            sleepData = nil
                        } else if model.deep == 0 && model.light == 0 && model.rem == 0 {
                            // 有模型但所有睡眠数据都为0，视为无数据
                            errorMessage = nil
                            sleepData = nil
                        } else {
                            errorMessage = "获取数据失败：\(error)"
                        }
                    }
                }
            }
        }
    }
    
    // 睡眠类型转字符串
    private func sleepTypeString(_ type: Int) -> String {
        switch type {
        case 0: return "清醒"
        case 1: return "浅睡"
        case 2: return "深睡"
        case 3: return "REM"
        default: return "未知"
        }
    }
    
    // 睡眠类型对应颜色
    private func sleepTypeColor(_ type: Int) -> Color {
        switch type {
        case 0: return Color.orange
        case 1: return Color.blue.opacity(0.7)
        case 2: return Color.indigo
        case 3: return Color.purple.opacity(0.7)
        default: return Color.gray
        }
    }
    
    // 睡眠质量等级
    private func sleepQualityLevel(score: Int) -> (String, Color) {
        switch score {
        case 0..<50:
            return ("较差", Color.orange)
        case 50..<70:
            return ("一般", Color.yellow)
        case 70..<85:
            return ("良好", Color.green)
        case 85...100:
            return ("优秀", Color.blue)
        default:
            return ("未知", Color.gray)
        }
    }
    
    // 睡眠质量描述
    private func sleepQualityDescription(score: Int) -> String {
        switch score {
        case 0..<50:
            return "您的睡眠质量较差，深睡眠比例不足"
        case 50..<70:
            return "您的睡眠质量一般，可以适当提高"
        case 70..<85:
            return "您的睡眠质量良好，深睡眠比例适中"
        case 85...100:
            return "您的睡眠质量优秀，各睡眠阶段分配合理"
        default:
            return "无法评估睡眠质量"
        }
    }
    
    // 睡眠建议
    private func sleepAdvice(data: CRPSleepRecordModel) -> String {
        let totalMinutes = data.deep + data.light + data.rem
        
        // 睡眠总时长不足
        if totalMinutes < 360 { // 6小时
            return "您的睡眠时间不足，建议保持7-8小时的睡眠时长，有助于身体恢复和精力充沛。"
        }
        
        // 深睡眠不足
        let deepPercent = Double(data.deep) / Double(max(1, totalMinutes))
        if deepPercent < 0.2 { // 深睡眠比例低于20%
            return "您的深睡眠比例偏低，建议调整作息规律，避免睡前饮酒、咖啡因摄入，创造安静黑暗的睡眠环境。"
        }
        
        // REM睡眠不足
        let remPercent = Double(data.rem) / Double(max(1, totalMinutes))
        if remPercent < 0.15 { // REM睡眠比例低于15%
            return "您的REM睡眠比例偏低，适当放松心情，睡前避免使用电子设备，保持良好的睡眠习惯有助于提高REM睡眠质量。"
        }
        
        // 睡眠质量良好
        return "您的睡眠状况良好，请继续保持规律的作息时间和良好的睡前习惯，享受高质量的睡眠。"
    }
    
    // 格式化时间
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

// MARK: - 辅助结构体
struct AlertData: Identifiable {
    let id: Int
    let message: String
}

// MARK: - 预览
struct SleepView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            SleepView()
        }
    }
} 
