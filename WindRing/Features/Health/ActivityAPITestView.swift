import SwiftUI
import Combine
import CRPSmartRing
import Charts

/// 活动API测试视图模型
class ActivityAPITestViewModel: ObservableObject {
    // MARK: - 属性
    @Published var results: [ActivityTestResult] = []
    @Published var isLoading: Bool = false
    @Published var selectedDay: Int = 0
    @Published var activityData: ActivityData? = nil
    @Published var showShareSheet: Bool = false
    @Published var shareText: String = ""
    
    // 步数明细相关
    @Published var stepDetails: [ActivityDetailData] = []
    @Published var isLoadingStepDetails: Bool = false
    @Published var showStepDetails: Bool = false
    
    // 提供多天选择
    let dayOptions = ["今天", "昨天", "前天", "3天前", "4天前", "5天前", "6天前"]
    
    // 支持自动上传的最大天数（包括今天）
    let maxAutoUploadDays = 6
    
    // 设备服务
    let deviceService = WindRingDeviceService.shared
    
    // 单例
    static let shared = ActivityAPITestViewModel()
    
    // MARK: - 初始化方法
    private init() {}
    
    // MARK: - 活动数据获取方法
    
    /// 获取指定日期的活动数据
    func fetchActivityData(day: Int) {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取活动数据", color: .red)
            return
        }
        
        // 检查是否超过最大自动上传天数
        if day >= maxAutoUploadDays {
            addResult("警告: 只支持获取最近\(maxAutoUploadDays)天的数据（包括今天）", color: .orange)
            return
        }
        
        isLoading = true
        addResult("正在获取\(dayOptions[day])的活动数据...", color: .blue)
        
        CRPSmartRingSDK.sharedInstance.getTrainingData(day) { [weak self] record, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if error == .none {
                    // 创建活动数据模型
                    let data = ActivityData(
                        day: day,
                        steps: record.step,
                        distance: record.distance,
                        calories: record.cal,
                        exerciseTime: record.exerciseTime
                    )
                    
                    self?.activityData = data
                    
                    // 添加测试结果
                    self?.addResult("成功获取\(self?.dayOptions[day] ?? "")的活动数据:", color: .green)
                    self?.addResult("· 步数: \(record.step) 步", color: .primary)
                    
                    // 距离转换为千米，保留2位小数
                    let distanceKm = Double(record.distance) / 100000.0 // 将厘米转换为千米
                    let formattedDistance = String(format: "%.2f", distanceKm)
                    
                    // 打印更详细的距离数据信息用于调试
                    print("===活动API测试页面距离数据===")
                    print("原始距离值(厘米): \(record.distance)")
                    print("转换公式: 原始值/100000.0 (厘米转公里)")
                    print("转换后距离值(公里): \(distanceKm)")
                    print("格式化后距离值(公里): \(formattedDistance)")
                    print("===========================")
                    
                    self?.addResult("· 距离: \(formattedDistance) 公里", color: .primary)
                    
                    // 卡路里
                    self?.addResult("· 卡路里: \(record.cal) 千卡", color: .primary)
                    
                    // 运动时间
                    self?.addResult("· 运动时间: \(record.exerciseTime) 分钟", color: .primary)
                    
                    // 获取步数明细数据
                    self?.fetchStepDetails(day: day)
                } else {
                    self?.activityData = nil
                    
                    // 错误处理
                    var errorMessage = "获取活动数据失败"
                    switch error {
                    case .disconnected:
                        errorMessage += ": 设备已断开连接"
                    case .timeout:
                        errorMessage += ": 请求超时"
                    case .busy:
                        errorMessage += ": 设备忙"
                    case .interrupted:
                        errorMessage += ": 操作被中断"
                    case .internalError:
                        errorMessage += ": 内部错误"
                    case .noCentralManagerSet:
                        errorMessage += ": 蓝牙管理器未设置"
                    case .other:
                        errorMessage += ": 其他错误"
                    default:
                        break
                    }
                    
                    self?.addResult(errorMessage, color: .red)
                }
            }
        }
    }
    
    // MARK: - 步数明细获取方法
    
    /// 获取指定日期的步数明细数据
    func fetchStepDetails(day: Int) {
        guard deviceService.connectionState.isConnected else {
            addResult("设备未连接，无法获取步数明细数据", color: .red)
            return
        }
        
        // 检查是否超过最大自动上传天数
        if day >= maxAutoUploadDays {
            addResult("警告: 只支持获取最近\(maxAutoUploadDays)天的数据（包括今天）", color: .orange)
            return
        }
        
        isLoadingStepDetails = true
        addResult("正在获取\(dayOptions[day])的步数明细数据...", color: .blue)
        
        RawDataUploadService.shared.fetchStepDetailData(day: day) { [weak self] details, error in
            DispatchQueue.main.async {
                self?.isLoadingStepDetails = false
                
                if let error = error {
                    self?.addResult("获取步数明细失败: \(error.localizedDescription)", color: .red)
                    self?.stepDetails = []
                    return
                }
                
                guard let details = details, !details.isEmpty else {
                    self?.addResult("步数明细数据为空", color: .orange)
                    self?.stepDetails = []
                    return
                }
                
                self?.stepDetails = details
                let totalStepsInDetail = details.map { $0.steps }.reduce(0, +)
                self?.addResult("成功获取步数明细数据，共\(details.count)条记录，总步数\(totalStepsInDetail)步", color: .green)
                
                // 如果明细数据获取成功，自动显示明细数据
                self?.showStepDetails = true
            }
        }
    }
    
    // MARK: - 结果管理方法
    
    /// 添加测试结果
    func addResult(_ message: String, color: Color = .primary) {
        let result = ActivityTestResult(message: message, color: color)
        DispatchQueue.main.async {
            self.results.insert(result, at: 0)
        }
    }
    
    /// 清除结果
    func clearResults() {
        DispatchQueue.main.async {
            self.results.removeAll()
            self.activityData = nil
            self.stepDetails = []
            self.showStepDetails = false
        }
    }
    
    /// 导出测试结果
    func exportResults() {
        var text = "活动API测试结果\n"
        text += "测试时间: \(Date())\n"
        text += "设备连接状态: \(deviceService.connectionState.description)\n\n"
        text += "当前选择日期: \(dayOptions[selectedDay])\n\n"
        
        if let data = activityData {
            text += "活动数据:\n"
            text += "- 步数: \(data.steps) 步\n"
            
            let distanceKm = Double(data.distance) / 100000.0
            let formattedDistance = String(format: "%.2f", distanceKm)
            text += "- 距离: \(formattedDistance) 公里\n"
            
            text += "- 卡路里: \(data.calories) 千卡\n"
            text += "- 运动时间: \(data.exerciseTime) 分钟\n\n"
        }
        
        if !stepDetails.isEmpty {
            text += "步数明细数据:\n"
            let maxItems = min(stepDetails.count, 10) // 最多显示10条记录
            for i in 0..<maxItems {
                let detail = stepDetails[i]
                text += "- \(detail.formattedTime()): \(detail.steps) 步\n"
            }
            if stepDetails.count > 10 {
                text += "... (共\(stepDetails.count)条记录)\n\n"
            } else {
                text += "\n"
            }
        }
        
        text += "测试记录:\n"
        for (index, result) in results.enumerated() {
            text += "\(index + 1). \(result.message)\n"
        }
        
        shareText = text
        showShareSheet = true
    }
    
    /// 生成活动明细的JSON字符串
    func generateActivityDetailJSON() -> String {
        guard let data = activityData, !stepDetails.isEmpty else {
            return "无可用数据"
        }
        
        // 将时间戳转为ISO字符串的辅助函数
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let calendar = Calendar.current
        let today = Date()
        let date = calendar.date(byAdding: .day, value: -selectedDay, to: today) ?? today
        let dateString = dateFormatter.string(from: date)
        
        // 构建records数组
        let records = stepDetails.map { detail -> [String: Any] in
            return [
                "steps": detail.steps,
                "time": detail.time
            ]
        }
        
        // 构建完整的JSON对象
        let jsonObject: [String: Any] = [
            "date": dateString,
            "steps": data.steps,
            "calories": data.calories,
            "time": data.exerciseTime * 60, // 分钟转秒
            "distance": data.distance / 100, // 厘米转米
            "records": records
        ]
        
        // 转换为格式化的JSON字符串
        if let jsonData = try? JSONSerialization.data(withJSONObject: jsonObject, options: [.prettyPrinted]),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            return jsonString
        } else {
            return "JSON转换失败"
        }
    }
}

// MARK: - 活动数据模型
struct ActivityData:Codable {
    let day: Int
    let steps: Int
    let distance: Int // 以厘米为单位
    let calories: Int
    let exerciseTime: Int // 以分钟为单位
}

// MARK: - 测试结果数据模型
struct ActivityTestResult: Identifiable {
    let id = UUID()
    let message: String
    let color: Color
    let timestamp = Date()
}

// MARK: - 活动API测试视图
struct ActivityAPITestView: View {
    @StateObject private var viewModel = ActivityAPITestViewModel.shared
    @State private var showRawJSON: Bool = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 连接状态
                connectionStatusView
                
                // 日期选择器
                daySelectionView
                
                // 上传天数提示
                HStack {
                    Image(systemName: "info.circle.fill")
                        .foregroundColor(.blue)
                    Text("自动上传仅支持最近\(viewModel.maxAutoUploadDays)天的数据（包括今天）")
                        .font(.footnote)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .padding(.horizontal, 8)
                
                // 活动数据显示
                if let data = viewModel.activityData {
                    activityDataView(data)
                }
                
                // 步数明细数据
                if viewModel.showStepDetails && !viewModel.stepDetails.isEmpty {
                    stepDetailsView
                }
                
                // 操作按钮
                actionsView
                
                // JSON数据显示
                if showRawJSON && !viewModel.stepDetails.isEmpty {
                    rawJSONView
                }
                
                Divider()
                    .padding(.vertical)
                
                // 测试结果
                resultsView
            }
            .padding()
        }
        .navigationTitle("活动API测试")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { viewModel.exportResults() }) {
                    Image(systemName: "square.and.arrow.up")
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { viewModel.clearResults() }) {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                }
            }
        }
        .overlay(
            ZStack {
                if viewModel.isLoading || viewModel.isLoadingStepDetails {
                    Color.black.opacity(0.3)
                        .edgesIgnoringSafeArea(.all)
                    
                    VStack {
                        ProgressView()
                            .scaleEffect(1.5)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        
                        Text("加载中...")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.top, 10)
                    }
                    .padding(20)
                    .background(Color.black.opacity(0.7))
                    .cornerRadius(10)
                }
            }
        )
        .sheet(isPresented: $viewModel.showShareSheet) {
            ActivityShareView(text: viewModel.shareText)
        }
        .onAppear {
            // 初次加载页面时自动获取当天数据
            if viewModel.results.isEmpty {
                viewModel.fetchActivityData(day: 0)
            }
        }
    }
    
    // MARK: - 子视图
    
    // 连接状态视图
    private var connectionStatusView: some View {
        HStack {
            Circle()
                .fill(viewModel.deviceService.connectionState.isConnected ? Color.green : Color.red)
                .frame(width: 12, height: 12)
            
            Text(viewModel.deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized)
                .font(.subheadline)
            
            Spacer()
            
            if !viewModel.deviceService.connectionState.isConnected {
                NavigationLink(destination: DeviceManagerView()) {
                    Text("连接设备")
                        .font(.caption)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
    
    // 日期选择视图
    private var daySelectionView: some View {
        VStack(alignment: .leading) {
            Text("选择日期")
                .font(.headline)
                .padding(.bottom, 5)
            
            Picker("选择日期", selection: $viewModel.selectedDay) {
                ForEach(0..<viewModel.dayOptions.count, id: \.self) { index in
                    Text(viewModel.dayOptions[index]).tag(index)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .onChange(of: viewModel.selectedDay) { _ in
                viewModel.fetchActivityData(day: viewModel.selectedDay)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
    
    // 活动数据显示视图
    private func activityDataView(_ data: ActivityData) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("活动数据")
                .font(.headline)
                .padding(.bottom, 5)
            
            // 步数
            HStack {
                Image(systemName: "figure.walk")
                    .frame(width: 30)
                VStack(alignment: .leading) {
                    Text("步数")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    Text("\(data.steps)")
                        .font(.title2)
                        .fontWeight(.bold)
                }
            }
            
            // 距离
            HStack {
                Image(systemName: "map")
                    .frame(width: 30)
                VStack(alignment: .leading) {
                    Text("距离")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    
                    let distanceKm = Double(data.distance) / 100000.0 // 将厘米转换为千米
                    let formattedDistance = String(format: "%.2f", distanceKm)
                    Text("\(formattedDistance) 公里")
                        .font(.title2)
                        .fontWeight(.bold)
                }
            }
            
            // 卡路里
            HStack {
                Image(systemName: "flame.fill")
                    .frame(width: 30)
                VStack(alignment: .leading) {
                    Text("卡路里")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    let displayCalories = Int(Double(data.calories) / 10000.0)
                    Text("\(displayCalories) 千卡")
                        .font(.title2)
                        .fontWeight(.bold)
                }
            }
            
            // 运动时间
            HStack {
                Image(systemName: "clock")
                    .frame(width: 30)
                VStack(alignment: .leading) {
                    Text("运动时间")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    Text("\(data.exerciseTime) 分钟")
                        .font(.title2)
                        .fontWeight(.bold)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
    
    // 步数明细视图
    private var stepDetailsView: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Text("步数明细")
                    .font(.headline)
                
                Spacer()
                
                // JSON格式切换按钮
                Button(action: { showRawJSON.toggle() }) {
                    HStack {
                        Image(systemName: showRawJSON ? "list.bullet" : "curlybraces")
                            .imageScale(.small)
                        Text(showRawJSON ? "列表视图" : "JSON格式")
                            .font(.caption)
                    }
                    .padding(.horizontal, 10)
                    .padding(.vertical, 5)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding(.bottom, 5)
            
            if showRawJSON {
                // 当选择JSON视图时在stepDetailsView里不显示内容，在rawJSONView中显示
                Text("请参见下方JSON数据显示")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .padding(.bottom, 5)
            } else {
                // 步数时间线图表
                if !viewModel.stepDetails.isEmpty {
                    stepDetailsChart
                        .frame(height: 180)
                        .padding(.vertical, 5)
                }
                
                // 步数明细列表
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(viewModel.stepDetails.sorted(by: { $0.time > $1.time })) { detail in
                            HStack {
                                Text(detail.formattedTime())
                                    .font(.system(size: 14))
                                    .frame(width: 70, alignment: .leading)
                                
                                Spacer()
                                
                                Text("\(detail.steps)")
                                    .font(.system(size: 14))
                                    .foregroundColor(.blue)
                                    .frame(width: 60, alignment: .trailing)
                                
                                Text("步")
                                    .font(.system(size: 14))
                                    .foregroundColor(.gray)
                                    .frame(width: 20, alignment: .leading)
                            }
                            .padding(.vertical, 8)
                            .padding(.horizontal, 15)
                            
                            Divider()
                        }
                    }
                }
                .frame(height: min(CGFloat(viewModel.stepDetails.count) * 40, 200))
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
    
    // 步数明细图表
    private var stepDetailsChart: some View {
        let sortedDetails = viewModel.stepDetails.sorted(by: { $0.time < $1.time })
        
        return Chart {
            ForEach(sortedDetails) { detail in
                BarMark(
                    x: .value("时间", detail.formattedTime()),
                    y: .value("步数", detail.steps)
                )
                .foregroundStyle(Color.blue.gradient)
            }
        }
        .chartXAxis {
            AxisMarks(values: .automatic) { value in
                if value.index % 3 == 0 {
                    AxisValueLabel()
                }
                AxisGridLine()
                AxisTick()
            }
        }
    }
    
    // JSON数据展示视图
    private var rawJSONView: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Text("活动JSON数据")
                    .font(.headline)
                
                Spacer()
            }
            
            ScrollView {
                Text(viewModel.generateActivityDetailJSON())
                    .font(.system(.caption, design: .monospaced))
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .frame(height: 300)
            .background(Color.black.opacity(0.03))
            .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
    
    // 操作按钮视图
    private var actionsView: some View {
        VStack(spacing: 10) {
            Button(action: {
                viewModel.fetchActivityData(day: viewModel.selectedDay)
            }) {
                Text("刷新活动数据")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(10)
            }
            .disabled(viewModel.isLoading || viewModel.isLoadingStepDetails)
            
            // 仅当有步数明细数据时才显示切换按钮
            if !viewModel.stepDetails.isEmpty {
                Button(action: {
                    withAnimation {
                        viewModel.showStepDetails.toggle()
                    }
                }) {
                    HStack {
                        Text(viewModel.showStepDetails ? "隐藏步数明细" : "显示步数明细")
                        Image(systemName: viewModel.showStepDetails ? "chevron.up" : "chevron.down")
                    }
                    .font(.subheadline)
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(10)
                }
            }
        }
    }
    
    // 测试结果视图
    private var resultsView: some View {
        VStack(alignment: .leading) {
            Text("测试结果")
                .font(.headline)
                .padding(.bottom, 5)
            
            if viewModel.results.isEmpty {
                Text("暂无测试结果")
                    .foregroundColor(.gray)
                    .padding()
                    .frame(maxWidth: .infinity)
            } else {
                ForEach(viewModel.results) { result in
                    VStack(alignment: .leading, spacing: 4) {
                        Text(result.message)
                            .foregroundColor(result.color)
                        
                        Text(result.timestamp, style: .time)
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    .padding(.vertical, 4)
                    
                    Divider()
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
}

// MARK: - 共享视图
struct ActivityShareView: UIViewControllerRepresentable {
    let text: String
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: [text],
            applicationActivities: nil
        )
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - 预览
struct ActivityAPITestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ActivityAPITestView()
        }
    }
} 
