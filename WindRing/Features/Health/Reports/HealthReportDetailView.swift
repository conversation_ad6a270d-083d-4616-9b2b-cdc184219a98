import SwiftUI
import Charts

/// 健康报告详情视图
struct HealthReportDetailView: View {
    // MARK: - 属性
    let report: HRMReport
    
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab = 0
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                CommonNavigationBar(
                    title: report.title,
                    rightItems: [
                        NavigationBarItem(
                            systemImage: "ellipsis.circle",
                            action: {
                                // 在这里添加菜单功能
                            }
                        )
                    ]
                )
                
                ScrollView {
                    VStack(alignment: .leading, spacing: 16) {
                        // 报告标题和日期
                        reportHeaderView
                        
                        // 报告内容
                        reportContentView
                    }
                    .padding()
                }
            }
            .background(Color.appBackground)
        }
        .background(Color.moduleBackground)
        .cornerRadius(8)
    }
    
    // MARK: - 报告标题视图
    private var reportHeaderView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(report.title)
                .font(.title)
                .fontWeight(.bold)
            
            HStack {
                Image(systemName: "calendar")
                    .foregroundColor(.secondary)
                
                Text(formatDateRange(start: report.startDate, end: report.endDate))
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Divider()
                .padding(.vertical, 8)
        }
    }
    
    // MARK: - 报告内容视图
    private var reportContentView: some View {
        VStack(alignment: .leading, spacing: 24) {
            ForEach(report.sections.indices, id: \.self) { index in
                let section = report.sections[index]
                
                ReportSectionView(section: section)
                    .id(index)
                
                if index < report.sections.count - 1 {
                    Divider()
                }
            }
        }
    }
    
    // MARK: - 操作方法
    
    /// 分享报告
    private func shareReport() {
        // 待实现：分享功能
        print("Share report: \(report.id)")
    }
    
    /// 保存为PDF
    private func saveAsPDF() {
        // 待实现：PDF导出功能
        print("Save report as PDF: \(report.id)")
    }
    
    // MARK: - 辅助方法
    
    /// 格式化日期范围
    private func formatDateRange(start: Date, end: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        
        return "\(dateFormatter.string(from: start)) - \(dateFormatter.string(from: end))"
    }
}

/// 报告部分视图
struct ReportSectionView: View {
    // MARK: - 属性
    let section: HRMSection
    
    @State private var isExpanded: Bool = true
    
    // MARK: - 主视图
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 部分标题
            HStack {
                Text(section.title)
                    .font(.headline)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button(action: { isExpanded.toggle() }) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.secondary)
                }
            }
            
            if isExpanded {
                // 展示内容
                if !section.content.isEmpty {
                    Text(section.content)
                        .font(.body)
                        .foregroundColor(.primary)
                        .fixedSize(horizontal: false, vertical: true)
                }
                
                // 展示数据点
                if let dataPoints = section.dataPoints, !dataPoints.isEmpty {
                    dataPointsView(dataPoints)
                }
                
                // 展示图表
                if let charts = section.charts, !charts.isEmpty {
                    chartsView(charts)
                }
            }
        }
    }
    
    // MARK: - 数据点视图
    private func dataPointsView(_ dataPoints: [HRMDataPoint]) -> some View {
        LazyVGrid(columns: [
            GridItem(.flexible(), spacing: 16),
            GridItem(.flexible(), spacing: 16)
        ], spacing: 16) {
            ForEach(dataPoints.indices, id: \.self) { index in
                let dataPoint = dataPoints[index]
                
                DataPointView(dataPoint: dataPoint)
            }
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - 图表视图
    private func chartsView(_ charts: [HRMChartData]) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            ForEach(charts.indices, id: \.self) { index in
                let chart = charts[index]
                
                ChartView(chartData: chart)
                    .frame(height: 240)
                    .padding(.vertical, 8)
            }
        }
    }
}

/// 数据点视图
struct DataPointView: View {
    // MARK: - 属性
    let dataPoint: HRMDataPoint
    
    // MARK: - 主视图
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(dataPoint.label)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Text(dataPoint.value)
                .font(.headline)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(12)
        .background(Color.moduleBackground)
        .cornerRadius(8)
    }
}

/// 图表视图
struct ChartView: View {
    // MARK: - 属性
    let chartData: HRMChartData
    
    // MARK: - 主视图
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(chartData.title)
                .font(.headline)
            
            chartContent
        }
    }
    
    // MARK: - 图表内容
    private var chartContent: some View {
        Group {
            switch chartData.type {
            case .line:
                lineChartView
            case .bar:
                barChartView
            case .pie:
                pieChartView
            case .area:
                areaChartView
            }
        }
    }
    
    // MARK: - 线图
    private var lineChartView: some View {
        Chart {
            ForEach(Array(zip(chartData.labels.indices, chartData.labels)), id: \.0) { index, label in
                LineMark(
                    x: .value("类别", label),
                    y: .value("数值", chartData.values[index])
                )
                .foregroundStyle(Color.blue)
                
                PointMark(
                    x: .value("类别", label),
                    y: .value("数值", chartData.values[index])
                )
                .foregroundStyle(Color.blue)
            }
        }
        .chartYAxisLabel(chartData.unit)
        .chartYScale(domain: minMaxYScale())
        .chartXAxis {
            AxisMarks(values: .automatic) { _ in
                AxisValueLabel(orientation: .vertical)
            }
        }
    }
    
    // MARK: - 柱状图
    private var barChartView: some View {
        Chart {
            ForEach(Array(zip(chartData.labels.indices, chartData.labels)), id: \.0) { index, label in
                BarMark(
                    x: .value("类别", label),
                    y: .value("数值", chartData.values[index])
                )
                .foregroundStyle(Color.blue.gradient)
            }
        }
        .chartYAxisLabel(chartData.unit)
        .chartYScale(domain: minMaxYScale())
        .chartXAxis {
            AxisMarks(values: .automatic) { _ in
                AxisValueLabel(orientation: .vertical)
            }
        }
    }
    
    // MARK: - 饼图
    private var pieChartView: some View {
        // SwiftUI Charts 需要使用 Swift Charts
        // 这里简单实现一个饼图
        GeometryReader { geometry in
            ZStack {
                ForEach(chartData.values.indices, id: \.self) { index in
                    PieSliceView(
                        startAngle: startAngle(at: index),
                        endAngle: endAngle(at: index),
                        color: sliceColor(at: index)
                    )
                }
                
                // 中心圆圈
                Circle()
                    .fill(Color.moduleBackground)
                    .frame(width: geometry.size.width * 0.5, height: geometry.size.width * 0.5)
            }
            
            // 图例
            VStack(alignment: .leading, spacing: 8) {
                ForEach(chartData.labels.indices, id: \.self) { index in
                    HStack(spacing: 8) {
                        Rectangle()
                            .fill(sliceColor(at: index))
                            .frame(width: 16, height: 16)
                        
                        Text(chartData.labels[index])
                            .font(.caption)
                        
                        Spacer()
                        
                        Text("\(Int(chartData.values[index])) \(chartData.unit)")
                            .font(.caption)
                            .fontWeight(.semibold)
                    }
                }
            }
            .padding()
            .background(Color.moduleBackground.opacity(0.5))
            .cornerRadius(8)
            .padding(.top, geometry.size.height * 0.7)
        }
    }
    
    // MARK: - 面积图
    private var areaChartView: some View {
        Chart {
            ForEach(Array(zip(chartData.labels.indices, chartData.labels)), id: \.0) { index, label in
                AreaMark(
                    x: .value("类别", label),
                    y: .value("数值", chartData.values[index])
                )
                .foregroundStyle(Color.blue.opacity(0.3).gradient)
                
                LineMark(
                    x: .value("类别", label),
                    y: .value("数值", chartData.values[index])
                )
                .foregroundStyle(Color.blue)
            }
        }
        .chartYAxisLabel(chartData.unit)
        .chartYScale(domain: minMaxYScale())
        .chartXAxis {
            AxisMarks(values: .automatic) { _ in
                AxisValueLabel(orientation: .vertical)
            }
        }
    }
    
    // MARK: - 辅助方法
    
    private func minMaxYScale() -> ClosedRange<Double> {
        let min = (chartData.values.min() ?? 0) * 0.9
        let max = (chartData.values.max() ?? 1) * 1.1
        return min...max
    }
    
    private func startAngle(at index: Int) -> Double {
        let sum = chartData.values.reduce(0, +)
        let proportions = chartData.values.map { $0 / sum }
        let startProportion = proportions.prefix(index).reduce(0, +)
        return startProportion * 360
    }
    
    private func endAngle(at index: Int) -> Double {
        let sum = chartData.values.reduce(0, +)
        let proportions = chartData.values.map { $0 / sum }
        let endProportion = proportions.prefix(index + 1).reduce(0, +)
        return endProportion * 360
    }
    
    private func sliceColor(at index: Int) -> Color {
        let colors: [Color] = [.blue, .green, .orange, .pink, .purple, .yellow, .red]
        return colors[index % colors.count]
    }
}

/// 饼图切片视图
struct PieSliceView: View {
    // MARK: - 属性
    let startAngle: Double
    let endAngle: Double
    let color: Color
    
    // MARK: - 主视图
    var body: some View {
        GeometryReader { geometry in
            Path { path in
                let center = CGPoint(x: geometry.size.width / 2, y: geometry.size.height / 2)
                let radius = min(geometry.size.width, geometry.size.height) / 2
                
                path.move(to: center)
                path.addArc(
                    center: center,
                    radius: radius,
                    startAngle: .degrees(startAngle),
                    endAngle: .degrees(endAngle),
                    clockwise: false
                )
                path.closeSubpath()
            }
            .fill(color)
        }
    }
}

// MARK: - 预览
struct HealthReportDetailView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建一个简单的预览报告
        let report = previewReport
        
        HealthReportDetailView(report: report)
    }
    
    // 创建预览数据
    static var previewReport: HRMReport {
        // 数据点
        let dataPoints = [
            HRMDataPoint(label: "平均心率", value: "72 次/分钟"),
            HRMDataPoint(label: "平均步数", value: "8,432 步"),
            HRMDataPoint(label: "睡眠时长", value: "7.5 小时"),
            HRMDataPoint(label: "健康评分", value: "85/100")
        ]
        
        // 图表数据
        let chartData = HRMChartData(
            title: "每日步数",
            type: .bar,
            labels: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
            values: [7500, 8200, 6800, 9100, 7600, 10200, 9500],
            unit: "步"
        )
        
        // 创建部分
        let sections = [
            ReportSection(
                title: "健康摘要",
                content: "您的健康状况总体表现良好。心率在正常范围内，步数接近推荐的10,000步目标，睡眠时间充足。",
                dataPoints: dataPoints
            ),
            ReportSection(
                title: "活动情况",
                content: "本周您的活动水平保持稳定，平均每天步行8,432步，距离每日10,000步的推荐目标还有一定差距。周末活动量明显增加，这是个好习惯！",
                dataPoints: [],
                chartData: chartData
            )
        ]
        
        // 创建报告
        return HRMReport(
            userId: "preview_user",
            type: .weekly,
            title: "每周健康报告"
        )
    }
} 