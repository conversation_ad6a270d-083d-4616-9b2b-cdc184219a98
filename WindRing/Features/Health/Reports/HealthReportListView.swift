import SwiftUI
// import Models.HealthReportModels

/// 健康报告列表视图
struct HealthReportListView: View {
    // MARK: - 属性
    @StateObject private var viewModel = HealthReportListViewModel()
    @State private var showingFilterSheet = false
    @State private var showingReportDetail = false
    @State private var selectedReport: HRMReport? = nil
    @State private var searchText = ""
    
    private var filteredReports: [HRMReportMetadata] {
        if searchText.isEmpty {
            return viewModel.reportMetadata
        } else {
            return viewModel.reportMetadata.filter { 
                $0.title.localizedCaseInsensitiveContains(searchText) 
            }
        }
    }
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            ZStack {
                // 背景
                Color.appBackground
                    .edgesIgnoringSafeArea(.all)
                
                // 主内容
                VStack {
                    // 报告列表
                    reportListView
                    
                    // 加载更多按钮
                    if viewModel.hasMoreReports && !viewModel.isLoading {
                        loadMoreButton
                    }
                }
                
                // 加载中指示器
                if viewModel.isLoading {
                    ProgressView()
                        .scaleEffect(1.5)
                        .padding()
                        .background(Color.moduleBackground.opacity(0.8))
                        .cornerRadius(10)
                }
                
                // 空状态视图
                if viewModel.reportMetadata.isEmpty && !viewModel.isLoading {
                    emptyStateView
                }
            }
            .searchable(text: $searchText, prompt: "搜索报告")
            .navigationTitle("健康报告")
            .navigationBarTitleDisplayMode(.inline)
//            .toolbar {
//                ToolbarItem(placement: .navigationBarTrailing) {
//                    filterButton
//                }
//                
//                ToolbarItem(placement: .navigationBarTrailing) {
//                    generateReportButton
//                }
//            }
            .onAppear {
                viewModel.loadReports()
            }
            .sheet(isPresented: $showingFilterSheet) {
                FilterSheetView(
                    selectedFilters: $viewModel.filters,
                    onApply: {
                        viewModel.applyFilters()
                    }
                )
                .presentationDetents([.medium])
            }
            .onChange(of: viewModel.selectedReportType) {  _ in
                viewModel.loadReports(refresh: true)
            }
            .fullScreenCover(item: $selectedReport) { report in
                HealthReportDetailView(report: report)
            }
        }
    }
    
    // MARK: - 报告列表视图
    private var reportListView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // 报告类型选择器
                reportTypeSelector
                    .padding(.horizontal)
                
                ForEach(filteredReports) { metadata in
                    ReportCardView(metadata: metadata) {
                        viewModel.loadReport(metadata: metadata) { report in
                            if let report = report {
                                selectedReport = report
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                
                // 底部间距
                if !viewModel.reportMetadata.isEmpty {
                    Spacer()
                        .frame(height: 20)
                }
            }
            .padding(.vertical)
        }
        .refreshable {
            await viewModel.refreshReports()
        }
    }
    
    // MARK: - 报告类型选择器
    private var reportTypeSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(HRMReportType.allCases, id: \.self) { type in
                    ReportTypeButton(
                        type: type,
                        isSelected: viewModel.selectedReportType == type
                    ) {
                        viewModel.selectedReportType = type
                    }
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - 筛选按钮
    private var filterButton: some View {
        Button(action: {
            showingFilterSheet = true
        }) {
            Image(systemName: "line.horizontal.3.decrease.circle")
                .foregroundColor(.primary)
        }
    }
    
    // MARK: - 生成报告按钮
    private var generateReportButton: some View {
        Button(action: {
            viewModel.generateNewReport()
        }) {
            Image(systemName: "plus.circle")
                .foregroundColor(.primary)
        }
    }
    
    // MARK: - 加载更多按钮
    private var loadMoreButton: some View {
        Button(action: {
            viewModel.loadMoreReports()
        }) {
            HStack {
                Text("加载更多")
                    .font(.subheadline)
                    .fontWeight(.medium)
                Image(systemName: "arrow.down.circle")
            }
            .foregroundColor(.blue)
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color.moduleBackground)
            .cornerRadius(8)
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
            .padding(.horizontal)
            .padding(.bottom)
        }
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 64))
                .foregroundColor(.secondary)
            
            Text("暂无健康报告")
                .font(.title2)
                .fontWeight(.medium)
            
            Text("生成您的第一份健康报告，开始追踪健康状况")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button(action: {
                viewModel.generateNewReport()
            }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("生成健康报告")
                }
                .padding()
                .foregroundColor(.white)
                .background(Color.blue)
                .cornerRadius(8)
            }
            .padding(.top)
        }
        .padding()
    }
}

/// 报告卡片视图
struct ReportCardView: View {
    // MARK: - 属性
    let metadata: HRMReportMetadata
    let onTap: () -> Void
    
    // MARK: - 主视图
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // 标题和日期
                HStack {
                    Text(metadata.title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text(formattedDate(metadata.createdAt))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // 报告类型和健康评分
                HStack {
                    reportTypeLabel(metadata.type)
                    
                    Spacer()
                    
                    if let healthScore = metadata.healthScore {
                        healthScoreView(healthScore)
                    }
                }
                
                // 摘要
                if let summary = metadata.summary, !summary.isEmpty {
                    Text(summary)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
            }
            .padding()
            .background(Color.moduleBackground.opacity(0.8))
            .cornerRadius(12)
        }
    }
    
    // MARK: - 报告类型标签
    private func reportTypeLabel(_ type: HRMReportType) -> some View {
        HStack(spacing: 4) {
            Image(systemName: type.iconName)
                .font(.caption)
            
            Text(type.description)
                .font(.caption)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(type.color.opacity(0.2))
        .foregroundColor(type.color)
        .cornerRadius(4)
    }
    
    // MARK: - 健康评分视图
    private func healthScoreView(_ score: Int) -> some View {
        HStack(spacing: 4) {
            Text("\(score)")
                .font(.callout)
                .fontWeight(.bold)
            
            Text("/100")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(scoreBackgroundColor(score))
        .foregroundColor(scoreTextColor(score))
        .cornerRadius(4)
    }
    
    // MARK: - 辅助方法
    
    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
    
    private func scoreBackgroundColor(_ score: Int) -> Color {
        switch score {
        case 0..<40:
            return Color.red.opacity(0.2)
        case 40..<70:
            return Color.orange.opacity(0.2)
        case 70..<90:
            return Color.green.opacity(0.2)
        default:
            return Color.blue.opacity(0.2)
        }
    }
    
    private func scoreTextColor(_ score: Int) -> Color {
        switch score {
        case 0..<40:
            return Color.red
        case 40..<70:
            return Color.orange
        case 70..<90:
            return Color.green
        default:
            return Color.blue
        }
    }
}

/// 报告类型选择按钮
struct ReportTypeButton: View {
    // MARK: - 属性
    let type: HRMReportType
    let isSelected: Bool
    let action: () -> Void
    
    // MARK: - 主视图
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: type.iconName)
                    .font(.subheadline)
                
                Text(type.description)
                    .font(.subheadline)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(isSelected ? type.color.opacity(0.2) : Color.moduleBackground)
            .foregroundColor(isSelected ? type.color : .secondary)
            .cornerRadius(8)
        }
    }
}

/// 筛选表单视图
struct FilterSheetView: View {
    // MARK: - 属性
    @Binding var selectedFilters: [String: Any]
    let onApply: () -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var startDate = Date().addingTimeInterval(-30 * 24 * 60 * 60)
    @State private var endDate = Date()
    @State private var lowerScoreBound: Float = 0
    @State private var upperScoreBound: Float = 100
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            Form {
                // 日期范围
                Section(header: Text("日期范围")) {
                    DatePicker("开始日期", selection: $startDate, displayedComponents: .date)
                    DatePicker("结束日期", selection: $endDate, displayedComponents: .date)
                }
                
                // 健康评分
                Section(header: Text("健康评分")) {
                    VStack {
                        HStack {
                            Text("\(Int(lowerScoreBound))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("\(Int(upperScoreBound))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Slider(
                            value: $lowerScoreBound,
                            in: 0...upperScoreBound
                        )
                        
                        Slider(
                            value: $upperScoreBound,
                            in: lowerScoreBound...100
                        )
                    }
                }
            }
            .navigationTitle("筛选")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("重置") {
                        resetFilters()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("应用") {
                        applyFilters()
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 操作方法
    
    /// 重置筛选条件
    private func resetFilters() {
        startDate = Date().addingTimeInterval(-30 * 24 * 60 * 60)
        endDate = Date()
        lowerScoreBound = 0
        upperScoreBound = 100
    }
    
    /// 应用筛选条件
    private func applyFilters() {
        selectedFilters["startDate"] = startDate
        selectedFilters["endDate"] = endDate
        selectedFilters["minScore"] = Int(lowerScoreBound)
        selectedFilters["maxScore"] = Int(upperScoreBound)
        
        onApply()
    }
}

// MARK: - 预览
struct HealthReportListView_Previews: PreviewProvider {
    static var previews: some View {
        HealthReportListView()
    }
} 
