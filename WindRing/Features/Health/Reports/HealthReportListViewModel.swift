import Foundation
import Combine

/// 健康报告列表视图模型
class HealthReportListViewModel: ObservableObject {
    // MARK: - 属性
    
    // 发布的属性
    @Published var reportMetadata: [HRMReportMetadata] = []
    @Published var selectedReportType: HRMReportType = .all
    @Published var isLoading: Bool = false
    @Published var hasMoreReports: Bool = false
    @Published var filters: [String: Any] = [:]
    
    // 私有属性
    private let healthReportService = HealthReportService.shared
    private let storageService = HealthReportStorageService.shared
    private var cancellables = Set<AnyCancellable>()
    private var currentPage: Int = 1
    private var pageSize: Int = 20
    private var userId: String {
        // 从身份验证服务获取当前用户ID
        // 这里简单返回固定值
        return "current_user"
    }
    
    // MARK: - 初始化方法
    
    init() {
        // 监听存储更新事件
        setupSubscriptions()
    }
    
    // MARK: - 公开方法
    
    /// 加载健康报告
    func loadReports(refresh: Bool = false) {
        if refresh {
            currentPage = 1
        }
        
        isLoading = true
        
        if refresh {
            reportMetadata.removeAll()
        }
        
        // 获取报告元数据
        let metadata = storageService.getReportMetadata(
            userId: userId,
            type: selectedReportType == .all ? nil : selectedReportType,
            startDate: filters["startDate"] as? Date,
            endDate: filters["endDate"] as? Date,
            minHealthScore: filters["minScore"] as? Int,
            maxHealthScore: filters["maxScore"] as? Int,
            page: currentPage,
            pageSize: pageSize
        )
        
        // 更新数据
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if refresh {
                self.reportMetadata = metadata
            } else {
                self.reportMetadata.append(contentsOf: metadata)
            }
            
            self.hasMoreReports = metadata.count >= self.pageSize
            self.isLoading = false
            
            // 如果没有报告，尝试生成一份
            if self.reportMetadata.isEmpty && refresh {
                self.checkAndGenerateInitialReport()
            }
        }
    }
    
    /// 刷新报告
    @MainActor
    func refreshReports() async {
        currentPage = 1
        loadReports(refresh: true)
        // 异步等待一小段时间模拟网络请求
        try? await Task.sleep(nanoseconds: 1_000_000_000)
    }
    
    /// 加载更多报告
    func loadMoreReports() {
        currentPage += 1
        loadReports()
    }
    
    /// 加载特定报告详情
    func loadReport(metadata: HRMReportMetadata, completion: @escaping (HRMReport?) -> Void) {
        Task {
            // 显示加载状态
            await MainActor.run {
                isLoading = true
            }
            
            // 获取完整报告
            let report = storageService.getReport(metadata: metadata)
            
            // 更新UI
            await MainActor.run {
                isLoading = false
                completion(report)
            }
        }
    }
    
    /// 生成新报告
    func generateNewReport() {
        // 显示加载状态
        isLoading = true
        
        Task {
            // 基于选择的报告类型生成报告
            let report: HRMReport?
            
            switch selectedReportType {
            case .daily:
                report = await generateDailyReport()
            case .weekly:
                report = await generateWeeklyReport()
            case .monthly:
                report = await generateMonthlyReport()
            case .custom:
                // 自定义报告需要日期范围，这里使用过去7天
                let endDate = Date()
                let startDate = Calendar.current.date(byAdding: .day, value: -7, to: endDate)!
                report = await generateCustomReport(startDate: startDate, endDate: endDate)
            case .all:
                // 默认生成日报
                report = await generateDailyReport()
            }
            
            // 保存报告
            if let report = report {
                await saveReport(report)
            }
            
            // 刷新列表
            await MainActor.run {
                isLoading = false
                loadReports(refresh: true)
            }
        }
    }
    
    /// 应用筛选器
    func applyFilters() {
        loadReports(refresh: true)
    }
    
    // MARK: - 私有方法
    
    /// 设置订阅
    private func setupSubscriptions() {
        // 监听报告存储服务的更新事件
        storageService.storageUpdatePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.loadReports(refresh: true)
            }
            .store(in: &cancellables)
    }
    
    /// 检查并生成初始报告
    private func checkAndGenerateInitialReport() {
        // 如果没有报告，可以自动生成一个
        // 这个功能可以在应用首次启动或用户没有报告时使用
        Task {
            // 生成日报
            if let report = await generateDailyReport() {
                // 保存报告
                await saveReport(report)
                
                // 刷新列表
                await MainActor.run {
                    loadReports(refresh: true)
                }
            }
        }
    }
    
    /// 生成日报
    private func generateDailyReport() async -> HRMReport? {
        return await withCheckedContinuation { continuation in
            Task {
                let report = healthReportService.generateDailyReport(userId: userId)
                continuation.resume(returning: report)
            }
        }
    }
    
    /// 生成周报
    private func generateWeeklyReport() async -> HRMReport? {
        return await withCheckedContinuation { continuation in
            Task {
                let report = healthReportService.generateWeeklyReport(userId: userId)
                continuation.resume(returning: report)
            }
        }
    }
    
    /// 生成月报
    private func generateMonthlyReport() async -> HRMReport? {
        return await withCheckedContinuation { continuation in
            Task {
                let report = healthReportService.generateMonthlyReport(userId: userId)
                continuation.resume(returning: report)
            }
        }
    }
    
    /// 生成自定义报告
    private func generateCustomReport(startDate: Date, endDate: Date) async -> HRMReport? {
        return await withCheckedContinuation { continuation in
            Task {
                let report = healthReportService.generateCustomReport(
                    userId: userId,
                    startDate: startDate,
                    endDate: endDate
                )
                continuation.resume(returning: report)
            }
        }
    }
    
    /// 保存报告
    private func saveReport(_ report: HRMReport) async {
        return await withCheckedContinuation { continuation in
            Task {
                storageService.saveReport(report)
                continuation.resume(returning: ())
            }
        }
    }
} 