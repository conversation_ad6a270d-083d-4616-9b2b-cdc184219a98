import SwiftUI

/// 健康报告生成表单视图
struct ReportGenerationView: View {
    // MARK: - 属性
    @StateObject private var viewModel = ReportGenerationViewModel()
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedReportType: HRMReportType = .daily
    @State private var startDate = Date().addingTimeInterval(-7 * 24 * 60 * 60)
    @State private var endDate = Date()
    @State private var isGenerating = false
    @State private var generatedReport: HRMReport? = nil
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            Form {
                // 报告类型选择
                Section(header: Text("报告类型")) {
                    Picker("选择报告类型", selection: $selectedReportType) {
                        ForEach(HRMReportType.allCases.filter { $0 != .all }, id: \.self) { type in
                            HStack {
                                Image(systemName: type.iconName)
                                Text(type.description)
                            }
                            .tag(type)
                        }
                    }
                    .pickerStyle(.navigationLink)
                }
                
                // 日期范围选择（仅当选择自定义报告时显示）
                if selectedReportType == .custom {
                    Section(header: Text("日期范围")) {
                        DatePicker("开始日期", selection: $startDate, displayedComponents: .date)
                        DatePicker("结束日期", selection: $endDate, in: startDate...Date(), displayedComponents: .date)
                    }
                }
                
                // 报告预览
                Section(header: Text("报告预览")) {
                    VStack(alignment: .leading, spacing: 8) {
                        // 报告标题
                        Text(getReportTitle())
                            .font(.headline)
                        
                        // 日期范围
                        HStack {
                            Image(systemName: "calendar")
                                .foregroundColor(.secondary)
                            Text(getDateRangeText())
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        // 报告内容预览
                        Text("报告将包含以下内容：")
                            .font(.subheadline)
                            .padding(.top, 8)
                        
                        // 报告内容预览列表
                        ForEach(getReportSections(), id: \.self) { section in
                            HStack(spacing: 8) {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                    .font(.caption)
                                
                                Text(section)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.vertical, 2)
                        }
                    }
                    .padding(.vertical, 8)
                }
                
                // 生成按钮
                Section {
                    Button(action: generateReport) {
                        HStack {
                            Spacer()
                            
                            if isGenerating {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            } else {
                                Text("生成报告")
                            }
                            
                            Spacer()
                        }
                    }
                    .disabled(isGenerating)
                }
            }
            .background(Color.appBackground)
            .scrollContentBackground(.hidden)
            .navigationTitle("生成健康报告")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
            .fullScreenCover(item: $generatedReport) { report in
                HealthReportDetailView(report: report)
            }
        }
    }
    
    // MARK: - 操作方法
    
    /// 生成报告
    private func generateReport() {
        isGenerating = true
        
        Task {
            // 生成报告
            let report: HRMReport?
            
            switch selectedReportType {
            case .daily:
                report = await viewModel.generateDailyReport()
            case .weekly:
                report = await viewModel.generateWeeklyReport()
            case .monthly:
                report = await viewModel.generateMonthlyReport()
            case .custom:
                report = await viewModel.generateCustomReport(startDate: startDate, endDate: endDate)
            default:
                report = nil
            }
            
            // 保存报告
            if let report = report {
                await viewModel.saveReport(report)
                
                // 显示报告
                await MainActor.run {
                    isGenerating = false
                    generatedReport = report
                }
            } else {
                await MainActor.run {
                    isGenerating = false
                    // 显示错误提示
                }
            }
        }
    }
    
    // MARK: - 辅助方法
    
    /// 获取报告标题
    private func getReportTitle() -> String {
        switch selectedReportType {
        case .daily:
            return "今日健康报告"
        case .weekly:
            return "每周健康报告"
        case .monthly:
            return "每月健康报告"
        case .custom:
            return "自定义健康报告"
        default:
            return "健康报告"
        }
    }
    
    /// 获取日期范围文本
    private func getDateRangeText() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        
        switch selectedReportType {
        case .daily:
            return dateFormatter.string(from: Date())
        case .weekly:
            let startDate = Calendar.current.date(byAdding: .day, value: -6, to: Date())!
            return "\(dateFormatter.string(from: startDate)) - \(dateFormatter.string(from: Date()))"
        case .monthly:
            let startDate = Calendar.current.date(byAdding: .day, value: -29, to: Date())!
            return "\(dateFormatter.string(from: startDate)) - \(dateFormatter.string(from: Date()))"
        case .custom:
            return "\(dateFormatter.string(from: startDate)) - \(dateFormatter.string(from: endDate))"
        default:
            return ""
        }
    }
    
    /// 获取报告内容预览
    private func getReportSections() -> [String] {
        switch selectedReportType {
        case .daily:
            return [
                "健康概览",
                "心率详情",
                "步数详情",
                "睡眠详情",
                "健康建议"
            ]
        case .weekly:
            return [
                "每周健康概览",
                "心率趋势分析",
                "步数趋势分析",
                "睡眠质量分析",
                "健康趋势分析",
                "个性化建议"
            ]
        case .monthly:
            return [
                "每月健康概览",
                "心率月度分析",
                "活动月度分析",
                "睡眠模式分析",
                "健康进步摘要",
                "长期健康建议"
            ]
        case .custom:
            return [
                "自定义时间段健康概览",
                "心率趋势分析",
                "活动趋势分析",
                "睡眠模式分析",
                "健康建议"
            ]
        default:
            return []
        }
    }
}

// MARK: - 视图模型
class ReportGenerationViewModel: ObservableObject {
    // MARK: - 属性
    private let healthReportService = HealthReportService.shared
    private let storageService = HealthReportStorageService.shared
    
    private var userId: String {
        // 从身份验证服务获取当前用户ID
        // 这里简单返回固定值
        return "current_user"
    }
    
    // MARK: - 公开方法
    
    /// 生成日报
    func generateDailyReport() async -> HRMReport? {
        return healthReportService.generateDailyReport(userId: userId)
    }
    
    /// 生成周报
    func generateWeeklyReport() async -> HRMReport? {
        return healthReportService.generateWeeklyReport(userId: userId)
    }
    
    /// 生成月报
    func generateMonthlyReport() async -> HRMReport? {
        return healthReportService.generateMonthlyReport(userId: userId)
    }
    
    /// 生成自定义报告
    func generateCustomReport(startDate: Date, endDate: Date) async -> HRMReport? {
        return healthReportService.generateCustomReport(
            userId: userId,
            startDate: startDate,
            endDate: endDate
        )
    }
    
    /// 保存报告
    func saveReport(_ report: HRMReport) async {
        storageService.saveReport(report)
    }
}

// MARK: - 预览
struct ReportGenerationView_Previews: PreviewProvider {
    static var previews: some View {
        ReportGenerationView()
    }
} 