import SwiftUI
// import Models.HealthReportModels
import Combine

/// 健康报告主页视图
struct HealthReportHomeView: View {
    // MARK: - 属性
    @State private var showGenerationSheet = false
    @State private var selectedTab: ReportTab = .all
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部标签栏
                tabBarView
                
                // 内容视图
                contentView
            }
            .navigationTitle("健康报告")
            .navigationBarTitleDisplayMode(.large)
            .background(Color.appBackground)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showGenerationSheet = true
                    }) {
                        Image(systemName: "plus")
                            .foregroundColor(.primary)
                    }
                }
            }
            .sheet(isPresented: $showGenerationSheet) {
                ReportGenerationView()
            }
        }
    }
    
    // MARK: - 标签栏视图
    private var tabBarView: some View {
        HStack(spacing: 0) {
            ForEach(ReportTab.allCases, id: \.self) { tab in
                tabBarButton(tab)
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color.moduleBackground)
        .shadow(color: Color.black.opacity(0.07), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 标签按钮
    private func tabBarButton(_ tab: ReportTab) -> some View {
        Button(action: {
            withAnimation {
                selectedTab = tab
            }
        }) {
            VStack(spacing: 6) {
                Image(systemName: tab.iconName)
                    .font(.system(size: 20))
                    .foregroundColor(selectedTab == tab ? .blue : .gray)
                
                Text(tab.title)
                    .font(.caption)
                    .fontWeight(selectedTab == tab ? .medium : .regular)
                    .foregroundColor(selectedTab == tab ? .blue : .gray)
            }
            .padding(.vertical, 8)
            .frame(maxWidth: .infinity)
            .background(
                ZStack {
                    if selectedTab == tab {
                        Color.blue.opacity(0.1)
                            .cornerRadius(8)
                    }
                }
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 内容视图
    private var contentView: some View {
        ZStack {
            switch selectedTab {
            case .all:
                HealthReportListView()
                    .transition(.opacity)
            case .favorites:
                FavoriteReportsView()
                    .transition(.opacity)
            case .statistics:
                HealthStatisticsView()
                    .transition(.opacity)
            case .settings:
                ReportSettingsView()
                    .transition(.opacity)
            }
        }
    }
}

// MARK: - 报告标签
enum ReportTab: String, CaseIterable {
    case all
    case favorites
    case statistics
    case settings
    
    var title: String {
        switch self {
        case .all:
            return "所有报告"
        case .favorites:
            return "收藏"
        case .statistics:
            return "统计"
        case .settings:
            return "设置"
        }
    }
    
    var iconName: String {
        switch self {
        case .all:
            return "list.bullet.rectangle"
        case .favorites:
            return "star"
        case .statistics:
            return "chart.xyaxis.line"
        case .settings:
            return "gear"
        }
    }
}

// MARK: - 收藏报告视图
struct FavoriteReportsView: View {
    // MARK: - 属性
    @StateObject private var viewModel = FavoriteReportsViewModel()
    
    // MARK: - 主视图
    var body: some View {
        Group {
            if viewModel.isLoading {
                ProgressView()
                    .scaleEffect(1.5)
            } else if viewModel.favoriteReports.isEmpty {
                emptyStateView
            } else {
                reportListView
            }
        }
        .onAppear {
            viewModel.loadFavoriteReports()
        }
    }
    
    // MARK: - 报告列表视图
    private var reportListView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(viewModel.favoriteReports) { metadata in
                    ReportCardView(metadata: metadata) {
                        viewModel.openReport(metadata: metadata)
                    }
                    .padding(.horizontal)
                }
                
                Spacer()
                    .frame(height: 20)
            }
            .padding(.vertical)
        }
        .refreshable {
            await viewModel.refreshFavorites()
        }
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "star.slash")
                .font(.system(size: 64))
                .foregroundColor(.secondary)
            
            Text("暂无收藏报告")
                .font(.title2)
                .fontWeight(.medium)
            
            Text("收藏您关注的健康报告以便快速访问")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .padding()
    }
}

// MARK: - 收藏报告视图模型
class FavoriteReportsViewModel: ObservableObject {
    // MARK: - 属性
    @Published var isLoading = false
    @Published var favoriteReports: [HRMReportMetadata] = []
    
    private let storageService = HealthReportStorageService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化方法
    init() {
        loadFavoriteReports()
    }
    
    // MARK: - 方法
    
    /// 加载收藏的报告
    func loadFavoriteReports() {
        isLoading = true
        
        // 获取收藏的报告元数据
        let metadata = storageService.getReportMetadata(
            userId: "current_user",
            page: 1,
            pageSize: 5
        )
        
        // 更新数据
        favoriteReports = metadata
        isLoading = false
    }
    
    /// 刷新收藏列表
    func refreshFavorites() {
        loadFavoriteReports()
    }
    
    /// 打开报告
    func openReport(metadata: HRMReportMetadata) {
        // 此方法将在外部处理
    }
}

// MARK: - 健康统计视图
struct HealthStatisticsView: View {
    // MARK: - 属性
    @State private var selectedPeriod: StatisticsPeriod = .week
    @StateObject private var viewModel = HealthStatisticsViewModel()
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 时间段选择器
                periodPicker
                
                // 健康分数卡片
                healthScoreCard
                
                // 健康指标卡片
                healthMetricsCard
                
                // 活动时间图表
                activityTimeChart
                
                // 睡眠质量图表
                sleepQualityChart
            }
            .padding()
        }
        .onAppear {
            viewModel.loadStatistics(for: selectedPeriod)
        }
        .onChange(of: selectedPeriod) {  newValue in
            // 切换时间段时重新加载数据
            loadData()
        }
    }
    
    // MARK: - 时间段选择器
    private var periodPicker: some View {
        Picker("时间段", selection: $selectedPeriod) {
            ForEach(StatisticsPeriod.allCases, id: \.self) { period in
                Text(period.description)
                    .tag(period)
            }
        }
        .pickerStyle(SegmentedPickerStyle())
    }
    
    // MARK: - 健康分数卡片
    private var healthScoreCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("平均健康评分")
                .font(.headline)
            
            HStack(spacing: 24) {
                ZStack {
                    Circle()
                        .stroke(
                            Color.blue.opacity(0.2),
                            lineWidth: 10
                        )
                    
                    Circle()
                        .trim(from: 0, to: CGFloat(viewModel.averageHealthScore) / 100)
                        .stroke(
                            Color.blue,
                            style: StrokeStyle(
                                lineWidth: 10,
                                lineCap: .round
                            )
                        )
                        .rotationEffect(.degrees(-90))
                    
                    VStack {
                        Text("\(viewModel.averageHealthScore)")
                            .font(.system(size: 28, weight: .bold))
                        
                        Text("总分100")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(width: 120, height: 120)
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: viewModel.scoreChangeIcon)
                            .foregroundColor(viewModel.scoreChangeColor)
                        
                        Text("\(abs(viewModel.healthScoreChange))% 较上一时段")
                            .font(.subheadline)
                            .foregroundColor(viewModel.scoreChangeColor)
                    }
                    
                    Text(viewModel.healthScoreDescription)
                        .font(.body)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 健康指标卡片
    private var healthMetricsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("健康指标平均值")
                .font(.headline)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                metricView(
                    title: "平均心率",
                    value: "\(viewModel.averageHeartRate)",
                    unit: "次/分钟",
                    icon: "heart.fill",
                    color: .red
                )
                
                metricView(
                    title: "平均步数",
                    value: "\(viewModel.averageSteps)",
                    unit: "步/天",
                    icon: "figure.walk",
                    color: .green
                )
                
                metricView(
                    title: "平均睡眠",
                    value: String(format: "%.1f", viewModel.averageSleepHours),
                    unit: "小时/天",
                    icon: "bed.double.fill",
                    color: .purple
                )
                
                metricView(
                    title: "活动卡路里",
                    value: "\(viewModel.averageCalories)",
                    unit: "千卡/天",
                    icon: "flame.fill",
                    color: .orange
                )
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 指标视图
    private func metricView(title: String, value: String, unit: String, icon: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(color)
                .frame(width: 32, height: 32)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                HStack(spacing: 2) {
                    Text(value)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.primary)
                    
                    Text(unit)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(12)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
    
    // MARK: - 活动时间图表
    private var activityTimeChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("活动时间趋势")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("平均每日活动：")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("\(viewModel.averageActiveMinutes) 分钟")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                // 模拟图表
                VStack(spacing: 8) {
                    HStack {
                        ForEach(0..<7) { i in
                            VStack(spacing: 6) {
                                Text("\(viewModel.activityData[i]) 分")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                ZStack(alignment: .bottom) {
                                    Rectangle()
                                        .fill(Color.moduleBackground)
                                        .frame(width: 20, height: 100)
                                    
                                    Rectangle()
                                        .fill(Color.blue)
                                        .frame(width: 20, height: CGFloat(viewModel.activityData[i]) / CGFloat(viewModel.activityData.max() ?? 1) * 100)
                                }
                                .cornerRadius(6)
                                
                                Text(viewModel.daysOfWeek[i])
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 睡眠质量图表
    private var sleepQualityChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("睡眠质量分析")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 12) {
                // 睡眠评分
                HStack {
                    Text("平均睡眠评分：")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("\(viewModel.averageSleepScore)/100")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                // 睡眠阶段
                VStack(spacing: 12) {
                    // 深睡
                    sleepStageBar(
                        label: "深度睡眠",
                        value: viewModel.averageDeepSleepPercentage,
                        color: .purple
                    )
                    
                    // 浅睡
                    sleepStageBar(
                        label: "浅度睡眠",
                        value: viewModel.averageLightSleepPercentage,
                        color: .blue
                    )
                    
                    // REM
                    sleepStageBar(
                        label: "REM睡眠",
                        value: viewModel.averageRemSleepPercentage,
                        color: .orange
                    )
                    
                    // 清醒
                    sleepStageBar(
                        label: "清醒",
                        value: viewModel.averageAwakePercentage,
                        color: .green
                    )
                }
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 睡眠阶段条形图
    private func sleepStageBar(label: String, value: Int, color: Color) -> some View {
        VStack(spacing: 4) {
            HStack {
                Text(label)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(value)%")
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.moduleBackground)
                        .frame(height: 8)
                        .cornerRadius(4)
                    
                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * CGFloat(value) / 100, height: 8)
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)
        }
    }
    
    // 添加loadData方法
    private func loadData() {
        viewModel.loadStatistics(for: selectedPeriod)
    }
}

// MARK: - 统计时间段
enum StatisticsPeriod: String, CaseIterable {
    case week
    case month
    case quarter
    case year
    
    var description: String {
        switch self {
        case .week:
            return "本周"
        case .month:
            return "本月"
        case .quarter:
            return "季度"
        case .year:
            return "年度"
        }
    }
}

// MARK: - 健康统计视图模型
class HealthStatisticsViewModel: ObservableObject {
    // MARK: - 发布属性
    @Published var averageHealthScore: Int = 0
    @Published var healthScoreChange: Int = 0
    @Published var healthScoreDescription: String = ""
    
    @Published var averageHeartRate: Int = 0
    @Published var averageSteps: Int = 0
    @Published var averageSleepHours: Double = 0
    @Published var averageCalories: Int = 0
    
    @Published var averageActiveMinutes: Int = 0
    @Published var activityData: [Int] = []
    @Published var daysOfWeek: [String] = []
    
    @Published var averageSleepScore: Int = 0
    @Published var averageDeepSleepPercentage: Int = 0
    @Published var averageLightSleepPercentage: Int = 0
    @Published var averageRemSleepPercentage: Int = 0
    @Published var averageAwakePercentage: Int = 0
    
    // MARK: - 计算属性
    var scoreChangeIcon: String {
        healthScoreChange >= 0 ? "arrow.up" : "arrow.down"
    }
    
    var scoreChangeColor: Color {
        healthScoreChange >= 0 ? .green : .red
    }
    
    // MARK: - 方法
    func loadStatistics(for period: StatisticsPeriod) {
        // 加载模拟数据
        switch period {
        case .week:
            loadWeeklyStatistics()
        case .month:
            loadMonthlyStatistics()
        case .quarter:
            loadQuarterlyStatistics()
        case .year:
            loadYearlyStatistics()
        }
    }
    
    // MARK: - 私有方法
    
    /// 加载每周统计数据
    private func loadWeeklyStatistics() {
        // 模拟数据
        averageHealthScore = 83
        healthScoreChange = 5
        healthScoreDescription = "健康状况良好，较上周有所提升。建议保持当前的活动水平并改善睡眠规律。"
        
        averageHeartRate = 72
        averageSteps = 8532
        averageSleepHours = 7.2
        averageCalories = 2150
        
        averageActiveMinutes = 42
        activityData = [35, 28, 45, 50, 38, 55, 40]
        daysOfWeek = ["一", "二", "三", "四", "五", "六", "日"]
        
        averageSleepScore = 78
        averageDeepSleepPercentage = 25
        averageLightSleepPercentage = 45
        averageRemSleepPercentage = 20
        averageAwakePercentage = 10
    }
    
    /// 加载每月统计数据
    private func loadMonthlyStatistics() {
        // 模拟数据
        averageHealthScore = 80
        healthScoreChange = -2
        healthScoreDescription = "健康状况良好，但较上月略有下降。建议增加有氧运动并保持规律饮食。"
        
        averageHeartRate = 74
        averageSteps = 7985
        averageSleepHours = 7.0
        averageCalories = 2080
        
        averageActiveMinutes = 38
        activityData = [32, 40, 36, 45, 28, 50, 35]
        daysOfWeek = ["第1周", "第2周", "第3周", "第4周", " ", " ", " "]
        
        averageSleepScore = 75
        averageDeepSleepPercentage = 22
        averageLightSleepPercentage = 48
        averageRemSleepPercentage = 18
        averageAwakePercentage = 12
    }
    
    /// 加载季度统计数据
    private func loadQuarterlyStatistics() {
        // 模拟数据
        averageHealthScore = 79
        healthScoreChange = 3
        healthScoreDescription = "健康状况良好，较上季度有所提升。建议维持现有锻炼习惯并关注饮食平衡。"
        
        averageHeartRate = 73
        averageSteps = 8120
        averageSleepHours = 7.3
        averageCalories = 2120
        
        averageActiveMinutes = 40
        activityData = [35, 42, 38, 0, 0, 0, 0]
        daysOfWeek = ["1月", "2月", "3月", "", "", "", ""]
        
        averageSleepScore = 76
        averageDeepSleepPercentage = 23
        averageLightSleepPercentage = 47
        averageRemSleepPercentage = 19
        averageAwakePercentage = 11
    }
    
    /// 加载年度统计数据
    private func loadYearlyStatistics() {
        // 模拟数据
        averageHealthScore = 81
        healthScoreChange = 7
        healthScoreDescription = "年度健康状况良好，有明显提升。坚持健康生活方式正在产生积极影响。"
        
        averageHeartRate = 71
        averageSteps = 8350
        averageSleepHours = 7.4
        averageCalories = 2200
        
        averageActiveMinutes = 45
        activityData = [32, 36, 40, 44, 48, 42, 38]
        daysOfWeek = ["Q1", "Q2", "Q3", "Q4", "", "", ""]
        
        averageSleepScore = 80
        averageDeepSleepPercentage = 26
        averageLightSleepPercentage = 44
        averageRemSleepPercentage = 22
        averageAwakePercentage = 8
    }
}

// MARK: - 报告设置视图
struct ReportSettingsView: View {
    // MARK: - 属性
    @State private var enableAutoGeneration = true
    @State private var selectedAutoType: HRMReportType = .weekly
    @State private var notifyOnGeneration = true
    @State private var showSummaryInNotification = true
    @State private var exportToPDF = false
    @State private var shareWithDoctor = false
    @State private var selectedLanguage = "简体中文"
    @State private var clearCacheAlert = false
    
    private let languages = ["简体中文", "English", "日本語", "Español"]
    
    // MARK: - 主视图
    var body: some View {
        Form {
            // 自动生成设置
            Section(header: Text("自动生成设置"), footer: Text("开启后将定期自动生成健康报告")) {
                Toggle("自动生成报告", isOn: $enableAutoGeneration)
                
                if enableAutoGeneration {
                    Picker("报告类型", selection: $selectedAutoType) {
                        ForEach(HRMReportType.allCases.filter { $0 != .all && $0 != .custom }, id: \.self) { type in
                            Text(type.description)
                                .tag(type)
                        }
                    }
                    
                    Toggle("生成时通知", isOn: $notifyOnGeneration)
                    
                    if notifyOnGeneration {
                        Toggle("通知中显示摘要", isOn: $showSummaryInNotification)
                    }
                }
            }
            
            // 共享与导出
            Section(header: Text("共享与导出"), footer: Text("自动导出报告或与您的医生共享健康数据")) {
                Toggle("自动导出PDF", isOn: $exportToPDF)
                Toggle("与医生共享", isOn: $shareWithDoctor)
            }
            
            // 语言设置
            Section(header: Text("语言设置")) {
                Picker("报告语言", selection: $selectedLanguage) {
                    ForEach(languages, id: \.self) { language in
                        Text(language).tag(language)
                    }
                }
            }
            
            // 数据管理
            Section(header: Text("数据管理")) {
                Button(action: {
                    clearCacheAlert = true
                }) {
                    HStack {
                        Text("清除缓存数据")
                            .foregroundColor(.red)
                        Spacer()
                    }
                }
                
                NavigationLink(destination: Text("数据导出页面")) {
                    Text("导出所有健康数据")
                }
            }
        }
        .alert(isPresented: $clearCacheAlert) {
            Alert(
                title: Text("清除缓存"),
                message: Text("确定要清除所有缓存的健康报告数据吗？此操作不可撤销。"),
                primaryButton: .destructive(Text("清除")) {
                    // 清除缓存逻辑
                    print("清除缓存")
                },
                secondaryButton: .cancel(Text("取消"))
            )
        }
    }
}

// MARK: - 预览
struct HealthReportHomeView_Previews: PreviewProvider {
    static var previews: some View {
        HealthReportHomeView()
    }
} 
