import Foundation
import SwiftUI
import PDFKit
import UIKit

/// 健康报告共享和导出服务
class ReportSharingService {
    // MARK: - 共享实例
    static let shared = ReportSharingService()
    
    // MARK: - 初始化方法
    private init() {}
    
    // MARK: - 共享方法
    
    /// 共享健康报告
    /// - Parameters:
    ///   - report: 要共享的健康报告
    ///   - sourceView: 共享弹出视图的来源视图
    ///   - completion: 共享完成后的回调
    func shareReport(report: HRMReport, from sourceView: UIView, completion: (() -> Void)? = nil) {
        // 生成PDF数据
        guard let pdfData = generatePDFData(from: report) else {
            print("Error: 无法生成PDF数据")
            completion?()
            return
        }
        
        // 创建临时PDF文件
        let tempDirectoryURL = FileManager.default.temporaryDirectory
        let fileName = "健康报告_\(formatDate(report.createdAt)).pdf"
        let fileURL = tempDirectoryURL.appendingPathComponent(fileName)
        
        do {
            try pdfData.write(to: fileURL, options: .atomic)
            
            // 创建分享项目
            let activityViewController = UIActivityViewController(
                activityItems: [fileURL],
                applicationActivities: nil
            )
            
            // 设置iPad的弹出位置
            if let popoverController = activityViewController.popoverPresentationController {
                popoverController.sourceView = sourceView
                popoverController.sourceRect = CGRect(x: sourceView.bounds.midX, y: sourceView.bounds.midY, width: 0, height: 0)
            }
            
            // 在主线程上显示共享表单
            DispatchQueue.main.async {
                // 获取当前活跃的窗口场景
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let rootViewController = windowScene.windows.first?.rootViewController {
                    rootViewController.present(
                        activityViewController,
                        animated: true,
                        completion: nil
                    )
                }
                
                // 设置完成回调
                activityViewController.completionWithItemsHandler = { _, _, _, _ in
                    // 删除临时文件
                    try? FileManager.default.removeItem(at: fileURL)
                    completion?()
                }
            }
        } catch {
            print("Error: 无法写入PDF文件: \(error.localizedDescription)")
            completion?()
        }
    }
    
    /// 导出健康报告为PDF
    /// - Parameters:
    ///   - report: 要导出的健康报告
    ///   - completion: 导出完成后的回调，参数为导出的文件URL
    func exportReportAsPDF(report: HRMReport, completion: @escaping (URL?) -> Void) {
        // 生成PDF数据
        guard let pdfData = generatePDFData(from: report) else {
            print("Error: 无法生成PDF数据")
            completion(nil)
            return
        }
        
        // 创建文档目录URL
        let documentDirectoryURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let reportDirectoryURL = documentDirectoryURL.appendingPathComponent("HealthReports", isDirectory: true)
        
        // 创建报告目录
        do {
            if !FileManager.default.fileExists(atPath: reportDirectoryURL.path) {
                try FileManager.default.createDirectory(
                    at: reportDirectoryURL,
                    withIntermediateDirectories: true,
                    attributes: nil
                )
            }
            
            // 创建PDF文件
            let fileName = "健康报告_\(formatDate(report.createdAt))_\(report.id).pdf"
            let fileURL = reportDirectoryURL.appendingPathComponent(fileName)
            
            try pdfData.write(to: fileURL, options: .atomic)
            completion(fileURL)
        } catch {
            print("Error: 无法保存PDF文件: \(error.localizedDescription)")
            completion(nil)
        }
    }
    
    // MARK: - 私有方法
    
    /// 生成PDF数据
    private func generatePDFData(from report: HRMReport) -> Data? {
        // 创建PDF页面大小
        let pageSize = CGSize(width: 612, height: 792) // 8.5 x 11 英寸，72 DPI
        
        // 创建PDF上下文
        let pdfMetadata = [
            kCGPDFContextCreator: "WindRing Health App" as CFString,
            kCGPDFContextTitle: report.title as CFString
        ]
        
        let data = NSMutableData()
        var pageRect = CGRect(origin: .zero, size: pageSize)
        
        // 创建临时文件 URL
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("report.pdf")
        
        // 创建 PDF 上下文
        guard let pdfContext = CGContext(tempURL as CFURL, mediaBox: &pageRect, pdfMetadata as CFDictionary) else {
            return nil
        }
        
        // 开始第一页 - 使用默认框架
        pdfContext.beginPDFPage(nil)
        
        // 绘制PDF内容
        drawPDFContent(
            in: pdfContext,
            pageSize: pageSize,
            report: report
        )
        
        // 结束PDF页面和上下文
        pdfContext.endPDFPage()
        pdfContext.closePDF()
        
        // 从临时文件中读取 PDF 数据
        do {
            let pdfData = try Data(contentsOf: tempURL)
            // 删除临时文件
            try FileManager.default.removeItem(at: tempURL)
            return pdfData
        } catch {
            print("读取或删除临时 PDF 文件失败: \(error)")
            return nil
        }
    }
    
    /// 绘制PDF内容
    private func drawPDFContent(in context: CGContext, pageSize: CGSize, report: HRMReport) {
        let renderer = UIGraphicsPDFRenderer(bounds: CGRect(origin: .zero, size: pageSize))
        _ = renderer.pdfData { context in
            context.beginPage()
            
            // 绘制标题
            drawTitle(report.title, in: context.cgContext, pageSize: pageSize)
            
            // 绘制日期和用户信息
            drawDateAndUserInfo(report, in: context.cgContext, pageSize: pageSize)
            
            // 绘制报告内容
            var yPosition: CGFloat = 180
            
            // 绘制报告各个部分
            for section in report.sections {
                yPosition = drawSection(section, in: context.cgContext, pageSize: pageSize, yPosition: yPosition)
                
                // 检查是否需要新页
                if yPosition > pageSize.height - 100 {
                    context.beginPage()
                    yPosition = 60
                }
            }
            
            // 绘制页脚
            drawFooter(in: context.cgContext, pageSize: pageSize)
        }
    }
    
    /// 绘制标题
    private func drawTitle(_ title: String, in context: CGContext, pageSize: CGSize) {
        let titleAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 24, weight: .bold),
            .foregroundColor: UIColor.black
        ]
        
        let attributedTitle = NSAttributedString(string: title, attributes: titleAttributes)
        let titleSize = attributedTitle.size()
        let titleRect = CGRect(
            x: (pageSize.width - titleSize.width) / 2,
            y: 60,
            width: titleSize.width,
            height: titleSize.height
        )
        
        context.saveGState()
        UIColor.systemBlue.setStroke()
        context.setLineWidth(2)
        context.move(to: CGPoint(x: pageSize.width * 0.3, y: 100))
        context.addLine(to: CGPoint(x: pageSize.width * 0.7, y: 100))
        context.strokePath()
        context.restoreGState()
        
        attributedTitle.draw(in: titleRect)
    }
    
    /// 绘制日期和用户信息
    private func drawDateAndUserInfo(_ report: HRMReport, in context: CGContext, pageSize: CGSize) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        
        let dateString = "日期范围: \(dateFormatter.string(from: report.startDate)) - \(dateFormatter.string(from: report.endDate))"
        let dateAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 12),
            .foregroundColor: UIColor.darkGray
        ]
        
        let attributedDate = NSAttributedString(string: dateString, attributes: dateAttributes)
        let dateSize = attributedDate.size()
        let dateRect = CGRect(
            x: (pageSize.width - dateSize.width) / 2,
            y: 110,
            width: dateSize.width,
            height: dateSize.height
        )
        
        let userString = "用户ID: \(report.userId)"
        let attributedUser = NSAttributedString(string: userString, attributes: dateAttributes)
        let userSize = attributedUser.size()
        let userRect = CGRect(
            x: (pageSize.width - userSize.width) / 2,
            y: 130,
            width: userSize.width,
            height: userSize.height
        )
        
        attributedDate.draw(in: dateRect)
        attributedUser.draw(in: userRect)
    }
    
    /// 绘制报告部分
    private func drawSection(_ section: HRMSection, in context: CGContext, pageSize: CGSize, yPosition: CGFloat) -> CGFloat {
        var currentY = yPosition
        
        // 绘制部分标题
        let sectionTitleAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 18, weight: .semibold),
            .foregroundColor: UIColor.black
        ]
        
        let attributedSectionTitle = NSAttributedString(string: section.title, attributes: sectionTitleAttributes)
        let sectionTitleSize = attributedSectionTitle.size()
        let sectionTitleRect = CGRect(
            x: 50,
            y: currentY,
            width: pageSize.width - 100,
            height: sectionTitleSize.height
        )
        
        attributedSectionTitle.draw(in: sectionTitleRect)
        
        // 绘制分隔线
        context.saveGState()
        UIColor.lightGray.setStroke()
        context.setLineWidth(1)
        context.move(to: CGPoint(x: 50, y: currentY + sectionTitleSize.height + 5))
        context.addLine(to: CGPoint(x: pageSize.width - 50, y: currentY + sectionTitleSize.height + 5))
        context.strokePath()
        context.restoreGState()
        
        currentY += sectionTitleSize.height + 15
        
        // 绘制部分内容
        if !section.content.isEmpty {
            let contentAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 12),
                .foregroundColor: UIColor.black
            ]
            
            let attributedContent = NSAttributedString(string: section.content, attributes: contentAttributes)
            let contentRect = CGRect(
                x: 50,
                y: currentY,
                width: pageSize.width - 100,
                height: 200 // 假设最大高度
            )
            
            let contentTextHeight = attributedContent.boundingRect(
                with: CGSize(width: pageSize.width - 100, height: .greatestFiniteMagnitude),
                options: [.usesLineFragmentOrigin, .usesFontLeading],
                context: nil
            ).height
            
            attributedContent.draw(in: contentRect)
            currentY += contentTextHeight + 15
        }
        
        // 绘制数据点
        if let dataPoints = section.dataPoints, !dataPoints.isEmpty {
            let columns = 2
            let cellWidth = (pageSize.width - 100) / CGFloat(columns)
            let cellHeight: CGFloat = 40
            
            for (index, dataPoint) in dataPoints.enumerated() {
                let column = index % columns
                let row = index / columns
                
                let x = 50 + CGFloat(column) * cellWidth
                let y = currentY + CGFloat(row) * cellHeight
                
                // 绘制数据点标签
                let labelAttributes: [NSAttributedString.Key: Any] = [
                    .font: UIFont.systemFont(ofSize: 10),
                    .foregroundColor: UIColor.darkGray
                ]
                
                let attributedLabel = NSAttributedString(string: dataPoint.label, attributes: labelAttributes)
                let labelRect = CGRect(
                    x: x + 5,
                    y: y + 5,
                    width: cellWidth - 10,
                    height: 15
                )
                
                // 绘制数据点值
                let valueAttributes: [NSAttributedString.Key: Any] = [
                    .font: UIFont.systemFont(ofSize: 14, weight: .medium),
                    .foregroundColor: UIColor.black
                ]
                
                let attributedValue = NSAttributedString(string: dataPoint.value, attributes: valueAttributes)
                let valueRect = CGRect(
                    x: x + 5,
                    y: y + 20,
                    width: cellWidth - 10,
                    height: 20
                )
                
                attributedLabel.draw(in: labelRect)
                attributedValue.draw(in: valueRect)
                
                // 绘制边框
                context.saveGState()
                UIColor.lightGray.withAlphaComponent(0.3).setStroke()
                context.setLineWidth(0.5)
                context.stroke(CGRect(x: x, y: y, width: cellWidth, height: cellHeight))
                context.restoreGState()
            }
            
            let rowCount = Int(ceil(Double(dataPoints.count) / Double(columns)))
            currentY += CGFloat(rowCount) * cellHeight + 15
        }
        
        // 绘制图表（此处仅添加图表标题，实际图表需要更复杂的绘制）
        if let charts = section.charts, !charts.isEmpty {
            for chart in charts {
                let chartTitleAttributes: [NSAttributedString.Key: Any] = [
                    .font: UIFont.systemFont(ofSize: 14, weight: .medium),
                    .foregroundColor: UIColor.darkGray
                ]
                
                let attributedChartTitle = NSAttributedString(string: "图表: \(chart.title)", attributes: chartTitleAttributes)
                let chartTitleRect = CGRect(
                    x: 50,
                    y: currentY,
                    width: pageSize.width - 100,
                    height: 20
                )
                
                attributedChartTitle.draw(in: chartTitleRect)
                currentY += 20
                
                // 简单图表呈现（此处为示例）
                context.saveGState()
                UIColor.lightGray.withAlphaComponent(0.2).setFill()
                context.fill(CGRect(x: 50, y: currentY, width: pageSize.width - 100, height: 100))
                context.restoreGState()
                
                let chartDescriptionAttributes: [NSAttributedString.Key: Any] = [
                    .font: UIFont.systemFont(ofSize: 10),
                    .foregroundColor: UIColor.darkGray
                ]
                
                let chartDescription = "数据单位: \(chart.unit)"
                let attributedChartDescription = NSAttributedString(string: chartDescription, attributes: chartDescriptionAttributes)
                let chartDescriptionRect = CGRect(
                    x: 50,
                    y: currentY + 105,
                    width: pageSize.width - 100,
                    height: 15
                )
                
                attributedChartDescription.draw(in: chartDescriptionRect)
                currentY += 130
            }
        }
        
        return currentY + 20 // 返回下一部分的起始Y位置
    }
    
    /// 绘制页脚
    private func drawFooter(in context: CGContext, pageSize: CGSize) {
        let footerAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 10),
            .foregroundColor: UIColor.darkGray
        ]
        
        let footerText = "由 WindRing 健康应用生成"
        let attributedFooter = NSAttributedString(string: footerText, attributes: footerAttributes)
        let footerSize = attributedFooter.size()
        let footerRect = CGRect(
            x: (pageSize.width - footerSize.width) / 2,
            y: pageSize.height - 40,
            width: footerSize.width,
            height: footerSize.height
        )
        
        // 绘制分隔线
        context.saveGState()
        UIColor.lightGray.setStroke()
        context.setLineWidth(0.5)
        context.move(to: CGPoint(x: 50, y: pageSize.height - 50))
        context.addLine(to: CGPoint(x: pageSize.width - 50, y: pageSize.height - 50))
        context.strokePath()
        context.restoreGState()
        
        attributedFooter.draw(in: footerRect)
    }
    
    /// 格式化日期为字符串
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        return formatter.string(from: date)
    }
}

// MARK: - 视图扩展
extension View {
    /// 添加报告分享功能
    func reportShareSheet(isPresented: Binding<Bool>, report: HRMReport?, onDismiss: (() -> Void)? = nil) -> some View {
        return self.background(
            ReportShareSheet(isPresented: isPresented, report: report, onDismiss: onDismiss)
                .frame(width: 0, height: 0)
        )
    }
}

// MARK: - 报告分享表单
struct ReportShareSheet: UIViewRepresentable {
    @Binding var isPresented: Bool
    let report: HRMReport?
    let onDismiss: (() -> Void)?
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        if isPresented && report != nil {
            let sharingService = ReportSharingService.shared
            sharingService.shareReport(report: report!, from: uiView) {
                isPresented = false
                if let onDismiss = self.onDismiss {
                    onDismiss()
                }
            }
        }
    }
} 