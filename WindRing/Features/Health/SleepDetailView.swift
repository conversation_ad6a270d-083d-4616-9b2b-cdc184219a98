import SwiftUI
import Charts
#if os(iOS)
//import CRPSmartRing
import UIKit // 添加UIKit框架导入，用于提供震动反馈
#endif
import Combine
import HealthKit
// 导入包含StressScoreResponse和StressScoreData模型的文件
import Foundation
// 尝试导入我们创建的API模型文件
// 注意：如果models目录不在main bundle中，可能需要调整导入路径

/// 睡眠详情视图
struct SleepDetailView: View {
    // MARK: - 属性
    // API基础URL
//    private let apiBaseURL = "http://ring-api-dev.weaving-park.com"
    // 使用AppStorage直接读取与AuthService相同的token
//    @AppStorage("auth_token") private var authToken: String?
    // 状态变量
    @Environment(\.presentationMode) var presentationMode
    @ObservedObject private var sharedDateViewModel = SharedDateViewModel.shared
    // 添加日期状态管理
//    @State private var selectedDate = Date()
    @State private var showCalendarPicker: Bool = false
//    @State private var selectedDay: Int = 0 // 当前选中的日偏移量（0表示今天）
    @State private var animateCalendar: Bool = false  // 添加animateCalendar状态变量
    // 添加日历数据状态
    @State private var calendarDatesWithData: [Date] = []
    @State private var isLoadingCalendarData = false
    // 蓝牙连接状态
    @State private var bluetoothState: BluetoothConnectionState = .disconnected
    @State private var isShowingBluetoothAlert = false // 控制蓝牙操作提示框
    @State private var shouldCancelConnection = false // 取消连接标志
    @State private var connectionCheckTimer: AnyCancellable? // 连接状态检查计时器
    @State private var connectedDeviceName = "" // 连接的设备名称
    @State private var showConnectedToast = false // 显示连接成功提示
    // 添加设备服务实例
    @StateObject private var deviceService = WindRingDeviceService.shared
    // 自动上传状态
    @State private var autoUploadEnabled: Bool = false
    
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var healthDataManager: HealthDataManager
//    @State private var selectedDate = Date()
    @State private var showDatePicker = false
    @State private var showCalendarView = false
    @State private var showBluetoothSettings = false
    @State private var showInfoSheet = false
    @State private var infoTitle = ""
    @State private var infoContent = ""
    @State private var untilTime: String = "18:30" // 添加时间显示变量
    
    
    // 添加睡眠数据状态
    @State private var sleepScoreData: SleepScoreData?
    @State private var isLoadingSleepScore = false
    @State private var sleepScoreError: String?
    
    // 添加体温数据状态
    @State private var temperatureData: SleepTemperatureModel?
    @State private var isLoadingTemperature = false
    @State private var temperatureError: String?
    
    // 添加睡眠心率视图模型
    @StateObject private var sleepHeartRateViewModel = SleepHeartRateViewModel()
    @StateObject private var sleepHRVViewModel = SleepHRVViewModel()
    @StateObject private var sleepSpO2ViewModel = SleepSpO2ViewModel() // Added SpO2 ViewModel
    // 计算文字的透明度 - 根据与中心日期的距离
    private func calculateOpacity(dayOffset: Int) -> Double {
        let distance = abs(dayOffset - sharedDateViewModel.selectedDay)
        if distance == 0 {
            return 1.0 // 选中项保持完全不透明
        } else if distance == 1 {
            return 0.8 // 相邻项稍微透明
        } else if distance == 2 {
            return 0.6 // 再远一点的项更透明
        } else {
            return 0.4 // 最远的项最透明
        }
    }
    
    // 计算文字的缩放 - 根据与中心日期的距离
    private func calculateScale(dayOffset: Int) -> CGFloat {
        let distance = abs(dayOffset - sharedDateViewModel.selectedDay)
        if distance == 0 {
            return 1.0 // 选中项不再放大，保持原始大小
        } else if distance == 1 {
            return 1.0 // 相邻项保持原始大小
        } else if distance == 2 {
            return 0.9 // 再远一点的项缩小
        } else {
            return 0.8 // 最远的项最小
        }
    }
    
    // 假数据，实际应用中应该从数据库获取
    @State private var sleepScore: Int = 0
    @State private var sleepScoreLevel: String = "--"
    @State private var sleepTotalTime: String = "--"
    @State private var sleepAsleepTime: String = "--"
    @State private var sleepEfficiency: Int = 0
    
    // 睡眠阶段数据
    @State private var awakePercentage: Double = 0
    @State private var remPercentage: Double = 0
    @State private var corePercentage: Double = 0
    @State private var deepPercentage: Double = 0
    
    @State private var awakeTime: String = "-- min"
    @State private var remTime: String = "-- min"
    @State private var coreTime: String = "-- min"
    @State private var deepTime: String = "-- min"
    
    // 心率和血氧数据
    @State private var heartRateAvg: Int = 0
    @State private var heartRateRecentAvg: Int = 0
    @State private var heartRateHighlight: String = "--bpm --"
    
    @State private var spo2Avg: Int = 0
    @State private var spo2RecentAvg: Int = 0
    @State private var spo2Highlight: String = "--% --"
    
    // 体温数据
    @State private var tempMin: Double = 0.0
    @State private var tempAvg: Double = 0.0
    @State private var tempMax: Double = 0.0
    
    // 睡眠阶段图表数据
    @State private var sleepStagesData: [SleepStage] = []
    
    // 新增：用于跟踪选中的睡眠阶段
    @State private var selectedSleepStage: String? = nil
    
    // 心率图表数据
    @State private var heartRateData: [HrVitalRecord] = []
    
    // 血氧图表数据
    @State private var spo2Data: [SPO2Point] = []
    
    // 睡眠相关提示
    private var sleepTips: [String] {
        ["sleep_tip1".localized, "sleep_tip2".localized, "sleep_tip3".localized]
    }
    
    @State private var selectedTip = 0
    
    // 日期格式化器
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "E"  // 周几缩写
        return formatter
    }()
    
    private let dayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"  // 日期数字
        return formatter
    }()
    
    // 获取本周日期
    private var weekDates: [Date] {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let dayOfWeek = calendar.component(.weekday, from: today)
        let weekdays = calendar.range(of: .weekday, in: .weekOfYear, for: today)!
        let days = (weekdays.lowerBound..<weekdays.upperBound)
            .compactMap { calendar.date(byAdding: .day, value: $0 - dayOfWeek, to: today) }
        return days
    }
    
    // 添加睡眠详情数据状态
    @State private var sleepDetailsData: [SleepDetailData] = []
    @State private var isLoadingSleepDetails = false
    @State private var sleepDetailsError: String?
    @State private var sleepHighlightPeriod: String = "12:30-4:00" // 高亮显示的睡眠区间
    
    // 在SleepDetailView类中添加状态变量
    @State private var showHighlightInfo: Bool = false
    @State private var sleepStageLabel: String = "快速眠动"
    
    // 在属性区添加：
    @State private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 主视图
    var body: some View {
        VStack(spacing: 0) {
            // 顶部状态栏
            VStack(spacing: 12) {
                // 自定义导航栏
                HStack {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .foregroundColor(.white)
                                .font(.system(size: 16))
                            
                            Text("sleep_detail_nav_title".localized)
                                .foregroundColor(.white)
                                .font(.system(size: 16, weight: .bold))
                        }
                    }
                                
                                Spacer()
                                
                    // 右侧图标按钮
                    HStack(spacing: 16) {
                            // 日历按钮 - Insight风格
                        Button(action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                showCalendarPicker = true
                            }
                        }) {
                            Image("日期 (2)")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 16, height: 16)
                        }
                        
                            // 蓝牙按钮 - 与Insight页面相同的实现
                            Button(action: {
                                // 蓝牙按钮点击逻辑
                                handleBluetoothButtonTap()
                            }) {
                                ZStack {
                                    // 根据连接状态显示不同图标
                                    if bluetoothState == .disconnected {
                                        // 断开连接时显示断连图标
                                        Image("断连")
                                .resizable()
                                            .frame(width: 16, height: 16)
                                    } else if bluetoothState == .connected {
                                        // 已连接状态显示连接状态图标 - 使用蓝色圆环
                                        Image("连联")
                                            .resizable()
                                            .frame(width: 16, height: 16)
                                    } else if bluetoothState == .connecting {
                                        // 连接中状态的子视图
                                        ConnectingStateView()
                                .frame(width: 16, height: 16)
                                    }
                                }
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 12) // 为状态栏留出更多空间
                .padding(.bottom, 8) // 增加底部间距，给日期选择器留出更多空间
            }
            
            // 日期选择器 - 作为固定的顶部元素
            DateSelectionView(
//                selectedDay: $sharedDateViewModel.selectedDay,
                selectedDate: $sharedDateViewModel.selectedDate,
                onDateSelected: { day, date in
                    // 加载数据
                    loadSleepData(for: date)
                    print("SleepDetailView - 日期已切换: \(date), 开始加载新数据")
                }
            )
            .zIndex(1) // 确保日期选择器始终位于顶层
            .padding(.bottom, 0) // 减少底部间距
            
            // 可滚动内容区域
            ScrollView {
                VStack(spacing: 10) { // 减少间距
                    // 添加顶部间距，使Sleep Score卡片下移10pt
                    Spacer().frame(height: 8)
                    
                    // 合并睡眠评分和时间汇总卡片
                    combinedSleepScoreAndTimeCard
                    
                    // 睡眠状态分析
                    sleepStateCard
                        .padding(.top, 14)
                        .padding(.bottom, 2) // 减少底部边距
                    
                    // 睡眠阶段图表
                    sleepStagesCard
                        .padding(.top, 8) // 添加负的顶部边距
                    
                    ///是否隐藏
                    if VersionUpdateService.shared.status == 1{
                        // 心率图表
                        heartRateCard
                            .padding(.top, 12) // 将顶部外部间距从20降低到12
                        
                        // HRV图表
                        hrvCard
                            .padding(.top, 12)
                        
                        // SpO2 Card - Updated to use ViewModel
                        sleepSpO2Card // Updated card
                            .padding(.top, 10) // 与上面的卡片保持10的间距
                        
                        // 体温数据
                        temperatureCard
                    }
                    
                    Spacer(minLength: 30)
                }
                .padding(.horizontal)
                .padding(.top, 0) // 减少顶部间距
            }
        }
        .background(Color.appBackground.edgesIgnoringSafeArea(.all))
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true) // 完全隐藏系统导航栏
        .sheet(isPresented: $showDatePicker) {
            VStack {
                DatePicker("sleep_select_date".localized, selection: $sharedDateViewModel.selectedDate, displayedComponents: .date)
                    .datePickerStyle(GraphicalDatePickerStyle())
                    .padding()
                
                Button("sleep_done_button".localized) {
                    showDatePicker = false
                    loadSleepData(for: sharedDateViewModel.selectedDate)
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
                .padding()
            }
            .background(Color(UIColor.systemBackground))
        }
        .sheet(isPresented: $showCalendarView) {
            NavigationView {
                VStack {
                    DatePicker("sleep_select_date".localized, selection: $sharedDateViewModel.selectedDate, displayedComponents: .date)
                        .datePickerStyle(GraphicalDatePickerStyle())
                        .padding()
                    
                    List {
                        ForEach(0..<10) { index in
                            let daysAgo = -index
                            let date = Calendar.current.date(byAdding: .day, value: daysAgo, to: Date())!
                            
                Button(action: {
                                sharedDateViewModel.selectedDate = date
                                showCalendarView = false
                                loadSleepData(for: date)
                            }) {
                                HStack {
                                    VStack(alignment: .leading) {
                                        Text(dateFormatter.string(from: date))
                                            .font(.headline)
                                        Text(dayFormatter.string(from: date))
                                            .font(.subheadline)
                                    }
                                    Spacer()
                                    VStack(alignment: .trailing) {
                                        Text("Sleep Score")
                                            .font(.caption)
                                        Text("\(Int.random(in: 65...95))")
                                            .font(.title3)
                                    }
                                }
                            }
                        }
                    }
                }
                .navigationTitle("sleep_history_title".localized)
                .navigationBarItems(trailing: Button("sleep_close_button".localized) {
                    showCalendarView = false
                })
            }
        }
        .sheet(isPresented: $showBluetoothSettings) {
            NavigationView {
                List {
                    Section(header: Text("sleep_bt_connected_devices".localized)) {
                        deviceRow(name: "Smart Watch", connected: true)
                        deviceRow(name: "Sleep Tracker", connected: true)
                    }
                    
                    Section(header: Text("sleep_bt_available_devices".localized)) {
                        deviceRow(name: "Smart Band", connected: false)
                        deviceRow(name: "Health Monitor", connected: false)
                    }
                }
                .navigationTitle("sleep_bt_devices_title".localized)
                .navigationBarItems(trailing: Button("sleep_close_button".localized) {
                    showBluetoothSettings = false
                })
            }
        }
        .sheet(isPresented: $showInfoSheet) {
            NavigationView {
                ScrollView {
                    VStack(alignment: .leading, spacing: 15) {
                        Text(infoContent)
                            .padding()
                    }
                }
                .navigationTitle(infoTitle)
                .navigationBarItems(trailing: Button("sleep_close_button".localized) {
                    showInfoSheet = false
                })
            }
        }
        .onAppear {
            // 不再生成示例数据
            
            // 加载数据 - 直接调用API获取睡眠评分数据
            loadSleepData(for: sharedDateViewModel.selectedDate)
            
            // 随机选择一个提示
            selectedTip = Int.random(in: 0..<sleepTips.count)
            
            print("SleepDetailView - onAppear: 开始获取睡眠数据，日期=\(sharedDateViewModel.selectedDate.string())")
            
            // 同步蓝牙状态
            syncBluetoothState()
            startConnectionStateChecking()
        }
        .onDisappear {
            connectionCheckTimer?.cancel()
        }
        // 日历选择器浮层
        .overlay(
                ZStack {
                if showCalendarPicker {
                    // 磨砂半透明背景
                    Color.black.opacity(0.8)
                        .ignoresSafeArea()
                        .blur(radius: 5)
                                .onTapGesture {
                            closeCalendarPicker()
                        }
                    
                    // 自定义日历视图
                    CustomCalendarView(
                        selectedDate: $sharedDateViewModel.selectedDate,
//                        selectedDay: $sharedDateViewModel.selectedDay,
                        onClose: closeCalendarPicker
                    )
                    .frame(width: UIScreen.main.bounds.width * 0.85)
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .animation(.spring(response: 0.35, dampingFraction: 0.8), value: showCalendarPicker)
        )
    }
    
    // MARK: - 合并睡眠评分和时间汇总卡片
    private var combinedSleepScoreAndTimeCard: some View {
        // 使用一个整体的容器，不含任何内容分隔
        VStack(spacing: 0) {
            // 直接在卡片内部放置所有内容，没有外部元素
        ZStack {
            // 背景视图
            Rectangle()
                    .fill(Color(red: 0.06, green: 0.07, blue: 0.1))
                    .cornerRadius(25)
            
                // 所有内容放在内部VStack中
                VStack(alignment: .leading, spacing: 0) {
                    // Sleep Score 标题作为内容的第一项
                HStack {
                    Text("sleep_detail_score_title".localized)
                            .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                    
                    InfoButtonWithGlossaryPopup(showKey: GlossaryKey.sleepScore.rawValue)
                
                    Spacer()
                }
                    .padding(.top, 16)
                    .padding(.horizontal, 16)
                
                // 圆环评分
                    HStack {
                        Spacer()
                    ZStack {
                            // 圆环背景 - 使用深色背景
                            Circle()
                                .fill(Color(red: 0.12, green: 0.14, blue: 0.18))
                                .frame(width: 160, height: 160)
                            
                            // 圆环背景轨道
                        Circle()
                        .stroke(LinearGradient(
                            gradient: Gradient(colors: [
                                Color(red: 0.1, green: 0.3, blue: 0.9).opacity(0.3),
                                Color(red: 0.1, green: 0.8, blue: 0.7).opacity(0.3)
                            ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                                ), lineWidth: 16)
                                .frame(width: 145, height: 145)
                            
                            // 显示刻度线
                            ForEach(0..<12) { i in
                                Rectangle()
                                    .fill(Color.white.opacity(0.3))
                                    .frame(width: 1, height: 5)
                                    .offset(y: -72)
                                    .rotationEffect(.degrees(Double(i) * 30))
                            }
                            
                            // 当前刻度指示器
                            Rectangle()
                                .fill(Color.white)
                                .frame(width: 2, height: 8)
                                .offset(y: -72)
                                .rotationEffect(.degrees(Double(sleepScore) * 3.6))
                    
                    // 进度圆环
                    Circle()
                        .trim(from: 0, to: sleepScore > 0 ? CGFloat(sleepScore) / 100 : 0.001)
                        .stroke(LinearGradient(
                            gradient: Gradient(colors: [
                                Color(red: 0.1, green: 0.3, blue: 0.9),
                                Color(red: 0.1, green: 0.8, blue: 0.7)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                                ), style: StrokeStyle(lineWidth: 16, lineCap: .round))
                                .frame(width: 145, height: 145)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut(duration: 1.0), value: sleepScore)
                    
                    // 分数和等级
                            VStack(spacing: 4) {
                            Text(sleepScore > 0 ? "\(sleepScore)" : "--")
                            .font(.system(size: 36, weight: .bold))
                                .foregroundColor(.white)
                            
                        Text(sleepScoreLevel)
                            .font(.system(size: 16))
                            .foregroundColor(Color(red: 0.1, green: 0.8, blue: 0.7))
                    }
                }
                        Spacer()
                    }
                    .padding(.vertical, 5)
                    .padding(.top, 15) // 添加顶部边距，让整个圆盘区域下移
                
                // 提示信息
                HStack {
                        Image("提醒")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                        .foregroundColor(Color(red: 0.1, green: 0.8, blue: 0.7))
                    
                    Text(sleepTips[selectedTip])
                        .font(.system(size: 12))
                        .foregroundColor(Color(red: 0.1, green: 0.8, blue: 0.7))
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
                    .padding(.vertical, 5) // 减少垂直内边距
                    .padding(.horizontal, 12)
                    .frame(minHeight: 26.5, maxHeight: 26.5) // 使用minHeight和maxHeight替代height
                    .background(Color.clear)
                    .cornerRadius(10)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color(red: 0.1, green: 0.8, blue: 0.7).opacity(0.3), lineWidth: 0.5)
                    )
                    .padding(.horizontal, 16)
                    .padding(.top, 10)
                    .padding(.bottom, 5) // 增加提示信息与下方内容的间距
                    
                    Spacer(minLength: 15)
                    
                    // 第二部分：睡眠时间汇总 - 使用与背景协调的颜色
                    HStack(spacing: 10) {
            // 总睡眠时间
            VStack(spacing: 5) {
                Text("sleep_detail_total_time_asleep".localized)
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                
                Text(sleepTotalTime)
                                .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
            }
                        .padding(.vertical, 10)
                        .frame(maxWidth: .infinity, minHeight: 76, maxHeight: 76) // 使用minHeight和maxHeight替代height
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 0.08, green: 0.09, blue: 0.13), // #141821
                                    Color(red: 0.16, green: 0.19, blue: 0.25)  // #2A3040
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(10)
            
            // 清醒时间
            VStack(spacing: 5) {
                Text("sleep_detail_time_asleep".localized)
                    .font(.system(size: 12))
                        .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                
                Text(sleepAsleepTime)
                                .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                }
                        .padding(.vertical, 10)
                        .frame(maxWidth: .infinity, minHeight: 76, maxHeight: 76) // 使用minHeight和maxHeight替代height
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 0.08, green: 0.09, blue: 0.13), // #141821
                                    Color(red: 0.16, green: 0.19, blue: 0.25)  // #2A3040
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(10)
            
            // 睡眠效率
            VStack(spacing: 5) {
                Text("sleep_detail_efficiency".localized)
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                
                Text(sleepEfficiency > 0 ? "\(sleepEfficiency)%" : "--")
                                .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
                        .padding(.vertical, 10)
                        .frame(maxWidth: .infinity, minHeight: 76, maxHeight: 76) // 使用minHeight和maxHeight替代height
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 0.08, green: 0.09, blue: 0.13), // #141821
                                    Color(red: 0.16, green: 0.19, blue: 0.25)  // #2A3040
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(10)
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 16)
                }
            }
            .frame(minHeight: 360.5, maxHeight: 360.5) // 使用minHeight和maxHeight替代height
        }
//        .onTapGesture {
//            // 点击睡眠评分卡片显示详细信息
//            infoTitle = "sleep_detail_score_info_title".localized
//            infoContent = String(format: "sleep_detail_score_info_content_format".localized, sleepScore, sleepScoreLevel) + "\n\n" +
//                        "sleep_detail_score_info_breakdown".localized + "\n" +
//                        String(format: "sleep_detail_score_info_duration".localized, Int.random(in: 70...95)) + "\n" +
//                        String(format: "sleep_detail_score_info_quality".localized, Int.random(in: 70...95)) + "\n" +
//                        String(format: "sleep_detail_score_info_consistency".localized, Int.random(in: 70...95))
//            showInfoSheet = true
//        }
    }
    
    // 关闭日历选择器
    private func closeCalendarPicker() {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                showCalendarPicker = false
        }
    }
    // MARK: - 蓝牙相关函数
    // 蓝牙按钮点击逻辑
    private func handleBluetoothButtonTap() {
        switch bluetoothState {
        case .disconnected:
            // 断开状态下点击，尝试连接之前的设备
            connectLastDevice()
        case .connecting:
            // 连接中状态下点击，取消连接
            cancelConnecting()
        case .connected:
            // 已连接状态下点击，显示断开连接提示
            isShowingBluetoothAlert = true
        }
    }
    
    // 尝试连接上次的设备
    private func connectLastDevice() {
        bluetoothState = .connecting
        shouldCancelConnection = false
        
        print("尝试连接设备...")
        
        // 首先检查设备服务是否有上次连接的设备
        if let lastMac = deviceService.lastConnectedDeviceMAC, !lastMac.isEmpty {
            print("尝试连接上次设备: \(lastMac)")
            
            // 先断开当前连接（如果有）
            if deviceService.connectionState.isConnected {
                deviceService.disconnectDevice()
            }
            
            // 开始扫描
            deviceService.startScan(duration: 10)
            
            // 设置10秒超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [self] in
                guard bluetoothState == .connecting else { return }
                
                // 检查是否已连接
                if !deviceService.connectionState.isConnected && !shouldCancelConnection {
                    bluetoothState = .disconnected
                    print("连接超时")
                }
            }
            
            // 尝试连接
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [self] in
                guard !shouldCancelConnection else { return }
                
                if let discovery = deviceService.discoveredDevices.first(where: {
                    $0.mac?.uppercased() == lastMac.uppercased()
                }) {
                    print("找到之前设备，开始连接")
                    deviceService.connectDevice(discovery: discovery)
                    
                    // 连接成功后记录设备名称，用于显示提示
                    if let deviceName = discovery.localName, !deviceName.isEmpty {
                        connectedDeviceName = deviceName
                    } else {
                        connectedDeviceName = "设备"
                    }
                }
            }
        } else {
            print("无之前连接的设备记录")
            
            // 开始扫描，连接任何发现的设备
            deviceService.startScan(duration: 5)
            
            // 设置扫描超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 6) { [self] in
                guard bluetoothState == .connecting else { return }
                
                if !deviceService.discoveredDevices.isEmpty && !shouldCancelConnection {
                    let firstDevice = deviceService.discoveredDevices.first!
                    print("尝试连接发现的设备: \(firstDevice.localName ?? "未知设备")")
                    deviceService.connectDevice(discovery: firstDevice)
                    
                    // 连接超时
                    DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [self] in
                        guard bluetoothState == .connecting else { return }
                        
                        if !deviceService.connectionState.isConnected && !shouldCancelConnection {
                            bluetoothState = .disconnected
                            print("连接超时")
                        }
                    }
                } else {
                    // 没有发现设备
                    bluetoothState = .disconnected
                    print("未发现设备")
                }
            }
        }
    }
    
    // 取消连接过程
    private func cancelConnecting() {
        shouldCancelConnection = true
        bluetoothState = .disconnected
        
        // 取消设备服务的连接操作
        if deviceService.connectionState == .connecting {
            deviceService.disconnectDevice()
        }
        
        if deviceService.isScanning {
            deviceService.stopScan()
        }
    }
    
    // 断开蓝牙连接
    private func disconnectBluetooth() {
        // 执行断开连接逻辑
        bluetoothState = .disconnected
        deviceService.disconnectDevice()
    }
    
    // 开始定期检查连接状态
    private func startConnectionStateChecking() {
        // 取消可能存在的计时器
        connectionCheckTimer?.cancel()
        
        // 创建新计时器，每1秒检查一次
        connectionCheckTimer = Timer.publish(every: 1, on: .main, in: .common)
            .autoconnect()
            .sink { [self] _ in
                syncBluetoothState()
                
                // 如果设备已连接但UI未显示连接状态，立即更新
                if deviceService.connectionState == .connected && bluetoothState != .connected {
                    bluetoothState = .connected
                    print("发现设备已连接，更新UI状态")
                    
                    // 显示连接成功提示
                    if !showConnectedToast {
                        if let deviceName = deviceService.currentDiscovery?.localName, !deviceName.isEmpty {
                            connectedDeviceName = deviceName
                        } else {
                            connectedDeviceName = "设备"
                        }
                        showConnectedToast = true
                        
                        // 3秒后隐藏提示
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                            withAnimation {
                                showConnectedToast = false
                            }
                        }
                    }
                }
                
                // 如果设备已断开但UI仍显示连接状态，立即更新
                if deviceService.connectionState != .connected && bluetoothState == .connected {
                    bluetoothState = .disconnected
                    print("发现设备已断开，更新UI状态")
                }
            }
    }
    
    // 同步蓝牙状态
    private func syncBluetoothState() {
        let previousState = bluetoothState
        
        // 先检查设备服务的连接状态
        if deviceService.connectionState == .connected {
            // 如果设备已连接，直接更新UI状态
            if bluetoothState != .connected {
                bluetoothState = .connected
                
                // 如果状态从非连接变为连接，更新设备名称但不显示提示
                if previousState != .connected {
                    connectedDeviceName = deviceService.currentDiscovery?.localName ?? "设备"
                }
            }
            return
        }
        
        // 连接中状态的特殊处理
        if bluetoothState == .connecting {
            if deviceService.connectionState == .connected {
                bluetoothState = .connected
            } else if deviceService.connectionState == .disconnected {
                if !shouldCancelConnection {
                    bluetoothState = .disconnected
                }
            }
        } else {
            // 常规状态同步
            if deviceService.connectionState == .connected && bluetoothState != .connected {
                bluetoothState = .connected
            } else if deviceService.connectionState != .connected && bluetoothState == .connected {
                bluetoothState = .disconnected
            }
        }
    }
    
    /// 更新自动上传设置
    private func updateAutoUploadSetting(enabled: Bool) {
        if enabled {
            // 启用自动上传
            let success = RawDataUploadService.shared.startAutoUpload(interval: 300) // 5分钟
            if !success {
                // 如果启动失败，在1秒后重试
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                    let retrySuccess = RawDataUploadService.shared.startAutoUpload(interval: 300)
                    if !retrySuccess {
                        // 如果重试仍然失败，5秒后再次尝试
                        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                            _ = RawDataUploadService.shared.startAutoUpload(interval: 300)
                            // 无论成功与否，都保持状态为开启
                            self.autoUploadEnabled = true
                        }
                    }
                }
            }
        } else {
            // 禁用自动上传 - 由于我们希望保持开启，这部分代码不会被执行
            // RawDataUploadService.shared.stopAutoUpload()
            
            // 强制重新启用自动上传
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.autoUploadEnabled = true
                _ = RawDataUploadService.shared.startAutoUpload(interval: 300)
            }
        }
    }
    
    // MARK: - 蓝牙设备行视图
    private func deviceRow(name: String, connected: Bool) -> some View {
        HStack {
            Text(name)
            Spacer()
            if connected {
                Text("sleep_bt_status_connected".localized)
                    .foregroundColor(.green)
                    .font(.caption)
            } else {
                Button(action: {
                    // 连接设备
                }) {
                    Text("sleep_bt_status_connect".localized)
                        .foregroundColor(.blue)
                }
            }
        }
    }
    
    // MARK: - 睡眠状态卡片
    private var sleepStateCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("sleep_detail_state_title".localized)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .padding(.bottom, 2)
            
            // 判断是否有数据
            if sleepScore > 0 || awakePercentage > 0 || remPercentage > 0 || corePercentage > 0 || deepPercentage > 0 {
                // 有数据时显示睡眠阶段
            // 清醒
            sleepStateRow(
                title: "sleep_stage_awake".localized,
                percentage: awakePercentage,
                time: awakeTime,
                color: .white
            )
            
            // REM
            sleepStateRow(
                title: "sleep_stage_rem".localized,
                percentage: remPercentage,
                time: remTime,
                color: Color(red: 0.0, green: 0.8, blue: 0.8)
            )
            
            // Core
            sleepStateRow(
                title: "sleep_stage_core".localized,
                percentage: corePercentage,
                time: coreTime,
                color: Color(red: 0.3, green: 0.2, blue: 0.8)
            )
            
            // Deep
            sleepStateRow(
                title: "sleep_stage_deep".localized,
                percentage: deepPercentage,
                time: deepTime,
                color: Color(red: 0.3, green: 0.2, blue: 0.8)
            )
            } else {
                // 无数据时显示占位符
                sleepStateNoDataRow(title: "sleep_stage_awake".localized, time: "--")
                sleepStateNoDataRow(title: "sleep_stage_rem".localized, time: "--")
                sleepStateNoDataRow(title: "sleep_stage_core".localized, time: "--")
                sleepStateNoDataRow(title: "sleep_stage_deep".localized, time: "--")
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 20)
        .frame(minHeight: 250, maxHeight: 250) // 增加高度以适应更高的进度条
        .background(Color(red: 0.06, green: 0.07, blue: 0.1))
        .cornerRadius(25)
        .shadow(color: Color.black.opacity(0.2), radius: 10, x: 0, y: 5)
        .onAppear {
            // 触发加载动画
            withAnimation(.easeInOut(duration: 1.0).delay(0.3)) {
                // 动画会在状态改变时触发
            }
        }
    }
    
    // 无数据时的睡眠状态行
    private func sleepStateNoDataRow(title: String, time: String) -> some View {
        let progressBarBackgroundColor = Color(red: 0.15, green: 0.16, blue: 0.2)

        return HStack(spacing: 4) { // MODIFIED: spacing from 8 to 4
            Text(title)
                .font(.system(size: 12))
                .foregroundColor(.white)
                .frame(width: 60, alignment: .leading)
            
            // Expanding progress bar container
            GeometryReader { geo in
            ZStack(alignment: .leading) {
                    // Background track - fills the entire GeometryReader space
                Rectangle()
                        .fill(progressBarBackgroundColor)
                    .cornerRadius(6)
                
                    // "--" Percentage Text (inside the bar, towards the right)
                HStack {
                        Spacer() // Pushes text to the right
                    Text("--")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.trailing, 8) // Padding from the right edge of the bar
            }
                    .frame(width: geo.size.width) // Ensure HStack takes full width of bar area
                }
            }
            .frame(height: 27.5) // Height for the progress bar container

            // Right-aligned group for indicator and time (now separate from progress bar)
            HStack(spacing: 4) { // MODIFIED: spacing from 6 to 4
            RoundedRectangle(cornerRadius: 1.5)
                .fill(Color.gray.opacity(0.5))
                    .frame(width: 3, height: 10)
            
                Text(time) // Time, which is passed as "--"
                    .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white)
                    .frame(width: 45, alignment: .trailing) // MODIFIED: width from 55 to 45
            }
        }
        .padding(.vertical, 1)
        .contentShape(Rectangle())
    }
    
    // 睡眠状态行
    private func sleepStateRow(title: String, percentage: Double, time: String, color: Color) -> some View {
        let progressBarBackgroundColor = Color(red: 0.15, green: 0.16, blue: 0.2) // Background of the track
        let unselectedBarColor = Color(red: 0.25, green: 0.26, blue: 0.30) // Default color for non-REM bars, and all unselected bars.

        let isSelected = (selectedSleepStage == title)

        // Colors for AnimatedProgressBar
        var barStartColor: Color
        var barEndColor: Color

        if isSelected {
            switch title {
            case "sleep_stage_awake".localized:
                barStartColor = indicatorColorForStage("Awake") // Blue 0BBDE3
                barEndColor = indicatorColorForStage("Awake")
            case "sleep_stage_rem".localized:
                barStartColor = colorFromHex("007CF8")         // Original REM bar start color
                barEndColor = colorFromHex("00D4D8")           // Original REM bar end color
            case "sleep_stage_core".localized:
                barStartColor = indicatorColorForStage("Core")  // Yellow-green AEE30B
                barEndColor = indicatorColorForStage("Core")
            case "sleep_stage_deep".localized:
                barStartColor = indicatorColorForStage("Deep")  // Orange-yellow E3BD0B
                barEndColor = indicatorColorForStage("Deep")
            default: // Should not be reached
                barStartColor = unselectedBarColor
                barEndColor = unselectedBarColor
            }
        } else { // Not selected
            barStartColor = unselectedBarColor
            barEndColor = unselectedBarColor
        }

        // Color for the small indicator
        let finalIndicatorColor = isSelected ? indicatorColorForStage(title) : Color.gray.opacity(0.5)


        return HStack(spacing: 4) { // MODIFIED: spacing from 8 to 4
            Text(title)
                            .font(.system(size: 12))
                            .foregroundColor(.white)
                .frame(width: 80, alignment: .leading)
            
            // Expanding progress bar container
            GeometryReader { geo in
                ZStack(alignment: .leading) {
                    // Background track - fills the entire GeometryReader space
                    Rectangle()
                        .fill(progressBarBackgroundColor)
                    .cornerRadius(6)
                
                    // Progress fill
                        AnimatedProgressBar(
                            percentage: percentage,
                        startColor: barStartColor,
                        endColor: barEndColor,
                        availableWidth: geo.size.width
                    )
                .cornerRadius(6)
                
                    // Percentage Text (inside the bar, towards the right)
                HStack {
                        Spacer() // Pushes text to the right
            Text("\(Int(percentage))%")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white) // White text as per target
                            .padding(.trailing, 8) // Padding from the right edge of the bar
            }
                    .frame(width: geo.size.width) // Ensure HStack takes full width of bar area
                }
            }
            .frame(height: 27.5) // Height for the progress bar container

            // Right-aligned group for indicator and time (now separate from progress bar)
            HStack(spacing: 4) { // MODIFIED: spacing from 6 to 4
                RoundedRectangle(cornerRadius: 1.5) // Small color indicator
                    .fill(finalIndicatorColor)
                    .frame(width: 3, height: 10) // Taller indicator

            Text(time)
                    .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white)
                    .frame(width: 45, alignment: .trailing) // MODIFIED: width from 55 to 45
        }
        }
        .padding(.vertical, 1) // Keep existing vertical padding for the row
        .contentShape(Rectangle()) // Keep existing
        .onTapGesture {
            selectedSleepStage = (selectedSleepStage == title) ? nil : title
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                // 轻微震动反馈效果（实际项目中可以使用UIImpactFeedbackGenerator提供触觉反馈）
            }
        }
    }
    
    // 为每个睡眠阶段返回特定的彩色指示器
    private func indicatorColorForStage(_ stage: String) -> Color {
        switch stage {
            case "Awake":
                return colorFromHex("0BBDE3") // 蓝色
            case "REM":
                return colorFromHex("00E3BC") // 青绿色
            case "Core":
                return colorFromHex("AEE30B") // 黄绿色
            case "Deep":
                return colorFromHex("E3BD0B") // 橙黄色
            default:
                return Color.gray
        }
    }
    
    // 用于处理十六进制颜色代码的扩展
    private func colorFromHex(_ hex: String) -> Color {
        let scanner = Scanner(string: hex)
        scanner.currentIndex = hex.startIndex
        var rgbValue: UInt64 = 0
        scanner.scanHexInt64(&rgbValue)
        
        let r = Double((rgbValue & 0xFF0000) >> 16) / 255.0
        let g = Double((rgbValue & 0x00FF00) >> 8) / 255.0
        let b = Double(rgbValue & 0x0000FF) / 255.0
        
        return Color(red: r, green: g, blue: b)
    }
    
    // 动画进度条
    private struct AnimatedProgressBar: View {
        let percentage: Double
        let startColor: Color
        let endColor: Color
        let availableWidth: CGFloat // New parameter for dynamic width
        
        @State private var displayedProgress: Double = 0 // For animation
        
        var body: some View {
            Rectangle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [startColor, endColor]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .frame(width: availableWidth * (displayedProgress / 100.0))
                .onAppear {
                    // 初始动画
                    withAnimation(.easeOut(duration: 1.0)) {
                        displayedProgress = percentage
                    }
                }
                .onChange(of: percentage) { newPercentage in
                     withAnimation(.easeOut(duration: 1.0)) {
                        displayedProgress = newPercentage
                    }
                }
        }
    }
    
    // MARK: - 睡眠阶段图表
    private var sleepStagesCard: some View {
        // 定义共享的睡眠阶段颜色常量 - 确保颜色精确匹配图片所示
        let awakeColor = Color(red: 0.0, green: 0.74, blue: 0.89)  // 亮蓝色 #00BCDF
        let remColor = Color(red: 0.0, green: 0.89, blue: 0.74)    // 绿松石色 #00E3BC
        let coreColor = Color(red: 0.55, green: 0.89, blue: 0.04)  // 黄绿色 #8CE30B
        let deepColor = Color(red: 0.89, green: 0.74, blue: 0.04)  // 橙黄色 #E3BD0B
        
        return VStack(alignment: .leading, spacing: 15) {
            Text("sleep_stages_card_title".localized)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
                .padding(.bottom, 2)
                
            // 图例（右对齐）
            HStack(spacing: 16) {
                Spacer()
                CircleLegendItem(color: awakeColor, label: "sleep_stage_awake".localized)
                CircleLegendItem(color: remColor, label: "sleep_stage_rem".localized)
                CircleLegendItem(color: coreColor, label: "sleep_stage_core".localized)
                CircleLegendItem(color: deepColor, label: "sleep_stage_deep".localized)
            }
            .padding(.bottom, 5)
            
            // 高亮信息框区域 - 保持固定高度
            Group {
                if showHighlightInfo {
                    Text("\(sleepStageLabel) \(sleepHighlightPeriod)")
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.6))
                        .padding(.horizontal, 10)
                        .frame(height: 20)
                        .background(
                            RoundedRectangle(cornerRadius: 5)
                                .fill(colorFromHex("272C38")) // 保持原有背景色
                        )
                } else {
                    // 空的占位视图，保持相同的高度和间距
                    Color.clear
                        .frame(height: 20)
                }
            }
            .padding(.top, 5)
            .padding(.bottom, 5)
            .frame(maxWidth: .infinity, alignment: .center)
            
            // 新的睡眠阶段图表 - 传递共享的颜色
            newSleepStagesChart(awakeColor: awakeColor, remColor: remColor, coreColor: coreColor, deepColor: deepColor)
                .frame(height: 160)
        }
        .padding()
        .background(Color(red: 0.06, green: 0.07, blue: 0.1))
        .cornerRadius(25)
    }
    
    // 新的图例项
    private struct CircleLegendItem: View {
        let color: Color
        let label: String
        
        var body: some View {
            HStack(spacing: 6) {
                            Circle()
                .fill(color)
                    .frame(width: 10, height: 10)
                            
            Text(label)
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.7))
            }
        }
    }
    
    // 新的睡眠阶段图表 - 接收颜色参数
    private func newSleepStagesChart(awakeColor: Color, remColor: Color, coreColor: Color, deepColor: Color) -> some View {
        VStack(spacing: 0) {
            // 主图表区域
            ZStack(alignment: .topLeading) {
                // 移除背景填充，使表格透明
                
                // 简化的网格线 - 只有4条水平线和4条垂直线
                Group {
                    // 水平线
                    ForEach(0..<5) { row in
                        Path { path in
                            let yPosition = row == 0 ? 0 : (35 + CGFloat(row - 1) * 35)
                            path.move(to: CGPoint(x: 0, y: yPosition))
                            path.addLine(to: CGPoint(x: 330, y: yPosition))
                        }
                        .stroke(Color.gray.opacity(0.3), lineWidth: 0.5)
                    }
                    
                    // 垂直线 - 改为虚线
                    ForEach(0..<5) { column in
                        Path { path in
                            path.move(to: CGPoint(x: 65 + CGFloat(column) * 60, y: 0))
                            path.addLine(to: CGPoint(x: 65 + CGFloat(column) * 60, y: 140))
                        }
                        .stroke(Color.gray.opacity(0.3), style: StrokeStyle(lineWidth: 0.5, dash: [3, 3]))
                    }
                }
                
                // 睡眠阶段标签(每个放在自己的格子里居中)
                // Awake - 第一行
                Text("sleep_stage_awake".localized)
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(width: 65, height: 35)
                    .position(x: 32.5, y: 17.5)
                
                // REM - 第二行
                Text("sleep_stage_rem".localized)
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(width: 65, height: 35)
                    .position(x: 32.5, y: 52.5)
                
                // Core - 第三行
                Text("sleep_stage_core".localized)
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(width: 65, height: 35)
                    .position(x: 32.5, y: 87.5)
                
                // Deep - 第四行
                Text("sleep_stage_deep".localized)
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(width: 65, height: 35)
                    .position(x: 32.5, y: 122.5)
                
                // 睡眠数据可视化 - 动态显示API返回的数据
                Group {
                    if !sleepDetailsData.isEmpty {
                        // 使用实际API数据绘制睡眠阶段
                        SleepStagesFromAPIData(
                            sleepData: sleepDetailsData,
                            awakeColor: awakeColor,
                            remColor: remColor,
                            coreColor: coreColor,
                            deepColor: deepColor,
                            showHighlightInfo: $showHighlightInfo,
                            sleepStageLabel: $sleepStageLabel,
                            sleepHighlightPeriod: $sleepHighlightPeriod
                        )
                    } else {
                        // 如果没有API数据，显示"--"占位符
                        VStack {
                            Spacer()
                            HStack {
                                Spacer()
                                Text("--")
                                    .font(.system(size: 16))
                                    .foregroundColor(.gray)
                                Spacer()
                            }
                            Spacer()
                        }
                        .frame(width: 265, height: 140)
                    }
                }
            }
            .frame(width: 330, height: 145)
            .padding(.bottom, 3)
            
            // 底部时间轴 - 修改为精确定位到每根垂直线的下方
            ZStack {
                // 20:00 - 第一根竖线
                Text("20:00")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .position(x: 65, y: 12)
                
                // 23:00 - 第二根竖线
                Text("23:00")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .position(x: 65 + 60, y: 12)
                
                // 02:00 - 第三根竖线
                Text("03:00")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .position(x: 65 + 60 * 2, y: 12)
                
                // 06:00 - 第四根竖线，从05:00改为06:00
                Text("06:00")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .position(x: 65 + 60 * 3, y: 12)
                
                // 10:00 - 第五根竖线，从08:00改为10:00
                Text("10:00")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .position(x: 65 + 60 * 4, y: 12)
            }
            .frame(width: 330, height: 24)
        }
        .padding(.vertical, 3)
        .frame(width: 330)
    }
    
    // 从API数据绘制睡眠阶段图表
    private struct SleepStagesFromAPIData: View {
        let sleepData: [SleepDetailData]
        let awakeColor: Color  // 使用传入的颜色
        let remColor: Color
        let coreColor: Color
        let deepColor: Color
        
        @Binding var showHighlightInfo: Bool
        @Binding var sleepStageLabel: String
        @Binding var sleepHighlightPeriod: String
        
        // 图表的时间范围 (默认从晚上8点到早上10点)
        let startHour: Int = 20
        let endHour: Int = 10
        let totalHours: Int = 14 // 10 + 24 - 20
        
        // 图表的尺寸参数
        let chartWidth: CGFloat = 265 // 从第一个竖线(x=65)到最后一个竖线(x=65+60*4)
        let rowHeight: CGFloat = 35
        let blockHeight: CGFloat = 26
        
        var body: some View {
            ZStack {
                // 绘制睡眠阶段块和连接线
                ForEach(processedSleepData.indices, id: \.self) { index in
                    let item = processedSleepData[index]
                    let nextItem = index < processedSleepData.count - 1 ? processedSleepData[index + 1] : nil
                    
                    // 绘制当前睡眠阶段色块
                    RoundedRectangle(cornerRadius: 10)
                        .fill(colorForType(item.type))
                        .frame(width: item.width, height: blockHeight)
                        .position(x: item.xPosition, y: yPositionForType(item.type))
                        .gesture(
                            // 只使用DragGesture，不再使用LongPressGesture
                            DragGesture(minimumDistance: 0)
                                .onChanged { _ in
                                    // 显示当前睡眠阶段信息
                                    sleepStageLabel = sleepStageNameForType(item.type)
                                    
                                    // 将毫秒时间戳转换为时间字符串
                                    let startDate = Date(timeIntervalSince1970: Double(sleepData[index].startTime) / 1000.0)
                                    let endDate = Date(timeIntervalSince1970: Double(sleepData[index].endTime) / 1000.0)
                                    
                                    let formatter = DateFormatter()
                                    formatter.dateFormat = "HH:mm"
                                    let startTimeStr = formatter.string(from: startDate)
                                    let endTimeStr = formatter.string(from: endDate)
                                    
                                    sleepHighlightPeriod = "\(startTimeStr)-\(endTimeStr)"
                                    showHighlightInfo = true
                                }
                                .onEnded { _ in
                                    // 只在松开时隐藏信息
                                    showHighlightInfo = false
                                }
                        )
                    
                    // 如果有下一个阶段，绘制连接线
                    if let next = nextItem {
                        Rectangle()
                            .fill(LinearGradient(
                                gradient: Gradient(colors: [
                                    colorForType(item.type),
                                    colorForType(next.type)
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            ))
                            .frame(width: 2, height: abs(yPositionForType(item.type) - yPositionForType(next.type)))
                            .position(
                                x: item.xPosition + item.width/2,
                                y: (yPositionForType(item.type) + yPositionForType(next.type)) / 2
                            )
                    }
                }
            }
        }
        
        // 处理睡眠数据为图表可用格式
        private var processedSleepData: [(type: Int, xPosition: CGFloat, width: CGFloat)] {
            // 对数据按开始时间排序
            let sortedData = sleepData.sorted { $0.startTime < $1.startTime }
            var result: [(type: Int, xPosition: CGFloat, width: CGFloat)] = []
            
            for item in sortedData {
                // 将毫秒时间戳转换为Date
                let startDate = Date(timeIntervalSince1970: Double(item.startTime) / 1000.0)
                let endDate = Date(timeIntervalSince1970: Double(item.endTime) / 1000.0)
                
                // 转换为本地时间
                let calendar = Calendar.current
                let startHourLocal = calendar.component(.hour, from: startDate)
                let startMinuteLocal = calendar.component(.minute, from: startDate)
                let endHourLocal = calendar.component(.hour, from: endDate)
                let endMinuteLocal = calendar.component(.minute, from: endDate)
                
                // 计算距离图表起点的小时数(考虑跨天情况)
                let startTotalHours = hoursSince8PM(hour: startHourLocal, minute: startMinuteLocal)
                let endTotalHours = hoursSince8PM(hour: endHourLocal, minute: endMinuteLocal)
                
                // 转换为图表中的x坐标和宽度
                let startX = 65 + (chartWidth * CGFloat(startTotalHours) / CGFloat(totalHours))
                let endX = 65 + (chartWidth * CGFloat(endTotalHours) / CGFloat(totalHours))
                let width = max(10, endX - startX) // 确保至少有10的宽度便于显示
                
                // 计算块的中心位置
                let centerX = startX + width/2
                
                result.append((type: item.type, xPosition: centerX, width: width))
            }
            
            return result
        }
        
        // 计算从晚上8点开始的小时数，处理跨天情况
        private func hoursSince8PM(hour: Int, minute: Int) -> Double {
            let hoursPart: Double
            if hour >= startHour {
                // 当天晚上时间段
                hoursPart = Double(hour - startHour)
            } else {
                // 第二天早上时间段
                hoursPart = Double(hour + 24 - startHour)
            }
            return hoursPart + Double(minute) / 60.0
        }
        
        // 根据睡眠类型返回对应的颜色 - 确保与传入的颜色一致
        private func colorForType(_ type: Int) -> Color {
            switch type {
            case 0: return awakeColor  // 清醒
            case 1: return coreColor   // 浅睡
            case 2: return deepColor   // 深睡
            case 3: return remColor    // REM
            default: return Color.gray
            }
        }
        
        // 根据睡眠类型计算y坐标位置
        private func yPositionForType(_ type: Int) -> CGFloat {
            switch type {
            case 0: return 17.5  // Awake行
            case 3: return 52.5  // REM行
            case 1: return 87.5  // Core行
            case 2: return 122.5 // Deep行
            default: return 0
            }
        }
        
        // 根据睡眠类型返回对应的显示名称
        private func sleepStageNameForType(_ type: Int) -> String {
            switch type {
            case 0: return "sleep_stage_awake".localized       // Awake
            case 1: return "sleep_stage_core".localized     // Core
            case 2: return "sleep_stage_deep".localized     // Deep
            case 3: return "sleep_stage_rem".localized        // REM
            default: return "sleep_stage_unknown".localized
            }
        }
    }
    
    // MARK: - 心率图表
    private var heartRateCard: some View {
        let chartData = sleepHeartRateViewModel.sleepHeartRateData

        // Calculate dynamic Y-axis upper bound
        let maxHeartDataValue = chartData.map { $0.hearts }.max() ?? 0
        let yDomainUpperBound: Double
        if maxHeartDataValue == 0 {
            yDomainUpperBound = 100.0 // Default if no data or max is 0
        } else {
            // 120% of max data value, but not less than 60
            yDomainUpperBound = max(Double(maxHeartDataValue) * 1.2, 60.0)
        }

        // Find max and min points from the filtered chartData
        let maxHeartRatePoint = chartData.max(by: { $0.hearts < $1.hearts })
        let minHeartRatePoint = chartData.min(by: { $0.hearts < $1.hearts })

        return VStack(alignment: .leading, spacing: 8) { // 减小整体间距
            // 标题和信息图标
            HStack {
                Text("sleep_hr_card_title".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.sleepHeartRate.rawValue)
                
                Spacer()
            }
            .padding(.bottom, 2) // 减小底部间距
            
            // 心率平均值数据框 - 更新样式匹配图片
            HStack(spacing: 16) {
                // 平均值数据框
                VStack(alignment: .center, spacing: 6) { // 减小内部间距
                    Text("sleep_hr_average".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.gray.opacity(0.7))
                    
                    if sleepHeartRateViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else if sleepHeartRateViewModel.errorMessage != nil {
                        Text("--")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    } else {
                        Text("\(sleepHeartRateViewModel.averageHeartRate)")
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.white)
                    }
                }
                .frame(height: 70) // 减小高度
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 0.08, green: 0.09, blue: 0.13), // #141821
                                    Color(red: 0.16, green: 0.19, blue: 0.25)  // #2A3040
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                )
                
                // 最近7天平均值数据框
                VStack(alignment: .center, spacing: 6) { // 减小内部间距
                    Text("sleep_hr_7day_average".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.gray.opacity(0.7))
                    
                    if sleepHeartRateViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else if sleepHeartRateViewModel.errorMessage != nil {
                        Text("--")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    } else {
                        Text("\(sleepHeartRateViewModel.recent7DayAverage)")
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.white)
                    }
                }
                .frame(height: 70) // 减小高度
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 0.08, green: 0.09, blue: 0.13), // #141821
                                    Color(red: 0.16, green: 0.19, blue: 0.25)  // #2A3040
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                )
            }
            .padding(.bottom, 10) // 减小底部间距
            
//            // 高亮显示
            HStack(spacing: 0) {
                Spacer()
                
            ZStack {
                    RoundedRectangle(cornerRadius: 5)
                        .fill(Color(red: 0.15, green: 0.17, blue: 0.22)) // #272C38
                        .frame(height: 20)
                    
                    HStack(spacing: 6) {
                        if sleepHeartRateViewModel.isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.7)
                        } else if sleepHeartRateViewModel.errorMessage != nil || sleepHeartRateViewModel.highlightPeriod.isEmpty {
                            Text("--")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.white)
                        } else {
                            Text(sleepHeartRateViewModel.highlightPeriod)
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)
                        }
                    }
                    .padding(.horizontal, 10)
                }
                .fixedSize(horizontal: true, vertical: false) // 宽度根据内容自适应
                
                Spacer()
            }
            .padding(.bottom, 6) // 减小底部间距
            
            // 心率图表
                    VStack(spacing: 0) {
                // 如果正在加载或出错，显示相应状态
                if sleepHeartRateViewModel.isLoading {
                        Spacer()
                    ProgressView("sleep_data_loading".localized)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .foregroundColor(.white)
                        .frame(height: 150) // 设置一个合适的高度
                        Spacer()
                } else if let errorMessage = sleepHeartRateViewModel.errorMessage {
                    Spacer()
                    VStack(spacing: 10) {
                        Image(systemName: "exclamationmark.triangle")
                            .foregroundColor(.orange)
                            .font(.system(size: 24))
                        Text(errorMessage)
                            .foregroundColor(.orange)
                            .multilineTextAlignment(.center)
                            .font(.system(size: 14))
                        Button("sleep_retry_button".localized) {
                            // 当前日期
                            let currentDate = sharedDateViewModel.selectedDate
                            sleepHeartRateViewModel.loadSleepHeartRateData(for: currentDate)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(Color.blue.opacity(0.7))
                        .foregroundColor(.white)
                        .cornerRadius(8)
                        }
                    .frame(height: 150) // 设置一个合适的高度
                    Spacer()
                } else if sleepHeartRateViewModel.sleepHeartRateData.isEmpty {
                    Spacer()
                    VStack(spacing: 10) {
                        Image(systemName: "moon.zzz")
                            .foregroundColor(.gray)
                            .font(.system(size: 24))
                        Text("sleep_no_hr_data".localized)
                            .foregroundColor(.gray)
                            .font(.system(size: 14))
                    }
                    .frame(height: 150) // 设置一个合适的高度
                    Spacer()
                } else {
                    // 新增基于SwiftUI的心率图表
                    Chart {
                        // Shaded background rectangle
                        RectangleMark(
                            yStart: .value("Range Min", 35),
                            yEnd: .value("Range Max", 55)
                        )
                        .foregroundStyle(Color.red.opacity(0.1))

                        // 绘制平均线 (Recent 7-night Average)
                        RuleMark(y: .value("7-Day Avg", sleepHeartRateViewModel.recent7DayAverage))
                            .foregroundStyle(Color.red.opacity(0.7))
                            .lineStyle(StrokeStyle(lineWidth: 1, dash: [4, 4]))
                            .annotation(position: .trailing, alignment: .centerLastTextBaseline) {
                                Text("\(sleepHeartRateViewModel.recent7DayAverage)")
                                    .font(.system(size: 10))
                                    .foregroundColor(Color.red.opacity(0.8))
                                    .padding(.leading, 2) // Keep label slightly off the edge
                            }

                        // Main Heart Rate Line
                        if !chartData.isEmpty {
                            ForEach(chartData) { point in
                                LineMark(
                                x: .value("时间", point.timeIndex),
                                    y: .value("心率", point.hearts)
                            )
                                .foregroundStyle(Color.red.opacity(0.9))
                                .lineStyle(StrokeStyle(lineWidth: 2))
                            }
//                            LineMark(
//                                x: .value("时间", timeValues),
//                                y: .value("心率", heartValues),
//                                series: .value("Series", "A")
//                            )
//                            .foregroundStyle(Color.red.opacity(0.9))
//                            .lineStyle(StrokeStyle(lineWidth: 2))
                        }

                        // Heart Rate Points
                        ForEach(chartData) { point in
                            let isMaxPoint = (point.id == maxHeartRatePoint?.id)
                            let isMinPoint = (point.id == minHeartRatePoint?.id)

                            if isMaxPoint || isMinPoint {
                                // Style for Max and Min points: Large white outer, red inner dot
                                PointMark(
                                x: .value("时间", point.timeIndex),
                                y: .value("心率", point.hearts)
                            )
                                .symbol(Circle())
                                .symbolSize(80) // Larger size for highlight (outer white part)
                                .foregroundStyle(Color.white)
                                PointMark(
                                    x: .value("时间", point.timeIndex),
                                    y: .value("心率", point.hearts)
                            )
                                .symbol(Circle())
                                .symbolSize(40) // Inner red dot
                                .foregroundStyle(Color.red.opacity(0.9))
                            } else {
                                // Standard style for other points: Small red dot
                            PointMark(
                                x: .value("时间", point.timeIndex),
                                y: .value("心率", point.hearts)
                            )
                                .symbol(Circle())
                                .symbolSize(15)
                                .foregroundStyle(Color.red.opacity(0.9))
                        }
                    }
                    }
                    .chartXScale(domain: 0...28) // User's current domain
                    .chartYScale(domain: 0...yDomainUpperBound) // Apply dynamic Y-axis upper bound
                    .frame(height: 170)
                    .chartXAxis {
                        AxisMarks(preset: .aligned, values: .stride(by: 1)) { value in
                                AxisValueLabel {
                                switch value.as(Int.self) {
                                    case 0:  Text("20:00")
                                    case 6:  Text("23:00")
                                    case 14: Text("03:00")
                                    case 20: Text("06:00")
                                    case 28: Text("10:00")
                                    default: Text("") // Should not happen
                                }
                            }
                                        .font(.system(size: 10))
                            .foregroundStyle(Color.gray.opacity(0.8))
                                }
                            }
                    .chartYAxis { // Keep Y-axis
                        AxisMarks(values: [10, 45, 90]) { value in
                            AxisGridLine().foregroundStyle(Color.gray.opacity(0.3))
                            AxisValueLabel()
                                .font(.system(size: 12))
                                .foregroundStyle(Color.gray.opacity(0.8))
                        }
                    }
                    .padding(.horizontal, 0)

                    
                }
            
                // 图例
            HStack(spacing: 16) {
                // Recent 7-night Average 图例
                HStack(spacing: 10) {
                    // 使用虚线代替实线矩形
                    Path { path in
                        path.move(to: CGPoint(x: 0, y: 0))
                        path.addLine(to: CGPoint(x: 30, y: 0))
                    }
                    .stroke(Color.red.opacity(0.7), style: StrokeStyle(lineWidth: 1.5, dash: [4, 4]))
                    .frame(width: 30, height: 1.5)
                    
                    Text("sleep_hr_legend_7day_average".localized)
                                    .font(.system(size: 12))
                        .foregroundColor(.gray.opacity(0.8))
                            }
                            
                        Spacer()
                            
                // Sleep Standard HR 图例
                HStack(spacing: 10) {
                    // 使用深红色实线/矩形代替紫色
                    Rectangle()
                        .fill(Color.red.opacity(0.6)) // Darker/Solid red rectangle
                        .frame(width: 30, height: 3) // Slightly thicker to appear as solid bar
                    
                    Text("sleep_hr_legend_standard".localized)
                                    .font(.system(size: 12))
                        .foregroundColor(.gray.opacity(0.8))
                }
            }
            .padding(.top, 8) // 减小顶部间距
            .padding(.horizontal, 10)
            .background(Color.clear)
            }
            .padding(.top, 10) // 上边距调整为10
            .padding([.leading, .trailing, .bottom], 14) // 左右底部边距保持14
            .background(Color(red: 0.06, green: 0.07, blue: 0.1))
            .cornerRadius(20)
            .padding(.top, 10) // 与上方的心率卡片保持10的间距
        }
        .padding(.top, 10) // 上边距调整为10
        .padding([.leading, .trailing, .bottom], 14) // 左右底部边距保持14
        .background(Color(red: 0.06, green: 0.07, blue: 0.1))
        .cornerRadius(20)
    }
    
    // MARK: - HRV图表
    private var hrvCard: some View {
        let hrvData = sleepHRVViewModel.sleepHRVData
        let maxHRVDataValue = hrvData.map { $0.hrv }.max() ?? 0 // Assuming SleepHRVPoint has an 'hrv' property
        var hrvYDomain: Double = 120.0
        if maxHRVDataValue > 0 {
            // 120% of max data value, but not less than 60 (adjust min as needed for HRV)
            hrvYDomain = max(Double(maxHRVDataValue) * 1.2, 60.0)
//            hrvYDomain = 120.0 // Default if no data or max is 0
        }
        return VStack(alignment: .leading, spacing: 8) { // 减小整体间距
            // Calculate dynamic Y-axis upper bound for HRV
            // 标题和信息图标
            HStack {
                Text("sleep_hrv_card_title".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.sleepHRV.rawValue)
                
                Spacer()
            }
            .padding(.bottom, 2) // 减小底部间距
            
            // HRV平均值数据框
            HStack(spacing: 16) {
                // 平均值数据框
                VStack(alignment: .center, spacing: 6) { // 减小内部间距
                    Text("sleep_hr_average".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.gray.opacity(0.7))
                    
                    if sleepHRVViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else if sleepHRVViewModel.errorMessage != nil {
                        Text("--")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    } else {
                        Text("\(sleepHRVViewModel.averageHRV)")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(sleepHRVViewModel.healthStatusColor)
                    }
                }
                .frame(height: 70) // 减小高度
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 0.08, green: 0.09, blue: 0.13), // #141821
                                    Color(red: 0.16, green: 0.19, blue: 0.25)  // #2A3040
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                )
                
                // 最近7天平均值数据框
                VStack(alignment: .center, spacing: 6) { // 减小内部间距
                    Text("sleep_hr_7day_average".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.gray.opacity(0.7))
                    
                    if sleepHRVViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else if sleepHRVViewModel.errorMessage != nil {
                        Text("--")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    } else {
                        Text("\(sleepHRVViewModel.recent7DayAverage)")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }
                .frame(height: 70) // 减小高度
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 0.08, green: 0.09, blue: 0.13), // #141821
                                    Color(red: 0.16, green: 0.19, blue: 0.25)  // #2A3040
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                )
            }
            .padding(.bottom, 10) // 减小底部间距
            
            // 健康状态和高亮区间
            HStack(spacing: 0) {
                Spacer()
                
                ZStack {
                    RoundedRectangle(cornerRadius: 5)
                        .fill(Color(red: 0.15, green: 0.17, blue: 0.22)) // #272C38
                        .frame(height: 20)
                    
                    HStack(spacing: 6) {
                        if sleepHRVViewModel.isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.7)
                        } else if sleepHRVViewModel.errorMessage != nil || sleepHRVViewModel.highlightPeriod.isEmpty {
                            Text("--")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)
                        } else {
                            Text(sleepHRVViewModel.highlightPeriod)
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)
                        }
                    }
                    .padding(.horizontal, 10)
                }
                .fixedSize(horizontal: true, vertical: false) // 宽度根据内容自适应
                            
                            Spacer()
            }
            .padding(.bottom, 6) // 减小底部间距
            
            // HRV图表
            VStack(spacing: 0) {
                // 如果正在加载或出错，显示相应状态
                if sleepHRVViewModel.isLoading {
                            Spacer()
                    ProgressView("sleep_data_loading".localized)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .foregroundColor(.white)
                        .frame(height: 150) // 设置一个合适的高度
                    Spacer()
                } else if let errorMessage = sleepHRVViewModel.errorMessage {
                    Spacer()
                    VStack(spacing: 10) {
                        Image(systemName: "exclamationmark.triangle")
                            .foregroundColor(.orange)
                            .font(.system(size: 24))
                        Text(errorMessage)
                            .foregroundColor(.orange)
                            .multilineTextAlignment(.center)
                            .font(.system(size: 14))
                        Button("sleep_retry_button".localized) {
                            // 当前日期
                            let currentDate = sharedDateViewModel.selectedDate
                            sleepHRVViewModel.loadSleepHRVData(for: currentDate)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(Color.blue.opacity(0.7))
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    .frame(height: 150) // 设置一个合适的高度
                    Spacer()
                } else if sleepHRVViewModel.sleepHRVData.isEmpty {
                    Spacer()
                    VStack(spacing: 10) {
                        Image(systemName: "moon.zzz")
                            .foregroundColor(.gray)
                            .font(.system(size: 24))
                        Text("sleep_no_hrv_data".localized)
                            .foregroundColor(.gray)
                            .font(.system(size: 14))
                    }
                    .frame(height: 150) // 设置一个合适的高度
                    Spacer()
                } else {
                    // 基于SwiftUI的HRV图表
                    Chart {
                        // 绘制平均线
                        RuleMark(y: .value("7-Day Avg", sleepHRVViewModel.recent7DayAverage))
                            .foregroundStyle(.red.opacity(0.7))
                            .lineStyle(StrokeStyle(lineWidth: 1, dash: [4, 4]))
                            .annotation(position: .trailing) {
                                Text("\(sleepHRVViewModel.recent7DayAverage)")
                                    .font(.system(size: 10))
                                    .foregroundColor(.red.opacity(0.8))
                            }
                        
                        // 绘制HRV曲线 - 使用AreaMark添加底部渐变填充
                        ForEach(0..<sleepHRVViewModel.sleepHRVData.count-1, id: \.self) { index in
                            let point = sleepHRVViewModel.sleepHRVData[index]
                            let nextPoint = sleepHRVViewModel.sleepHRVData[index+1]
                            
                            // 填充区域，添加渐变效果
                            AreaMark(
                                x: .value("时间", point.timeIndex),
                                y: .value("HRV", point.hrv),
                                series: .value("系列", "HRV")
                            )
                            .foregroundStyle(
                                .linearGradient(
                                    colors: [.blue.opacity(0.3), .clear],
                                    startPoint: .top,
                                    endPoint: .bottom
                                )
                            )
                            
                            AreaMark(
                                x: .value("时间", nextPoint.timeIndex),
                                y: .value("HRV", nextPoint.hrv),
                                series: .value("系列", "HRV")
                            )
                            .foregroundStyle(
                                .linearGradient(
                                    colors: [.blue.opacity(0.3), .clear],
                                    startPoint: .top,
                                    endPoint: .bottom
                                )
                            )
                            
                            // 连接线
                            LineMark(
                                x: .value("时间", point.timeIndex),
                                y: .value("HRV", point.hrv)
                            )
                            .foregroundStyle(
                                point.isHighlightPoint ? Color.green.opacity(0.9) : Color.blue.opacity(0.8)
                            )
                            .lineStyle(StrokeStyle(lineWidth: 2, lineCap: .round, lineJoin: .round))
                            
                            LineMark(
                                x: .value("时间", nextPoint.timeIndex),
                                y: .value("HRV", nextPoint.hrv)
                            )
                            .foregroundStyle(
                                nextPoint.isHighlightPoint ? Color.green.opacity(0.9) : Color.blue.opacity(0.8)
                            )
                            .lineStyle(StrokeStyle(lineWidth: 2, lineCap: .round, lineJoin: .round))
                        }
                        
                        // 单独绘制数据点，确保它们在线的上面
                        ForEach(sleepHRVViewModel.sleepHRVData.indices, id: \.self) { index in
                            let point = sleepHRVViewModel.sleepHRVData[index]
                            // 绘制HRV点
                            PointMark(
                                x: .value("时间", point.timeIndex),
                                y: .value("HRV", point.hrv)
                            )
                            .foregroundStyle(point.isHighlightPoint ? .green : .blue)
                            .symbolSize(point.isHighlightPoint ? 50 : 20)
                        }
                    }
                    .chartXScale(domain: 0...28) // 24小时 * 2 (半小时为单位)
                    .chartYScale(domain: 0...hrvYDomain) // Apply dynamic Y-axis upper bound for HRV
                    .frame(height: 170)
                    .chartXAxis {
                        AxisMarks(preset: .aligned, values: .stride(by: 1)) { value in
                                AxisValueLabel {
                                switch value.as(Int.self) {
                                    case 0:  Text("20:00")
                                    case 6:  Text("23:00")
                                    case 14: Text("03:00")
                                    case 20: Text("06:00")
                                    case 28: Text("10:00")
                                    default: Text("") // Should not happen
                                }
                            }
                                        .font(.system(size: 10))
                            .foregroundStyle(Color.gray.opacity(0.8))
                        }
                    }
                    .chartYAxis {
                        AxisMarks(preset: .aligned, values: .stride(by: 20)) { value in
                            AxisValueLabel {
                                if let hrv = value.as(Int.self) {
                                    Text("\(hrv)")
                                        .font(.system(size: 10))
                                        .foregroundColor(.gray.opacity(0.8))
                                }
                            }
                            AxisGridLine(centered: true, stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 2]))
                            AxisTick(centered: true, length: 4, stroke: StrokeStyle(lineWidth: 0.5))
                        }
                    }
                    .padding(.horizontal, 10)
                    .overlay(
                        // 添加指示最高HRV的垂直线
                        GeometryReader { geometry in
                            if let highestPoint = sleepHRVViewModel.sleepHRVData.first(where: { $0.isHighlightPoint }) {
                                let xPosition = CGFloat(highestPoint.timeIndex) / 47.0 * geometry.size.width
                                Rectangle()
                                    .fill(Color.green.opacity(0.2))
                                    .frame(width: 20, height: geometry.size.height)
                                    .position(x: xPosition, y: geometry.size.height / 2)
                            }
                        }
                    )
                }
                
                // 图例
            HStack(spacing: 16) {
                // Recent 7-night Average 图例
                HStack(spacing: 10) {
                    // 使用虚线代替实线矩形
                    Path { path in
                        path.move(to: CGPoint(x: 0, y: 0))
                        path.addLine(to: CGPoint(x: 30, y: 0))
                    }
                    .stroke(Color.red.opacity(0.7), style: StrokeStyle(lineWidth: 1.5, dash: [4, 4]))
                    .frame(width: 30, height: 1)
                    
                    Text("sleep_hr_legend_7day_average".localized)
                            .font(.system(size: 12))
                        .foregroundColor(.gray.opacity(0.8))
                }
            
            Spacer()
            
                    // Sleep HRV 图例
                HStack(spacing: 10) {
                        // 使用深蓝色实线
                        Path { path in
                            path.move(to: CGPoint(x: 0, y: 0))
                            path.addLine(to: CGPoint(x: 30, y: 0))
                        }
                        .stroke(Color.blue.opacity(0.8), style: StrokeStyle(lineWidth: 2, lineCap: .round))
                        .frame(width: 30, height: 2)
                    
                        Text("sleep_hrv_info_title".localized)
                            .font(.system(size: 12))
                        .foregroundColor(.gray.opacity(0.8))
                }
            }
            .padding(.top, 8) // 减小顶部间距
            .padding(.horizontal, 10)
            .background(Color.clear)
            }
            .padding(.top, 10) // 上边距调整为10
            .padding([.leading, .trailing, .bottom], 14) // 左右底部边距保持14
            .background(Color(red: 0.06, green: 0.07, blue: 0.1))
            .cornerRadius(20)
            .padding(.top, 10) // 与上方的组件保持10的间距
        }
        .padding(.top, 10) // 上边距调整为10
        .padding([.leading, .trailing, .bottom], 14) // 左右底部边距保持14
        .background(Color(red: 0.06, green: 0.07, blue: 0.1))
        .cornerRadius(20)
    }
    
    // MARK: - SpO2 Card
    private var sleepSpO2Card: some View {
        let spo2ChartData = sleepSpO2ViewModel.sleepSpO2Data
        let yDomainLowerBound: Double
        let yDomainUpperBound: Double

        if spo2ChartData.isEmpty && !sleepSpO2ViewModel.isLoading {
            yDomainLowerBound = 88.0
            yDomainUpperBound = 102.0
        } else {
            let minSpo2 = spo2ChartData.map { $0.spo2 }.min() ?? 95
            let maxSpo2 = spo2ChartData.map { $0.spo2 }.max() ?? 100
            
            var proposedLower = max(80.0, Double(minSpo2) - 2)
            var proposedUpper = min(105.0, Double(maxSpo2) + 2) // Allow a bit above 100

            if proposedUpper <= proposedLower { // Ensure upper is greater than lower
                 proposedLower = 88.0
                 proposedUpper = 102.0
            }
            // Ensure a minimum span for the Y-axis
            if (proposedUpper - proposedLower) < 5.0 { // e.g. min 5 points range
                let mid = (proposedLower + proposedUpper) / 2.0
                proposedLower = mid - 2.5
                proposedUpper = mid + 2.5
            }
            yDomainLowerBound = proposedLower
            yDomainUpperBound = proposedUpper
        }

        return VStack(alignment: .leading, spacing: 8) {
            // Title
            HStack {
                Text("sleep_spo2_card_title".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
//                Image(systemName: "info.circle")
//                    .foregroundColor(.gray.opacity(0.7))
//                    .font(.system(size: 14))
//                    .onTapGesture {
//                        infoTitle = "sleep_spo2_info_title".localized
//                        infoContent = "sleep_spo2_info_content".localized
//                        showInfoSheet = true
//                    }
                Spacer()
            }
            .padding(.bottom, 2)

            // Average Boxes
            HStack(spacing: 16) {
                // Average SpO2
                VStack(alignment: .center, spacing: 6) {
                    Text("sleep_hr_average".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.gray.opacity(0.7))
                    if sleepSpO2ViewModel.isLoading {
                        ProgressView().scaleEffect(0.8).progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else if sleepSpO2ViewModel.errorMessage != nil && sleepSpO2ViewModel.averageSpO2 == 0 {
                        Text("--").font(.system(size: 24, weight: .semibold)).foregroundColor(.white)
                    } else {
                        Text("\(sleepSpO2ViewModel.averageSpO2)%")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }
                .frame(height: 70).frame(maxWidth: .infinity)
                .background(RoundedRectangle(cornerRadius: 12).fill(LinearGradient(gradient: Gradient(colors: [colorFromHex("141821"), colorFromHex("2A3040")]), startPoint: .leading, endPoint: .trailing)))

                // Recent 7-night Average SpO2
                VStack(alignment: .center, spacing: 6) {
                    Text("sleep_hr_7day_average".localized)
                        .font(.system(size: 14))
                        .foregroundColor(.gray.opacity(0.7))
                    if sleepSpO2ViewModel.isLoading {
                        ProgressView().scaleEffect(0.8).progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else if sleepSpO2ViewModel.errorMessage != nil && sleepSpO2ViewModel.recent7DayAverageSpO2 == 0 {
                        Text("--").font(.system(size: 24, weight: .semibold)).foregroundColor(.white)
                    } else {
                        Text("\(sleepSpO2ViewModel.recent7DayAverageSpO2)%")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }
                .frame(height: 70).frame(maxWidth: .infinity)
                .background(RoundedRectangle(cornerRadius: 12).fill(LinearGradient(gradient: Gradient(colors: [colorFromHex("141821"), colorFromHex("2A3040")]), startPoint: .leading, endPoint: .trailing)))
            }
            .padding(.bottom, 10)

            // Highlight Period
            HStack(spacing: 0) {
                Spacer()
                if sleepSpO2ViewModel.isLoading {
                    ProgressView().scaleEffect(0.7).progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else if sleepSpO2ViewModel.errorMessage != nil && sleepSpO2ViewModel.highlightPeriod.isEmpty {
                     Text("--").font(.system(size: 14, weight: .semibold)).foregroundColor(.white)
                } else if !sleepSpO2ViewModel.highlightPeriod.isEmpty {
                ZStack {
                        RoundedRectangle(cornerRadius: 5)
                            .fill(colorFromHex("272C38"))
                            .frame(height: 20)
                        Text(sleepSpO2ViewModel.highlightPeriod)
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 10)
                    }
                    .fixedSize(horizontal: true, vertical: false)
                } else {
                     // Empty placeholder to maintain layout if no highlight and no error
                     Text("").frame(height: 20)
                }
                Spacer()
            }
            .frame(height: 20) // Consistent height for this row
            .padding(.bottom, 6)

            // SpO2 Chart Area
            VStack(spacing: 0) {
                if sleepSpO2ViewModel.isLoading {
                    ProgressView("sleep_spo2_loading".localized)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .foregroundColor(.white)
                        .frame(height: 170)
                } else if let errorMessage = sleepSpO2ViewModel.errorMessage, !errorMessage.isEmpty && sleepSpO2ViewModel.sleepSpO2Data.isEmpty {
                    VStack(spacing: 10) {
                        Image(systemName: "exclamationmark.triangle.fill").foregroundColor(.orange).font(.title3)
                        Text(errorMessage)
                            .font(.caption)
                            .foregroundColor(.orange)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        Button("sleep_retry_button".localized) {
                            sleepSpO2ViewModel.loadSleepSpO2Data(for: sharedDateViewModel.selectedDate)
                        }
                        .padding(.vertical, 5).padding(.horizontal, 10)
                        .background(Color.blue.opacity(0.7)).foregroundColor(.white).cornerRadius(5)
                    }
                    .frame(height: 170)
                } else if sleepSpO2ViewModel.sleepSpO2Data.isEmpty {
                    VStack(spacing: 10) {
                         Image(systemName: "moon.zzz.fill").foregroundColor(.gray).font(.title3)
                         Text("sleep_no_spo2_data".localized)
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    .frame(height: 170)
                } else {
                    Chart {
                        RectangleMark( // Optimal range background
                            yStart: .value("Optimal Min", 90),
                            yEnd: .value("Optimal Max", 100)
                        )
                        .foregroundStyle(Color.green.opacity(0.05))

                        if sleepSpO2ViewModel.recent7DayAverageSpO2 > 0 { // 7-day average line
                             RuleMark(y: .value("7-Day Avg", sleepSpO2ViewModel.recent7DayAverageSpO2))
                                .foregroundStyle(Color.orange.opacity(0.7))
                                .lineStyle(StrokeStyle(lineWidth: 1, dash: [4, 4]))
                                .annotation(position: .trailing, alignment: .leadingLastTextBaseline) {
                                    Text("\(sleepSpO2ViewModel.recent7DayAverageSpO2)")
                                        .font(.system(size: 10))
                                        .foregroundColor(Color.orange.opacity(0.8))
                                }
                        }

                        ForEach(sleepSpO2ViewModel.sleepSpO2Data) { point in // SpO2 data line
                            LineMark(
                                x: .value("Time", point.timeIndex),
                                y: .value("SpO2", point.spo2)
                            )
                            .foregroundStyle(colorFromHex("0A84FF")) // System Blue like color for SpO2

                            if point.isHighlightPoint { // Highlight specific points
                                PointMark(
                                    x: .value("Time", point.timeIndex),
                                    y: .value("SpO2", point.spo2)
                                )
                                .symbol(Circle().strokeBorder(lineWidth: 1.5))
                                .symbolSize(60) // Make highlight points noticeable
                                .foregroundStyle(Color.yellow)
                            }
                        }
                    }
                    .chartXScale(domain: 0...28) // Consistent X-axis with other charts
                    .chartYScale(domain: yDomainLowerBound...yDomainUpperBound)
                    .chartXAxis {
                        AxisMarks(preset: .aligned, values: .stride(by: 1)) { value in
                            AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2,2])).foregroundStyle(Color.gray.opacity(0.2))
                            AxisValueLabel {
                                switch value.as(Int.self) {
                                    case 0:  Text("20:00")
                                    case 6:  Text("23:00")
                                    case 14: Text("03:00")
                                    case 20: Text("06:00")
                                    case 28: Text("10:00")
                                    default: Text("")
                                }
                            }
                            .font(.system(size: 10)).foregroundStyle(Color.gray.opacity(0.8))
                        }
                    }
                    .chartYAxis { // SpO2 percentage labels
                        AxisMarks(preset: .automatic ) { value in //.automatic(desiredTickCount: 4)
                            AxisGridLine().foregroundStyle(Color.gray.opacity(0.3))
                            AxisValueLabel {
                                if let intValue = value.as(Int.self) {
                                    Text("\(intValue)%")
                                       .font(.system(size: 10))
                                       .foregroundStyle(Color.gray.opacity(0.8))
                                }
                            }
                        }
                    }
                    .frame(height: 170)
                }
            }
            // Legend for SpO2 Chart
            HStack(spacing: 16) {
                HStack(spacing: 6) { // SpO2 line legend
                    Rectangle().fill(colorFromHex("0A84FF")).frame(width: 15, height: 3)
                    Text("sleep_spo2_legend_spo2".localized).font(.system(size: 12)).foregroundColor(.gray.opacity(0.8))
                }
                if sleepSpO2ViewModel.recent7DayAverageSpO2 > 0 { // 7-day average legend
                    HStack(spacing: 6) {
                        LineShape(lineWidth: 1, dashPattern: [4,4]).stroke(Color.orange.opacity(0.7)).frame(width: 15, height: 1.5) // Use 1.5 for line height
                        Text("sleep_spo2_legend_7day_avg".localized).font(.system(size: 12)).foregroundColor(.gray.opacity(0.8))
                    }
                }
                Spacer()
                 HStack(spacing: 6) { // Optimal range legend
                    Rectangle().fill(Color.green.opacity(0.15)).frame(width: 15, height: 10) // Area color
                    Text("sleep_spo2_legend_optimal".localized).font(.system(size: 12)).foregroundColor(.gray.opacity(0.8))
                }
            }
            .padding(.top, 8)
            .padding(.horizontal, 10)
            // 健康提示 - 使用圆角矩形容器
            HStack {
                Image(systemName: "bell.fill")
                    .foregroundColor(Color(hex: "00BCD4"))
                    .font(.system(size: 16))
                
                Text("sleep_spo2_tip".localized)
                    .font(.system(size: 14))
                    .foregroundColor(Color(hex: "00BCD4"))
                
                Spacer()
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 12)
            .background(Color.black.opacity(0.3))
            .cornerRadius(20)
        }
        .padding(.top, 10)
        .padding([.leading, .trailing, .bottom], 14)
        .background(colorFromHex("0F1219")) // Standard card background color
        .cornerRadius(20)
    }

    // Helper struct for dashed line in legend (if not defined elsewhere)
    struct LineShape: Shape {
        var lineWidth: CGFloat
        var dashPattern: [CGFloat]
        func path(in rect: CGRect) -> Path {
            var path = Path()
            path.move(to: CGPoint(x: rect.minX, y: rect.midY))
            path.addLine(to: CGPoint(x: rect.maxX, y: rect.midY))
            // Use .strokedPath for shapes that should be stroked
            return path //.strokedPath(.init(lineWidth: lineWidth, dash: dashPattern)) -> This is for filling, stroke is applied on view
        }
    }
    
    // MARK: - 体温卡片
    private var temperatureCard: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("sleep_temp_card_title".localized)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .padding(.bottom, 5) // 增加底部间距
            
            // 体温数据盒
            HStack(spacing: 10) {
                temperatureBox(
                    title: "sleep_hr_average".localized,
                    value: String(format: "%.1f", temperatureData?.average ?? 0.0),
                    showValue: temperatureData != nil,
                    gradientTopColorHex: "19647D",
                    gradientBottomColorHex: "0D3644",
                    iconColorHex: "00BCD4"
                )
                temperatureBox(
                    title: "sleep_temp_baseline".localized,
                    value: String(format: "%.1f", temperatureData?.baseline ?? 0.0),
                    showValue: temperatureData != nil,
                    gradientTopColorHex: "2C4C73",
                    gradientBottomColorHex: "17283D",
                    iconColorHex: "4095DF"
                )
                temperatureBox(
                    title: "sleep_temp_differ".localized,
                    value: String(format: "%.1f", temperatureData?.differ ?? 0.0),
                    showValue: temperatureData != nil,
                    gradientTopColorHex: "49466B",
                    gradientBottomColorHex: "2D2B42",
                    iconColorHex: "B06FEF"
                )
            }
            .padding(.bottom, 12) // 增加底部间距
            
            // 健康提示 - 使用圆角矩形容器
            healthTipView(
                text: "sleep_temp_tip".localized,
                iconName: "bell.fill",
                color: colorFromHex("00BCD4")
            )
        }
        .padding(.vertical, 18) // 增加垂直内边距
        .padding(.horizontal, 16) // 确保水平内边距一致
        .background(Color(red: 0.06, green: 0.07, blue: 0.1))
        .cornerRadius(25)
    }
    
    // 体温数据盒子
    private func temperatureBox(title: String, value: String, showValue: Bool, gradientTopColorHex: String, gradientBottomColorHex: String, iconColorHex: String) -> some View {
        VStack(spacing: 2) { // 进一步减小垂直间距
            // 温度计图标
            ZStack {
                Circle()
                    .fill(Color.black.opacity(0.3))
                    .frame(width: 36, height: 36) // 圆形背景大小减小
                
                Image(systemName: "thermometer")
                    .font(.system(size: 16)) // 图标大小减小
                    .foregroundColor(colorFromHex(iconColorHex)) // Corrected to use colorFromHex
            }
            .padding(.top, 4) // 顶部内边距减小
            
            // 标题
            Text(title)
                .font(.system(size: 13)) // 稍微减小标题大小
                .foregroundColor(.white.opacity(0.8))
                .padding(.bottom, 1) // 标题底部间距减小
            
            // 温度值
            Text(showValue ? value : "--")
                .font(.system(size: 23, weight: .bold)) // 减小数字大小
                .foregroundColor(.white)
                .padding(.bottom, 4) // 底部间距减小
        }
        .frame(maxWidth: .infinity)
        .frame(height: 75) // 保持高度为75
        .background(
            LinearGradient(
                gradient: Gradient(colors: [colorFromHex(gradientTopColorHex), colorFromHex(gradientBottomColorHex)]), // Corrected to use colorFromHex
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .cornerRadius(10) // 圆角保持为10
    }
    
    // 健康提示视图
    private func healthTipView(text: String, iconName: String, color: Color) -> some View {
        HStack {
            Image(systemName: iconName)
                .foregroundColor(color)
                .font(.system(size: 16))
            
            Text(text)
                .font(.system(size: 14))
                .foregroundColor(color)
            
            Spacer()
        }
        .padding(.vertical, 10)
        .padding(.horizontal, 12)
        .background(Color.black.opacity(0.3))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(color, lineWidth: 1)
        )
    }
    
    // MARK: - 数据加载方法
    private func loadSleepData(for date: Date) {
        // 加载睡眠评分数据
        fetchSleepScore(for: date)
            
        // 加载睡眠详情数据
        fetchSleepDetails(for: date)
            
        // 加载睡眠心率数据
        sleepHeartRateViewModel.loadSleepHeartRateData(for: date)
        
        // 加载睡眠HRV数据
        sleepHRVViewModel.loadSleepHRVData(for: date)

        // 加载睡眠SpO2数据
        sleepSpO2ViewModel.loadSleepSpO2Data(for: date)
        
        // 加载体温数据
        fetchTemperatureData(for: date)
    }
    
    // 获取睡眠评分数据
    // 注意：此处实现的API调用与MainInsightView中类似，但是独立实现以避免相互依赖
    // 两处的API调用都使用相同的SleepScoreResponse和SleepScoreData模型
    private func fetchSleepScore(for date: Date) {
        isLoadingSleepScore = true
        sleepScoreError = nil
        let apiDateFormatter = DateFormatter()
        apiDateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = apiDateFormatter.string(from: date)
        let endpoint = "/app-api/iot/sleep/getScore?date=\(dateString)"
        NetworkManager.shared.request(
            endpoint: endpoint,
            method: .get,
            responseType: SleepScoreData.self
        )
        .sink(receiveCompletion: { completion in
                self.isLoadingSleepScore = false
            if case let .failure(error) = completion {
                self.handleSleepScoreFetchError(error.localizedDescription)
            }
        }, receiveValue: { scoreData in
            
                self.sleepScoreData = scoreData
                        print("SleepDetailView - 获取睡眠评分数据成功: \(String(describing: self.sleepScoreData))")
                        
                        // 更新UI数据
//                if let scoreData = response.data {
                            // 更新睡眠评分
                            self.sleepScore = scoreData.score
                            
                            // 设置睡眠评分级别
                            self.sleepScoreLevel = scoreData.scoreName ?? self.getSleepScoreLevelFromScore(scoreData.score)
                            
                            // 更新睡眠时间 - 始终使用deep + light + rem的总和作为实际的总睡眠时长
                            let calculatedTotal = scoreData.deep + scoreData.light + scoreData.rem
                            let hours = calculatedTotal / 60
                            let minutes = calculatedTotal % 60
                            self.sleepTotalTime = scoreData.totalSleep?.formatMinutesToHoursAndMinutes() ?? "--"
                            
                            self.sleepAsleepTime = "\(hours) hr \(minutes) min"
                            // 更新睡眠效率
                            self.sleepEfficiency = scoreData.efficiency ?? 0
            
                            // 更新睡眠阶段百分比
                    // 更新睡眠阶段时间 - 直接使用API返回的原始分钟数据
                    self.deepTime = self.formatMinutes(scoreData.deep)
                    self.remTime = self.formatMinutes(scoreData.rem)
                    self.coreTime = self.formatMinutes(scoreData.light)
                    
                    // 设置清醒时间显示
//                    self.awakeTime = self.formatMinutes(scoreData.awake ?? 0)
//                    self.sleepAsleepTime = self.formatMinutes(scoreData.awake)
                    
                    // 打印睡眠阶段时间数据用于调试
//                    print("睡眠阶段时间: DEEP=\(scoreData.deep)分钟, Core=\(scoreData.light)分钟, REM=\(scoreData.rem)分钟, AWAKE=\(scoreData.awake)分钟")
                    print("睡眠阶段百分比: DEEP=\(Int(self.deepPercentage))%, Core=\(Int(self.corePercentage))%, REM=\(Int(self.remPercentage))%, AWAKE=\(Int(self.awakePercentage))%")
//                }
                
                // 如果数据为空，设置错误信息
//                if response.data == nil {
//                    self.handleSleepScoreFetchError("该日期没有睡眠数据")
//                }
//            } else {
//                // API返回错误码时，显示为--
//                self.sleepScore = 0
//                self.sleepScoreLevel = "--"
//                self.sleepTotalTime = "--"
//                self.sleepAsleepTime = "--"
//                self.sleepEfficiency = 0
//
//                // 睡眠阶段时间设置为--
//                self.awakeTime = "--"
//                self.remTime = "--"
//                self.coreTime = "--"
//                self.deepTime = "--"
//
//                // 睡眠阶段百分比重置
//                self.awakePercentage = 0
//                self.remPercentage = 0
//                self.corePercentage = 0
//                self.deepPercentage = 0
//
//                self.handleSleepScoreFetchError("API错误: \(response.msg)")
//            }
        })
        .store(in: &cancellables)
    }
    
    // 处理睡眠数据获取错误的辅助方法
    private func handleSleepScoreFetchError(_ message: String) {
        isLoadingSleepScore = false
        sleepScoreError = message
        sleepScoreData = nil
        
        // 设置默认的"--"显示
        if !message.contains("解析错误") && !message.contains("网络错误") {
            // 如果是数据不存在的情况（而非网络或解析错误），设置为"--"
            // 圆环评分设为0，不显示进度
            sleepScore = 0
            sleepScoreLevel = "--"
            sleepTotalTime = "--"
            sleepAsleepTime = "--"
            sleepEfficiency = 0
            
            // 睡眠阶段数据重置
            awakePercentage = 0
            remPercentage = 0
            corePercentage = 0
            deepPercentage = 0
            
            // 睡眠阶段时间设置为--
            awakeTime = "--"
            remTime = "--"
            coreTime = "--"
            deepTime = "--"
        }
        
        print("睡眠评分错误: \(message)")
    }
    
    // 从评分获取等级描述
    private func getSleepScoreLevelFromScore(_ score: Int) -> String {
        if score == 0 {
            return "--" // 对于0分，返回--
        } else if score >= 90 {
            return "sleep_score_excellent".localized
        } else if score >= 80 {
            return "sleep_score_very_good".localized
        } else if score >= 70 {
            return "sleep_score_good".localized
        } else if score >= 60 {
            return "sleep_score_fair".localized
        } else {
            return "sleep_score_poor".localized
        }
    }
    
    // 生成示例数据
    private func generateSampleData() {
        // 睡眠阶段图表数据
        sleepStagesData = [
            SleepStage(time: "20:00", stage: .awake),
            SleepStage(time: "21:00", stage: .awake),
            SleepStage(time: "22:00", stage: .rem),
            SleepStage(time: "23:00", stage: .core),
            SleepStage(time: "00:00", stage: .deep),
            SleepStage(time: "01:00", stage: .core),
            SleepStage(time: "02:00", stage: .rem),
            SleepStage(time: "03:00", stage: .deep),
            SleepStage(time: "04:00", stage: .core),
            SleepStage(time: "05:00", stage: .rem),
            SleepStage(time: "06:00", stage: .awake),
            SleepStage(time: "07:00", stage: .awake)
        ]
        
        // 心率图表数据
//        heartRateData = (0..<24).map { hour in
//            let baseValue = 65.0
//            let time = String(format: "%02d:00", hour)
//            // 生成波动的心率数据，夜间较低，白天较高
//            let value = baseValue +
//                       (hour >= 6 && hour <= 18 ? 20 : 5) +
//                       Double.random(in: -5...10)
//            return HeartRatePoint(time: time, value: Int(value))
//        }
        
        // 血氧图表数据
        spo2Data = (0..<24).map { hour in
            let baseValue = 96.0
            let time = String(format: "%02d:00", hour)
            // 血氧数据一般在95-100之间波动
            let value = baseValue + Double.random(in: -1...3)
            return SPO2Point(time: time, value: value)
        }
    }
    
    // 更新睡眠阶段数据
    private func updateSleepStagesData(seed: Int) {
        let stages: [SleepStageType] = [.awake, .rem, .core, .deep]
        var updatedData: [SleepStage] = []
        
        for i in 0..<12 {
            let hour = i + 20
            let time = String(format: "%02d:00", hour % 24)
            let stageIndex = (i + seed) % stages.count
            updatedData.append(SleepStage(time: time, stage: stages[stageIndex]))
        }
        
        sleepStagesData = updatedData
    }
    
    // 更新心率数据
//    private func updateHeartRateData(seed: Int) {
//        var updatedData: [HeartRatePoint] = []
//
//        for hour in 0..<24 {
//            let baseValue = 65.0 + Double(seed)
//            let time = String(format: "%02d:00", hour)
//            // 生成波动的心率数据，夜间较低，白天较高
//            let value = baseValue +
//                       (hour >= 6 && hour <= 18 ? 20 : 5) +
//                       Double.random(in: -5...10)
//            updatedData.append(HeartRatePoint(time: time, value: Int(value)))
//        }
//
//        heartRateData = updatedData
//        heartRateHighlight = "\(heartRateAvg)bpm 12:30-4:00"
//    }
    
    // 更新血氧数据
    private func updateSPO2Data(seed: Int) {
        var updatedData: [SPO2Point] = []
        
        for hour in 0..<24 {
            let baseValue = 96.0 - Double(seed % 3)
            let time = String(format: "%02d:00", hour)
            // 血氧数据一般在95-100之间波动
            let value = min(99.0, baseValue + Double.random(in: -1...3))
            updatedData.append(SPO2Point(time: time, value: value))
        }
        
        spo2Data = updatedData
        spo2Highlight = "\(spo2Avg)% 12:30-4:00"
    }
    
    // 分钟格式化为小时分钟字符串
    private func formatMinutes(_ minutes: Int) -> String {
        let hours = minutes / 60
        let mins = minutes % 60
        return "\(hours) hr \(mins) min"
    }

    // 获取睡眠详情数据
    private func fetchSleepDetails(for date: Date) {
        isLoadingSleepDetails = true
        sleepDetailsError = nil
        let apiDateFormatter = DateFormatter()
        apiDateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = apiDateFormatter.string(from: date)
        let endpoint = "/app-api/iot/sleep/getSleepDetails?date=\(dateString)"
        NetworkManager.shared.request(
            endpoint: endpoint,
            method: .get,
            responseType: [SleepDetailData].self
        )
        .sink(receiveCompletion: { completion in
                self.isLoadingSleepDetails = false
            if case let .failure(error) = completion {
                self.handleSleepDetailsFetchError(error.localizedDescription)
            }
        }, receiveValue: { response in
//            if response.code == 200 || response.code == 0 {
                        // 更新UI数据
//                if let sleepDetails = response.data, !sleepDetails.isEmpty {
                    self.sleepDetailsData = response
                    print("SleepDetailView - 获取睡眠详情数据成功: \(response.count) 条记录")
                            
                            // 处理并显示睡眠阶段数据
                    self.processSleepStagesData(response)
                            
                            // 设置高亮睡眠区间
                    self.updateSleepHighlightPeriod(response)
//                } else {
//                    self.handleSleepDetailsFetchError("该日期没有睡眠详情数据")
//                }
//            } else {
//                self.handleSleepDetailsFetchError("API错误: \(response.msg)")
//            }
        })
        .store(in: &cancellables)
    }
    
    // 处理睡眠详情数据并更新图表
    private func processSleepStagesData(_ sleepDetails: [SleepDetailData]) {
        // 这里我们需要将API返回的数据转换为图表所需的格式
        // 睡眠阶段类型转换: 0=清醒(Awake), 1=浅睡(Core), 2=深睡(Deep), 3=REM
        
        // 首先对数据按开始时间排序
        let sortedDetails = sleepDetails.sorted {
            $0.startTime < $1.startTime
        }
        
        if sortedDetails.isEmpty {
            return
        }
        
        // 检查是否有有效数据
        print("处理睡眠阶段数据: \(sortedDetails.count)条记录")
        
        // 在此处我们可以计算各个阶段的总时间和百分比
        var totalAwake = 0
        var totalREM = 0
        var totalCore = 0
        var totalDeep = 0
        
        for detail in sortedDetails {
            switch detail.type {
            case 0: // 清醒
                totalAwake += detail.total
            case 1: // 浅睡(Core)
                totalCore += detail.total
            case 2: // 深睡
                totalDeep += detail.total
            case 3: // REM
                totalREM += detail.total
            default:
                break
            }
        }
        
        // 计算总时间
        let totalTime = totalAwake + totalREM + totalCore + totalDeep
        
        if totalTime > 0 {
            // 更新百分比
            self.awakePercentage = Double(totalAwake * 100) / Double(totalTime)
            self.remPercentage = Double(totalREM * 100) / Double(totalTime)
            self.corePercentage = Double(totalCore * 100) / Double(totalTime)
            self.deepPercentage = Double(totalDeep * 100) / Double(totalTime)
            
            // 更新时间显示
            self.awakeTime = "\(totalAwake) min"//self.formatMinutes(totalAwake)
            self.remTime = "\(totalREM) min" //self.formatMinutes(totalREM)
            self.coreTime = "\(totalCore) min" //self.formatMinutes(totalCore)
            self.deepTime = "\(totalDeep) min" //self.formatMinutes(totalDeep)
            
            print("睡眠阶段时间(API): Awake=\(totalAwake)分钟, REM=\(totalREM)分钟, Core=\(totalCore)分钟, Deep=\(totalDeep)分钟")
            print("睡眠阶段百分比(API): Awake=\(Int(self.awakePercentage))%, REM=\(Int(self.remPercentage))%, Core=\(Int(self.corePercentage))%, Deep=\(Int(self.deepPercentage))%")
        }
        
        // 注意：在实际项目中，我们需要更新newSleepStagesChart视图
        // 目前我们只能在这里生成了百分比和时间数据
        // 但Sleep Stages图表的实际实现中，我们需要根据每个睡眠阶段的具体时间
        // 动态生成每个色块和连接线，这需要修改newSleepStagesChart视图的代码
    }
    
    // 更新睡眠高亮区间
    private func updateSleepHighlightPeriod(_ sleepDetails: [SleepDetailData]) {
        // 找出深睡区间
        let deepSleepStages = sleepDetails.filter { $0.type == 2 } // 2 = 深睡
        if let firstDeep = deepSleepStages.first, let lastDeep = deepSleepStages.last {
            // 将毫秒时间戳转换为Date
            let startDate = Date(timeIntervalSince1970: Double(firstDeep.startTime) / 1000.0)
            let endDate = Date(timeIntervalSince1970: Double(lastDeep.endTime) / 1000.0)
            
            let timeFormatter = DateFormatter()
            timeFormatter.dateFormat = "HH:mm"
            timeFormatter.timeZone = TimeZone.current
            
            let startTimeStr = timeFormatter.string(from: startDate)
            let endTimeStr = timeFormatter.string(from: endDate)
            self.sleepHighlightPeriod = "\(startTimeStr)-\(endTimeStr)"
            print("设置睡眠高亮区间: \(self.sleepHighlightPeriod)")
        }
    }
    
    // 处理睡眠详情数据获取错误
    private func handleSleepDetailsFetchError(_ message: String) {
        sleepDetailsError = message
        print("SleepDetailView - 睡眠详情获取错误: \(message)")
    }

    private func fetchTemperatureData(for date: Date) {
        isLoadingTemperature = true
        temperatureError = nil
        let apiDateFormatter = DateFormatter()
        apiDateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = apiDateFormatter.string(from: date)
        let endpoint = "/app-api/iot/sleep/getTemperature?date=\(dateString)"
        NetworkManager.shared.request(
            endpoint: endpoint,
            method: .get,
            responseType: SleepTemperatureResponse.self
        )
        .sink(receiveCompletion: { completion in
            self.isLoadingTemperature = false
            if case let .failure(error) = completion {
                self.handleTemperatureFetchError(error.localizedDescription)
            }
        }, receiveValue: { response in
            if response.code == 200 || response.code == 0 {
                self.temperatureData = response.data
                if response.data == nil {
                     self.handleTemperatureFetchError("sleep_error_no_temp_data".localized)
                }
            } else {
                self.handleTemperatureFetchError(response.msg ?? "sleep_error_unknown".localized)
            }
        })
        .store(in: &cancellables)
    }

    private func handleTemperatureFetchError(_ message: String) {
        isLoadingTemperature = false
        temperatureError = message
        temperatureData = nil
        print("体温数据错误: \(message)")
    }
}

// MARK: - 数据模型
enum SleepStageType {
    case awake, rem, core, deep
}

struct SleepStage {
    let time: String
    let stage: SleepStageType
}

struct SPO2Point {
    let time: String
    let value: Double
}

// MARK: - 睡眠详情API模型
struct SleepDetailsResponse: Codable {
    let code: Int
    let data: [SleepDetailData]?
    let msg: String
}

struct SleepDetailData: Codable {
    let type: Int        // 阶段类型: 0=清醒, 1=浅睡, 2=深睡, 3=REM
    let total: Int       // 该阶段总时长(分钟)
    let startTime: Int   // 开始时间（毫秒时间戳）
    let endTime: Int     // 结束时间（毫秒时间戳）
}

// MARK: - 体温API模型
struct SleepTemperatureResponse: Codable {
    let code: Int
    let data: SleepTemperatureModel?
    let msg: String?
}

struct SleepTemperatureModel: Codable {
    let average: Double?
    let baseline: Double?
    let differ: Double?
}

// MARK: - 预览
struct SleepDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            SleepDetailView()
                .environmentObject(HealthDataManager.shared) // 使用shared单例而不是直接初始化
        }
        .preferredColorScheme(.dark)
    }
}
