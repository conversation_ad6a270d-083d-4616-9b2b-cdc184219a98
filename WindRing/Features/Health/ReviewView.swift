import SwiftUI

/// 健康数据回顾视图
struct ReviewView: View {
    // MARK: - 状态属性
    @StateObject private var deviceService = WindRingDeviceService.shared
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 顶部卡片 - 今日总结
                dailySummaryCard
                
                // 健康数据卡片
                Text("健康趋势")
                    .font(.headline)
                    .padding(.horizontal)
                
                // 心率趋势
                healthDataCard(
                    title: "心率趋势",
                    subtitle: "平均心率",
                    value: "\(deviceService.connectionState.isConnected ? "68" : "--")",
                    unit: "BPM",
                    color: .red
                )
                
                // 血氧趋势
                healthDataCard(
                    title: "血氧趋势",
                    subtitle: "平均血氧",
                    value: "\(deviceService.connectionState.isConnected ? "98" : "--")",
                    unit: "%",
                    color: .blue
                )
                
                // 睡眠趋势
                NavigationLink(destination: SleepDetailView()) {
                    healthDataCard(
                        title: "睡眠趋势",
                        subtitle: "昨晚睡眠",
                        value: "\(deviceService.connectionState.isConnected ? "7.2" : "--")",
                        unit: "小时",
                        color: .purple
                    )
                }
                .buttonStyle(PlainButtonStyle())
                
                // 其他健康数据图表
                // ...
                
                Spacer(minLength: 50)
            }
            .padding(.vertical)
        }
        .background(Color.appBackground)
        .navigationTitle("健康回顾")
    }
    
    // MARK: - 今日总结卡片
    private var dailySummaryCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("今日总结")
                .font(.headline)
            
            if deviceService.connectionState.isConnected {
                HStack(spacing: 20) {
                    // 步数
                    summaryItem(
                        value: "6,238",
                        label: "步数",
                        icon: "figure.walk",
                        color: .green
                    )
                    
                    // 卡路里
                    summaryItem(
                        value: "324",
                        label: "卡路里",
                        icon: "flame.fill",
                        color: .orange
                    )
                    
                    // 活动
                    summaryItem(
                        value: "42",
                        label: "活动分钟",
                        icon: "heart.circle.fill",
                        color: .red
                    )
                }
            } else {
                // 未连接设备时显示提示
                HStack {
                    Spacer()
                    
                    VStack(spacing: 10) {
                        Image(systemName: "exclamationmark.circle")
                            .font(.system(size: 30))
                            .foregroundColor(.secondary)
                        
                        Text("请连接设备获取数据")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
                .padding(.vertical, 30)
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    // MARK: - 辅助视图
    /// 总结项目视图
    private func summaryItem(value: String, label: String, icon: String, color: Color) -> some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(color)
            
            Text(value)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(.primary)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
    
    /// 健康数据卡片视图
    private func healthDataCard(title: String, subtitle: String, value: String, unit: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(.headline)
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(subtitle)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    HStack(alignment: .firstTextBaseline) {
                        Text(value)
                            .font(.system(size: 28, weight: .bold))
                        
                        Text(unit)
                            .font(.system(size: 16))
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 这里可以添加简单的趋势图
                trendIndicator(color: color)
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    /// 趋势指示器
    private func trendIndicator(color: Color) -> some View {
        HStack(spacing: 2) {
            ForEach(0..<7, id: \.self) { i in
                RoundedRectangle(cornerRadius: 2)
                    .fill(color)
                    .frame(width: 4, height: CGFloat(10 + (i % 3) * 10))
            }
        }
        .padding(8)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
}

struct ReviewView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ReviewView()
        }
    }
} 