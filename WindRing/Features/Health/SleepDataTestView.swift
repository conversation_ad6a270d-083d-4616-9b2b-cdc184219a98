import SwiftUI
import CRPSmartRing

/// 睡眠数据测试视图 - 专门用于测试从戒指设备中获取睡眠数据
struct SleepDataTestView: View {
    // MARK: - 状态属性
    @StateObject private var deviceService = WindRingDeviceService.shared
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var selectedDayOffset = 1 // 默认获取昨天的数据
    @State private var algorithmType: WindRingDeviceService.SleepAlgorithmType = .goMore // 默认使用GoMore高级算法
    @State private var timeout: Double = 10.0 // 默认超时时间10秒
    @State private var debugLogs: [String] = []
    @State private var sleepData: WindRingDeviceService.SleepData? = nil
    @State private var rawSleepData: CRPSleepRecordModel? = nil
    @State private var showShareSheet = false
    @State private var jsonData = ""
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 连接状态显示
                connectionStatusView
                
                // 设置参数部分
                settingsView
                
                // 获取数据按钮
                fetchDataButton
                
                // 综合数据测试按钮
                dataTestButton
                
                // 测试结果显示区域
                resultView
                
                // 调试日志
                debugLogView
            }
            .padding()
        }
        .navigationTitle("睡眠数据获取测试")
        .sheet(isPresented: $showShareSheet) {
            // 使用项目中已有的ShareSheet组件
            ShareSheet(activityItems: [jsonData])
        }
        .onAppear {
            addLog("视图加载，当前设备连接状态: \(deviceService.connectionState.description)")
            if deviceService.connectionState.isConnected {
                // 不要获取物理MAC地址，改用设备名称和UUID
                if let deviceInfo = deviceService.deviceInfo {
                    addLog("已连接设备: \(deviceInfo.localName?.description ?? "")")
                    // 使用设备UUID或其他标识符替代MAC地址
                    if let deviceId = deviceInfo.mac {
                        addLog("设备ID: \(deviceId)")
                    }
                } else {
                    addLog("已连接设备，但无法获取设备信息")
                }
                addLog("当前固件类型: \(deviceService.firmwareType)")
                addLog("当前睡眠算法: \(deviceService.sleepAlgorithmType.description)")
            }
        }
    }
    
    // MARK: - 连接状态视图
    private var connectionStatusView: some View {
        HStack {
            Circle()
                .fill(deviceService.connectionState.isConnected ? Color.green : Color.red)
                .frame(width: 12, height: 12)
            
            Text(deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized)
                .font(.footnote)
            
            Spacer()
            
            if !deviceService.connectionState.isConnected {
                NavigationLink(destination: DevicePairingView()) {
                    Text("连接设备")
                        .font(.footnote)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue)
                        .cornerRadius(8)
                }
            } else {
                // 只显示设备名称，不显示MAC地址
                Text("设备名称: \(deviceService.deviceInfo?.localName ?? "未知")")
                    .font(.footnote)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - 设置参数视图
    private var settingsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测试参数设置")
                .font(.headline)
                .padding(.bottom, 4)
            
            // 要获取的日期
            VStack(alignment: .leading, spacing: 6) {
                Text("选择日期")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Picker("选择日期", selection: $selectedDayOffset) {
                    Text("今天").tag(0)
                    Text("昨天").tag(1)
                    Text("前天").tag(2)
                    Text("3天前").tag(3)
                    Text("4天前").tag(4)
                    Text("5天前").tag(5)
                    Text("6天前").tag(6)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            // 算法选择
            VStack(alignment: .leading, spacing: 6) {
                Text("睡眠算法")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Picker("睡眠算法", selection: $algorithmType) {
                    Text("GoMore高级算法").tag(WindRingDeviceService.SleepAlgorithmType.goMore)
                    Text("基础算法").tag(WindRingDeviceService.SleepAlgorithmType.basic)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            // 超时设置
            VStack(alignment: .leading, spacing: 6) {
                Text("超时时间: \(Int(timeout))秒")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Slider(value: $timeout, in: 5...30, step: 1) {
                    Text("超时时间")
                } minimumValueLabel: {
                    Text("5秒")
                } maximumValueLabel: {
                    Text("30秒")
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - 获取数据按钮
    private var fetchDataButton: some View {
        Button(action: fetchSleepData) {
            HStack {
                Image(systemName: "arrow.down.circle")
                Text("获取睡眠数据")
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(deviceService.connectionState.isConnected ? Color.blue : Color.gray)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .disabled(!deviceService.connectionState.isConnected || isLoading)
        .overlay(
            Group {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                }
            }
        )
    }
    
    // MARK: - 综合数据测试按钮
    private var dataTestButton: some View {
        NavigationLink(destination: AllDataTestView()) {
            HStack {
                Image(systemName: "gear.circle")
                Text("综合数据测试工具")
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.purple)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
    }
    
    // MARK: - 结果显示区域
    private var resultView: some View {
        Group {
            if let errorMsg = errorMessage {
                VStack(alignment: .leading, spacing: 10) {
                    Text("获取数据失败")
                        .font(.headline)
                        .foregroundColor(.red)
                    
                    Text(errorMsg)
                        .foregroundColor(.red)
                        .font(.body)
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(Color(UIColor.systemBackground))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red, lineWidth: 1)
                )
            } else if let data = sleepData {
                VStack(alignment: .leading, spacing: 15) {
                    HStack {
                        Text("睡眠数据获取成功")
                            .font(.headline)
                            .foregroundColor(.green)
                        
                        Spacer()
                        
                        Button(action: exportData) {
                            Image(systemName: "square.and.arrow.up")
                                .foregroundColor(.blue)
                        }
                    }
                    
                    Divider()
                    
                    // 睡眠时间范围
                    HStack {
                        VStack(alignment: .leading) {
                            Text("开始时间")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text(formatDate(data.startTime))
                                .font(.body)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing) {
                            Text("结束时间")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text(formatDate(data.endTime))
                                .font(.body)
                        }
                    }
                    
                    Divider()
                    
                    // 睡眠时长和质量
                    HStack {
                        // 总睡眠时长
                        let totalMinutes = data.deepSleepMinutes + data.lightSleepMinutes + data.remSleepMinutes
                        let hours = totalMinutes / 60
                        let minutes = totalMinutes % 60
                        
                        VStack(alignment: .leading) {
                            Text("总时长")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(hours)小时\(minutes)分钟")
                                .font(.body)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing) {
                            Text("睡眠质量")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(data.sleepQuality)")
                                .font(.body)
                        }
                    }
                    
                    Divider()
                    
                    // 睡眠阶段
                    HStack {
                        sleepStageView(title: "深睡", minutes: data.deepSleepMinutes, color: .indigo)
                        sleepStageView(title: "浅睡", minutes: data.lightSleepMinutes, color: .blue)
                        sleepStageView(title: "REM", minutes: data.remSleepMinutes, color: .purple)
                        if data.awakeMinutes > 0 {
                            sleepStageView(title: "清醒", minutes: data.awakeMinutes, color: .orange)
                        }
                    }
                }
                .padding()
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(12)
            } else if !isLoading {
                Text("点击上方按钮获取睡眠数据")
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
            }
        }
    }
    
    // MARK: - 调试日志视图
    private var debugLogView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("调试日志111")
                    .font(.headline)
                
                Spacer()
                
                Button(action: clearLogs) {
                    Text("清除")
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
            
            ScrollView {
                VStack(alignment: .leading, spacing: 6) {
                    ForEach(debugLogs.indices, id: \.self) { index in
                        Text(debugLogs[index])
                            .font(.system(.caption, design: .monospaced))
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.vertical, 2)
                    }
                }
            }
            .frame(height: 200)
            .background(Color(UIColor.systemBackground))
            .cornerRadius(8)
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - 辅助视图
    private func sleepStageView(title: String, minutes: Int, color: Color) -> some View {
        VStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("\(minutes)")
                .font(.headline)
                .foregroundColor(color)
            
            Text("分钟")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - 方法
    private func fetchSleepData() {
        guard deviceService.connectionState.isConnected else {
            errorMessage = "设备未连接，请先连接设备"
            return
        }
        
        isLoading = true
        errorMessage = nil
        sleepData = nil
        addLog("开始获取睡眠数据，日期偏移: \(selectedDayOffset)天，算法: \(algorithmType.description)，超时: \(Int(timeout))秒")
        
        // 保存当前的算法设置
        let originalAlgorithm = deviceService.sleepAlgorithmType
        
        // 设置要使用的算法
        if deviceService.sleepAlgorithmType != algorithmType {
            addLog("临时更改睡眠算法为: \(algorithmType.description)")
            deviceService.sleepAlgorithmType = algorithmType
        }
        
        // 创建一个超时任务
        let timeoutTask = DispatchWorkItem {
            if isLoading {
                isLoading = false
                errorMessage = "获取睡眠数据超时，请检查设备连接并重试"
                addLog("❌ 获取睡眠数据超时 (\(Int(timeout))秒)")
                
                // 恢复原始算法设置
                if deviceService.sleepAlgorithmType != originalAlgorithm {
                    deviceService.sleepAlgorithmType = originalAlgorithm
                    addLog("恢复睡眠算法为: \(originalAlgorithm.description)")
                }
            }
        }
        
        // 设置超时
        DispatchQueue.main.asyncAfter(deadline: .now() + timeout, execute: timeoutTask)
        
        // 获取原始睡眠数据（用于详细分析）
        if algorithmType == .basic {
            CRPSmartRingSDK.sharedInstance.getSleepData(selectedDayOffset) { model, error in
                // 打印原始睡眠数据
                if error == .none {
                    self.rawSleepData = model
                    
                    // 打印原始睡眠数据详情
                    print("\n====== 原始睡眠数据详情 ======")
                    print("深睡时间: \(model.deep) 分钟")
                    print("浅睡时间: \(model.light) 分钟")
                    print("REM时间: \(model.rem) 分钟")
                    print("总睡眠时间: \(model.deep + model.light + model.rem) 分钟")
                    
                    self.addLog("深睡时间: \(model.deep) 分钟")
                    self.addLog("浅睡时间: \(model.light) 分钟")
                    self.addLog("REM时间: \(model.rem) 分钟")
                    self.addLog("总睡眠时间: \(model.deep + model.light + model.rem) 分钟")
                    
                    // 打印详细睡眠阶段信息
                    if model.detail.isEmpty {
                        print("没有详细的睡眠阶段数据")
                        self.addLog("没有详细的睡眠阶段数据")
                    } else {
                        print("\n----- 睡眠阶段详情 -----")
                        self.addLog("----- 睡眠阶段详情 -----")
                        let dateFormatter = DateFormatter()
                        dateFormatter.dateFormat = "HH:mm:ss"
                        
                        for (index, detail) in model.detail.enumerated() {
                            if let typeString = detail["type"],
                               let startTimeString = detail["start"],
                               let endTimeString = detail["end"],
                               let totalString = detail["total"],
                               let type = Int(typeString),
                               let startTime = TimeInterval(startTimeString),
                               let endTime = TimeInterval(endTimeString),
                               let total = Int(totalString) {
                                
                                let startDate = Date(timeIntervalSince1970: startTime)
                                let endDate = Date(timeIntervalSince1970: endTime)
                                
                                let typeText: String
                                switch type {
                                case 0: typeText = "清醒"
                                case 1: typeText = "浅睡"
                                case 2: typeText = "深睡"
                                case 3: typeText = "REM"
                                default: typeText = "未知"
                                }
                                
                                let stageLog = "阶段 #\(index+1): \(typeText) - 开始: \(dateFormatter.string(from: startDate)) 结束: \(dateFormatter.string(from: endDate)) 持续: \(total)分钟"
                                print(stageLog)
                                self.addLog(stageLog)
                            }
                        }
                    }
                    
                    // 将数据转换为JSON格式并打印
                    let now = Date()
                    let calendar = Calendar.current
                    let selectedDate = calendar.date(byAdding: .day, value: -self.selectedDayOffset, to: now) ?? now
                    
                    let sleepJsonData: [String: Any] = [
                        "total_minutes": model.deep + model.light + model.rem,
                        "awake_minutes": 0, // SDK模型中可能没有此字段，显示为0
                        "end_time": ISO8601DateFormatter().string(from: selectedDate),
                        "total_sleep_minutes": model.deep + model.light + model.rem,
                        "start_time": ISO8601DateFormatter().string(from: selectedDate.addingTimeInterval(-Double(model.deep + model.light + model.rem) * 60)),
                        "deep_sleep_minutes": model.deep,
                        "light_sleep_minutes": model.light,
                        "rem_sleep_minutes": model.rem,
                        "sleep_quality": 0 // SDK模型中可能没有此字段，显示为0
                    ]
                    
                    if let jsonData = try? JSONSerialization.data(withJSONObject: sleepJsonData, options: .prettyPrinted),
                       let jsonString = String(data: jsonData, encoding: .utf8) {
                        print("\n----- 睡眠数据JSON格式 -----")
                        print(jsonString)
                        print("==============================\n")
                        self.addLog("----- 睡眠数据JSON格式 -----")
                        self.addLog(jsonString)
                    }
                }
            }
        }
        
        // 获取睡眠数据
        deviceService.getSleepData(day: selectedDayOffset) { data, error in
            // 取消超时任务
            timeoutTask.cancel()
            
            DispatchQueue.main.async {
                isLoading = false
                
                // 恢复原始算法设置
                if deviceService.sleepAlgorithmType != originalAlgorithm {
                    deviceService.sleepAlgorithmType = originalAlgorithm
                    addLog("恢复睡眠算法为: \(originalAlgorithm.description)")
                }
                
                if let data = data {
                    sleepData = data
                    self.convertToJsonAndLog(data)
                    addLog("✅ 成功获取睡眠数据")
                } else if let error = error {
                    errorMessage = "获取睡眠数据失败: \(error.localizedDescription)"
                    addLog("❌ 获取睡眠数据失败: \(error.localizedDescription)")
                } else {
                    errorMessage = "未获取到睡眠数据，但没有错误信息"
                    addLog("⚠️ 未获取到睡眠数据，但没有错误信息")
                }
            }
        }
    }
    
    private func exportData() {
        guard let data = sleepData else { return }
        
        // 将数据转换为JSON格式
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        let jsonObject: [String: Any] = [
            "start_time": dateFormatter.string(from: data.startTime),
            "end_time": dateFormatter.string(from: data.endTime),
            "deep_sleep_minutes": data.deepSleepMinutes,
            "light_sleep_minutes": data.lightSleepMinutes,
            "rem_sleep_minutes": data.remSleepMinutes,
            "awake_minutes": data.awakeMinutes,
            "sleep_quality": data.sleepQuality,
            "total_minutes": data.deepSleepMinutes + data.lightSleepMinutes + data.remSleepMinutes + data.awakeMinutes
        ]
        
        if let jsonData = try? JSONSerialization.data(withJSONObject: jsonObject, options: .prettyPrinted),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            self.jsonData = jsonString
            showShareSheet = true
        }
    }
    
    private func convertToJsonAndLog(_ data: WindRingDeviceService.SleepData) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        // 打印睡眠数据的详细信息
        print("\n====== 处理后的睡眠数据详情 ======")
        print("开始时间: \(dateFormatter.string(from: data.startTime))")
        print("结束时间: \(dateFormatter.string(from: data.endTime))")
        print("深睡时间: \(data.deepSleepMinutes) 分钟")
        print("浅睡时间: \(data.lightSleepMinutes) 分钟")
        print("REM时间: \(data.remSleepMinutes) 分钟")
        print("清醒时间: \(data.awakeMinutes) 分钟")
        print("睡眠质量: \(data.sleepQuality)")
        print("总睡眠时间: \(data.deepSleepMinutes + data.lightSleepMinutes + data.remSleepMinutes + data.awakeMinutes) 分钟")
        
        // 添加到调试日志
        addLog("处理后的睡眠数据详情:")
        addLog("深睡时间: \(data.deepSleepMinutes) 分钟")
        addLog("浅睡时间: \(data.lightSleepMinutes) 分钟")
        addLog("REM时间: \(data.remSleepMinutes) 分钟")
        addLog("清醒时间: \(data.awakeMinutes) 分钟")
        addLog("睡眠质量: \(data.sleepQuality)")
        
        let jsonObject: [String: Any] = [
            "start_time": dateFormatter.string(from: data.startTime),
            "end_time": dateFormatter.string(from: data.endTime),
            "deep_sleep_minutes": data.deepSleepMinutes,
            "light_sleep_minutes": data.lightSleepMinutes,
            "rem_sleep_minutes": data.remSleepMinutes,
            "awake_minutes": data.awakeMinutes,
            "sleep_quality": data.sleepQuality,
            "total_minutes": data.deepSleepMinutes + data.lightSleepMinutes + data.remSleepMinutes + data.awakeMinutes
        ]
        
        if let jsonData = try? JSONSerialization.data(withJSONObject: jsonObject, options: .prettyPrinted),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            // 打印 JSON 格式数据
            print("\n----- 处理后的睡眠数据JSON格式 -----")
            print(jsonString)
            print("======================================\n")
            
            addLog("处理后的睡眠数据JSON: \n\(jsonString)")
            self.jsonData = jsonString
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }
    
    private func addLog(_ message: String) {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        let timestamp = formatter.string(from: Date())
        debugLogs.insert("[\(timestamp)] \(message)", at: 0)
        
        // 限制日志数量
        if debugLogs.count > 100 {
            debugLogs = Array(debugLogs.prefix(100))
        }
    }
    
    private func clearLogs() {
        debugLogs.removeAll()
        addLog("日志已清除")
    }
} 
