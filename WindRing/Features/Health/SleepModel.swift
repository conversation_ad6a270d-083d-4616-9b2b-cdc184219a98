import Foundation

class SleepModel: ObservableObject {
    enum TimeRange: String, CaseIterable {
        case week = "Week"
        case month = "Month"
        case year = "Year"
    }
    
    struct SleepData {
        let weekday: String
        let sleepScore: Double
        let sleepEfficiency: Double
        let totalSleepTime: TimeInterval
        let averageHeartRate: Double
        let hrvValue: Double
        let oxygenSaturation: Double
        let skinTemperatureOffset: Double
    }
    
    struct Stats {
        var min: Double
        var avg: Double
        var max: Double
    }
    
    @Published var selectedTimeRange: TimeRange = .week
    @Published var sleepData: [SleepData] = []
    @Published var dateRange: String = "2023.3.10-2023.3.16"
    
    // 各项指标的统计数据
    var sleepScoreStats: Stats { calculateStats(from: sleepData.map { $0.sleepScore }) }
    var sleepEfficiencyStats: Stats { calculateStats(from: sleepData.map { $0.sleepEfficiency }) }
    var totalSleepTimeStats: Stats { calculateStats(from: sleepData.map { $0.totalSleepTime }) }
    var heartRateStats: Stats { calculateStats(from: sleepData.map { $0.averageHeartRate }) }
    var hrvStats: Stats { calculateStats(from: sleepData.map { $0.hrvValue }) }
    var oxygenSaturationStats: Stats { calculateStats(from: sleepData.map { $0.oxygenSaturation }) }
    
    var skinTemperatureOffsetAvg: Double {
        let values = sleepData.map { $0.skinTemperatureOffset }
        return values.reduce(0, +) / Double(values.count)
    }
    
    init() {
        loadMockData()
    }
    
    func changeTimeRange(to range: TimeRange) {
        selectedTimeRange = range
        loadMockData()
    }
    
    private func calculateStats(from values: [Double]) -> Stats {
        guard !values.isEmpty else { return Stats(min: 0, avg: 0, max: 0) }
        
        let min = values.min() ?? 0
        let max = values.max() ?? 0
        let avg = values.reduce(0, +) / Double(values.count)
        
        return Stats(min: min, avg: avg, max: max)
    }
    
    private func loadMockData() {
        // 模拟周数据: 周五到周四
        let weekdays = ["Fri", "Sat", "Sun", "Mon", "Tue", "Wed", "Thu"]
        
        // 周一是高亮日，这里设置对应的数据
        var data = [SleepData]()
        
        // 睡眠评分: 46-48 bpm, 周一为47
        let sleepScores = [47.0, 48.0, 47.0, 47.0, 46.0, 47.0, 46.0]
        
        // 睡眠效率: 56-83%, 周一为70%
        let sleepEfficiencies = [65.0, 83.0, 60.0, 70.0, 65.0, 56.0, 60.0]
        
        // 总睡眠时间 (秒)
        // 最大值: 3小时48分钟, 平均值: 2小时39分钟, 最小值: 1小时30分钟
        let sleepTimes: [TimeInterval] = [
            3.5 * 3600, 
            3.8 * 3600, 
            2.5 * 3600, 
            2.65 * 3600, 
            2.4 * 3600, 
            1.5 * 3600, 
            2.2 * 3600
        ]
        
        // 睡眠心率
        let heartRates = [60.0, 65.0, 58.0, 62.0, 59.0, 57.0, 61.0]
        
        // HRV值: 最大为72ms, 平均42ms, 最小17ms
        let hrvValues = [40.0, 72.0, 35.0, 42.0, 45.0, 17.0, 38.0]
        
        // 血氧: 最大98%, 平均96%, 最小91%
        let oxygenValues = [95.0, 98.0, 96.0, 96.0, 97.0, 91.0, 95.0]
        
        // 皮肤温度偏移
        let skinTempOffsets = [-0.5, 0.8, -0.3, 0.5, -0.2, -1.2, 0.3]
        
        for i in 0..<7 {
            data.append(SleepData(
                weekday: weekdays[i],
                sleepScore: sleepScores[i],
                sleepEfficiency: sleepEfficiencies[i],
                totalSleepTime: sleepTimes[i],
                averageHeartRate: heartRates[i],
                hrvValue: hrvValues[i],
                oxygenSaturation: oxygenValues[i],
                skinTemperatureOffset: skinTempOffsets[i]
            ))
        }
        
        sleepData = data
    }
} 