import SwiftUI
import CRPSmartRing

/// 睡眠API测试视图 - 用于测试智能戒指的所有睡眠相关API
public struct SleepAPITestView: View {
    // MARK: - 状态属性
    @StateObject private var viewModel = SleepAPITestViewModel()
    @State private var showShareSheet = false
    @State private var csvData: String = ""
    @State private var selectedTab: Int = 0
    
    // MARK: - 主视图
    public var body: some View {
        NavigationView {
            VStack {
                // 顶部导航栏和连接状态
                connectionStatusView
                
                // 标签选择器
                Picker("测试类别", selection: $selectedTab) {
                    Text("基础睡眠").tag(0)
                    Text("温度相关").tag(1)
                    Text("高级功能").tag(2)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal)
                
                // 根据标签显示不同的测试选项
                ScrollView {
                    VStack(spacing: 20) {
                        // 测试选项区域
                        Group {
                            if selectedTab == 0 {
                                basicSleepTestView
                            } else if selectedTab == 1 {
                                temperatureTestView
                            } else {
                                advancedSleepTestView
                            }
                        }
                        .padding(.horizontal)
                        
                        Divider()
                            .padding(.vertical)
                        
                        // 测试结果区域
                        resultsView
                            .padding(.horizontal)
                    }
                    .padding(.vertical)
                }
            }
            .navigationTitle("睡眠API测试")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: exportResults) {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { viewModel.clearResults() }) {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                    }
                }
            }
            .sheet(isPresented: $showShareSheet) {
                SleepActivityViewController(activityItems: [csvData])
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 连接状态视图
    private var connectionStatusView: some View {
        HStack {
            Circle()
                .fill(viewModel.deviceService.connectionState.isConnected ? Color.green : Color.red)
                .frame(width: 12, height: 12)
            
            Text(viewModel.deviceService.connectionState.isConnected ? "已连接: \(viewModel.deviceService.deviceInfo?.localName ?? "")" : "disconnected".localized)
                .font(.footnote)
            
            Spacer()
            
            if !viewModel.deviceService.connectionState.isConnected {
                NavigationLink(destination: DeviceManagerView()) {
                    Text("连接设备")
                        .font(.footnote)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue)
                        .cornerRadius(8)
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
    
    /// 基础睡眠测试区域
    private var basicSleepTestView: some View {
        VStack(spacing: 12) {
            Text("基础睡眠API")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 获取睡眠数据
            ForEach([0, 1, 2, 3], id: \.self) { day in
                Button(action: {
                    viewModel.testGetSleepData(day: day)
                }) {
                    HStack {
                        Image(systemName: "moon.zzz")
                        Text(day == 0 ? "获取今天睡眠数据" : "获取\(day)天前睡眠数据")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
                .disabled(!viewModel.deviceService.connectionState.isConnected)
            }
            
            // 设置睡眠数据
            Button(action: {
                viewModel.testSetSleepData()
            }) {
                HStack {
                    Image(systemName: "arrow.up.doc")
                    Text("设置睡眠数据")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
            
            // 数据同步按钮
            Button(action: {
                viewModel.syncAllSleepDataToServer()
            }) {
                HStack {
                    Image(systemName: "arrow.2.circlepath")
                    Text("同步睡眠数据到云端")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            
            // 获取多天睡眠数据
            Button(action: {
                viewModel.testGetMultiDaySleepData()
            }) {
                HStack {
                    Image(systemName: "calendar")
                    Text("获取多天睡眠数据(0-6天)")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
            
            // 获取前一个月睡眠数据
            Button(action: {
                viewModel.testGetLastMonthSleepData()
            }) {
                HStack {
                    Image(systemName: "calendar.badge.clock")
                    Text("获取前一个月睡眠数据")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
        }
    }
    
    /// 温度相关测试区域
    private var temperatureTestView: some View {
        VStack(spacing: 12) {
            Text("睡眠温度API")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 获取睡眠温度状态
            Button(action: {
                viewModel.testGetSleepTemperatureState()
            }) {
                HStack {
                    Image(systemName: "thermometer")
                    Text("获取睡眠温度监测状态")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
            
            // 开启睡眠温度监测
            Button(action: {
                viewModel.testSetSleepTemperatureState(open: true)
            }) {
                HStack {
                    Image(systemName: "switch.2")
                    Text("开启睡眠温度监测")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
            
            // 关闭睡眠温度监测
            Button(action: {
                viewModel.testSetSleepTemperatureState(open: false)
            }) {
                HStack {
                    Image(systemName: "switch.2")
                    Text("关闭睡眠温度监测")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
            
            // 获取睡眠温度数据
            ForEach([0, 1], id: \.self) { day in
                Button(action: {
                    viewModel.testGetSleepTemperatureData(day: day)
                }) {
                    HStack {
                        Image(systemName: "thermometer.sun")
                        Text(day == 0 ? "获取今天睡眠温度数据" : "获取昨天睡眠温度数据")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
                .disabled(!viewModel.deviceService.connectionState.isConnected)
            }
        }
    }
    
    /// 高级睡眠测试区域
    private var advancedSleepTestView: some View {
        VStack(spacing: 12) {
            Text("高级睡眠API")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 获取睡眠血氧支持状态
            Button(action: {
                viewModel.testGetSleepO2SupportState()
            }) {
                HStack {
                    Image(systemName: "lungs")
                    Text("获取睡眠血氧支持状态")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
            
            // 获取GoMore睡眠类型
            Button(action: {
                viewModel.testGetGoMoreSleepType()
            }) {
                HStack {
                    Image(systemName: "chart.bar.doc.horizontal")
                    Text("获取GoMore睡眠类型")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
            
            // 获取GoMore睡眠数据列表
            Button(action: {
                viewModel.testGetGoMoreSleepDataList()
            }) {
                HStack {
                    Image(systemName: "list.bullet")
                    Text("获取GoMore睡眠数据列表")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
            
            // 设置GoMore睡眠生成
            Button(action: {
                viewModel.testSetGoMoreSleepGenerate()
            }) {
                HStack {
                    Image(systemName: "gear")
                    Text("设置GoMore睡眠生成")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
            
            // 终止睡眠
            Button(action: {
                viewModel.testSetTerminationSleep()
            }) {
                HStack {
                    Image(systemName: "stop.circle")
                    Text("终止睡眠")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .disabled(!viewModel.deviceService.connectionState.isConnected)
        }
    }
    
    /// 测试结果视图
    private var resultsView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("测试结果")
                    .font(.headline)
                
                Spacer()
                
                Text("\(viewModel.testResults.count)条记录")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            if viewModel.testResults.isEmpty {
                Text("暂无测试结果")
                    .foregroundColor(.gray)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .center)
            } else {
                ScrollView {
                    VStack(alignment: .leading, spacing: 4) {
                        ForEach(Array(viewModel.testResults.enumerated()), id: \.offset) { index, result in
                            Text(result)
                                .font(.system(.footnote, design: .monospaced))
                                .foregroundColor(viewModel.resultColors[index] ?? .primary)
                                .padding(.vertical, 2)
                        }
                    }
                }
                .frame(height: 300)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
    
    // MARK: - 测试方法
    
    /// 导出测试结果
    private func exportResults() {
        if viewModel.testResults.isEmpty {
            return
        }
        
        // 创建CSV数据
        if csvData.isEmpty {
            var csv = "测试结果导出,\(Date())\n\n"
            
            for result in viewModel.testResults {
                csv += "\(result)\n"
            }
            
            csvData = csv
        }
        
        // 显示分享表单
        showShareSheet = true
    }
}

class SleepAPITestViewModel: ObservableObject {
    @Published var testResults: [String] = []
    @Published var resultColors: [Int: Color] = [:]
    @Published var deviceService: WindRingDeviceService
    private let apiService = APIService.shared
    private let healthDataManager = HealthDataManager.shared
    private var autoSyncTimer: Timer?
    
    init() {
        self.deviceService = WindRingDeviceService.shared
        CRPSmartRingSDK.sharedInstance.delegate = self
        setupAutoSync()
    }
    
    deinit {
        autoSyncTimer?.invalidate()
    }
    
    /// 设置自动同步计时器
    private func setupAutoSync() {
        // 检查是否需要同步（上次同步时间超过4小时）
        let lastSyncTime = UserDefaults.standard.object(forKey: "lastSyncTime") as? Date
        let now = Date()
        
        if lastSyncTime == nil || now.timeIntervalSince(lastSyncTime!) > 4 * 60 * 60 {
            // 应用启动后延迟30秒执行首次同步
            DispatchQueue.main.asyncAfter(deadline: .now() + 30) { [weak self] in
                self?.backgroundSync()
            }
        }
        
        // 设置定期同步（每4小时一次）
        autoSyncTimer = Timer.scheduledTimer(withTimeInterval: 4 * 60 * 60, repeats: true) { [weak self] _ in
            self?.backgroundSync()
        }
    }
    
    /// 后台执行同步，不显示UI提示
    private func backgroundSync() {
        // 只有在设备连接状态下才同步
        guard deviceService.connectionState.isConnected else {
            print("设备未连接，跳过自动同步")
            return
        }
        
        print("开始后台自动同步睡眠数据...")
        
        let userId = "user1" // 实际应用中应从用户服务获取
        let lastSync = UserDefaults.standard.object(forKey: "lastSyncTime") as? Date
        let startDate = lastSync ?? Calendar.current.date(byAdding: .day, value: -7, to: Date())!
        
        // 从本地数据库获取所有睡眠数据
        let sleepData = healthDataManager.getSleep(userId: userId, startDate: startDate, endDate: Date())
        
        if sleepData.isEmpty {
            print("本地数据库无新睡眠数据可同步")
            return
        }
        
        print("找到 \(sleepData.count) 条本地睡眠数据记录")
        
        // 创建同步任务组
        let group = DispatchGroup()
        var successCount = 0
        var failCount = 0
        
        // 为每条数据创建同步任务
        for (index, sleepEntity) in sleepData.enumerated() {
            group.enter()
            
            // 获取日期字符串
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: sleepEntity.startTime ?? Date())
            
            // 获取睡眠阶段数据
            let sleepStages = healthDataManager.getSleepStages(sleepId: sleepEntity.id!)
            
            // 转换睡眠阶段为JSON
            var records: [[String: Any]] = []
            for stage in sleepStages {
                if let startTime = stage.startTime, let type = stage.type {
                    // 计算结束时间
                    let endTime = startTime.addingTimeInterval(TimeInterval(stage.duration * 60))
                    
                    // 转换类型
                    let typeValue: Int
                    switch type {
                    case "awake": typeValue = 0
                    case "light": typeValue = 1
                    case "deep": typeValue = 2
                    case "rem": typeValue = 3
                    default: typeValue = 0
                    }
                    
                    let record: [String: Any] = [
                        "type": typeValue,
                        "start_time": Int(startTime.timeIntervalSince1970),
                        "end_time": Int(endTime.timeIntervalSince1970),
                        "duration": Int(stage.duration)
                    ]
                    
                    records.append(record)
                }
            }
            
            // 构建请求数据
            let requestData: [String: Any] = [
                "date": dateString,
                "deep": sleepEntity.deepMinutes ?? 0,
                "light": sleepEntity.lightMinutes ?? 0,
                "rem": sleepEntity.remMinutes ?? 0,
                "records": records
            ]
            
            // 请求头部
            let headers = [
                "Content-Type": "application/json",
                "tenant-id": "1",
                "Authorization": "Bearer \(AuthService.shared.currentToken?.accessToken ?? "")"
            ]
            
            // 构建URL
            guard let url = URL(string: "https://yapi.weaving-park.com/mock/434/app-api/iot/sleep/upload/sleep/data") else {
                print("无效的API URL")
                failCount += 1
                group.leave()
                continue
            }
            
            // 创建请求
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            for (key, value) in headers {
                request.setValue(value, forHTTPHeaderField: key)
            }
            
            do {
                request.httpBody = try JSONSerialization.data(withJSONObject: requestData)
            } catch {
                print("请求数据序列化失败: \(error.localizedDescription)")
                failCount += 1
                group.leave()
                continue
            }
            
            // 执行网络请求
            URLSession.shared.dataTask(with: request) { data, response, error in
                defer { group.leave() }
                
                if let error = error {
                    print("网络请求失败: \(error.localizedDescription)")
                    failCount += 1
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("无效的HTTP响应")
                    failCount += 1
                    return
                }
                
                if (200...299).contains(httpResponse.statusCode) {
                    // 解析响应数据
                    if let data = data {
                        do {
                            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                                print("第\(index+1)条睡眠数据同步响应: \(json)")
                                successCount += 1
                            }
                        } catch {
                            print("响应解析失败: \(error.localizedDescription)")
                            failCount += 1
                        }
                    }
                } else {
                    print("同步失败, HTTP状态码: \(httpResponse.statusCode)")
                    failCount += 1
                }
            }.resume()
        }
        
        // 所有任务完成后的处理
        group.notify(queue: .main) {
            let totalCount = sleepData.count
            print("睡眠数据同步完成 - 总计: \(totalCount), 成功: \(successCount), 失败: \(failCount)")
            
            // 更新本地同步标记
            UserDefaults.standard.set(Date(), forKey: "lastSyncTime")
        }
    }
    
    func clearResults() {
        testResults = []
        resultColors = [:]
    }
    
    func addResult(_ text: String, color: Color = .primary) {
        DispatchQueue.main.async {
            let index = self.testResults.count
            self.testResults.append(text)
            self.resultColors[index] = color
        }
    }
    
    func setResultColor(index: Int, color: Color) {
        DispatchQueue.main.async {
            self.resultColors[index] = color
        }
    }
    
    // MARK: - 同步方法
    
    /// 将睡眠数据同步到服务器
    /// - Parameters:
    ///   - sleepData: 从设备获取的睡眠数据
    ///   - day: 数据日期（0=今天, 1=昨天, 以此类推）
    func syncSleepDataToServer(sleepData: CRPSleepModel, day: Int) {
        let testIndex = testResults.count
        addResult("开始同步睡眠数据到服务器...", color: .blue)
        
        // 1. 将数据保存到本地数据库
        let userId = "user1" // 实际应用中应从用户服务获取
        let today = Date()
        let calendar = Calendar.current
        let dataDate = calendar.date(byAdding: .day, value: -day, to: today) ?? today
        
        // 创建日期字符串（格式：yyyy-MM-dd）
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: dataDate)
        
        // 2. 保存到本地数据库
        let sleepDetails = convertSleepDetails(from: sleepData.detail)
        let success = healthDataManager.addSleep(
            userId: userId,
            startTime: dataDate, // 实际应用中应使用真实的睡眠开始时间
            endTime: Date(), // 实际应用中应使用真实的睡眠结束时间
            totalMinutes: Int16(sleepData.deep + sleepData.light + sleepData.rem),
            deepMinutes: Int16(sleepData.deep),
            lightMinutes: Int16(sleepData.light),
            remMinutes: Int16(sleepData.rem),
            awakeMinutes: 0, // 从设备获取或计算
            score: 0, // 从设备获取或计算
            efficiency: 0, // 从设备获取或计算
            deviceId: deviceService.deviceInfo?.localName ?? "Unknown",
            sleepStages: sleepDetails
        )
        
        if success {
            addResult("✅ 睡眠数据已保存到本地数据库", color: .green)
        } else {
            addResult("❌ 睡眠数据保存到本地数据库失败", color: .red)
            return
        }
        
        // 3. 同步到远程服务器
        // 构建请求数据
        let requestData: [String: Any] = [
            "date": dateString,
            "deep": sleepData.deep,
            "light": sleepData.light,
            "rem": sleepData.rem,
            "records": convertRecordsToJSON(from: sleepData.detail)
        ]
        
        // 请求头部
        let headers = [
            "Content-Type": "application/json",
            "tenant-id": "1",
            "Authorization": "Bearer \(AuthService.shared.currentToken?.accessToken ?? "")"
        ]
        
        // 构建URL
        guard let url = URL(string: "https://yapi.weaving-park.com/mock/434/app-api/iot/sleep/upload/sleep/data") else {
            addResult("❌ 无效的API URL", color: .red)
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        for (key, value) in headers {
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestData)
        } catch {
            addResult("❌ 请求数据序列化失败: \(error.localizedDescription)", color: .red)
            return
        }
        
        // 执行网络请求
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }
            
            if let error = error {
                self.addResult("❌ 网络请求失败: \(error.localizedDescription)", color: .red)
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                self.addResult("❌ 无效的HTTP响应", color: .red)
                return
            }
            
            if (200...299).contains(httpResponse.statusCode) {
                // 解析响应数据
                if let data = data {
                    do {
                        if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                            print("睡眠数据同步响应: \(json)")
                            self.addResult("✅ 睡眠数据成功同步到服务器", color: .green)
                            self.addResult("  日期: \(dateString)", color: .green)
                        }
                    } catch {
                        self.addResult("⚠️ 响应解析失败: \(error.localizedDescription)", color: .orange)
                    }
                }
            } else {
                self.addResult("❌ 同步失败, HTTP状态码: \(httpResponse.statusCode)", color: .red)
            }
        }.resume()
    }
    
    /// 转换睡眠阶段详情为本地数据库格式
    private func convertSleepDetails(from details: [[String: String]]) -> [(type: String, startTime: Date, duration: Int16)] {
        var result: [(type: String, startTime: Date, duration: Int16)] = []
        
        for detail in details {
            if let typeString = detail["type"],
               let startTimeString = detail["start"],
               let endTimeString = detail["end"],
               let type = Int(typeString),
               let startTime = TimeInterval(startTimeString),
               let endTime = TimeInterval(endTimeString) {
                
                let typeText: String
                switch type {
                case 0: typeText = "awake"
                case 1: typeText = "light"
                case 2: typeText = "deep"
                case 3: typeText = "rem"
                default: typeText = "unknown"
                }
                
                let duration = Int16(endTime - startTime) / 60 // 转换为分钟
                result.append((
                    type: typeText,
                    startTime: Date(timeIntervalSince1970: startTime),
                    duration: duration
                ))
            }
        }
        
        return result
    }
    
    /// 转换睡眠详情记录为JSON格式
    private func convertRecordsToJSON(from details: [[String: String]]) -> [[String: Any]] {
        var result: [[String: Any]] = []
        
        for detail in details {
            if let typeString = detail["type"],
               let startTimeString = detail["start"],
               let endTimeString = detail["end"],
               let type = Int(typeString),
               let startTime = TimeInterval(startTimeString),
               let endTime = TimeInterval(endTimeString) {
                
                let duration = Int(endTime - startTime) / 60 // 转换为分钟
                
                var record: [String: Any] = [
                    "type": type,
                    "start_time": Int(startTime),
                    "end_time": Int(endTime),
                    "duration": duration
                ]
                
                result.append(record)
            }
        }
        
        return result
    }
    
    /// 同步所有本地睡眠数据到服务器
    func syncAllSleepDataToServer() {
        let testIndex = testResults.count
        addResult("开始同步所有本地睡眠数据到服务器...", color: .blue)
        
        let userId = "user1" // 实际应用中应从用户服务获取
        let startDate = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date() // 最近30天
        
        // 从本地数据库获取所有睡眠数据
        let sleepData = healthDataManager.getSleep(userId: userId, startDate: startDate, endDate: Date())
        
        if sleepData.isEmpty {
            addResult("⚠️ 本地数据库无睡眠数据可同步", color: .orange)
            return
        }
        
        addResult("找到 \(sleepData.count) 条本地睡眠数据记录", color: .blue)
        
        // 创建同步任务组
        let group = DispatchGroup()
        var successCount = 0
        var failCount = 0
        
        // 为每条数据创建同步任务
        for (index, sleepEntity) in sleepData.enumerated() {
            group.enter()
            
            // 获取日期字符串
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: sleepEntity.startTime ?? Date())
            
            // 获取睡眠阶段数据
            let sleepStages = healthDataManager.getSleepStages(sleepId: sleepEntity.id!)
            
            // 转换睡眠阶段为JSON
            var records: [[String: Any]] = []
            for stage in sleepStages {
                if let startTime = stage.startTime, let type = stage.type {
                    // 计算结束时间
                    let endTime = startTime.addingTimeInterval(TimeInterval(stage.duration * 60))
                    
                    // 转换类型
                    let typeValue: Int
                    switch type {
                    case "awake": typeValue = 0
                    case "light": typeValue = 1
                    case "deep": typeValue = 2
                    case "rem": typeValue = 3
                    default: typeValue = 0
                    }
                    
                    let record: [String: Any] = [
                        "type": typeValue,
                        "start_time": Int(startTime.timeIntervalSince1970),
                        "end_time": Int(endTime.timeIntervalSince1970),
                        "duration": Int(stage.duration)
                    ]
                    
                    records.append(record)
                }
            }
            
            // 构建请求数据
            let requestData: [String: Any] = [
                "date": dateString,
                "deep": sleepEntity.deepMinutes ?? 0,
                "light": sleepEntity.lightMinutes ?? 0,
                "rem": sleepEntity.remMinutes ?? 0,
                "records": records
            ]
            
            // 请求头部
            let headers = [
                "Content-Type": "application/json",
                "tenant-id": "1",
                "Authorization": "Bearer \(AuthService.shared.currentToken?.accessToken ?? "")"
            ]
            
            // 构建URL
            guard let url = URL(string: "https://yapi.weaving-park.com/mock/434/app-api/iot/sleep/upload/sleep/data") else {
                addResult("❌ 无效的API URL", color: .red)
                group.leave()
                continue
            }
            
            // 创建请求
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            for (key, value) in headers {
                request.setValue(value, forHTTPHeaderField: key)
            }
            
            do {
                request.httpBody = try JSONSerialization.data(withJSONObject: requestData)
            } catch {
                print("请求数据序列化失败: \(error.localizedDescription)")
                failCount += 1
                group.leave()
                continue
            }
            
            // 执行网络请求
            URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
                defer { group.leave() }
                guard let self = self else { return }
                
                if let error = error {
                    print("网络请求失败: \(error.localizedDescription)")
                    failCount += 1
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("无效的HTTP响应")
                    failCount += 1
                    return
                }
                
                if (200...299).contains(httpResponse.statusCode) {
                    // 解析响应数据
                    if let data = data {
                        do {
                            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                                print("第\(index+1)条睡眠数据同步响应: \(json)")
                                successCount += 1
                            }
                        } catch {
                            print("响应解析失败: \(error.localizedDescription)")
                            failCount += 1
                        }
                    }
                } else {
                    print("同步失败, HTTP状态码: \(httpResponse.statusCode)")
                    failCount += 1
                }
            }.resume()
        }
        
        // 所有任务完成后的处理
        group.notify(queue: .main) { [weak self] in
            guard let self = self else { return }
            
            let totalCount = sleepData.count
            self.addResult("✅ 睡眠数据同步完成", color: .green)
            self.addResult("  总计: \(totalCount) 条数据", color: .green)
            self.addResult("  成功: \(successCount) 条", color: .green)
            
            if failCount > 0 {
                self.addResult("  失败: \(failCount) 条", color: .red)
            }
            
            // 更新本地同步标记
            UserDefaults.standard.set(Date(), forKey: "lastSyncTime")
        }
    }
    
    // MARK: - 测试方法
    
    /// 测试获取睡眠数据
    func testGetSleepData(day: Int) {
        let testIndex = testResults.count
        addResult("开始测试获取睡眠数据 (天数: \(day))...")
        
        // 控制台打印设备信息和连接状态
        print("[睡眠API测试] 设备连接状态: \(deviceService.connectionState)")
        print("[睡眠API测试] 设备信息: \(String(describing: deviceService.deviceInfo))")
        print("[睡眠API测试] 正在调用 getSleepData(\(day))")
        
        // 添加超时处理
        var hasReceived = false
        let timeout: TimeInterval = 5.0 // 5秒超时
        
        // 设置超时定时器
        DispatchQueue.main.asyncAfter(deadline: .now() + timeout) {
            if !hasReceived {
                print("[睡眠API测试] getSleepData(\(day)) 调用超时")
                self.addResult("⚠️ 获取睡眠数据请求超时，未收到响应", color: .orange)
                self.addResult("  可能原因：", color: .orange)
                self.addResult("  1. 设备没有今天的睡眠数据", color: .orange)
                self.addResult("  2. SDK回调未触发", color: .orange)
                self.addResult("  3. 设备连接状态异常", color: .orange)
                self.setResultColor(index: testIndex, color: .orange)
            }
        }
        
        CRPSmartRingSDK.sharedInstance.getSleepData(day) { model, error in
            hasReceived = true
            print("[睡眠API测试] getSleepData(\(day)) 回调返回")
            print("[睡眠API测试] 错误状态: \(error)")
            
            if error == .none {
                // 检查model是否为空或数据是否为零
                let hasSleepData = (model.deep > 0 || model.light > 0 || model.rem > 0 || !model.detail.isEmpty)
                
                // 详细打印模型数据
                print("[睡眠API测试] 成功获取睡眠数据")
                print("[睡眠API测试] 模型类型: \(type(of: model))")
                print("[睡眠API测试] 深睡: \(model.deep)分钟")
                print("[睡眠API测试] 浅睡: \(model.light)分钟")
                print("[睡眠API测试] REM: \(model.rem)分钟")
                print("[睡眠API测试] 总睡眠: \(model.deep + model.light + model.rem)分钟") 
                print("[睡眠API测试] 详情数量: \(model.detail.count)")
                
                if hasSleepData {
                    // 打印详细数据结构
                    if !model.detail.isEmpty {
                        print("[睡眠API测试] 详情第一项: \(model.detail[0])")
                    }
                    
                    self.addResult("✅ 获取睡眠数据成功:", color: .green)
                    self.addResult("- 深睡: \(model.deep)分钟")
                    self.addResult("- 浅睡: \(model.light)分钟")
                    self.addResult("- REM: \(model.rem)分钟")
                    self.addResult("- 总睡眠: \(model.deep + model.light + model.rem)分钟")
                    self.addResult("- 详情数量: \(model.detail.count)")
                    
                    // 创建CRPSleepModel实例用于同步
                    let sleepModel = CRPSleepModel(deep: model.deep, light: model.light, rem: model.rem, detail: model.detail)
                    
                    // 同步数据到服务器
                    self.syncSleepDataToServer(sleepData: sleepModel, day: day)
                    
                    // 打印睡眠详情
                    if !model.detail.isEmpty {
                        self.addResult("睡眠阶段详情:")
                        for (index, detail) in model.detail.enumerated().prefix(5) {
                            if let typeString = detail["type"],
                               let startTimeString = detail["start"],
                               let endTimeString = detail["end"],
                               let type = Int(typeString),
                               let startTime = TimeInterval(startTimeString),
                               let endTime = TimeInterval(endTimeString) {
                                
                                let startDate = Date(timeIntervalSince1970: startTime)
                                let endDate = Date(timeIntervalSince1970: endTime)
                                
                                let dateFormatter = DateFormatter()
                                dateFormatter.dateFormat = "HH:mm:ss"
                                
                                let typeText: String
                                switch type {
                                case 0: typeText = "清醒"
                                case 1: typeText = "浅睡"
                                case 2: typeText = "深睡"
                                case 3: typeText = "REM"
                                default: typeText = "未知"
                                }
                                
                                self.addResult("  \(index). \(typeText) \(dateFormatter.string(from: startDate)) - \(dateFormatter.string(from: endDate))")
                            }
                        }
                        
                        if model.detail.count > 5 {
                            self.addResult("  ...还有\(model.detail.count - 5)个阶段未显示")
                        }
                    }
                } else {
                    // 没有睡眠数据的情况
                    print("[睡眠API测试] 成功获取但无睡眠数据")
                    self.addResult("ℹ️ 当日(\(day == 0 ? "今天" : "\(day)天前"))没有睡眠数据", color: .blue)
                }
            } else {
                print("[睡眠API测试] 获取睡眠数据失败: \(error)")
                self.addResult("❌ 获取睡眠数据失败: \(error)", color: .red)
                self.setResultColor(index: testIndex, color: .red)
            }
        }
    }
    
    /// 测试设置睡眠数据
    func testSetSleepData() {
        let testIndex = testResults.count
        addResult("开始测试设置睡眠数据...")
        
        // 确保使用SDK中实际定义的枚举值
        let sleepDetails = [
            CRPSleepDetailModel(type: .light, hour: 0, minute: 0),  // 浅睡，0:00开始
            CRPSleepDetailModel(type: .deep, hour: 1, minute: 0),   // 深睡，1:00开始
            CRPSleepDetailModel(type: .rem, hour: 2, minute: 0),    // REM，2:00开始
            CRPSleepDetailModel(type: .awake, hour: 3, minute: 0)   // 清醒，3:00开始
        ]
        
        // 方法不会抛出错误，所以不需要使用do-catch
        CRPSmartRingSDK.sharedInstance.setSleepData(data: sleepDetails)
        addResult("✅ 睡眠数据已发送到设备", color: .green)
        
        for (index, detail) in sleepDetails.enumerated() {
            let typeText: String
            switch detail.type {
            case .awake: typeText = "清醒"
            case .light: typeText = "浅睡"
            case .deep: typeText = "深睡"
            case .rem: typeText = "REM"
            @unknown default: typeText = "未知"
            }
            addResult("- 阶段\(index): \(typeText), 时间: \(detail.hour):\(detail.minute)")
        }
    }
    
    /// 测试获取睡眠温度状态
    func testGetSleepTemperatureState() {
        let testIndex = testResults.count
        addResult("开始测试获取睡眠温度状态...")
        
//        CRPSmartRingSDK.sharedInstance.getSleepTemperatureState { isEnabled, error in
//            if error == .none {
//                self.addResult("✅ 睡眠温度监测状态: \(isEnabled ? "已启用" : "未启用")", color: isEnabled ? .green : .orange)
//            } else {
//                self.addResult("❌ 获取睡眠温度状态失败: \(error)", color: .red)
//                self.setResultColor(index: testIndex, color: .red)
//            }
//        }
    }
    
    /// 测试设置睡眠温度状态
    func testSetSleepTemperatureState(open: Bool) {
        let testIndex = testResults.count
        addResult("开始测试\(open ? "启用" : "禁用")睡眠温度监测...")
        
//        CRPSmartRingSDK.sharedInstance.setSleepTemperatureState(open: open)
        addResult("✅ 已发送\(open ? "启用" : "禁用")睡眠温度监测的请求", color: .green)
    }
    
    /// 测试获取睡眠温度数据
    func testGetSleepTemperatureData(day: Int) {
        let testIndex = testResults.count
        addResult("开始测试获取睡眠温度数据 (天数: \(day))...")
        CRPSmartRingSDK.sharedInstance.getTimingTemperatureData(day: day) { model, error in
            if error == .none {
                self.addResult("✅ 获取睡眠温度数据成功", color: .green)
                // 处理温度数据...
            } else {
                self.addResult("❌ 获取睡眠温度数据失败: \(error)", color: .red)
                self.setResultColor(index: testIndex, color: .red)
            }
        }  //getTimingTemperatureData
//        CRPSmartRingSDK.sharedInstance.getSleepTemperatureData(day: day) { model, error in
//            
//        }
    }
    
    /// 测试获取睡眠血氧支持状态
    func testGetSleepO2SupportState() {
        let testIndex = testResults.count
        addResult("开始测试获取睡眠血氧支持状态...")
        
        CRPSmartRingSDK.sharedInstance.getSleepO2SupportState { state, error in
            if error == .none {
                let supportText = state == 1 ? "支持" : "不支持"
                self.addResult("✅ 睡眠血氧支持状态: \(supportText)", color: state == 1 ? .green : .orange)
            } else {
                self.addResult("❌ 获取睡眠血氧支持状态失败: \(error)", color: .red)
                self.setResultColor(index: testIndex, color: .red)
            }
        }
    }
    
    /// 测试获取GoMore睡眠类型
    func testGetGoMoreSleepType() {
        let testIndex = testResults.count
        addResult("开始测试获取GoMore睡眠类型...")
        
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepType { sleepType, error in
            if error == .none {
                self.addResult("✅ GoMore睡眠类型: \(sleepType)", color: .green)
            } else {
                self.addResult("❌ 获取GoMore睡眠类型失败: \(error)", color: .red)
                self.setResultColor(index: testIndex, color: .red)
            }
        }
    }
    
    /// 测试获取GoMore睡眠数据列表
    func testGetGoMoreSleepDataList() {
        let testIndex = testResults.count
        addResult("开始测试获取GoMore睡眠数据列表...")
        
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataList()
        addResult("✅ 已发送获取GoMore睡眠数据列表请求", color: .green)
        addResult("⚠️ 注意: 结果通过代理回调方法返回，请检查日志", color: .orange)
        
        // 延迟检查属性名称
        let workItem = DispatchWorkItem {
            // 检查SDK中可用的属性
            let mirror = Mirror(reflecting: CRPSmartRingSDK.sharedInstance)
            for (label, _) in mirror.children {
                if let label = label, label.lowercased().contains("sleep") {
                    self.addResult("找到SDK属性: \(label)", color: .blue)
                }
            }
            
            // 尝试访问goMoreSleepIds属性
            let hasSleepIds = mirror.children.contains { label, _ in
                return label?.contains("goMoreSleepIds") ?? false
            }
            
            if hasSleepIds {
                self.addResult("✅ 成功找到goMoreSleepIds属性", color: .green)
            } else {
                self.addResult("❌ 未找到goMoreSleepIds属性", color: .orange)
            }
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 1, execute: workItem)
    }
    
    /// 测试设置GoMore睡眠生成
    func testSetGoMoreSleepGenerate() {
        let testIndex = testResults.count
        addResult("开始测试设置GoMore睡眠生成...")
        
        // 获取当前时间戳
        let now = Date().timeIntervalSince1970
        let yesterdayEvening = now - 24 * 60 * 60 + 22 * 60 * 60 // 昨天晚上10点
        let sleepTime = 7 * 60 // 7小时睡眠（分钟）
        let todayMorning = yesterdayEvening + Double(sleepTime * 60) // 今天早上5点
        
        let startTime = Int(yesterdayEvening)
        let awakeTime = Int(todayMorning)
        
        CRPSmartRingSDK.sharedInstance.setGoMoreSleepGenerate(
            startTime: startTime,
            sleepTime: sleepTime,
            awakeTime: awakeTime
        )
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        let startDate = Date(timeIntervalSince1970: TimeInterval(startTime))
        let endDate = Date(timeIntervalSince1970: TimeInterval(awakeTime))
        
        addResult("✅ 已发送GoMore睡眠生成请求:", color: .green)
        addResult("- 开始时间: \(dateFormatter.string(from: startDate))")
        addResult("- 睡眠时长: \(sleepTime/60)小时\(sleepTime%60)分钟")
        addResult("- 醒来时间: \(dateFormatter.string(from: endDate))")
    }
    
    /// 测试终止睡眠
    func testSetTerminationSleep() {
        let testIndex = testResults.count
        addResult("开始测试终止睡眠...")
        
        CRPSmartRingSDK.sharedInstance.setTerminationSleep { result, error in
            if error == .none {
                if result == 0 {
                    self.addResult("✅ 睡眠终止成功", color: .green)
                } else {
                    self.addResult("⚠️ 终止睡眠返回: \(result)", color: .orange)
                }
            } else {
                self.addResult("❌ 终止睡眠失败: \(error)", color: .red)
                self.setResultColor(index: testIndex, color: .red)
            }
        }
    }
    
    /// 测试获取多天睡眠数据
    func testGetMultiDaySleepData() {
        let testIndex = testResults.count
        addResult("开始获取多天睡眠数据 (0-6天)...")
        
        let daysToFetch = 7
        var completedDays = 0
        var collectedData: [Int: CRPSleepModel] = [:]
        
        func fetchNextDay(day: Int) {
            guard day < daysToFetch else {
                return
            }
            
            CRPSmartRingSDK.sharedInstance.getSleepData(day) { model, error in
                DispatchQueue.main.async {
                    completedDays += 1
                    
                    if error == .none {
                        // 创建CRPSleepModel实例
                        let sleepModel = CRPSleepModel(deep: model.deep, light: model.light, rem: model.rem, detail: model.detail)
                        collectedData[day] = sleepModel
                        self.addResult("✅ 获取第\(day)天睡眠数据成功")
                    } else {
                        self.addResult("❌ 获取第\(day)天睡眠数据失败: \(error)", color: .red)
                    }
                    
                    if completedDays == daysToFetch {
                        self.summarizeMultiDayData(collectedData: collectedData, testIndex: testIndex)
                    }
                }
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                fetchNextDay(day: day + 1)
            }
        }
        
        fetchNextDay(day: 0)
    }
    
    private func summarizeMultiDayData(collectedData: [Int: CRPSleepModel], testIndex: Int) {
        if collectedData.isEmpty {
            addResult("❌ 未能获取任何睡眠数据", color: .red)
                setResultColor(index: testIndex, color: .red)
        } else {
            addResult("✅ 获取到\(collectedData.count)天的睡眠数据:", color: .green)
            
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            
            for (day, data) in collectedData.sorted(by: { $0.key < $1.key }) {
                let date = Date().addingTimeInterval(-Double(day) * 24 * 60 * 60)
                let dateString = dateFormatter.string(from: date)
                addResult("- \(dateString): 深睡\(data.deep)分钟, 浅睡\(data.light)分钟, REM\(data.rem)分钟")
                
                // 同步数据到服务器
                syncSleepDataToServer(sleepData: data, day: day)
            }
        }
    }
    
    /// 测试获取前一个月睡眠数据
    func testGetLastMonthSleepData() {
        let testIndex = testResults.count
        addResult("开始获取前一个月睡眠数据...")
        
        let currentDate = Date()
        let calendar = Calendar.current
        
        var dateComponents = DateComponents()
        dateComponents.month = -1
        guard let oneMonthAgo = calendar.date(byAdding: dateComponents, to: currentDate) else {
            addResult("❌ 日期计算错误", color: .red)
            return
        }
        
        let daysBetween = calendar.dateComponents([.day], from: oneMonthAgo, to: currentDate).day ?? 30
        let daysToFetch = min(daysBetween, 30)
        
        addResult("将获取最近 \(daysToFetch) 天的睡眠数据...")
        
        let sampleDays = [0, 7, 14, 21, 28].filter { $0 < daysToFetch }
        addResult("先尝试获取关键日期的数据: \(sampleDays.map { "\($0)天前" }.joined(separator: ", "))")
        
        var fetchIndex = 0
        var collectedData: [Int: CRPSleepModel] = [:]
        
        func fetchNextSampleDay() {
            guard fetchIndex < sampleDays.count else {
                summarizeMonthData(collectedData: collectedData)
                return
            }
            
            let day = sampleDays[fetchIndex]
            fetchIndex += 1
            
            CRPSmartRingSDK.sharedInstance.getSleepData(day) { model, error in
                    if error == .none {
                    let hasSleepData = (model.deep > 0 || model.light > 0 || model.rem > 0)
                        if hasSleepData {
                            // 创建CRPSleepModel实例
                            let sleepModel = CRPSleepModel(deep: model.deep, light: model.light, rem: model.rem, detail: model.detail)
                            collectedData[day] = sleepModel
                        self.addResult("✅ 获取\(day)天前睡眠数据成功")
                        } else {
                        self.addResult("ℹ️ \(day)天前没有睡眠数据", color: .blue)
                        }
                    } else {
                    self.addResult("❌ 获取\(day)天前睡眠数据失败: \(error)", color: .red)
                    }
                    
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    fetchNextSampleDay()
                }
            }
        }
        
        fetchNextSampleDay()
    }
    
    private func summarizeMonthData(collectedData: [Int: CRPSleepModel]) {
            if collectedData.isEmpty {
                addResult("❌ 未能获取任何睡眠数据", color: .red)
            } else {
                addResult("✅ 成功获取\(collectedData.count)天的睡眠数据:", color: .green)
                
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                
                for (day, data) in collectedData.sorted(by: { $0.key < $1.key }) {
                    let date = Date().addingTimeInterval(-Double(day) * 24 * 60 * 60)
                    let dateString = dateFormatter.string(from: date)
                    addResult("- \(dateString): 深睡\(data.deep)分钟, 浅睡\(data.light)分钟, REM\(data.rem)分钟")
                    
                    // 同步数据到服务器
                    syncSleepDataToServer(sleepData: data, day: day)
                }
            }
        }
    }
    
extension SleepAPITestViewModel: CRPManagerDelegate {
    func didState(_ state: CRPState) {}
    func didBluetoothState(_ state: CRPBluetoothState) {}
    func receiveSteps(_ model: CRPStepModel) {
        print("[睡眠API测试] receiveSteps回调: \(model)")
    }
    func receiveHeartRate(_ heartRate: Int) {
        print("[睡眠API测试] receiveHeartRate回调: \(heartRate)")
    }
    func receiveRealTimeHeartRate(_ heartRate: Int) {}
    func receiveHRV(_ hrv: Int) {}
    func receiveSpO2(_ o2: Int) {}
    func receiveOTA(_ state: CRPOTAState, _ progress: Int) {}
    func receiveStress(_ stress: Int) {}
    
    // 实现睡眠数据列表接收方法
    func receiveSleepList(_ sleepRecords: [CRPGoMoreSleepRecord]) {
        print("[睡眠API测试] receiveSleepList回调: 获取到\(sleepRecords.count)条睡眠记录")
        
        // 在主线程上更新UI
        DispatchQueue.main.async {
            let list = sleepRecords
            
            self.addResult("收到睡眠记录列表回调消息", color: .blue)
            self.addResult("共\(list.count)条睡眠记录", color: .blue)

            // 避免数据为空时出现无意义输出
            guard !list.isEmpty else {
                self.addResult("没有可用的睡眠记录", color: .orange)
                return
            }
            
            // 获取所有睡眠记录的详细信息
            self.addResult("正在获取所有睡眠记录的详细数据...", color: .blue)
            self.fetchDetailedSleepData(for: list)
        }
    }
    
    /// 获取详细的睡眠数据
    private func fetchDetailedSleepData(for records: [CRPGoMoreSleepRecord]) {
        guard !records.isEmpty else { return }
        
        // 添加提示信息，告知用户正在处理的数据量
        if records.count > 5 {
            addResult("⚠️ 正在处理 \(records.count) 条记录，可能需要较长时间，请耐心等待...", color: .orange)
        }
        
        // 检查记录的ID分布情况
        let recordIDs = records.map { $0.id }
        let uniqueIDs = Set(recordIDs)
        
        addResult("收到的睡眠记录ID情况:", color: .blue)
        addResult("- 总记录数: \(records.count)")
        addResult("- 唯一ID数: \(uniqueIDs.count)")
        if uniqueIDs.count < records.count {
            addResult("- ⚠️ 存在重复ID，这可能导致数据混淆", color: .orange)
        }
        
        // 打印每个记录的基本信息
        for (index, record) in records.enumerated() {
            let startDate = Date(timeIntervalSince1970: TimeInterval(record.startTime))
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
            addResult("记录 #\(index+1): ID=\(record.id), 开始时间=\(dateFormatter.string(from: startDate))")
        }
        
        addResult("\n正在尝试逐条获取详细数据...", color: .blue)
        
        // 一次只处理一个记录，使用串行队列避免并发导致的问题
        let queue = DispatchQueue(label: "com.windring.sleepapi.serialqueue")
        var processedCount = 0
        var sleepDetails: [Int: CRPGoMoreSleepDataModel] = [:]
        var sleepSegmentations: [Int: CRPSleepModel] = [:]
        var sleepTypes: [CRPGoMoreSleepType] = []
        var errors: [Int: CRPError] = [:]
        
        // 创建一个组来等待所有处理完成
        let group = DispatchGroup()
        
        // 选择几个关键记录进行测试，避免处理全部30条造成UI卡顿
        let samplesToProcess = records.count <= 5 ? records : [
            records.first!,                      // 第一条
            records[records.count / 4],          // 25%位置
            records[records.count / 2],          // 中间位置
            records[records.count * 3 / 4],      // 75%位置
            records.last!                        // 最后一条
        ]
        
        addResult("为了避免大量数据处理，将只处理 \(samplesToProcess.count) 条关键记录", color: .blue)
        
        // 处理选定的记录
        for (index, record) in samplesToProcess.enumerated() {
            queue.async(group: group) {
                // 记录开始处理时间
                let startTime = Date()
                print("[睡眠API测试] 开始处理记录 #\(index+1), ID=\(record.id), 时间=\(startTime)")
                
                // 同步获取Sleep Detail数据
                let detailSemaphore = DispatchSemaphore(value: 0)
                
                print("[睡眠API测试] 正在请求ID=\(record.id)的详细睡眠数据...")
                CRPSmartRingSDK.sharedInstance.getGoMoreSleepDataDetail(id: record.id) { sleepData, error in
                    if error == .none {
                        print("[睡眠API测试] 成功获取ID=\(record.id)的详细睡眠数据")
                        print("[睡眠API测试] 睡眠类型: \(sleepData.type), 效率: \(sleepData.sleepEfficiency), 得分: \(sleepData.sleepScore)")
                        sleepDetails[index] = sleepData
        } else {
                        print("[睡眠API测试] 获取ID=\(record.id)的详细睡眠数据失败: \(error)")
                        errors[index] = error
                    }
                    detailSemaphore.signal()
                }
                detailSemaphore.wait()
                
                // 适当延迟，避免连续请求导致设备或SDK无法响应
                Thread.sleep(forTimeInterval: 0.5)
                
                // 同步获取Sleep Segmentation数据
                let segmentSemaphore = DispatchSemaphore(value: 0)
                
                print("[睡眠API测试] 正在请求ID=\(record.id)的睡眠分段数据...")
                CRPSmartRingSDK.sharedInstance.getGoMoreSleepSegmentationData(id: record.id) { segmentData, error in
                    if error == .none {
                        print("[睡眠API测试] 成功获取ID=\(record.id)的睡眠分段数据")
                        print("[睡眠API测试] 深睡: \(segmentData.deep)分钟, 浅睡: \(segmentData.light)分钟, REM: \(segmentData.rem)分钟")
                        
                        // 创建CRPSleepModel来保存数据
                        let sleepModel = CRPSleepModel(deep: segmentData.deep, light: segmentData.light, rem: segmentData.rem, detail: [])
                        
                        // 保存转换后的模型
                        sleepSegmentations[index] = sleepModel
                        
                        // 同时同步数据到服务器
                        DispatchQueue.main.async {
                            self.syncSleepDataToServer(sleepData: sleepModel, day: 0) // 使用0作为当天
                        }
                    } else {
                        print("[睡眠API测试] 获取ID=\(record.id)的睡眠分段数据失败: \(error)")
                    }
                    segmentSemaphore.signal()
                }
                segmentSemaphore.wait()
                
                // 适当延迟
                Thread.sleep(forTimeInterval: 0.5)
                
                // 记录完成处理时间
                let endTime = Date()
                let processingTime = endTime.timeIntervalSince(startTime)
                print("[睡眠API测试] 完成处理记录 #\(index+1), ID=\(record.id), 耗时=\(processingTime)秒")
                
                // 更新进度
        DispatchQueue.main.async {
                    processedCount += 1
                    self.addResult("已处理 \(processedCount)/\(samplesToProcess.count) 条记录...", color: .blue)
                }
            }
        }
        
        // 单独获取一次睡眠类型数据
        group.enter()
        CRPSmartRingSDK.sharedInstance.getGoMoreSleepType { sleepType, error in
            if error == .none {
                sleepTypes.append(sleepType)
                print("[睡眠API测试] 获取到睡眠类型数据: type=\(sleepType.type), state=\(sleepType.state)")
            } else {
                print("[睡眠API测试] 获取睡眠类型失败: \(error)")
            }
            group.leave()
        }
        
        // 所有请求完成后处理数据
        group.notify(queue: .main) {
            self.addResult("\n数据获取测试结果:", color: .green)
            self.addResult("1. 成功获取的详细数据: \(sleepDetails.count)条")
            self.addResult("2. 成功获取的分段数据: \(sleepSegmentations.count)条")
            self.addResult("3. 成功获取的睡眠类型: \(sleepTypes.count)条")
            self.addResult("4. 错误数: \(errors.count)条")
            
            // 检查获取到的数据是否都相同
            if sleepDetails.count > 1 {
                let allSleepEfficiencies = sleepDetails.values.map { $0.sleepEfficiency }
                let allSleepScores = sleepDetails.values.map { $0.sleepScore }
                let allTypes = sleepDetails.values.map { $0.type }
                
                let uniqueEfficiencies = Set(allSleepEfficiencies)
                let uniqueScores = Set(allSleepScores)
                let uniqueTypes = Set(allTypes)
                
                if uniqueEfficiencies.count == 1 && uniqueScores.count == 1 && uniqueTypes.count == 1 {
                    self.addResult("\n⚠️ 异常情况: 所有获取到的详细数据完全相同，可能是SDK或设备问题", color: .red)
                } else {
                    self.addResult("\n✅ 正常情况: 获取到的详细数据各不相同", color: .green)
                }
            }
            
            // 显示详细数据
            for (index, record) in samplesToProcess.enumerated() {
                let recordStartDate = Date(timeIntervalSince1970: TimeInterval(record.startTime))
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                
                self.addResult("\n记录 #\(index + 1) (ID: \(record.id)) 详细数据 (\(dateFormatter.string(from: recordStartDate))):", color: .blue)
                
                // 显示详细睡眠数据
                if let sleepDetail = sleepDetails[index] {
                    let startDate = Date(timeIntervalSince1970: TimeInterval(sleepDetail.startTime))
                    let endDate = Date(timeIntervalSince1970: TimeInterval(sleepDetail.endTime))
                    dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                    
                    self.addResult("睡眠详细信息:", color: .green)
                    self.addResult("- 请求的ID: \(record.id) 返回的数据:")
                    self.addResult("- 开始时间: \(dateFormatter.string(from: startDate))")
                    self.addResult("- 结束时间: \(dateFormatter.string(from: endDate))")
                    self.addResult("- 睡眠效率: \(Int(sleepDetail.sleepEfficiency * 100))%")
                    self.addResult("- 睡眠得分: \(Int(sleepDetail.sleepScore))")
                    self.addResult("- 总睡眠时间: \(Int(sleepDetail.totalTime))分钟")
                    
                    // 显示睡眠类型
                    let typeText: String
                    switch sleepDetail.type {
                    case 0: typeText = "短睡眠"
                    case 1: typeText = "长睡眠"
                    default: typeText = "未知类型(\(sleepDetail.type))"
                    }
                    self.addResult("- 睡眠类型: \(typeText)")
                } else {
                    self.addResult("未能获取到睡眠详细信息", color: .orange)
                }
                
                // 显示睡眠分段数据
                if let segmentData = sleepSegmentations[index] {
                    self.addResult("睡眠分段数据:", color: .green)
                    self.addResult("- 请求的ID: \(record.id) 返回的数据:")
                    self.addResult("- 深睡: \(segmentData.deep)分钟")
                    self.addResult("- 浅睡: \(segmentData.light)分钟")
                    self.addResult("- REM: \(segmentData.rem)分钟")
                    self.addResult("- 总睡眠: \(segmentData.deep + segmentData.light + segmentData.rem)分钟")
                } else {
                    self.addResult("未能获取到睡眠分段数据", color: .orange)
                }
            }
            
            // 显示睡眠类型数据
            if !sleepTypes.isEmpty {
                let sleepType = sleepTypes.first!
                self.addResult("\n睡眠类型信息:", color: .green)
                
                // 睡眠状态解释
                let stateText: String
                switch sleepType.state {
                case 0: stateText = "长睡眠数据收集进行中"
                case 1: stateText = "已收集一天长睡眠数据，有基本时间类型指标"
                case 2: stateText = "在一天内收集的长睡眠数据，但有睡眠片段，有基本时间类型指标"
                case 3: stateText = "连续7天收集4天或更多天的长睡眠数据，具有较高置信度"
                case 4: stateText = "连续7天收集长睡眠数据，具有较高置信度"
                case 5: stateText = "连续7天收集长睡眠数据，但置信度较低"
                case 71: stateText = "睡眠时间超过15小时"
                case 72: stateText = "连续7天内未收集长期睡眠数据"
                default: stateText = "未知状态(\(sleepType.state))"
                }
                self.addResult("- 睡眠状态: \(stateText)")
                
                // 睡眠类型解释
                let typeText: String
                switch sleepType.type {
                case 1: typeText = "蜂鸟型"
                case 2: typeText = "猫头鹰型"
                case 3: typeText = "云雀型"
                default: typeText = "未知类型(\(sleepType.type))"
                }
                self.addResult("- 睡眠类型: \(typeText)")
                
                // 可靠性解释
                let reliabilityText = sleepType.reliability == 1 ? "高" : "低"
                self.addResult("- 数据可靠性: \(reliabilityText)")
                
                // 就寝时间
                if sleepType.bedtime > 0 {
                    let bedtimeHour = sleepType.bedtime / 60
                    let bedtimeMinute = sleepType.bedtime % 60
                    self.addResult("- 通常就寝时间: \(bedtimeHour):\(String(format: "%02d", bedtimeMinute))")
                }
            } else {
                self.addResult("未能获取到睡眠类型信息", color: .orange)
            }
            
            // 总结和建议
            self.addResult("\n总结和建议:", color: .blue)
            if uniqueIDs.count < records.count {
                self.addResult("1. SDK返回了多条记录但ID有重复，可能需要通过其他方式区分记录", color: .orange)
            }
            if sleepDetails.values.contains(where: { $0.type == 2 && $0.sleepEfficiency == 0.0 }) {
                self.addResult("2. 部分记录为无效数据（类型=2，效率=0），这可能是SDK的预期行为", color: .orange)
            }
            if sleepDetails.count > 1 && Set(sleepDetails.values.map { $0.sleepEfficiency }).count == 1 {
                self.addResult("3. 所有获取的详细数据完全相同，建议联系SDK提供方确认API使用方式", color: .red)
            }
            self.addResult("4. 建议通过SDK的其他API或文档了解正确的数据获取流程", color: .blue)
        }
    }
}

/// 分享表单
struct SleepActivityViewController: UIViewControllerRepresentable {
    var activityItems: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: nil
        )
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // 不需要更新
    }
}

// MARK: - 预览
struct SleepAPITestView_Previews: PreviewProvider {
    static var previews: some View {
        SleepAPITestView()
    }
} 
