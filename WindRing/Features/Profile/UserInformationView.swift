import SwiftUI
import UIKit

/// 身高单位枚举
enum HeightUnit: String, CaseIterable {
    case metric = "Metric"
    case inch = "inch"
}

/// 体重单位枚举
enum WeightUnit: String, CaseIterable {
    case metric = "Metric"
    case inch = "inch"
}

/// 媒体选择底部弹框组件，可重用于需要照片选择的场景
struct MediaPickerBottomSheet: View {
    // MARK: - 属性
    @Binding var isPresented: Bo<PERSON>
    @Binding var selectedImage: UIKit.UIImage?
    var onCameraSelected: () -> Void
    var onConfirm: (UIKit.UIImage?) -> Void
    
    var body: some View {
        ZStack {
            // 渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(UIColor(red: 0.16, green: 0.19, blue: 0.25, alpha: 1)),
                    Color(UIColor(red: 0.08, green: 0.09, blue: 0.13, alpha: 1))
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 0) {
                // 顶部标题区域
                HStack(alignment: .center, spacing: 10) {
                    // 蓝色竖线装饰
                    Rectangle()
                        .fill(Color(hex: "#0048B5"))
                        .frame(width: 4, height: 12)
                    
                    Text("Select from the album")
                        .font(.custom("PingFang-SC-Medium", size: 14))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // 关闭按钮
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 10)
                .padding(.bottom, 50)
                
                Divider()
                    .background(Color(white: 0.2))
                
                // Camera按钮
                Button(action: {
                    onCameraSelected()
                    isPresented = false
                }) {
                    Text("Camera")
                        .font(.custom("PingFang-SC-Regular", size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 40)
                }
                
                Divider()
                    .background(Color(white: 0.2))
                
                // Cancel按钮
                Button(action: {
                    isPresented = false
                }) {
                    Text("Cancel")
                        .font(.custom("PingFang-SC-Regular", size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 40)
                }
                
                Divider()
                    .background(Color(white: 0.2))
                
                Spacer()
                    .frame(height: 40) // 增加空间高度，使Confirm按钮下移
                
                // Confirm按钮 - 特殊样式
                Button(action: {
                    // 仅当用户选择了新照片时，确认按钮才有效
                    onConfirm(selectedImage)
                    isPresented = false
                }) {
                    Text("confirm".localized)
                        .font(.custom("PingFang-SC-Medium", size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 35)
                        .background(
                            ZStack {
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color(hex: "#0048B5").opacity(0.2))
                                
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                            }
                        )
                        .padding(.horizontal, 20)
                }
                .padding(.bottom, 40)
                .disabled(selectedImage == nil)
            }
        }
        .background(
            // 添加一个圆角矩形作为整个弹框的背景
            RoundedRectangle(cornerRadius: 50)
                .fill(Color.clear)
        )
        .presentationDetents([.height(300)])
        .presentationDragIndicator(.visible)
    }
}

/// 用户名编辑底部弹框组件，可重用于需要编辑用户名的场景
struct UsernameEditorBottomSheet: View {
    // MARK: - 属性
    @Binding var isPresented: Bool
    @Binding var username: String
    @State private var editedUsername: String = ""
    @State private var isUpdating = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    var onConfirm: (String) -> Void
    
    var body: some View {
        ZStack {
            // 渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(UIColor(red: 0.16, green: 0.19, blue: 0.25, alpha: 1)),
                    Color(UIColor(red: 0.08, green: 0.09, blue: 0.13, alpha: 1))
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 0) {
                // 顶部标题区域
                HStack(alignment: .center, spacing: 10) {
                    // 蓝色竖线装饰
                    Rectangle()
                        .fill(Color(hex: "#0048B5"))
                        .frame(width: 4, height: 12)
                    
                    Text("username".localized)
                        .font(.custom("PingFang-SC-Medium", size: 14))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // 关闭按钮
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 10)
                .padding(.bottom, 20)
                
                // 用户名输入框
                TextField("username".localized, text: $editedUsername)
                    .font(.custom("PingFang-SC-Medium", size: 16))
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .frame(height: 35)
                    .background(
                        ZStack {
                            // 外层背景
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color(UIColor(red: 0.26, green: 0.26, blue: 0.26, alpha: 1)))
                            
                            // 内层背景，稍小一点，创建描边效果
                            RoundedRectangle(cornerRadius: 9.5)
                                .inset(by: 0.5)
                                .fill(Color(UIColor(red: 0.03, green: 0.03, blue: 0.03, alpha: 0.50)))
                        }
                    )
                    .padding(.horizontal, 48)
                    .padding(.top, 50)
                    .padding(.bottom, 20)
                    .disabled(isUpdating)
                
                Spacer()
                
                // Confirm按钮 - 特殊样式
                Button(action: {
                    if !editedUsername.isEmpty {
                        onConfirm(editedUsername)
                    }
                    isPresented = false
                }) {
                    Text("confirm".localized)
                        .font(.custom("PingFang-SC-Medium", size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 35)
                        .background(
                            ZStack {
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color(hex: "#0048B5").opacity(0.2))
                                
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                            }
                        )
                        .padding(.horizontal, 20)
                }
                .padding(.bottom, 40)
                .disabled(editedUsername.isEmpty || isUpdating)
            }
        }
        .background(
            // 添加一个圆角矩形作为整个弹框的背景
            RoundedRectangle(cornerRadius: 50)
                .fill(Color.clear)
        )
        .presentationDetents([.height(300)])
        .presentationDragIndicator(.visible)
        .onAppear {
            editedUsername = username
        }
        .alert(isPresented: $showErrorAlert) {
            Alert(
                title: Text("Error"),
                message: Text(errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .overlay(
            Group {
                if isUpdating {
                    Color.black.opacity(0.3)
                    
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                }
            }
        )
    }
}

/// 用户信息详情页
struct UserInformationView: View {
    // MARK: - 属性
    @AppStorage("isLoggedIn") private var isLoggedIn: Bool = false
    @AppStorage("userMobile") private var userMobile: String = ""
    @AppStorage("userEmail") private var userEmail: String = ""
    
    // 用户个人信息 - 改为 @State
    @State private var userName: String = "Test User"
    @State private var userId: String = "King88888888"
    @State private var userBirthday: String = ""
    @State private var userGender: String = ""
    @State private var userHeight: String = ""
    @State private var userWeight: String = ""
    
    @Environment(\.presentationMode) var presentationMode
    @ObservedObject private var authService = AuthService.shared

    
    @State private var isLoading = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    
    @State private var showProfilePictureOptions = false
    @State private var showImagePicker = false
    @State private var imageSource: ProfileImagePicker.ImageSource = .photoLibrary
    @State private var userProfileImage: UIKit.UIImage?
    @State private var navigateToProfilePicture = false
    @State private var showBirthdayPicker = false
    @State private var showGenderPicker = false
    @State private var selectedGender: String = ""
    @State private var showHeightPicker = false
    @State private var heightUnit: HeightUnit = .metric
    @State private var showWeightPicker = false
    @State private var weightUnit: WeightUnit = .metric
    @State private var showUsernameEditor = false
    @State private var editedUsername: String = ""
    
    @Namespace private var unitNamespace
    
    private var formattedHeight: String {
        guard !userHeight.isEmpty, let heightCm = Double(userHeight) else {
            return "not_set".localized
        }

        if heightUnit == .metric {
            return "\(Int(heightCm)) " + "unit_cm".localized
        } else {
            // cm to inches, then to ft and in
            let totalInches = heightCm /// 2.54
            let feet = Int(totalInches / 12)
            let inches = Int(round(totalInches.truncatingRemainder(dividingBy: 12)))
            return "\(feet) " + "unit_ft".localized + "\(inches) " + "unit_in".localized
        }
    }
    
    private var formattedWeight: String {
        guard !userWeight.isEmpty, let weightKg = Double(userWeight) else {
            return "not_set".localized
        }

        if weightUnit == .metric {
            return "\(Int(weightKg)) " + "unit_kg".localized
        } else {
            // kg to lbs
            let weightLbs = weightKg //* 2.20462
            return "\(Int(round(weightLbs))) " + "unit_lbs".localized
        }
    }
    
    // MARK: - 主视图
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色 - 使用深色背景
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            // 使用封装的光晕背景组件
            CornerGlowBackground()
            
            VStack(spacing: 0) {
                // 自定义导航栏 - 与GoalSettingScreen一致，固定在顶部
                HStack(alignment: .center) {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    // 标题居左，也可点击返回
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("user_information".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
                            .padding(.leading, 5)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .frame(height: 44) // 标准iOS导航栏高度
                .padding(.top, 8)
                
                // 可滚动内容区域
                ScrollView {
                    VStack(spacing: 20) {
                        // 头像部分
                        VStack(spacing: 8) {
                            // 移除外部的Button，使头像本身不可点击
                            ZStack {
                                if let image = userProfileImage {
                                    Image(uiImage: image)
                                        .resizable()
                                        .scaledToFill()
                                        .frame(width: 74.5, height: 74.5)
                                        .clipShape(Circle())
                                } else {
                                    Circle()
                                        .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
                                        .frame(width: 74.5, height: 74.5)
                                    Image(systemName: "person.fill")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 37, height: 37)
                                        .foregroundColor(.gray)
                                }
                            }
                            .overlay(
                                // 仅编辑图标可点击
                                Button(action: {
                                    showProfilePictureOptions = true
                                }) {
                                    Circle()
                                        .fill(Color(white: 0.2))
                                        .frame(width: 24, height: 24)
                                        .overlay(
                                            Image(systemName: "pencil")
                                                .font(.system(size: 12))
                                                .foregroundColor(.white)
                                        )
                                        .shadow(color: Color.black.opacity(0.3), radius: 2, x: 0, y: 1)
                                }
                                .buttonStyle(ScaleButtonStyle()),
                                alignment: .bottomTrailing
                            )
                            .padding(.top, 5)
                            
                            Text("profile_picture".localized)
                                .font(.custom("PingFang-SC-Regular", size: 13))
                                .foregroundColor(.white)
                                .frame(height: 10)
                        }
                        .padding(.top, 10) // 增加头像与导航栏之间的间距
                        
                        // 信息卡片
                        VStack(spacing: 0) {
                            // ID信息
                            infoRow(titleKey: "id", value: userId,isRight: false)
                            
                            Divider()
                                .background(Color(white: 0.2))
                                .padding(.vertical, 1)
                                .padding(.horizontal, 16)
                            
                            // 用户名
                            infoRow(titleKey: "username", value: userName)
                            
                            Divider()
                                .background(Color(white: 0.2))
                                .padding(.vertical, 1)
                                .padding(.horizontal, 16)
                            
                            // 生日
                            infoRow(titleKey: "birthday", value: userBirthday.isEmpty ? "not_set".localized : userBirthday)
                            
                            Divider()
                                .background(Color(white: 0.2))
                                .padding(.vertical, 1)
                                .padding(.horizontal, 16)
                            
                            // 性别
                            infoRow(titleKey: "gender", value: userGender.isEmpty ? "secrecy".localized : userGender.localized)
                            
                            Divider()
                                .background(Color(white: 0.2))
                                .padding(.vertical, 1)
                                .padding(.horizontal, 16)
                            
                            // 身高
                            infoRow(titleKey: "height", value: formattedHeight)
                            
                            Divider()
                                .background(Color(white: 0.2))
                                .padding(.vertical, 1)
                                .padding(.horizontal, 16)
                            
                            // 体重
                            infoRow(titleKey: "weight", value: formattedWeight)
                            
                            Divider()
                                .background(Color(white: 0.2))
                                .padding(.vertical, 1)
                                .padding(.horizontal, 16)
                            
                            // 退出登录按钮 - 移到卡片内部
                            Button(action: logout) {
                                HStack {
                                    Spacer()
                                    Text("log_out".localized)
                                        .font(.system(size: 17))
                                        .fontWeight(.medium)
                                        .foregroundColor(.white)
                                    Spacer()
                                }
                                .frame(width: 335, height: 35)
                                .background(
                                    ZStack {
                                        RoundedRectangle(cornerRadius: 10)
                                            .fill(Color(red: 0, green: 0.28, blue: 0.71).opacity(0.1))
                                        
                                        // 添加内部边框层
                                        RoundedRectangle(cornerRadius: 10)
                                            .inset(by: 0.5)
                                            .stroke(Color(red: 0, green: 0.28, blue: 0.71), lineWidth: 0.5)
                                    }
                                )
                                .padding(.horizontal, 20)
                                .padding(.vertical, 15)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        .padding(.vertical, 5)
                        .background(Color(red: 0.06, green: 0.07, blue: 0.1)) // 匹配提供的UIColor
                        .cornerRadius(25)
                        .padding(.horizontal, 10) // 匹配提供的x位置
                        
                        // 底部说明文字
                        HStack(alignment: .top, spacing: 10) {
                            Image("q_1")
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 20, height: 20)
                            
                            Text("health_data_explanation".localized)
                                .font(.custom("PingFang-SC-Medium", size: 13))
                                .foregroundColor(.white)
                                .fixedSize(horizontal: false, vertical: true)
                                .multilineTextAlignment(.leading)
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 10)
                        .padding(.bottom, 30) // 增加底部间距，确保滚动时底部内容可完全显示
                    }
                }
                .scrollIndicators(.hidden) // 隐藏滚动指示器
            }
        }
        .navigationBarHidden(true) // 隐藏系统导航栏，使用自定义导航栏
        
        // 各种sheet和弹出窗口
        .sheet(isPresented: $showImagePicker) {
            ProfileImagePicker(imageSource: imageSource) { image in
                if let image = image {
                    self.userProfileImage = image // 预览
                    uploadUserAvatar(image)      // 上传
                }
                showImagePicker = false
            }
        }
        .sheet(isPresented: $showProfilePictureOptions) {
            MediaPickerBottomSheet(
                isPresented: $showProfilePictureOptions,
                selectedImage: $userProfileImage,
                onCameraSelected: {
                    imageSource = .camera
                    showImagePicker = true
                },
                onConfirm: { image in
                    if let image = image {
                        uploadUserAvatar(image)
                    }
                }
            )
        }
        .sheet(isPresented: $showUsernameEditor) {
            UsernameEditorBottomSheet(
                isPresented: $showUsernameEditor,
                username: $userName,
                onConfirm: { newUsername in
                    updateUsername(newUsername)
                }
            )
        }
        .sheet(isPresented: $showBirthdayPicker) {
            BirthdayPickerView(
                isPresented: $showBirthdayPicker,
                userBirthday: $userBirthday
            )
            .presentationDetents([.height(350)])
            .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $showHeightPicker) {
            HeightPickerView(
                isPresented: $showHeightPicker,
                userHeight: $userHeight,
                heightUnit: $heightUnit
            )
            .presentationDetents([.height(420)])
            .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $showGenderPicker) {
            GenderPickerView(
                isPresented: $showGenderPicker,
                userGender: $userGender,
                selectedGender: $selectedGender
            )
            .presentationDetents([.height(350)])
            .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $showWeightPicker) {
            WeightPickerView(
                isPresented: $showWeightPicker,
                userWeight: $userWeight,
                weightUnit: $weightUnit
            )
            .presentationDetents([.height(420)])
            .presentationDragIndicator(.visible)
        }
        .onAppear {
            // 获取用户信息
            loadUserInfo()
            
            // 加载用户头像
            if let user = authService.currentUser {
                loadUserAvatar(from: user.avatar)
            } else if let avatarUrl = UserDefaults.standard.string(forKey: "userAvatarUrl"), !avatarUrl.isEmpty {
                loadUserAvatar(from: avatarUrl)
            }
        }
        .alert(isPresented: $showErrorAlert) {
            Alert(
                title: Text("Error"),
                message: Text(errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .overlay(
            Group {
                if isLoading {
                    Color.black.opacity(0.3)
                        .edgesIgnoringSafeArea(.all)
                    
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                }
            }
        )
    }
    
    // 信息行组件
    private func infoRow(titleKey: String, value: String,isRight:Bool = true) -> some View {
        Button(action: {
            handleInfoRowTap(titleKey: titleKey, value: value)
        }) {
            HStack(spacing: 12) {
                // 根据不同的title选择不同的图像资源
                getRowIcon(for: titleKey)
                    .frame(width: 24, height: 24)
                
                Text(titleKey.localized)
                    .font(.custom("PingFang-SC-Medium", size: 17)) // 使用PingFang字体
                    .foregroundColor(.white)
                
                Spacer()
                
                Text(value)
                    .font(.custom("PingFang-SC-Medium", size: 17)) // 使用PingFang字体
                    .foregroundColor(Color(white: 0.6))
                if isRight {
                    // 使用项目中的right图像资源替换系统图标
                    Image("right")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 14, height: 14)
                }
                
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 17) // 增加行高
        }
    }
    
    // 根据不同的行标题返回不同的图标
    private func getRowIcon(for titleKey: String) -> AnyView {
        switch titleKey {
        case "id":
            return AnyView(
                Image("v_1")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            )
        case "username":
            return AnyView(
                Image("v_2")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            )
        case "birthday":
            return AnyView(
                Image("v_3")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            )
        case "gender":
            return AnyView(
                Image("v_4")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            )
        case "height":
            return AnyView(
                Image("v_5")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            )
        case "weight":
            return AnyView(
                Image("v_6")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            )
        default:
            return AnyView(
                Image(systemName: "circle.fill")
                    .font(.system(size: 18))
                    .foregroundColor(.gray)
            )
        }
    }
    
    private func handleInfoRowTap(titleKey: String, value: String) {
        switch titleKey {
        case "id":
            print("ID row clicked: \(value)")
        case "username":
            showUsernameEditor = true
            editedUsername = userName
        case "birthday":
            showBirthdayPicker = true
        case "gender":
            showGenderPicker = true
            selectedGender = userGender
        case "height":
            showHeightPicker = true
        case "weight":
            showWeightPicker = true
        default:
            break
        }
    }
    
    // MARK: - 退出登录
    private func logout() {
        // 显示确认对话框
        let alert = UIAlertController(
            title: "log_out".localized,
            message: "log_out_confirm".localized,
            preferredStyle: .alert
        )
        
        // 取消按钮
        alert.addAction(UIAlertAction(
            title: "cancel".localized,
            style: .cancel,
            handler: nil
        ))
        
        // 确认按钮
        alert.addAction(UIAlertAction(
            title: "confirm".localized,
            style: .destructive,
            handler: { _ in
                // 调用AuthService的logout方法
                AuthService.shared.logout { success in
                    DispatchQueue.main.async {
                        if success {
                            print("退出登录成功")
                            // 清除本地保存的用户信息
                            self.isLoggedIn = false
                            self.userName = "游客"
                            // 退出登录成功后返回上一页
//                            LoginStateChanged
                            
//                            self.presentationMode.wrappedValue.dismiss()
                        } else {
                            print("退出登录失败")
                        }
                    }
                }
            }
        ))
        
        // 显示对话框
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            rootViewController.present(alert, animated: true, completion: nil)
        }
    }
    
    // MARK: - 加载用户信息
    private func loadUserInfo() {
        // 1. 优先从Keychain加载数据显示，提供即时反馈
        if let storedUser = KeychainHelper.shared.loadUserInfo() {
            updateView(with: storedUser)
        }

        // 2. 然后从网络获取最新数据
        isLoading = true
        authService.getUserInfo { result in
            isLoading = false
            switch result {
            case .success(let latestUserInfo):
                // 将最新信息保存到Keychain
                KeychainHelper.shared.saveUserInfo(latestUserInfo)
                // 使用最新信息更新视图
                updateView(with: latestUserInfo)
                
            case .failure(let error):
                print("用户信息网络加载失败: \(error.localizedDescription)")
                // 如果网络失败，我们已经从Keychain加载了数据，可以暂时容错
                // 或者显示一个非阻塞的错误提示
                // errorMessage = "Failed to load user information: \(error.localizedDescription)"
                // showErrorAlert = true
            }
        }
    }
    
    // MARK: - 使用UserInfo模型更新视图状态
    private func updateView(with userInfo: UserInfo) {
        self.userName = userInfo.nickname ?? "Test User"
        self.userId = userInfo.id ?? ""

        if let birthdayTimestamp = userInfo.birthday {
            let date = Date(timeIntervalSince1970: TimeInterval(birthdayTimestamp) / 1000)
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            self.userBirthday = formatter.string(from: date)
        } else {
            self.userBirthday = ""
        }

        switch userInfo.sex {
        case 1:
            userGender = "male"
        case 2:
            userGender = "female"
        default:
            userGender = "secrecy"
        }

        if let heightType = userInfo.heightType {
            self.heightUnit = (heightType == 1) ? .metric : .inch
        } else {
            heightUnit = .metric
        }
        
        if let height = userInfo.height {
            self.userHeight = "\(height)"
        } else {
            self.userHeight = ""
        }

        if let weightType = userInfo.weightType {
            self.weightUnit = (weightType == 1) ? .metric : .inch
        } else {
            weightUnit = .metric
        }

        if let weight = userInfo.weight {
            self.userWeight = "\(weight)"
        } else {
            self.userWeight = ""
        }
        let avatarUrl = userInfo.avatar
        if !avatarUrl.isEmpty {
            loadUserAvatar(from: avatarUrl)
        }
    }
    
    // MARK: - 加载用户头像
    private func loadUserAvatar(from urlString: String) {
        guard let url = URL(string: urlString) else {
            print("无效的头像URL: \(urlString)")
            return
        }
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                print("加载头像失败: \(error.localizedDescription)")
                return
            }
            
            guard let data = data, let image = UIKit.UIImage(data: data) else {
                print("无法将数据转换为图像")
                return
            }
            
            DispatchQueue.main.async {
                self.userProfileImage = image
            }
        }.resume()
    }
    
    // MARK: - 上传用户头像
    private func uploadUserAvatar(_ image: UIKit.UIImage) {
        // 1. 首先显示上传中状态
        isLoading = true
        
        // 2. 使用AuthService上传头像
        AuthService.shared.uploadAvatar(image: image) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                
                switch result {
                case .success(let urls):
                    print("头像上传成功")
                    if let imageUrl = urls.first {
                        let updateRequest = UpdateUserInfoRequest(avatar: imageUrl)
                        
                        self.authService.updateUserInfo(userInfo: updateRequest) { updateResult in
                            self.isLoading = false
                            switch updateResult {
                            case .success(_):
                                self.loadUserAvatar(from: imageUrl)
                                
                                // 更新并保存到Keychain
                                if var userInfo = KeychainHelper.shared.loadUserInfo() {
                                    userInfo.avatar = imageUrl
                                    KeychainHelper.shared.saveUserInfo(userInfo)
                                }
                                
                                NotificationCenter.default.post(name: NSNotification.Name("UserAvatarUpdated"), object: nil)
                                
                                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                   let rootViewController = windowScene.windows.first?.rootViewController {
                                    let alert = UIAlertController(
                                        title: "头像更新成功",
                                        message: "您的头像已成功更新",
                                        preferredStyle: .alert
                                    )
                                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                                    rootViewController.present(alert, animated: true)
                                }
                                
                            case .failure(let error):
                                self.errorMessage = "Failed to update avatar URL: \(error.localizedDescription)"
                                self.showErrorAlert = true
                            }
                        }
                    } else {
                        self.isLoading = false
                    }
                    
                case .failure(let error):
                    self.isLoading = false
                    print("头像上传失败: \(error.localizedDescription)")
                    self.errorMessage = "头像上传失败: \(error.localizedDescription)"
                    self.showErrorAlert = true
                }
            }
        }
    }
    
    // 更新用户名方法
    private func updateUsername(_ newUsername: String) {
        if newUsername.isEmpty || newUsername == userName {
            return
        }
        
        isLoading = true
        
        let updateRequest = UpdateUserInfoRequest(nickname: newUsername)
        
        authService.updateUserInfo(userInfo: updateRequest) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                
                switch result {
                case .success(_):
                    // 更新成功
                    self.userName = newUsername
                    
                    // 更新并保存到Keychain
                    if var userInfo = KeychainHelper.shared.loadUserInfo() {
                        userInfo.nickname = newUsername
                        KeychainHelper.shared.saveUserInfo(userInfo)
                    }
                    
                    // 显示成功提示
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootViewController = windowScene.windows.first?.rootViewController {
                        let alert = UIAlertController(
                            title: "modified_successfully".localized,
                            message: nil,
                            preferredStyle: .alert
                        )
                        alert.addAction(UIAlertAction(title: "ok".localized, style: .default))
                        rootViewController.present(alert, animated: true)
                    }
                    
                case .failure(let error):
                    // 更新失败
                    self.errorMessage = "Failed to update username: \(error.localizedDescription)"
                    self.showErrorAlert = true
                }
            }
        }
    }
}

// MARK: - 子页面视图
struct ProfilePictureView: View {
    @Binding var profileImage: UIImage?
    var onImageUpdated: (UIImage) -> Void

    @State private var showImagePicker = false
    @State private var imageSource: ProfileImagePicker.ImageSource = .photoLibrary
    
    var body: some View {
        VStack(spacing: 20) {
            Text("选择您的头像")
                .font(.headline)
                .padding(.top)
            
            ZStack {
                if let image = profileImage {
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 150, height: 150)
                        .clipShape(Circle())
                } else {
                    Circle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 150, height: 150)
                    
                    Image(systemName: "camera.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.gray)
                }
            }
            .padding()
            
            Button(action: {
                showImagePicker = true
            }) {
                Text("更换头像")
                    .foregroundColor(.white)
                    .padding()
                    .frame(width: 200)
                    .background(Color.blue)
                    .cornerRadius(10)
            }
            .padding(.bottom)
            
            Spacer()
        }
        .sheet(isPresented: $showImagePicker) {
            ProfileImagePicker(imageSource: imageSource) { image in
                if let image = image {
                    self.profileImage = image // 预览
                    self.onImageUpdated(image)      // 通过闭包返回并上传
                }
                showImagePicker = false
            }
        }
        .navigationBarTitle("profile_picture".localized, displayMode: .inline)
    }
}

// MARK: - ProfileImagePicker
struct ProfileImagePicker: UIViewControllerRepresentable {
    enum ImageSource {
        case photoLibrary
        case camera
    }
    
    var imageSource: ImageSource
    var onImagePicked: (UIImage?) -> Void
    
    func makeUIViewController(context: Context) -> UIViewController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.allowsEditing = true
        
        switch imageSource {
        case .photoLibrary:
            picker.sourceType = .photoLibrary
        case .camera:
            if UIImagePickerController.isSourceTypeAvailable(.camera) {
                picker.sourceType = .camera
            } else {
                // 如果相机不可用，可以回退到相册或显示错误
                print("Camera not available.")
                // 这里可以调用 onImagePicked(nil) 并立即返回一个占位符VC
                // 或者让调用者处理这种情况
                return UIViewController() // 返回一个空的控制器
            }
        }
        
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UINavigationControllerDelegate, UIImagePickerControllerDelegate {
        let parent: ProfileImagePicker
        
        init(_ parent: ProfileImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            let image = (info[.editedImage] as? UIImage) ?? (info[.originalImage]as? UIImage)
            parent.onImagePicked(image)
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.onImagePicked(nil)
        }
    }
}

struct UsernameEditorView: View {
    @Binding var isPresented: Bool
    @Binding var userName: String
    @Binding var editedUsername: String
    @State private var isUpdating = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    @ObservedObject var authService: AuthService
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航栏
            HStack {
                Button("Cancel") {
                    isPresented = false
                }
                .foregroundColor(Color(hex: "#FA6C2D"))
                
                Spacer()
                
                Text("Username")
                    .font(.title3)
                    .fontWeight(.medium)
                
                Spacer()
                
                Button("Confirm") {
                    if !editedUsername.isEmpty {
                        updateUsername()
                    } else {
                        isPresented = false
                    }
                }
                .foregroundColor(Color(hex: "#FA6C2D"))
                .disabled(isUpdating)
            }
            .padding()
            .background(Color(UIColor.systemBackground))
            
            Divider()
            
            // 用户名输入框
            TextField("Username", text: $editedUsername)
                .font(.headline)
                .padding()
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(8)
                .padding(.horizontal)
                .padding(.top, 20)
                .disabled(isUpdating)
        }
        .background(Color(UIColor.systemBackground))
        .alert(isPresented: $showErrorAlert) {
            Alert(
                title: Text("Error"),
                message: Text(errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .overlay(
            Group {
                if isUpdating {
                    Color.black.opacity(0.3)
                    
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                }
            }
        )
    }
    
    private func updateUsername() {
        isUpdating = true
        
        let updateRequest = UpdateUserInfoRequest(nickname: editedUsername)
        
        authService.updateUserInfo(userInfo: updateRequest) { result in
            isUpdating = false
            
            switch result {
            case .success(_):
                // 更新成功，关闭编辑器
                userName = editedUsername
                // 不需要手动更新authService.currentUserName，因为updateUserInfo方法已经更新了它
                isPresented = false
            case .failure(let error):
                // 更新失败，显示错误
                errorMessage = "Failed to update username: \(error.localizedDescription)"
                showErrorAlert = true
            }
        }
    }
}

struct BirthdayPickerView: View {
    @Binding var isPresented: Bool
    @Binding var userBirthday: String
    @State private var selectedDate = Date()
    @State private var isUpdating = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    
    var body: some View {
        ZStack {
            // 渐变背景 - 与 UsernameEditorBottomSheet 类似
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(UIColor(red: 0.16, green: 0.19, blue: 0.25, alpha: 1)),
                    Color(UIColor(red: 0.08, green: 0.09, blue: 0.13, alpha: 1))
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                // 顶部标题区域 - 与 UsernameEditorBottomSheet 类似
                HStack(alignment: .center, spacing: 10) {
                    // 蓝色竖线装饰
                    Rectangle()
                        .fill(Color(hex: "#0048B5"))
                        .frame(width: 4, height: 12)
                    
                    Text("birthday".localized)
                        .font(.custom("PingFang-SC-Medium", size: 14))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // 关闭按钮
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                    }
                    .disabled(isUpdating)
                }
                .padding(.horizontal, 16)
                .padding(.top, 10)
                .padding(.bottom, 20)
                
                // 使用自定义日期选择器，并限制最大日期为今天
                CustomDatePickerView(selectedDate: $selectedDate)
                    .frame(height: 180) // 增加高度
                    .padding(.horizontal, 20) // 增加水平内边距
                    .padding(.top, 15) // 增加顶部内边距
                    .disabled(isUpdating)

                Spacer()

                // Confirm按钮 - 特殊样式
                Button(action: {
                    updateBirthday()
                }) {
                    Text("confirm".localized)
                        .font(.custom("PingFang-SC-Medium", size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 35)
                        .background(
                            ZStack {
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color(hex: "#0048B5").opacity(0.2))
                                
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                            }
                        )
                        .padding(.horizontal, 20)
                }
                .padding(.bottom, 40)
                .disabled(isUpdating)
            }
        }
        .background(
            // 添加一个圆角矩形作为整个弹框的背景
            RoundedRectangle(cornerRadius: 50)
                .fill(Color.clear)
        )
        .onAppear {
            // 如果生日已设置，使用该日期
            if !userBirthday.isEmpty {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd" // 设置日期格式
                if let date = formatter.date(from: userBirthday) {
                    selectedDate = date
                }
            }
        }
        .alert(isPresented: $showErrorAlert) {
            Alert(
                title: Text("Error"),
                message: Text(errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .overlay(
            Group {
                if isUpdating {
                    Color.black.opacity(0.3)
                    
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                }
            }
        )
    }
    
    private func updateBirthday() {
        isUpdating = true
        
        // 将选择的日期转换为时间戳（毫秒）
        let timestamp = Int(selectedDate.timeIntervalSince1970 * 1000)
        
        let updateRequest = UpdateUserInfoRequest(birthday: timestamp)
        
        AuthService.shared.updateUserInfo(userInfo: updateRequest) { result in
            isUpdating = false
            
            switch result {
            case .success(_):
                // 更新成功，关闭选择器
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd" // 设置日期格式
                userBirthday = formatter.string(from: selectedDate)
                
                // 更新并保存到Keychain
                if var userInfo = KeychainHelper.shared.loadUserInfo() {
                    userInfo.birthday = timestamp
                    KeychainHelper.shared.saveUserInfo(userInfo)
                }
                
                isPresented = false
            case .failure(let error):
                // 更新失败，显示错误
                errorMessage = "Failed to update birthday: \(error.localizedDescription)"
                showErrorAlert = true
            }
        }
    }
}

struct GenderPickerView: View {
    @Binding var isPresented: Bool
    @Binding var userGender: String
    @Binding var selectedGender: String
    @State private var isUpdating = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""

    let genderOptions = ["male", "female", "secrecy"]

    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(UIColor(red: 0.16, green: 0.19, blue: 0.25, alpha: 1)),
                    Color(UIColor(red: 0.08, green: 0.09, blue: 0.13, alpha: 1))
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                // Header
                HStack(alignment: .center, spacing: 10) {
                    Rectangle()
                        .fill(Color(hex: "#0048B5"))
                        .frame(width: 4, height: 12)

                    Text("gender".localized)
                        .font(.custom("PingFang-SC-Medium", size: 14))
                        .foregroundColor(.white)

                    Spacer()

                    Button(action: { isPresented = false }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                    }
                    .disabled(isUpdating)
                }
                .padding(.horizontal, 16)
                .padding(.top, 10)
                .padding(.bottom, 20)

                // Gender options
                VStack(spacing: 20) {
                    ForEach(genderOptions, id: \.self) { gender in
                        Button(action: {
                            selectedGender = gender
                        }) {
                            Text(gender.localized)
                                .font(.custom("PingFang-SC-Medium", size: 16))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 40)
                                .background(
                                    ZStack {
                                        RoundedRectangle(cornerRadius: 10)
                                            .fill(selectedGender == gender ? Color(hex: "#0048B5").opacity(0.4) : Color(hex: "#2d313c"))

                                        RoundedRectangle(cornerRadius: 10)
                                            .stroke(selectedGender == gender ? Color(hex: "#0048B5") : Color.clear, lineWidth: 1)
                                    }
                                )
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 15)

                Spacer()

                // Confirm button
                Button(action: {
                    if !selectedGender.isEmpty {
                        updateGender()
                    }
                }) {
                    Text("confirm".localized)
                        .font(.custom("PingFang-SC-Medium", size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 35)
                        .background(
                            ZStack {
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color(hex: "#0048B5").opacity(0.2))

                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                            }
                        )
                        .padding(.horizontal, 20)
                }
                .padding(.bottom, 40)
                .disabled(isUpdating || selectedGender.isEmpty)
            }
        }
        .onAppear {
            if !userGender.isEmpty {
                selectedGender = userGender
            } else {
                selectedGender = "secrecy"
            }
        }
        .alert(isPresented: $showErrorAlert) {
            Alert(
                title: Text("Error"),
                message: Text(errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .overlay(
            Group {
                if isUpdating {
                    Color.black.opacity(0.3)

                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                }
            }
        )
    }

    private func updateGender() {
        isUpdating = true

        // 将性别字符串转换为整数
        let sexValue: Int

        switch selectedGender {
        case "male":
            sexValue = 1
        case "female":
            sexValue = 2
        default:
            sexValue = 0
        }

        let updateRequest = UpdateUserInfoRequest(sex: sexValue)

        AuthService.shared.updateUserInfo(userInfo: updateRequest) { result in
            isUpdating = false

            switch result {
            case .success(_):
                // 更新成功，关闭选择器
                userGender = selectedGender

                // 更新并保存到Keychain
                if var userInfo = KeychainHelper.shared.loadUserInfo() {
                    userInfo.sex = sexValue
                    KeychainHelper.shared.saveUserInfo(userInfo)
                }

                isPresented = false
            case .failure(let error):
                // 更新失败，显示错误
                errorMessage = "Failed to update gender: \(error.localizedDescription)"
                showErrorAlert = true
            }
        }
    }
}

struct HeightPickerView: View {
    @Binding var isPresented: Bool
    @Binding var userHeight: String
    @Binding var heightUnit: HeightUnit
    
    @State private var selectedHeightCm = 170
    @State private var selectedFeet = 5
    @State private var selectedInches = 7
    @State private var selectedUnit: HeightUnit = .metric
    @State private var isUpdating = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    
    @Namespace private var unitNamespace
    
    private func unitTitle(for unit: HeightUnit) -> String {
        switch unit {
        case .metric:
            return "metric".localized
        case .inch:
            return "imperial".localized
        }
    }

    var body: some View {
        ZStack {
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(UIColor(red: 0.16, green: 0.19, blue: 0.25, alpha: 1)),
                    Color(UIColor(red: 0.08, green: 0.09, blue: 0.13, alpha: 1))
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                // Header
                HStack(alignment: .center, spacing: 10) {
                    Rectangle()
                        .fill(Color(hex: "#0048B5"))
                        .frame(width: 4, height: 12)

                    Text("height".localized)
                        .font(.custom("PingFang-SC-Medium", size: 14))
                        .foregroundColor(.white)

                    Spacer()

                    Button(action: { isPresented = false }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                    }
                    .disabled(isUpdating)
                }
                .padding(.horizontal, 16)
                .padding(.top, 10)
                .padding(.bottom, 20)

                // Unit switcher
                HStack(spacing: 40) {
                    ForEach(HeightUnit.allCases, id: \.self) { unit in
                        Button(action: {
                            withAnimation(.spring()) {
                                selectedUnit = unit
                            }
                        }) {
                            VStack(spacing: 8) {
                                Text(unitTitle(for: unit))
                                    .font(.custom("PingFang-SC-Medium", size: 16))
                                    .foregroundColor(selectedUnit == unit ? .white : .gray)

                                if selectedUnit == unit {
                                    Rectangle()
                                        .frame(height: 3)
                                        .foregroundColor(.white)
                                        .matchedGeometryEffect(id: "underline", in: unitNamespace)
                                } else {
                                    Rectangle()
                                        .frame(height: 3)
                                        .foregroundColor(.clear)
                                }
                            }
                            .fixedSize()
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding(.top, 15)

                // Height Pickers
                GeometryReader { geometry in
                    if selectedUnit == .metric {
                        HStack {
                            Spacer()
                            Picker("", selection: $selectedHeightCm) {
                                ForEach(120...220, id: \.self) { cm in
                                    Text("\(cm)").tag(cm)
                                }
                            }
                            .pickerStyle(WheelPickerStyle())
                            .frame(width: geometry.size.width * 0.8)
                            .colorScheme(.dark) // ✅ 强制暗色文字
                            
                            Text("unit_cm".localized)
                                .foregroundColor(.white)
                                .font(.custom("PingFang-SC-Medium", size: 16))
                            Spacer()
                        }
                    } else {
                        HStack {
                            Spacer()
                            Picker("", selection: $selectedFeet) {
                                ForEach(2...7, id: \.self) { feet in
                                    Text("\(feet)").tag(feet)
                                }
                            }
                            .pickerStyle(WheelPickerStyle())
                            .frame(width: geometry.size.width * 0.4)
                            .colorScheme(.dark) // ✅

                            Text("unit_ft".localized)
                                .foregroundColor(.white)
                                .font(.custom("PingFang-SC-Medium", size: 16))

                            Picker("", selection: $selectedInches) {
                                ForEach(0...11, id: \.self) { inch in
                                    Text("\(inch)").tag(inch)
                                }
                            }
                            .pickerStyle(WheelPickerStyle())
                            .frame(width: geometry.size.width * 0.4)
                            .colorScheme(.dark) // ✅
                            
                            Text("unit_in".localized)
                                .foregroundColor(.white)
                                .font(.custom("PingFang-SC-Medium", size: 16))
                            Spacer()
                        }
                    }
                }
                .frame(height: 180)
                .padding(.top, 20)

                Spacer()

                // Confirm button
                Button(action: updateHeight) {
                    Text("confirm".localized)
                        .font(.custom("PingFang-SC-Medium", size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 35)
                        .background(
                            ZStack {
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color(hex: "#0048B5").opacity(0.2))

                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                            }
                        )
                        .padding(.horizontal, 20)
                }
                .padding(.bottom, 40)
                .disabled(isUpdating)
            }
        }
        .onAppear {
            selectedUnit = heightUnit
            if !userHeight.isEmpty, let heightCm = Double(userHeight) {
                if heightUnit == .metric {
                    selectedHeightCm = Int(heightCm)
                } else {
                    let totalInches = heightCm /// 2.54
                    selectedFeet = Int(totalInches / 12)
                    selectedInches = Int(round(totalInches.truncatingRemainder(dividingBy: 12)))
                }
            }
        }
        .alert(isPresented: $showErrorAlert) {
            Alert(title: Text("Error"), message: Text(errorMessage), dismissButton: .default(Text("OK")))
        }
        .overlay(
            Group {
                if isUpdating {
                    Color.black.opacity(0.3)
                    ProgressView().progressViewStyle(CircularProgressViewStyle(tint: .white)).scaleEffect(1.5)
                }
            }
        )
    }

    private func updateHeight() {
        isUpdating = true
        let heightValue: Double
        let heightTypeValue: Int = selectedUnit == .metric ? 1 : 2

        if selectedUnit == .metric {
            heightValue = Double(selectedHeightCm)
        } else {
            heightValue = Double(selectedFeet * 12 + selectedInches)
        }

        let updateRequest = UpdateUserInfoRequest(height: heightValue, heightType: heightTypeValue)
        AuthService.shared.updateUserInfo(userInfo: updateRequest) { result in
            isUpdating = false
            switch result {
            case .success(_):
                userHeight = "\(heightValue)"
                heightUnit = selectedUnit
                if var userInfo = KeychainHelper.shared.loadUserInfo() {
                    userInfo.height = heightValue
                    userInfo.heightType = heightTypeValue
                    KeychainHelper.shared.saveUserInfo(userInfo)
                }
                isPresented = false
            case .failure(let error):
                errorMessage = "Failed to update height: \(error.localizedDescription)"
                showErrorAlert = true
            }
        }
    }
}

struct WeightPickerView: View {
    @Binding var isPresented: Bool
    @Binding var userWeight: String
    @Binding var weightUnit: WeightUnit
    
    @State private var selectedWeightKg = 70
    @State private var selectedWeightLbs = 150
    @State private var selectedUnit: WeightUnit = .metric
    @State private var isUpdating = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    
    @Namespace private var unitNamespace
    
    private func unitTitle(for unit: WeightUnit) -> String {
        switch unit {
        case .metric:
            return "metric".localized
        case .inch:
            return "imperial".localized
        }
    }

    var body: some View {
        ZStack {
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(UIColor(red: 0.16, green: 0.19, blue: 0.25, alpha: 1)),
                    Color(UIColor(red: 0.08, green: 0.09, blue: 0.13, alpha: 1))
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                // Header
                HStack(alignment: .center, spacing: 10) {
                    Rectangle()
                        .fill(Color(hex: "#0048B5"))
                        .frame(width: 4, height: 12)

                    Text("weight".localized)
                        .font(.custom("PingFang-SC-Medium", size: 14))
                        .foregroundColor(.white)

                    Spacer()

                    Button(action: { isPresented = false }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                    }
                    .disabled(isUpdating)
                }
                .padding(.horizontal, 16)
                .padding(.top, 10)
                .padding(.bottom, 20)

                // Unit switcher
                HStack(spacing: 40) {
                    ForEach(WeightUnit.allCases, id: \.self) { unit in
                        Button(action: {
                            withAnimation(.spring()) {
                                selectedUnit = unit
                            }
                        }) {
                            VStack(spacing: 8) {
                                Text(unitTitle(for: unit))
                                    .font(.custom("PingFang-SC-Medium", size: 16))
                                    .foregroundColor(selectedUnit == unit ? .white : .gray)

                                if selectedUnit == unit {
                                    Rectangle()
                                        .frame(height: 3)
                                        .foregroundColor(.white)
                                        .matchedGeometryEffect(id: "underline", in: unitNamespace)
                                } else {
                                    Rectangle()
                                        .frame(height: 3)
                                        .foregroundColor(.clear)
                                }
                            }
                            .fixedSize()
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding(.top, 15)

                // Weight Pickers
                GeometryReader { geometry in
                    if selectedUnit == .metric {
                        HStack {
                            Spacer()
                            Picker("", selection: $selectedWeightKg) {
                                ForEach(30...200, id: \.self) { kg in
                                    Text("\(kg)").tag(kg)
                                }
                            }
                            .pickerStyle(WheelPickerStyle())
                            .frame(width: geometry.size.width * 0.8)
                            .colorScheme(.dark) // ✅ 强制暗色模式
                            
                            Text("unit_kg".localized)
                                .foregroundColor(.white)
                                .font(.custom("PingFang-SC-Medium", size: 16))
                            Spacer()
                        }
                    } else {
                        HStack {
                            Spacer()
                            Picker("", selection: $selectedWeightLbs) {
                                ForEach(60...440, id: \.self) { lbs in
                                    Text("\(lbs)").tag(lbs)
                                }
                            }
                            .pickerStyle(WheelPickerStyle())
                            .frame(width: geometry.size.width * 0.8)
                            .colorScheme(.dark) // ✅ 强制暗色模式
                            
                            Text("unit_lbs".localized)
                                .foregroundColor(.white)
                                .font(.custom("PingFang-SC-Medium", size: 16))
                            Spacer()
                        }
                    }
                }
                .frame(height: 180)
                .padding(.top, 20)
                
                Spacer()

                // Confirm button
                Button(action: updateWeight) {
                    Text("confirm".localized)
                        .font(.custom("PingFang-SC-Medium", size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 35)
                        .background(
                            ZStack {
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color(hex: "#0048B5").opacity(0.2))

                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                            }
                        )
                        .padding(.horizontal, 20)
                }
                .padding(.bottom, 40)
                .disabled(isUpdating)
            }
        }
        .onAppear {
            selectedUnit = weightUnit
            if !userWeight.isEmpty, let weightKg = Double(userWeight) {
                if weightUnit == .metric {
                    selectedWeightKg = Int(weightKg)
                } else {
                    selectedWeightLbs = Int(weightKg)
                    //Int(round(weightKg * 2.20462))
                }
            }
        }
        .alert(isPresented: $showErrorAlert) {
            Alert(title: Text("Error"), message: Text(errorMessage), dismissButton: .default(Text("OK")))
        }
        .overlay(
            Group {
                if isUpdating {
                    Color.black.opacity(0.3)
                    ProgressView().progressViewStyle(CircularProgressViewStyle(tint: .white)).scaleEffect(1.5)
                }
            }
        )
    }

    private func updateWeight() {
        isUpdating = true
        let weightValue: Double
        let weightTypeValue: Int = selectedUnit == .metric ? 1 : 2

        if selectedUnit == .metric {
            weightValue = Double(selectedWeightKg)
        } else {
            weightValue = Double(selectedWeightLbs)//) / 2.20462
        }

        let updateRequest = UpdateUserInfoRequest(weight: weightValue, weightType: weightTypeValue)
        AuthService.shared.updateUserInfo(userInfo: updateRequest) { result in
            isUpdating = false
            switch result {
            case .success(_):
                userWeight = "\(weightValue)"
                weightUnit = selectedUnit
                if var userInfo = KeychainHelper.shared.loadUserInfo() {
                    userInfo.weight = weightValue
                    userInfo.weightType = weightTypeValue
                    KeychainHelper.shared.saveUserInfo(userInfo)
                }
                isPresented = false
            case .failure(let error):
                errorMessage = "Failed to update weight: \(error.localizedDescription)"
                showErrorAlert = true
            }
        }
    }
}

// MARK: - 预览
struct UserInformationView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            UserInformationView()
        }
    }
}



// MARK: - UILabel Representable
struct UILabelRepresentable: UIViewRepresentable {
    let text: String
    let font: UIFont
    let textColor: UIColor
    let width: CGFloat
    
    func makeUIView(context: Context) -> UILabel {
        let label = UILabel()
        let attrString = NSMutableAttributedString(string: text)
        let attr: [NSAttributedString.Key: Any] = [.font: font, .foregroundColor: textColor]
        attrString.addAttributes(attr, range: NSRange(location: 0, length: attrString.length))
        label.attributedText = attrString
        label.numberOfLines = 0
        label.frame = CGRect(x: 0, y: 0, width: width, height: 0)
        label.sizeToFit()
        return label
    }
    
    func updateUIView(_ uiView: UILabel, context: Context) {
        let attrString = NSMutableAttributedString(string: text)
        let attr: [NSAttributedString.Key: Any] = [.font: font, .foregroundColor: textColor]
        attrString.addAttributes(attr, range: NSRange(location: 0, length: attrString.length))
        uiView.attributedText = attrString
    }
}

// 在文件末尾添加ScaleButtonStyle实现
struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Self.Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1)
            .opacity(configuration.isPressed ? 0.9 : 1)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - 自定义日期选择器
struct CustomDatePickerView: View {
    @Binding var selectedDate: Date
    @State private var selectedYear: Int
    @State private var selectedMonth: Int
    @State private var selectedDay: Int
    
    private let years: [Int]
    private let months = 1...12
    private let days = 1...31
    
    private let today: Date
    private let maxYear: Int
    private let maxMonth: Int
    private let maxDay: Int
    
    // 初始化器
    init(selectedDate: Binding<Date>) {
        self._selectedDate = selectedDate
        
        // 保存今天的日期
        self.today = Date()
        
        // 获取当前年份、月份和日期
        let calendar = Calendar.current
        let currentComponents = calendar.dateComponents([.year, .month, .day], from: self.today)
        self.maxYear = currentComponents.year!
        self.maxMonth = currentComponents.month!
        self.maxDay = currentComponents.day!
        
        // 创建年份范围（2000到当前年份）
        self.years = Array(2000...self.maxYear).reversed() // 倒序显示
        
        // 设置初始选中的年、月、日
        let dateComponents = calendar.dateComponents([.year, .month, .day], from: selectedDate.wrappedValue)
        let initialYear = min(dateComponents.year ?? self.maxYear, self.maxYear)
        let initialMonth = dateComponents.month ?? 1
        let initialDay = dateComponents.day ?? 1
        
        // 初始化状态变量
        _selectedYear = State(initialValue: initialYear)
        _selectedMonth = State(initialValue: initialMonth)
        _selectedDay = State(initialValue: initialDay)
    }
    
    var body: some View {
        // 按照 "年-月-日" 的顺序排列
        HStack(spacing: 0) {
            // 年份选择器
            Picker("", selection: $selectedYear) {
                ForEach(years, id: \.self) { year in
                    Text("\(year)").tag(year)
                }
            }
            .pickerStyle(WheelPickerStyle())
            .frame(width: 100)
            .clipped()
            .onChange(of: selectedYear) { _ in
                validateAndFixDate()
            }

            // 月份选择器
            Picker("", selection: $selectedMonth) {
                ForEach(months, id: \.self) { month in
                    if selectedYear < maxYear || month <= maxMonth {
                        Text(String(format: "%02d", month)).tag(month)
                    }
                }
            }
            .pickerStyle(WheelPickerStyle())
            .frame(width: 80)
            .clipped()
            .onChange(of: selectedMonth) { _ in
                validateAndFixDate()
            }

            // 日期选择器
            Picker("", selection: $selectedDay) {
                ForEach(days, id: \.self) { day in
                    if isDateValid(year: selectedYear, month: selectedMonth, day: day) {
                        Text(String(format: "%02d", day)).tag(day)
                    }
                }
            }
            .pickerStyle(WheelPickerStyle())
            .frame(width: 80)
            .clipped()
            .onChange(of: selectedDay) { _ in
                updateSelectedDate()
            }
        }
        .colorInvert() // 反转颜色，使文字变白
        .colorMultiply(.white) // 确保文字是纯白色
    }
    
    private func isDateValid(year: Int, month: Int, day: Int) -> Bool {
        guard day <= daysInMonth(year: year, month: month) else {
            return false
        }
        
        if year == maxYear {
            if month > maxMonth {
                return false
            }
            if month == maxMonth && day > maxDay {
                return false
            }
        }
        return true
    }
    
    private func validateAndFixDate() {
        // 如果月份超出范围，重置为最大允许月份
        if selectedYear == maxYear && selectedMonth > maxMonth {
            selectedMonth = maxMonth
        }
        
        // 如果日期超出范围，重置为当月最大允许日期
        let maxAllowedDay = daysInMonth(year: selectedYear, month: selectedMonth)
        if selectedDay > maxAllowedDay {
            selectedDay = maxAllowedDay
        }
        
        if selectedYear == maxYear && selectedMonth == maxMonth && selectedDay > maxDay {
            selectedDay = maxDay
        }
        
        updateSelectedDate()
    }
    
    private func daysInMonth(year: Int, month: Int) -> Int {
        let calendar = Calendar.current
        let dateComponents = DateComponents(year: year, month: month)
        let date = calendar.date(from: dateComponents)!
        let range = calendar.range(of: .day, in: .month, for: date)!
        return range.count
    }
    
    private func updateSelectedDate() {
        var components = DateComponents()
        components.year = selectedYear
        components.month = selectedMonth
        components.day = selectedDay
        
        if let date = Calendar.current.date(from: components) {
            self.selectedDate = date
        }
    }
}
