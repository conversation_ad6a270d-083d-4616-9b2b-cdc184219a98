import SwiftUI

/// 分享设置视图
struct SharingSettingsView: View {
    // MARK: - 状态属性
    @State private var enableSharing = false
    @State private var familySharing = false
    @State private var doctorSharing = false
    @State private var emergencyContact = false
    @State private var shareLocation = false
    @State private var shareHealthData = true
    @State private var shareSleepData = true
    @State private var shareActivityData = true
    @State private var shareHeartRateData = true
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 启用分享开关
                Toggle("启用健康数据分享", isOn: $enableSharing)
                    .padding()
                    .background(Color.white.opacity(0.1))
                    .cornerRadius(10)
                    .padding(.horizontal)
                    .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
                
                if enableSharing {
                    // 分享对象设置
                    sharingTargetsSection
                    
                    // 分享内容设置
                    sharingContentSection
                    
                    // 分享方式
                    sharingMethodSection
                }
            }
            .padding(.vertical)
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#00204A"),
                    Color(hex: "#002D67")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .edgesIgnoringSafeArea(.all)
        )
        .navigationTitle("分享设置")
        .navigationBarTitleDisplayMode(.inline)
        .foregroundColor(.white)
    }
    
    // MARK: - 分享对象设置
    private var sharingTargetsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("分享对象")
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal)
            
            VStack(spacing: 0) {
                Toggle("家人", isOn: $familySharing)
                    .padding()
                    .background(Color.white.opacity(0.05))
                    .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
                
                Divider().background(Color.white.opacity(0.1))
                
                Toggle("医生", isOn: $doctorSharing)
                    .padding()
                    .background(Color.white.opacity(0.05))
                    .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
                
                Divider().background(Color.white.opacity(0.1))
                
                Toggle("紧急联系人", isOn: $emergencyContact)
                    .padding()
                    .background(Color.white.opacity(0.05))
                    .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
            }
            .background(Color.white.opacity(0.1))
            .cornerRadius(10)
            .padding(.horizontal)
        }
    }
    
    // MARK: - 分享内容设置
    private var sharingContentSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("分享内容")
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal)
            
            VStack(spacing: 0) {
                Toggle("位置信息", isOn: $shareLocation)
                    .padding()
                    .background(Color.white.opacity(0.05))
                    .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
                
                Divider().background(Color.white.opacity(0.1))
                
                Toggle("健康状态", isOn: $shareHealthData)
                    .padding()
                    .background(Color.white.opacity(0.05))
                    .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
                
                Divider().background(Color.white.opacity(0.1))
                
                Toggle("睡眠数据", isOn: $shareSleepData)
                    .padding()
                    .background(Color.white.opacity(0.05))
                    .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
                
                Divider().background(Color.white.opacity(0.1))
                
                Toggle("活动数据", isOn: $shareActivityData)
                    .padding()
                    .background(Color.white.opacity(0.05))
                    .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
                
                Divider().background(Color.white.opacity(0.1))
                
                Toggle("心率数据", isOn: $shareHeartRateData)
                    .padding()
                    .background(Color.white.opacity(0.05))
                    .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
            }
            .background(Color.white.opacity(0.1))
            .cornerRadius(10)
            .padding(.horizontal)
        }
    }
    
    // MARK: - 分享方式
    private var sharingMethodSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("分享方式")
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal)
            
            Button(action: {
                // 生成分享链接
            }) {
                HStack {
                    Image(systemName: "link.circle.fill")
                        .foregroundColor(.white)
                        .font(.title2)
                    
                    Text("生成分享链接")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.white.opacity(0.7))
                        .font(.subheadline)
                }
                .padding()
                .background(Color(hex: "#003A8C"))
                .cornerRadius(10)
                .padding(.horizontal)
            }
            
            Button(action: {
                // 分享到社交媒体
            }) {
                HStack {
                    Image(systemName: "square.and.arrow.up.fill")
                        .foregroundColor(.white)
                        .font(.title2)
                    
                    Text("分享到社交媒体")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.white.opacity(0.7))
                        .font(.subheadline)
                }
                .padding()
                .background(Color(hex: "#003A8C"))
                .cornerRadius(10)
                .padding(.horizontal)
            }
        }
    }
}

// MARK: - 预览
struct SharingSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SharingSettingsView()
    }
} 