//
//  AboutView.swift
//  WindRing
//
//  Created by zx on 2025/5/16.
//

import SwiftUI

// 添加新的详情页面
struct AboutView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var notificationManager: LocalNotificationManager
    private var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "N/A"
    }
    
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色xf
            Color(UIColor(red: 0.027, green: 0.027, blue: 0.031, alpha: 1)).edgesIgnoringSafeArea(.all)
            
            // 添加光晕背景
            VersionGlowBackground()
            
            VStack(spacing: 0) {
                // 自定义导航栏
                HStack(alignment: .center) {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    // 标题居左 - 添加点击返回功能
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("about_ring_title".localized())
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
                            .padding(.leading, 5)
                    }
                   
                    Spacer()
                }
                .padding(.horizontal, 16)
                .frame(height: 44) // 标准iOS导航栏高度
                .padding(.top, 8)
                
                // 主内容
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 20) {
                        // 顶部卡片
                        VStack(spacing: 16) {
                            // 使用ZStack将logo叠放在圆圈上
                            ZStack {
                                // 背景圆圈
                                Image("slices_32")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 95, height: 95)
                                
                                // 叠放在上面的logo
                                Image("logo")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 60, height: 60)
                                    .offset(y: -5) // 微调位置，使logo在圆圈中间偏上位置
                            }
                            .padding(.top, 30)
                            
                            // 应用名称
                            Text("about_us_app_name".localized())
                                .font(.custom("PingFang-SC-Medium", size: 20))
                                .foregroundColor(.white)
                            
                            // 版本号
                            Text(String(format: "about_us_version".localized, appVersion))
                                .font(.custom("PingFang-SC-Regular", size: 15))
                                .foregroundColor(Color(UIColor(red: 0.62, green: 0.63, blue: 0.64, alpha: 1)))
                                .padding(.bottom, 30)
                        }
                        .frame(maxWidth: .infinity)
                        .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
                        .cornerRadius(25)
                        .padding(.horizontal, 10)
                        
                        // 底部卡片
                        VStack(spacing: 0) {
                            // Terms Of Use
                            //VersionTermsOfUseView()
                            NavigationLink(destination: CommonWebView(tag: .termsOfUse)) {
                                HStack {
                                    Image("slices_28")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 16, height: 16)
                                    
                                    Text("about_us_terms_of_use".localized())
                                        .font(.custom("PingFang-SC-Medium", size: 15))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Image(systemName: "chevron.right")
                                        .foregroundColor(Color(UIColor(red: 0.62, green: 0.63, blue: 0.64, alpha: 1)))
                                }
                                .padding(.vertical, 16)
                                .padding(.horizontal, 16)
                            }
                            
                            Divider()
                                .background(Color.gray.opacity(0.2))
                                .padding(.leading, 16)
                            
                            // Privacy Policy
                            //VersionPrivacyPolicyView()
                            NavigationLink(destination: CommonWebView(tag: .privacyPolicy)) {
                                HStack {
                                    Image("slices_29")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 16, height: 16)
                                    
                                    Text("about_us_privacy_policy".localized())
                                        .font(.custom("PingFang-SC-Medium", size: 15))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Image(systemName: "chevron.right")
                                        .foregroundColor(Color(UIColor(red: 0.62, green: 0.63, blue: 0.64, alpha: 1)))
                                }
                                .padding(.vertical, 16)
                                .padding(.horizontal, 16)
                            }
                            
                            Divider()
                                .background(Color.gray.opacity(0.2))
                                .padding(.leading, 16)
                            
                            // Information list
                            //VersionInformationListView()
                            NavigationLink(destination: CommonWebView(tag: .informationList)) {
                                HStack {
                                    Image("slices_30")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 16, height: 16)
                                    
                                    Text("about_us_information_list".localized())
                                        .font(.custom("PingFang-SC-Medium", size: 15))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Image(systemName: "chevron.right")
                                        .foregroundColor(Color(UIColor(red: 0.62, green: 0.63, blue: 0.64, alpha: 1)))
                                }
                                .padding(.vertical, 16)
                                .padding(.horizontal, 16)
                            }
                        }
                        .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
                        .cornerRadius(25)
                        .padding(.horizontal, 10)
                    }
                    .padding(.top, 20)
                    .padding(.bottom, 30)
                }
            }
        }
        .navigationBarHidden(true)
    }
}
