//
//  ShareDataContentView.swift
//  WindRing
//
//  Created by zx on 2025/6/24.
//

import SwiftUI
import Combine
// MARK: - Share Data Content View
struct ShareDataContentView: View {
    @Environment(\.presentationMode) var presentationMode
    var onLinkCreated: ((String) -> Void)?
    var onUpdate: (() -> Void)?
    var userData: Binding<MySharedUserData?>?
    
    @State private var shareSleep = false
    @State private var shareActivity = true
    @State private var shareStress = false
    @State private var shareHealthStatus = true
    @State private var shareWeeklyReport = false
    
    @State private var isLoading = false
    @State private var errorMessage: String?

    private var isEditMode: Bool {
        return userData != nil
    }

    var body: some View {
        
        ZStack {
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 0) {
                ScrollView {
                    VStack(alignment: .leading, spacing: 16) {
                        Text("share_data_content_description".localized)
                            .font(.system(size: 13))
                            .foregroundColor(Color(white: 0.7))
                            .padding(.horizontal)
                            .padding(.bottom, 10)
                        
                        // Guiding Standard Data
                        Text("share_data_content_standard_data_header".localized)
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(Color(white: 0.5))
                            .padding(.horizontal)

                        VStack(spacing: 0) {
                            dataSelectionRow(title: "share_data_content_sleep".localized, icon: "share_data_icon_sleep", isOn: $shareSleep)

                            dataSelectionRow(title: "share_data_content_activity".localized, icon: "share_data_icon_activity", isOn: $shareActivity)

                            if VersionUpdateService.shared.status == 1 {
                                dataSelectionRow(title: "share_data_content_stress".localized, icon: "share_data_icon_ stress", isOn: $shareStress)
                            
                                dataSelectionRow(title: "share_data_content_health_status".localized, icon: "share_data_icon_healthstatus", isOn: $shareHealthStatus)
                            }
                        }
                        .background(Color(white: 0.1))
                        .cornerRadius(10)
                        .padding(.horizontal)

                        // Report
//                        Text("share_data_content_report_header".localized)
//                            .font(.system(size: 12, weight: .medium))
//                            .foregroundColor(Color(white: 0.5))
//                            .padding(.top, 20)
//                            .padding(.horizontal)
//
//                        VStack(spacing: 0) {
//                            dataSelectionRow(title: "share_data_content_weekly_report".localized, icon: "Sleep", isOn: $shareWeeklyReport)
//                        }
//                        .background(Color(white: 0.1))
//                        .cornerRadius(10)
//                        .padding(.horizontal)

                        Spacer(minLength: 40)
                        if !isEditMode {
                            linkGenerationNotice
                                .padding(.horizontal)
                        }

                    }
                    .padding(.vertical)
                }
                
                bottomButtons
            }
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { presentationMode.wrappedValue.dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 17, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
            ToolbarItem(placement: .principal) {
                Text("share_data_content_title".localized)
                    .font(.custom("PingFang-SC-Heavy", size: 19))
                    .foregroundColor(.white)
            }
        }
        .onAppear {
            if let user = userData?.wrappedValue {
                shareSleep = user.sleep == 0
                shareActivity = user.activity == 0
                shareStress = user.stress == 0
                shareHealthStatus = user.healthStatus == 0
            }
        }
    }
    
    private func dataSelectionRow(title: String, icon: String, isOn: Binding<Bool>) -> some View {
        HStack {
            Image(icon)
                .resizable()
                .renderingMode(.template)
                .scaledToFit()
                .frame(width: 20, height: 20)
                .foregroundColor(.gray)
                
            Text(title.localized)
                .foregroundColor(.white)
                .font(.system(size: 15))

            Spacer()
            Toggle("", isOn: Binding(
                get: { isOn.wrappedValue },
                set: { newValue in
                    if isOn.wrappedValue != newValue {
                        isOn.wrappedValue = newValue
                        if isEditMode {
                            updateSharingSettings()
                        }
                    }
                }
            ))
            .toggleStyle(SwitchToggleStyle(tint: Color(hex: "5AD439")))
//            Toggle("", isOn: isOn)
//                .toggleStyle(SwitchToggleStyle(tint: Color(hex: "5AD439")))
//                .onChange(of: isOn.wrappedValue) { _ in
//                    if isEditMode {
//                        updateSharingSettings()
//                    }
//                }
        }
        .padding()
    }
    
    @State private var showStopSharingAlert = false

    private var bottomButtons: some View {
        VStack(spacing: 16) {
            if isEditMode {
                Button(action: {
                    showStopSharingAlert = true
                }) {
                    Text("Stop Sharing") // Needs localization
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(
                            RoundedRectangle(cornerRadius: 22)
                                .stroke(Color.red, lineWidth: 1)
                        )
                }
                .alert("Stop Sharing", isPresented: $showStopSharingAlert) { // Needs localization
                    Button("Cancel", role: .cancel) { }
                    Button("Confirm", role: .destructive) {
                        stopSharing()
                    }
                } message: {
                    Text("Are you sure you want to stop sharing your data?") // Needs localization
                }
            } else {
                Button(action: confirmButton) {
                    Text("confirm".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(
                            RoundedRectangle(cornerRadius: 22)
                                .fill(isLoading ? Color.gray : Color(hex: "0048B5"))
                        )
                }
                .disabled(isLoading)
                
                Button(action: { presentationMode.wrappedValue.dismiss() }) {
                    Text("cancel".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(
                            RoundedRectangle(cornerRadius: 22)
                                .stroke(Color.gray, lineWidth: 1)
                        )
                }
            }
        }
        .padding()
    }
    
    private func stopSharing() {
        isLoading = true
        Task {
            do {
                if let user = userData?.wrappedValue {
                    let success = try await APIService.shared.updateShareInfo(
                        id: Int(user.id),
                        sleep: 1,
                        activity: 1,
                        stress: 1,
                        healthStatus: 1
                    )
                    if success {
                        onUpdate?()
                        presentationMode.wrappedValue.dismiss()
                    } else {
                        errorMessage = "Failed to stop sharing." // Placeholder
                    }
                }
            } catch {
                errorMessage = error.localizedDescription
            }
            isLoading = false
        }
    }

    private func updateSharingSettings() {
        isLoading = true
        Task {
            do {
                if var user = userData?.wrappedValue {
                    user.sleep = shareSleep ? 0 : 1
                    user.activity = shareActivity ? 0 : 1
                    user.stress = shareStress ? 0 : 1
                    user.healthStatus = shareHealthStatus ? 0 : 1
                    userData?.wrappedValue = user

                    let success = try await APIService.shared.updateShareInfo(
                        id: Int(user.id),
                        sleep: user.sleep,
                        activity: user.activity,
                        stress: user.stress,
                        healthStatus: user.healthStatus
                    )
                    if success {
                        onUpdate?()
                    } else {
                        errorMessage = "Failed to update." // Placeholder
                    }
                }
            } catch {
                errorMessage = error.localizedDescription
            }
            isLoading = false
        }
    }

    private func confirmButton() {
        isLoading = true
        Task {
            do {
                if isEditMode, let user = userData?.wrappedValue {
                    // Update logic
                    let success = try await APIService.shared.updateShareInfo(
                        id: Int(user.id),
                        sleep: shareSleep ? 0 : 1,
                        activity: shareActivity ? 0 : 1,
                        stress: shareStress ? 0 : 1,
                        healthStatus: shareHealthStatus ? 0 : 1
                    )
                    if success {
                        onUpdate?()
                        presentationMode.wrappedValue.dismiss()
                    } else {
                        errorMessage = "Failed to update." // Placeholder for error message
                    }
                } else {
                    // Create logic
                    let link = try await APIService.shared.shareWithOthers(
                        sleep: shareSleep ? 0 : 1,
                        activity: shareActivity ? 0 : 1,
                        stress: shareStress ? 0 : 1,
                        healthStatus: shareHealthStatus ? 0 : 1
                    )
                    onLinkCreated?(link)
                    presentationMode.wrappedValue.dismiss()
                }
            } catch {
                errorMessage = error.localizedDescription
            }
            isLoading = false
        }
    }
    
    private var linkGenerationNotice: some View {
        Text("share_data_content_link_notice".localized)
            .font(.system(size: 12))
            .foregroundColor(.gray)
            .multilineTextAlignment(.leading)
            .fixedSize(horizontal: false, vertical: true)
    }
}
