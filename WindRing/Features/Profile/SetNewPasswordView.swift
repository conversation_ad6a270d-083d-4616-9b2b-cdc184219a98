import SwiftUI

struct SetNewPasswordView: View {
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var showPassword = false
    @State private var showConfirmPassword = false
    @State private var showPasswordError = false
    @State private var errorMessage = ""
    @State private var isLoading = false
    
    let account: String
    let code: String
    let onPasswordSet: () -> Void
    
    @Environment(\.dismiss) private var dismiss

    private func validateAndResetPassword() {
        let passwordRegex = AppGlobals.passwordRegex
        let passwordPredicate = NSPredicate(format: "SELF MATCHES %@", passwordRegex)
        
        if !passwordPredicate.evaluate(with: password) {
            errorMessage = "password_format_error".localized
            showPasswordError = true
            return
        }
        
        if password != confirmPassword {
            errorMessage = "password_mismatch_error".localized
            showPasswordError = true
            return
        }
        
        showPasswordError = false
        isLoading = true
        
        AuthService.shared.resetPassword(account: account, code: code, password: password, confirmPassword: confirmPassword) { result in
            isLoading = false
            switch result {
            case .success:
                onPasswordSet()
            case .failure(let error):
                errorMessage = error.localizedDescription
                showPasswordError = true
            }
        }
    }
    
    var body: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)
            
            VStack {
                Image("Login_bg")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 200)
                    .edgesIgnoringSafeArea(.top)
                Spacer()
            }
            
            VStack(spacing: 16) {
                if showPasswordError {
                    Text(errorMessage)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                        .padding(12)
                        .background(Color.red)
                                .cornerRadius(8)
                        .transition(.opacity.animation(.easeInOut))
                }
                
                passwordField(
                    title: "enter_password".localized,
                    text: $password,
                    isSecure: !showPassword,
                    toggleAction: { showPassword.toggle() }
                )
                
                Text("password_rules".localized)
                    .font(.custom("PingFang-SC-Regular", size: 12))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .fixedSize(horizontal: false, vertical: true)

                passwordField(
                    title: "enter_again".localized,
                    text: $confirmPassword,
                    isSecure: !showConfirmPassword,
                    toggleAction: { showConfirmPassword.toggle() }
                )
                
                                                    Spacer()
                
                nextButton
            }
            .padding()
            .padding(.top, 50)
                                }
        .navigationTitle("set_password_title".localized)
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { dismiss() }) {
                    Image(systemName: "chevron.left")
                                            .foregroundColor(.white)
                                    }
                                }
                            }
        .toolbarBackground(.visible, for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
    }

    private func passwordField(title: String, text: Binding<String>, isSecure: Bool, toggleAction: @escaping () -> Void) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.custom("PingFang-SC-Medium", size: 14))
                .foregroundColor(.white)

            HStack {
                ZStack(alignment: .leading) {
                    if text.wrappedValue.isEmpty {
                        Text("placeholder_new_password".localized)
                            .foregroundColor(.gray)
                    }
                    if isSecure {
                        SecureField("", text: text)
                            .foregroundColor(.white)
                    } else {
                        TextField("", text: text)
                            .foregroundColor(.white)
                    }
                }
                
                Button(action: toggleAction) {
                    Image(systemName: "eye")
                        .foregroundColor(.gray)
                }
            }
            .padding(.horizontal)
            .frame(height: 50)
            .background(Color(white: 0.1))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(white: 0.3), lineWidth: 1)
            )
        }
    }
    
    private var nextButton: some View {
        Button(action: validateAndResetPassword) {
            Text("confirm".localized)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(Color.blue)
                .cornerRadius(25)
        }
        .disabled(isLoading)
    }
}

#Preview {
    SetNewPasswordView(account: "<EMAIL>", code: "123456", onPasswordSet: {})
} 
