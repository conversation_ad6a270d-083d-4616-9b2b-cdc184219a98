import UIKit
import SwiftUI

// 添加NavigationCoordinator单例类
class NavigationCoordinator {
    static let shared = NavigationCoordinator()
    
    func goBack() {
        DispatchQueue.main.async {
            // 尝试找到当前活动的导航控制器
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first,
                  let rootViewController = window.rootViewController else {
                return
            }
            
            // 尝试不同的导航返回方法
            if let navigationController = rootViewController.findNavigationController() {
                if navigationController.viewControllers.count > 1 {
                    navigationController.popViewController(animated: true)
                } else {
                    navigationController.dismiss(animated: true)
                }
            } else {
                // 如果没有找到导航控制器，尝试dismiss当前呈现的视图控制器
                var currentVC = rootViewController
                while let presentedVC = currentVC.presentedViewController {
                    currentVC = presentedVC
                }
                currentVC.dismiss(animated: true)
            }
        }
    }
}

// 添加UIViewController扩展
extension UIViewController {
    func findNavigationController() -> UINavigationController? {
        if let nav = self as? UINavigationController {
            return nav
        }
        
        if let nav = self.navigationController {
            return nav
        }
        
        if let tab = self as? UITabBarController {
            if let selectedVC = tab.selectedViewController {
                return selectedVC.findNavigationController()
            }
        }
        
        for child in children {
            if let nav = child.findNavigationController() {
                return nav
            }
        }
        
        if let presentingNav = presentingViewController?.findNavigationController() {
            return presentingNav
        }
        
        return nil
    }
} 