//
//  Untitled.swift
//  WindRing
//
//  Created by zx on 2025/6/18.
//

import SwiftUI

struct UserGuideView: View {
    // 添加环境变量以访问导航设置
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ZStack {
            // 背景色
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            // 添加光晕背景
            CornerGlowBackground(
                colors: [
                    Color(hex: "#0048B5").opacity(0.4),
                    Color(hex: "#0048B5").opacity(0.2),
                    Color(hex: "#0048B5").opacity(0.0)
                ]
            )
            
            VStack(spacing: 0) {
                // 列表内容 - 使用深色背景和圆角
                VStack(spacing: 0) {
                    // App 项目
                    NavigationLink(destination: CommonWebView(tag: .appUsageGuide)) {
                        listItemWithImage(title: "user_guide_app".localized, imageName: "slices_23")
                    }
                    
                    Divider()
                        .background(Color.gray.opacity(0.3))
                        .padding(.horizontal, 10)
                    
                    // Ring 项目
                    NavigationLink(destination: CommonWebView(tag: .ringUsageGuide)) {
                        listItemWithImage(title: "user_guide_ring".localized, imageName: "slices_24")
                    }
                    
                    Divider()
                        .background(Color.gray.opacity(0.3))
                        .padding(.horizontal, 10)
                    
                    // Account Password 项目
//                    NavigationLink(destination: CommonWebView(tag: .passwordUsageGuide)) {
//                        listItemWithImage(title: "user_guide_account_password".localized, imageName: "slices_25")
//                    }
                }
                .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
                .cornerRadius(25)
                .padding(.horizontal, 10)
                .padding(.top, 10)
                
                Spacer() // 填充剩余空间
            }
            .navigationBarTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        self.presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack {
                            Image(systemName: "chevron.left")
                                .foregroundColor(.white)
                            Text("user_guide_title".localized)
                                .font(.custom("PingFang-SC-Heavy", size: 19))
                                .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
                        }
                    }
                }
            }
        }
    }
    
    // 带图片的列表项组件
    private func listItemWithImage(title: String, imageName: String) -> some View {
        HStack {
            Image(imageName)
                .resizable()
                .scaledToFit()
                .frame(width: 24, height: 24)
                .padding(.leading, 10)
            
            Text(title)
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .foregroundColor(.gray)
                .font(.caption)
                .padding(.trailing, 10)
        }
        .padding(.vertical, 15)
    }
}


// App使用指南页面
struct AppGuideView: View {
    // 添加环境变量以访问导航设置
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ZStack {
            // 背景色
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            // 添加光晕背景
            CornerGlowBackground(
                colors: [
                    Color(hex: "#0048B5").opacity(0.4),
                    Color(hex: "#0048B5").opacity(0.2),
                    Color(hex: "#0048B5").opacity(0.0)
                ]
            )
            
            // 内容容器
            VStack(spacing: 10) {
                // 标题与返回按钮由导航栏处理
                
                // 深色容器内的列表
                VStack(spacing: 0) {
                    // 项目1
                    guideItem(title: "app_guide_logout".localized, content: "app_guide_logout_content".localized)
                    
                    Divider()
                        .background(Color.gray.opacity(0.3))
                    
                    // 项目2
                    guideItem(title: "app_guide_bind_ring".localized, content: "app_guide_bind_ring_content".localized)
                    
                    Divider()
                        .background(Color.gray.opacity(0.3))
                    
                    // 项目3
                    guideItem(title: "app_guide_unbind_ring".localized, content: "app_guide_unbind_ring_content".localized)
                }
                .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
                .cornerRadius(25)
                .padding(.horizontal, 10)
                .padding(.top, 10)
                
                Spacer()
            }
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    self.presentationMode.wrappedValue.dismiss()
                }) {
                    HStack {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.white)
                        Text("app_guide_title".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(.white)
                    }
                }
            }
        }
    }
    
    // 指南项目组件
    private func guideItem(title: String, content: String) -> some View {
        HStack {
            Text(title)
                .font(.custom("PingFang-SC-Medium", size: 15))
                .foregroundColor(.white)
                .padding(.leading, 20)
            
            Spacer()
            
            Text(content)
                .font(.custom("PingFang-SC-Regular", size: 14))
                .foregroundColor(Color.white.opacity(0.7))
                .padding(.trailing, 20)
        }
        .padding(.vertical, 15)
    }
}

// Ring使用指南页面
struct RingGuideView: View {
    // 添加环境变量以访问导航设置
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ZStack {
            // 背景色
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            // 添加光晕背景
            CornerGlowBackground(
                colors: [
                    Color(hex: "#0048B5").opacity(0.4),
                    Color(hex: "#0048B5").opacity(0.2),
                    Color(hex: "#0048B5").opacity(0.0)
                ]
            )
            
            // 内容容器
            VStack(spacing: 10) {
                // 深色容器内的列表
                VStack(spacing: 0) {
                    // 项目1
                    guideItem(title: "ring_guide_getting_started".localized, content: "ring_guide_getting_started_content".localized)
                    
                    Divider()
                        .background(Color.gray.opacity(0.3))
                    
                    // 项目2
                    guideItem(title: "ring_guide_button_operations".localized, content: "ring_guide_button_operations_content".localized)
                    
                    Divider()
                        .background(Color.gray.opacity(0.3))
                    
                    // 项目3
                    guideItem(title: "ring_guide_function_menu".localized, content: "ring_guide_function_menu_content".localized)
                }
                .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
                .cornerRadius(25)
                .padding(.horizontal, 10)
                .padding(.top, 10)
                
                Spacer()
            }
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    self.presentationMode.wrappedValue.dismiss()
                }) {
                    HStack {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.white)
                        Text("ring_guide_title".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(.white)
                    }
                }
            }
        }
    }
    
    // 指南项目组件
    private func guideItem(title: String, content: String) -> some View {
        HStack {
            Text(title)
                .font(.custom("PingFang-SC-Medium", size: 15))
                .foregroundColor(.white)
                .padding(.leading, 20)
            
            Spacer()
            
            Text(content)
                .font(.custom("PingFang-SC-Regular", size: 14))
                .foregroundColor(Color.white.opacity(0.7))
                .padding(.trailing, 20)
        }
        .padding(.vertical, 15)
    }
}

// 账户密码指南页面
struct AccountPasswordGuideView: View {
    // 添加环境变量以访问导航设置
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ZStack {
            // 背景色
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            // 添加光晕背景
            CornerGlowBackground(
                colors: [
                    Color(hex: "#0048B5").opacity(0.4),
                    Color(hex: "#0048B5").opacity(0.2),
                    Color(hex: "#0048B5").opacity(0.0)
                ]
            )
            
            // 内容容器
            VStack(spacing: 10) {
                // 深色容器内的列表
                VStack(spacing: 0) {
                    // 项目1
                    guideItem(title: "account_guide_register".localized, content: "account_guide_register_content".localized)
                    
                    Divider()
                        .background(Color.gray.opacity(0.3))
                    
                    // 项目2
                    guideItem(title: "account_guide_login_methods".localized, content: "account_guide_login_methods_content".localized)
                    
                    Divider()
                        .background(Color.gray.opacity(0.3))
                    
                    // 项目3
                    guideItem(title: "account_guide_password_security".localized, content: "account_guide_password_security_content".localized)
                }
                .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
                .cornerRadius(25)
                .padding(.horizontal, 10)
                .padding(.top, 10)
                
                Spacer()
            }
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    self.presentationMode.wrappedValue.dismiss()
                }) {
                    HStack {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.white)
                        Text("account_guide_title".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(.white)
                    }
                }
            }
        }
    }
    
    // 指南项目组件
    private func guideItem(title: String, content: String) -> some View {
        HStack {
            Text(title)
                .font(.custom("PingFang-SC-Medium", size: 15))
                .foregroundColor(.white)
                .padding(.leading, 20)
            
            Spacer()
            
            Text(content)
                .font(.custom("PingFang-SC-Regular", size: 14))
                .foregroundColor(Color.white.opacity(0.7))
                .padding(.trailing, 20)
        }
        .padding(.vertical, 15)
    }
}
