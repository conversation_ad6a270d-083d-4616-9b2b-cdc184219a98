//
//  FeedbackRecordView.swift
//  WindRing
//
//  Created by zx on 2025/6/18.
//

import SwiftUI

// MARK: - FeedbackRecordView
struct FeedbackRecordView: View {
    @Environment(\.presentationMode) var presentationMode
    
    // 模拟数据 - 实际项目中应从API获取
    private let feedbackRecords: [FeedbackRecord] = [
        FeedbackRecord(
            id: "1",
            types: ["Ring", "App"],
            content: "When opening the invitation page with a browser, it still prompts that it cannot be accessed!",
            datetime: "2025-02-17 12:00:00",
            status: .submitted
        ),
        FeedbackRecord(
            id: "2",
            types: ["App"],
            content: "The app cannot be updated!",
            datetime: "2025-02-17 12:00:00",
            status: .replied
        )
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    ForEach(feedbackRecords) { record in
                        FeedbackRecordItemView(record: record)
                    }
                }
                .padding()
            }
            .navigationBarTitle("Feedback Record", displayMode: .inline)
            .navigationBarItems(leading: But<PERSON>(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                HStack {
                    Image(systemName: "chevron.left")
                    Text("Back")
                }
                .foregroundColor(.gray)
            })
            .background(Color(UIColor.systemGroupedBackground).edgesIgnoringSafeArea(.all))
        }
    }
}

// 反馈记录项视图
struct FeedbackRecordItemView: View {
    let record: FeedbackRecord
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            // 顶部: 类型和状态标签
            HStack {
                Text(record.types.joined(separator: ", "))
                    .font(.headline)
                    .foregroundColor(.black)
                
                Spacer()
                
                StatusBadgeView(status: record.status)
            }
            
            // 内容
            Text(record.content)
                .font(.body)
                .foregroundColor(.black)
                .lineLimit(nil)
                .multilineTextAlignment(.leading)
            
            // 时间
            Text(record.datetime)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

// 状态标签视图
struct StatusBadgeView: View {
    let status: FeedbackStatus
    
    var body: some View {
        Text(status.displayText)
            .font(.caption)
            .foregroundColor(status.textColor)
            .padding(.horizontal, 10)
            .padding(.vertical, 4)
            .background(status.backgroundColor)
            .cornerRadius(10)
    }
}

// 反馈状态枚举
enum FeedbackStatus: String {
    case submitted = "Submitted"
    case replied = "Replied"
    case processing = "Processing"
    
    var displayText: String {
        return self.rawValue
    }
    
    var backgroundColor: Color {
        switch self {
        case .submitted:
            return Color.orange.opacity(0.2)
        case .replied:
            return Color.green.opacity(0.2)
        case .processing:
            return Color.blue.opacity(0.2)
        }
    }
    
    var textColor: Color {
        switch self {
        case .submitted:
            return Color.orange
        case .replied:
            return Color.green
        case .processing:
            return Color.blue
        }
    }
}

// 反馈记录数据模型
struct FeedbackRecord: Identifiable {
    let id: String
    let types: [String]
    let content: String
    let datetime: String
    let status: FeedbackStatus
}

