import SwiftUI
import UIKit
import Combine

// 添加返回通知名称
//extension Notification.Name {
//    static let navigateBack = Notification.Name("navigateBackFromGoalSetting")
//}


class GoalSettingViewModel: ObservableObject {
    @Published var sleepHours: Int = 0
    @Published var sleepMinutes: Int = 0
    @Published var stepGoal: Int = 0
    @Published var calorieGoal: Int = 0
    @Published var activityHours: Int = 0
    @Published var activityMinutes: Int = 0
    
    @Published var isLoading = false
    @Published var errorMessage: String?

    private var cancellables = Set<AnyCancellable>()
    private let apiService = UserAPIService(networkManager: NetworkManager.shared)

    func fetchGoals() {
        isLoading = true
        apiService.getMemberGoal()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.errorMessage = error.localizedDescription
                    "\(error.localizedDescription)".showToast()
                }
            } receiveValue: { [weak self] goal in
                guard let self = self else { return }
                let (sleepHours, sleepMinutes) = goal.sleepTime.convertSecondsToHourMinute()
                let (activityHours, activityMinutes) = goal.activityDuration.convertSecondsToHourMinute()
                self.sleepHours = sleepHours
                self.sleepMinutes = sleepMinutes
                self.stepGoal = goal.steps
                self.calorieGoal = goal.calories
                self.activityHours = activityHours
                self.activityMinutes = activityMinutes
            }
            .store(in: &cancellables)
    }
    
    func updateGoals() {
        isLoading = true
        let sleepTotalMinutes = sleepHours * 3600 + sleepMinutes * 60
        let activityTotalMinutes = activityHours * 3600 + activityMinutes * 60
        
        let dict = ["sleepTime":sleepTotalMinutes,
                    "steps":stepGoal,
                    "calories":calorieGoal,
                    "activityDuration":activityTotalMinutes]
        
        apiService.updateMemberGoal(goal: dict)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                 if case .failure(let error) = completion {
                    self?.errorMessage = error.localizedDescription
                    "\(error.localizedDescription)".showToast()
                }
            } receiveValue: { response in
                if response.code == 0 {
//                    "Goals updated successfully".showToast()
                } else {
                    "\(response.msg ?? "Unknown error")".showToast()
                }
            }
            .store(in: &cancellables)
    }
}

struct GoalSettingScreen: View {
    // 导航环境变量
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel = GoalSettingViewModel()
    
    // 控制底部表单显示
    @State private var showSleepGoalSheet = false
    @State private var showStepGoalSheet = false
    @State private var showCalorieGoalSheet = false
    @State private var showDurationGoalSheet = false
    
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色 - 使用深色背景
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            // 使用封装的光晕背景组件
            CornerGlowBackground()
            
            VStack(spacing: 0) {
                // 导航栏
                CommonNavigationBar(
                    title: "goal_settings_title".localized,
                    onBackButtonTapped: {
                        // 使用多种方式尝试返回
                        NotificationCenter.default.post(name: .navigateBack, object: nil)
                        DispatchQueue.main.async {
                            presentationMode.wrappedValue.dismiss()
                        }
                    }
                )
                
                // 网格布局（可滚动）
                ScrollView {
                    VStack(spacing: 16) {
                        HStack(spacing: 16) {
                            // 睡眠目标卡片
                            fixedSizeGoalCard(
                                icon: "bed.double.fill",
                                iconColor: .indigo,
                                title: "goal_sleep_title".localized,
                                value: String(format: "goal_time_format".localized, viewModel.sleepHours, viewModel.sleepMinutes),
                                action: { showSleepGoalSheet = true },
                                customImage: "slices_13"
                            )
                            
                            // 步数目标卡片
                            fixedSizeGoalCard(
                                icon: "figure.walk",
                                iconColor: .white,
                                title: "goal_steps_title".localized,
                                value: "\(viewModel.stepGoal.formatted())",
                                action: { showStepGoalSheet = true },
                                customImage: "slices_14"
                            )
                        }
                        
                        HStack(spacing: 16) {
                            // 活动卡路里目标卡片
                            fixedSizeGoalCard(
                                icon: "flame.fill",
                                iconColor: .orange,
                                title: "goal_activity_calorie_title".localized,
                                value: String(format: "goal_calorie_format".localized, viewModel.calorieGoal.formatted()),
                                action: { showCalorieGoalSheet = true },
                                customImage: "slices_15"
                            )
                            
                            // 活动时长目标卡片
                            fixedSizeGoalCard(
                                icon: "clock.fill",
                                iconColor: .gray,
                                title: "goal_activity_duration_title".localized,
                                value: String(format: "goal_time_format".localized, viewModel.activityHours, viewModel.activityMinutes),
                                action: { showDurationGoalSheet = true },
                                customImage: "slices_16"
                            )
                        }
                        
                        // 添加额外的底部空间用于滚动
                        Spacer(minLength: 50)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 16)
                }
            }
            
            // 自定义模态视图 - 睡眠目标
            if showSleepGoalSheet {
                customModalView {
                    CustomSleepGoalView(
                        sleepHours: $viewModel.sleepHours,
                        sleepMinutes: $viewModel.sleepMinutes,
                        isPresented: $showSleepGoalSheet,
                        onConfirm: {
                            viewModel.updateGoals()
                        }
                    )
                }
            }
            
            // 自定义模态视图 - 步数目标
            if showStepGoalSheet {
                customModalView {
                    CustomStepGoalView(
                        stepGoal: $viewModel.stepGoal,
                        isPresented: $showStepGoalSheet,
                        onConfirm: {
                            viewModel.updateGoals()
                        }
                    )
                }
            }
            
            // 自定义模态视图 - 卡路里目标
            if showCalorieGoalSheet {
                customModalView {
                    CustomActivityCalorieGoalView(
                        calorieGoal: $viewModel.calorieGoal,
                        isPresented: $showCalorieGoalSheet,
                        onConfirm: {
                            viewModel.updateGoals()
                        }
                    )
                }
            }
            
            // 自定义模态视图 - 活动时长目标
            if showDurationGoalSheet {
                customModalView {
                    CustomActivityDurationGoalView(
                        activityHours: $viewModel.activityHours,
                        activityMinutes: $viewModel.activityMinutes,
                        isPresented: $showDurationGoalSheet,
                        onConfirm: {
                            viewModel.updateGoals()
                        }
                    )
                }
            }
            
            if viewModel.isLoading {
                ProgressView()
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            viewModel.fetchGoals()
        }
    }
    
    // 自定义模态容器视图
    private func customModalView<Content: View>(@ViewBuilder content: () -> Content) -> some View {
        ZStack {
            // 半透明背景遮罩
            Color.black.opacity(0.5)
                .edgesIgnoringSafeArea(.all)
                .transition(.opacity)
            
            // 模态内容
            VStack {
                Spacer()
                
                VStack {
                    content()
                }
                .padding(.bottom, 0)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color(red: 0.13, green: 0.15, blue: 0.22))
                )
                .padding(.horizontal, 0)
            }
            .edgesIgnoringSafeArea(.bottom)
            .transition(.move(edge: .bottom))
        }
        .animation(.easeInOut(duration: 0.3), value: true)
    }
    
    // 固定尺寸的目标卡片组件
    private func fixedSizeGoalCard(
        icon: String,
        iconColor: Color,
        title: String,
        value: String,
        action: @escaping () -> Void,
        customImage: String? = nil
    ) -> some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                HStack(spacing: 8) {
                    // 图标
                    if let customImage = customImage, UIImage(named: customImage) != nil {
                        // 使用自定义图片，仅当图片确实存在时，不添加外围圆圈
                        Image(customImage)
                            .resizable()
                            .scaledToFit()
                            .frame(width: 26, height: 26) // 设置宽高为26
                            .onAppear {
                                print("成功加载图片: \(customImage)")
                            }
                    } else {
                        // 使用系统图标（当自定义图片不存在时）
                        ZStack {
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(width: 36, height: 36)
                            
                            Image(systemName: icon)
                                .font(.system(size: 16))
                                .foregroundColor(iconColor)
                                .onAppear {
                                    if let customImage = customImage {
                                        print("图片加载失败，使用系统图标替代: \(customImage)")
                                    }
                                }
                        }
                    }
                    
                    // 使用自定义标题风格，针对Sleep Goals和Step Goals特殊处理
                    if title.contains("goal_sleep_title".localized) {
                        Text("goal_sleep_title".localized)
                            .font(.custom("PingFang-SC-Medium", size: 13))
                            .foregroundColor(.white)
                    } else if title.contains("goal_steps_title".localized) {
                        Text("goal_steps_title".localized)
                            .font(.custom("PingFang-SC-Medium", size: 13))
                            .foregroundColor(.white)
                    } else if title.contains("goal_activity_calorie_title".localized) {
                        Text("goal_activity_calorie_title".localized)
                            .font(.custom("PingFang-SC-Medium", size: 13))
                            .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
                            .fixedSize(horizontal: false, vertical: true)
                    } else if title.contains("goal_activity_duration_title".localized) {
                        Text("goal_activity_duration_title".localized)
                            .font(.custom("PingFang-SC-Medium", size: 13))
                            .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
                            .fixedSize(horizontal: false, vertical: true)
                    } else {
                        Text(title)
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                            .lineLimit(2)
                    }
                }
                
                HStack {
                    // 为所有目标卡片的值使用统一样式
                    Text(value)
                        .font(.custom("PingFang-SC-Medium", size: 13))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                        .font(.system(size: 14))
                }
            }
            .padding(16)
            .frame(width: UIScreen.main.bounds.width / 2 - 24, height: 95)
            .background(
                // 添加渐变背景
                LinearGradient(
                    gradient: Gradient(
                        colors: [
                            Color(red: 0.16, green: 0.19, blue: 0.25),
                            Color(red: 0.08, green: 0.09, blue: 0.13)
                        ]
                    ),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 睡眠目标自定义视图
struct CustomSleepGoalView: View {
    @Binding var sleepHours: Int
    @Binding var sleepMinutes: Int
    @Binding var isPresented: Bool
    var onConfirm: () -> Void
    
    @State private var tempHours: Int
    @State private var tempMinutes: Int
    
    init(sleepHours: Binding<Int>, sleepMinutes: Binding<Int>, isPresented: Binding<Bool>, onConfirm: @escaping () -> Void) {
        self._sleepHours = sleepHours
        self._sleepMinutes = sleepMinutes
        self._isPresented = isPresented
        self.onConfirm = onConfirm
        self._tempHours = State(initialValue: sleepHours.wrappedValue)
        self._tempMinutes = State(initialValue: sleepMinutes.wrappedValue)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                // 添加标识色块
                Rectangle()
                    .fill(Color(hex: "#0048B5"))
                    .frame(width: 3, height: 10)
                    .padding(.trailing, 5)
                
                Text("goal_sleep_title".localized)
                    .font(.custom("PingFang-SC-Medium", size: 16))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    isPresented = false
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            
            // 选择器
            HStack {
                // 小时选择
                Picker("Hours", selection: $tempHours) {
                    ForEach(0...23, id: \.self) { hour in
                        Text("\(hour)")
                            .foregroundColor(.white)
                            .tag(hour)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(width: 100)
                .clipped()
                
                Text("hr".localized)
                    .foregroundColor(.white)
                    .padding(.trailing, 20)
                
                // 分钟选择
                Picker("Minutes", selection: $tempMinutes) {
                    ForEach(Array(stride(from: 0, to: 60, by: 5)), id: \.self) { minute in
                        Text("\(minute)")
                            .foregroundColor(.white)
                            .tag(minute)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(width: 100)
                .clipped()
                
                Text("min".localized)
                    .foregroundColor(.white)
            }
            .padding(.vertical, 20)
            
            // 确认按钮
            Button(action: {
                sleepHours = tempHours
                sleepMinutes = tempMinutes
                isPresented = false
                onConfirm()
            }) {
                Text("confirm".localized)
                    .font(.custom("PingFang-SC-Medium", size: 16))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color(hex: "#0048B5").opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "#0048B5").opacity(0.9), lineWidth: 0.5)
                            )
                    )
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        .frame(height: 250)
    }
}

// 步数目标自定义视图
struct CustomStepGoalView: View {
    @Binding var stepGoal: Int
    @Binding var isPresented: Bool
    var onConfirm: () -> Void
    
    @State private var tempStepGoal: Int
    
    init(stepGoal: Binding<Int>, isPresented: Binding<Bool>, onConfirm: @escaping () -> Void) {
        self._stepGoal = stepGoal
        self._isPresented = isPresented
        self.onConfirm = onConfirm
        self._tempStepGoal = State(initialValue: stepGoal.wrappedValue)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                // 添加标识色块
                Rectangle()
                    .fill(Color(hex: "#0048B5"))
                    .frame(width: 3, height: 10)
                    .padding(.trailing, 5)
                
                Text("goal_steps_title".localized)
                    .font(.custom("PingFang-SC-Medium", size: 16))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    isPresented = false
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            
            // 步数选择
            Picker("Steps", selection: $tempStepGoal) {
                ForEach(Array(stride(from: 1000, to: 20001, by: 1000)), id: \.self) { steps in
                    Text("\(steps.formatted())")
                        .foregroundColor(.white)
                        .tag(steps)
                }
            }
            .pickerStyle(WheelPickerStyle())
            .padding(.vertical, 20)
            
            // 确认按钮
            Button(action: {
                stepGoal = tempStepGoal
                isPresented = false
                onConfirm()
            }) {
                Text("confirm".localized)
                    .font(.custom("PingFang-SC-Medium", size: 16))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color(hex: "#0048B5").opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "#0048B5").opacity(0.9), lineWidth: 0.5)
                            )
                    )
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        .frame(height: 300)
    }
}

// 卡路里目标自定义视图
struct CustomActivityCalorieGoalView: View {
    @Binding var calorieGoal: Int
    @Binding var isPresented: Bool
    var onConfirm: () -> Void
    
    @State private var tempCalorieGoal: Int
    
    init(calorieGoal: Binding<Int>, isPresented: Binding<Bool>, onConfirm: @escaping () -> Void) {
        self._calorieGoal = calorieGoal
        self._isPresented = isPresented
        self.onConfirm = onConfirm
        self._tempCalorieGoal = State(initialValue: calorieGoal.wrappedValue)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                // 添加标识色块
                Rectangle()
                    .fill(Color(hex: "#0048B5"))
                    .frame(width: 3, height: 10)
                    .padding(.trailing, 5)
                
                Text("goal_activity_calorie_popup_title".localized)
                    .font(.custom("PingFang-SC-Medium", size: 16))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    isPresented = false
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            
            // 卡路里选择
            Picker("Calories", selection: $tempCalorieGoal) {
                ForEach(Array(stride(from: 100, to: 1001, by: 50)), id: \.self) { calories in
                    Text(String(format: "goal_calorie_format".localized, calories.formatted()))
                        .foregroundColor(.white)
                        .tag(calories)
                }
            }
            .pickerStyle(WheelPickerStyle())
            .padding(.vertical, 20)
            
            // 确认按钮
            Button(action: {
                calorieGoal = tempCalorieGoal
                isPresented = false
                onConfirm()
            }) {
                Text("confirm".localized)
                    .font(.custom("PingFang-SC-Medium", size: 16))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color(hex: "#0048B5").opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "#0048B5").opacity(0.9), lineWidth: 0.5)
                            )
                    )
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        .frame(height: 300)
    }
}

// 活动时长目标自定义视图
struct CustomActivityDurationGoalView: View {
    @Binding var activityHours: Int
    @Binding var activityMinutes: Int
    @Binding var isPresented: Bool
    var onConfirm: () -> Void
    
    @State private var tempHours: Int
    @State private var tempMinutes: Int
    
    init(activityHours: Binding<Int>, activityMinutes: Binding<Int>, isPresented: Binding<Bool>, onConfirm: @escaping () -> Void) {
        self._activityHours = activityHours
        self._activityMinutes = activityMinutes
        self._isPresented = isPresented
        self.onConfirm = onConfirm
        self._tempHours = State(initialValue: activityHours.wrappedValue)
        self._tempMinutes = State(initialValue: activityMinutes.wrappedValue)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                // 添加标识色块
                Rectangle()
                    .fill(Color(hex: "#0048B5"))
                    .frame(width: 3, height: 10)
                    .padding(.trailing, 5)
                
                Text("goal_activity_duration_popup_title".localized)
                    .font(.custom("PingFang-SC-Medium", size: 16))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    isPresented = false
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            
            // 选择器
            HStack {
                // 小时选择
                Picker("Hours", selection: $tempHours) {
                    ForEach(0...23, id: \.self) { hour in
                        Text("\(hour)")
                            .foregroundColor(.white)
                            .tag(hour)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(width: 100)
                .clipped()
                
                Text("hr".localized)
                    .foregroundColor(.white)
                    .padding(.trailing, 20)
                
                // 分钟选择
                Picker("Minutes", selection: $tempMinutes) {
                    ForEach(Array(stride(from: 0, to: 60, by: 5)), id: \.self) { minute in
                        Text("\(minute)")
                            .foregroundColor(.white)
                            .tag(minute)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(width: 100)
                .clipped()
                
                Text("min".localized)
                    .foregroundColor(.white)
            }
            .padding(.vertical, 20)
            
            // 确认按钮
            Button(action: {
                activityHours = tempHours
                activityMinutes = tempMinutes
                isPresented = false
                onConfirm()
            }) {
                Text("confirm".localized)
                    .font(.custom("PingFang-SC-Medium", size: 16))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color(hex: "#0048B5").opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color(hex: "#0048B5").opacity(0.9), lineWidth: 0.5)
                            )
                    )
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        .frame(height: 250)
    }
}

// 预览
struct GoalSettingScreen_Previews: PreviewProvider {
    static var previews: some View {
        GoalSettingScreen()
            .preferredColorScheme(.dark)
    }
} 
