import SwiftUI

struct FriendDataDetailView: View {
    @Environment(\.presentationMode) var presentationMode
    let member: FamilyMemberData
    @State private var selectedTabIndex: Int

    private let tabTitles: [String] = ["sleep", "activity", "stress", "vital_signs"]

    init(member: FamilyMemberData, selectedTab: Int) {
        self.member = member
        self._selectedTabIndex = State(initialValue: selectedTab)
    }

    var body: some View {
        ZStack {
            Color(hex: "#1C1C1E").edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                // Custom Tab Bar
                HStack(spacing: 10) {
                    ForEach(tabTitles.indices, id: \.self) { index in
                        Button(action: {
                            withAnimation {
                                selectedTabIndex = index
                            }
                        }) {
                            Text(tabTitles[index].localized)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(selectedTabIndex == index ? .white : Color(white: 0.8))
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    ZStack {
                                        if selectedTabIndex == index {
                                            Capsule()
                                                .fill(Color(hex: "#007AFF"))
                                        }
                                    }
                                )
                        }
                    }
                    Spacer()
                }
                .padding(.horizontal)
                .padding(.vertical, 10)

                // Tab Content
                TabView(selection: $selectedTabIndex) {
                    // Replace with actual data views for each tab
                    Text("sleep_data_for".localized() + member.nickname)
                        .foregroundColor(.white)
                        .tag(0)
                    Text("activity_data_for".localized() + member.nickname)
                         .foregroundColor(.white)
                        .tag(1)
                    Text("stress_data_for".localized() + member.nickname)
                         .foregroundColor(.white)
                        .tag(2)
                    Text("vital_signs_data_for".localized() + member.nickname)
                         .foregroundColor(.white)
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))

                Spacer()
            }
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { presentationMode.wrappedValue.dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 17, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
            ToolbarItem(placement: .principal) {
                HStack {
                    Text("friend_data_title".localized)
                        .font(.headline)
                        .foregroundColor(.white)
                    Spacer()
                    // This should be a date picker
                    Text("2025.06.22~2025.06.28")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    Image(systemName: "chevron.down")
                        .foregroundColor(.gray)
                }
                .frame(width: UIScreen.main.bounds.width * 0.7) // Adjust width
            }
        }
    }
} 
