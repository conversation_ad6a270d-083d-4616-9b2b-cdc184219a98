import SwiftUI
import UIKit
import PhotosUI

/// 用户个人信息视图
struct UserProfileView: View {
    // MARK: - 属性
    @AppStorage("isLoggedIn") private var isLoggedIn: Bool = false
    @AppStorage("userMobile") private var userMobile: String = ""
    @AppStorage("userEmail") private var userEmail: String = ""
    @AppStorage("userName") private var userName: String = "Test User"
    @State private var isDeviceBound: Bool = false
    @Environment(\.colorScheme) private var colorScheme
    @State private var showDeveloperOptions: Bool = false
    @State private var developerTapCount: Int = 0
    @ObservedObject private var authService = AuthService.shared
    @StateObject private var deviceService = WindRingDeviceService.shared
    @State private var userProfileImage: UIKit.UIImage? = nil
    @State private var deviceStatusRefreshTrigger: Bool = false // 用于强制刷新UI的触发器
    
    // 主tab视图模型引用
    @EnvironmentObject private var mainTabViewModel: MainTabViewModel
    
    // 数据库调试相关
    @State private var showDatabaseDebugView: Bool = false
    
    // 添加状态刷新控制
    @State private var lastRefreshTimestamp = Date()
    @State private var isRefreshTimerActive = true
     
    // 测试账号信息
    private let testAccount = (mobile: "***********", email: "<EMAIL>", password: "123456", code: "1234")
    
    // MARK: - 视图体
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色
            Color(hex: "#070708")
                .edgesIgnoringSafeArea(.all)
            
            // 顶部背景图
            // Image("Review_bg")
            //     .resizable()
            //     .aspectRatio(contentMode: .fill)
            //     .frame(maxWidth: .infinity)
            //     .frame(height: 250) // 根据需要调整高度
            //     .edgesIgnoringSafeArea(.top)
            //     .position(x: UIScreen.main.bounds.width/2, y: 125) // 根据图片高度的一半进行定位
             // 顶部背景图 - 占据整个顶部区域
            VStack {
                Image("Review_bg")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 250)
                    .frame(maxWidth: .infinity)
                    .offset(y: 110)  // 向下偏移30像素
                    .edgesIgnoringSafeArea(.top)
                
                Spacer()
            }
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 用户头部区域 - 固定在顶部
                userHeaderView
                    .background(Color.clear) // 改为透明以显示背景图
                    .zIndex(1) // 确保显示在最前面
                
                // 可滚动内容
                ScrollView {
                    VStack(spacing: 10) { // 将间距设为20
                        // 添加padding以避免内容被头部遮挡
                        Color.clear.frame(height: 5) // 减少上方空白区域
                        
                        // 设备展示区域
                        deviceDisplayView
                        
                        // 分享卡片
                        shareCardView
                        
                        // 设置列表区域
                        settingsListView
                        
                        // 添加开发者工具区域 - 保留原有功能
                        if showDeveloperOptions {
                            developerToolsSection
                        }
                        
                        // 导航链接到数据库调试视图 - 保留原有功能
                        if showDatabaseDebugView {
                            let _ = navigateToDatabaseDebugView()
                        }
                        
                        Spacer(minLength: 80) // 为底部导航栏留出空间
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            // 如果用户已登录，获取最新的用户信息
            if (authService.currentToken != nil) {
                print("UserProfileView - onAppear: 开始获取用户信息")
                authService.getUserInfo { result in
                    switch result {
                    case .success(_):
                        print("UserProfileView - 用户信息获取成功，开始加载头像")
                        // 获取用户信息成功后再加载头像
                        self.loadUserAvatar()
                    case .failure(let error):
                        print("UserProfileView - 用户信息获取失败: \(error)")
                    }
                }
            }
            
            // 添加通知观察者
            setupNotifications()
            
            // 开启自动刷新计时器
//            startRefreshTimer()
        }
        .onDisappear {
            // 移除通知观察者
            NotificationCenter.default.removeObserver(self)
            
            // 停止自动刷新计时器
            isRefreshTimerActive = false
        }
//        .overlay(
//            // 底部导航栏
//            VStack {
//                Spacer()
//                bottomNavigationBar
//            }
//            .edgesIgnoringSafeArea(.bottom)
//        )
    }
    
    // MARK: - 用户头部区域
    private var userHeaderView: some View {
        VStack(spacing: 0) {
            // 用户信息区域
            HStack(alignment: .center, spacing: 10) { // 添加固定的小间距
                // 用户头像
                if let profileImage = userProfileImage {
                    Image(uiImage: profileImage)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 50, height: 50)
                        .clipShape(RoundedRectangle(cornerRadius: 10))
                } else {
                    Image(systemName: "person.crop.circle.fill")
                        .resizable()
                        .frame(width: 50, height: 50)
                        .foregroundColor(.white.opacity(0.8))
                        .clipShape(RoundedRectangle(cornerRadius: 10))
                }
                
                // 用户名和问候文本 - 紧贴头像
                VStack(alignment: .leading, spacing: 2) { // 减小文本间距
                    Text(authService.currentUser?.nickname ?? "")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text(greetingText())
                        .font(.body)
                        .foregroundColor(.white.opacity(0.9))
                }
                .padding(.leading, 0) // 确保没有额外的左边距
                
                Spacer()
                
                // 箭头图标 - 根据登录状态导航到不同页面
                if authService.currentToken != nil {
                    // 已登录 - 导航到用户信息页面
                    NavigationLink(destination: UserInformationView()) {
                        Image("right")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                            .foregroundColor(.white)
                            .padding(8) // 保留内边距
                    }
                } else {
                    // 未登录 - 导航到登录页面
                    NavigationLink(destination: LoginView()) {
                        Image("right")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                            .foregroundColor(.white)
                            .padding(8) // 保留内边距
                    }
                }
            }
            .padding(.horizontal, 16) // 减小水平内边距
            .padding(.vertical, 12) // 减小垂直内边距
        }
        .onAppear {
            // 添加返回通知观察者
            NotificationCenter.default.addObserver(forName: .navigateBack, object: nil, queue: .main) { _ in
                // 确保在主线程更新UI
                DispatchQueue.main.async {
                    if let navigationController = UIApplication.shared.windows.first?.rootViewController?.findNavigationController() {
                        navigationController.popViewController(animated: true)
                    }
                }
            }
        }
    }
    
    // MARK: - 设备展示区域
    private var deviceDisplayView: some View {
        VStack(spacing: 0) {
            // 创建一个独立的容器来放置戒指图片和解除绑定按钮
            VStack {
                // 设置宽高比例约束
                GeometryReader { geometry in
                    ZStack {
                        // 设备图片居中
                        Image("Ring")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 180, height: 180)
                            .frame(maxWidth: .infinity)
                            .padding(.top, 30)

                        // 如果已连接，在右侧显示解除绑定按钮
                        if deviceService.deviceInfo != nil  {
                            // 在左上角添加"Binding-State"和"connected".localized
                            if deviceService.deviceInfo != nil  {
                                VStack(alignment: .leading, spacing: 2) {
                                    Text("user_profile_binding_state".localized)
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.white)
                                    
                                    HStack(spacing: 4) {
                                        
                                        Circle()
                                            .fill(deviceService.connectionState.color)
                                            .frame(width: 8, height: 8)
                                        
                                        // 根据连接状态切换文字
                                        Text(deviceService.connectionState.description.localized)
                                            .font(.system(size: 14))
                                            .foregroundColor(.white)
                                    }
                                }
                                .position(x: geometry.size.width * 0.18, y: geometry.size.height * 0.5)
                            }
                            VStack {
                                Spacer()
                                
                                HStack {
                                    Spacer()
                                    
                                    Button(action: {
//                                        deviceService.setAutoReconnect(true)
                                        deviceService.disconnectDevice(isUnbinding: true)
                                        let generator = UINotificationFeedbackGenerator()
                                        generator.notificationOccurred(.success)
                                        print("解除绑定按钮被点击：已执行主动解绑操作")
                                    }) {
                                        Text("user_profile_unbind_button".localized)
                                            .font(.system(size: 14))
                                            .foregroundColor(.white)
                                            .frame(width: 69, height: 25)
                                            .background(Color(red: 0, green: 0.28, blue: 0.71))
                                            .cornerRadius(10)
                                    }
                                    .padding(.trailing, 20)
                                    .padding(.bottom, 105)
                                }
                            }
                        }
                    }
                }
                .frame(height: 220)
            }
            
            // 根据连接状态显示不同UI
            if deviceService.connectionState.isConnected {
                // 已连接状态 - 仅显示电池信息
                ChargingBannerView(batteryLevel: deviceService.batteryLevel, isCharging: deviceService.isCharging)
//                VStack(spacing: 1) {
//                    // 电池状态
////                    NavigationLink(destination: DeviceDetailView()) {
//                    HStack(spacing: 5) {
//                        // 电池图标
//                        HStack(spacing: 6) {
//                            Image(systemName: getBatteryIcon(level: deviceService.batteryLevel))
//                                .foregroundColor(getBatteryColor(level: deviceService.batteryLevel))
//                                .font(.system(size: 16))
//                        }
//                        
//                        Spacer()
//                        
//                        Text(String(format: "user_profile_battery_info".localized, deviceService.batteryLevel, 3))
//                            .font(.system(size: 14))
//                            .foregroundColor(.white)
//                        
//                    }
//                    .padding(.horizontal, 10)
//                    .padding(.vertical, 0)
//                    .frame(height: 43.5)
//                    .background(Color.white.opacity(0.05))
//                    .cornerRadius(10)
//                    .overlay(
//                        RoundedRectangle(cornerRadius: 10)
//                            .stroke(Color.white.opacity(0.2), lineWidth: 0.5)
//                    )
//                }
//                .padding(.horizontal, 10)
//                }
                
            } else {
                if deviceService.deviceInfo == nil {
                    // 未连接状态 - 显示配对按钮
                    NavigationLink(destination: DevicePairingView()) {
                        HStack {
                            Spacer()
                            
                            HStack(spacing: 4) {
                                Text("user_profile_start_pairing_button".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                                
                                Text(">")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            
                            Spacer()
                        }
                        .padding(.vertical, 11)
                        .frame(height: 43.5)
                        .frame(maxWidth: .infinity)
                        .background(Color.white.opacity(0.05))
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.white.opacity(0.2), lineWidth: 0.5)
                        )
                        .cornerRadius(10)
                        .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
                        .padding(.horizontal, 10)
                    }
                }
                
            }
        }
        .id(deviceStatusRefreshTrigger) // 使用ID确保状态变化时重新渲染
    }
    
    // MARK: - 已连接设备视图
    private var connectedDeviceView: some View {
        VStack(spacing: 15) {
            // 连接状态
            HStack {
                Text("user_profile_binding_state".localized)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
                
                // 状态指示
                HStack(spacing: 8) {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 8, height: 8)
                    
                    Text("connected".localized)
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 20)
            
            // 电池状态
            NavigationLink(destination: DeviceDetailView()) {
                HStack(spacing: 10) {
                    // 电池图标
                    HStack(spacing: 2) {
                        Rectangle()
                            .fill(Color.red)
                            .frame(width: 20, height: 15)
                        
                        Rectangle()
                            .fill(Color.orange)
                            .frame(width: 20, height: 15)
                        
                        Rectangle()
                            .fill(Color.green)
                            .frame(width: 20, height: 15)
                    }
                    
                    Text(String(format: "user_profile_battery_info".localized, deviceService.batteryLevel, 3))
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                .padding(.vertical, 11)
                .padding(.horizontal, 15)
                .background(Color.white.opacity(0.05))
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.white.opacity(0.2), lineWidth: 0.5)
                )
                .padding(.horizontal, 10)
            }
            
            // 解除绑定按钮
            Button(action: {
                deviceService.disconnectDevice()
            }) {
                Text("user_profile_unbind_button".localized)
                    .font(.system(size: 16))
                    .foregroundColor(.white)
                    .padding(.horizontal, 30)
                    .padding(.vertical, 8)
                    .background(Color.blue)
                    .cornerRadius(20)
            }
        }
    }
    
    // MARK: - 分享卡片
    private var shareCardView: some View {
//        SharingSettingsView
        NavigationLink(destination: FamilySharingView()) {
            HStack {
                VStack(alignment: .leading, spacing: 11) { // 调整标题和副标题之间的间距
                    Text("user_profile_sharing_title".localized)
                        .font(.custom("PingFang-SC-Bold", size: 15, relativeTo: .headline))
                        .foregroundColor(.white)
                    
                    Text("user_profile_sharing_subtitle".localized)
                        .font(.custom("PingFang-SC-Regular", size: 12, relativeTo: .footnote))
                        .foregroundColor(.white) // 纯白色，不透明
                        .frame(width: 250, height: 11.5, alignment: .leading) // 根据参考代码设置大小
                        .offset(x: 2, y: 0) // 微调位置，更接近参考代码中的坐标
                }
                .padding(.leading, 27) // 精确按照参考代码设置左边距
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.white.opacity(0.7))
                    .font(.system(size: 14))
                    .padding(.trailing, 16)
            }
            .padding(.top, 18) // 调整顶部内边距
            .padding(.bottom, 18) // 调整底部内边距
            .frame(maxWidth: .infinity) // 改为最大宽度自适应
            .frame(height: 77.5) // 保持固定高度
            .background(Color(red: 0.08, green: 0.09, blue: 0.13, opacity: 1)) // 使用参考代码中的颜色
            .cornerRadius(10)
            .padding(.horizontal, 10)
        }
    }
    
    // MARK: - 设置列表区域
    private var settingsListView: some View {
        VStack(spacing: 1) { // 设置垂直间距为1，为分隔线预留空间
            // 目标设置
            settingItemLink(icon: "target", title: "user_profile_goal_setting".localized, destination: AnyView(GoalSettingView()))
            
            Divider().background(Color.white.opacity(0.05)).frame(height: 1)
            
            // 使用指南
            settingItemLink(icon: "book.fill", title: "user_profile_usage_guide".localized, destination: AnyView(UserGuideView()))
            
            Divider().background(Color.white.opacity(0.05)).frame(height: 1)
            
            // 版本信息
            settingItemLink(icon: "arrow.triangle.2.circlepath", title: "user_profile_versioning".localized, destination: AnyView(VersioningView()))
            
            Divider().background(Color.white.opacity(0.05)).frame(height: 1)
            
            // 其他设置
            settingItemLink(icon: "gear", title: "user_profile_other_settings".localized, destination: AnyView(SystemSettingsView()))
            
            Divider().background(Color.white.opacity(0.05)).frame(height: 1)
            
            // 销售支持
            settingItemLink(icon: "headphones", title: "user_profile_sales_support".localized, destination: AnyView(SalesSupportView()))
            
            Divider().background(Color.white.opacity(0.05)).frame(height: 1)
            
            // 关于我们
            settingItemLink(icon: "info.circle", title: "user_profile_about_us".localized, destination: AnyView(AboutView()))//AboutView
            
            Divider().background(Color.white.opacity(0.05)).frame(height: 1)
            
//            #if DEBUG
//            // 添加开发者测试中心按钮
//            settingItemLink(icon: "hammer.circle.fill", title: "user_profile_dev_test_center".localized, destination: AnyView(DeveloperTestCenterView()))
//            
//            Divider().background(Color.white.opacity(0.05)).frame(height: 1)
//            #endif

            // 删除Sleep Data Test按钮
            
            // 保留开发者工具入口 - 可以保留但是隐藏
//            if case .authenticated = authService.authState {
                settingItemLink(icon: "hammer.fill", title: "user_profile_dev_tools".localized, destination: AnyView(DeveloperToolsListView()))
                    .opacity(0) // 隐藏但保留功能
                    .frame(height: 0)
//            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 399) // 调整总体高度：7个项目 * 55高度 + 7个分隔线 * 2高度
        .background(
            LinearGradient(
                gradient: Gradient(
                    colors: [
                        Color(hex: "#141821"),
                        Color(hex: "#2A3040")
                    ]
                ),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .cornerRadius(10)
        .padding(.horizontal, 10) // 左右边距设为10
    }
    
    // 设置项链接
    private func settingItemLink(icon: String, title: String, destination: AnyView) -> AnyView {
        if title == "user_profile_goal_setting".localized {
            // 特殊处理"Goal Setting"选项，使用slices_17图像
            return AnyView(
                NavigationLink(destination: GoalSettingScreen()) {
                    HStack(spacing: 12) {
                        // 使用slices_17图像替代系统图标
                        Image("slices_17")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                        
                        Text(title)
                            .foregroundColor(.white)
                            .font(.custom("PingFang-SC-Regular", size: 13))
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.white.opacity(0.6))
                            .font(.system(size: 14))
                    }
                    .frame(height: 55) // 固定每个项目高度为55，用于平均分布
                    .padding(.horizontal, 16)
                }
            )
        } else if title == "user_profile_usage_guide".localized {
            // 特殊处理"Usage Guide"选项，使用slices_18图像
            return AnyView(
                NavigationLink(destination: destination) {
                    HStack(spacing: 12) {
                        // 使用slices_18图像替代系统图标
                        Image("slices_18")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                        
                        Text(title)
                            .foregroundColor(.white)
                            .font(.custom("PingFang-SC-Regular", size: 13))
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.white.opacity(0.6))
                            .font(.system(size: 14))
                    }
                    .frame(height: 55) // 固定每个项目高度为55，用于平均分布
                    .padding(.horizontal, 16)
                }
            )
        } else if title == "user_profile_versioning".localized {
            // 特殊处理"Versioning"选项，使用slices_19图像
            return AnyView(
                NavigationLink(destination: destination) {
                    HStack(spacing: 12) {
                        // 使用slices_19图像替代系统图标
                        Image("slices_19")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                        
                        Text(title)
                            .foregroundColor(.white)
                            .font(.custom("PingFang-SC-Regular", size: 13))
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.white.opacity(0.6))
                            .font(.system(size: 14))
                    }
                    .frame(height: 55) // 固定每个项目高度为55，用于平均分布
                    .padding(.horizontal, 16)
                }
            )
        } else if title == "user_profile_other_settings".localized {
            // 特殊处理"Other Settings"选项，使用slices_20图像
            return AnyView(
                NavigationLink(destination: destination) {
                    HStack(spacing: 12) {
                        // 使用slices_20图像替代系统图标
                        Image("slices_20")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                        
                        Text(title)
                            .foregroundColor(.white)
                            .font(.custom("PingFang-SC-Regular", size: 13))
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.white.opacity(0.6))
                            .font(.system(size: 14))
                    }
                    .frame(height: 55) // 固定每个项目高度为55，用于平均分布
                    .padding(.horizontal, 16)
                }
            )
        } else if title == "user_profile_sales_support".localized {
            // 特殊处理"Sales Support"选项，使用slices_21图像
            return AnyView(
                NavigationLink(destination: destination) {
                    HStack(spacing: 12) {
                        // 使用slices_21图像替代系统图标
                        Image("slices_21")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                        
                        Text(title)
                            .foregroundColor(.white)
                            .font(.custom("PingFang-SC-Regular", size: 13))
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.white.opacity(0.6))
                            .font(.system(size: 14))
                    }
                    .frame(height: 55) // 固定每个项目高度为55，用于平均分布
                    .padding(.horizontal, 16)
                }
            )
        } else if title == "user_profile_about_us".localized {
            // 特殊处理"About Us"选项，使用slices_22图像
            return AnyView(
                NavigationLink(destination: destination) {
                    HStack(spacing: 12) {
                        // 使用slices_22图像替代系统图标
                        Image("slices_22")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                        
                        Text(title)
                            .foregroundColor(.white)
                            .font(.custom("PingFang-SC-Regular", size: 13))
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.white.opacity(0.6))
                            .font(.system(size: 14))
                    }
                    .frame(height: 55) // 固定每个项目高度为55，用于平均分布
                    .padding(.horizontal, 16)
                }
            )
        } else if title == "user_profile_dev_test_center".localized {
            // 特殊处理"开发者测试中心"选项
            return AnyView(
                NavigationLink(destination: destination) {
                    HStack(spacing: 12) {
                        // 使用锤子图标
                        Image(systemName: "hammer.circle.fill")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 16, height: 16)
                            .foregroundColor(.orange)
                        
                        Text(title)
                            .foregroundColor(.white)
                            .font(.custom("PingFang-SC-Regular", size: 13))
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.white.opacity(0.6))
                            .font(.system(size: 14))
                    }
                    .frame(height: 55) // 固定每个项目高度为55，用于平均分布
                    .padding(.horizontal, 16)
                }
            )
        } else {
            // 其他视图使用默认处理
            return AnyView(
                NavigationLink(destination: destination) {
                    settingItemRow(icon: icon, title: title)
                }
            )
        }
    }
    
    // 添加设置项行视图方法
    private func settingItemRow(icon: String, title: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.white)
                .font(.system(size: 17))
                .frame(width: 26)
            
            Text(title)
                .foregroundColor(.white)
                .font(.system(size: 17, weight: .regular))
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .foregroundColor(.white.opacity(0.6))
                .font(.system(size: 14))
        }
        .frame(height: 55) // 固定每个项目高度为55，用于平均分布
        .padding(.horizontal, 16)
    }
    
    // MARK: - 辅助方法
    
    // 根据时间生成问候语
    private func greetingText() -> String {
        let hour = Calendar.current.component(.hour, from: Date())
        if hour < 12 {
            return "user_profile_greeting_morning".localized
        } else if hour < 18 {
            return "user_profile_greeting_afternoon".localized
        } else {
            return "user_profile_greeting_evening".localized
        }
    }
    
    // 更新设备状态UI
    private func updateDeviceStatus() {
        // 触发UI刷新
        deviceStatusRefreshTrigger.toggle()
        print("🔄 设备状态更新: 连接状态=\(deviceService.connectionState.description)")
    }
    
    
    // MARK: - 设置通知观察者
    private func setupNotifications() {
        // 监听头像更新通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("UserAvatarUpdated"),
            object: nil,
            queue: .main
        ) { _ in
            // 重新加载用户头像
            self.loadUserAvatar()
        }
        
        // 设置设备状态监听
        setupDeviceStatusObserver()
    }
    
    // 添加设备连接状态改变的监听
    private func setupDeviceStatusObserver() {
        // 监听设备连接状态变化
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name.deviceConnected,
            object: nil,
            queue: .main
        ) { _ in
            print("👁️ UserProfileView接收到设备已连接通知")
            updateDeviceStatus()
        }
        
        // 监听设备断开连接通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name.deviceDisconnected,
            object: nil,
            queue: .main
        ) { _ in
            print("👁️ UserProfileView接收到设备已断开通知")
            updateDeviceStatus()
        }
        
        // 监听设备连接状态变化通知 - 更通用的通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("DeviceConnectionStateChanged"),
            object: nil,
            queue: .main
        ) { _ in
            print("👁️ UserProfileView接收到设备连接状态变化通知")
            updateDeviceStatus()
        }
    }
    
    // MARK: - 加载用户头像
    private func loadUserAvatar() {
        // 首先尝试从currentUser获取头像URL
        if let user = authService.currentUser {
            print("UserProfileView - 从currentUser获取到头像URL: \(user.avatar)")
            loadUserAvatarFromUrl(user.avatar)
            return
        }
        
        // 如果currentUser中没有头像URL，尝试从UserDefaults获取
        if let avatarUrl = UserDefaults.standard.string(forKey: "userAvatarUrl"), !avatarUrl.isEmpty {
            print("UserProfileView - 从UserDefaults获取到头像URL: \(avatarUrl)")
            loadUserAvatarFromUrl(avatarUrl)
            return
        }
        
        print("UserProfileView - 未找到头像URL")
    }
    
    // 从URL加载头像
    private func loadUserAvatarFromUrl(_ avatarUrl: String) {
        guard let url = URL(string: avatarUrl) else {
            print("UserProfileView - 无效的头像URL: \(avatarUrl)")
            return
        }
        
        print("UserProfileView - 开始从URL加载头像: \(avatarUrl)")
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                print("UserProfileView - 加载头像失败: \(error.localizedDescription)")
                return
            }
            
            guard let data = data, let image = UIKit.UIImage(data: data) else {
                print("UserProfileView - 无法将数据转换为图像")
                return
            }
            
            print("UserProfileView - 成功加载头像")
            
            DispatchQueue.main.async {
                self.userProfileImage = image
            }
        }.resume()
    }
    
    // MARK: - 绑定状态区域 (登录后)
    private var bindingStatusSection: some View {
        VStack(spacing: 0) {
            // 设备绑定状态区域
            VStack(alignment: .leading, spacing: 0) {
                // 绑定状态文本
                VStack(alignment: .leading, spacing: 2) {
                    Text("user_profile_binding_state".localized)
                        .font(.caption)
                        .foregroundColor(.gray)
                        .onAppear {
                            // 调试信息：每次视图出现时检查设备连接状态
                            print("🟢 绑定状态区域-onAppear: 连接状态=\(deviceService.connectionState.description), MAC=\(deviceService.lastConnectedDeviceMAC ?? "未知"), 自动重连=\(deviceService.autoReconnect)")
                            
                            // 区域显示时主动更新一次状态
                            updateDeviceStatus()
                        }
                    
                    Text(deviceService.connectionState.isConnected ? "user_profile_bound".localized : "user_profile_unbound".localized)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(deviceService.connectionState.isConnected ? .green : .primary)
                }
                .padding(.top, 16)
                .padding(.horizontal, 16)
                
                // 设备图像和按钮区域
                HStack(alignment: .center, spacing: 0) {
                    VStack(alignment: .leading, spacing: 12) {
                        // 根据设备连接状态显示不同的按钮
                        if deviceService.connectionState.isConnected {
                            // 设备已连接状态 - 显示设备设置按钮
                            NavigationLink(destination:
                                Group {
                                    if isLoggedIn {
                                        DeviceManagerView()
                                    } else {
                                        // 直接传递一个新实例，绕过登录检查
                                        DeviceManagerView()
                                            .onAppear {
                                                print("DEBUG: 未登录状态访问设备设置页面")
                                            }
                                    }
                                }
                            ) {
                                HStack {
                                    Text("user_profile_device_settings".localized)
                                        .fontWeight(.semibold)
                                        .frame(maxWidth: .infinity)
                                    
                                    if !isLoggedIn {
                                        Image(systemName: "lock.open.fill")
                                            .foregroundColor(.white.opacity(0.8))
                                            .font(.system(size: 10))
                                    }
                                }
                                .padding(.vertical, 12)
                                .background(Color(hex: "#FA6C2D"))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                            }
                            .frame(width: 150)
                            .accessibility(hint: Text("user_profile_guest_device_settings_hint".localized))
                            .buttonStyle(PlainButtonStyle()) // 确保按钮样式不会影响导航
                            
                            // 断开连接按钮
                            Button(action: {
                                // 断开设备连接
                                deviceService.disconnectDevice()
                                // 打印断开连接后的状态
                                print("🔴 断开连接按钮点击: 连接状态=\(deviceService.connectionState.description)")
                            }) {
                                Text("user_profile_disconnect_button".localized)
                                    .fontWeight(.semibold)
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(Color.moduleBackground)
                                    .foregroundColor(.red)
                                    .cornerRadius(8)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.red, lineWidth: 1)
                                    )
                            }
                            .frame(width: 150)
                        } else {
                            // 未连接状态 - 显示配对按钮
                            NavigationLink(destination: DevicePairingView()) {
                            Text("user_profile_start_pairing_button".localized)
                                .fontWeight(.semibold)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(Color(hex: "#FA6C2D"))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                            }
                            .frame(width: 150)
                        }
                        
                        // 关于我的戒指 - 无论连接状态都显示
                        NavigationLink(destination: DeviceDetailView()) {
                            HStack {
                                Text("user_profile_about_my_ring".localized)
                                    .font(.body)
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.gray)
                                    .font(.footnote)
                            }
                            .padding(.vertical, 12)
                        }
                    }
                    .padding(.leading, 16)
                    
                    Spacer()
                    
                    // 右侧显示设备图像和设备信息
                    VStack(alignment: .center, spacing: 4) {
                    // 智能环设备图像
                    Image("ring_device")
                        .resizable()
                        .scaledToFit()
                        .frame(maxWidth: .infinity)
                        .frame(maxHeight: 200)
                        .padding()
                        
                        // 如果设备已连接，显示设备电量信息
                        if deviceService.connectionState.isConnected {
                            HStack(spacing: 4) {
                                // 电池图标
                                HStack(spacing: 6) {
                                    Image(systemName: getBatteryIcon(level: deviceService.batteryLevel))
                                        .foregroundColor(getBatteryColor(level: deviceService.batteryLevel))
                                        .font(.system(size: 16))
                                }
                                
                                // 电池电量
                                Text("\(deviceService.batteryLevel)%")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.top, 4)
                        }
                    }
                        .padding(.trailing, 16)
                }
                .padding(.top, 12)
                .padding(.bottom, 16)
            }
        }
        .background(Color.clear)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
        .onAppear {
            // 当视图出现时，检查设备状态并更新UI
            updateDeviceStatus()
        }
        // 使用refreshTrigger、连接状态和时间戳作为id，强制刷新整个视图
        .id("bindingStatus-\(deviceService.connectionState.rawValue)-\(deviceStatusRefreshTrigger)-\(lastRefreshTimestamp.timeIntervalSince1970)")
    }
    
    // 获取电池图标
    private func getBatteryIcon(level: Int) -> String {
        if level <= 10 {
            return "battery.0"
        } else if level <= 25 {
            return "battery.25"
        } else if level <= 50 {
            return "battery.50"
        } else if level <= 75 {
            return "battery.75"
        } else {
            return "battery.100"
        }
    }
    
    // 获取电池颜色
    private func getBatteryColor(level: Int) -> Color {
        if level <= 20 {
            return .red
        } else if level <= 50 {
            return .orange
        } else {
            return .green
        }
    }
    
    // MARK: - 顶部登录区域 (未登录)
    private var loginSection: some View {
        VStack(spacing: 0) {
            // 登录按钮
            NavigationLink(destination: LoginView()) {
                HStack {
                    // 用户头像 - 使用实际图片
                    Image("login_icon") // 使用橙色半圆图标的实际图片
                        .resizable()
                        .scaledToFit()
                        .frame(width: 50, height: 50)
                        .padding(.trailing, 12)
                    
                    Text("login".localized)
                        .font(.title3)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
            
            // 设备绑定状态区域
            VStack(alignment: .leading, spacing: 0) {
                // 绑定状态文本
                VStack(alignment: .leading, spacing: 2) {
                    Text("user_profile_binding_state".localized)
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    Text(deviceService.connectionState.isConnected ? "user_profile_bound".localized : "user_profile_unbound".localized)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(deviceService.connectionState.isConnected ? .green : .primary)
                }
                .padding(.top, 16)
                .padding(.horizontal, 16)
                
                // 设备图像和按钮区域
                HStack(alignment: .center, spacing: 0) {
                    VStack(alignment: .leading, spacing: 12) {
                        // 根据设备连接状态显示不同的按钮
                        if deviceService.connectionState.isConnected {
                            // 设备已连接状态 - 显示设备设置按钮
                            NavigationLink(destination: LoginView()) {
                                Text("user_profile_device_settings".localized)
                                    .fontWeight(.semibold)
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(Color(hex: "#FA6C2D"))
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
                            .frame(width: 150)
                            
                            // 断开连接按钮
                            Button(action: {
                                // 断开设备连接
                                deviceService.disconnectDevice()
                            }) {
                                Text("user_profile_disconnect_button".localized)
                                    .fontWeight(.semibold)
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(Color.moduleBackground)
                                    .foregroundColor(.red)
                                    .cornerRadius(8)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.red, lineWidth: 1)
                                    )
                            }
                            .frame(width: 150)
                        } else {
                            // 未连接状态 - 显示配对按钮
                            NavigationLink(destination: DevicePairingView()) {
                            Text("user_profile_start_pairing_button".localized)
                                .fontWeight(.semibold)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(Color(hex: "#FA6C2D"))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                        }
                        .frame(width: 150)
                        }
                        
                        // 关于我的戒指 - 无论连接状态都显示
                        NavigationLink(destination: DeviceDetailView()) {
                            HStack {
                                Text("user_profile_about_my_ring".localized)
                                    .font(.body)
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.gray)
                                    .font(.footnote)
                            }
                            .padding(.vertical, 12)
                        }
                    }
                    .padding(.leading, 16)
                    
                    Spacer()
                    
                    // 右侧显示设备图像和设备信息
                    VStack(alignment: .center, spacing: 4) {
                        // 智能环设备图像
                        Image("ring_device")
                        .resizable()
                        .scaledToFit()
                        .frame(maxWidth: .infinity)
                        .frame(maxHeight: 200)
                        .padding()
                        
                        // 如果设备已连接，显示设备电量信息
                        if deviceService.connectionState.isConnected {
                            HStack(spacing: 6) {
                                Image(systemName: getBatteryIcon(level: deviceService.batteryLevel))
                                    .foregroundColor(getBatteryColor(level: deviceService.batteryLevel))
                                    .font(.system(size: 16))
                            }
                            .padding(.top, 4)
                        }
                    }
                        .padding(.trailing, 16)
                }
                .padding(.top, 12)
                .padding(.bottom, 16)
            }
        }
        .background(Color.clear)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
        .onAppear {
            // 当视图出现时，检查设备状态并更新UI
            updateDeviceStatus()
        }
    }
    
    // MARK: - 功能列表区域
    private var featureListSection: some View {
        VStack(spacing: 0) {
            // 共享卡片 - 直接导航到FamilySharingView
            authNavigationLink(icon: "square.and.arrow.up", title: "user_profile_sharing_title".localized, subtitle: "user_profile_sharing_subtitle".localized, destination: FamilySharingView())
            
            // 添加心率数据流程测试按钮
            Divider().padding(.leading, 56)
            
            Button(action: {
                // 发送通知以打开心率数据流程测试视图
                NotificationCenter.default.post(name: Notification.Name("OpenHeartRateFlowTest"), object: nil)
            }) {
                HStack {
                    Image(systemName: "heart.fill")
                        .resizable()
                        .frame(width: 24, height: 20)
                        .foregroundColor(.red)
                        .padding(.trailing, 16)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("user_profile_dev_hr_flow_test".localized)
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("dev_tools_hr_flow_test_subtitle".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                }
                .padding(16)
            }
            .background(Color.clear)
            
            // 注意：这里移除了步数API测试和睡眠API测试入口，因为已经整合到了历史数据页面的API测试中心
        }
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
    
    // MARK: - 开发者工具区域
    private var developerToolsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("user_profile_dev_tools_title".localized)
                .font(.headline)
                .padding(.horizontal)
            
            VStack {
                Button(action: {
                    mainTabViewModel.openStepDetailTest()
                }) {
                    HStack {
                        Image(systemName: "footprints.fill")
                            .foregroundColor(.blue)
                        Text("user_profile_dev_step_test".localized)
                            .foregroundColor(.primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal, 16)
                }
                
                Divider()
                
                Button(action: {
                    mainTabViewModel.openHeartRateFlowTest()
                }) {
                    HStack {
                        Image(systemName: "heart.fill")
                            .foregroundColor(.red)
                        Text("user_profile_dev_hr_flow_test".localized)
                            .foregroundColor(.primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal, 16)
                }
                
                Divider()
                
                Button(action: {
                    mainTabViewModel.openHeartRateDetailTest()
                }) {
                    HStack {
                        Image(systemName: "waveform.path.ecg")
                            .foregroundColor(.red)
                        Text("user_profile_dev_hr_detail_test".localized)
                            .foregroundColor(.primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal, 16)
                }
                
                Divider()
                
                Button(action: {
                    NotificationCenter.default.post(name: Notification.Name("OpenHRVDetailTest"), object: nil)
                }) {
                    HStack {
                        Image(systemName: "waveform.path.ecg.rectangle")
                            .foregroundColor(.purple)
                        Text("user_profile_dev_hrv_detail_test".localized)
                            .foregroundColor(.primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal, 16)
                }
                
                Divider()
                
                Button(action: {
                    mainTabViewModel.openDebugView()
                }) {
                    HStack {
                        Image(systemName: "ladybug.fill")
                            .foregroundColor(.purple)
                        Text("user_profile_dev_db_tools".localized)
                            .foregroundColor(.primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal, 16)
                }
            }
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
            .padding(.horizontal)
        }
    }
    
    // MARK: - 带认证检查的导航链接
    private func authNavigationLink<Destination: View>(icon: String, title: String, destination: Destination) -> some View {
        // 无需检查登录状态，直接导航到目标页面
        NavigationLink(destination: destination) {
            HStack(spacing: 16) {
                // 图标
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.primary)
                    .frame(width: 24, height: 24)
                
                // 标题
                Text(title)
                    .font(.body)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // 箭头图标
                Image(systemName: "chevron.right")
                    .foregroundColor(.gray)
            }
            .padding(.vertical, 16)
            .padding(.horizontal)
        }
    }
    
    // MARK: - 带认证检查和副标题的导航链接
    private func authNavigationLink<Destination: View>(icon: String, title: String, subtitle: String, destination: Destination) -> some View {
        // 无需检查登录状态，直接导航到目标页面
        NavigationLink(destination: destination) {
            HStack {
                VStack(alignment: .leading, spacing: 6) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.gray)
            }
            .padding()
        }
    }
    
    // MARK: - 快速测试登录
    private func quickLogin() {
        isLoggedIn = true
        userMobile = userName // 使用用户昵称而不是手机号
        // userName = "Test User" // 这行已经在属性声明中设置了默认值
    }
    
    // MARK: - 设备配对按钮
    private func authDeviceButton<Destination: View>(title: String, destination: Destination) -> some View {
        // 无需检查登录状态，直接导航到目标页面
        NavigationLink(destination: destination) {
            Text(title)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color(hex: "#FA6C2D"))
                .foregroundColor(.white)
                .cornerRadius(8)
        }
    }
    
    // MARK: - 关于戒指链接
    private func authAboutRingLink<Destination: View>(destination: Destination) -> some View {
        // 无需检查登录状态，直接导航到目标页面
        NavigationLink(destination: destination) {
            HStack {
                Text("About My Ring")
                    .font(.body)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.gray)
                    .font(.footnote)
            }
            .padding(.vertical, 12)
        }
    }
    
    // MARK: - 方法
    
    // 导航到数据库调试视图
    private func navigateToDatabaseDebugView() {
        // 使用通知中心发布调试视图请求
        NotificationCenter.default.post(name: Notification.Name("OpenDatabaseDebugView"), object: nil)
        print("已请求打开数据库调试视图")
        // 重置标志
        showDatabaseDebugView = false
    }
    
    // 设置用户头像
    private func setUserAvatar(with image: UIKit.UIImage) {
        // ... existing code ...
    }
}

// MARK: - 子页面视图
struct EditProfileView: View {
    @AppStorage("userName") private var userName: String = "Test User"
    @State private var editedName: String = ""
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        Form {
            Section(header: Text("edit_profile_section_header".localized)) {
                TextField("edit_profile_name_placeholder".localized, text: $editedName)
            }
            
            Section {
                Button(action: {
                    if !editedName.isEmpty {
                        userName = editedName
                    }
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Text("edit_profile_save_button".localized)
                        .frame(maxWidth: .infinity)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color(hex: "#FA6C2D"))
                        .cornerRadius(8)
                }
            }
        }
        .navigationTitle("edit_profile_title".localized)
        .onAppear {
            editedName = userName
        }
    }
}

struct AboutRingView: View {
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Image("ring_device")
                    .resizable()
                    .scaledToFit()
                    .frame(maxWidth: .infinity)
                    .frame(maxHeight: 200)
                    .padding()
                
                VStack(alignment: .leading, spacing: 12) {
                    Text("about_ring_device_name".localized)
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("about_ring_device_description".localized)
                        .font(.body)
                }
                .padding(.horizontal)
                
                Divider()
                
                VStack(alignment: .leading, spacing: 12) {
                    Text("about_ring_key_features".localized)
                        .font(.headline)
                        .padding(.horizontal)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        featureRow(icon: "heart.fill", title: "about_ring_feature_hr".localized, description: "about_ring_feature_hr_desc".localized)
                        featureRow(icon: "zzz", title: "about_ring_feature_sleep".localized, description: "about_ring_feature_sleep_desc".localized)
                        featureRow(icon: "figure.walk", title: "about_ring_feature_activity".localized, description: "about_ring_feature_activity_desc".localized)
                        featureRow(icon: "waveform.path.ecg", title: "about_ring_feature_health_metrics".localized, description: "about_ring_feature_health_metrics_desc".localized)
                    }
                    .padding(.horizontal)
                }
                
                Divider()
                
                VStack(alignment: .leading, spacing: 12) {
                    Text("about_ring_tech_specs".localized)
                        .font(.headline)
                        .padding(.horizontal)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        specRow(title: "about_ring_spec_battery".localized, value: "about_ring_spec_battery_value".localized)
                        specRow(title: "about_ring_spec_water_resistance".localized, value: "about_ring_spec_water_resistance_value".localized)
                        specRow(title: "about_ring_spec_sensors".localized, value: "about_ring_spec_sensors_value".localized)
                        specRow(title: "about_ring_spec_connectivity".localized, value: "about_ring_spec_connectivity_value".localized)
                    }
                    .padding(.horizontal)
                }
            }
            .padding(.bottom, 40)
        }
        .navigationTitle("about_ring_title".localized)
    }
    
    private func featureRow(icon: String, title: String, description: String) -> some View {
        HStack(alignment: .top, spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(Color(hex: "#FA6C2D"))
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
    
    private func specRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
        }
        .padding(.vertical, 4)
    }
}

struct SharingView: View {
    var body: some View {
        VStack {
            // Header image or icon
            Image(systemName: "heart.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(Color(hex: "#FA6C2D"))
                .padding(.top, 20)
            
            Text("sharing_title".localized)
                .font(.title)
                .fontWeight(.bold)
                .padding(.top, 10)
            
            Text("sharing_subtitle".localized)
                .font(.subheadline)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal, 20)
                .padding(.top, 5)
            
            Spacer()
            
            NavigationLink(destination: FamilySharingView()) {
                HStack {
                    Image(systemName: "person.2.fill")
                        .foregroundColor(.white)
                    Text("sharing_family_sharing_button".localized)
                        .foregroundColor(.white)
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(hex: "#FA6C2D"))
                .cornerRadius(10)
                .padding(.horizontal)
            }
            
            Button(action: {
                // Future implementation for other sharing options
            }) {
                HStack {
                    Image(systemName: "link.circle.fill")
                        .foregroundColor(Color(hex: "#FA6C2D"))
                    Text("sharing_personal_data_sharing_button".localized)
                        .foregroundColor(.primary)
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(10)
                .padding(.horizontal)
            }
            
            Spacer()
        }
        .padding(.bottom, 40)
        .navigationTitle("sharing_title".localized)
        .navigationBarTitleDisplayMode(.inline)
    }
}
struct ChargingBannerView: View {
    let batteryLevel: Int
    let isCharging: Bool

    var body: some View {
        HStack(spacing: 8) {
            // 电池图标
            Image(systemName: batteryIconName)
                .foregroundColor(.green)
                .font(.system(size: 18, weight: .semibold))
            let charging = ", it's charging now"
            // 文字描述
            Text("\(batteryLevel)% charge \(isCharging ? charging : "")")
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white)

            Spacer(minLength: 0)
        }
        .padding(.horizontal, 16)
        .frame(height: 48)
        .background(Color.white.opacity(0.08))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.white.opacity(0.15), lineWidth: 0.5)
        )
        .cornerRadius(12)
        .padding(.horizontal, 20)
    }

    private var batteryIconName: String {
        if isCharging {
            return "battery.100.bolt"
        } else {
            switch batteryLevel {
            case 0..<20:
                return "battery.25"
            case 20..<50:
                return "battery.50"
            case 50..<80:
                return "battery.75"
            default:
                return "battery.100"
            }
        }
    }
}

// MARK: - 分享弹框视图
struct ShareSheetView: View {
    @Binding var isPresented: Bool
    let shareLink: String
    
    var body: some View {
        // 使用Material背景的TabView作为显示底板
        ZStack {
            // 半透明黑色背景
            Rectangle()
                .fill(Color.black.opacity(0.5))
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    isPresented = false
                }
            
            // 白色弹框容器
            VStack(spacing: 0) {
                // 关闭按钮区域
                HStack {
                    Spacer()
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.gray)
                            .padding(12)
                    }
                }
                
                // 链接图标
                Circle()
                    .fill(Color(UIColor.systemGray5))
                    .frame(width: 70, height: 70)
                    .overlay(
                        Image(systemName: "link")
                            .font(.system(size: 28))
                            .foregroundColor(Color(hex: "#FA6C2D"))
                    )
                    .padding(.top, 10)
                
                // 链接标题
                Text("【WindRing】开启亲友关怀之旅:")
                    .font(.headline)
                    .padding(.top, 16)
                    .padding(.bottom, 10)
                
                // 链接URL区域
                HStack {
                    Text(shareLink)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .lineLimit(1)
                        .truncationMode(.middle)
                        .padding(.leading, 10)
                    
                    Spacer()
                    
                    Button(action: {
                        UIPasteboard.general.string = shareLink
                    }) {
                        Image(systemName: "doc.on.doc")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .padding(8)
                    }
                    .background(Color(hex: "#FA6C2D"))
                    .cornerRadius(6)
                    .padding(.trailing, 8)
                }
                .padding(8)
                .background(Color(UIColor.systemGray6))
                .cornerRadius(8)
                .padding(.horizontal, 20)
                
                // 分享到文本
                Text("分享到")
                    .font(.footnote)
                    .foregroundColor(.secondary)
                    .padding(.top, 20)
                
                // 分享选项区域
                HStack(spacing: 30) {
                    shareButton(name: "share_sheet_wechat_friends".localized, color: .green, icon: "person.circle.fill")
                    shareButton(name: "share_sheet_wechat_favorites".localized, color: .yellow, icon: "square.grid.2x2.fill")
                    shareButton(name: "share_sheet_qq_friends".localized, color: .blue, icon: "bubble.left.fill")
                    shareButton(name: "share_sheet_qq_mail".localized, color: .orange, icon: "envelope.fill")
                }
                .padding(.vertical, 16)
            }
            .background(Color.white)
            .cornerRadius(16)
            .frame(width: UIScreen.main.bounds.width * 0.85)
            .frame(height: 385) // 保留固定高度
            .shadow(radius: 10)
        }
    }
    
    // 分享按钮组件
    private func shareButton(name: String, color: Color, icon: String) -> some View {
        VStack {
            ZStack {
                Circle()
                    .fill(color)
                    .frame(width: 50, height: 50)
                
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(.white)
            }
            
            Text(name)
                .font(.caption)
                .foregroundColor(.black)
        }
    }
}

// 自定义圆角形状
struct CustomCorners: Shape {
    var corners: UIRectCorner
    var radius: CGFloat
    
    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// 版本信息数据模型
struct VersionInfo: Identifiable {
    let id = UUID()
    let version: String
    let date: String
    let changes: [String]
}

// 功能信息数据模型
struct FeatureInfo: Identifiable {
    let id = UUID()
    let title: String
    let description: String
}

// MARK: - 预览
struct UserProfileView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            UserProfileView()
                .preferredColorScheme(.light)
            
            UserProfileView()
                .preferredColorScheme(.dark)
        }
    }
}





// 添加AnyView扩展方法
extension AnyView {
    func extractWrappedValue<Content: View>() -> Content? {
        let mirror = Mirror(reflecting: self)
        if let storage = mirror.children.first?.value,
           let storageMirror = Mirror(reflecting: storage).children.first?.value,
           let content = storageMirror as? Content {
            return content
        }
        return nil
    }
}

// 首先添加一个自定义形状来实现只对顶部圆角
struct CustomCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}

