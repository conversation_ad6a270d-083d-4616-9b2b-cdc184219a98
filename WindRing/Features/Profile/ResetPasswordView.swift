import SwiftUI
import UIKit

struct ResetPasswordView: View {
    // MARK: - State properties
    @State private var resetMethod: ResetMethod = .mobile
    @State private var mobile: String = ""
    @State private var email: String = ""
    @State private var newPassword: String = ""
    @State private var confirmPassword: String = ""
    @State private var verificationCode: String = ""
    @State private var isCountingDown: Bool = false
    @State private var countdown: Int = 60
    @State private var timer: Timer? = nil
    @State private var showErrorMessage: Bool = false
    @State private var errorMessage: String = ""
    @State private var errorTitle: String = "Password Reset Failed"
    @State private var showLoginLink: Bool = false
    @State private var isLoading: Bool = false
    @State private var currentStep: ResetStep = .verifyAccount
    @State private var codeSentSuccessfully: Bool = false
    @State private var showSetPasswordView: Bool = false
    @State private var verifiedAccount: String = ""
    @State private var selectedCountryCode: String = ""
    
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss
    var onResetSuccess: (() -> Void)?
    
    // MARK: - Enums
    enum ResetMethod {
        case mobile
        case email
    }
    
    enum ResetStep {
        case verifyAccount
        case resetPassword
        case success
    }
    
    // Form validation
    private var isMobileValid: Bool {
        mobile.count >= 5
    }
    
    private var isEmailValid: Bool {
        let emailRegex = #"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"#
        return NSPredicate(format: "SELF MATCHES %@", emailRegex).evaluate(with: email)
    }
    
    private var isPasswordValid: Bool {
        newPassword.count >= 6
    }
    
    private var isConfirmPasswordValid: Bool {
        confirmPassword == newPassword && !confirmPassword.isEmpty
    }
    
    private var isVerificationCodeValid: Bool {
        verificationCode.count == 6 && verificationCode.allSatisfy { $0.isNumber }
    }
    
    private var canGetCode: Bool {
        resetMethod == .mobile ? isMobileValid : isEmailValid
    }
    
    private var isFormValid: Bool {
        canGetCode && isVerificationCodeValid
    }
    
    // 深色主题颜色定义
    private var darkBackgroundColor: Color {
        Color(hex: "#151921")
    }
    
    private var inputBackgroundColor: Color {
        Color(red: 0.03, green: 0.03, blue: 0.03)
    }
    
    private var inputBorderColor: Color {
        Color(red: 0.26, green: 0.26, blue: 0.26)
    }
    
    private var primaryBlueColor: Color {
        Color(red: 0, green: 0.28, blue: 0.71)
    }
    
    private var successGreenColor: Color {
        Color(red: 0, green: 0.5, blue: 0.3)
    }
    
    // MARK: - Next button
    private var nextButton: some View {
        Button(action: handleNextButtonAction) {
            ZStack {
                RoundedRectangle(cornerRadius: 17.5)
                    .fill(Color(red: 0, green: 0.28, blue: 0.71))
                    .frame(height: 35)
                
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else {
                    Text("next".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            .frame(maxWidth: .infinity)
        }
        .disabled(!isFormValid || isLoading)
        .animation(.easeInOut(duration: 0.2), value: isFormValid)
        .animation(.easeInOut(duration: 0.2), value: isLoading)
    }
    
    // MARK: - Main view
    var body: some View {
        ZStack(alignment: .top) {
            Color(hex: "#151921").edgesIgnoringSafeArea(.all)
            
            backgroundHeader
            
            ScrollView {
                VStack(spacing: 0) {
                    Spacer().frame(height: 130)
                    
                    mainCard
                        .padding(.horizontal, 20)
                        .padding(.top, 10)
                    
                    Spacer()
                }
            }
        }
        .background(
            NavigationLink(destination: RegisterSettingPassword(account: verifiedAccount, code: verificationCode, viewMode: .resetPassword, onPasswordResetSuccess: onResetSuccess), isActive: $showSetPasswordView) { EmptyView() }
        )
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarBackButtonHidden(true)
        .toolbar(content: toolbarContent)
        .toolbarBackground(.hidden, for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
        .onAppear {
            showErrorMessage = false
            errorMessage = ""
            errorTitle = "Password Reset Failed"

            if selectedCountryCode.isEmpty {
                let langCode = Locale.current.language.languageCode?.identifier ?? "en"
                selectedCountryCode = langCode.lowercased().hasPrefix("en") ? "+1" : "+86"
            }
        }
        .onDisappear {
            timer?.invalidate()
            timer = nil
        }
    }
    
    private var backgroundHeader: some View {
            VStack(spacing: 0) {
                Image("Login_bg")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: UIScreen.main.bounds.width * 1.2)
                    .offset(y: -UIScreen.main.bounds.height * 0.01)
                    .opacity(0.8)
                
                Spacer()
            }
            .edgesIgnoringSafeArea(.top)
    }
    
    private var mainCard: some View {
                    VStack(spacing: 0) {
                                resetMethodPicker
                                    .padding(.horizontal, -44)
            
                            if codeSentSuccessfully {
                successBanner
            }
            
            formContent
                                    .padding(.top, 25)
                        
                        nextButton
                            .padding(.top, 40)
                    }
                    .padding(.horizontal, 48)
                    .padding(.bottom, 30)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color(red: 0.08, green: 0.1, blue: 0.13))
                    )
    }
    
    private var resetMethodPicker: some View {
        HStack(spacing: 0) {
            methodButton("login_mobile_tab_title".localized, for: .mobile)
            methodButton("login_email_tab_title".localized, for: .email)
        }
        .frame(height: 60)
        .background(
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    Spacer()
                    Rectangle()
                        .frame(height: 4)
                        .foregroundColor(.clear)
                        .overlay(
                            Rectangle()
                                .frame(width: 20, height: 4)
                                .foregroundColor(.white)
                                .cornerRadius(1)
                                .offset(x: resetMethod == .mobile ?
                                    -geometry.size.width/4 :
                                    geometry.size.width/4)
                                .animation(.easeInOut(duration: 0.3), value: resetMethod)
                        )
                }
            }
        )
        .background(
            Color(red: 0.12, green: 0.14, blue: 0.18)
                .clipShape(
                    RoundedCorner(radius: 25, corners: [.topLeft, .topRight])
                )
        )
    }
    
    @ViewBuilder
    private var formContent: some View {
        if resetMethod == .mobile {
            mobileResetForm
        } else {
            emailResetForm
                }
            }
    
    private var mobileResetForm: some View {
        VStack(spacing: 20) {
            mobileNumberInput
            verificationCodeInput
        }
    }
    
    private var emailResetForm: some View {
        VStack(spacing: 20) {
            emailInput
            verificationCodeInput
        }
    }
    
    private var mobileNumberInput: some View {
            HStack {
            countryCodePicker
                .padding(.leading, -16)
            Divider().background(Color.gray.opacity(0.5)).frame(height: 20)
                TextField("", text: $mobile)
                    .keyboardType(.phonePad)
                    .foregroundColor(.white)
                    .font(.system(size: 16))
                    .placeholder(when: mobile.isEmpty) {
                    Text("register_placeholder_mobile".localized)
                            .foregroundColor(Color(red: 0.4, green: 0.4, blue: 0.4, opacity: 1))
                            .font(.custom("PingFang-SC-Medium", size: 12))
                    }
            }
            .padding()
            .formFieldStyle()
    }
    
    private var emailInput: some View {
        TextField("", text: $email)
            .keyboardType(.emailAddress)
            .foregroundColor(.white)
            .placeholder(when: email.isEmpty) {
                Text("register_placeholder_email".localized).foregroundColor(.gray)
            }
            .padding()
            .formFieldStyle()
                }
    
    private var verificationCodeInput: some View {
            HStack {
                TextField("", text: $verificationCode)
                    .keyboardType(.numberPad)
                    .foregroundColor(.white)
                    .placeholder(when: verificationCode.isEmpty) {
                    Text("register_placeholder_code".localized).foregroundColor(.gray)
                }
                
                Spacer()
            Divider().background(Color.gray.opacity(0.5)).frame(height: 20).padding(.horizontal, 8)
                
            getCodeButton
        }
        .padding()
        .formFieldStyle()
                }
    
    private var countryCodePicker: some View {
        Menu {
            Button("+1") { selectedCountryCode = "+1" }
            Button("+86") { selectedCountryCode = "+86" }
        } label: {
            HStack(spacing: 4) {
                Text(selectedCountryCode)
                    .font(.custom("PingFang-SC-Medium", size: 13))
                    .foregroundColor(.white)
                    .frame(minWidth: 40, alignment: .center)
                Image(systemName: "chevron.down")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 10)
                        }
                    }
                
    private var getCodeButton: some View {
        Button(action: getVerificationCode) {
            Text(isCountingDown ? "\(countdown)s" : "register_button_get_code".localized)
                        .font(.custom("PingFang-SC-Medium", size: 12))
                        .foregroundColor(.white)
                }
                .padding(.trailing, 8)
        .disabled(isCountingDown || !canGetCode)
    }
    
    private var successBanner: some View {
        Text("code_sent_success".localized)
            .foregroundColor(.white)
            .font(.system(size: 14))
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color.green.opacity(0.7))
            .cornerRadius(10)
            .padding(.top, 25)
    }
    
    @ToolbarContentBuilder
    private func toolbarContent() -> some ToolbarContent {
        ToolbarItem(placement: .navigationBarLeading) {
            Button(action: { dismiss() }) {
                HStack(spacing: 4) {
                    Image(systemName: "chevron.left")
                    Text("reset_password_button_title".localized)
                        .font(.custom("PingFangSC-Heavy", size: 19))
                    }
                .foregroundColor(.white)
            }
        }
    }
    
    // MARK: - Methods
    /// Send verification code
    private func getVerificationCode() {
        guard canGetCode else {
            let message = resetMethod == .mobile ? "register_error_invalid_mobile" : "register_error_invalid_email"
            message.localized.showToast()
            return
        }
        
        startCountdown()
        
        let completionHandler: (Result<Bool, AuthError>) -> Void = { result in
            DispatchQueue.main.async {
            switch result {
            case .success:
                    self.codeSentSuccessfully = true
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                        withAnimation { self.codeSentSuccessfully = false }
                }
            case .failure(let error):
                    self.stopCountdown()
                    error.localizedDescription.showToast()
                }
            }
        }
        
        if resetMethod == .mobile {
            AuthService.shared.sendSMSCode(mobile: mobile, scene: 4, countryCode: selectedCountryCode, completion: completionHandler)
        } else {
            AuthService.shared.getEmailVerificationCode(email: email, scene: .forgotCredentials, completion: completionHandler)
        }
    }
    
    /// Start countdown
    private func startCountdown() {
        isCountingDown = true
        countdown = 60
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            if countdown > 0 {
                countdown -= 1
            } else {
                isCountingDown = false
                timer?.invalidate()
            }
            }
        }
        
    /// Stop countdown
    private func stopCountdown() {
        isCountingDown = false
        timer?.invalidate()
    }
    
    /// Verify account
    private func validateCode() {
        guard isFormValid else {
            "register_error_invalid_code".localized.showToast()
            return
        }
        
        isLoading = true
        
        let account = resetMethod == .mobile ? mobile : email
        let scene: Any = resetMethod == .mobile ? SmsSceneEnum.forgotPassword : EmailSceneEnum.forgotCredentials
        
        let completionHandler: (Result<Bool, AuthError>) -> Void = { result in
            DispatchQueue.main.async {
            self.isLoading = false
            switch result {
            case .success:
                self.verifiedAccount = account
                self.showSetPasswordView = true
            case .failure(let error):
                error.localizedDescription.showToast()
                }
            }
        }

        if resetMethod == .mobile {
            AuthService.shared.validateSmsCode(mobile: mobile, scene: .forgotPassword, code: verificationCode, completion: completionHandler)
        } else {
            AuthService.shared.validateEmailCode(email: email, scene: .forgotCredentials, code: verificationCode, completion: completionHandler)
        }
    }
    
    /// Handle next button action
    private func handleNextButtonAction() {
        switch currentStep {
        case .verifyAccount:
            validateCode()
        case .resetPassword:
            resetPassword()
        case .success:
            // 返回到登录页面
            dismiss()
        }
    }
    
    /// Reset password
    private func resetPassword() {
        // Validate form
        guard isPasswordValid && isConfirmPasswordValid else {
            if !isPasswordValid {
                errorMessage = "Password must be at least 6 characters"
            } else {
                errorMessage = "Passwords do not match"
            }
            errorMessage.showToast()
            return
        }
        
        // Show loading
        isLoading = true
        
        // Call reset password API
        let account = resetMethod == .mobile ? mobile : email
        
        // 模拟API调用延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            self.isLoading = false
            self.presentationMode.wrappedValue.dismiss()
        }
    }

    private func methodButton(_ title: String, for method: ResetMethod) -> some View {
        Button(action: { resetMethod = method }) {
            Text(title)
                .font(.custom("PingFangSC-Heavy", size: 14))
                .foregroundColor(resetMethod == method ? .white : .gray.opacity(0.7))
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
    }
}

fileprivate extension View {
    func formFieldStyle() -> some View {
        self.frame(height: 35)
            .background(Color(hex: "#070708").opacity(0.34))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color(hex: "#434242"), lineWidth: 0.5)
            )
    }
}

// MARK: - Preview
struct ResetPasswordView_Previews: PreviewProvider {
    static var previews: some View {
        ResetPasswordView()
    }
} 
