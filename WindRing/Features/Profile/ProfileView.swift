import SwiftUI
import CRPSmartRing
import UIKit

/// 个人资料视图 - 基于第一张图片的设计
struct ProfileView: View {
    // MARK: - 状态属性
    @StateObject private var deviceService = WindRingDeviceService.shared
    @State private var showLoginView = false
    @AppStorage("isLoggedIn") private var isLoggedIn: Bool = false
    @AppStorage("userName") private var userName: String = "User Name"
    @StateObject var profileViewModel = ProfileViewModel()
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 用户头部
                UserProfileHeaderView(
                    userName: userName,
                    greeting: "Good Morning."
                )
                
                // 戒指展示和配对按钮
                RingPairingView()
                
                // Sharing 卡片
                SharingCardView()
                
                // 设置列表
                SettingsListView()
                
                Spacer().frame(height: 70) // 为底部导航栏留出空间
            }
        }
        .background(Color(hex: "#0A1B4A"))
        .edgesIgnoringSafeArea(.all)
        .overlay(
            BottomNavigationBar(),
            alignment: .bottom
        )
        .sheet(isPresented: $showLoginView) {
            NavigationView {
//                LoginView(showLoginView: $showLoginView, username: $userName)
            }
        }
    }
    
    // MARK: - 睡眠数据测试函数
    private func testSleepDataUpload() {
        let sleepService = SleepUploadService.shared
        
        // 显示加载状态
        let alertController = UIAlertController(title: "测试中", message: "正在获取和上传睡眠数据...", preferredStyle: .alert)
        // 获取当前活动窗口的根视图控制器
        let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene
        let window = windowScene?.windows.first(where: { $0.isKeyWindow })
        window?.rootViewController?.present(alertController, animated: true)
        
        // 获取睡眠数据
        sleepService.fetchAndSaveBasicSleepHistory { count, error in
            // 更新警示框
            DispatchQueue.main.async {
                if let error = error {
                    alertController.message = "获取睡眠数据失败: \(error.localizedDescription)"
                    
                    // 3秒后自动关闭
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        alertController.dismiss(animated: true)
                    }
                } else {
                    alertController.message = "成功获取\(count)天睡眠数据，正在上传..."
                    
                    // 上传数据
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        sleepService.uploadPendingSleepData { uploadCount, uploadError in
                            DispatchQueue.main.async {
                                if let uploadError = uploadError {
                                    alertController.message = "获取成功，但上传失败: \(uploadError.localizedDescription)"
                                } else {
                                    alertController.message = "测试完成！获取\(count)天数据，成功上传\(uploadCount)天数据"
                                }
                                
                                // 3秒后自动关闭
                                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                                    alertController.dismiss(animated: true)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    /// 测试GoMore睡眠数据获取与上传
    private func testGoMoreSleepDataUpload() {
        let sleepService = SleepUploadService.shared
        let goMoreService = GoMoreService.shared
        
        // 显示加载状态
        let alertController = UIAlertController(title: "测试中", message: "正在检查GoMore支持状态...", preferredStyle: .alert)
        // 获取当前活动窗口的根视图控制器
        let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene
        let window = windowScene?.windows.first(where: { $0.isKeyWindow })
        window?.rootViewController?.present(alertController, animated: true)
        
        // 先检查设备是否支持GoMore算法
        goMoreService.checkGoMoreSupport { isSupported, error in
            DispatchQueue.main.async {
                if !isSupported {
                    alertController.message = "当前设备不支持GoMore算法，无法使用高级睡眠算法"
                    
                    // 3秒后自动关闭
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        alertController.dismiss(animated: true)
                    }
                    return
                }
                
                // 支持GoMore算法，继续测试
                alertController.message = "设备支持GoMore算法，正在获取睡眠数据..."
                
                // 获取GoMore睡眠数据
                sleepService.fetchAndSaveGoMoreSleepHistory { count, error in
                    DispatchQueue.main.async {
                        if let error = error {
                            alertController.message = "获取GoMore睡眠数据失败: \(error.localizedDescription)"
                            
                            // 3秒后自动关闭
                            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                                alertController.dismiss(animated: true)
                            }
                        } else {
                            alertController.message = "成功获取\(count)条GoMore睡眠数据，正在上传..."
                            
                            // 上传数据
                            sleepService.uploadPendingSleepData { uploadCount, uploadError in
                                DispatchQueue.main.async {
                                    if let uploadError = uploadError {
                                        alertController.message = "获取成功，但上传失败: \(uploadError.localizedDescription)"
                                    } else {
                                        alertController.message = "测试完成！获取\(count)条GoMore数据，成功上传\(uploadCount)条数据"
                                    }
                                    
                                    // 3秒后自动关闭
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                                        alertController.dismiss(animated: true)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    /// 强制上传所有睡眠数据
    private func forceUploadAllSleepData() {
        let sleepService = SleepUploadService.shared
        
        // 显示加载状态
        let alertController = UIAlertController(title: "测试中", message: "正在强制上传所有睡眠数据...", preferredStyle: .alert)
        // 获取当前活动窗口的根视图控制器
        let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene
        let window = windowScene?.windows.first(where: { $0.isKeyWindow })
        window?.rootViewController?.present(alertController, animated: true)
        
        // 强制上传所有睡眠数据
        sleepService.forceUploadAllSleepData { count, error in
            DispatchQueue.main.async {
                if let error = error {
                    alertController.message = "强制上传睡眠数据失败: \(error.localizedDescription)"
                } else {
                    alertController.message = "强制上传完成！成功上传\(count)条睡眠数据"
                }
                
                // 3秒后自动关闭
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    alertController.dismiss(animated: true)
                }
            }
        }
    }
}

// MARK: - 用户资料头部视图
struct UserProfileHeaderView: View {
    var userName: String
    var greeting: String
    
    var body: some View {
        VStack(spacing: 0) {
            // 状态栏区域
            HStack {
                // 时间
                Text(currentTime())
                    .font(.system(size: 36, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
                
                // 信号、WiFi和电池图标
                HStack(spacing: 6) {
                    Image(systemName: "cellularbars")
                        .foregroundColor(.white)
                    Image(systemName: "wifi")
                        .foregroundColor(.white)
                    Image(systemName: "battery.100")
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            // 用户信息
            HStack(spacing: 15) {
                // 用户头像
                ZStack {
                    // 使用一个示例图像作为用户头像
                    Image(systemName: "person.crop.square.fill")
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 65, height: 65)
                        .clipShape(RoundedRectangle(cornerRadius: 10))
                        .foregroundColor(.gray)
                }
                .padding(.leading, 20)
                
                // 用户名称和问候语
                VStack(alignment: .leading, spacing: 5) {
                    Text(userName)
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                    
                    Text(greeting)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                // 箭头
                Image(systemName: "chevron.right")
                    .foregroundColor(.white.opacity(0.6))
                    .padding(.trailing, 20)
            }
            .padding(.vertical, 20)
        }
        .background(Color(hex: "#0A1B4A"))
    }
    
    // 获取当前时间
    private func currentTime() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: Date())
    }
}

// MARK: - 戒指展示和配对视图
struct RingPairingView: View {
    var body: some View {
        VStack(spacing: 0) {
            // 戒指3D展示
            ZStack {
                // 背景网格
                GridBackgroundView()
                    .frame(height: 280)
                
                // 戒指3D模型
                Ring3DView()
            }
            .frame(maxWidth: .infinity)
            
            // 配对按钮
            Button(action: {
                // 配对操作
            }) {
                HStack {
                    Spacer()
                    Text("Start Pairing >")
                        .font(.headline)
                        .foregroundColor(.white)
                    Spacer()
                }
                .frame(height: 56)
                .background(Color.black.opacity(0.3))
                .cornerRadius(28)
                .overlay(
                    RoundedRectangle(cornerRadius: 28)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
                .padding(.horizontal, 20)
                .padding(.bottom, 5)
                .padding(.top, 15)
            }
        }
    }
}

// MARK: - 网格背景视图
struct GridBackgroundView: View {
    var body: some View {
        GeometryReader { geo in
            ZStack {
                // 深蓝色背景
                Color(hex: "#0A1B4A")
                
                // 网格线
                Path { path in
                    let width = geo.size.width
                    let height = geo.size.height
                    let gridSize: CGFloat = 40
                    
                    // 水平线
                    for y in stride(from: 0, to: height, by: gridSize) {
                        path.move(to: CGPoint(x: 0, y: y))
                        path.addLine(to: CGPoint(x: width, y: y))
                    }
                    
                    // 垂直线
                    for x in stride(from: 0, to: width, by: gridSize) {
                        path.move(to: CGPoint(x: x, y: 0))
                        path.addLine(to: CGPoint(x: x, y: height))
                    }
                }
                .stroke(Color.blue.opacity(0.2), lineWidth: 1)
            }
        }
    }
}

// MARK: - 戒指3D视图
struct Ring3DView: View {
    var body: some View {
        ZStack {
            // 戒指底座
            Ellipse()
                .fill(Color.black.opacity(0.5))
                .frame(width: 140, height: 40)
                .offset(y: 90)
                .blur(radius: 10)
            
            // 戒指主体
            ZStack {
                // 主体
                Circle()
                    .fill(Color.black)
                    .frame(width: 110, height: 110)
                
                // 内部电路板
                Circle()
                    .fill(Color.black)
                    .overlay(
                        ZStack {
                            // 电路元件模拟
                            Circle()
                                .fill(Color.yellow)
                                .frame(width: 15, height: 15)
                                .offset(x: -15, y: -10)
                            
                            Circle()
                                .fill(Color.yellow)
                                .frame(width: 10, height: 10)
                                .offset(x: 5, y: -15)
                            
                            Circle()
                                .fill(Color.yellow)
                                .frame(width: 18, height: 18)
                                .offset(x: 10, y: 10)
                            
                            // 一些小的电路组件
                            ForEach(0..<5) { i in
                                Rectangle()
                                    .fill(Color.yellow)
                                    .frame(width: 3, height: 3)
                                    .offset(
                                        x: CGFloat.random(in: -25...25),
                                        y: CGFloat.random(in: -25...25)
                                    )
                            }
                        }
                    )
                    .frame(width: 90, height: 90)
            }
            .rotationEffect(.degrees(30))
            .shadow(color: Color.black.opacity(0.5), radius: 10, x: 5, y: 5)
        }
    }
}

// MARK: - 分享卡片
struct SharingCardView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Sharing")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.gray)
            }
            
            Text("Love begins with safeguardinghealth")
                .font(.subheadline)
                .foregroundColor(.gray)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color(hex: "#131E36"))
        .cornerRadius(12)
        .padding(.horizontal, 15)
        .padding(.top, 5)
    }
}

// MARK: - 设置列表视图
struct SettingsListView: View {
    var body: some View {
        VStack(spacing: 0) {
            // 目标设置
            SettingItem(icon: "target", title: "Goal Setting")
            
            // 使用指南
            SettingItem(icon: "questionmark.circle", title: "Usage Guide")
            
            // 版本信息
            SettingItem(icon: "arrow.triangle.2.circlepath", title: "Versioning")
            
            // 其他设置
            SettingItem(icon: "gear", title: "Other Settings")
            
            // 销售支持
            SettingItem(icon: "headphones", title: "Sales Support")
            
            // 关于我们
            SettingItem(icon: "info.circle", title: "About Us")
            
            // 开发者工具入口 - 保留测试入口
            NavigationLink(destination: DeveloperToolsView()) {
                SettingItem(icon: "hammer.fill", title: "开发者工具", showNavigation: false)
            }
        }
        .background(Color(hex: "#131E36"))
        .cornerRadius(12)
        .padding(.horizontal, 15)
        .padding(.top, 5)
    }
}

// MARK: - 设置项
struct SettingItem: View {
    var icon: String
    var title: String
    var showNavigation: Bool = true
    
    var body: some View {
        HStack {
            // 图标容器
            ZStack {
                Circle()
                    .fill(Color.clear)
                    .frame(width: 36, height: 36)
                
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(.gray)
            }
            .padding(.leading, 15)
            
            // 标题
            Text(title)
                .font(.body)
                .foregroundColor(.white)
                .padding(.leading, 10)
            
            Spacer()
            
            if showNavigation {
                // 箭头
                Image(systemName: "chevron.right")
                    .foregroundColor(.gray)
                    .padding(.trailing, 15)
            }
        }
        .padding(.vertical, 16)
        .background(Color(hex: "#131E36"))
        
        if showNavigation {
            Divider()
                .background(Color.gray.opacity(0.3))
                .padding(.leading, 60)
        }
    }
}

// MARK: - 底部导航栏
struct BottomNavigationBar: View {
    var body: some View {
        HStack(spacing: 0) {
            // Insight
            NavBarItem(icon: "house.fill", title: "Insight", isSelected: false)
            
            // Review
            NavBarItem(icon: "chart.bar.fill", title: "Review", isSelected: false)
            
            // Mine - 当前选中
            NavBarItem(icon: "person.fill", title: "Mine", isSelected: true)
        }
        .frame(height: 70)
        .background(Color(hex: "#131E36"))
        .edgesIgnoringSafeArea(.bottom)
    }
}

// MARK: - 导航栏项
struct NavBarItem: View {
    var icon: String
    var title: String
    var isSelected: Bool
    
    var body: some View {
        VStack(spacing: 5) {
            Image(systemName: icon)
                .font(.system(size: 22))
                .foregroundColor(isSelected ? .white : .gray)
            
            Text(title)
                .font(.system(size: 12))
                .foregroundColor(isSelected ? .white : .gray)
        }
        .frame(maxWidth: .infinity)
    }
}

extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}

class ProfileViewModel: ObservableObject {
    @Published var isLoggedIn: Bool = WindRingUserService.shared.isLogin
    
    func logout() {
        WindRingUserService.shared.logout()
        isLoggedIn = false
    }
}

struct ProfileView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ProfileView()
        }
    }
} 
