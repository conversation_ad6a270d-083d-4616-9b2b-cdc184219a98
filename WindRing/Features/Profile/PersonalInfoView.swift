import SwiftUI
import UIKit

enum UnitSystem: Int, CaseIterable, Identifiable {
    case metric = 0
    case imperial = 1

    var id: Int { self.rawValue }

    var localized: String {
        switch self {
        case .metric:
            return "metric".localized
        case .imperial:
            return "imperial".localized
        }
    }
}


enum MeasurementType {
    case height
    case weight
}

struct PersonalInfoView: View {
    // MARK: - 状态属性
    @State private var name: String = ""
    @State private var birthDate: Date = Date()
    @State private var showDatePicker: Bool = false
    @State private var gender: String = "male"
    @State private var isLoading: Bool = false

    @State private var heightValue: Double = 170 // Store in cm
    @State private var weightValue: Double = 60  // Store in kg
    @State private var heightUnitSystem: UnitSystem = .metric
    @State private var weightUnitSystem: UnitSystem = .metric

    @State private var showHeightPicker = false
    @State private var showWeightPicker = false

    // 性别选项
    private let genderOptions = ["male", "female", "secrecy"]

    // 环境变量
    @Environment(\.dismiss) private var dismiss
    @Environment(\.presentationMode) private var presentationMode
    
    // 从上一个视图传递过来的数据
    let account: String
    let password: String
    let code: String
    var onRegistrationComplete: (() -> Void)?

    // MARK: - 主视图
    var body: some View {
        ZStack(alignment: .top) {
            // 深色背景
            Color(hex: "#151921")
                .edgesIgnoringSafeArea(.all)
            
            // 顶部背景图
            VStack(spacing: 0) {
                Image("Login_bg")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: UIScreen.main.bounds.width * 1.2)
                    .offset(y: -UIScreen.main.bounds.height * 0.01)
                    .opacity(0.8)
                
                Spacer()
            }
            .edgesIgnoringSafeArea(.top)
            
            // 主要内容
            ScrollView {
                VStack(spacing: 0) {
                    Spacer()
                        .frame(height: 130)
                    
                    // 卡片背景
                    VStack(spacing: 0) {
                        // 状态指示器
//                        Rectangle()
//                            .fill(Color(red: 0.2, green: 0.69, blue: 0.54))
//                            .frame(width: 279, height: 25)
//                            .cornerRadius(10)
//                            .overlay(
//                                Text("register_success_title".localized)
//                                    .foregroundColor(.white)
//                                    .font(.system(size: 14, weight: .medium))
//                            )
//                            .padding(.top, 20)
                        
                        // 标题
                        Text("personal_info_prompt".localized)
                            .font(.custom("SourceHanSansCN-Bold", size: 14))
                            .foregroundColor(.white)
                            .frame(width: 269, height: 14.5)
                            .padding(.top, 15)
                            .padding(.bottom, 30)
                        
                        // 表单内容
                        VStack(spacing: 20) {
                            // 姓名输入
                            formField(title: "username".localized, placeholder: "personal_info_name_placeholder".localized, text: $name, showDropdown: false)
                            
                            // 出生日期
                            datePickerField()
                            
                            // 性别选择
                            genderPickerField()
                            
                            // 身高输入
                            measurementField(title: "height".localized, value: heightValue, unitSystem: heightUnitSystem, type: .height) {
                                showHeightPicker = true
                            }
                            
                            // 体重输入
                            measurementField(title: "weight".localized, value: weightValue, unitSystem: weightUnitSystem, type: .weight) {
                                showWeightPicker = true
                            }
                            
                            // 隐私说明
                            Text("health_data_explanation".localized)
                                .foregroundColor(Color(red: 0.5, green: 0.5, blue: 0.5))
                                .font(.system(size: 14))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 30)
                                .padding(.top, 10)
                            
                            // 确认按钮
                            Button(action: savePersonalInfo) {
                                ZStack {
                                    RoundedRectangle(cornerRadius: 17.5)
                                        .fill(Color(red: 0, green: 0.28, blue: 0.71))
                                        .frame(width: 279.1, height: 35)
                                    
                                    if isLoading {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    } else {
                                        Text("confirm".localized)
                                            .font(.system(size: 16, weight: .medium))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                            .padding(.top, 20)
                            .padding(.bottom, 30)
                        }
                        .padding(.horizontal, 40)
                    }
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color(red: 0.08, green: 0.1, blue: 0.13))
                            .shadow(
                                color: Color.black.opacity(0.34),
                                radius: 11.5,
                                x: 0,
                                y: 0
                            )
                    )
                    .cornerRadius(25)
                    .padding(.horizontal, 30)
                }
            }
        }
        .sheet(isPresented: $showHeightPicker) {
            CustomPickerView(
                title: "height".localized,
                value: $heightValue,
                unitSystem: $heightUnitSystem,
                type: .height,
                isPresented: $showHeightPicker
            )
                .presentationDetents([.height(320)])
        }
        .sheet(isPresented: $showWeightPicker) {
            CustomPickerView(
                title: "weight".localized,
                value: $weightValue,
                unitSystem: $weightUnitSystem,
                type: .weight,
                isPresented: $showWeightPicker
            )
                .presentationDetents([.height(320)])
        }
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.white)
                }
            }
        }
        .toolbarBackground(.hidden, for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
    }

    // MARK: - 辅助视图
    
    @ViewBuilder
    private func datePickerField() -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("birthday".localized)
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white)
                .frame(width: 100, height: 10, alignment: .leading)
                .padding(.leading, 12)
            
            Button(action: { showDatePicker.toggle() }) {
                formContainer {
                    HStack {
                        Text(formatDate(birthDate))
                            .foregroundColor(.white)
                            .font(.custom("PingFang-SC-Medium", size: 12))
                            .padding(.leading, 12.5)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.down")
                            .foregroundColor(.gray)
                            .padding(.trailing, 16)
                    }
                }
            }
            .sheet(isPresented: $showDatePicker) {
                DatePicker("select_date".localized, selection: $birthDate, displayedComponents: .date)
                    .datePickerStyle(WheelDatePickerStyle())
                    .presentationDetents([.height(300)])
                    .background(Color(UIColor.systemBackground).ignoresSafeArea())
            }
        }
    }

    @ViewBuilder
    private func genderPickerField() -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("gender".localized)
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white)
                .frame(width: 100, height: 10, alignment: .leading)
                .padding(.leading, 12)
            
            Menu {
                ForEach(genderOptions, id: \.self) { option in
                    Button(option.localized) { gender = option }
                }
            } label: {
                formContainer {
                    HStack {
                        Text(gender.localized)
                            .foregroundColor(.white)
                            .font(.custom("PingFang-SC-Medium", size: 12))
                            .padding(.leading, 12.5)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.down")
                            .foregroundColor(.gray)
                            .padding(.trailing, 16)
                    }
                }
            }
        }
    }
    
    @ViewBuilder
    private func measurementField(title: String, value: Double, unitSystem: UnitSystem, type: MeasurementType, action: @escaping () -> Void) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white)
                .frame(width: 100, height: 10, alignment: .leading)
                .padding(.leading, 12)
            
            Button(action: action) {
                formContainer {
                    HStack {
                        Text(formattedMeasurement(value: value, unitSystem: unitSystem, type: type))
                            .foregroundColor(.white)
                            .font(.custom("PingFang-SC-Medium", size: 12))
                            .padding(.leading, 12.5)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.down")
                            .foregroundColor(.gray)
                            .padding(.trailing, 16)
                    }
                }
            }
        }
    }
    
    // 自定义表单字段
    private func formField(title: String, placeholder: String, text: Binding<String>, keyboardType: UIKeyboardType = .default, showDropdown: Bool = false) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white)
                .frame(width: 100, height: 10, alignment: .leading)
                .padding(.leading, 12)
            
            formContainer {
                HStack {
                    TextField("", text: text)
                        .foregroundColor(.white)
                        .font(.system(size: 16))
                        .keyboardType(keyboardType)
                        .padding(.leading, 16)
                        .frame(width: showDropdown ? 230 : 278.1)
                        .overlay(
                            HStack {
                                Text(placeholder)
                                    .foregroundColor(Color(red: 0.4, green: 0.4, blue: 0.4))
                                    .font(.custom("PingFang-SC-Medium", size: 12))
                                    .padding(.leading, 16)
                                Spacer()
                            }
                            .opacity(text.wrappedValue.isEmpty ? 1 : 0),
                            alignment: .leading
                        )
                    
                    if showDropdown {
                        Spacer()
                        Image(systemName: "chevron.down")
                            .foregroundColor(.gray)
                            .padding(.trailing, 16)
                    }
                }
            }
        }
    }

    private func formContainer<Content: View>(@ViewBuilder content: () -> Content) -> some View {
        ZStack {
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(red: 0.26, green: 0.26, blue: 0.26))
                .frame(width: 279.1, height: 35)
            
            RoundedRectangle(cornerRadius: 9.5)
                .fill(Color(red: 0.03, green: 0.03, blue: 0.03))
                .frame(width: 278.1, height: 34)
            
            content()
                .frame(width: 278.1)
        }
    }
    
    // MARK: - 辅助方法
    
    private func formattedMeasurement(value: Double, unitSystem: UnitSystem, type: MeasurementType) -> String {
        if type == .height {
            if unitSystem == .metric {
                return "\(Int(round(value))) \("unit_cm".localized)"
            } else {
                let totalInches = value / 2.54
                let feet = floor(totalInches / 12)
                let inches = round(totalInches.truncatingRemainder(dividingBy: 12))
                return "\(Int(feet)) \("unit_ft".localized) \(Int(inches)) in"
            }
        } else { // Weight
            if unitSystem == .metric {
                return "\(Int(round(value))) \("unit_kg".localized)"
            } else {
                let lbs = value * 2.20462
                return "\(Int(round(lbs))) \("unit_lbs".localized)"
            }
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    // 保存个人信息
    private func savePersonalInfo() {
        isLoading = true
        
        let sex: Int
        switch gender {
        case "male": sex = 1
        case "female": sex = 2
        default: sex = 0
        }
        
        let heightInCm = heightValue
        var finalHeight = heightValue
        if heightUnitSystem == .imperial {
            let totalInches = heightValue / 2.54
            let feet = floor(totalInches / 12)
            let inches = round(totalInches.truncatingRemainder(dividingBy: 12))
            finalHeight = feet * 12 + inches
        }
        
        let weightInKg = weightValue
        var finalWeight = weightValue
        if weightUnitSystem == .imperial {
            finalWeight = weightValue / 2.20462
        }

        AuthService.shared.registerWithProfile(
            account: account,
            password: password,
            confirmPassword: password, // Assuming confirm password is same as password
            code: code,
            nickname: name,
            avatar: "https://www.iocoder.cn/x.png",
            sex: sex,
            birthday: formatDate(birthDate),
            height: Int(round(finalHeight)),
            heightType: heightUnitSystem.rawValue,
            weight: Int(round(finalWeight)),
            weightType: weightUnitSystem.rawValue
        ) { result in
            isLoading = false
            switch result {
            case .success:
                print("Registration with profile successful!")
                "register_success_title".localized.showToast()
                DispatchQueue.main.async {
                    self.onRegistrationComplete?()
                }
            case .failure(let error):
                print("Registration failed: \(error.localizedDescription)")
                // Show an alert to the user
                error.localizedDescription.showToast()
            }
        }
    }
}

// MARK: - Custom Picker View
struct CustomPickerView: View {
    let title: String
    @Binding var value: Double
    @Binding var unitSystem: UnitSystem
    let type: MeasurementType
    @Binding var isPresented: Bool
    
    @State private var selectedUnit: UnitSystem
    @State private var selectedHeightCm: Int
    @State private var selectedHeightFeet: Int
    @State private var selectedHeightInches: Int
    @State private var selectedWeightKg: Int
    @State private var selectedWeightLbs: Int
    ///身高
    private let cmRange = 100...300
    private let feetRange = 3...8
    private let inchesRange = 0...11
    ///体重
    private let kgRange = 30...300
    private let lbsRange = 60...662

    init(title: String, value: Binding<Double>, unitSystem: Binding<UnitSystem>, type: MeasurementType, isPresented: Binding<Bool>) {
        self.title = title
        self._value = value
        self._unitSystem = unitSystem
        self.type = type
        self._isPresented = isPresented
        
        _selectedUnit = State(initialValue: unitSystem.wrappedValue)
        
        if type == .height {
            let currentCm = value.wrappedValue
            _selectedHeightCm = State(initialValue: Int(currentCm))
            
            let totalInches = currentCm / 2.54
            _selectedHeightFeet = State(initialValue: Int(floor(totalInches / 12)))
            _selectedHeightInches = State(initialValue: Int(round(totalInches.truncatingRemainder(dividingBy: 12))))
            
            _selectedWeightKg = State(initialValue: 60)
            _selectedWeightLbs = State(initialValue: 132)
        } else { // Weight
            let currentKg = value.wrappedValue
            _selectedWeightKg = State(initialValue: Int(currentKg))
            _selectedWeightLbs = State(initialValue: Int(currentKg * 2.20462))
            
            _selectedHeightCm = State(initialValue: 170)
            _selectedHeightFeet = State(initialValue: 5)
            _selectedHeightInches = State(initialValue: 7)
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            headerView
            
            unitPicker
            
            valuePicker
                .padding(.vertical)
            
            Spacer()
            
            confirmButton
        }
        .padding()
        .background(Color(hex: "#151921").edgesIgnoringSafeArea(.all))
        .colorScheme(.dark)
    }
    
    private var headerView: some View {
        HStack {
            Image(systemName: "circle.fill")
                .foregroundColor(.blue)
                .font(.caption)
            Text(title)
                .foregroundColor(.white)
                .font(.headline)
            Spacer()
            Button(action: { isPresented = false }) {
                Image(systemName: "xmark")
                    .foregroundColor(.white)
            }
        }
        .padding(.bottom)
    }
    
    private var unitPicker: some View {
        Picker("Unit", selection: $selectedUnit) {
            ForEach(UnitSystem.allCases) { unit in
                Text(unit == .metric ? (type == .height ? "cm" : "kg") : (type == .height ? "ft/in" : "lbs"))
                    .tag(unit)
            }
        }
        .pickerStyle(SegmentedPickerStyle())
    }
    
    @ViewBuilder
    private var valuePicker: some View {
        if type == .height {
            if selectedUnit == .metric {
                Picker("Height", selection: $selectedHeightCm) {
                    ForEach(cmRange, id: \.self) { cm in
                        Text("\(cm) cm").tag(cm)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(maxHeight: 150)
            } else {
                HStack {
                    Picker("Feet", selection: $selectedHeightFeet) {
                        ForEach(feetRange, id: \.self) { ft in
                            Text("\(ft) ft").tag(ft)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(width: 150)
                    
                    Picker("Inches", selection: $selectedHeightInches) {
                        ForEach(inchesRange, id: \.self) { inches in
                            Text("\(inches) in").tag(inches)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(width: 150)
                }
                .frame(maxHeight: 150)
            }
        } else { // Weight
            if selectedUnit == .metric {
                Picker("Weight", selection: $selectedWeightKg) {
                    ForEach(kgRange, id: \.self) { kg in
                        Text("\(kg) kg").tag(kg)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(maxHeight: 150)
            } else {
                Picker("Weight", selection: $selectedWeightLbs) {
                    ForEach(lbsRange, id: \.self) { lbs in
                        Text("\(lbs) lbs").tag(lbs)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(maxHeight: 150)
            }
        }
    }
    
    private var confirmButton: some View {
        Button(action: {
            unitSystem = selectedUnit
            if type == .height {
                if selectedUnit == .metric {
                    value = Double(selectedHeightCm)
                } else {
                    let totalInches = Double(selectedHeightFeet * 12 + selectedHeightInches)
                    value = totalInches * 2.54
                }
            } else { // Weight
                if selectedUnit == .metric {
                    value = Double(selectedWeightKg)
                } else {
                    value = Double(selectedWeightLbs) / 2.20462
                }
            }
            isPresented = false
        }) {
            Text("confirm".localized)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(10)
        }
    }
}

// Preview requires passing the necessary parameters
struct PersonalInfoView_Previews: PreviewProvider {
    static var previews: some View {
        PersonalInfoView(account: "12345", password: "pass", code: "1234", onRegistrationComplete: {})
    }
}
