//
//  RegisterSettingPassword.swift
//  WindRing
//
//  Created by zx on 2025/6/16.
//

import SwiftUI

#if canImport(UIKit)
import UIKit
#endif

struct RegisterSettingPassword: View {
    enum ViewMode {
        case registration
        case resetPassword
    }
    
    // MARK: - State properties
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var showPassword = false
    @State private var showConfirmPassword = false
    @State private var navigateToPersonalInfo = false
    @State private var showPasswordError = false
    @State private var errorMessage = ""
    @State private var isLoading = false

    // Passed from RegisterView
    let account: String
    let code: String
    var onRegistrationComplete: (() -> Void)?
    var viewMode: ViewMode = .registration
    var onPasswordResetSuccess: (() -> Void)?
    
    @Environment(\.dismiss) private var dismiss

    // Form validation
    private func validatePassword() {
        // Rule: 8-20 chars, 1 uppercase, 1 lowercase, 1 number
//        let passwordRegex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,20}$"
        let passwordRegex = AppGlobals.passwordRegex
        let passwordPredicate = NSPredicate(format: "SELF MATCHES %@", passwordRegex)
        
        if !passwordPredicate.evaluate(with: password) {
            errorMessage = "password_format_error".localized
            showPasswordError = true
            return
        }
        
        if password != confirmPassword {
            errorMessage = "password_mismatch_error".localized // Assuming this key exists
            showPasswordError = true
            return
        }
        
        showPasswordError = false
        
        if viewMode == .registration {
            navigateToPersonalInfo = true
        } else {
            performPasswordReset()
        }
    }
    
    private func performPasswordReset() {
        isLoading = true
        AuthService.shared.resetPassword(account: account, code: code, password: password, confirmPassword: confirmPassword) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                switch result {
                case .success:
                    "reset_password_success_title".localized.showToast()
                    self.onPasswordResetSuccess?()
                case .failure(let error):
                    self.errorMessage = error.localizedDescription
                    self.showPasswordError = true
                }
            }
        }
    }
    
    // MARK: - Color definitions
    private var errorBackgroundColor: Color {
        Color(red: 204/255, green: 0, blue: 0)
    }

    // MARK: - Main view
    var body: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)
            
            VStack {
                Image("Login_bg")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 200)
                    .edgesIgnoringSafeArea(.top)
                Spacer()
            }
            
            VStack(spacing: 16) {
                if showPasswordError {
                    Text(errorMessage)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(12)
                        .background(errorBackgroundColor)
                        .cornerRadius(8)
                        .transition(.opacity.animation(.easeInOut))
                }
                
                passwordField(
                    title: "enter_password".localized,
                    text: $password,
                    isSecure: !showPassword,
                    toggleAction: { showPassword.toggle() }
                )
                
                Text("password_rules".localized)
                    .font(.custom("PingFang-SC-Regular", size: 12))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .fixedSize(horizontal: false, vertical: true)

                passwordField(
                    title: "enter_again".localized,
                    text: $confirmPassword,
                    isSecure: !showConfirmPassword,
                    toggleAction: { showConfirmPassword.toggle() }
                )
                
                Spacer()
                
                nextButton
            }
            .padding()
            .padding(.top, 50)
        }
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { dismiss() }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.white)
                }
            }
        }
        .toolbarBackground(.hidden, for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
        .navigationViewStyle(StackNavigationViewStyle())
        .background(
            NavigationLink(
                destination: PersonalInfoView(account: account, password: password, code: code, onRegistrationComplete: onRegistrationComplete),
                isActive: .init(get: { self.viewMode == .registration && self.navigateToPersonalInfo }, set: { self.navigateToPersonalInfo = $0 }),
                label: { EmptyView() }
            )
        )
    }

    // MARK: - Subviews
    private func passwordField(title: String, text: Binding<String>, isSecure: Bool, toggleAction: @escaping () -> Void) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.custom("PingFang-SC-Medium", size: 14))
                .foregroundColor(.white)

            HStack {
                ZStack(alignment: .leading) {
                    if text.wrappedValue.isEmpty {
                        Text("password_placeholder".localized)
                            .foregroundColor(.gray)
                    }
                    if isSecure {
                        SecureField("", text: text)
                            .foregroundColor(.white)
                    } else {
                        TextField("", text: text)
                            .foregroundColor(.white)
                    }
                }
                
                Button(action: toggleAction) {
                    Image(systemName: "eye")
                        .foregroundColor(.gray)
                }
            }
            .padding(.horizontal)
            .frame(height: 50)
            .background(Color(white: 0.1))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(white: 0.3), lineWidth: 1)
            )
        }
    }
    
    private var nextButton: some View {
        Button(action: validatePassword) {
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.blue)
                    .cornerRadius(25)
            } else {
                Text("finish".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.blue)
                    .cornerRadius(25)
            }
        }
        .disabled(isLoading)
    }
}


struct RegisterSettingPassword_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            RegisterSettingPassword(account: "**********", code: "123456", onRegistrationComplete: {})
        }
    }
}

