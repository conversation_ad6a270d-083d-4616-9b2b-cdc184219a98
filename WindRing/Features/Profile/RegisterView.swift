import SwiftUI
import UIKit

struct RegisterView: View {
    // MARK: - State properties
    @State private var registerMethod: RegisterMethod = .mobile
    @State private var mobile: String = ""
    @State private var email: String = ""
    @State private var password: String = ""
    @State private var confirmPassword: String = ""
    @State private var verificationCode: String = ""
    @State private var isCountingDown: Bool = false
    @State private var countdown: Int = 60
    @State private var timer: Timer? = nil
    @State private var showErrorMessage: Bool = false
    @State private var errorMessage: String = ""
    @State private var errorTitle: String = "register_error_title"
    @State private var showLoginLink: Bool = false
    @State private var isLoading: Bool = false
    @State private var registerSuccess: Bool = false
    @State private var focusedField: FormField? = nil
    @State private var showPassword: Bool = false
    @State private var showConfirmPassword: Bool = false
    @State private var navigateToSetPassword: Bool = false
    
    // 新增验证码发送成功状态
    @State private var codeSentSuccessfully: Bool = false
    @State private var selectedCountryCode: String = ""
    var onRegistrationComplete: (() -> Void)?
    
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Enums
    enum RegisterMethod {
        case mobile
        case email
    }
    
    enum FormField {
        case mobile
        case email
        case password
        case confirmPassword
        case verificationCode
    }
    
    // Form validation
    private var isMobileValid: Bool {
        mobile.count == 11 && mobile.allSatisfy { $0.isNumber }
    }
    
    private var isEmailValid: Bool {
        let emailRegex = #"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"#
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    private var isPasswordValid: Bool {
        password.count >= 6
    }
    
    private var isConfirmPasswordValid: Bool {
        confirmPassword == password && !confirmPassword.isEmpty
    }
    
    private var isVerificationCodeValid: Bool {
        verificationCode.count == 6 && verificationCode.allSatisfy { $0.isNumber }
    }
    
    private var isFormValid: Bool {
        switch registerMethod {
        case .mobile:
            return isMobileValid && isVerificationCodeValid
        case .email:
            return isEmailValid && isVerificationCodeValid
        }
    }
    
    // 深色主题颜色定义
    private var darkBackgroundColor: Color {
        Color(hex: "#151921")
    }
    
    private var inputBackgroundColor: Color {
        Color(red: 0.03, green: 0.03, blue: 0.03)
    }
    
    private var inputBorderColor: Color {
        Color(red: 0.26, green: 0.26, blue: 0.26)
    }
    
    private var primaryBlueColor: Color {
        Color(red: 0, green: 0.28, blue: 0.71)
    }
    
    private var successGreenColor: Color {
        Color(red: 0, green: 0.5, blue: 0.3)
    }
    
    // MARK: - Register button
    private var registerButton: some View {
        Button(action: register) {
            ZStack {
                RoundedRectangle(cornerRadius: 17.5)
                    .fill(Color(red: 0, green: 0.28, blue: 0.71))
                    .frame(height: 35)
                
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else {
                    Text("next".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            .frame(maxWidth: .infinity)
        }
        .disabled(!isFormValid || isLoading)
        .animation(.easeInOut(duration: 0.2), value: isFormValid)
        .animation(.easeInOut(duration: 0.2), value: isLoading)
    }
    
    // MARK: - Get code button
    private func getCodeButton(action: @escaping () -> Void, isEnabled: Bool) -> some View {
        Button(action: action) {
            Text(isCountingDown ? "\(countdown)s" : "register_button_get_code".localized)
                .font(.custom("PingFang-SC-Medium", size: 12))
                .foregroundColor(.white)
                .frame(width: 80)
                .contentShape(Rectangle())
        }
        .disabled(isCountingDown || !isEnabled)
        .animation(.easeInOut(duration: 0.2), value: isCountingDown)
        .animation(.easeInOut(duration: 0.2), value: isEnabled)
    }
    
    // MARK: - Main view
    var body: some View {
        //NavigationView {
            ZStack(alignment: .top) {
                // 深色背景
                Color(hex: "#151921")
                    .edgesIgnoringSafeArea(.all)
                
                // 顶部背景图
                VStack(spacing: 0) {
                    Image("Login_bg")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: UIScreen.main.bounds.width * 1.2)
                        .offset(y: -UIScreen.main.bounds.height * 0.01)
                        .opacity(0.8)
                    
                    Spacer()
                }
                .edgesIgnoringSafeArea(.top)
                
                // 主要内容
                ScrollView {
                    VStack(spacing: 0) {
                        Spacer()
                            .frame(height: 130) // 添加适当的顶部空间
                        // 卡片背景
                        VStack(spacing: 0) {
                            // 登录方式选择器
                            registerMethodPicker
                                .padding(.horizontal, -44)
                                .padding(.top, 0)
                            
                            // 成功提示条
                            if codeSentSuccessfully {
                                Text("register_code_sent_success".localized)
                                    .foregroundColor(.white)
                                    .font(.system(size: 14))
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(successGreenColor)
                                    .cornerRadius(10)
                                    .padding(.top, 25)
                            }
                            
                            // 输入表单
                            if registerMethod == .mobile {
                                mobileRegisterForm
                                    .padding(.top, 25)
                            } else {
                                emailRegisterForm
                                    .padding(.top, 25)
                            }
                            
                            // 注册按钮
                            registerButton
                                .padding(.top, 40)

                            // 导航链接到设置密码页面
                            // NavigationLink is handled by the .background modifier on the parent view
                        }
                        .padding(.horizontal, 48)
                        .padding(.top, 0)
                        .padding(.bottom, 30)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color(red: 0.08, green: 0.1, blue: 0.13))
                        )
                        .cornerRadius(25)
                        .shadow(color: Color.black.opacity(0), radius: 0, x: 0, y: 0)
                        .overlay(
                            RoundedRectangle(cornerRadius: 25)
                                .stroke(Color.gray.opacity(0), lineWidth: 0)
                        )
                        .padding(.horizontal, 20)
                        .padding(.top, 10)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 0)
                }
            }
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "chevron.left")
                        Text("register".localized)
                            .font(.custom("PingFangSC-Heavy", size: 19))
                    }
                    .foregroundColor(.white)
                }
            }
        }
        .toolbarBackground(.hidden, for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
        .navigationViewStyle(StackNavigationViewStyle())
        .onChange(of: registerSuccess) {success in
            if success {
                withAnimation(.spring()) {
                    // 成功的视觉反馈
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                    dismiss()
                }
            }
        }
        .onAppear {
            showErrorMessage = false
            errorMessage = ""
            errorTitle = "register_reset_password_failed_title".localized
            showLoginLink = false
            
            if selectedCountryCode.isEmpty {
                if let langCode = Locale.current.language.languageCode?.identifier, langCode.lowercased().hasPrefix("en") {
                    self.selectedCountryCode = "+1"
                } else {
                    self.selectedCountryCode = "+86"
                }
            }
        }
        .onDisappear {
            timer?.invalidate()
            timer = nil
        }
        .background(
            NavigationLink(
                destination: RegisterSettingPassword(
                    account: registerMethod == .mobile ? mobile : email,
                    code: verificationCode,
                    onRegistrationComplete: onRegistrationComplete
                ),
                isActive: $navigateToSetPassword,
                label: { EmptyView() }
            )
        )
    }
    
    // MARK: - Register method selector
    private var registerMethodPicker: some View {
        HStack(spacing: 0) {
            Button(action: {
                registerMethod = .mobile
                showErrorMessage = false
            }) {
                Text("login_mobile_tab_title".localized)
                    .font(.custom("PingFangSC-Heavy", size: 14))
                    .foregroundColor(registerMethod == .mobile ? .white : .gray.opacity(0.7))
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
            
            Button(action: {
                registerMethod = .email
                showErrorMessage = false
            }) {
                Text("login_email_tab_title".localized)
                    .font(.custom("PingFangSC-Heavy", size: 14))
                    .foregroundColor(registerMethod == .email ? .white : .gray.opacity(0.7))
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .frame(height: 60)
        .background(
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    Spacer()
                    Rectangle()
                        .frame(height: 4)
                        .foregroundColor(.clear)
                        .overlay(
                            Rectangle()
                                .frame(width: 20, height: 4)
                                .foregroundColor(.white)
                                .cornerRadius(1)
                                .offset(x: registerMethod == .mobile ? 
                                    -geometry.size.width/4 : 
                                    geometry.size.width/4)
                                .animation(.easeInOut(duration: 0.3), value: registerMethod)
                        )
                }
            }
        )
        .background(
            Color(red: 0.12, green: 0.14, blue: 0.18)
                .clipShape(
                    RoundedCorner(radius: 25, corners: [.topLeft, .topRight])
                )
        )
    }
    
    // MARK: - Mobile registration form
    private var mobileRegisterForm: some View {
        VStack(spacing: 20) {
            // 手机号输入
            HStack {
                countryCodePicker
                
                Divider()
                    .background(Color.gray.opacity(0.5))
                    .frame(height: 20)
                
                TextField("", text: $mobile)
                    .keyboardType(.phonePad)
                    .foregroundColor(.white)
                    .font(.system(size: 16))
                    .placeholder(when: mobile.isEmpty) {
                        Text("register_placeholder_mobile".localized)
                            .foregroundColor(Color(red: 0.4, green: 0.4, blue: 0.4, opacity: 1))
                            .font(.custom("PingFang-SC-Medium", size: 12))
                    }
            }
            .frame(height: 35)
            .background(
                ZStack {
                    RoundedRectangle(cornerRadius: 10)
                        .fill(inputBorderColor)
                    RoundedRectangle(cornerRadius: 9.5)
                        .fill(inputBackgroundColor)
                        .padding(0.5)
                }
            )
            .cornerRadius(10)
            
            // 验证码输入
            HStack {
                TextField("", text: $verificationCode)
                    .keyboardType(.numberPad)
                    .foregroundColor(.white)
                    .font(.system(size: 16))
                    .placeholder(when: verificationCode.isEmpty) {
                        HStack {
                            Spacer().frame(width: 10)
                            Text("register_placeholder_code".localized)
                                .foregroundColor(Color(red: 0.4, green: 0.4, blue: 0.4, opacity: 1))
                                .font(.custom("PingFang-SC-Medium", size: 12))
                        }
                    }
                
                Spacer()
                
                Divider()
                    .background(Color.gray.opacity(0.5))
                    .frame(height: 20)
                    .padding(.horizontal, 8)
                
                Button(action: {
                    getVerificationCode()
                }) {
                    Text(isCountingDown ? "\(countdown)s" : "register_button_get_code".localized)
                        .font(.custom("PingFang-SC-Medium", size: 12))
                        .foregroundColor(.white)
                }
                .padding(.trailing, 8)
                .disabled(isCountingDown || !isMobileValid)
            }
            .frame(height: 35)
            .background(
                ZStack {
                    RoundedRectangle(cornerRadius: 10)
                        .fill(inputBorderColor)
                    RoundedRectangle(cornerRadius: 9.5)
                        .fill(inputBackgroundColor)
                        .padding(0.5)
                }
            )
            .cornerRadius(10)
        }
    }
    
    private var countryCodePicker: some View {
        Menu {
            Button("+1") { selectedCountryCode = "+1" }
            Button("+86") { selectedCountryCode = "+86" }
        } label: {
            HStack(spacing: 4) {
                Text(selectedCountryCode)
                    .font(.custom("PingFang-SC-Medium", size: 13))
                    .foregroundColor(.white)
                    .frame(minWidth: 40, alignment: .center)
                Image(systemName: "chevron.down")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 10)
        }
    }
    
    // MARK: - Email registration form
    private var emailRegisterForm: some View {
        VStack(spacing: 20) {
            // 邮箱输入
            TextField("", text: $email)
                .keyboardType(.emailAddress)
                .foregroundColor(.white)
                .font(.system(size: 16))
                .placeholder(when: email.isEmpty) {
                    HStack {
                        Spacer().frame(width: 10)
                        Text("register_placeholder_email".localized)
                            .foregroundColor(Color(red: 0.4, green: 0.4, blue: 0.4, opacity: 1))
                            .font(.custom("PingFang-SC-Medium", size: 12))
                    }
                }
                .frame(height: 35)
                .background(
                    ZStack {
                        RoundedRectangle(cornerRadius: 10)
                            .fill(inputBorderColor)
                        RoundedRectangle(cornerRadius: 9.5)
                            .fill(inputBackgroundColor)
                            .padding(0.5)
                    }
                )
                .cornerRadius(10)
            
            // 验证码输入
            HStack {
                TextField("", text: $verificationCode)
                    .keyboardType(.numberPad)
                    .foregroundColor(.white)
                    .font(.system(size: 16))
                    .placeholder(when: verificationCode.isEmpty) {
                        HStack {
                            Spacer().frame(width: 10)
                            Text("register_placeholder_code".localized)
                                .foregroundColor(Color(red: 0.4, green: 0.4, blue: 0.4, opacity: 1))
                                .font(.custom("PingFang-SC-Medium", size: 12))
                        }
                    }
                
                Spacer()
                
                Divider()
                    .background(Color.gray.opacity(0.5))
                    .frame(height: 20)
                    .padding(.horizontal, 8)
                
                Button(action: {
                    getEmailVerificationCode()
                }) {
                    Text(isCountingDown ? "\(countdown)s" : "register_button_get_code".localized)
                        .font(.custom("PingFang-SC-Medium", size: 12))
                        .foregroundColor(.white)
                }
                .padding(.trailing, 8)
                .disabled(isCountingDown || !isEmailValid)
            }
            .frame(height: 35)
            .background(
                ZStack {
                    RoundedRectangle(cornerRadius: 10)
                        .fill(inputBorderColor)
                    RoundedRectangle(cornerRadius: 9.5)
                        .fill(inputBackgroundColor)
                        .padding(0.5)
                }
            )
            .cornerRadius(10)
        }
    }
    
    // MARK: - Methods
    /// Get verification code for mobile
    private func getVerificationCode() {
        guard isMobileValid else {
            "register_error_invalid_mobile".localized.showToast()
            return
        }
        
        // Start countdown
        isCountingDown = true
        countdown = 60
        
        // 保存当前状态的引用
        let phoneNumber = mobile
        
        // Set timer - 使用捕获列表来避免引用self
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { timer in
            if self.countdown > 0 {
                self.countdown -= 1
            } else {
                self.isCountingDown = false
                timer.invalidate()
            }
        }
        AuthService.shared.sendSMSCode(mobile: mobile, scene: 5, countryCode: selectedCountryCode) { result in // Commented out
             DispatchQueue.main.async {
                 switch result {
                 case .success(_):
                     print("Mobile verification code sent successfully to: \(phoneNumber)")
                     DispatchQueue.main.async {
                         self.codeSentSuccessfully = true
                         // 设置一个定时器，5秒后自动隐藏成功提示
                         DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                             withAnimation {
                                 self.codeSentSuccessfully = false
                             }
                         }
                     }
                 case .failure(let error):
                     DispatchQueue.main.async {
                         // 直接更新状态变量
                         self.isCountingDown = false
                         self.timer?.invalidate()
                         error.localizedDescription.showToast()
                     }
                 }
             }
         }
    }
    
    /// Get verification code for email
    private func getEmailVerificationCode() {
        guard isEmailValid else {
            "register_error_invalid_email".localized.showToast()
            return
        }
        
        // Start countdown
        isCountingDown = true
        countdown = 60
        
        // 保存当前状态的引用
        let emailAddress = email
        
        // Set timer
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { timer in
            if self.countdown > 0 {
                self.countdown -= 1
            } else {
                self.isCountingDown = false
                timer.invalidate()
            }
        }
        
        // Call AuthService to get email verification code
        AuthService.shared.getEmailVerificationCode(email: emailAddress, scene: .register) { result in // scene: 2 for registration
             switch result {
             case .success:
                 print("Email verification code sent successfully to: \(emailAddress)")
                 DispatchQueue.main.async {
                     self.codeSentSuccessfully = true
                     // 设置一个定时器，5秒后自动隐藏成功提示
                     DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                         withAnimation {
                             self.codeSentSuccessfully = false
                         }
                     }
                 }
             case .failure(let error):
                 DispatchQueue.main.async {
                     // 直接更新状态变量
                     self.isCountingDown = false
                     self.timer?.invalidate()
                     error.localizedDescription.showToast()
                 }
             }
         }

    }
    
    /// Register user (修改为重置密码流程)
    private func register() {
        // Validate form
        guard isFormValid else {
            showLoginLink = false
            errorTitle = "register_validation_failed_title".localized
            
            switch registerMethod {
            case .mobile:
                if !isMobileValid {
                    "register_error_invalid_mobile".localized.showToast()
                } else {
                    "register_error_invalid_code".localized.showToast()
                }
            case .email:
                if !isEmailValid {
                    "register_error_invalid_email".localized.showToast()
                } else if !isVerificationCodeValid {
                    "register_error_invalid_code".localized.showToast()
                }
            }
            return
        }
        
        // 清理之前的错误状态
        showLoginLink = false
        errorTitle = "register_reset_password_failed_title".localized
        
        // Show loading
        isLoading = true
        
        // 定义验证码验证完成后的操作
        let validationCompletion: (Result<Bool, AuthError>) -> Void = { result in
            self.isLoading = false
            switch result {
            case .success(true):
                self.registerSuccess = true
                // 导航到设置密码页面
                self.navigateToSetPassword = true
            case .success(false):
                // This case should not happen with current AuthService implementation
                "Unknown validation error".showToast()
            case .failure(let error):
                error.localizedDescription.showToast()
            }
        }
        
        // 根据注册方式调用不同的验证接口
        if registerMethod == .mobile {
             AuthService.shared.validateSmsCode(mobile: mobile, scene: .register, code: verificationCode, completion: validationCompletion)
        } else {
             AuthService.shared.validateEmailCode(email: email, scene: .register, code: verificationCode, completion: validationCompletion)
        }
    }
}

// MARK: - Preview
struct RegisterView_Previews: PreviewProvider {
    static var previews: some View {
        RegisterView(onRegistrationComplete: {})
    }
} 


