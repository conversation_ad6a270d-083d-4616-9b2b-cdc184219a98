//
//  DeveloperToolsView.swift
//  WindRing
//
//  Created by zx on 2025/6/18.
//

import SwiftUI

// MARK: - 开发者工具视图
struct DeveloperToolsView: View {
    var body: some View {
        List {
            Section(header: Text("数据调试工具")) {
                // 数据库调试工具
                Button(action: {
                    NotificationCenter.default.post(name: Notification.Name("OpenDatabaseDebugView"), object: nil)
                }) {
                    SettingItemView(
                        icon: "wrench.and.screwdriver.fill",
                        iconColor: .orange,
                        title: "数据库调试工具",
                        showArrow: true
                    )
                }
                
                // 数据同步调试
                NavigationLink(destination: SyncDebugView()) {
                    SettingItemView(
                        icon: "antenna.radiowaves.left.and.right",
                        iconColor: .blue,
                        title: "数据同步调试",
                        showArrow: true
                    )
                }
            }
            
            Section(header: Text("健康数据测试")) {
                // 心率明细测试
                Button(action: {
                    NotificationCenter.default.post(name: Notification.Name("OpenHeartRateDetailTest"), object: nil)
                }) {
                    SettingItemView(
                        icon: "waveform.path.ecg",
                        iconColor: .red,
                        title: "心率明细数据测试",
                        showArrow: true
                    )
                }
                
                // 睡眠数据测试
                NavigationLink(destination: SleepDataTestView()) {
                    SettingItemView(
                        icon: "moon.zzz.fill",
                        iconColor: .purple,
                        title: "睡眠数据测试",
                        showArrow: true
                    )
                }
                
                // 步数API测试
                NavigationLink(destination: StepAPITestView()) {
                    SettingItemView(
                        icon: "figure.walk",
                        iconColor: .green,
                        title: "步数API测试",
                        showArrow: true
                    )
                }
                
                // 睡眠API测试
                NavigationLink(destination: SleepAPITestView()) {
                    SettingItemView(
                        icon: "bed.double",
                        iconColor: .indigo,
                        title: "睡眠API测试",
                        showArrow: true
                    )
                }
                
                // GoMore高级睡眠数据测试
                NavigationLink(destination: GoMoreSleepTestView()) {
                    SettingItemView(
                        icon: "waveform.path",
                        iconColor: .teal,
                        title: "GoMore睡眠测试",
                        showArrow: true
                    )
                }
            }
            
            Section(header: Text("其他测试工具")) {
                // 心率API测试
                NavigationLink(destination: HeartRateAPITestView()) {
                    SettingItemView(
                        icon: "heart.fill",
                        iconColor: .red,
                        title: "心率API测试",
                        showArrow: true
                    )
                }
                
                // 卡路里API测试
                NavigationLink(destination: CalorieAPITestView()) {
                    SettingItemView(
                        icon: "flame.fill",
                        iconColor: .orange,
                        title: "卡路里API测试",
                        showArrow: true
                    )
                }
                
                // 活动数据API测试
                NavigationLink(destination: ActivityAPITestView()) {
                    SettingItemView(
                        icon: "figure.walk",
                        iconColor: .green,
                        title: "活动数据API测试",
                        showArrow: true
                    )
                }
                
                // 步数明细数据测试
                NavigationLink(destination: StepDetailTestView()) {
                    SettingItemView(
                        icon: "footprints.fill",
                        iconColor: .blue,
                        title: "步数明细数据测试",
                        showArrow: true
                    )
                }
            }
        }
        .navigationTitle("开发者工具")
        .navigationBarTitleDisplayMode(.inline)
        .background(Color(.systemGroupedBackground))
    }
}

// 设置项视图组件
struct SettingItemView: View {
    var icon: String
    var iconColor: Color = .blue
    var title: String
    var subtitle: String? = nil
    var showArrow: Bool = true
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 22))
                .foregroundColor(iconColor)
                .frame(width: 28, height: 28)
                .padding(.trailing, 8)
            
            if let subtitle = subtitle {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.body)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            } else {
                Text(title)
                    .font(.body)
            }
            
            Spacer()
            
            if showArrow {
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
            }
        }
        .padding(.vertical, 8)
    }
}
