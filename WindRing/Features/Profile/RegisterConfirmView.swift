import SwiftUI

struct RegisterConfirmView: View {
    // MARK: - 状态属性
    let account: String
    let verificationCode: String
    let password: String
    
    @State private var isLoading = false
    @State private var showSuccess = false
    @State private var showError = false
    @State private var errorMessage = ""
    
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 主视图
    var body: some View {
        ZStack(alignment: .top) {
            // 深色背景
            Color(hex: "#151921")
                .edgesIgnoringSafeArea(.all)
            
            // 顶部背景图
            VStack(spacing: 0) {
                Image("Login_bg")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: UIScreen.main.bounds.width * 1.2)
                    .offset(y: -UIScreen.main.bounds.height * 0.01)
                    .opacity(0.8)
                
                Spacer()
            }
            .edgesIgnoringSafeArea(.top)
            
            // 主要内容
            ScrollView {
                VStack(spacing: 0) {
                    Spacer()
                        .frame(height: 130)
                    
                    // 卡片背景
                    VStack(spacing: 0) {
                        // 标题
                        Text("Confirm Registration")
                            .font(.custom("PingFang-SC-Bold", size: 22))
                            .foregroundColor(.white)
                            .padding(.top, 30)
                            .padding(.bottom, 20)
                        
                        // 账号信息
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Account Information")
                                .font(.custom("PingFang-SC-Medium", size: 16))
                                .foregroundColor(.white.opacity(0.7))
                            
                            HStack {
                                Text("Account:")
                                    .foregroundColor(.white.opacity(0.7))
                                Spacer()
                                Text(account)
                                    .foregroundColor(.white)
                            }
                            
                            HStack {
                                Text("Verification Code:")
                                    .foregroundColor(.white.opacity(0.7))
                                Spacer()
                                Text(verificationCode)
                                    .foregroundColor(.white)
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 15)
                        .background(Color.white.opacity(0.05))
                        .cornerRadius(10)
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)
                        
                        // 确认按钮
                        Button(action: confirmRegistration) {
                            ZStack {
                                RoundedRectangle(cornerRadius: 17.5)
                                    .fill(Color(red: 0, green: 0.28, blue: 0.71))
                                    .frame(height: 35)
                                
                                if isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                } else {
                                    Text("Confirm")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.white)
                                }
                            }
                            .frame(maxWidth: .infinity)
                        }
                        .disabled(isLoading)
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 20)
                    .background(Color(red: 0.08, green: 0.1, blue: 0.13))
                    .cornerRadius(25)
                    .padding(.horizontal, 20)
                    
                    Spacer()
                }
            }
        }
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarBackButtonHidden(true)
//        .toolbar {
//            ToolbarItem(placement: .navigationBarLeading) {
//                Button(action: {
//                    dismiss()
//                }) {
//                    HStack(spacing: 4) {
//                        Image(systemName: "chevron.left")
//                        Text("Back")
//                            .font(.custom("PingFangSC-Heavy", size: 19))
//                    }
//                    .foregroundColor(.white)
//                }
//            }
//        }
        .toolbarBackground(.hidden, for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
        .alert(isPresented: $showError) {
            Alert(
                title: Text("Registration Failed"),
                message: Text(errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .onChange(of: showSuccess) { success in
            if success {
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                    dismiss()
                }
            }
        }
    }
    
    // MARK: - 方法
    private func confirmRegistration() {
        isLoading = true
        
        // 模拟API调用延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            isLoading = false
            showSuccess = true
        }
    }
}

// MARK: - Preview
struct RegisterConfirmView_Previews: PreviewProvider {
    static var previews: some View {
        RegisterConfirmView(
            account: "<EMAIL>",
            verificationCode: "123456",
            password: "password123"
        )
    }
} 
