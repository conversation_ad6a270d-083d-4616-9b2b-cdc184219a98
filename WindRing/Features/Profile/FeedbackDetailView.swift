import SwiftUI


struct FeedbackDetailView: View {
    let item: FeedbackHistoryItem
    
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        ZStack {
            // 背景色 - 使用深色背景
            Color(hex: "#070708").ignoresSafeArea()
            
            // 使用封装的光晕背景组件
            CornerGlowBackground()
            
            VStack(spacing: 0) {
                navigationBar
                content(for: item)
            }
        }
        .navigationBarHidden(true)
    }

    private var navigationBar: some View {
        HStack {
            Button(action: { presentationMode.wrappedValue.dismiss() }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(.white)
            }
            Spacer()
            Text("feedback_detail_title".localized)
                .font(.custom("PingFang-SC-Heavy", size: 19))
                .foregroundColor(.white)
            Spacer()
            Image(systemName: "chevron.left").opacity(0) // Placeholder
        }
        .padding(.horizontal)
        .frame(height: 44)
    }

    private func content(for detail: FeedbackHistoryItem) -> some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                statusHeader(for: detail)
                
                if let reply = detail.replyContent, !reply.isEmpty {
                    replyBanner(text: reply)
                }
                
                detailsSection(for: detail)
                
                if !detail.imageUrls.isEmpty {
                    imageSection(for: detail.imageUrls)
                }
            }
            .padding()
        }
    }
    
    private func statusHeader(for detail: FeedbackHistoryItem) -> some View {
        VStack(spacing: 12) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 40))
                .foregroundColor(Color(hex: "#4CAF50"))
            Text(detail.status)
                .font(.custom("PingFang-SC-Medium", size: 16))
                .foregroundColor(.white)
        }
        .frame(maxWidth: .infinity, alignment: .center) // 横向居中
    }

    private func replyBanner(text: String) -> some View {
        HStack {
            Image(systemName: "bell.fill")
                .foregroundColor(Color(hex: "#FA6C2D"))
            Text(text)
                .font(.custom("PingFang-SC-Regular", size: 14))
                .foregroundColor(.white)
            Spacer()
        }
        .padding()
        .background(Color.white.opacity(0.1))
        .cornerRadius(12)
    }

    private func detailsSection(for detail: FeedbackHistoryItem) -> some View {
        VStack(spacing: 0) {
            detailRow(title: "feedback_detail_time", value: (Date(timeIntervalSince1970: (Double(detail.date) ?? 0) / 1000.0).string(withFormat: "yyyy-MM-dd HH:mm:ss")))
            detailRow(title: "feedback_detail_type", value: detail.types)
            detailRow(title: "feedback_detail_channel", value: detail.purchaseChannel)
            detailRow(title: "feedback_detail_content", value: detail.content)
            detailRow(title: "feedback_detail_address", value: detail.contactEmail, isLast: true)
        }
        .background(Color(white: 1, opacity: 0.05))
        .cornerRadius(16)
    }
    
    private func imageSection(for urls: [URL]) -> some View {
        VStack(alignment: .leading) {
            Text("feedback_detail_images")
                .font(.custom("PingFang-SC-Regular", size: 14))
                .foregroundColor(.white.opacity(0.8))
                .padding(.bottom, 8)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 10) {
                    ForEach(urls, id: \.self) { url in
                        AsyncImage(url: url) { image in
                            image.resizable()
                        } placeholder: {
                            ProgressView()
                                .frame(width: 100, height: 100)
                                .background(Color.gray.opacity(0.3))
                        }
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 100, height: 100)
                        .clipped()
                        .cornerRadius(8)
                    }
                }
            }
        }
    }

    private func detailRow(title: String, value: String, isLast: Bool = false) -> some View {
        VStack(spacing: 0) {
            HStack {
                Text(title.localized)
                    .font(.custom("PingFang-SC-Regular", size: 14))
                    .foregroundColor(.white.opacity(0.8))
                Spacer()
                Text(value)
                    .font(.custom("PingFang-SC-Regular", size: 14))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.trailing)
            }
            .padding()
            
            if !isLast {
                Divider().background(Color.white.opacity(0.1))
                    .padding(.horizontal)
            }
        }
    }
}

struct FeedbackDetailView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleItem = FeedbackHistoryItem(
            id: "1",
            types: "Ring, App",
            content: "This is a test content string.",
            date: "2025-01-01 12:00:00",
            status: "Replied",
            statusColor: .green,
            purchaseChannel: "Amazon",
            contactEmail: "<EMAIL>",
            replyContent: "We have received your feedback and are looking into it.",
            imageUrls: [URL(string: "https://example.com/image.jpg")!]
        )
        return FeedbackDetailView(item: sampleItem)
    }
} 
