//
//  HealthDataShareAlert.swift
//  WindRing
//
//  Created by zx on 2025/6/19.
//

import SwiftUI

    enum HealthDataAlertType: Int {
        case activeShare = 0     // type = 0
        case invitationShare // type = 1
    }

struct DeepLinkInfo {
    let type: HealthDataAlertType
    let uuid: String
    let nickname: String
}

struct HealthDataShareAlert: View {
    var type: HealthDataAlertType = .activeShare
    var nickname: String = ""
    var customMessage: String? = nil
    var onConfirm: () -> Void
    var onCancel: () -> Void

//    private var messageText: String {
//        switch type {
//        case .activeShare:
//            return "Do you accept \(nickname) sharing his health data with you?"
//        case .invitationShare:
//            return "\(nickname) invites you to share your health data. Do you accept?"
//        }
//    }
    private var messageText: String {
        if let customMessage = customMessage {
            return customMessage
        }

        switch type {
        case .activeShare:
            return "Do you accept \(nickname) sharing his health data with you?"
        case .invitationShare:
            return "\(nickname) invites you to share your health data. Do you accept?"
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            Spacer().frame(height: 26)

            // 图标
//            Image(<#T##String#>)
            Image("icon_link_pop")
                .resizable()
                .scaledToFit()
                .frame(width: 52, height: 52)
                .foregroundColor(.white)
                .background(
                    Circle()
                        .fill(Color.teal.opacity(0.2))
                        .frame(width: 52, height: 52)
                )

            Spacer().frame(height: 24)

            // 文案
            Text(messageText)
                .font(.system(size: 14))
                .foregroundColor(.white.opacity(0.85))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 26)

            Spacer().frame(height: 42)

            // 按钮组
            HStack(spacing: 40) {
                Button(action: onCancel) {
                    Text("Cancel")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .frame(height: 44)
                        .frame(maxWidth: .infinity)
                        .background(Color.clear)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.white.opacity(0.4), lineWidth: 1)
                        )
                }

                Button(action: {
                    onConfirm()
                }) {
                    Text("Confirm")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .frame(height: 44)
                        .frame(maxWidth: .infinity)
                        .background(Color(hex: "#042A63"))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.blue.opacity(0.6), lineWidth: 1)
                        )
                }
            }
            .padding(.horizontal, 24)

            Spacer().frame(height: 48)
        }
        .frame(width: 315)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [Color(hex: "#141821"), Color(hex: "#2A3040")]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
        )
        .shadow(radius: 25)
    }
}
struct HealthShareTipAlert: View {
    var message: String = ""
    var onConfirm: () -> Void



    var body: some View {
        VStack(spacing: 0) {
            Spacer().frame(height: 10)
            // 文案
            Text(message)
                .font(.system(size: 14))
                .foregroundColor(.white.opacity(0.85))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 26)

            Spacer().frame(height: 20)

            // 按钮组
            HStack(spacing: 0) {
                Button(action: {
                    onConfirm()
                }) {
                    Text("copy_the_link_button".localized)
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .frame(height: 44)
                        .frame(maxWidth: .infinity)
                        .background(Color(hex: "#042A63"))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.blue.opacity(0.6), lineWidth: 1)
                        )
                }
            }
            .padding(.horizontal, 24)

            Spacer().frame(height: 20)
        }
        .frame(width: 315)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [Color(hex: "#141821"), Color(hex: "#2A3040")]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
        )
        .shadow(radius: 25)
    }
}
