import SwiftUI

struct SMSLoginView: View {
    // MARK: - State properties
    @State private var mobile: String = ""
    @State private var verificationCode: String = ""
    @State private var isCountingDown: Bool = false
    @State private var countdown: Int = 60
    @State private var timer: Timer? = nil
    @State private var showErrorMessage: Bool = false
    @State private var errorMessage: String = ""
    @State private var isLoading: Bool = false
    @State private var loginSuccess: Bool = false
    @State private var rememberAccount: Bool = true
    
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Main view
    var body: some View {
        ScrollView {
            VStack(spacing: 30) {
                // 标题
                Text("SMS Login")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.top, 20)
                
                // 输入表单
                VStack(spacing: 20) {
                    // 手机号输入
                    TextField("Enter your mobile number", text: $mobile)
                        .keyboardType(.phonePad)
                        .padding()
                        .background(Color(UIColor.systemGray6))
                        .cornerRadius(8)
                    
                    // 验证码输入和发送按钮
                    HStack {
                        TextField("Verification code", text: $verificationCode)
                            .keyboardType(.numberPad)
                            .padding()
                            .background(Color(UIColor.systemGray6))
                            .cornerRadius(8)
                        
                        // 发送验证码按钮
                        Button(action: sendVerificationCode) {
                            Text(isCountingDown ? "\(countdown)s" : "Send")
                                .foregroundColor(isCountingDown ? .gray : .white)
                                .font(.system(size: 14, weight: .semibold))
                                .frame(width: 80)
                                .frame(height: 50)
                                .background(
                                    isCountingDown 
                                    ? Color.gray.opacity(0.3) 
                                    : WRTheme.Colors.primary
                                )
                                .cornerRadius(8)
                        }
                        .disabled(isCountingDown || !isValidMobile(mobile))
                    }
                }
                
                // 登录按钮
                Button(action: login) {
                    ZStack {
                        RoundedRectangle(cornerRadius: 28)
                            .fill(isFormValid ? WRTheme.Colors.primary : Color.gray.opacity(0.3))
                            .frame(height: 56)
                        
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        } else {
                            Text("Login")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                        }
                    }
                }
                .disabled(!isFormValid || isLoading)
                .padding(.top, 30)
                
                // 使用条款和隐私政策
                Text("By logging in, you agree to the Terms of Use and Privacy Policy")
                    .font(.footnote)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.top, 16)
                
                // 添加记住账号选项
                HStack {
                    Button(action: {
                        rememberAccount.toggle()
                    }) {
                        Image(systemName: rememberAccount ? "checkmark.square.fill" : "square")
                            .foregroundColor(rememberAccount ? WRTheme.Colors.primary : .gray)
                            .imageScale(.large)
                    }
                    
                    Text("Remember Account")
                        .font(.footnote)
                        .foregroundColor(.gray)
                    
                    Spacer()
                }
                .padding(.top, 10)
            }
            .padding(.horizontal, 24)
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarItems(
            leading: backButton,
            trailing: EmptyView()
        )
        .alert(isPresented: $showErrorMessage) {
            Alert(
                title: Text("Login Failed"),
                message: Text(errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .onChange(of: loginSuccess) {success in
            if success {
                dismiss()
            }
        }
        .onAppear {
            // 加载保存的手机号
            if let savedMobile = UserDefaults.standard.string(forKey: "savedMobile") {
                mobile = savedMobile
            }
        }
        .onDisappear {
            // 停止计时器
            timer?.invalidate()
            timer = nil
        }
    }
    
    // MARK: - 计算属性
    
    // 表单验证
    private var isFormValid: Bool {
        return isValidMobile(mobile) && isValidCode(verificationCode)
    }
    
    // MARK: - UI 组件
    
    // 返回按钮
    private var backButton: some View {
        Button(action: {
            presentationMode.wrappedValue.dismiss()
        }) {
            Image(systemName: "chevron.left")
                .foregroundColor(.primary)
                .imageScale(.large)
        }
    }
    
    // MARK: - 方法
    
    // 发送验证码
    private func sendVerificationCode() {
        guard isValidMobile(mobile) else {
            errorMessage = "请输入有效的手机号"
            showErrorMessage = true
            return
        }
        
        // 开始倒计时
        isCountingDown = true
        countdown = 60
        
        // 创建计时器
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { timer in
            if self.countdown > 0 {
                self.countdown -= 1
            } else {
                self.isCountingDown = false
                timer.invalidate()
            }
        }
        
        // 调用API发送验证码
//        AuthService.shared.sendSMSCode(mobile: mobile, scene: 1, countryCode: <#String#>) { result in
//            DispatchQueue.main.async {
//                switch result {
//                case .success(_):
//                    print("验证码发送成功到手机: \(self.mobile)")
//                case .failure(let error):
//                    self.isCountingDown = false
//                    self.timer?.invalidate()
//                    self.errorMessage = error.localizedDescription
//                    self.showErrorMessage = true
//                }
//            }
//        }
    }
    
    // 登录
    private func login() {
        guard isFormValid else {
            if !isValidMobile(mobile) {
                errorMessage = "请输入有效的手机号"
            } else if !isValidCode(verificationCode) {
                errorMessage = "请输入6位验证码"
            }
            showErrorMessage = true
            return
        }
        
        // 开始加载
        isLoading = true
        
        // 调用API进行短信验证码登录
        AuthService.shared.loginWithSMSCode(mobile: mobile, code: verificationCode) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                
                switch result {
                case .success(_):
                    // 登录成功
                    print("短信验证码登录成功")
                    
                    // 如果选择记住账号，保存手机号
                    if self.rememberAccount {
                        UserDefaults.standard.set(self.mobile, forKey: "savedMobile")
                    } else {
                        UserDefaults.standard.removeObject(forKey: "savedMobile")
                    }
                    
                    self.loginSuccess = true
                case .failure(let error):
                    // 登录失败
                    self.errorMessage = error.localizedDescription
                    self.showErrorMessage = true
                }
            }
        }
    }
    
    // 验证手机号
    private func isValidMobile(_ mobile: String) -> Bool {
        return mobile.count == 11 && mobile.allSatisfy { $0.isNumber }
    }
    
    // 验证验证码
    private func isValidCode(_ code: String) -> Bool {
        return code.count == 6 && code.allSatisfy { $0.isNumber }
    }
}

// MARK: - Preview
struct SMSLoginView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            SMSLoginView()
        }
    }
} 
