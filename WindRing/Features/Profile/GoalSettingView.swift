//
//  GoalSettingView.swift
//  WindRing
//
//  Created by zx on 2025/6/18.
//

import SwiftUI

// 重新实现目标设置视图，现代化UI设计
struct GoalSettingView: View {
    // 目标设置状态变量
    @State private var sleepHours: Int = 0
    @State private var sleepMinutes: Int = 0
    @State private var stepGoal: Int = 0
    @State private var calorieGoal: Int = 0
    @State private var activityHours: Int = 0
    @State private var activityMinutes: Int = 0
    
    // 控制底部表单显示
    @State private var showSleepGoalSheet = false
    @State private var showStepGoalSheet = false
    @State private var showCalorieGoalSheet = false
    @State private var showDurationGoalSheet = false
    
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色 - 使用深色背景
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 0) {
                // 删除状态栏部分

                // 导航栏
                HStack {
                    But<PERSON>(action: {
                        // 返回操作
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                            
                            Text("Goal Settings")
                                .font(.system(size: 24, weight: .bold))
                                .foregroundColor(.white)
                        }
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .padding(.top, 10) // 增加一点顶部边距
                
                // 网格布局
                VStack(spacing: 16) {
                    HStack(spacing: 16) {
                        // 睡眠目标卡片
                        fixedSizeGoalCard(
                            icon: "bed.double.fill",
                            iconColor: .indigo,
                            title: "Sleep\nGoals",
                            value: "\(sleepHours) hr \(sleepMinutes) min",
                            action: { showSleepGoalSheet = true }
                        )
                        
                        // 步数目标卡片
                        fixedSizeGoalCard(
                            icon: "figure.walk",
                            iconColor: .white,
                            title: "Step Goals",
                            value: "\(stepGoal.formatted())",
                            action: { showStepGoalSheet = true }
                        )
                    }
                    
                    HStack(spacing: 16) {
                        //
                        fixedSizeGoalCard(
                            icon: "flame.fill",
                            iconColor: .orange,
                            title: "Activity calorie\ngoal",
                            value: "\(calorieGoal)kal",
                            action: { showCalorieGoalSheet = true }
                        )
                        
                        // 活动时长目标卡片
                        fixedSizeGoalCard(
                            icon: "clock.fill",
                            iconColor: .gray,
                            title: "Activity duration\ngoal",
                            value: "\(activityHours) hr \(activityMinutes) min",
                            action: { showDurationGoalSheet = true }
                        )
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 16)
                
                Spacer()
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showSleepGoalSheet) {
            SleepGoalView(
                sleepHours: $sleepHours,
                sleepMinutes: $sleepMinutes,
                isPresented: $showSleepGoalSheet
            )
            .presentationDetents([.height(250)])
            .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $showStepGoalSheet) {
            StepGoalView(
                stepGoal: $stepGoal,
                isPresented: $showStepGoalSheet
            )
            .presentationDetents([.height(250)])
            .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $showCalorieGoalSheet) {
            ActivityCalorieGoalView(
                calorieGoal: $calorieGoal,
                isPresented: $showCalorieGoalSheet
            )
            .presentationDetents([.height(250)])
            .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $showDurationGoalSheet) {
            ActivityDurationGoalView(
                activityHours: $activityHours,
                activityMinutes: $activityMinutes,
                isPresented: $showDurationGoalSheet
            )
            .presentationDetents([.height(250)])
            .presentationDragIndicator(.visible)
        }
    }
    
    // 新增：固定尺寸的目标卡片组件
    private func fixedSizeGoalCard(
        icon: String,
        iconColor: Color,
        title: String,
        value: String,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                HStack(spacing: 8) {
                    // 图标
                    ZStack {
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 36, height: 36)
                        
                        Image(systemName: icon)
                            .font(.system(size: 16))
                            .foregroundColor(iconColor)
                    }
                    
                    Text(title)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                        .lineLimit(2)
                }
                
                HStack {
                    Text(value)
                        .font(.system(size: 18))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                        .font(.system(size: 14))
                }
            }
            .padding(16)
            .frame(width: UIScreen.main.bounds.width / 2 - 24, height: 95)
            .background(
                // 添加渐变背景
                LinearGradient(
                    gradient: Gradient(
                        colors: [
                            Color(red: 0.16, green: 0.19, blue: 0.25),
                            Color(red: 0.08, green: 0.09, blue: 0.13)
                        ]
                    ),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
// 睡眠目标设置视图 - 现代化UI
struct SleepGoalView: View {
    @Binding var sleepHours: Int
    @Binding var sleepMinutes: Int
    @Binding var isPresented: Bool
    @State private var tempHours: Int
    @State private var tempMinutes: Int
    
    init(sleepHours: Binding<Int>, sleepMinutes: Binding<Int>, isPresented: Binding<Bool>) {
        self._sleepHours = sleepHours
        self._sleepMinutes = sleepMinutes
        self._isPresented = isPresented
        self._tempHours = State(initialValue: sleepHours.wrappedValue)
        self._tempMinutes = State(initialValue: sleepMinutes.wrappedValue)
    }
    
    var body: some View {
        // 移除外层ZStack，改为直接使用背景渐变色和圆角
        VStack(spacing: 0) {
            // 标题栏 - 图5样式
            HStack(spacing: 0) {
                // 左侧蓝色小矩形标识
                Rectangle()
                    .fill(Color.blue)
                    .frame(width: 4, height: 20)
                    .padding(.trailing, 10)
                
                Text("Sleep Goals")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    isPresented = false
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, 16)
            .padding(.horizontal, 20)
            .background(Color(hex: "#272B3B"))
            
            // 删除当前选择的时间显示
            
            // 选择器区域
            HStack {
                // 小时选择器和hr标签
                HStack(spacing: 5) {
                    Picker("Hours", selection: $tempHours) {
                        ForEach(6...11, id: \.self) { hour in
                            Text("\(hour)")
                                .foregroundColor(.white)
                                .tag(hour)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(width: 50)
                    .clipped()
                    
                    Text("hr")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                }
                
                // 中间冒号
                Text(":")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 10)
                
                // 分钟选择器和min标签
                HStack(spacing: 5) {
                    Picker("Minutes", selection: $tempMinutes) {
                        ForEach(Array(stride(from: 0, to: 60, by: 5)), id: \.self) { minute in
                            Text("\(minute)")
                                .foregroundColor(.white)
                                .tag(minute)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(width: 50)
                    .clipped()
                    
                    Text("min")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.top, 20)
            .padding(.bottom, 20)
            
            // 确认按钮 - 根据图2参数优化
            Button(action: {
                // 保存设置
                sleepHours = tempHours
                sleepMinutes = tempMinutes
                isPresented = false
            }) {
                Text("Confirm")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(height: 35)
                    .frame(maxWidth: .infinity)
                    .background(
                        ZStack {
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color(hex: "#0048B5").opacity(0.2))
                            
                            // 添加内层边框效果
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                        }
                    )
            }
            .padding(.horizontal, 18.5)
            .padding(.bottom, 20)
        }
        .frame(maxWidth: .infinity)
        .background(
            LinearGradient(
                gradient: Gradient(
                    colors: [
                        Color(hex: "#2A3040"),
                        Color(hex: "#141821")
                    ]
                ),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .clipShape(
            CustomCorner(radius: 25, corners: [.topLeft, .topRight])
        )
        .edgesIgnoringSafeArea(.bottom)
    }
}

// 步数目标设置视图
struct StepGoalView: View {
    @Binding var stepGoal: Int
    @Binding var isPresented: Bool
    @State private var tempStepGoal: Int
    
    init(stepGoal: Binding<Int>, isPresented: Binding<Bool>) {
        self._stepGoal = stepGoal
        self._isPresented = isPresented
        self._tempStepGoal = State(initialValue: stepGoal.wrappedValue)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏 - 图5样式
            HStack(spacing: 0) {
                // 左侧蓝色小矩形标识
                Rectangle()
                    .fill(Color.blue)
                    .frame(width: 4, height: 20)
                    .padding(.trailing, 10)
                
                Text("Step Goals")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    isPresented = false
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, 16)
            .padding(.horizontal, 20)
            .background(Color(hex: "#272B3B"))
            
            Divider()
            
            // 步数选择器
            Picker("Step Goal", selection: $tempStepGoal) {
                ForEach(Array(stride(from: 1000, to: 30001, by: 1000)), id: \.self) { steps in
                    Text("\(steps)")
                        .foregroundColor(.white)
                        .tag(steps)
                }
            }
            .pickerStyle(WheelPickerStyle())
            .padding(.horizontal)
            .padding(.top, 20) // 增加顶部间距以弥补删除的组件
            .frame(height: 150)
            
            // 添加确认按钮
            Button(action: {
                // 保存设置
                stepGoal = tempStepGoal
                isPresented = false
            }) {
                Text("Confirm")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(height: 35)
                    .frame(maxWidth: .infinity)
                    .background(
                        ZStack {
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color(hex: "#0048B5").opacity(0.2))
                            
                            // 添加内层边框效果
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                        }
                    )
            }
            .padding(.horizontal, 18.5)
            .padding(.bottom, 20)
        }
        .background(
            LinearGradient(
                gradient: Gradient(
                    colors: [
                        Color(hex: "#2A3040"),
                        Color(hex: "#141821")
                    ]
                ),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .clipShape(
            CustomCorner(radius: 25, corners: [.topLeft, .topRight])
        )
        .edgesIgnoringSafeArea(.bottom)
    }
}

// 活动卡路里目标设置视图
struct ActivityCalorieGoalView: View {
    @Binding var calorieGoal: Int
    @Binding var isPresented: Bool
    @State private var tempCalorieGoal: Int
    
    init(calorieGoal: Binding<Int>, isPresented: Binding<Bool>) {
        self._calorieGoal = calorieGoal
        self._isPresented = isPresented
        self._tempCalorieGoal = State(initialValue: calorieGoal.wrappedValue)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏 - 图5样式
            HStack(spacing: 0) {
                // 左侧蓝色小矩形标识
                Rectangle()
                    .fill(Color.blue)
                    .frame(width: 4, height: 20)
                    .padding(.trailing, 10)
                
                Text("Activity Calorie Goal")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    isPresented = false
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, 16)
            .padding(.horizontal, 20)
            .background(Color(hex: "#272B3B"))
            
            Divider()
            
            // 显示当前选择的卡路里
            Text("\(tempCalorieGoal) kcal")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)
                .padding(.top, 20)
                .padding(.bottom, 20)
            
            // 卡路里选择器
            Picker("Calorie Goal", selection: $tempCalorieGoal) {
                ForEach(Array(stride(from: 100, to: 1001, by: 50)), id: \.self) { calories in
                    Text("\(calories)")
                        .foregroundColor(.white)
                        .tag(calories)
                }
            }
            .pickerStyle(WheelPickerStyle())
            .padding(.horizontal)
            .frame(height: 150)
            
            // 添加确认按钮
            Button(action: {
                // 保存设置
                calorieGoal = tempCalorieGoal
                isPresented = false
            }) {
                Text("Confirm")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(height: 35)
                    .frame(maxWidth: .infinity)
                    .background(
                        ZStack {
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color(hex: "#0048B5").opacity(0.2))
                            
                            // 添加内层边框效果
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                        }
                    )
            }
            .padding(.horizontal, 18.5)
            .padding(.bottom, 20)
        }
        .background(
            LinearGradient(
                gradient: Gradient(
                    colors: [
                        Color(hex: "#2A3040"),
                        Color(hex: "#141821")
                    ]
                ),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .clipShape(
            CustomCorner(radius: 25, corners: [.topLeft, .topRight])
        )
        .edgesIgnoringSafeArea(.bottom)
    }
}

// 活动时长目标设置视图
struct ActivityDurationGoalView: View {
    @Binding var activityHours: Int
    @Binding var activityMinutes: Int
    @Binding var isPresented: Bool
    @State private var tempHours: Int
    @State private var tempMinutes: Int
    
    init(activityHours: Binding<Int>, activityMinutes: Binding<Int>, isPresented: Binding<Bool>) {
        self._activityHours = activityHours
        self._activityMinutes = activityMinutes
        self._isPresented = isPresented
        self._tempHours = State(initialValue: activityHours.wrappedValue)
        self._tempMinutes = State(initialValue: activityMinutes.wrappedValue)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏 - 图5样式
            HStack(spacing: 0) {
                // 左侧蓝色小矩形标识
                Rectangle()
                    .fill(Color.blue)
                    .frame(width: 4, height: 20)
                    .padding(.trailing, 10)
                
                Text("Activity Duration Goal")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    isPresented = false
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, 16)
            .padding(.horizontal, 20)
            .background(Color(hex: "#272B3B"))
            
            Divider()
            
            // 显示当前选择的活动时长
            Text("\(tempHours) hr : \(tempMinutes) min")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)
                .padding(.top, 20)
                .padding(.bottom, 20)
            
            // 选择器
            HStack(spacing: 20) {
                // 小时选择器
                Picker("Hours", selection: $tempHours) {
                    ForEach(0...5, id: \.self) { hour in
                        Text("\(hour)")
                            .foregroundColor(.white)
                            .tag(hour)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(width: 50)
                .clipped()
                
                Text(":")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                
                // 分钟选择器
                Picker("Minutes", selection: $tempMinutes) {
                    ForEach(Array(stride(from: 0, to: 60, by: 5)), id: \.self) { minute in
                        Text("\(minute)")
                            .foregroundColor(.white)
                            .tag(minute)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(width: 50)
                .clipped()
            }
            .padding(.bottom, 20)
            
            // 添加确认按钮
            Button(action: {
                // 保存设置
                activityHours = tempHours
                activityMinutes = tempMinutes
                isPresented = false
            }) {
                Text("Confirm")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(height: 35)
                    .frame(maxWidth: .infinity)
                    .background(
                        ZStack {
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color(hex: "#0048B5").opacity(0.2))
                            
                            // 添加内层边框效果
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                        }
                    )
            }
            .padding(.horizontal, 18.5)
            .padding(.bottom, 20)
        }
        .background(
            LinearGradient(
                gradient: Gradient(
                    colors: [
                        Color(hex: "#2A3040"),
                        Color(hex: "#141821")
                    ]
                ),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .clipShape(
            CustomCorner(radius: 25, corners: [.topLeft, .topRight])
        )
        .edgesIgnoringSafeArea(.bottom)
    }
}
