//
//  SystemSettingsView.swift
//  WindRing
//
//  Created by zx on 2025/7/1.
//

import SwiftUI

/// 系统设置视图
struct SystemSettingsView: View {
    // MARK: - Alert Type
    private enum SystemAlertType {
        case sedentary, lowBattery, factoryReset, powerOff
    }
    
    // MARK: - 状态属性
    @Environment(\.presentationMode) var presentationMode
    @State private var darkMode = false
    @State private var language = 0
    @State private var timeFormat = 0
    @State private var measurementSystem = 0
    @State private var fontSize = 1
    @State private var sedentaryReminder : ActivityReminderModel = ActivityReminderModel.init(open: false)
    
    // 弹框状态
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var alertIcon = "exclamationmark.triangle"
    @State private var alertIconColor: Color = .red
    @State private var currentAlertType: SystemAlertType?
    
    @State private var showLanguagePicker = false
    @State private var showMeasurementUnitPicker = false
    @State private var showTemperatureUnitPicker = false
    @State private var selectedLanguageIndex = 0 // 0: English, 1: 中文
    @ObservedObject private var languageManager = LanguageManager.shared
    @ObservedObject private var userSettings = UserSettings.shared
    
    // 选项数组
    private let languages = ["English", "Traditional Chinese", "Simplified Chinese", "Japanese"]
    private let timeFormats = ["12-hour", "24-hour"]
    private let measurementSystems = ["metric".localized, "imperial".localized]
    private let fontSizes = ["Small", "Medium", "Large"]
    
    // MARK: - 主视图
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色 - 使用深色背景
            Color(hex: "#070708").ignoresSafeArea()
            
            // 使用封装的光晕背景组件
            CornerGlowBackground()
            
            VStack(spacing: 0) {
                // 自定义导航栏
                HStack(alignment: .center) {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    // 标题居左 - 添加点击返回功能
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("other_settings".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(.white)
                            .padding(.leading, 5)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .frame(height: 44) // 标准iOS导航栏高度
                .padding(.top, 8)
                
                // 设置内容
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 10) {
                        // 第一组卡片 - 区域和语言设置
                        VStack(spacing: 0) {
                            // 语言设置 - 弹出底部弹窗
                            HStack {
                                Image("s_1")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 22, height: 22)
                                    .padding(.leading, 8)
                                Text("language".localized)
                                    .font(.custom("PingFang-SC-Medium", size: 15))
                                    .foregroundColor(.white)
                                    .frame(height: 12, alignment: .leading)
                                    .padding(.leading, 10)
                                Spacer()
                                Text(languageManager.currentLanguageDisplayName)
                                    .foregroundColor(.gray)
                                Image(systemName: "chevron.right")
                                    .font(.system(size: 14))
                                    .foregroundColor(.gray)
                            }
                            .padding(.vertical, 16)
                            .padding(.horizontal)
                            .onTapGesture {
                                self.selectedLanguageIndex = languageManager.selectedLanguage == "zh-Hans" ? 1 : 0
                                withAnimation { showLanguagePicker = true }
                            }
                            Divider()
                                .background(Color.gray.opacity(0.3))
                                .padding(.leading, 60)
                            
                            // 度量单位设置
//                            HStack {
//                                Image("s_2")
//                                    .resizable()
//                                    .scaledToFit()
//                                    .frame(width: 22, height: 22)
//                                    .padding(.leading, 8)
//                                
//                                Text("system_settings_measurement_units".localized)
//                                    .font(.custom("PingFang-SC-Medium", size: 15))
//                                    .foregroundColor(.white)
//                                    .frame(height: 12, alignment: .leading)
//                                    .padding(.leading, 10)
//                                
//                                Spacer()
//                                
//                                Text(userSettings.measurementUnit.displayName)
//                                    .foregroundColor(.gray)
//                                
//                                Image(systemName: "chevron.right")
//                                    .font(.system(size: 14))
//                                    .foregroundColor(.gray)
//                            }
//                            .padding(.vertical, 16)
//                            .padding(.horizontal)
//                            .onTapGesture {
//                                withAnimation { showMeasurementUnitPicker = true }
//                            }
//                            
//                            Divider()
//                                .background(Color.gray.opacity(0.3))
//                                .padding(.leading, 60)
                            if VersionUpdateService.shared.status == 1{
                                // 温度单位设置
                                HStack {
                                    Image("s_3")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 22, height: 22)
                                        .padding(.leading, 8)
                                    
                                    Text("system_settings_temp_units".localized)
                                        .font(.custom("PingFang-SC-Medium", size: 15))
                                        .foregroundColor(.white)
                                        .frame(height: 12, alignment: .leading)
                                        .padding(.leading, 10)
                                    
                                    Spacer()
                                    
                                    Text(userSettings.temperatureUnit.id)
                                        .foregroundColor(.gray)
                                    
                                    Image(systemName: "chevron.right")
                                        .font(.system(size: 14))
                                        .foregroundColor(.gray)
                                }
                                .padding(.vertical, 16)
                                .padding(.horizontal)
                                .onTapGesture {
                                    withAnimation { showTemperatureUnitPicker = true }
                                }

                            }
                                                    }
                        .frame(width: 355, height: 112)
                        .background(Color(red: 0.06, green: 0.07, blue: 0.1))
                        .cornerRadius(25)
                        .padding(.top, 10)
                        
                        // 第二组卡片 - 提醒设置
                        VStack(spacing: 0) {
                            // 久坐提醒
                            HStack {
                                Image("s_4")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 22, height: 22)
                                    .padding(.leading, 8)
                                
                                Text("system_settings_sedentary_reminder".localized)
                                    .font(.custom("PingFang-SC-Medium", size: 15))
                                    .foregroundColor(.white)
                                    .frame(height: 12, alignment: .leading)
                                    .padding(.leading, 10)
                                
                                Spacer()
                                
                                Toggle("", isOn: $sedentaryReminder.open)
                                    .labelsHidden()
                                    .toggleStyle(SwitchToggleStyle(tint: .green))
                                    .allowsHitTesting(false)
                            }
                            .padding(.vertical, 16)
                            .padding(.horizontal)
                            .contentShape(Rectangle()) // 扩大点击区域
                            .onTapGesture {
                                currentAlertType = .sedentary
                                alertTitle = "alert_sedentary_reminder_title".localized
                                alertMessage = "alert_sedentary_reminder_message".localized
                                alertIcon = "chair"
                                alertIconColor = .orange
                                showAlert = true
                            }
                            
                            Divider()
                                .background(Color.gray.opacity(0.3))
                                .padding(.leading, 60)
                            
                            // 低电量提醒
                            HStack {
                                Image("s_5")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 22, height: 22)
                                    .padding(.leading, 8)
                                
                                Text("system_settings_low_battery_alert".localized)
                                    .font(.custom("PingFang-SC-Medium", size: 15))
                                    .foregroundColor(.white)
                                    .frame(height: 12, alignment: .leading)
                                    .padding(.leading, 10)
                                
                                Spacer()
                                
                                Toggle("", isOn: $userSettings.lowBatteryAlert)
                                    .labelsHidden()
                                    .toggleStyle(SwitchToggleStyle(tint: .green))
                                    .allowsHitTesting(false)
                            }
                            .padding(.vertical, 16)
                            .padding(.horizontal)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                currentAlertType = .lowBattery
                                alertTitle = "alert_low_battery_title".localized
                                alertMessage = "alert_low_battery_message".localized
                                alertIcon = "battery.25"
                                alertIconColor = .green
                                showAlert = true
                            }
                        }
                        .frame(width: 355, height: 123.5)
                        .background(Color(red: 0.06, green: 0.07, blue: 0.1))
                        .cornerRadius(25)
                        
                        // 第三组卡片 - 高级设置
                        VStack(spacing: 0) {
                            // 恢复出厂设置
                                HStack {
                                    Image("s_6")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 22, height: 22)
                                        .padding(.leading, 8)
                                    
                                    Text("system_settings_factory_reset".localized)
                                        .font(.custom("PingFang-SC-Medium", size: 15))
                                        .foregroundColor(.white)
                                        .frame(height: 12, alignment: .leading)
                                        .padding(.leading, 10)
                                    
                                    Spacer()
                                    
                                    Image(systemName: "chevron.right")
                                        .font(.system(size: 14))
                                        .foregroundColor(.gray)
                            }
                            .padding(.vertical, 16)
                            .padding(.horizontal)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                currentAlertType = .factoryReset
                                alertTitle = "alert_factory_reset_title".localized
                                alertMessage = "alert_factory_reset_message".localized
                                alertIcon = "power"
                                alertIconColor = .red
                                showAlert = true
                            }
                            
                            Divider()
                                .background(Color.gray.opacity(0.3))
                                .padding(.leading, 60)
                            
                            // 关机选项
                                HStack {
                                    Image("s_7")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 22, height: 22)
                                        .padding(.leading, 8)
                                    
                                    Text("system_settings_power_off".localized)
                                        .font(.custom("PingFang-SC-Medium", size: 15))
                                        .foregroundColor(.white)
                                        .frame(height: 12, alignment: .leading)
                                        .padding(.leading, 10)
                                    
                                    Spacer()
                                    
                                    Image(systemName: "chevron.right")
                                        .font(.system(size: 14))
                                        .foregroundColor(.gray)
                            }
                            .padding(.vertical, 16)
                            .padding(.horizontal)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                currentAlertType = .powerOff
                                alertTitle = "alert_power_off_title".localized
                                alertMessage = "alert_power_off_message".localized
                                alertIcon = "power"
                                alertIconColor = .red
                                showAlert = true
                            }
                        }
                        .frame(width: 355, height: 123.5)
                        .background(Color(red: 0.06, green: 0.07, blue: 0.1))
                        .cornerRadius(25)
                        
                        // 底部间距
                        Spacer(minLength: 50)
                    }
                    .padding(.top, 16)
                    .padding(.horizontal, 10)
                }
                .onAppear {
                    CRPSmartRingManage.shared.getActivityReminderInfo { reminderModel, error in
                        if error == nil && reminderModel != nil {
                            self.sedentaryReminder = reminderModel!
                        }
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .overlay(
            LanguagePickerSheet(
                isPresented: $showLanguagePicker,
                selectedLanguage: $selectedLanguageIndex,
                onConfirm: {
                    let newLang = self.selectedLanguageIndex == 1 ? "zh-Hans" : "en"
                    self.languageManager.selectedLanguage = newLang
                }
            )
        )
        .overlay(
            MeasurementUnitPickerSheet(
                isPresented: $showMeasurementUnitPicker,
                selectedUnit: $userSettings.measurementUnit
            )
        )
        .overlay(
            TemperatureUnitPickerSheet(
                isPresented: $showTemperatureUnitPicker,
                selectedUnit: $userSettings.temperatureUnit
            )
        )
        .overlay(
            ZStack {
                if showAlert {
                    CustomAlertView(
                        title: alertTitle,
                        message: alertMessage,
                        icon: alertIcon,
                        iconColor: alertIconColor,
                        onCancel: {
                            showAlert = false
                        },
                        onConfirm: {
                            if let type = currentAlertType {
                                switch type {
                                case .sedentary:
                                    sedentaryReminder.open.toggle()
                                    CRPSmartRingManage.shared.setActivityReminder(sedentaryReminder)
                                case .lowBattery:
                                    userSettings.lowBatteryAlert.toggle()
                                case .factoryReset:
                                    CRPSmartRingManage.shared.restoreFactory { state, error in
                                        if error == .none  {
                                            "factory_reset_success_toast".localized.showToast()
                                        }else{
                                            "factory_reset_failed_toast".localized.showToast()
                                        }
                                        
                                    }
                                    print("Factory Reset confirmed and initiated.")
                                case .powerOff:
                                    CRPSmartRingManage.shared.shutdown { state, error in
                                        if error == .none   {
                                            "shutdown_success_toast".localized.showToast()
                                        }else{
                                            "shutdown_failed_toast".localized.showToast()
                                        }
                                    }
                                    print("Power Off confirmed and initiated.")
                                }
                            }
                            showAlert = false
                        }
                    )
                }
            }
        )
    }
}
/// 语言选择底部弹窗（严格底部弹出）
struct LanguagePickerSheet: View {
    @Binding var isPresented: Bool
    @Binding var selectedLanguage: Int // 0: English, 1: 中文
    private let languages = ["language_selection_english".localized, "language_selection_simplified_chinese".localized]
    var onConfirm: (() -> Void)? = nil

    var body: some View {
        ZStack(alignment: .bottom) {
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation { isPresented = false }
                    }
                
                VStack(spacing: 0) {
                    HStack {
                        Text("language_selection_title".localized)
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                        Spacer()
                        Button(action: { withAnimation { isPresented = false } }) {
                            Image(systemName: "xmark")
                                .foregroundColor(.white)
                                .padding(8)
                        }
                    }
                    .padding(.horizontal)
                    .padding(.top, 16)
                    .padding(.bottom, 8)

                    Picker(selection: $selectedLanguage, label: Text("")) {
                        ForEach(0..<languages.count, id: \.self) { idx in
                            Text(languages[idx])
                                .foregroundColor(selectedLanguage == idx ? .white : .gray)
                                .font(.system(size: 18, weight: selectedLanguage == idx ? .bold : .regular))
                                .tag(idx)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(height: 120)
                    .clipped()

                    Spacer().frame(height: 10)

                    Button(action: {
                        withAnimation { isPresented = false }
                        onConfirm?()
                    }) {
                        Text("confirm".localized)
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 14)
                            .background(RoundedRectangle(cornerRadius: 12).fill(Color.blue))
                            .padding(.horizontal, 24)
                    }
                    .padding(.bottom, 24)
                }
                .background(Color(red: 0.13, green: 0.14, blue: 0.18))
                .cornerRadius(20)
                .frame(maxWidth: .infinity)
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .padding(.bottom, 0)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .ignoresSafeArea()
        .animation(.easeInOut, value: isPresented)
    }
}

/// 度量单位选择弹窗
struct MeasurementUnitPickerSheet: View {
    @Binding var isPresented: Bool
    @Binding var selectedUnit: MeasurementUnit
    var body: some View {
        ZStack(alignment: .bottom) {
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture { withAnimation { isPresented = false } }
                VStack(spacing: 0) {
                    HStack {
                        Text("system_settings_measurement_units".localized)
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                        Spacer()
                        Button(action: { withAnimation { isPresented = false } }) {
                            Image(systemName: "xmark")
                                .foregroundColor(.white)
                                .padding(8)
                        }
                    }
                    .padding(.horizontal)
                    .padding(.top, 16)
                    .padding(.bottom, 8)
                    Picker(selection: $selectedUnit, label: Text("")) {
                        ForEach(MeasurementUnit.allCases) { unit in
                            Text(unit.displayName)
                                .foregroundColor(selectedUnit == unit ? .white : .gray)
                                .font(.system(size: 18, weight: selectedUnit == unit ? .bold : .regular))
                                .tag(unit)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(height: 120)
                    .clipped()
                    Spacer().frame(height: 10)
                    Button(action: { withAnimation { isPresented = false } }) {
                        Text("confirm".localized)
                            .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 14)
                            .background(RoundedRectangle(cornerRadius: 12).fill(Color.blue))
                            .padding(.horizontal, 24)
                    }
                    .padding(.bottom, 24)
                }
                .background(Color(red: 0.13, green: 0.14, blue: 0.18))
                .cornerRadius(20)
                .frame(maxWidth: .infinity)
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .padding(.bottom, 0)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .ignoresSafeArea()
        .animation(.easeInOut, value: isPresented)
    }
}

/// 温度单位选择弹窗
struct TemperatureUnitPickerSheet: View {
    @Binding var isPresented: Bool
    @Binding var selectedUnit: TemperatureUnit
    var body: some View {
        ZStack(alignment: .bottom) {
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture { withAnimation { isPresented = false } }
                VStack(spacing: 0) {
                    HStack {
                        Text("system_settings_temp_units".localized)
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                        Spacer()
                        Button(action: { withAnimation { isPresented = false } }) {
                            Image(systemName: "xmark")
                                .foregroundColor(.white)
                                .padding(8)
                        }
                    }
                .padding(.horizontal)
                    .padding(.top, 16)
                    .padding(.bottom, 8)
                    Picker(selection: $selectedUnit, label: Text("")) {
                        ForEach(TemperatureUnit.allCases) { unit in
                            Text(unit.id)
                                .foregroundColor(selectedUnit == unit ? .white : .gray)
                                .font(.system(size: 18, weight: selectedUnit == unit ? .bold : .regular))
                                .tag(unit)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(height: 120)
                    .clipped()
                    Spacer().frame(height: 10)
                    Button(action: { withAnimation { isPresented = false } }) {
                        Text("confirm".localized)
                            .font(.system(size: 17, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                            .padding(.vertical, 14)
                            .background(RoundedRectangle(cornerRadius: 12).fill(Color.blue))
                            .padding(.horizontal, 24)
                    }
                    .padding(.bottom, 24)
            }
                .background(Color(red: 0.13, green: 0.14, blue: 0.18))
            .cornerRadius(20)
                .frame(maxWidth: .infinity)
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .padding(.bottom, 0)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .ignoresSafeArea()
        .animation(.easeInOut, value: isPresented)
    }
}
