import SwiftUI
import UIKit

struct LoginView: View {
    // MARK: - State properties
    @State private var loginMethod: LoginMethod = .mobile
    @State private var mobile: String = ""
    @State private var email: String = ""
    @State private var password: String = ""
    @State private var showErrorMessage: Bool = false
    @State private var errorMessage: String = ""
    @State private var showingRegisterView: Bool = false
    @State private var showingResetPasswordView: Bool = false
    @State private var loginSuccess: Bool = false
    @State private var isLoading: Bool = false
    @State private var agreedToTerms: Bool = false
    @State private var indicatorPosition: Int = 0  // 0: Mobile, 1: Email, 2: Sms Login
    @State private var showingTermsWebView = false
    @State private var showingPrivacyWebView = false
    // 短信验证码登录相关状态
    @State private var verificationCode: String = ""
    @State private var isCountingDown: Bool = false
    @State private var countdown: Int = 60
    @State private var timer: Timer? = nil
    @State private var selectedCountryCode: String = ""
    
    // 进入登录页面时，清空之前的登录参数
    @State private var isInitialized: Bool = false
    
    // 外部传入的绑定，用于在登录成功后更新外部状态
    @Binding var showLoginView: Bool
    @Binding var username: String
    
    // 初始化方法，支持不带参数的默认初始化
    init() {
        // 使用临时状态变量初始化绑定
        self._showLoginView = .constant(true)
        self._username = .constant("")
    }
    
    // 初始化方法，接受外部绑定
    init(showLoginView: Binding<Bool>, username: Binding<String>) {
        self._showLoginView = showLoginView
        self._username = username
    }
    
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Enums
    enum LoginMethod {
        case mobile
        case email
    }
    
    // MARK: - Main view
    var body: some View {
        NavigationView {
            ZStack {
                // 深色背景
                Color(hex: "#070708")  // 使用指定的更深的黑色背景
                    .edgesIgnoringSafeArea(.all)
                
                // 顶部背景图
                VStack {
                    Image("Login_bg")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: UIScreen.main.bounds.width * 1.2)
                        .offset(y: -UIScreen.main.bounds.height * 0.01)
                        .opacity(0.8)
                    
                    Spacer()
                }
                .edgesIgnoringSafeArea(.top)
                
                // 主要内容
                ScrollView {
                    VStack(spacing: 0) { // 首先添加一个外层VStack
                        Spacer()
                            .frame(height: 35) // 添加顶部空间，将内容下移
                        
                        VStack(spacing: 20) {
                            // 顶部返回按钮
                            HStack {
                                Button(action: {
                                    presentationMode.wrappedValue.dismiss()
                                }) {
                                    Image(systemName: "chevron.left")
                                        .foregroundColor(.white)
                                        .imageScale(.large)
                                        .padding()
                                }
                                Spacer()
                            }
                            
                            // 欢迎标题
                            Text("login_welcome_title".localized)
                                .font(.custom("SourceHanSansCN-Bold", size: 26))
                                .foregroundColor(.white)
                                .lineSpacing(4)
                                .multilineTextAlignment(.leading)
                                .frame(width: 280, alignment: .leading)
                                .padding(.top, 40)
                                .padding(.bottom, 10)
                                .padding(.leading, -40)  // 精确设置左边距，使其与卡片内容左边缘对齐
                            
                            // 登录方式选择器（作为独立元素）
                            loginMethodPicker
                                .padding(.bottom, 0)  // 移除底部内边距
                                .zIndex(1)  // 确保选择器显示在顶层
                            
                            // 深色卡片背景
                            VStack(spacing: 25) {
                                // 输入表单
                                VStack(spacing: 20) {
                                    if indicatorPosition == 0 {
                                        // 手机号输入 (带国家代码 +86)
                                        HStack {
//                                            countryCodePicker
//                                                .padding(.leading, -16)
//                                            Divider()
//                                                .background(Color.gray.opacity(0.5))
//                                                .frame(height: 20)
                                            
                                            TextField("", text: $mobile)
                                                .keyboardType(.phonePad)
                                                .foregroundColor(.white)
                                                .font(.custom("PingFang-SC-Medium", size: 12))
                                                .placeholder(when: mobile.isEmpty) {
                                                    Text("login_phone_placeholder".localized)
                                                        .foregroundColor(Color(hex: "#666666"))
                                                        .font(.custom("PingFang-SC-Medium", size: 12))
                                                        .lineSpacing(8)
                                                }
                                        }
                                        .padding()
                                        .frame(height: 35) // 设置固定高度为35点
                                        .background(Color(hex: "#070708").opacity(0.34)) // 填充34%，颜色#070708
                                        .cornerRadius(10) // 圆角10pt
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 10)
                                                .stroke(Color(hex: "#434242"), lineWidth: 0.5) // 内边框粗细0.5pt，颜色#434242
                                        )
                                        
                                        // 密码输入
                                        SecureField("", text: $password)
                                            .foregroundColor(.white)
                                            .font(.custom("PingFang-SC-Medium", size: 12))
                                            .placeholder(when: password.isEmpty) {
                                                Text("login_password_placeholder".localized)
                                                    .foregroundColor(Color(hex: "#666666"))
                                                    .font(.custom("PingFang-SC-Medium", size: 12))
                                                    .lineSpacing(8)
                                            }
                                            .padding()
                                            .frame(height: 35) // 设置固定高度为35点
                                            .background(Color(hex: "#070708").opacity(0.34)) // 填充34%，颜色#070708
                                            .cornerRadius(10) // 圆角10pt
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 10)
                                                    .stroke(Color(hex: "#434242"), lineWidth: 0.5) // 内边框粗细0.5pt，颜色#434242
                                            )
                                    } else if indicatorPosition == 1 {
                                        // 邮箱输入
                                        TextField("", text: $email)
                                            .keyboardType(.emailAddress)
                                            .foregroundColor(.white)
                                            .font(.custom("PingFang-SC-Medium", size: 12))
                                            .placeholder(when: email.isEmpty) {
                                                Text("login_email_placeholder".localized)
                                                    .foregroundColor(Color(hex: "#666666"))
                                                    .font(.custom("PingFang-SC-Medium", size: 12))
                                                    .lineSpacing(8)
                                            }
                                            .padding()
                                            .frame(height: 35) // 设置固定高度为35点
                                            .background(Color(hex: "#070708").opacity(0.34)) // 填充34%，颜色#070708
                                            .cornerRadius(10) // 圆角10pt
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 10)
                                                    .stroke(Color(hex: "#434242"), lineWidth: 0.5) // 内边框粗细0.5pt，颜色#434242
                                            )
                                        
                                        // 密码输入
                                        SecureField("", text: $password)
                                            .foregroundColor(.white)
                                            .font(.custom("PingFang-SC-Medium", size: 12))
                                            .placeholder(when: password.isEmpty) {
                                                Text("login_password_placeholder".localized)
                                                    .foregroundColor(Color(hex: "#666666"))
                                                    .font(.custom("PingFang-SC-Medium", size: 12))
                                                    .lineSpacing(8)
                                            }
                                            .padding()
                                            .frame(height: 35) // 设置固定高度为35点
                                            .background(Color(hex: "#070708").opacity(0.34))
                                            .cornerRadius(10)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 10)
                                                    .stroke(Color(hex: "#434242"), lineWidth: 0.5)
                                            )
                                    } else {
                                        // SMS登录界面
                                        // 手机号输入
                                        HStack {
                                            countryCodePicker
                                                .padding(.leading, -16)
                                            Divider()
                                                .background(Color.gray.opacity(0.5))
                                                .frame(height: 20)
                                            
                                            TextField("", text: $mobile)
                                                .keyboardType(.phonePad)
                                                .foregroundColor(.white)
                                                .font(.custom("PingFang-SC-Medium", size: 12))
                                                .placeholder(when: mobile.isEmpty) {
                                                    Text("login_phone_placeholder".localized)
                                                        .foregroundColor(Color(hex: "#666666"))
                                                        .font(.custom("PingFang-SC-Medium", size: 12))
                                                        .lineSpacing(8)
                                                }
                                        }
                                        .padding()
                                        .frame(height: 35)
                                        .background(Color(hex: "#070708").opacity(0.34))
                                        .cornerRadius(10)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 10)
                                                .stroke(Color(hex: "#434242"), lineWidth: 0.5)
                                        )
                                        
                                        // 验证码输入框
                                        HStack {
                                            TextField("", text: $verificationCode)
                                                .keyboardType(.numberPad)
                                                .foregroundColor(.white)
                                                .font(.custom("PingFang-SC-Medium", size: 12))
                                                .placeholder(when: verificationCode.isEmpty) {
                                                    Text("login_code_placeholder".localized)
                                                        .foregroundColor(Color(hex: "#666666"))
                                                        .font(.custom("PingFang-SC-Medium", size: 12))
                                                }
                                            
                                            Spacer()
                                            
                                            Divider()
                                                .background(Color.gray.opacity(0.5))
                                                .frame(height: 20)
                                                .padding(.horizontal, 8)
                                            Button(action: getVerificationCode) {
                                                Text(isCountingDown ? "\(countdown)s" : "login_send_code".localized)
                                                    .foregroundColor(isCountingDown ? .gray : .white)
                                                    .font(.system(size: 12))
//                                                    .background(
//                                                        isCountingDown
//                                                        ? Color.gray.opacity(0.3)
//                                                        : WRTheme.Colors.primary
//                                                    )
                                                    .cornerRadius(8)
                                            }
                                            .disabled(isCountingDown || !isValidMobile(mobile))
//                                            Button(action: {
//                                                getVerificationCode()
//                                            }) {
//                                                Text(isGettingCode ? "\(countdownSeconds)s" : "Get Code")
//                                                    .font(.system(size: 12))
//                                                    .foregroundColor(.white)
//                                            }
//                                            .disabled(isGettingCode || mobile.isEmpty)
                                        }
                                        .padding()
                                        .frame(height: 35)
                                        .background(Color(hex: "#070708").opacity(0.34))
                                        .cornerRadius(10)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 10)
                                                .stroke(Color(hex: "#434242"), lineWidth: 0.5)
                                        )
                                    }
                                    
                                    // 找回密码链接 - 只在非SMS登录模式显示
                                    if indicatorPosition != 2 {
                                        HStack {
                                            Spacer()
                                            Button(action: { showingResetPasswordView = true }) {
                                                Text("login_retrieve_password".localized)
                                                    .font(.footnote)
                                                    .foregroundColor(.white)
                                             }
                                        }
                                        .background(
                                            NavigationLink(destination: ResetPasswordView(onResetSuccess: {
                                                showingResetPasswordView = false
                                            }), isActive: $showingResetPasswordView) { EmptyView() }
                                                .hidden()
                                        )
                                        .padding(.top, -10)
                                        .padding(.trailing, 5)
                                    }
                                }
                                
                                // 登录按钮
                                Button(action: {
                                    login()
                                }) {
                                    if isLoading {
                                        ZStack {
                                            Text("login_button_text".localized)
                                                .font(.custom("PingFang-SC-Bold", size: 13))
                                                .foregroundColor(.clear) // 隐藏文本但保持布局
                                                
                                            ProgressView()
                                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        }
                                        .frame(width: 279.1, height: 35)
                                        .background(Color(red: 0, green: 0.28, blue: 0.71))
                                        .cornerRadius(17.5)
                                    } else {
                                        Text("login_button_text".localized)
                                            .font(.custom("PingFang-SC-Bold", size: 13))
                                            .foregroundColor(.white)
                                            .frame(width: 279.1, height: 35)
                                            .background(Color(red: 0, green: 0.28, blue: 0.71))
                                            .cornerRadius(17.5)
                                    }
                                }
                                .disabled(isLoading)
                                .padding(.top, 10)
                                .padding(.horizontal, 10)
                                
                                // 注册新账号链接
                                 Button(action: {
                                     showingRegisterView = true
                                 }) {
                                    Text("login_register_account".localized)
                                        .foregroundColor(.white)
                                        .font(.custom("PingFang-SC-Bold", size: 13))
                                        .frame(width: 147, height: 12.5)
                                 }
                                 .background(
                                     NavigationLink(
                                        destination: RegisterView(onRegistrationComplete: {
                                            self.showingRegisterView = false
                                        }),
                                        isActive: $showingRegisterView,
                                        label: { EmptyView() }
                                     ).hidden()
                                 )
                                .padding(.top, 5)
                                
                                // 使用条款和隐私政策
                                HStack(alignment: .top) {
                                    Button(action: {
                                        agreedToTerms.toggle()
                                    }) {
                                        ZStack {
                                            // 外层灰色圆形
                                            Circle()
                                                .frame(width: 11, height: 11)
                                                .foregroundColor(Color(red: 0.66, green: 0.66, blue: 0.66))
                                            
                                            // 内层深色圆形
                                            Circle()
                                                .frame(width: 9, height: 9)
                                                .foregroundColor(Color(red: 0.13, green: 0.13, blue: 0.16))
                                            
                                            // 选中状态显示小圆点
                                            if agreedToTerms {
                                                Circle()
                                                    .frame(width: 5, height: 5)
                                                    .foregroundColor(.white)
                                            }
                                        }
                                    }
                                    .padding(.top, 2)
                                    
                                    Text(termsAndPrivacyText)
                                        .font(.custom("PingFang-SC-Medium", size: 12))
                                        .foregroundColor(.white)
                                        .lineLimit(nil)
                                        .environment(\.openURL, OpenURLAction { url in
                                            if url.absoluteString == "show_terms" {
                                                showingTermsWebView = true
                                            } else if url.absoluteString == "show_privacy_policy" {
                                                showingPrivacyWebView = true
                                            }
                                            return .handled
                                        })
                                }
                                .padding(.top, 15)
                                .padding(.bottom, 15)
                            }
                            .padding(20)
                            .frame(width: 330)  // 只固定宽度，让高度自适应
                            .background(
                                RoundedCorners(topLeft: 0, topRight: 0, bottomLeft: 15, bottomRight: 15)
                                    .fill(Color(red: 0.08, green: 0.1, blue: 0.13))  // 设置参考代码中的背景色
                            )
                            .shadow(color: Color.black.opacity(0.34), radius: 11.5, x: 0, y: 0)  // 添加阴影效果
                            .padding(.horizontal, 22.5)  // 水平边距设置为参考代码中的x值
                            .padding(.top, -20)  // 向上偏移，使卡片与选择器重叠
                        }
                        .padding(.horizontal, 0)  // 移除外部的水平内边距，因为已经在卡片上设置了边距
                    }
                }
            }
            .background(
                VStack {
                    NavigationLink(destination: CommonWebView(tag: .termsOfUse), isActive: $showingTermsWebView) { EmptyView() }
                    NavigationLink(destination: CommonWebView(tag: .privacyPolicy), isActive: $showingPrivacyWebView) { EmptyView() }
                }
            )
            .navigationBarHidden(true)
        }
        .onChange(of: loginSuccess) { newValue in
            if newValue {
                dismiss()
            }
        }
        .onAppear {
            if !isInitialized {
                if let langCode = Locale.current.language.languageCode?.identifier, langCode.lowercased().hasPrefix("en") {
                    self.selectedCountryCode = "+1"
                } else {
                    self.selectedCountryCode = "+86"
                }
                isInitialized = true
            }
        }
    }
    
    // MARK: - Login method selector
    private var loginMethodPicker: some View {
        ZStack {
            // 背景
            RoundedCorners(topLeft: 15, topRight: 15, bottomLeft: 0, bottomRight: 0)
                .fill(Color(red: 0.12, green: 0.14, blue: 0.18))
                .frame(width: 330, height: 60)
                .shadow(color: Color.black.opacity(0.2), radius: 10, x: 0, y: 0)
            
            // 主容器 - 调整为垂直居中
            VStack {
                // 选项按钮行
                HStack(spacing: 0) {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.25)) {
                            loginMethod = .mobile
                            indicatorPosition = 0
                        }
                        showErrorMessage = false
                    }) {
                        Text("login_mobile_tab_title".localized)
                            .font(.system(size: 15))
                            .fontWeight(indicatorPosition == 0 ? .bold : .regular)
                            .foregroundColor(indicatorPosition == 0 ? .white : .gray.opacity(0.7))
                            .frame(maxWidth: .infinity)
                    }
                    
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.25)) {
                            loginMethod = .email
                            indicatorPosition = 1
                        }
                        showErrorMessage = false
                    }) {
                        Text("login_email_tab_title".localized)
                            .font(.system(size: 15))
                            .fontWeight(indicatorPosition == 1 ? .bold : .regular)
                            .foregroundColor(indicatorPosition == 1 ? .white : .gray.opacity(0.7))
                            .frame(maxWidth: .infinity)
                    }
                    
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.25)) {
                            indicatorPosition = 2
                        }
                        showErrorMessage = false
                    }) {
                        Text("login_sms_tab_title".localized)
                            .font(.system(size: 15))
                            .fontWeight(indicatorPosition == 2 ? .bold : .regular)
                            .foregroundColor(indicatorPosition == 2 ? .white : .gray.opacity(0.7))
                            .frame(maxWidth: .infinity)
                    }
                }
            }
            .frame(height: 36) // 设置适当的高度使标题居中
            .padding(.top, 4) // 稍微向下移动一点
            
            // 指示器行 - 单独放在底部，使用动画
            VStack {
                Spacer()
                ZStack(alignment: .leading) {
                    // 使用GeometryReader计算滑块位置
                    GeometryReader { geometry in
                        let totalWidth = geometry.size.width
                        let itemWidth = totalWidth / 3
                        let indicatorWidth: CGFloat = 20 // 滑块宽度
                        
                        // 滑块 - 会有动画效果移动
                        RoundedRectangle(cornerRadius: 2)
                            .fill(Color.white)
                            .frame(width: indicatorWidth, height: 4)
                            .offset(x: calculateIndicatorOffset(totalWidth: totalWidth, indicatorWidth: indicatorWidth), y: 0)
                    }
                }
                .frame(height: 4)
                .padding(.bottom, 0) // 移除底部内边距，使滑块贴近标题栏底部
            }
            .frame(width: 330)
        }
        .frame(width: 330, height: 60)
    }
    
    // 创建包含可点击链接的属性字符串
    private var termsAndPrivacyText: AttributedString {
        let fullString = "login_terms_agreement".localized
        var attributedString = AttributedString(fullString)
        
        // 使用一个通用颜色
        let linkColor = Color(hex: "#4A90E2")

        func applyLink(to text: String, urlString: String) {
            if let range = attributedString.range(of: text) {
                attributedString[range].link = URL(string: urlString)
                attributedString[range].foregroundColor = linkColor
                attributedString[range].underlineStyle = .single
            }
        }

        // 为中英文版本应用链接
        applyLink(to: "Terms of Use", urlString: "show_terms")
        applyLink(to: "Privacy Policy", urlString: "show_privacy_policy")
        applyLink(to: "用户协议", urlString: "show_terms")
        applyLink(to: "隐私政策", urlString: "show_privacy_policy")
        
        return attributedString
    }
    
    // 计算滑动指示器的偏移量
    private func calculateIndicatorOffset(totalWidth: CGFloat, indicatorWidth: CGFloat) -> CGFloat {
        let itemWidth = totalWidth / 3
        
        // 计算指示器的中心位置
        let centerOffset = (itemWidth - indicatorWidth) / 2
        return CGFloat(indicatorPosition) * itemWidth + centerOffset
    }
    
    // MARK: - Login verification
    private func login() {
        // 首先检查是否同意条款
        if !agreedToTerms {
            "login_error_agree_terms".localized.showToast()
            return
        }
        
        // 根据选择的登录方式进行不同的表单验证
        if indicatorPosition == 0 {
            // 手机号+密码登录
            guard !mobile.isEmpty && isValidMobile(mobile) && !password.isEmpty else {
                if !isValidMobile(mobile) {
                    "login_error_invalid_phone".localized.showToast()
                } else if password.isEmpty {
                    "login_error_enter_password".localized.showToast()
                }
                return
            }
            
            // 开始加载
            isLoading = true
            
            // 调用手机号密码登录API
 
            AuthService.shared.loginWithMobile(mobile: mobile, password: password) { result in
                DispatchQueue.main.async {
                    self.isLoading = false
                    
                    switch result {
                    case .success(let user):
                        handleLoginSuccess(user: user)
                    case .failure(let error):
                        error.localizedDescription.showToast()
                    }
                }
            }

//            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { // Simulate network request
//                isLoading = false
//                handleLoginSuccess(user: "TempUser")
//            }
        } else if indicatorPosition == 1 {
            // 邮箱+密码登录
            guard !email.isEmpty && isValidEmail(email) && !password.isEmpty else {
                if !isValidEmail(email) {
                    "login_error_invalid_email".localized.showToast()
                } else if password.isEmpty {
                    "login_error_enter_password".localized.showToast()
                }
                return
            }
            
            // 开始加载
            isLoading = true
            
            // 调用邮箱密码登录API
            // Commented out due to missing AuthService

            AuthService.shared.login(email: email, password: password) { result in
                DispatchQueue.main.async {
                    self.isLoading = false
                    
                    switch result {
                    case .success(let user):
                        handleLoginSuccess(user: user)
                    case .failure(let error):
                        self.errorMessage = error.localizedDescription
                        self.showErrorMessage = true
                    }
                }
            }

//            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { // Simulate network request
//                isLoading = false
//                handleLoginSuccess(user: "TempUser")
//            }
        } else {
            // 短信验证码登录
            guard !mobile.isEmpty && isValidMobile(mobile) && !verificationCode.isEmpty else {
                if !isValidMobile(mobile) {
                    "login_error_invalid_phone".localized.showToast()
                } else if verificationCode.isEmpty {
                    "login_error_enter_code".localized.showToast()
                }
                return
            }
            
            // 开始加载
            isLoading = true
            
            // 模拟验证码登录成功
            // 这里应该调用真实的API，但由于AuthService没有实现loginWithSMS方法，先模拟成功
//            /app-api/member/auth/sms-login
            // Commented out due to missing AuthService
            AuthService.shared.login(mobile: mobile, code: verificationCode, completion:  { result in
                DispatchQueue.main.async {
                    self.isLoading = false
                    
                    switch result {
                    case .success(let user):
                        handleLoginSuccess(user: user)
                    case .failure(let error):
                        error.localizedDescription.showToast()
                    }
                }
            })

            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.isLoading = false
                
                // 模拟登录成功
                self.username = self.mobile
                UserDefaults.standard.set(true, forKey: "isLoggedIn")
                UserDefaults.standard.set(self.username, forKey: "userName")
                
                self.loginSuccess = true
                self.showLoginView = false
            }
        }
    }
    
    // 登录成功后的公共处理
    private func handleLoginSuccess(user: Any) {
        // 这里应该处理真实的用户数据，但由于User类型缺失，简化处理
        if !mobile.isEmpty {
            username = mobile
        } else if !email.isEmpty {
            username = email
        }
        
        // 设置全局登录状态
        UserDefaults.standard.set(true, forKey: "isLoggedIn")
        UserDefaults.standard.set(username, forKey: "userName")
        
        // 更新 AppState
//        if let appState = (UIApplication.shared.connectedScenes.first?.delegate as? SceneDelegate)?.appState {
//            appState.login(userId: username)
//        }
        
        // 关闭登录页面
        self.loginSuccess = true
        self.showLoginView = false
        
        // 强制更新视图
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: NSNotification.Name("LoginStateChanged"), object: nil)
        }
    }
    
    // 获取短信验证码
    private func getVerificationCode() {
        guard !mobile.isEmpty && isValidMobile(mobile) else {
            "login_error_invalid_phone".localized.showToast()
            return
        }
        
        // 开始倒计时
        isCountingDown = true
        countdown = 60
        
        // 创建计时器
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { timer in
            if self.countdown > 0 {
                self.countdown -= 1
            } else {
                self.isCountingDown = false
                timer.invalidate()
            }
        }
        
        AuthService.shared.sendSMSCode(mobile: mobile, scene: 1, countryCode: selectedCountryCode) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(_):
                    print("login_code_sent".localized + ": \(self.mobile)")
                case .failure(let error):
                    self.isCountingDown = false
                    self.timer?.invalidate()
                    error.localizedDescription.showToast()
                }
            }
        }
    }
    
    // MARK: - Validation helpers
    
    /// 验证手机号格式
    private func isValidMobile(_ mobile: String) -> Bool {
        return mobile.count == 11 && mobile.allSatisfy { $0.isNumber }
    }
    
    /// 验证邮箱格式
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    private var isFormValid: Bool {
        if loginMethod == .mobile {
            return !mobile.isEmpty && isValidMobile(mobile) && !password.isEmpty
        } else if loginMethod == .email {
            return !email.isEmpty && isValidEmail(email) && !password.isEmpty
        }
        return false
    }
    
    private func isValidPassword(_ password: String) -> Bool {
        return !password.isEmpty
    }
    
    private var countryCodePicker: some View {
        Menu {
            Button("+1") { selectedCountryCode = "+1" }
            Button("+86") { selectedCountryCode = "+86" }
        } label: {
            HStack(spacing: 4) {
                Text(selectedCountryCode)
                    .font(.custom("PingFang-SC-Medium", size: 13))
                    .foregroundColor(.white)
                    .frame(minWidth: 40, alignment: .center)
                Image(systemName: "chevron.down")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 10)
        }
    }
}

// MARK: - Preview
struct LoginView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            LoginView()
        }
    }
}

// MARK: - View扩展，提供自定义placeholder功能
extension View {
    func placeholder<Content: View>(
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> Content) -> some View {
        
        ZStack(alignment: alignment) {
            placeholder().opacity(shouldShow ? 1 : 0)
            self
        }
    }
}


// MARK: - 自定义形状
/// 自定义圆角形状，可以单独设置各个角的圆角半径
struct RoundedCorners: Shape {
    var topLeft: CGFloat = 0
    var topRight: CGFloat = 0
    var bottomLeft: CGFloat = 0
    var bottomRight: CGFloat = 0

    func path(in rect: CGRect) -> Path {
        var path = Path()

        let width = rect.size.width
        let height = rect.size.height

        // 确保半径不超过宽度/高度的一半
        let tr = min(min(topRight, width/2), height/2)
        let tl = min(min(topLeft, width/2), height/2)
        let bl = min(min(bottomLeft, width/2), height/2)
        let br = min(min(bottomRight, width/2), height/2)

        // 开始路径
        path.move(to: CGPoint(x: width / 2, y: 0))
        
        // 顶部右侧
        path.addLine(to: CGPoint(x: width - tr, y: 0))
        path.addArc(center: CGPoint(x: width - tr, y: tr), 
                   radius: tr, 
                   startAngle: Angle(degrees: -90), 
                   endAngle: Angle(degrees: 0), 
                   clockwise: false)
                   
        // 右侧
        path.addLine(to: CGPoint(x: width, y: height - br))
        path.addArc(center: CGPoint(x: width - br, y: height - br), 
                   radius: br, 
                   startAngle: Angle(degrees: 0), 
                   endAngle: Angle(degrees: 90), 
                   clockwise: false)
                   
        // 底部
        path.addLine(to: CGPoint(x: bl, y: height))
        path.addArc(center: CGPoint(x: bl, y: height - bl), 
                   radius: bl, 
                   startAngle: Angle(degrees: 90), 
                   endAngle: Angle(degrees: 180), 
                   clockwise: false)
                   
        // 左侧
        path.addLine(to: CGPoint(x: 0, y: tl))
        path.addArc(center: CGPoint(x: tl, y: tl), 
                   radius: tl, 
                   startAngle: Angle(degrees: 180), 
                   endAngle: Angle(degrees: 270), 
                   clockwise: false)
                   
        path.closeSubpath()
        return path
    }
} 
