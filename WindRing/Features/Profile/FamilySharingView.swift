import SwiftUI
import UIKit
//import CRPSmartRing

struct FamilySharingView: View {
    @AppStorage("hasEmbarkedOnFamilySharing") private var hasEmbarkedOnFamilySharing: Bool = false

    var body: some View {
        if hasEmbarkedOnFamilySharing {
            FamilySharingDataView()
        } else {
            FamilySharingOnboardingView(hasEmbarkedOnFamilySharing: $hasEmbarkedOnFamilySharing)
        }
    }
}

// MARK: - Onboarding View (First Time)
struct FamilySharingOnboardingView: View {
    @Environment(\.presentationMode) var presentationMode
    @Binding var hasEmbarkedOnFamilySharing: Bool
    
    @State private var isPrivacyPolicyAccepted = false
    @State private var showPrivacyAlert = false

    var body: some View {
        ZStack {
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            VStack(alignment: .leading, spacing: 24) {
                ScrollView {
                    VStack(spacing: 10) {
                        sharingCard(
                            icon: "person.2",
                            iconColor: .blue,
                            title: "family_sharing_card_1_title".localized,
                            description: "family_sharing_card_1_desc".localized
                        )
                        
                        sharingCard(
                            icon: "link",
                            iconColor: Color(red: 0.2, green: 0.8, blue: 1.0),
                            title: "family_sharing_card_2_title".localized,
                            description: "family_sharing_card_2_desc".localized
                        )
                        
                        sharingCard(
                            icon: "shield.lefthalf.filled",
                            iconColor: Color(red: 0.6, green: 0.4, blue: 1.0),
                            title: "family_sharing_card_3_title".localized,
                            description: "family_sharing_card_3_desc".localized
                        )
                        
                        privacyPolicyToggle
                        
                        startJourneyButton
                    }
                    .padding(.horizontal)
                }
            }
            
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { presentationMode.wrappedValue.dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 17, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
            ToolbarItem(placement: .principal) {
                Text("family_sharing_title".localized)
                    .font(.custom("PingFang-SC-Heavy", size: 19))
                    .foregroundColor(.white)
            }
        }
        .onAppear {
            let appearance = UINavigationBarAppearance()
            appearance.configureWithOpaqueBackground()
            appearance.backgroundColor = UIColor(Color(hex: "#070708"))
            appearance.titleTextAttributes = [.foregroundColor: UIColor.white]
            UINavigationBar.appearance().standardAppearance = appearance
            UINavigationBar.appearance().scrollEdgeAppearance = appearance
            UINavigationBar.appearance().compactAppearance = appearance
        }
        .alert("family_sharing_alert_privacy_title".localized, isPresented: $showPrivacyAlert) {
            Button("cancel".localized, role: .cancel) { }
            Button("agree".localized) {
                isPrivacyPolicyAccepted = true
                hasEmbarkedOnFamilySharing = true
            }
        } message: {
            Text("family_sharing_alert_privacy_message".localized)
        }
        .enableSwipeBack()
    }
    
    private var privacyPolicyToggle: some View {
        Button(action: { isPrivacyPolicyAccepted.toggle() }) {
            HStack(spacing: 12) {
                ZStack {
                    Circle()
                        .stroke(Color(white: 0.5), lineWidth: 1)
                        .frame(width: 15, height: 15)
                    
                    if isPrivacyPolicyAccepted {
                        Circle()
                            .fill(Color(red: 0.12, green: 0.2, blue: 0.4))
                            .frame(width: 9, height: 9)
                    }
                }
                
                Text("family_sharing_agree_privacy".localized)
                    .font(.system(size: 15))
                    .foregroundColor(Color(white: 0.7))
            }
        }
        .padding(.top, 10)
    }
    
    private var startJourneyButton: some View {
        Button(action: {
            if isPrivacyPolicyAccepted {
                hasEmbarkedOnFamilySharing = true
            } else {
                showPrivacyAlert = true
            }
        }) {
            Text("family_sharing_start_journey_button".localized)
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 35)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(red: 0.12, green: 0.2, blue: 0.4))
                        .opacity(isPrivacyPolicyAccepted ? 1 : 0.5)
                )
        }
        .padding(.top, 30)
    }

    private func sharingCard(icon: String, iconColor: Color, title: String, description: String) -> some View {
        HStack(alignment: .top, spacing: 16) {
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(red: 0.1, green: 0.12, blue: 0.18))
                    .frame(width: 48, height: 48)
                
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(iconColor)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text(title)
                    .font(.custom("PingFang-SC-Heavy", size: 13))
                    .foregroundColor(.white)
                    .fixedSize(horizontal: false, vertical: true)
                
                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(Color(white: 0.7))
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(16)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color(red: 0.08, green: 0.1, blue: 0.15))
        .cornerRadius(10)
    }
}

// MARK: - Data View (Subsequent Times)
struct FamilySharingDataView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedTab = 0
    @Namespace var animation
    let tabs = ["family_data_sharing_tab_family".localized, "family_data_sharing_tab_my_data".localized]
    
    @State private var showInviteDialog = false
    @State private var showShareTipDialog = false
    @State private var navigateToShareContent = false
    @State private var showCreateLinkPopup = false
    @State private var createdLink: String = ""
    @State private var selectedUserForEditing: MySharedUserData?
    @State private var navigateToEditShareContent = false
    
    // Data states
    @State private var familyData: [FamilyMemberData] = []
    @State private var myData: [MySharedUserData] = []
    @State private var isLoading = true
    @State private var errorMessage: String?
    
    var body: some View {
        ZStack {
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            VStack(alignment: .leading, spacing: 0) {
                tabBar
                
                TabView(selection: $selectedTab) {
                    familyDataView.tag(0)
                    myDataView.tag(1)
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
            }
            
//            if showCreateLinkPopup {
//                CreateLinkPopupView(isPresented: $showCreateLinkPopup, link: createdLink)
//            }
            
            NavigationLink("", destination: ShareDataContentView(onLinkCreated: { link in
                self.createdLink = link
                self.showCreateLinkPopup = true
            }, onUpdate: {
//                fetchData()
            }, userData: nil), isActive: $navigateToShareContent)

            NavigationLink(
                "",
                destination: ShareDataContentView(
                    onUpdate: {
//                        fetchData()
                    },
                    userData: $selectedUserForEditing
                ),
                isActive: $navigateToEditShareContent
            )
        }
        .toast(isPresented: $showCreateLinkPopup) {
              // 居中的弹窗
            HealthShareTipAlert(message: "create_link_success_message".localized, onConfirm: {
                self.showShareTipDialog = false
                UIPasteboard.general.string = self.createdLink
            })
        }
        .toast(isPresented: $showInviteDialog) {
              // 居中的弹窗
              HealthDataShareAlert(
                customMessage: "family_data_sharing_dialog_title".localized,
                  onConfirm: {
                      print("✅ 接受")
                      Task {
                          do {
                              let link = try await APIService.shared.inviteOthersToShare()
//                              if response.code == 0, let link = response.data {
                              if var components = URLComponents(string: link) {
                                  var queryItems = components.queryItems ?? []
                                  queryItems.append(URLQueryItem(name: "type", value: selectedTab.description))
                                  components.queryItems = queryItems
                                  if let finalURL = components.url?.absoluteString {
                                      self.createdLink = finalURL
                                      self.showShareTipDialog = true
                                      print("✅ invite link: \(finalURL)")
                                  } else {
                                      self.createdLink = link
                                      print("❌ 拼接 URL 失败")
                                  }
                              } else {
                                  self.createdLink = link
                                  print("❌ 无效 URL: \(link)")
                              }
//                                  self.createdLink = linSEk
                                  self.showShareTipDialog = true
                                  print("✅ invite link: \(link)")
                          } catch {
                              errorMessage = error.localizedDescription
                              print("❌ invite error: \(error)")
                          }
                      }
                      self.showInviteDialog = false
                  },
                  onCancel: {
                      print("❌ 拒绝")
                      self.showInviteDialog = false
                  }
              )
        }
        .toast(isPresented: $showShareTipDialog) {
              // 居中的弹窗
            HealthShareTipAlert(message: "create_link_success_message".localized, onConfirm: {
                self.showShareTipDialog = false
                UIPasteboard.general.string = self.createdLink
            })
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { presentationMode.wrappedValue.dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 17, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
            ToolbarItem(placement: .principal) {
                Text("family_sharing_title".localized)
                    .font(.custom("PingFang-SC-Heavy", size: 19))
                    .foregroundColor(.white)
            }
        }
        .onAppear(perform: fetchData)
        .enableSwipeBack()
    }

    // MARK: - Child Views
    
    @ViewBuilder
    private var familyDataView: some View {
        if isLoading {
            ProgressView().tint(.white)
        } else if let errorMessage = errorMessage {
            Text(errorMessage)
                .foregroundColor(.red)
                .padding()
        } else {
            FamilyDataListView(familyMembers: familyData, onInvite: {
                showInviteDialog = true
            })
        }
    }
    
    @ViewBuilder
    private var myDataView: some View {
        if isLoading {
            ProgressView().tint(.white)
        } else if let errorMessage = errorMessage {
            Text(errorMessage)
                .foregroundColor(.red)
                .padding()
        }else {
            MyDataListView(
                sharedUsers: myData,
                onInvite: { navigateToShareContent = true },
                onEdit: { user in
                    selectedUserForEditing = user
                    navigateToEditShareContent = true
                }
            )
        }
//        else if myData.isEmpty {
//            NoDataView(
//                prompt: "family_data_sharing_prompt_let_know".localized,
//                buttonTitle: "family_data_sharing_share_with_others_button".localized,
//                onAction: { navigateToShareContent = true }
//            )
//        }
    }

    private func fetchData() {
        Task {
            do {
                isLoading = true
                errorMessage = nil
                let familyDataTask = try await APIService.shared.getFamilyData()
                let myDataTask = try await APIService.shared.getMyData()
                
                self.familyData = try await familyDataTask
                self.myData = try await myDataTask
                
            } catch {
                self.errorMessage = error.localizedDescription
            }
            isLoading = false
        }
    }
    
    private var tabBar: some View {
        HStack(spacing: 30) {
            ForEach(0..<tabs.count, id: \.self) { index in
                tabBarItem(for: index)
            }
        }
        .padding(.horizontal)
        .padding(.top, 10)
    }
    
    private func tabBarItem(for index: Int) -> some View {
        VStack(spacing: 8) {
            Text(tabs[index])
                .font(.system(size: 16, weight: selectedTab == index ? .semibold : .regular))
                .foregroundColor(selectedTab == index ? .white : .gray)
            
            if selectedTab == index {
                Color.white
                    .frame(height: 2)
                    .matchedGeometryEffect(id: "underline", in: animation)
            } else {
                Color.clear.frame(height: 2)
            }
        }
        .onTapGesture {
            withAnimation(.spring()) {
                selectedTab = index
            }
        }
    }
    
    private struct NoDataView: View {
        let prompt: String
        let buttonTitle: String
        var onAction: () -> Void
        
        var body: some View {
            VStack {
                Spacer()
                Image("icon_family_empty")
                    .font(.system(size: 70))
                    .foregroundColor(Color.gray.opacity(0.3))
                
                Text(prompt)
                    .font(.system(size: 16))
                    .foregroundColor(.gray)
                    .padding(.top, 24)
                    .multilineTextAlignment(.center)
                
                Spacer()
                
                Button(action: onAction) {
                    Text(buttonTitle)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 35)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color(hex: "#0048B5").opacity(0.2))
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                        )
                }
            }
            .padding(.horizontal, 40)
            .padding(.bottom)
        }
    }
}

// MARK: - Family Data List View
private struct FamilyDataListView: View {
    let familyMembers: [FamilyMemberData]
    var onInvite: () -> Void
    
    var body: some View {
        VStack {
            if familyMembers.isEmpty {
                Spacer()
                Image("icon_family_empty")
                    .font(.system(size: 70))
                    .foregroundColor(Color.gray.opacity(0.3))
                
                Text("family_data_sharing_prompt_invite".localized)
                    .font(.system(size: 16))
                    .foregroundColor(.gray)
                    .padding(.top, 24)
                    .multilineTextAlignment(.center)
                
                Spacer()
            } else {
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(familyMembers) { member in
                            FamilyMemberRowView(member: member)
                        }
                    }
                    .padding()
                }
            }
            
            Button(action: onInvite) {
                Text("family_data_sharing_invite_button".localized)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 35)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color(hex: "#0048B5").opacity(0.2))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                    )
            }
            .padding(.horizontal, 40)
            .padding(.bottom)
        }
    }
}

private struct FamilyMemberRowView: View {
    let member: FamilyMemberData
    @State private var isExpanded = false

    var body: some View {
        VStack(spacing: 0) {
            // 用户头部区域（头像 + 昵称 + 展开箭头）
            Button(action: {
                withAnimation(.spring()) {
                    isExpanded.toggle()
                }
            }) {
                ProfileHeaderView(member: member, isExpanded: isExpanded)
            }

            // 展开内容（数据行）
            if isExpanded {
                VStack(spacing: 0) {
//                    NavigationLink(destination: FriendDataDetailView(member: member, selectedTab: 0)) {
                        DataRowView(icon: "moon", title: "sleep_detail_score_title".localized, value: member.sleepTime?.formatMinutesToHoursAndMinutes() ?? "--", subtitle: "history_7day_average".localized)
//                    }
//                    NavigationLink(destination: FriendDataDetailView(member: member, selectedTab: 1)) {
                        DataRowView(icon: "figure.walk", title: "activity_detail_score_title".localized, value: member.activityScore.map { "\($0) \("my_data_unit_score".localized)" } ?? "--", subtitle: "history_7day_average".localized)
//                    }
                    
                    if VersionUpdateService.shared.status == 1 {
//                    NavigationLink(destination: FriendDataDetailView(member: member, selectedTab: 2)) {
                        DataRowView(icon: "brain.head.profile", title: "stress_history_section_title_score".localized, value: member.stressScore.map { "\($0) \("my_data_unit_score".localized)" } ?? "--", subtitle: "history_7day_average".localized)
//                    }
//                    NavigationLink(destination: FriendDataDetailView(member: member, selectedTab: 3)) {
                        DataRowView(icon: "heart.fill", title: "vital_signs".localized, value: member.vitalSings.map { "\($0) \("insight_unit_bpm".localized)" } ?? "--", subtitle: "history_7day_average".localized)
//                    }
                    }

                }
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(hex: "#1A1D25"))
                        .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 4)
                )
                .padding(.top, 8)
            }
        }
        .padding()
        .background(Color.black.opacity(0.05))
        .cornerRadius(16)
        .animation(.easeInOut, value: isExpanded)
    }

    // MARK: - 子视图：头部
    struct ProfileHeaderView: View {
        let member: FamilyMemberData
        let isExpanded: Bool

        var body: some View {
            HStack {
                if let url = URL(string: member.avatar) {
                    AsyncImage(url: url) { image in
                        image.resizable()
                    } placeholder: {
                        ProgressView()
                    }
                    .frame(width: 48, height: 48)
                    .clipShape(Circle())
                } else {
                    Image(systemName: "person.crop.circle.fill")
                        .resizable()
                        .frame(width: 48, height: 48)
                        .foregroundColor(.gray)
                }

                Text(member.nickname)
                    .font(.headline)
                    .foregroundColor(.white)

                Spacer()

                Image(systemName: "chevron.right")
                    .rotationEffect(.degrees(isExpanded ? 90 : 0))
                    .animation(.easeInOut(duration: 0.25), value: isExpanded)
                    .foregroundColor(.white.opacity(0.7))
            }
            .padding()
            .background(
                LinearGradient(colors: [Color(hex: "#1C1F27"), Color(hex: "#2A2F3A")],
                               startPoint: .top, endPoint: .bottom)
            )
            .cornerRadius(12)
        }
    }

    // MARK: - 子视图：单行数据
    struct DataRowView: View {
        var icon: String
        var title: String
        var value: String
        var subtitle: String

        var body: some View {
            HStack(alignment: .top) {
                Image(systemName: icon)
                    .frame(width: 24, height: 24)
                    .foregroundColor(.gray)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .foregroundColor(.white)
                        .font(.system(size: 14, weight: .medium))
                    if !subtitle.isEmpty {
                        Text(subtitle)
                            .foregroundColor(.gray)
                            .font(.system(size: 12))
                    }
                }

                Spacer()

                Text(value)
                    .foregroundColor(.white)
                    .font(.system(size: 14, weight: .bold))
            }
            .padding(.horizontal)
            .padding(.vertical, 10)
            .background(Color(hex: "#1F222B"))
        }
    }
}

// MARK: - My Data List View
private struct MyDataListView: View {
    let sharedUsers: [MySharedUserData]
    var onInvite: () -> Void
    var onEdit: (MySharedUserData) -> Void
    
    var body: some View {
        VStack {
            if sharedUsers.isEmpty {
                Spacer()
                Image("icon_family_empty")
                    .font(.system(size: 70))
                    .foregroundColor(Color.gray.opacity(0.3))
                
                Text("family_data_sharing_prompt_let_know".localized)
                    .font(.system(size: 16))
                    .foregroundColor(.gray)
                    .padding(.top, 24)
                    .multilineTextAlignment(.center)
                
                Spacer()
            } else {
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(sharedUsers) { member in
                            MyDataRowView(user: member)
                                .onTapGesture {
                                    onEdit(member)
                                }
                        }
                    }
                    .padding()
                }
            }
            
            Button(action: onInvite) {
                Text("family_data_sharing_share_with_others_button".localized)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 35)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color(hex: "#0048B5").opacity(0.2))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color(hex: "#0048B5"), lineWidth: 0.5)
                    )
            }
            .padding(.horizontal, 40)
            .padding(.bottom)
        }
    }
}

private struct MyDataRowView: View {
    let user: MySharedUserData
    
    var body: some View {
        VStack(spacing: 0) {
            // 用户头部区域（头像 + 昵称 + 展开箭头）
//            Button(action: {
//                
//            }) {
//                
//            }
            ProfileHeaderView(member: user)
        }
        .padding()
        .background(Color.black.opacity(0.05))
        .cornerRadius(16)
    }
    // MARK: - 子视图：头部
    struct ProfileHeaderView: View {
        let member: MySharedUserData

        var body: some View {
            HStack {
                if let url = URL(string: member.avatar) {
                    AsyncImage(url: url) { image in
                        image.resizable()
                    } placeholder: {
                        ProgressView()
                    }
                    .frame(width: 48, height: 48)
                    .clipShape(Circle())
                } else {
                    Image(systemName: "person.crop.circle.fill")
                        .resizable()
                        .frame(width: 48, height: 48)
                        .foregroundColor(.gray)
                }

                Text(member.nickname)
                    .font(.headline)
                    .foregroundColor(.white)

                Spacer()

                Image(systemName: "chevron.right")
                    .rotationEffect(.degrees( 0))
                    .foregroundColor(.white.opacity(0.7))
            }
            .padding()
            .background(
                LinearGradient(colors: [Color(hex: "#1C1F27"), Color(hex: "#2A2F3A")],
                               startPoint: .top, endPoint: .bottom)
            )
            .cornerRadius(12)
        }
    }
}

// MARK: - Data Models
struct FamilySharingUser: Codable,Identifiable {
    var id = UUID()
    let name: String
    let avatar: String
    var isExpanded: Bool = false
    let healthMetrics: [FamilySharingHealthMetric]
}

struct FamilySharingHealthMetric: Codable,Identifiable {
    var id = UUID()
    let icon: String
    let name: String
    let value: String
    let subValue: String?
    let description: String?
}

// MARK: - User Health Row View
private struct UserHealthRowView: View {
    @Binding var user: FamilySharingUser
//    var users: [FamilySharingUser]
    var body: some View {
        VStack(spacing: 0) {
            // Collapsed Row
            HStack {
                // You should replace "person.circle.fill" with an actual image loader for user.avatar
                Image(systemName: "person.circle.fill")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
                
                Text(user.name)
                    .foregroundColor(.white)
                    .font(.system(size: 16))
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.gray)
                    .rotationEffect(.degrees(user.isExpanded ? 90 : 0))
            }
            .padding()
            .background(Color.white.opacity(0.1))
            .cornerRadius(10)
            .onTapGesture {
                withAnimation(.spring()) {
                    user.isExpanded.toggle()
                }
            }
            
            // Expanded Content
            if user.isExpanded {
                VStack(spacing: 1) {
                    ForEach(user.healthMetrics) { metric in
                        HealthMetricRowView(metric: metric)
                    }
                }
                .padding(.horizontal, 10)
                .padding(.bottom, 10)
            }
        }
        .padding(.horizontal)
    }
}

// MARK: - Health Metric Row View
private struct HealthMetricRowView: View {
    let metric: FamilySharingHealthMetric
    
    var body: some View {
        HStack {
            Image(systemName: metric.icon)
                .font(.system(size: 20))
                .foregroundColor(.gray)
                .frame(width: 30)

            Text(metric.name)
                .foregroundColor(.white)
                .font(.system(size: 14))

            Spacer()

            VStack(alignment: .trailing, spacing: 2) {
                HStack(alignment: .lastTextBaseline, spacing: 4) {
                    Text(metric.value)
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                    if let subValue = metric.subValue {
                        Text(subValue)
                            .font(.system(size: 12))
                            .foregroundColor(.white)
                    }
                }
                if let description = metric.description {
                    Text(description)
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal)
        .background(Color.black.opacity(0.1))
    }
}

// MARK: - Previews
struct FamilySharingView_Previews: PreviewProvider {
    static var previews: some View {
        // Preview the initial onboarding state
        NavigationView {
             FamilySharingView()
        }
       
        
        // You can also preview the data view state like this:
        // FamilySharingView(hasEmbarkedOnFamilySharing: true)
    }
}

// MARK: - Swipe Back Gesture Helper
class PopGestureDelegate: NSObject, UIGestureRecognizerDelegate {
    weak var navigationController: UINavigationController?
    
    init(navigationController: UINavigationController?) {
        self.navigationController = navigationController
        super.init()
    }
    
    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        guard let navigationController = navigationController else { return false }
        return navigationController.viewControllers.count > 1
    }
}

// A view that installs the gesture recognizer delegate.
struct PopGestureController: UIViewControllerRepresentable {
    @State private var delegate: PopGestureDelegate?
    
    func makeUIViewController(context: Context) -> UIViewController {
        let controller = UIViewController()
        DispatchQueue.main.async {
            if let navigationController = controller.navigationController {
                let newDelegate = PopGestureDelegate(navigationController: navigationController)
                // The key is to hold a strong reference to the delegate.
                delegate = newDelegate
                navigationController.interactivePopGestureRecognizer?.delegate = newDelegate
                navigationController.interactivePopGestureRecognizer?.isEnabled = true
            }
        }
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {}
}

extension View {
    func enableSwipeBack() -> some View {
        self.background(PopGestureController())
    }
}


