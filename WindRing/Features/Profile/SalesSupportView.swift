//
//  SalesSupportView.swift
//  WindRing
//
//  Created by zx on 2025/6/18.
//

import SwiftUI
import PhotosUI
import Combine
import UIKit

// MARK: - Feedback Models
struct FeedbackTypeOption: Hashable, Identifiable {
    let id: String
    var name: String
}

struct FeedbackChannelOption: Hashable, Identifiable {
    let id: String
    var name: String
}

struct SalesSupportView: View {
    // 状态变量
    @State private var selectedFeedbackTypes = Set<String>()
    @State private var selectedChannelId: String?
    @State private var feedbackContent: String = ""
    @State private var abnormalStartTime: Date?
    @State private var abnormalEndTime: Date?
    @State private var contactName: String = ""
    @State private var contactEmail: String = ""
    @State private var selectedImages: [UIImage] = []
    
    @State private var showChannelOptions: Bool = false
    @State private var showImagePicker: Bool = false
    @State private var showRecordView: Bool = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var showDatePicker = false
    @State private var datePickerTarget: DatePickerTarget?
    @State private var temporaryDate: Date = Date()
    
    enum DatePickerTarget {
        case start, end
    }
    
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var salesSupportVM = SalesSupportViewModel()
    
    // 反馈类型选项
    private let feedbackTypes: [FeedbackTypeOption] = [
        .init(id: "1", name: "Ring"),
        .init(id: "2", name: "App"),
        .init(id: "3", name: "Other")
    ]
    
    // 购买渠道选项
    private let channelOptions: [FeedbackChannelOption] = [
        .init(id: "1", name: NSLocalizedString("sales_support_channel_official", comment: "")),
        .init(id: "2", name: NSLocalizedString("sales_support_channel_amazon", comment: "")),
        .init(id: "3", name: NSLocalizedString("sales_support_channel_kickstarter", comment: "")),
        .init(id: "4", name: NSLocalizedString("sales_support_channel_offline", comment: "")),
        .init(id: "5", name: NSLocalizedString("sales_support_channel_indiegogo", comment: "")),
        .init(id: "6", name: NSLocalizedString("sales_support_channel_other", comment: ""))
    ]
    
    private var selectedChannelName: String {
        channelOptions.first(where: { $0.id == selectedChannelId })?.name ?? NSLocalizedString("sales_support_select_channel_placeholder", comment: "")
    }
    
    private var selectedFeedbackTypeNames: String {
        if selectedFeedbackTypes.isEmpty {
            return NSLocalizedString("sales_support_feedback_type_placeholder", comment: "Please select feedback type")
        }
        return selectedFeedbackTypes.compactMap { id in
            feedbackTypes.first { $0.id == id }?.name
        }.joined(separator: ", ")
    }
    
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色 - 使用深色背景
            Color(hex: "#070708").ignoresSafeArea()
            
            // 使用封装的光晕背景组件
            CornerGlowBackground()
            
            VStack(spacing: 0) {
                navigationBar
                
                ScrollView {
                    VStack(alignment: .leading, spacing: 16) {
                        feedbackTypeSection
                        channelSection
                        feedbackContentSection
                        imageUploadSection
                        abnormalTimeSection
                        contactNameSection
                        contactEmailSection
                        submitButton
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 16)
                    .padding(.bottom, 20)
                }
            }
            .background(
                NavigationLink(destination: FeedbackHistoryView(), isActive: $showRecordView) {
                    EmptyView()
                }
            )
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImages: $selectedImages, maxSelections: 6)
        }
        .customPopup(isPresented: $showDatePicker) {
            datePickerSheetView
        }
//        .alert(isPresented: $showAlert) {
//            Alert(title: Text(NSLocalizedString("sales_support_alert_title", comment: "")), message: Text(alertMessage), dismissButton: .default(Text(NSLocalizedString("ok", comment: ""))))
//        }
    }
    
    // MARK: - 子视图
    
    private var navigationBar: some View {
        HStack(alignment: .center) {
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(.white)
            }
            
            Text(NSLocalizedString("sales_support_title", comment: ""))
                .font(.custom("PingFang-SC-Heavy", size: 19))
                .foregroundColor(.white)
                .padding(.leading, 5)
            
            Spacer()
            
            Button(action: {
                showRecordView = true
            }) {
                Image(systemName: NSLocalizedString("sales_support_record_button_icon", comment: ""))
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(Color(hex: "#FA6C2D"))
            }
        }
        .padding(.horizontal, 16)
        .frame(height: 44)
        .padding(.top, 8)
    }
    
    private var feedbackTypeSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("sales_support_feedback_type_prompt", comment: ""))
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white.opacity(0.6))
            
            HStack(spacing: 12) {
                ForEach(feedbackTypes) { type in
                    feedbackTypeButton(type)
                }
                Spacer()
            }
        }
    }
    
    private var channelSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("sales_support_channel_prompt", comment: ""))
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white.opacity(0.6))
            
            channelDropdownField
            
            Text(NSLocalizedString("sales_support_channel_tip", comment: ""))
                .font(.custom("PingFang-SC-Medium", size: 12))
                .foregroundColor(.white.opacity(0.4))
        }
    }
    
    private var feedbackContentSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("sales_support_content_prompt", comment: ""))
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white.opacity(0.6))
            
            feedbackTextArea
        }
    }
    
    private var imageUploadSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("sales_support_image_upload_prompt", comment: ""))
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white.opacity(0.6))
            
            HStack {
                Button(action: {
                    showImagePicker = true
                }) {
                    Image(systemName: "camera")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                        .frame(width: 80, height: 80)
                        .background(Color.white.opacity(0.1))
                        .cornerRadius(10)
                }
                
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 10) {
                        ForEach(0..<selectedImages.count, id: \.self) { index in
                            Image(uiImage: selectedImages[index])
                                .resizable()
                                .scaledToFill()
                                .frame(width: 80, height: 80)
                                .clipShape(RoundedRectangle(cornerRadius: 10))
                                .overlay(
                                    Button(action: {
                                        selectedImages.remove(at: index)
                                    }) {
                                        Image(systemName: "xmark.circle.fill")
                                            .foregroundColor(.red)
                                            .background(Color.white)
                                            .clipShape(Circle())
                                    }
                                    .padding(4),
                                    alignment: .topTrailing
                                )
                        }
                    }
                }
            }
        }
    }
    
    private var abnormalTimeSection: some View {
        HStack(alignment: .center, spacing: 15) {
            VStack(alignment: .leading, spacing: 8) {
                Text(NSLocalizedString("sales_support_abnormal_start_time_prompt", comment: ""))
                    .font(.custom("PingFang-SC-Medium", size: 13))
                    .foregroundColor(.white.opacity(0.6))
                Button(action: {
                    self.datePickerTarget = .start
                    self.temporaryDate = self.abnormalStartTime ?? Date()
                    self.showDatePicker = true
                }) {
                    dateDisplayField(placeholder: NSLocalizedString("sales_support_start_time_placeholder", comment: ""), date: abnormalStartTime)
                }
            }
            
            Text("-")
                .foregroundColor(.white.opacity(0.6))
                .padding(.top, 20)

            VStack(alignment: .leading, spacing: 8) {
                Text(NSLocalizedString("sales_support_abnormal_end_time_prompt", comment: ""))
                    .font(.custom("PingFang-SC-Medium", size: 13))
                    .foregroundColor(.white.opacity(0.6))
                Button(action: {
                    self.datePickerTarget = .end
                    self.temporaryDate = self.abnormalEndTime ?? Date()
                    self.showDatePicker = true
                }) {
                    dateDisplayField(placeholder: NSLocalizedString("sales_support_end_time_placeholder", comment: ""), date: abnormalEndTime)
                }
            }
        }
    }
    
    private var contactNameSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("sales_support_contact_name_prompt", comment: ""))
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white.opacity(0.6))
            timeInputField(placeholder: NSLocalizedString("sales_support_contact_name_placeholder", comment: ""), text: $contactName)
        }
    }
    
    private var contactEmailSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("sales_support_contact_email_prompt", comment: ""))
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white.opacity(0.6))
            timeInputField(placeholder: NSLocalizedString("sales_support_contact_email_placeholder", comment: ""), text: $contactEmail)
        }
    }
    
    private var submitButton: some View {
        Button(action: submitFeedback) {
            Text(NSLocalizedString("sales_support_submit_button", comment: ""))
                .font(.custom("PingFang-SC-Medium", size: 16))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 15)
                .background(Color(hex: "0048B5"))
                .cornerRadius(10)
        }
        .padding(.top, 8)
    }
    
    // MARK: - 组件
    
    private var channelDropdownField: some View {
        Menu {
            ForEach(channelOptions) { option in
                Button(action: {
                    selectedChannelId = option.id
                }) {
                    Text(option.name)
                }
            }
        } label: {
            HStack {
                Text(selectedChannelName)
                    .font(.custom("PingFang-SC-Medium", size: 13))
                    .foregroundColor(selectedChannelId == nil ? .white.opacity(0.3) : .white)
                Spacer()
                Image(systemName: "chevron.down")
                    .foregroundColor(.white.opacity(0.6))
                    .font(.footnote)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 10)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white.opacity(0.1))
            .cornerRadius(5)
        }
    }
    
    private func feedbackTypeButton(_ type: FeedbackTypeOption) -> some View {
        let isSelected = selectedFeedbackTypes.contains(type.id)
        return Button(action: {
            if isSelected {
                selectedFeedbackTypes.remove(type.id)
            } else {
                selectedFeedbackTypes.insert(type.id)
            }
        }) {
            Text(type.name)
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(isSelected ? .white : .white.opacity(0.6))
                .padding(.horizontal, 20)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 5)
                        .fill(isSelected ? Color(hex: "#0048B5") : Color.clear)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 5)
                        .stroke(isSelected ? Color.clear : Color.white.opacity(0.3), lineWidth: 1)
                )
        }
    }
    
    private var feedbackTextArea: some View {
        ZStack(alignment: .topLeading) {
            if feedbackContent.isEmpty {
                Text(NSLocalizedString("sales_support_feedback_content_placeholder", comment: ""))
                    .font(.custom("PingFang-SC-Medium", size: 13))
                    .foregroundColor(.white.opacity(0.3))
                    .padding(.top, 8)
                    .padding(.leading, 5)
            }
            
            TextEditor(text: $feedbackContent)
                .font(.custom("PingFang-SC-Medium", size: 13))
                .foregroundColor(.white)
                .frame(minHeight: 120)
                .opacity(feedbackContent.isEmpty ? 0.25 : 1)
                .colorScheme(.dark)
                .background(Color.clear)
        }
        .padding(8)
        .background(Color.white.opacity(0.1))
        .cornerRadius(10)
    }
    
    private func timeInputField(placeholder: String, text: Binding<String>) -> some View {
        TextField(placeholder, text: text)
            .font(.custom("PingFang-SC-Medium", size: 13))
            .foregroundColor(.white)
            .padding(.vertical, 12)
            .padding(.horizontal, 10)
            .background(Color.white.opacity(0.1))
            .cornerRadius(5)
    }
    
    // MARK: - 功能
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: email)
    }
    
    private func formattedDate(_ date: Date?) -> String {
        guard let date = date else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return formatter.string(from: date)
    }

    private func dateDisplayField(placeholder: String, date: Date?) -> some View {
        HStack {
            if let date = date, !formattedDate(date).isEmpty {
                Text(formattedDate(date))
                    .foregroundColor(.white)
            } else {
                Text(placeholder)
                    .foregroundColor(.white.opacity(0.3))
            }
            Spacer()
        }
        .font(.custom("PingFang-SC-Medium", size: 13))
        .padding(.vertical, 12)
        .padding(.horizontal, 10)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color.white.opacity(0.1))
        .cornerRadius(5)
    }

    private var datePickerSheetView: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Button(action: { showDatePicker = false }) {
                    Text("cancel".localized)
                        .foregroundColor(.white.opacity(0.7))
                }
                Spacer()
                Text(datePickerTarget == .start ? "sales_support_abnormal_start_time_prompt".localized : "sales_support_abnormal_end_time_prompt".localized)
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(.white)
                Spacer()
                Button(action: {
                    if datePickerTarget == .start {
                        self.abnormalStartTime = temporaryDate
                    } else {
                        self.abnormalEndTime = temporaryDate
                    }
                    showDatePicker = false
                }) {
                    Text("confirm".localized)
                        .foregroundColor(Color(hex: "#FA6C2D"))
                }
            }
            .padding()

            DatePicker(
                "",
                selection: $temporaryDate,
                displayedComponents: [.date, .hourAndMinute]
            )
            .datePickerStyle(.wheel)
            .labelsHidden()
            .colorScheme(.dark)
        }
        .padding(.vertical)
        .frame(maxWidth: 400)
        .background(Color(hex: "1C1C1E"))
        .cornerRadius(20)
    }
    
    private func submitFeedback() {
        if selectedFeedbackTypes.isEmpty {
            "sales_support_alert_type_missing".showToast()
            return
        }
        
        guard let channelId = selectedChannelId else {
            "sales_support_alert_channel_missing".showToast()
//            alertMessage = NSLocalizedString("sales_support_alert_channel_missing", comment: "")
//            showAlert = true
            return
        }
        
        if feedbackContent.isEmpty {
            "sales_support_alert_content_missing".showToast()
//            alertMessage = NSLocalizedString("sales_support_alert_content_missing", comment: "")
//            showAlert = true
            return
        }
        
        if contactEmail.isEmpty {
            "sales_support_alert_email_missing".showToast()
            return
        }
        
        if !isValidEmail(contactEmail) {
            "login_error_invalid_email".showToast()
            return
        }
        
        let submitAction = { (imageUrls: [String]) in
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
            
            let startTimeString = self.abnormalStartTime.map { formatter.string(from: $0) } ?? ""
            let endTimeString = self.abnormalEndTime.map { formatter.string(from: $0) } ?? ""
            
            let urlsData = try? JSONSerialization.data(withJSONObject: imageUrls, options: [])
            let urlsString = urlsData.flatMap { String(data: $0, encoding: .utf8) } ?? "[]"
            
            var parameters: [String: Any] = [
                "feedbackType": self.selectedFeedbackTypes.joined(separator: ","),
                "feedbackChannel": channelId,
                "feedbackContent": self.feedbackContent,
                "startTime": startTimeString,
                "endTime": endTimeString,
                "attachUrls": urlsString
            ]
            
            if !self.contactName.isEmpty {
                parameters["contactName"] = self.contactName
            }
            if !self.contactEmail.isEmpty {
                parameters["contactEmail"] = self.contactEmail
            }
            
            self.salesSupportVM.submitFeedback(parameters: parameters) { success in
                if success {
                    "sales_support_submit_success".showToast()
//                    self.alertMessage = NSLocalizedString("sales_support_submit_success", comment: "")
//                    self.showAlert = true
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                        self.presentationMode.wrappedValue.dismiss()
                    }
                } else {
                    "sales_support_submit_failure".showToast()
//                    self.alertMessage = NSLocalizedString("sales_support_submit_failure", comment: "")
                    self.showAlert = true
                }
            }
        }
        
        if selectedImages.isEmpty {
            submitAction([])
            return
        }
        
        let group = DispatchGroup()
        var imageUrls: [String] = []
        var uploadError: Error?
        
        for image in selectedImages {
            group.enter()
            AuthService.shared.uploadAvatar(image: image) { result in
                defer { group.leave() }
                switch result {
                case .success(let urls):
                    if let url = urls.first {
                        imageUrls.append(url)
                    }
                case .failure(let error):
                    uploadError = error
                }
            }
        }
        
        group.notify(queue: .main) {
            if let error = uploadError {
                error.localizedDescription.showToast()
//                self.alertMessage = "Image upload failed: \(error.localizedDescription)"
//                self.showAlert = true
                return
            }
            submitAction(imageUrls)
        }
    }
    
    // MARK: - 上传图片
    private func uploadImage(_ image: UIKit.UIImage) {
        // 1. 首先显示上传中状态
//        isLoading = true
        
        // 2. 使用AuthService上传头像
        AuthService.shared.uploadAvatar(image: image) { result in
            DispatchQueue.main.async {
//                self.isLoading = false
                
                switch result {
                case .success(let urls):
                    print("图片上传成功")
                    if let imageUrl = urls.first {
                        
                        
                    } else {
//                        self.isLoading = false
                    }
                    
                case .failure(let error):
//                    self.isLoading = false
                    print("图片上传失败: \(error.localizedDescription)")
                    self.alertMessage = "图片上传失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }
    }
}

// MARK: - ViewModel
class SalesSupportViewModel: ObservableObject {
    private var userAPIService = UserAPIService(networkManager: NetworkManager.shared)
    private var cancellables = Set<AnyCancellable>()
    
    func submitFeedback(parameters: [String: Any], completion: @escaping (Bool) -> Void) {
        userAPIService.createFeedback(parameters: parameters)
            .sink(receiveCompletion: { completionResult in
                switch completionResult {
                case .failure(let error):
                    print("Feedback submission failed: \(error.localizedDescription)")
                    completion(false)
                case .finished:
                    break
                }
            }, receiveValue: { response in
                if response.code == 0 {
                    print("Feedback submitted successfully")
                    completion(true)
                } else {
                    print("Feedback submission failed with code \(response.code): \(response.msg ?? "No message")")
                    completion(false)
                }
            })
            .store(in: &cancellables)
    }
}

// MARK: - ImagePicker
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImages: [UIImage]
    var maxSelections: Int
    @Environment(\.presentationMode) private var presentationMode
    
    func makeUIViewController(context: Context) -> PHPickerViewController {
        var configuration = PHPickerConfiguration()
        configuration.filter = .images
        configuration.selectionLimit = maxSelections - selectedImages.count
        
        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            parent.presentationMode.wrappedValue.dismiss()
            
            guard !results.isEmpty else { return }
            
            for result in results {
                if parent.selectedImages.count >= parent.maxSelections {
                    break
                }
                
                result.itemProvider.loadObject(ofClass: UIImage.self) { (object, error) in
                    if let image = object as? UIImage {
                        DispatchQueue.main.async {
                            self.parent.selectedImages.append(image)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Previews
struct SalesSupportView_Previews: PreviewProvider {
    static var previews: some View {
        SalesSupportView()
    }
}



extension View {
    func customPopup<Content: View>(isPresented: Binding<Bool>, @ViewBuilder content: @escaping () -> Content) -> some View {
        self.overlay(
            ZStack {
                if isPresented.wrappedValue {
                    Color.black.opacity(0.4).edgesIgnoringSafeArea(.all)
                        .onTapGesture {
                            withAnimation {
                                isPresented.wrappedValue = false
                            }
                        }
                    
                    content()
                        .padding(40)
                        .transition(.scale.combined(with: .opacity))
                }
            }
            .animation(.easeInOut, value: isPresented.wrappedValue)
        )
    }
}



