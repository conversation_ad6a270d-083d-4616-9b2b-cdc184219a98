import SwiftUI
import Combine



struct FeedbackHistoryItem: Identifiable {
    let id: String
    let types: String
    let content: String
    let date: String
    let status: String
    let statusColor: Color
    
    // For detail view
    let purchaseChannel: String
    let contactEmail: String
    let replyContent: String?
    let imageUrls: [URL]
}

class FeedbackHistoryViewModel: ObservableObject {
    @Published var feedbackItems: [FeedbackHistoryItem] = []
    @Published var isLoading = false
    
    private var userAPIService = UserAPIService(networkManager: NetworkManager.shared)
    private var cancellables = Set<AnyCancellable>()
    
    func fetchHistory() {
        isLoading = true
        userAPIService.getFeedbackHistory(page: 1, pageSize: 20)
            .sink { completion in
                self.isLoading = false
                switch completion {
                case .failure(let error):
                    print("Failed to fetch feedback history: \(error.localizedDescription)")
                case .finished:
                    break
                }
            } receiveValue: { historyPage in
                self.feedbackItems = historyPage.list.map { self.mapDTOToItem($0) }
            }
            .store(in: &cancellables)
    }
    
    private func mapDTOToItem(_ dto: FeedbackHistoryItemDTO) -> FeedbackHistoryItem {
        let statusInfo = mapStatus(dto.status)
        let imageUrls = parseImageUrls(from: dto.attachUrls)
        
        return FeedbackHistoryItem(
            id: String(dto.id),
            types: mapFeedbackType(dto.feedbackType),
            content: dto.feedbackContent,
            date: dto.createTime,
            status: statusInfo.text,
            statusColor: statusInfo.color,
            purchaseChannel: mapPurchaseChannel(dto.feedbackChannel),
            contactEmail: dto.contactEmail,
            replyContent: dto.replyContent,
            imageUrls: imageUrls
        )
    }
    
    private func mapFeedbackType(_ typeString: String) -> String {
        let typeMap = ["1": "Ring", "2": "App", "3": "Other"]
        return typeString.split(separator: ",")
            .compactMap { typeMap[String($0)] }
            .joined(separator: ", ")
    }
    
    private func parseImageUrls(from jsonString: String) -> [URL] {
        guard let data = jsonString.data(using: .utf8),
              let urls = try? JSONDecoder().decode([String].self, from: data) else {
            return []
        }
        return urls.compactMap { URL(string: $0) }
    }
    
    private func mapPurchaseChannel(_ channelId: String) -> String {
        switch channelId {
        case "1": return NSLocalizedString("sales_support_channel_official", comment: "")
        case "2": return NSLocalizedString("sales_support_channel_amazon", comment: "")
        case "3": return NSLocalizedString("sales_support_channel_kickstarter", comment: "")
        case "4": return NSLocalizedString("sales_support_channel_offline", comment: "")
        case "5": return NSLocalizedString("sales_support_channel_indiegogo", comment: "")
        case "6": return NSLocalizedString("sales_support_channel_other", comment: "")
        default: return "Unknown"
        }
    }
    
    private func mapStatus(_ status: Int) -> (text: String, color: Color) {
        switch status {
        case 0:
            return (NSLocalizedString("feedback_status_pending", comment: "Pending"), Color.orange)
        case 1:
            return (NSLocalizedString("feedback_status_replied", comment: "Replied"), Color(hex: "#4CAF50"))
        default:
            return ("Unknown", Color.gray)
        }
    }
}

struct FeedbackHistoryView: View {
    @StateObject private var viewModel = FeedbackHistoryViewModel()
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ZStack {
            // 背景色 - 使用深色背景
            Color(hex: "#070708").ignoresSafeArea()
            
            // 使用封装的光晕背景组件
            CornerGlowBackground()
            
            VStack(spacing: 0) {
                navigationBar
                
                if viewModel.isLoading {
                    Spacer()
                    ProgressView().colorScheme(.dark)
                    Spacer()
                } else if viewModel.feedbackItems.isEmpty {
                    emptyView
                } else {
                    feedbackList
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            viewModel.fetchHistory()
        }
    }
    
    private var navigationBar: some View {
        HStack {
            Button(action: { presentationMode.wrappedValue.dismiss() }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(.white)
            }
            Spacer()
            Text(NSLocalizedString("feedback_history_title", comment: "Feedback history"))
                .font(.custom("PingFang-SC-Heavy", size: 19))
                .foregroundColor(.white)
            Spacer()
            // A placeholder to keep title centered
            Image(systemName: "chevron.left").opacity(0)
        }
        .padding(.horizontal)
        .frame(height: 44)
    }
    
    private var emptyView: some View {
        VStack {
            Spacer()
            Image("疑问")
                .resizable()
                .renderingMode(.template)
                .foregroundColor(.white.opacity(0.3))
                .scaledToFit()
                .frame(width: 80, height: 80)
                .padding()
                .background(
                    Circle()
                        .fill(Color.white.opacity(0.05))
                        .overlay(Circle().stroke(Color.white.opacity(0.1), lineWidth: 1))
                )
            Text(NSLocalizedString("feedback_history_empty", comment: "No historical feedback"))
                .foregroundColor(.white.opacity(0.6))
                .font(.custom("PingFang-SC-Medium", size: 16))
                .padding(.top)
            Spacer()
        }
    }
    
    private var feedbackList: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(viewModel.feedbackItems) { item in
                    NavigationLink(destination: FeedbackDetailView(item: item)) {
                        FeedbackRowView(item: item)
                    }
                }
            }
            .padding()
        }
    }
}

struct FeedbackRowView: View {
    let item: FeedbackHistoryItem
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(item.types)
                    .font(.custom("PingFang-SC-Medium", size: 16))
                    .foregroundColor(.white)
                Spacer()
                Text(item.status)
                    .font(.custom("PingFang-SC-Medium", size: 12))
                    .foregroundColor(.white)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 4)
                    .background(item.statusColor)
                    .cornerRadius(12)
            }
            
            Text(item.content)
                .font(.custom("PingFang-SC-Regular", size: 14))
                .foregroundColor(.white.opacity(0.8))
                .lineLimit(2)
            
            HStack(spacing: 6) {
                Image(systemName: "clock")
                    .font(.system(size: 12))
                Text(Date(timeIntervalSince1970: (Double(item.date) ?? 0) / 1000.0).string(withFormat: "yyyy-MM-dd HH:mm:ss"))
                    .font(.system(size: 12))
            }
            .foregroundColor(.white.opacity(0.6))
        }
        .padding()
        .background(Color(white: 1, opacity: 0.05))
        .cornerRadius(16)
    }
}

struct FeedbackHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        FeedbackHistoryView()
    }
} 
