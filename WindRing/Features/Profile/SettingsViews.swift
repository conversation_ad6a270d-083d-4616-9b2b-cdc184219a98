import SwiftUI
import StoreKit

/// 通知设置视图
struct NotificationSettingsView: View {
    // MARK: - 状态属性
    @State private var enableNotifications = true
    @State private var enableSoundAlerts = true
    @State private var enableVibration = true
    @State private var healthAlerts = true
    @State private var sleepAlerts = true
    @State private var activityAlerts = true
    @State private var systemAlerts = true
    
    // MARK: - 主视图
    var body: some View {
        Form {
            Section(header: Text("notification_settings_title".localized)) {
                Toggle("enable_notifications".localized, isOn: $enableNotifications)
                
                if enableNotifications {
                    Toggle("sound_alerts".localized, isOn: $enableSoundAlerts)
                    Toggle("vibration".localized, isOn: $enableVibration)
                }
            }
            
            Section(header: Text("notification_types".localized)) {
                Toggle("health_alerts".localized, isOn: $healthAlerts)
                Toggle("sleep_alerts".localized, isOn: $sleepAlerts)
                Toggle("activity_alerts".localized, isOn: $activityAlerts)
                Toggle("system_notifications".localized, isOn: $systemAlerts)
            }
        }
        .navigationTitle("notification_settings_title".localized)
        .navigationBarTitleDisplayMode(.inline)
        .listStyle(.plain)
    }
}

/// 隐私设置视图
struct PrivacySettingsView: View {
    // MARK: - 状态属性
    @State private var locationTracking = true
    @State private var healthDataCollection = true
    @State private var analytics = true
    @State private var showPassword = false
    @State private var currentPassword = ""
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 数据隐私设置
                VStack(alignment: .leading, spacing: 15) {
                    Text("data_privacy".localized)
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    VStack(spacing: 0) {
                        Toggle("location_tracking".localized, isOn: $locationTracking)
                            .padding()
                            .background(Color.white.opacity(0.05))
                            .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
                        
                        Divider().background(Color.white.opacity(0.1))
                        
                        Toggle("health_data_collection".localized, isOn: $healthDataCollection)
                            .padding()
                            .background(Color.white.opacity(0.05))
                            .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
                        
                        Divider().background(Color.white.opacity(0.1))
                        
                        Toggle("usage_analytics".localized, isOn: $analytics)
                            .padding()
                            .background(Color.white.opacity(0.05))
                            .toggleStyle(SwitchToggleStyle(tint: Color(hex: "#FA6C2D")))
                    }
                    .background(Color.white.opacity(0.1))
                    .cornerRadius(10)
                }
                .padding(.horizontal)
                
                // 密码安全
                VStack(alignment: .leading, spacing: 15) {
                    Text("account_security".localized)
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    VStack(spacing: 15) {
                        // 当前密码
                        HStack {
                            if showPassword {
                                TextField("current_password".localized, text: $currentPassword)
                                    .foregroundColor(.white)
                            } else {
                                SecureField("current_password".localized, text: $currentPassword)
                                    .foregroundColor(.white)
                            }
                            
                            Button(action: {
                                showPassword.toggle()
                            }) {
                                Image(systemName: showPassword ? "eye.slash" : "eye")
                                    .foregroundColor(.white.opacity(0.7))
                            }
                        }
                        .padding()
                        .background(Color.white.opacity(0.1))
                        .cornerRadius(10)
                        
                        // 新密码
                        SecureField("new_password".localized, text: $newPassword)
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.white.opacity(0.1))
                            .cornerRadius(10)
                        
                        // 确认密码
                        SecureField("confirm_new_password".localized, text: $confirmPassword)
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.white.opacity(0.1))
                            .cornerRadius(10)
                        
                        // 更新按钮
                        Button(action: {
                            // 更新密码
                        }) {
                            Text("update_password".localized)
                                .fontWeight(.semibold)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(Color(hex: "#FA6C2D"))
                                .foregroundColor(.white)
                                .cornerRadius(10)
                        }
                    }
                    .padding()
                    .background(Color.white.opacity(0.1))
                    .cornerRadius(10)
                }
                .padding(.horizontal)
                
                // 数据删除
                VStack(alignment: .leading, spacing: 15) {
                    Text("data_management".localized)
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Button(action: {
                        // 清除缓存
                    }) {
                        HStack {
                            Text("clear_cache".localized)
                                .foregroundColor(.white)
                            
                            Spacer()
                            
                            Image(systemName: "trash")
                                .foregroundColor(.white.opacity(0.7))
                        }
                        .padding()
                        .background(Color.white.opacity(0.1))
                        .cornerRadius(10)
                    }
                    
                    Button(action: {
                        // 删除所有数据
                    }) {
                        HStack {
                            Text("delete_all_data".localized)
                                .foregroundColor(.red)
                            
                            Spacer()
                            
                            Image(systemName: "exclamationmark.triangle")
                                .foregroundColor(.red)
                        }
                        .padding()
                        .background(Color.white.opacity(0.1))
                        .cornerRadius(10)
                    }
                }
                .padding(.horizontal)
            }
            .padding(.vertical)
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#00204A"),
                    Color(hex: "#002D67")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
        )
        .navigationTitle("privacy_settings_title".localized)
        .navigationBarTitleDisplayMode(.inline)
    }
}

/// 自定义弹框视图
struct CustomAlertView: View {
    var title: String
    var message: String
    var icon: String
    var iconColor: Color = .red
    var onCancel: () -> Void
    var onConfirm: () -> Void
    
    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)
            
            // 弹框内容
            VStack(spacing: 20) {
                // 图标
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.2))
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: icon)
                        .resizable()
                        .scaledToFit()
                        .foregroundColor(iconColor)
                        .frame(width: 30, height: 30)
                }
                .padding(.top, 20)
                
                // 标题
                Text(title)
                    .font(.custom("PingFang-SC-Bold", size: 18))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                // 信息
                Text(message)
                    .font(.custom("PingFang-SC-Regular", size: 14))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                    .padding(.bottom, 10)
                
                // 按钮
                HStack(spacing: 15) {
                    // 取消按钮
                    Button(action: onCancel) {
                        Text("cancel".localized)
                            .font(.custom("PingFang-SC-Medium", size: 16))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                            )
                    }
                    
                    // 确认按钮
                    Button(action: onConfirm) {
                        Text("confirm".localized)
                            .font(.custom("PingFang-SC-Medium", size: 16))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color.blue)
                            )
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 20)
            }
            .frame(width: 300)
            .background(Color(red: 0.15, green: 0.17, blue: 0.23))
            .cornerRadius(20)
        }
    }
}





/// 帮助与支持视图
struct HelpSupportView: View {
    // MARK: - 状态属性
    @State private var searchQuery = ""
    @State private var selectedFAQ: String? = nil
    
    // FAQ数据
    private let faqs = [
        "help_faq_q1".localized: "help_faq_a1".localized,
        "help_faq_q2".localized: "help_faq_a2".localized,
        "help_faq_q3".localized: "help_faq_a3".localized,
        "help_faq_q4".localized: "help_faq_a4".localized,
        "help_faq_q5".localized: "help_faq_a5".localized
    ]
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 搜索栏
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.white.opacity(0.7))
                    
                    TextField("help_search_placeholder".localized, text: $searchQuery)
                        .foregroundColor(.white)
                }
                .padding()
                .background(Color.white.opacity(0.1))
                .cornerRadius(10)
                .padding(.horizontal)
                
                // 常见问题
                VStack(alignment: .leading, spacing: 15) {
                    Text("help_faq".localized)
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.horizontal)
                    
                    VStack(spacing: 0) {
                        ForEach(faqs.keys.sorted().filter {
                            searchQuery.isEmpty || $0.lowercased().contains(searchQuery.lowercased())
                        }, id: \.self) { question in
                            VStack(alignment: .leading, spacing: 10) {
                                Button(action: {
                                    if selectedFAQ == question {
                                        selectedFAQ = nil
                                    } else {
                                        selectedFAQ = question
                                    }
                                }) {
                                    HStack {
                                        Text(question)
                                            .foregroundColor(.white)
                                            .multilineTextAlignment(.leading)
                                        
                                        Spacer()
                                        
                                        Image(systemName: selectedFAQ == question ? "chevron.up" : "chevron.down")
                                            .foregroundColor(.white.opacity(0.7))
                                    }
                                }
                                
                                if selectedFAQ == question {
                                    Text(faqs[question] ?? "")
                                        .font(.subheadline)
                                        .foregroundColor(.white.opacity(0.8))
                                        .padding(.top, 5)
                                }
                            }
                            .padding()
                            .background(selectedFAQ == question ? Color.white.opacity(0.1) : Color.clear)
                            
                            if question != faqs.keys.sorted().last {
                                Divider().background(Color.white.opacity(0.1))
                            }
                        }
                    }
                    .background(Color.white.opacity(0.05))
                    .cornerRadius(10)
                    .padding(.horizontal)
                }
                
                // 联系支持
                VStack(alignment: .leading, spacing: 15) {
                    Text("help_contact_support".localized)
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.horizontal)
                    
                    VStack(spacing: 10) {
                        Button(action: {
                            // 发送邮件
                        }) {
                            HStack {
                                Image(systemName: "envelope.fill")
                                    .foregroundColor(.white)
                                
                                Text("help_send_email".localized)
                                    .foregroundColor(.white)
                                
                                Spacer()
                                
                                Text("<EMAIL>")
                                    .foregroundColor(.white.opacity(0.7))
                            }
                            .padding()
                            .background(Color.white.opacity(0.1))
                            .cornerRadius(10)
                        }
                        
                        Button(action: {
                            // 拨打电话
                        }) {
                            HStack {
                                Image(systemName: "phone.fill")
                                    .foregroundColor(.white)
                                
                                Text("help_hotline".localized)
                                    .foregroundColor(.white)
                                
                                Spacer()
                                
                                Text("************")
                                    .foregroundColor(.white.opacity(0.7))
                            }
                            .padding()
                            .background(Color.white.opacity(0.1))
                            .cornerRadius(10)
                        }
                    }
                    .padding(.horizontal)
                }
            }
            .padding(.vertical)
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#00204A"),
                    Color(hex: "#002D67")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
        )
        .navigationTitle("help_support_title".localized)
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - 开发者工具视图
struct DeveloperToolsListView: View {
    var body: some View {
        List {
            // 数据库调试
            NavigationLink(destination: EmptyView()) {
                SettingsItemView(
                    icon: "database.fill",
                    iconColor: .purple,
                    title: "dev_tools_db_debugging".localized
                )
            }
            
            // API测试
            NavigationLink(destination: EmptyView()) {
                SettingsItemView(
                    icon: "network",
                    iconColor: .blue,
                    title: "dev_tools_api_testing".localized
                )
            }
            
            // 设备测试
            NavigationLink(destination: EmptyView()) {
                SettingsItemView(
                    icon: "bluetooth",
                    iconColor: .blue,
                    title: "dev_tools_device_testing".localized
                )
            }
            
            // 传感器测试
            NavigationLink(destination: EmptyView()) {
                SettingsItemView(
                    icon: "sensor.tag.radiowaves.forward.fill",
                    iconColor: .green,
                    title: "dev_tools_sensor_testing".localized
                )
            }
            
            // 日志查看
            NavigationLink(destination: EmptyView()) {
                SettingsItemView(
                    icon: "doc.text.magnifyingglass",
                    iconColor: .orange,
                    title: "dev_tools_log_viewer".localized
                )
            }
            
            // 性能测试
            NavigationLink(destination: EmptyView()) {
                SettingsItemView(
                    icon: "gauge",
                    iconColor: .red,
                    title: "dev_tools_performance_testing".localized
                )
            }
        }
        .navigationTitle("developer_tools".localized)
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - 设置项视图
struct SettingsItemView: View {
    var icon: String
    var iconColor: Color = .blue
    var title: String
    var subtitle: String? = nil
    var showArrow: Bool = true
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 22))
                .foregroundColor(iconColor)
                .frame(width: 28, height: 28)
                .padding(.trailing, 8)
            
            if let subtitle = subtitle {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.body)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            } else {
                Text(title)
                    .font(.body)
            }
            
            Spacer()
            
            if showArrow {
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
            }
        }
        .padding(.vertical, 8)
    }
} 
