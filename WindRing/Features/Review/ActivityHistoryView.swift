import SwiftUI
import UIKit
import Charts

/// 活动历史详情页面
struct ActivityHistoryView: View {
    // MARK: - 属性
    @Environment(\.presentationMode) var presentationMode
    
    // ViewModel
    @StateObject private var viewModel = ActivityHistoryViewModel()
    
    // 时间范围选择
    @State private var selectedTimeRange = 0 // 0: Week, 1: Month, 2: Year
    var timeRanges: [String] {
        ["sleep_history_time_range_week".localized, "sleep_history_time_range_month".localized, "sleep_history_time_range_year".localized]
    }
    
    // 日期选择
    @State private var showingDatePicker = false
    @State private var selectedDate = Date()
    
    // 详情弹窗
    @State private var showingDetailSheet = false
    @State private var selectedDetailTitle = ""
    @State private var selectedDetailIcon = ""
    @State private var selectedDetailMaxValue = ""
    @State private var selectedDetailAvgValue = ""
    @State private var selectedDetailMinValue = ""
    @State private var selectedDetailColor = Color.blue
    @State private var selectedDetailData: [Int] = []
    @State private var selectedDetailShowPercentages = false
    @State private var selectedDetailChartType = 0 // 0: bar, 1: line
    
    // 星期和月份标签
    var yearMonths: [String] {
        ["sleep_history_year_jan".localized, "sleep_history_year_feb".localized, "sleep_history_year_mar".localized, "sleep_history_year_apr".localized, "sleep_history_year_may".localized, "sleep_history_year_jun".localized, "sleep_history_year_jul".localized, "sleep_history_year_aug".localized, "sleep_history_year_sep".localized, "sleep_history_year_oct".localized, "sleep_history_year_nov".localized, "sleep_history_year_dec".localized].map { $0.localized }
    }
    
    // 动态X轴标签
    var xAxisLabels: [String] {
        let calendar = Calendar.current
        
        switch selectedTimeRange {
        case 0: // 周
            return ["sleep_history_weekday_sun".localized, "sleep_history_weekday_mon".localized, "sleep_history_weekday_tue".localized, "sleep_history_weekday_wed".localized, "sleep_history_weekday_thu".localized, "sleep_history_weekday_fri".localized, "sleep_history_weekday_sat".localized]

        case 1: // 月
            let components = viewModel.dateRange.split(separator: "-").map { String($0) }
            guard components.count == 2 else { return [] }
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy.M.d"
            guard let startDate = dateFormatter.date(from: components[0]) else { return [] }
            guard let daysInMonth = calendar.range(of: .day, in: .month, for: startDate)?.count else { return [] }
            return (1...daysInMonth).map { String($0) }

        case 2: // 年
            return yearMonths//DateFormatter().shortMonthSymbols
            
        default:
            return []
        }
    }
    
    // MARK: - 视图
    var body: some View {
        VStack(spacing: 0) {
            // 时间范围选择器
            timeRangeSelector
                .background(Color(red: 0.08, green: 0.09, blue: 0.13)) // 使用深色背景
            
            // 内容区域
            ActivityHistoryContentView(
                viewModel: viewModel,
                showingDatePicker: $showingDatePicker,
                selectedDate: $selectedDate,
                selectedTimeRange: $selectedTimeRange
            )
        }
        .background( // 整体背景
            LinearGradient(
                gradient: Gradient(colors: [Color(red: 0.16, green: 0.19, blue: 0.25), Color(red: 0.08, green: 0.09, blue: 0.13)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)
        )
        .navigationBarBackButtonHidden(true)
        .navigationBarTitleDisplayMode(.inline)
        .toolbarBackground(Color(red: 0.08, green: 0.09, blue: 0.13).opacity(1.0), for: .navigationBar)
        .toolbarBackground(.visible, for: .navigationBar)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image("back")
                            .font(.system(size: 14))
                        Text("activity_history_nav_title".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                    }
                    .foregroundColor(.white)
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        showingDatePicker = true
                    }
                }) {
                    HStack {
                        Text(viewModel.dateRange)
                            .font(.custom("DIN-Medium", size: 13))
                            .foregroundColor(.white)
                            .lineLimit(nil)
                        
                        Image("down")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 11, height: 11)
                    }
                    .frame(height: 9.5)
                }
            }
        }
        .onAppear {
            viewModel.loadDataForTimeRange(selectedTimeRange)
        }
        .onChange(of: selectedTimeRange) { newValue in
            viewModel.loadDataForTimeRange(newValue)
        }
        .sheet(isPresented: $showingDetailSheet) {
            detailSheetView
        }
    }
    
    // MARK: - 子视图
    
    // MARK: - 时间范围选择器
    private var timeRangeSelector: some View {
        VStack(spacing: 0) {
            HStack(spacing: 0) {
                ForEach(0..<timeRanges.count, id: \.self) { index in
                    Button(action: {
                        withAnimation {
                            selectedTimeRange = index
                        }
                    }) {
                        Text(timeRanges[index])
                            .font(.system(size: 15, weight: .semibold))
                            .foregroundColor(.white)
                            .opacity(selectedTimeRange == index ? 1.0 : 0.6) // 未选中时透明度设置为60%
                            .frame(maxWidth: .infinity)
                            .frame(height: 36)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(selectedTimeRange == index ? Color.blue : Color.clear)
                            )
                            .contentShape(Rectangle())
                    }
                    .padding(.horizontal, 5)
                }
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 8)
            .background(Color(red: 0.08, green: 0.09, blue: 0.13))
        }
    }
    
    // MARK: - 辅助视图
    
    // 详情弹窗视图
    private var detailSheetView: some View {
        VStack(spacing: 20) {
            // 标题栏
            HStack {
                Button("activity_history_detail_sheet_close".localized) {
                    showingDetailSheet = false
                }
                .foregroundColor(.blue)
                
                Spacer()
                
                Text(selectedDetailTitle)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button("activity_history_detail_sheet_help".localized) {
                    // 显示帮助信息
                }
                .foregroundColor(.blue)
            }
            .padding()
            .background(Color(red: 0.1, green: 0.1, blue: 0.1))
            
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // 数据卡片
                    HStack(spacing: 8) {
                        // 最大值
                        detailMetricCard(title: "activity_history_detail_sheet_maximum".localized, value: selectedDetailMaxValue)
                            .frame(maxWidth: .infinity)
                        
                        // 平均值
                        detailMetricCard(title: "activity_history_detail_sheet_average".localized, value: selectedDetailAvgValue)
                            .frame(maxWidth: .infinity)
                        
                        // 最小值
                        detailMetricCard(title: "activity_history_detail_sheet_minimum".localized, value: selectedDetailMinValue)
                            .frame(maxWidth: .infinity)
                    }
                    .padding(.horizontal)
                    
                    // 大图表
                    VStack {
                        if selectedDetailChartType == 0 {
                            // 柱状图
                            detailBarChartView
                        } else {
                            // 曲线图
                            detailLineChartView
                        }
                        
                        // 时间标签
                        HStack(spacing: 0) {
                            ForEach(xAxisLabels, id: \.self) { label in
                                Text(label)
                                    .font(.system(size: 12))
                                    .foregroundColor(.gray)
                                    .frame(maxWidth: .infinity)
                            }
                        }
                        .padding(.top, 8)
                        .padding(.horizontal)
                    }
                    .frame(height: 250)
                    .padding(.vertical)
                    .background(Color(red: 0.06, green: 0.06, blue: 0.08))
                    
                    // 说明文本
                    VStack(alignment: .leading, spacing: 16) {
                        Text(String(format: "activity_history_detail_sheet_about".localized, selectedDetailTitle))
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        Text(getDetailDescription())
                            .font(.body)
                            .foregroundColor(.gray)
                            .lineSpacing(4)
                        
                        Text("activity_history_detail_sheet_how_to_improve".localized)
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.top, 8)
                        
                        Text(getDetailImprovement())
                            .font(.body)
                            .foregroundColor(.gray)
                            .lineSpacing(4)
                    }
                    .padding()
                    
                    // 活动按钮
                    Button(action: {
                        // 触发活动操作
                        showingDetailSheet = false
                    }) {
                        Text("activity_history_detail_sheet_start_activity".localized)
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                    .padding()
                }
            }
        }
        .background(Color.black)
        .edgesIgnoringSafeArea(.bottom)
    }
    
    // 详情页大柱状图
    private var detailBarChartView: some View {
        HStack(alignment: .bottom, spacing: 0) {
            ForEach(0..<selectedDetailData.count, id: \.self) { index in
                VStack {
                    // 高亮选中的数据点
                    if (selectedTimeRange == 0 && index == 3) || 
                       (selectedTimeRange == 1 && index == 2) || 
                       (selectedTimeRange == 2 && index == 2) {
                        Rectangle()
                            .fill(selectedDetailColor)
                            .frame(
                                width: selectedDetailShowPercentages ? 8 : 16,
                                height: getBarHeight(for: index)
                            )
                            .cornerRadius(4)
                    } else {
                        Rectangle()
                            .fill(selectedDetailColor.opacity(0.3))
                            .frame(
                                width: selectedDetailShowPercentages ? 8 : 16,
                                height: getBarHeight(for: index)
                            )
                            .cornerRadius(4)
                    }
                    
                    if selectedDetailShowPercentages {
                        // 显示百分比刻度线
                        HStack(spacing: 2) {
                            ForEach(0..<3) { _ in
                                Rectangle()
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(width: 3, height: 3)
                                    .cornerRadius(1.5)
                            }
                        }
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal)
        .animation(.easeInOut, value: selectedDetailData)
    }
    
    // 计算柱状图高度
    private func getBarHeight(for index: Int) -> CGFloat {
        if selectedDetailData.isEmpty { return 0 }
        
        let maxVal = selectedDetailData.max() ?? 1
        let val = selectedDetailData[index]
        
        // 避免除以零
        if maxVal <= 0 { return 1 }
        
        let heightPercentage = CGFloat(val) / CGFloat(maxVal)
        
        return heightPercentage * 200
    }
    
    // 详情页大曲线图
    private var detailLineChartView: some View {
        ZStack {
            // 横向网格线
            VStack(spacing: 40) {
                ForEach(0..<5) { _ in
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 1)
                }
            }
            
            // 示例曲线
            Path { path in
                let width = UIScreen.main.bounds.width - 32
                let spacing = width / CGFloat(xAxisLabels.count - 1)
                
                path.move(to: CGPoint(x: 0, y: 120))
                
                if selectedTimeRange == 0 {
                    // 周视图曲线
                    path.addCurve(
                        to: CGPoint(x: width, y: 80),
                        control1: CGPoint(x: spacing * 2, y: 40),
                        control2: CGPoint(x: spacing * 5, y: 160)
                    )
                } else if selectedTimeRange == 1 {
                    // 月视图曲线
                    path.addCurve(
                        to: CGPoint(x: width, y: 100),
                        control1: CGPoint(x: spacing * 1, y: 80),
                        control2: CGPoint(x: spacing * 3, y: 40)
                    )
                } else {
                    // 年视图曲线
                    path.addCurve(
                        to: CGPoint(x: width, y: 60),
                        control1: CGPoint(x: spacing * 3, y: 180),
                        control2: CGPoint(x: spacing * 8, y: 20)
                    )
                }
            }
            .stroke(selectedDetailColor, lineWidth: 3)
            
            // 数据点
            Path { path in
                let width = UIScreen.main.bounds.width - 32
                let spacing = width / CGFloat(xAxisLabels.count - 1)
                
                var y = 120.0
                
                if selectedTimeRange == 0 {
                    // 周视图点
                    y = 120
                    let x1: CGFloat = 0
                    let centerX1 = x1 - 5
                    let centerY1 = y - 5
                    let rectX1 = centerX1
                    let rectY1 = centerY1
                    path.addEllipse(in: CGRect(x: rectX1, y: rectY1, width: 10, height: 10))
                    
                    y = 80
                    let x2: CGFloat = spacing * 2
                    let centerX2 = x2 - 5
                    let centerY2 = y - 5
                    let rectX2 = centerX2
                    let rectY2 = centerY2
                    path.addEllipse(in: CGRect(x: rectX2, y: rectY2, width: 10, height: 10))
                    
                    y = 120
                    let x3: CGFloat = spacing * 4
                    let centerX3 = x3 - 5
                    let centerY3 = y - 5
                    let rectX3 = centerX3
                    let rectY3 = centerY3
                    path.addEllipse(in: CGRect(x: rectX3, y: rectY3, width: 10, height: 10))
                    
                    y = 60
                    let x4: CGFloat = spacing * 6
                    let centerX4 = x4 - 5
                    let centerY4 = y - 5
                    let rectX4 = centerX4
                    let rectY4 = centerY4
                    path.addEllipse(in: CGRect(x: rectX4, y: rectY4, width: 10, height: 10))
                } else if selectedTimeRange == 1 {
                    // 月视图点
                    for i in 0..<xAxisLabels.count {
                        y = Double.random(in: 40...180)
                        let xPos: CGFloat = spacing * CGFloat(i)
                        let centerX = xPos - 5
                        let centerY = y - 5
                        let rectX = centerX
                        let rectY = centerY
                        path.addEllipse(in: CGRect(x: rectX, y: rectY, width: 10, height: 10))
                    }
                } else {
                    // 年视图点
                    for i in 0..<xAxisLabels.count {
                        y = Double.random(in: 20...180)
                        let xPos: CGFloat = spacing * CGFloat(i)
                        let centerX = xPos - 5
                        let centerY = y - 5
                        let rectX = centerX
                        let rectY = centerY
                        path.addEllipse(in: CGRect(x: rectX, y: rectY, width: 10, height: 10))
                    }
                }
            }
            .fill(selectedDetailColor)
        }
        .animation(.easeInOut, value: selectedTimeRange)
    }
    
    // 详情页指标卡片
    private func detailMetricCard(title: String, value: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.system(size: 14))
                .foregroundColor(.gray)
            
            Text(value)
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(.white)
        }
        .padding(16)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color(red: 0.1, green: 0.1, blue: 0.12))
        .cornerRadius(10)
    }
    
    // 获取详情页描述
    private func getDetailDescription() -> String {
        switch selectedDetailTitle {
        case "activity_history_desc_title_score".localized:
            return "activity_history_desc_content_score".localized
        case "activity_history_desc_title_steps".localized:
            return "activity_history_desc_content_steps".localized
        case "activity_history_desc_title_distance".localized:
            return "activity_history_desc_content_distance".localized
        case "activity_history_desc_title_calories".localized:
            return "activity_history_desc_content_calories".localized
        case "activity_history_desc_title_hr".localized:
            return "activity_history_desc_content_hr".localized
        case "activity_history_desc_title_zones".localized:
            return "activity_history_desc_content_zones".localized
        default:
            return "activity_history_detail_sheet_no_info".localized
        }
    }
    
    // 获取详情页改善建议
    private func getDetailImprovement() -> String {
        switch selectedDetailTitle {
        case "activity_history_improve_title_score".localized:
            return "activity_history_improve_content_score".localized
        case "activity_history_improve_title_steps".localized:
            return "activity_history_improve_content_steps".localized
        case "activity_history_improve_title_distance".localized:
            return "activity_history_improve_content_distance".localized
        case "activity_history_improve_title_calories".localized:
            return "activity_history_improve_content_calories".localized
        case "activity_history_improve_title_hr".localized:
            return "activity_history_improve_content_hr".localized
        case "activity_history_improve_title_zones".localized:
            return "activity_history_improve_content_zones".localized
        default:
            return "activity_history_detail_sheet_no_suggestion".localized
        }
    }
    
    // 根据选择的日期更新日期范围
    private func updateDateRangeFromSelection() {
        viewModel.updateDateRange(from: selectedDate, for: selectedTimeRange)
    }
    
}

// MARK: - 统一活动柱状图组件
struct ActivityBarChartView: View {
    struct ChartDataPoint: Identifiable {
        let id = UUID()
        let label: String
        let value: Double
    }
    
    enum BarStyle {
        case solid(Color)
        case gradient(LinearGradient)
    }

    let chartData: [ChartDataPoint]
    let style: BarStyle
    let maxValue: Double
    let selectedTimeRange: Int
    let valueFormat: String
    
    @State private var selectedPoint: ChartDataPoint?

    var body: some View {
        Chart {
            ForEach(chartData) { point in
                switch style {
                case .solid(let color):
                    BarMark(
                        x: .value("Day", point.label),
                        y: .value("Value", point.value)
                    )
                    .foregroundStyle(color)
                    .cornerRadius(2)
                    
                case .gradient(let gradient):
                    BarMark(
                        x: .value("Day", point.label),
                        y: .value("Value", point.value)
                    )
                    .foregroundStyle(gradient)
                    .cornerRadius(2)
                }
            }
        }
        .chartYScale(domain: 0...(maxValue > 0 ? maxValue * 1.2 : 100))
        .chartXAxis {
            AxisMarks(values: .automatic) { value in
                if let label = value.as(String.self) {
                    if shouldShowLabel(label: label) {
                        AxisValueLabel(label)
                            .font(.system(size: 8))
                            .foregroundStyle(Color.white)
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(values: .automatic) { value in
                if let doubleValue = value.as(Double.self) {
                    AxisGridLine()
                    AxisTick()
                    AxisValueLabel {
                        Text(String(format: "%.0f", doubleValue)) // 或格式化成 Int
                            .font(.system(size: 8))
                            .foregroundColor(.white)
                    }
                }
            }
        }
//        .chartYAxis(.hidden)
        .chartOverlay { proxy in
            GeometryReader { geometry in
                Rectangle().fill(.clear).contentShape(Rectangle())
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { value in
                                let origin = geometry[proxy.plotAreaFrame].origin
                                let location = value.location
                                if let label: String = proxy.value(atX: location.x - origin.x) {
                                    var minDistance: Double = .infinity
                                    var closestPoint: ChartDataPoint? = nil
                                    for point in chartData {
                                        let pointX = proxy.position(forX: point.label) ?? 0
                                        let distance = abs(pointX - (location.x - origin.x))
                                        if distance < minDistance && distance < 20 { // 仅在足够近时选择
                                            minDistance = distance
                                            closestPoint = point
                                        }
                                    }
                                    selectedPoint = closestPoint
                                }
                            }
                            .onEnded { _ in selectedPoint = nil }
                    )
            }
        }
        .chartOverlay { proxy in
            GeometryReader { geo in
                if let selectedPoint = selectedPoint {
                    let xPos = proxy.position(forX: selectedPoint.label) ?? 0
                    
                    ZStack(alignment: .topLeading) {
                        // 竖线
                        Path { path in
                            path.move(to: CGPoint(x: xPos, y: 0))
                            path.addLine(to: CGPoint(x: xPos, y: geo.size.height))
                        }
                        .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                        
                        // 悬浮提示框
                        VStack {
                            Text(String(format: "%.1f", selectedPoint.value))
                                .font(.caption)
                                .padding(6)
                                .background(Color.black.opacity(0.7))
                                .foregroundColor(.white)
                                .cornerRadius(4)
                        }
                        .position(x: xPos, y: 10) // 10是离顶部距离，可调
                    }
                    .frame(width: geo.size.width, height: geo.size.height)
                }
            }
        }
        
    }

    private func shouldShowLabel(label: String) -> Bool {
        switch selectedTimeRange {
        case 0, 2: // Week and Year, show all
            return true
        case 1: // Month, show specific labels
            let daysToShow = ["3", "10", "17", "24",chartData.count.description]
            return daysToShow.contains(label)
        default:
            return false
        }
    }
    
}

// MARK: - 预览
struct ActivityHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ActivityHistoryView()
        }
        .preferredColorScheme(.dark)
    }
}



