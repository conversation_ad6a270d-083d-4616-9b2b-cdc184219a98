import SwiftUI
import Charts

/// 压力历史详情页面
struct StressHistoryView: View {
    // MARK: - 属性
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel = StressHistoryViewModel()
    
    // 图表类型
    enum ChartType {
        case line
        case bar
    }
    
    // 时间范围选择
    @State private var selectedTimeRange = 0 // 0: Week, 1: Month, 2: Year
    var timeRanges: [String] {
        ["sleep_history_time_range_week".localized, "sleep_history_time_range_month".localized, "sleep_history_time_range_year".localized]
    }
    
    // 日期范围
    @State private var dateRange = "0000.0.00-0000.00.00"
    @State private var showingDatePicker = false
    @State private var selectedDate = Date()
    
    var yearMonths: [String] {
        ["sleep_history_year_jan", "sleep_history_year_feb", "sleep_history_year_mar", "sleep_history_year_apr", "sleep_history_year_may", "sleep_history_year_jun", "sleep_history_year_jul", "sleep_history_year_aug", "sleep_history_year_sep", "sleep_history_year_oct", "sleep_history_year_nov", "sleep_history_year_dec"].map { $0.localized }
    }
    
    // 动态X轴标签
    var xAxisLabels: [String] {
        let calendar = Calendar.current
        
        switch selectedTimeRange {
        case 0: // 周
            return ["sleep_history_weekday_sun".localized, "sleep_history_weekday_mon".localized, "sleep_history_weekday_tue".localized, "sleep_history_weekday_wed".localized, "sleep_history_weekday_thu".localized, "sleep_history_weekday_fri".localized, "sleep_history_weekday_sat".localized]
            
        case 1: // 月
            let components = viewModel.dateRange.split(separator: "-").map { String($0) }
            guard components.count == 2 else { return [] }
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy.M.d"
            guard let startDate = dateFormatter.date(from: components[0]) else { return [] }
            guard let daysInMonth = calendar.range(of: .day, in: .month, for: startDate)?.count else { return [] }
            return (1...daysInMonth).map { String($0) }

        case 2: // 年
            return yearMonths//DateFormatter().shortMonthSymbols
            
        default: 
            return []
        }
    }
    
    // 详情弹窗
    @State private var showingDetailSheet = false
    
    // 压力得分数据
    var currentStressScoreData: [Int] {
        switch selectedTimeRange {
        case 1: return viewModel.stressScoreMonthData
        case 2: return viewModel.stressScoreYearData
        default: return viewModel.stressScoreWeekData
        }
    }
    
    // 夜间压力得分数据
    var currentNightStressScoreData: [Int] {
        switch selectedTimeRange {
        case 1: return viewModel.eveningScoreMonthData
        case 2: return viewModel.eveningScoreYearData
        default: return viewModel.eveningScoreWeekData
        }
    }
    
    // MARK: - 视图
    var body: some View {
        VStack(spacing: 0) {
            // 时间范围选择器
            timeRangeSelector
                .background(Color(red: 0.08, green: 0.09, blue: 0.13)) // 使用深色背景
            
            // 内容区域
            ZStack {
                ScrollView {
                    VStack(spacing: 16) {
                        // 内容从这里开始
                        if viewModel.isLoading && currentStressScoreData.isEmpty {
                            loadingView
                        } else if let errorMessage = viewModel.errorMessage {
                            errorView(message: errorMessage)
                        } else {
                        // 压力模块
                            stressScoreSection(
                            title: "stress_history_section_title_score".localized,
                            icon: "star.fill",
                                iconColor: .blue,
                                maxValue: "\(viewModel.stressScoreMax)",
                                avgValue: "\(viewModel.stressScoreAvg)",
                                minValue: "\(viewModel.stressScoreMin)",
                                unit: "activity_history_unit_score".localized,
                                data: currentStressScoreData,
                                color: Color(hex: "#4A90E2"),
                                gradient: LinearGradient(
                                    gradient: Gradient(colors: [Color(hex: "#4A90E2").opacity(0.4), Color(hex: "#4A90E2").opacity(0.0)]),
                                    startPoint: .top,
                                    endPoint: .bottom
                                ),
                            chartType: .line, glossaryKey: .historyStressValue // 压力分数使用线形图
                            )
                            
                        // 夜间压力得分模块
                            stressScoreSection(
                            title: "stress_history_section_title_night_score".localized,
                            icon: "moon.stars.fill",
                                iconColor: .purple,
                                maxValue: "\(viewModel.eveningScoreMax)",
                                avgValue: "\(viewModel.eveningScoreAvg)",
                                minValue: "\(viewModel.eveningScoreMin)",
                                unit: "activity_history_unit_score".localized,
                                data: currentNightStressScoreData,
                                color: .purple,
                                gradient: LinearGradient(
                                    gradient: Gradient(colors: [Color.purple.opacity(0.4), Color.purple.opacity(0.0)]),
                                    startPoint: .top,
                                    endPoint: .bottom
                                ),
                            chartType: .bar, glossaryKey: .historyStressEvening // 夜间压力使用柱状图
                            )
                        }
                        
                        Spacer(minLength: 30)
                    }
                    .padding(.horizontal)
                }
                .refreshable {
                    Task {
                        await viewModel.refreshData()
                    }
                }
                .background(Color(red: 0.07, green: 0.07, blue: 0.09).edgesIgnoringSafeArea(.all))
                
                // 日历弹框直接覆盖在页面上，而不是作为sheet呈现
                if showingDatePicker {
                    datePickerView
                        .zIndex(100) // 确保在最上层
                        .transition(.opacity.animation(.easeInOut(duration: 0.2)))
                }
            }
        }
        .background( // 整体背景
            LinearGradient(
                gradient: Gradient(colors: [Color(red: 0.16, green: 0.19, blue: 0.25), Color(red: 0.08, green: 0.09, blue: 0.13)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)
        )
        .navigationBarBackButtonHidden(true)
        .navigationBarTitleDisplayMode(.inline)
        .toolbarBackground(Color(red: 0.08, green: 0.09, blue: 0.13).opacity(1.0), for: .navigationBar)
        .toolbarBackground(.visible, for: .navigationBar)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image("back")
                            .font(.system(size: 14))
                        Text("stress_history_nav_title".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                    }
                    .foregroundColor(.white)
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        showingDatePicker = true
                    }
                }) {
                    HStack {
                        Text(viewModel.dateRange)
                            .font(.custom("DIN-Medium", size: 13))
                            .foregroundColor(.white)
                        
                        Image("down")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 11, height: 11)
                    }
                    .frame(height: 9.5)
                }
            }
        }
        .onAppear {
            viewModel.loadDataForTimeRange(selectedTimeRange)
        }
        .onChange(of: selectedTimeRange) { newValue in
            viewModel.loadDataForTimeRange(newValue)
        }
    }
    
    
    // MARK: - 子视图
    
    // 统一的压力分数/时段模块
    private func stressScoreSection(
        title: String,
        icon: String,
        iconColor: Color,
        maxValue: String,
        avgValue: String,
        minValue: String,
        unit: String,
        data: [Int],
        color: Color,
        gradient: LinearGradient,
        chartType: ChartType,
        glossaryKey: GlossaryKey
    ) -> some View {
        let yValues = getYAxisValues(for: Double(data.max() ?? 0))
        let chartData = chartData(for: data.map { Double($0) })

        return VStack(alignment: .leading, spacing: 12) {
            // 标题行
                HStack {
                    Image(systemName: icon)
                    .font(.system(size: 18))
                    .foregroundColor(iconColor)
                    
                    Text(title)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                    
                    InfoButtonWithGlossaryPopup(showKey: glossaryKey.rawValue)
                    Spacer()
                 
                    
//                Image(systemName: "info.circle")
//                    .font(.system(size: 14))
//                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            
            // 最大、平均、最小值卡片
            HStack(spacing: 10) {
                metricCard(title: "activity_history_metric_max".localized, value: maxValue, unit: unit)
                metricCard(title: "activity_history_metric_ave".localized, value: avgValue, unit: unit)
                metricCard(title: "activity_history_metric_min".localized, value: minValue, unit: unit)
            }
            .padding(.horizontal, 16)
            
            // 图表
            if chartType == .line {
                StressLineChartView(
                    chartData: chartData,
                    lineColor: color,
                    areaGradient: gradient,
                    yValues: yValues,
                    selectedTimeRange: selectedTimeRange,
                    valueFormat: "%.0f"
                )
                .frame(height: 120)
                .padding(.top, 10)
            } else {
                StressBarChartView(
                    chartData: chartData,
                    barColor: color,
                    yValues: yValues,
                    selectedTimeRange: selectedTimeRange,
                    valueFormat: "%.0f"
                )
                .frame(height: 120)
                .padding(.top, 10)
            }
        }
        .padding(.vertical, 16)
        .background(Color(red: 0.1, green: 0.1, blue: 0.12).cornerRadius(16))
                        }
    
    // 时间范围选择器
    private var timeRangeSelector: some View {
        VStack(spacing: 0) {
            HStack(spacing: 0) {
                ForEach(0..<timeRanges.count, id: \.self) { index in
                    Button(action: {
                        withAnimation {
                            selectedTimeRange = index
                        }
                    }) {
                        Text(timeRanges[index])
                            .font(.system(size: 15, weight: .semibold))
                            .foregroundColor(.white)
                            .opacity(selectedTimeRange == index ? 1.0 : 0.6) // 未选中时透明度设置为60%
                        .frame(maxWidth: .infinity)
                            .frame(height: 36)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(selectedTimeRange == index ? Color.blue : Color.clear)
                            )
                            .contentShape(Rectangle())
                    }
                    .padding(.horizontal, 5)
                    }
                }
            .padding(.horizontal, 10)
            .padding(.vertical, 8)
            .background(Color(red: 0.08, green: 0.09, blue: 0.13))
        }
    }
    
    // 数据卡片
    private func metricCard(title: String, value: String, unit: String) -> some View {
        VStack(alignment: .center, spacing: 4) {
            Text(title)
                .font(.system(size: 14))
                .foregroundColor(.gray)
            
            HStack(alignment: .firstTextBaseline, spacing: 2) {
            Text(value)
                    .font(.system(size: 20, weight: .bold))
                .foregroundColor(.white)
                
                Text(unit)
                    .font(.system(size: 14))
                    .foregroundColor(.white)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(Color(red: 0.13, green: 0.13, blue: 0.15))
        .cornerRadius(10)
    }
    
    private func chartData(for data: [Double]) -> [StressLineChartView.ChartDataPoint] {
        let labels = xAxisLabels
        var points: [StressLineChartView.ChartDataPoint] = []
        for i in 0..<labels.count {
            let value = i < data.count ? data[i] : 0.0
            points.append(.init(label: labels[i], value: value > 0 ? value : nil))
        }
        return points
    }

    private func getYAxisValues(for maxValue: Double) -> (top: Double, mid: Double) {
        let topValue: Double
        let midValue: Double

        if maxValue > 0 {
            topValue = maxValue * 1.2
            midValue = topValue / 2
        } else {
            topValue = 100
            midValue = 50
        }
        
        return (topValue, midValue)
    }

    // 加载视图
    private var loadingView: some View {
        ProgressView()
            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
            .frame(height: 300)
    }
    
    // 错误视图
    private func errorView(message: String) -> some View {
        Text(message)
            .foregroundColor(.red)
            .frame(height: 300)
    }
    
    // 日期选择器视图
    private var datePickerView: some View {
        CustomCalendarView(
            selectedDate: $selectedDate,
            onClose: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    showingDatePicker = false
                }
            },
            onDateSelected: { date in
                // 更新日期并重新加载数据
                selectedDate = date
                updateDateRangeFromSelection()
                withAnimation(.easeInOut(duration: 0.2)) {
                    showingDatePicker = false
                }
            }
        )
    }
    
    // 根据选择的日期更新日期范围
    private func updateDateRangeFromSelection() {
        viewModel.updateDateRange(from: selectedDate, for: selectedTimeRange)
    }
    
    // MARK: - 日历视图
    struct CustomCalendarView: View {
        @Binding var selectedDate: Date
        var onClose: () -> Void
        var onDateSelected: (Date) -> Void
        
        @State private var currentMonth: Date
        
        private let calendar = Calendar.current
        private let monthFormatter: DateFormatter
        private let weekdaySymbols: [String]
        
        init(selectedDate: Binding<Date>, onClose: @escaping () -> Void, onDateSelected: @escaping (Date) -> Void) {
            self._selectedDate = selectedDate
            self.onClose = onClose
            self.onDateSelected = onDateSelected
            self._currentMonth = State(initialValue: selectedDate.wrappedValue)
            
            self.monthFormatter = DateFormatter()
            self.monthFormatter.dateFormat = "MMMM yyyy"
            
            self.weekdaySymbols = calendar.shortWeekdaySymbols
        }
        
        var body: some View {
            ZStack {
                // 半透明背景
                Color.black.opacity(0.6)
                    .ignoresSafeArea()
                    .onTapGesture(perform: onClose)
                
                VStack(spacing: 15) {
                    // 月份导航
                    HStack {
                        Button(action: { changeMonth(by: -1) }) {
                            Image(systemName: "chevron.left").font(.title2)
                        }
                        Spacer()
                        Text(monthFormatter.string(from: currentMonth))
                            .font(.system(size: 18, weight: .semibold))
                        Spacer()
                        Button(action: { changeMonth(by: 1) }) {
                            Image(systemName: "chevron.right").font(.title2)
                        }
                    }
                                .foregroundColor(.white)
                    .padding(.horizontal)
                    
                    // 星期标题
                    HStack {
                        ForEach(weekdaySymbols, id: \.self) { symbol in
                            Text(symbol)
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .frame(maxWidth: .infinity)
                        }
                    }
                    
                    // 日期网格
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 10) {
                        ForEach(fetchDates()) { dateValue in
                            dayView(for: dateValue)
                        }
                    }
                    
                    // 底部按钮
                    HStack {
                        Spacer()
                        Button("sleep_history_calendar_back_to_today".localized) {
                            onDateSelected(Date())
                            onClose()
                        }
                                .font(.system(size: 15))
                                .foregroundColor(.white)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 16)
                        .background(Capsule().stroke(Color.white.opacity(0.4)))
                    }
                    .padding(.horizontal)
                    
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(red: 0.10, green: 0.12, blue: 0.18))
                )
                .padding(.horizontal, 20)
            }
        }
        
        @ViewBuilder
        private func dayView(for dateValue: DateValue) -> some View {
            if dateValue.day != -1 {
                Button(action: {
                    selectedDate = dateValue.date
                    onDateSelected(dateValue.date)
                }) {
                    Text("\(dateValue.day)")
                        .font(.system(size: 16))
                        .foregroundColor(dateValue.isCurrentMonth ? .white : .gray)
                        .frame(width: 35, height: 35)
                        .background(
                            Circle()
                                .fill(calendar.isDate(dateValue.date, inSameDayAs: selectedDate) ? Color.blue : Color.clear)
                        )
                }
            } else {
                Rectangle().fill(.clear)
            }
        }
        
        private func changeMonth(by amount: Int) {
            if let newMonth = calendar.date(byAdding: .month, value: amount, to: currentMonth) {
                currentMonth = newMonth
            }
        }
        
        private func fetchDates() -> [DateValue] {
            guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else { return [] }
            
            var dates: [DateValue] = []
            
            let startDayOfWeek = calendar.component(.weekday, from: monthInterval.start) - 1
            
            // Add padding for days before the start of the month
            for _ in 0..<startDayOfWeek {
                dates.append(DateValue(day: -1, date: Date()))
        }
        
            // Add days of the month
            let daysInMonth = calendar.range(of: .day, in: .month, for: currentMonth)!.count
            for day in 1...daysInMonth {
                if let date = calendar.date(byAdding: .day, value: day - 1, to: monthInterval.start) {
                    dates.append(DateValue(day: day, date: date, isCurrentMonth: true))
            }
        }
            
            return dates
        }
        
    struct DateValue: Identifiable {
            let id = UUID()
        var day: Int
        var date: Date
            var isCurrentMonth: Bool = false
        }
    }
}




// MARK: - 新的线形图组件
struct StressLineChartView: View {
    struct ChartDataPoint: Identifiable {
        let id = UUID()
        let label: String
        let value: Double?
    }

    let chartData: [ChartDataPoint]
    let lineColor: Color
    let areaGradient: LinearGradient
    let yValues: (top: Double, mid: Double)
    let selectedTimeRange: Int
    let valueFormat: String
    
    @State private var selectedPoint: ChartDataPoint?
        
        var body: some View {
        Chart {
            ForEach(chartData) { point in
                // 绘制面积图
                AreaMark(
                    x: .value("Day", point.label),
                    y: .value("Value", point.value ?? 0)
                )
                .foregroundStyle(areaGradient)
                .interpolationMethod(.catmullRom)

                // 绘制线图
                LineMark(
                    x: .value("Day", point.label),
                    y: .value("Value", point.value ?? 0)
                )
                .foregroundStyle(lineColor)
                .interpolationMethod(.catmullRom)
                
                // 仅为有数据的点绘制符号
                if let value = point.value {
                    PointMark(
                        x: .value("Day", point.label),
                        y: .value("Value", value)
                    )
                    .foregroundStyle(lineColor)
                    .symbol {
                        Circle()
                                .strokeBorder(lineColor, lineWidth: 2)
                                .background(Circle().fill(Color.black))
                                .frame(width: 8, height: 8)
                        }
                }
            }
        }
        .chartYScale(domain: 0...yValues.top)
        .chartXAxis {
            AxisMarks(values: .automatic) { value in
                if let label = value.as(String.self), shouldShowLabel(label: label) {
                    AxisValueLabel(label)
                        .font(.system(size: 10))
                        .foregroundStyle(Color.gray)
                }
            }
        }
        .chartYAxis {
            AxisMarks(values: [0, yValues.mid, yValues.top]) { value in
                AxisGridLine().foregroundStyle(Color.gray.opacity(0.3))
                AxisValueLabel() {
                    if let yValue = value.as(Double.self) {
                        Text(String(format: "%.0f", yValue))
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                    }
                }
            }
        }
        .chartOverlay { proxy in
            GeometryReader { geometry in
                Rectangle().fill(.clear).contentShape(Rectangle())
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { value in
                                let origin = geometry[proxy.plotAreaFrame].origin
                                let location = value.location
                                if let label: String = proxy.value(atX: location.x - origin.x) {
                                    var minDistance: Double = .infinity
                                    var closestPoint: ChartDataPoint? = nil
                                    for point in chartData {
                                        if point.value != nil { // 只选择有数据的点
                                            let pointX = proxy.position(forX: point.label) ?? 0
                                            let distance = abs(pointX - (location.x - origin.x))
                                            if distance < minDistance && distance < 20 {
                                                minDistance = distance
                                                closestPoint = point
                                            }
                                        }
                                    }
                                    selectedPoint = closestPoint
                                }
                            }
                            .onEnded { _ in selectedPoint = nil }
                    )
            }
        }
        .chartOverlay { proxy in
            ZStack {
                if let selectedPoint, let value = selectedPoint.value {
                    let xPos = proxy.position(forX: selectedPoint.label) ?? 0
                    
//                    RuleMark(x: .value("Selected", selectedPoint.label))
//                        .foregroundStyle(Color.gray.opacity(0.5))
//                        .lineStyle(StrokeStyle(lineWidth: 1))

                    VStack(alignment: .center) {
                        Text(String(format: valueFormat, value))
                            .font(.caption)
                            .padding(6)
                            .background(Color.black.opacity(0.7))
                            .foregroundColor(.white)
                            .cornerRadius(4)
                    }
                    .position(x: xPos, y: 0)
                }
            }
        }
    }

    private func shouldShowLabel(label: String) -> Bool {
        switch selectedTimeRange {
        case 0, 2: // Week and Year, show all
            return true
        case 1: // Month, show specific labels
            let daysToShow = ["1", "7", "15", "28"]
            return daysToShow.contains(label)
        default:
            return false
        }
    }
}


// MARK: - 新的柱状图组件
struct StressBarChartView: View {
    // 使用与StressLineChartView相同的ChartDataPoint
    typealias ChartDataPoint = StressLineChartView.ChartDataPoint

    let chartData: [ChartDataPoint]
    let barColor: Color
    let yValues: (top: Double, mid: Double)
    let selectedTimeRange: Int
    let valueFormat: String
    
    @State private var selectedPoint: ChartDataPoint?

    var body: some View {
        Chart {
            ForEach(chartData) { point in
                // 修改为普通柱状图，即使值为0也显示
                BarMark(
                    x: .value("Day", point.label),
                    y: .value("Value", point.value ?? 0)
                )
                .foregroundStyle(barColor)
                .cornerRadius(4)
            }
        }
        .chartYScale(domain: 0...yValues.top)
        .chartXAxis {
            AxisMarks(values: .automatic) { value in
                if let label = value.as(String.self), shouldShowLabel(label: label) {
                    AxisValueLabel(label)
                        .font(.system(size: 10))
                        .foregroundStyle(Color.gray)
                }
            }
        }
        .chartYAxis {
            AxisMarks(values: [0, yValues.mid, yValues.top]) { value in
                AxisGridLine().foregroundStyle(Color.gray.opacity(0.3))
                AxisValueLabel() {
                    if let yValue = value.as(Double.self) {
                        Text(String(format: "%.0f", yValue))
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                    }
                }
            }
        }
        .chartOverlay { proxy in
            GeometryReader { geometry in
                Rectangle().fill(.clear).contentShape(Rectangle())
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { value in
                                let origin = geometry[proxy.plotAreaFrame].origin
                                let location = value.location
                                if let label: String = proxy.value(atX: location.x - origin.x) {
                                    var minDistance: Double = .infinity
                                    var closestPoint: ChartDataPoint? = nil
                                    for point in chartData {
                                        // 移除对 value 是否为 nil 的检查，允许选择值为 0 的点
                                        let pointX = proxy.position(forX: point.label) ?? 0
                                        let distance = abs(pointX - (location.x - origin.x))
                                        if distance < minDistance && distance < 20 {
                                            minDistance = distance
                                            closestPoint = point
                                        }
                                    }
                                    selectedPoint = closestPoint
                                }
                            }
                            .onEnded { _ in selectedPoint = nil }
                    )
            }
        }
        .chartOverlay { proxy in
            ZStack {
                if let selectedPoint = selectedPoint {
                    let xPos = proxy.position(forX: selectedPoint.label) ?? 0
                    
                    VStack(alignment: .center) {
                        Text(String(format: valueFormat, selectedPoint.value ?? 0))
                            .font(.caption)
                            .padding(6)
                            .background(Color.black.opacity(0.7))
                            .foregroundColor(.white)
                            .cornerRadius(4)
                    }
                    .position(x: xPos, y: 0)
            }
        }
        }
    }

    private func shouldShowLabel(label: String) -> Bool {
        switch selectedTimeRange {
        case 0, 2: // Week and Year, show all
            return true
        case 1: // Month, show specific labels
            let daysToShow = ["1", "7", "15", "28"]
            return daysToShow.contains(label)
        default:
            return false
    }
}
}


// MARK: - 预览
struct StressHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            StressHistoryView()
        }
        .preferredColorScheme(.dark)
    }
} 

