import Foundation
import Combine
import WindRing

/// 压力历史视图模型 - 负责处理和管理压力相关数据
@MainActor
class StressHistoryViewModel: ObservableObject {
    // MARK: - 发布属性
    @Published var stressAvgScore: Int? = nil
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    
    // 当前选中的时间范围
    @Published var currentTimeRange: Int = 0 // 0: 周, 1: 月, 2: 年
    
    // 日期范围
    @Published var dateRange = "2025.3.10-2025.3.16"
    
    // 历史压力数据
    @Published var stressScoreMax: Int = 0
    @Published var stressScoreAvg: Int = 0
    @Published var stressScoreMin: Int = 0
    @Published var stressScoreWeekData: [Int] = []
    @Published var stressScoreMonthData: [Int] = []
    @Published var stressScoreYearData: [Int] = []
    
    // 低压力时段数据
    @Published var lowStressMaxPeriod: Int = 330 // 5小时30分钟，单位分钟
    @Published var lowStressAvgPeriod: Int = 270 // 4小时30分钟，单位分钟
    @Published var lowStressMinPeriod: Int = 210 // 3小时30分钟，单位分钟
    @Published var lowStressWeekData: [Double] = []
    @Published var lowStressMonthData: [Double] = []
    @Published var lowStressYearData: [Double] = []
    
    // 过度压力时段数据
    @Published var excessiveMaxPeriod: Int = 330 // 5小时30分钟，单位分钟
    @Published var excessiveAvgPeriod: Int = 270 // 4小时30分钟，单位分钟
    @Published var excessiveMinPeriod: Int = 210 // 3小时30分钟，单位分钟
    @Published var excessiveWeekData: [Double] = []
    @Published var excessiveMonthData: [Double] = []
    @Published var excessiveYearData: [Double] = []
    
    // 夜间压力分数数据
    @Published var eveningScoreMax: Int = 0
    @Published var eveningScoreAvg: Int = 0
    @Published var eveningScoreMin: Int = 0
    @Published var eveningScoreWeekData: [Int] = []
    @Published var eveningScoreMonthData: [Int] = []
    @Published var eveningScoreYearData: [Int] = []
    
    // 时间标签数据
    @Published var weekdayLabels: [String] = []
    @Published var monthdayLabels: [String] = []
    @Published var yearmonthLabels: [String] = []
    
    // 选中数据索引
    @Published var selectedDayIndex: Int = 3 // 默认选中星期一
    
    // MARK: - 私有属性
    private let apiService = APIService.shared
    private var cancellables = Set<AnyCancellable>()
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy.M.d"
        return formatter
    }()
    
    // MARK: - 初始化
    init() {
        print("StressHistoryViewModel初始化")
    }
    
    // MARK: - 公共方法
    
    /// 加载7天平均压力分数数据
    func loadHistory7AvgStressScore() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let token = AuthService.shared.currentToken?.accessToken
                let response = try await apiService.health.getHistory7AvgStressScore(token: token)
                
                DispatchQueue.main.async {
                    self.stressAvgScore = response
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.errorMessage = "获取压力数据失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
                print("加载7天平均压力分数失败: \(error)")
            }
        }
    }
    
    /// 根据UI时间范围转换API类型 (0=周 -> 1, 1=月 -> 2, 2=年 -> 3)
    func convertTimeRangeToAPIType(_ timeRange: Int) -> Int {
        switch timeRange {
        case 0: return 1 // 周
        case 1: return 2 // 月
        case 2: return 3 // 年
        default: return 1
        }
    }

    /// 根据选择的日期和时间范围更新数据
    func updateDateRange(from date: Date, for timeRange: Int) {
        let apiType = convertTimeRangeToAPIType(timeRange)
        let (startDate, endDate) = calculateDateRange(for: timeRange, from: date)
        
        // 更新显示的日期范围
        self.dateRange = formatDateRangeForDisplay(startDate: startDate, endDate: endDate, timeRange: timeRange)
        
        // 加载数据
        loadData(startDate: startDate, endDate: endDate, for: timeRange)
    }
    
    /// 切换时间范围时加载数据
    func loadDataForTimeRange(_ timeRange: Int) {
        self.currentTimeRange = timeRange
        updateDateRange(from: Date(), for: timeRange) // 切换时默认使用当天
    }

    /// 加载所有相关数据
    private func loadData(startDate: Date, endDate: Date, for timeRange: Int) {
        self.isLoading = true
        self.errorMessage = nil
        self.currentTimeRange = timeRange

        let startString = formatDateForAPI(date: startDate)
        let endString = formatDateForAPI(date: endDate)
        let apiType = convertTimeRangeToAPIType(timeRange)
        
        Task {
            do {
                // 并行加载数据
                async let stressData = getHistoryStressScore(startDate: startString, endDate: endString, type: apiType)
                async let eveningData = getHistoryEvening(startDate: startString, endDate: endString, type: apiType)
                
                // 等待结果并更新UI
                let (stressResult, eveningResult) = try await (stressData, eveningData)
                
                // 更新压力分数
                self.stressScoreMax = stressResult.maxStress
                self.stressScoreAvg = stressResult.avgStress
                self.stressScoreMin = stressResult.minStress
                let stressScores = stressResult.records.map { $0.stress }
                
                // 更新夜间压力分数
                self.eveningScoreMax = eveningResult.maxStress
                self.eveningScoreAvg = eveningResult.avgStress
                self.eveningScoreMin = eveningResult.minStress
                let eveningScores = eveningResult.records.map { $0.stress }

                // 根据类型更新对应数据
                switch timeRange {
                case 0: // 周
                    self.stressScoreWeekData = stressScores
                    self.eveningScoreWeekData = eveningScores
                case 1: // 月
                    self.stressScoreMonthData = stressScores
                    self.eveningScoreMonthData = eveningScores
                case 2: // 年
                    self.stressScoreYearData = stressScores
                    self.eveningScoreYearData = eveningScores
                default:
                    break
                }
                
            } catch {
                self.errorMessage = "获取压力数据失败: \(error.localizedDescription)"
                print("加载压力数据失败: \(error)")
            }
            
            self.isLoading = false
        }
    }
    
    // MARK: - 数据获取方法
    
    private func getHistoryStressScore(startDate: String, endDate: String, type: Int) async throws -> StressHistoryScoreData {
        let token = AuthService.shared.currentToken?.accessToken
        return try await apiService.health.getHistoryStressScore(
            startDate: startDate,
            endDate: endDate,
            type: type,
            token: token
        )
    }
    
    private func getHistoryEvening(startDate: String, endDate: String, type: Int) async throws -> StressHistoryScoreData {
        let token = AuthService.shared.currentToken?.accessToken
        return try await apiService.health.getHistoryEvening(
            startDate: startDate,
            endDate: endDate,
            type: type,
            token: token
        )
    }

    // MARK: - 辅助方法
    
    private func calculateDateRange(for timeRange: Int, from fromDate: Date) -> (Date, Date) {
        let calendar = Calendar.current
        
        switch timeRange {
        case 0: // Week
            let weekday = calendar.component(.weekday, from: fromDate)
            let daysToSubtract = weekday - 1 // 1=周日, so subtract (1-1)=0 days for sunday
            
            guard let startOfWeek = calendar.date(byAdding: .day, value: -daysToSubtract, to: fromDate),
                  let endOfWeek = calendar.date(byAdding: .day, value: 6, to: startOfWeek) else {
                return (fromDate, fromDate)
            }
            return (startOfWeek, endOfWeek)
            
        case 1: // Month
            guard let monthInterval = calendar.dateInterval(of: .month, for: fromDate),
                  let endOfMonth = calendar.date(byAdding: .day, value: -1, to: monthInterval.end) else {
                return (fromDate, fromDate)
            }
            return (monthInterval.start, endOfMonth)
            
        case 2: // Year
            guard let yearInterval = calendar.dateInterval(of: .year, for: fromDate),
                  let endOfYear = calendar.date(byAdding: .day, value: -1, to: yearInterval.end) else {
                return (fromDate, fromDate)
            }
            return (yearInterval.start, endOfYear)
            
        default: // 月 (或默认)
                return (fromDate, fromDate)
        }
    }
    
    private func formatDateForAPI(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    private func formatDateRangeForDisplay(startDate: Date, endDate: Date, timeRange: Int) -> String {
        return "\(dateFormatter.string(from: startDate))-\(dateFormatter.string(from: endDate))"
    }
    
    /// 下拉刷新功能
    func refreshData() async {
        updateDateRange(from: Date(), for: currentTimeRange)
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
    }
} 
