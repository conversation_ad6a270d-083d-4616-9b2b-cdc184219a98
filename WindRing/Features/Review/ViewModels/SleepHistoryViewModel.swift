import Foundation
import Combine
import SwiftUI

/// 睡眠历史视图模型 - 负责处理和管理睡眠相关数据
class SleepHistoryViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var selectedTimeRange = 0 // 0: Week, 1: Month, 2: Year
    @Published var dateRange = "0000.0.00-0000.00.00"
    @Published var selectedDate = Date()
    @Published var isLoading = false
    @Published var errorMessage: String? = nil
    @Published var hasError = false
    @Published var isRefreshing = false
    @Published var xAxisCount: Int = 7
    @Published var showingDatePicker = false
    
    // Sleep Score Data
    @Published var sleepScoreMax = 48
    @Published var sleepScoreAvg = 47
    @Published var sleepScoreMin = 46
    @Published var sleepScoreWeekData = [42, 44, 46, 49, 47, 45, 43]
    @Published var sleepScoreMonthData = [45, 47, 49, 46, 44]
    @Published var sleepScoreYearData = [46, 48, 47, 45, 44, 46, 48, 49, 47, 45, 44, 46]
    
    // Sleep Efficiency Data
    @Published var sleepEfficiencyMax = 83
    @Published var sleepEfficiencyAvg = 70
    @Published var sleepEfficiencyMin = 56
    @Published var sleepEfficiencyWeekData = [60, 65, 58, 70, 68, 64, 62]
    @Published var sleepEfficiencyMonthData = [63, 68, 72, 65, 61]
    @Published var sleepEfficiencyYearData = [61, 64, 67, 70, 73, 68, 65, 62, 67, 70, 66, 63]
    
    // Sleep Time Data
    @Published var sleepTimeMaxHours = 0
    @Published var sleepTimeMaxMins = 0
    @Published var sleepTimeAvgHours = 0
    @Published var sleepTimeAvgMins = 0
    @Published var sleepTimeMinHours = 0
    @Published var sleepTimeMinMins = 0
    @Published var currentSleepTimeData: [Int] = []
    
    // Sleep Heart Rate Data
    @Published var sleepHeartRateModel = SleepHistoryHeartRateData(maxHeartRate: 0, avgHeartRate: 0, minHeartRate: 0, records: [])
    
    // Sleep HRV Data
    @Published var sleepHRVModel = SleepHistoryHRVData(maxHRV: 0, avgHRV: 0, minHRV: 0, records: [])
    @Published var currentSleepHRVData: [Int] = []
    
    // Sleep SpO2 Data
    @Published var sleepSpO2Model = SleepHistorySpO2Data(maxSpO2: 0, avgSpO2: 0, minSpO2: 0, records: [])
    @Published var currentSleepSpO2Data: [Int] = []
    
    // Skin Temperature Data
    @Published var skinTempAvg: Double = 0.0
    @Published var skinTempRecords: [SleepHistorySkinTempRecord] = []
    @Published var skinTempSuccess: Bool = false
    @Published var skinTempError: Bool = false
    
    @Published var spo2Max: Int = 0
    @Published var spo2Avg: Int = 0
    @Published var spo2Min: Int = 0
    @Published private var spo2Data: [Int] = []
    // MARK: - 发布属性
    @Published var sleepAvgTime: SleepAvgTimeData? = nil
//    @Published var isLoading: Bool = false
//    @Published var errorMessage: String? = nil
    
    // MARK: - 私有属性
    private let apiService = APIService.shared
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var currentSleepScoreData: [Int] {
        switch selectedTimeRange {
        case 1: return sleepScoreMonthData
        case 2: return sleepScoreYearData
        default: return sleepScoreWeekData
        }
    }
    
    var currentSleepEfficiencyData: [Int] {
        switch selectedTimeRange {
        case 1: return sleepEfficiencyMonthData
        case 2: return sleepEfficiencyYearData
        default: return sleepEfficiencyWeekData
        }
    }
    
    var timeRanges: [String] {
        ["sleep_history_time_range_week".localized, "sleep_history_time_range_month".localized, "sleep_history_time_range_year".localized]
    }
    
    var weekdays: [String] {
        ["sleep_history_weekday_sun", "sleep_history_weekday_mon", "sleep_history_weekday_tue", "sleep_history_weekday_wed", "sleep_history_weekday_thu", "sleep_history_weekday_fri", "sleep_history_weekday_sat"].map { $0.localized }
    }
    
    var monthDays: [String] {
        ["sleep_history_month_w1", "sleep_history_month_w2", "sleep_history_month_w3", "sleep_history_month_w4", "sleep_history_month_w5"].map { $0.localized }
    }
    
    var yearMonths: [String] {
        ["sleep_history_year_jan", "sleep_history_year_feb", "sleep_history_year_mar", "sleep_history_year_apr", "sleep_history_year_may", "sleep_history_year_jun", "sleep_history_year_jul", "sleep_history_year_aug", "sleep_history_year_sep", "sleep_history_year_oct", "sleep_history_year_nov", "sleep_history_year_dec"].map { $0.localized }
    }
    
    var currentTimeLabels: [String] {
        switch selectedTimeRange {
        case 1: return monthDays
        case 2: return yearMonths
        default: return weekdays
        }
    }
    // 动态X轴标签
    var xAxisLabels: [String] {
        let calendar = Calendar.current
        
        switch selectedTimeRange {
        case 0: // 周
            return ["sleep_history_weekday_sun".localized, "sleep_history_weekday_mon".localized, "sleep_history_weekday_tue".localized, "sleep_history_weekday_wed".localized, "sleep_history_weekday_thu".localized, "sleep_history_weekday_fri".localized, "sleep_history_weekday_sat".localized]

        case 1: // 月
            let components = self.dateRange.split(separator: "-").map { String($0) }
            guard components.count == 2 else { return [] }
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy.M.d"
            guard let startDate = dateFormatter.date(from: components[0]) else { return [] }
            guard let daysInMonth = calendar.range(of: .day, in: .month, for: startDate)?.count else { return [] }
            return (1...daysInMonth).map { String($0) }

        case 2: // 年
            return yearMonths//DateFormatter().shortMonthSymbols
            
        default:
            return []
        }
    }
    
    // MARK: - Methods
    func updateDisplayRangeAndFetchData() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy.M.d"
        
        let calendar = Calendar.current
        
        switch selectedTimeRange {
        case 1: // Month
            if let startOfMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: selectedDate)),
               let endOfMonth = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: startOfMonth) {
                dateRange = "\(formatter.string(from: startOfMonth))-\(formatter.string(from: endOfMonth))"
                if let monthRange = calendar.range(of: .day, in: .month, for: selectedDate) {
                    xAxisCount = monthRange.count
                }
            }
        case 2: // Year
            if let startOfYear = calendar.date(from: calendar.dateComponents([.year], from: selectedDate)),
               let endOfYear = calendar.date(from: DateComponents(year: calendar.component(.year, from: selectedDate), month: 12, day: 31)) {
                dateRange = "\(formatter.string(from: startOfYear))-\(formatter.string(from: endOfYear))"
                xAxisCount = 12
            }
        default: // Week
            guard let weekInterval = calendar.dateInterval(of: .weekOfYear, for: selectedDate) else {
                xAxisCount = 7
                return
            }
            let startOfWeek = weekInterval.start
            let endOfWeek = calendar.date(byAdding: .day, value: 6, to: startOfWeek)!
            dateRange = "\(formatter.string(from: startOfWeek))-\(formatter.string(from: endOfWeek))"
            xAxisCount = 7
        }
        
        updateDataForNewDateRange()
    }
    
    func refreshData() async {
        isRefreshing = true
        
        let parts = dateRange.split(separator: "-")
        guard parts.count == 2 else {
            isRefreshing = false
            return
        }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy.M.d"
        let apiFormatter = DateFormatter()
        apiFormatter.dateFormat = "yyyy-MM-dd"
        
        guard let startDate = formatter.date(from: String(parts[0])),
              let endDate = formatter.date(from: String(parts[1])) else {
            isRefreshing = false
            return
        }
        
        let startDateStr = apiFormatter.string(from: startDate)
        let endDateStr = apiFormatter.string(from: endDate)
        let typeParam = selectedTimeRange + 1
        
        await fetchAllData(startDate: startDateStr, endDate: endDateStr, type: typeParam, forceRefresh: true)
        
        isRefreshing = false
    }
    
    private func updateDataForNewDateRange() {
        isLoading = true
        errorMessage = nil
        hasError = false
        
        let parts = dateRange.split(separator: "-")
        guard parts.count == 2 else {
            return
        }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy.M.d"
        let apiFormatter = DateFormatter()
        apiFormatter.dateFormat = "yyyy-MM-dd"
        
        guard let startDate = formatter.date(from: String(parts[0])),
              let endDate = formatter.date(from: String(parts[1])) else {
            return
        }
        
        let startDateStr = apiFormatter.string(from: startDate)
        let endDateStr = apiFormatter.string(from: endDate)
        let typeParam = selectedTimeRange + 1
        
        Task {
            await fetchAllData(startDate: startDateStr, endDate: endDateStr, type: typeParam, forceRefresh: false)
            isLoading = false
        }
    }
    
    private func fetchAllData(startDate: String, endDate: String, type: Int, forceRefresh: Bool) async {
        await withCheckedContinuation { continuation in
        let group = DispatchGroup()
        
        var scoreSuccess = false
        var efficiencySuccess = false
        var timeAsleepSuccess = false
        var heartRateSuccess = false
        var hrvSuccess = false
        var spO2Success = false
        var skinTempSuccess = false
        
        // Fetch Sleep Score Data
        group.enter()
        SleepHistoryAPIService.shared.getSleepHistoryScore(startDate: startDate, endDate: endDate, type: type, forceRefresh: forceRefresh) { [weak self] result in
            defer { group.leave() }
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                switch result {
                case .success(let data):
                    scoreSuccess = true
                    self.updateSleepScoreData(with: data)
                case .failure:
                    scoreSuccess = false
                }
            }
        }
        
        // 强制刷新睡眠效率数据
        group.enter()
        SleepHistoryAPIService.shared.getSleepHistoryEfficiency(startDate: startDate, endDate: endDate, type: type, forceRefresh: true) { [weak self] result in
            defer { group.leave() }
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let data):
                    efficiencySuccess = true
                    // 在主线程外暂存数据
                    //                DispatchQueue.main.async {
                    self.updateSleepEfficiencyData(with: data)
                    //                }
                    
                case .failure(_):
                    efficiencySuccess = false
                }
            }
        }
        
        // 强制刷新睡眠时长数据
        group.enter()
        SleepHistoryAPIService.shared.getSleepTimeAsleep(startDate: startDate, endDate: endDate, type: type, forceRefresh: true) { [weak self] result in
            defer { group.leave() }
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let data):
                    timeAsleepSuccess = true
                    // 在主线程外暂存数据
                    //                DispatchQueue.main.async {
                    self.updateSleepTimeAsleepData(with: data)
                    //                }
                    
                case .failure(_):
                    timeAsleepSuccess = false
                }
            }
        }
        
        // 调用API获取睡眠心率数据，不强制刷新，优先使用缓存
        group.enter()
        SleepHistoryAPIService.shared.getSleepHeartRate(startDate: startDate, endDate: endDate, type: type, forceRefresh: false) { [weak self] result in
            defer { group.leave() }
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let data):
                    heartRateSuccess = true
                    // 在主线程外暂存数据
                    //                DispatchQueue.main.async {
                    var processedData = data
                    let sortedRecords = processedData.records.sorted { record1, record2 in
                        (TimeInterval(record1.time) ?? 0) < (TimeInterval(record2.time) ?? 0)
                    }
                    
                    let finalRecords = sortedRecords.enumerated().map { (index, record) -> SleepHistoryHeartRateRecord in
                        var mutableRecord = record
                        mutableRecord.id = index
                        return mutableRecord
                    }
                    processedData.records = finalRecords
                    self.sleepHeartRateModel = processedData
                    //                }
                    
                case .failure(_):
                    heartRateSuccess = false
                }
            }
        }
        
        // 调用API获取睡眠HRV数据，不强制刷新，优先使用缓存
        group.enter()
        SleepHistoryAPIService.shared.getSleepHRV(startDate: startDate, endDate: endDate, type: type, forceRefresh: false) { [weak self] result in
            defer { group.leave() }
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let data):
                    hrvSuccess = true
                    // 在主线程外暂存数据
                    //                DispatchQueue.main.async {
                    self.sleepHRVModel = data
                    //                }
                    
                case .failure(_):
                    hrvSuccess = false
                }
            }
        }
        
        // 调用API获取睡眠SpO2数据，不强制刷新，优先使用缓存
        group.enter()
        SleepHistoryAPIService.shared.getSleepSpO2(startDate: startDate, endDate: endDate, type: type, forceRefresh: false) { [weak self] result in
            defer { group.leave() }
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let data):
                    spO2Success = true
                    // 在主线程外暂存数据
                    //                DispatchQueue.main.async {
                    self.sleepSpO2Model = data
                    self.updateSleepSpO2Data(with: data)
                    //                }
                    
                case .failure(_):
                    spO2Success = false
                }
            }
        }
        
        // 获取睡眠皮肤温度
        group.enter()
        SleepHistoryAPIService.shared.getSleepSkinTemp(startDate: startDate, endDate: endDate, type: type, forceRefresh: true) {[weak self] result in
            defer { group.leave() }
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let data):
                    skinTempSuccess = true
                    // 在主线程外暂存数据
                    //                DispatchQueue.main.async {
                    self.updateSleepSkinTempData(with: data)
                    //                }
                    
                case .failure(_):
                    skinTempSuccess = false
                }
            }
        }
        // Add similar calls for other data types...
        
//        await withCheckedContinuation { continuation in
            group.notify(queue: .main) { [weak self] in
                guard let self = self else { return }
                
                if !scoreSuccess && !efficiencySuccess && !timeAsleepSuccess && !heartRateSuccess && !hrvSuccess && !spO2Success && !skinTempSuccess {
                    self.errorMessage = "sleep_history_data_refresh_failed".localized
                    self.hasError = true
                } else {
                    let failedApis: [String] = []
                    // Add failed APIs to the list...
                    
                    if !failedApis.isEmpty {
                        self.errorMessage = String(format: "sleep_history_load_failed_multiple".localized, failedApis.joined(separator: "、"))
                        self.hasError = true
                    } else {
                        self.hasError = false
                        self.errorMessage = nil
                    }
                }
                
                continuation.resume(returning: ())
            }
            
        }
    }
    
    private func updateSleepScoreData(with data: SleepHistoryScoreData) {
        sleepScoreMax = data.maxScore
        sleepScoreAvg = data.avgScore
        sleepScoreMin = data.minScore
        
        var newScoreData: [Int] = []
        for record in data.records {
            newScoreData.append(record.score)
        }
        
        switch selectedTimeRange {
        case 1:
            sleepScoreMonthData = newScoreData
        case 2:
            sleepScoreYearData = newScoreData
        default:
            sleepScoreWeekData = newScoreData
        }
    }
    /// 加载7天平均睡眠时间数据
    func loadHistory7AvgSleepTime() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let token = AuthService.shared.currentToken?.accessToken
                let response = try await apiService.health.getHistory7AvgSleepTime(token: token)
                
                DispatchQueue.main.async {
                    let hours = response / 60
                    let minutes = response % 60

                    if hours > 0 && minutes > 0 {
//                        return "\(response) hr \(minutes) min"
            //            return "\(hours)小时\(minutes)分钟"
                    } else if hours > 0 {
//                        return "\(hours) hr"
            //            return "\(hours)小时"
                    } else {
//                        return "\(hours) min"
            //            return "\(minutes)分钟"
                    }
                    let data = SleepAvgTimeData.init(hours: hours,minutes: minutes)
                    self.sleepAvgTime = data//.data
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.errorMessage = "获取睡眠数据失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
                print("加载7天平均睡眠时间失败: \(error)")
            }
        }
    }
    // 使用API数据更新睡眠效率
    private func updateSleepEfficiencyData(with data: SleepHistoryEfficiencyData) {
        // 更新最大、平均和最小值
        sleepEfficiencyMax = data.maxEfficiency
        sleepEfficiencyAvg = data.avgEfficiency
        sleepEfficiencyMin = data.minEfficiency
        
        // 提取并映射记录数据到图表数据
        var newEfficiencyData: [Int] = []
        
        // 根据时间范围处理不同数量的记录
        for record in data.records {
            newEfficiencyData.append(record.efficiency)
        }
        
        // 根据选定的时间范围更新对应的数据数组
        switch selectedTimeRange {
        case 1: // 月视图
            sleepEfficiencyMonthData = newEfficiencyData
        case 2: // 年视图
            sleepEfficiencyYearData = newEfficiencyData
        default: // 周视图
            sleepEfficiencyWeekData = newEfficiencyData
        }
    }
    
    // 使用API数据更新睡眠时长
    private func updateSleepTimeAsleepData(with data: SleepHistoryTimeAsleepData) {
        // 更新最大、平均和最小值（以分钟为单位的数据转换为小时和分钟格式）
        sleepTimeMaxHours = data.maxTimeAsleep / 60
        sleepTimeMaxMins = data.maxTimeAsleep % 60
        
        sleepTimeAvgHours = data.avgTimeAsleep / 60
        sleepTimeAvgMins = data.avgTimeAsleep % 60
        
        sleepTimeMinHours = data.minTimeAsleep / 60
        sleepTimeMinMins = data.minTimeAsleep % 60
        
        // 提取并映射记录数据到图表数据（用于曲线图）
        var sleepTimeData: [Int] = []
        
        // 根据时间范围处理不同数量的记录
        for record in data.records {
            sleepTimeData.append(record.timeAsleep)
        }
        
        // 保存睡眠时长数据到对应的数据结构
        currentSleepTimeData = sleepTimeData
    }
    
    // 睡眠时长数据（用于曲线图）
//    @State private var currentSleepTimeData: [Int] = []
    
    // 随机生成睡眠得分数据（API失败时使用）
    private func generateRandomScoreData() {
        // 睡眠得分数据
        if selectedTimeRange == 0 {
            sleepScoreWeekData = sleepScoreWeekData.map { max(40, min(50, $0 + Int.random(in: -5...5))) }
        } else if selectedTimeRange == 1 {
            sleepScoreMonthData = sleepScoreMonthData.map { max(40, min(50, $0 + Int.random(in: -5...5))) }
        } else {
            sleepScoreYearData = sleepScoreYearData.map { max(40, min(50, $0 + Int.random(in: -5...5))) }
        }
        
        // 更新最大、平均和最小值
        if let max = currentSleepScoreData.max(), let min = currentSleepScoreData.min() {
            sleepScoreMax = max
            sleepScoreMin = min
            sleepScoreAvg = currentSleepScoreData.reduce(0, +) / currentSleepScoreData.count
        }
    }
    
    // 随机生成睡眠效率数据（API失败时使用）
    private func generateRandomEfficiencyData() {
        // 睡眠效率数据
        if selectedTimeRange == 0 {
            sleepEfficiencyWeekData = sleepEfficiencyWeekData.map { max(50, min(85, $0 + Int.random(in: -10...10))) }
        } else if selectedTimeRange == 1 {
            sleepEfficiencyMonthData = sleepEfficiencyMonthData.map { max(50, min(85, $0 + Int.random(in: -10...10))) }
        } else {
            sleepEfficiencyYearData = sleepEfficiencyYearData.map { max(50, min(85, $0 + Int.random(in: -10...10))) }
        }
        
        // 更新最大、平均和最小值
        if let max = currentSleepEfficiencyData.max(), let min = currentSleepEfficiencyData.min() {
            sleepEfficiencyMax = max
            sleepEfficiencyMin = min
            sleepEfficiencyAvg = currentSleepEfficiencyData.reduce(0, +) / currentSleepEfficiencyData.count
        }
    }
    
    // 随机生成睡眠时长数据（API失败时使用）
    private func generateRandomTimeAsleepData() {
        // 随机生成睡眠时长数据（分钟）
        let minMinutes = 90  // 1.5小时
        let maxMinutes = 540 // 9小时
        
        // 生成7个随机睡眠时长
        currentSleepTimeData = (0..<7).map { _ in Int.random(in: minMinutes...maxMinutes) }
        
        // 更新最大、平均和最小值
        if let maxTime = currentSleepTimeData.max(),
           let minTime = currentSleepTimeData.min() {
            sleepTimeMaxHours = maxTime / 60
            sleepTimeMaxMins = maxTime % 60
            
            let avgTime = currentSleepTimeData.reduce(0, +) / currentSleepTimeData.count
            sleepTimeAvgHours = avgTime / 60
            sleepTimeAvgMins = avgTime % 60
            
            sleepTimeMinHours = minTime / 60
            sleepTimeMinMins = minTime % 60
        } else {
            // 如果没有数据，设置默认值
            sleepTimeMaxHours = 0
            sleepTimeMaxMins = 0
            sleepTimeAvgHours = 0
            sleepTimeAvgMins = 0
            sleepTimeMinHours = 0
            sleepTimeMinMins = 0
        }
    }
    // 更新睡眠皮肤温度数据
    private func updateSleepSkinTempData(with data: SleepHistorySkinTempData) {
        // 更新平均值
        skinTempAvg = data.avgSkinTemp
        skinTempRecords = data.records
    }
    
    // 更新睡眠SpO2数据
    private func updateSleepSpO2Data(with data: SleepHistorySpO2Data) {
        // 更新最大、平均和最小值
        spo2Max = data.maxSpO2
        spo2Avg = data.avgSpO2
        spo2Min = data.minSpO2
        
        // 提取并映射记录数据到图表数据
        var spo2Data: [Int] = []
        
        // 根据时间范围处理不同数量的记录
        for record in data.records {
            spo2Data.append(record.spO2)
        }
        
        // 保存睡眠SpO2数据到对应的数据结构
        currentSleepSpO2Data = spo2Data
        
//        // 根据选定的时间范围更新对应的数据数组
//        switch selectedTimeRange {
//        case 1: // 月视图
//            spo2MonthData = spo2Data
//        case 2: // 年视图
//            spo2YearData = spo2Data
//        default: // 周视图
//            spo2WeekData = spo2Data
//        }
    }
}
