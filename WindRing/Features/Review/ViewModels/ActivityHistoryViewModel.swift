import Foundation
import Combine
import SwiftUI

@MainActor
class ActivityHistoryViewModel: ObservableObject {
    // MARK: - 发布的属性
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // 活动得分数据
    @Published var activityAvgScore = ActivityAvgScore(score: 0)
    @Published var activityScoreMax = 0
    @Published var activityScoreAvg = 0
    @Published var activityScoreMin = 0
    @Published var activityScoreWeekData: [Int] = []
    @Published var activityScoreMonthData: [Int] = []
    @Published var activityScoreYearData: [Int] = []
    
    // 步数数据
    @Published var stepsMax = 0
    @Published var stepsAvg = 0
    @Published var stepsMin = 0
    @Published var stepsWeekData: [Int] = []
    @Published var stepsMonthData: [Int] = []
    @Published var stepsYearData: [Int] = []
    
    // 卡路里数据
    @Published var caloriesMax = 0
    @Published var caloriesAvg = 0
    @Published var caloriesMin = 0
    @Published var caloriesWeekData: [Int] = []
    @Published var caloriesMonthData: [Int] = []
    @Published var caloriesYearData: [Int] = []
    
    // 站立时长数据
    @Published var standingDurationMaxHours = 0
    @Published var standingDurationMaxMinutes = 0
    @Published var standingDurationAvgHours = 0
    @Published var standingDurationAvgMinutes = 0
    @Published var standingDurationMinHours = 0
    @Published var standingDurationMinMinutes = 0
    @Published var standingDurationWeekData: [Double] = []
    @Published var standingDurationMonthData: [Double] = []
    @Published var standingDurationYearData: [Double] = []
    
    // 日期范围
    @Published var dateRange = "2025.3.10-2025.3.16"
    @Published var currentTimeRange: Int = 0 // 0: 周, 1: 月, 2: 年
    
    // MARK: - 私有属性
    private let activityService = ActivityHistoryService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // 日期格式化
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy.M.d"
        return formatter
    }()
    
    // MARK: - 初始化方法
    init() {
        // 默认加载周数据
        loadDataForTimeRange(0) // 0 = 周数据
    }

    /// 根据UI时间范围转换API类型 (0=周 -> 1, 1=月 -> 2, 2=年 -> 3)
    func convertTimeRangeToAPIType(_ timeRange: Int) -> Int {
        switch timeRange {
        case 0: return 1 // 周
        case 1: return 2 // 月
        case 2: return 3 // 年
        default: return 1
        }
    }
    
    /// 根据选择的日期和时间范围更新数据
    func updateDateRange(from date: Date, for timeRange: Int) {
        let (startDate, endDate) = calculateDateRange(for: timeRange, from: date)
        
        // 更新显示的日期范围
        self.dateRange = formatDateRangeForDisplay(startDate: startDate, endDate: endDate, timeRange: timeRange)
        
        // 加载数据
        loadAllData(startDate: startDate, endDate: endDate, for: timeRange)
    }
    
    /// 切换时间范围时加载数据
    func loadDataForTimeRange(_ timeRange: Int) {
        self.currentTimeRange = timeRange
        updateDateRange(from: Date(), for: timeRange) // 切换时默认使用当天
    }

    /// 加载所有相关数据
    private func loadAllData(startDate: Date, endDate: Date, for timeRange: Int) {
        isLoading = true
        errorMessage = nil
        currentTimeRange = timeRange

        let startString = formatDateForAPI(date: startDate)
        let endString = formatDateForAPI(date: endDate)
        let apiType = convertTimeRangeToAPIType(timeRange)
        
        Task {
            do {
                // 并行加载所有数据
                async let scoreData = getHistoryScore(startDate: startString, endDate: endString, type: apiType)
                async let stepsData = getHistorySteps(startDate: startString, endDate: endString, type: apiType)
                async let caloriesData = getHistoryCalories(startDate: startString, endDate: endString, type: apiType)
                async let standingData = getHistoryStandingDuration(startDate: startString, endDate: endString, type: apiType)
                
                let (scoreResult, stepsResult, caloriesResult, standingResult) = try await (scoreData, stepsData, caloriesData, standingData)
                
                // 更新UI
                updateActivityScore(with: scoreResult, for: timeRange)
                updateSteps(with: stepsResult, for: timeRange)
                updateCalories(with: caloriesResult, for: timeRange)
                updateStandingDuration(with: standingResult, for: timeRange)
                
            } catch {
                errorMessage = "Failed to load activity data: \(error.localizedDescription)"
                print("Error loading activity data: \(error)")
            }
            
            isLoading = false
        }
    }
    
    // MARK: - 数据更新方法
    private func updateActivityScore(with data: ActivityScoreHistoryData, for timeRange: Int) {
        activityScoreMax = data.maxScore
        activityScoreAvg = data.avgScore
        activityScoreMin = data.minScore
        let scores = data.records.map { $0.score }
        
        switch timeRange {
        case 0: activityScoreWeekData = scores
        case 1: activityScoreMonthData = scores
        case 2: activityScoreYearData = scores
        default: break
        }
    }
    
    private func updateSteps(with data: ActivityStepsHistoryData, for timeRange: Int) {
        stepsMax = data.maxSteps
        stepsAvg = data.avgSteps
        stepsMin = data.minSteps
        let steps = data.records.map { $0.steps }
        
        switch timeRange {
        case 0: stepsWeekData = steps
        case 1: stepsMonthData = steps
        case 2: stepsYearData = steps
        default: break
        }
    }
    
    private func updateCalories(with data: ActivityCaloriesHistoryData, for timeRange: Int) {
        caloriesMax = data.maxCalories
        caloriesAvg = data.avgCalories
        caloriesMin = data.minCalories
        let calories = data.records.map { $0.calories }
        
        switch timeRange {
        case 0: caloriesWeekData = calories
        case 1: caloriesMonthData = calories
        case 2: caloriesYearData = calories
        default: break
        }
    }
    
    private func updateStandingDuration(with data: ActivityStandingDurationHistoryData, for timeRange: Int) {
        standingDurationMaxHours = data.maxDuration / 60
        standingDurationMaxMinutes = data.maxDuration % 60
        standingDurationAvgHours = data.avgDuration / 60
        standingDurationAvgMinutes = data.avgDuration % 60
        standingDurationMinHours = data.minDuration / 60
        standingDurationMinMinutes = data.minDuration % 60
        let durations = data.records.map { Double($0.duration) / 60.0 }
        
        switch timeRange {
        case 0: standingDurationWeekData = durations
        case 1: standingDurationMonthData = durations
        case 2: standingDurationYearData = durations
        default: break
        }
    }

    // MARK: - 数据获取方法 (async/await)
    private func getHistoryScore(startDate: String, endDate: String, type: Int) async throws -> ActivityScoreHistoryData {
        return try await withCheckedThrowingContinuation { continuation in
            activityService.getHistoryScore(startDate: startDate, endDate: endDate, type: type)
                .sink(receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        continuation.resume(throwing: error)
                    }
                }, receiveValue: { data in
                    continuation.resume(returning: data)
                })
                .store(in: &cancellables)
        }
    }
    
    private func getHistorySteps(startDate: String, endDate: String, type: Int) async throws -> ActivityStepsHistoryData {
        return try await withCheckedThrowingContinuation { continuation in
            activityService.getHistorySteps(startDate: startDate, endDate: endDate, type: type)
                .sink(receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        continuation.resume(throwing: error)
                    }
                }, receiveValue: { data in
                    continuation.resume(returning: data)
                })
                .store(in: &cancellables)
        }
    }
    
    private func getHistoryCalories(startDate: String, endDate: String, type: Int) async throws -> ActivityCaloriesHistoryData {
        return try await withCheckedThrowingContinuation { continuation in
            activityService.getHistoryCalories(startDate: startDate, endDate: endDate, type: type)
                .sink(receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        continuation.resume(throwing: error)
                    }
                }, receiveValue: { data in
                    continuation.resume(returning: data)
                })
                .store(in: &cancellables)
        }
    }
    
    private func getHistoryStandingDuration(startDate: String, endDate: String, type: Int) async throws -> ActivityStandingDurationHistoryData {
        return try await withCheckedThrowingContinuation { continuation in
            activityService.getHistoryStandingDuration(startDate: startDate, endDate: endDate, type: type)
                .sink(receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        continuation.resume(throwing: error)
                    }
                }, receiveValue: { data in
                    continuation.resume(returning: data)
                })
                .store(in: &cancellables)
        }
    }
    
    func getHistory7AvgScore() {
//        /app-api/iot/activity/getHistory7AvgScore
        activityService.getHistory7AvgScore()
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completion {
                        self?.errorMessage = error.localizedDescription
                        print("Error loading activity score: \(error)")
                    }
                },
                receiveValue: { [weak self] data in
                    guard let self = self else { return }
                    
                    // 提取分数数据
                    self.activityAvgScore = data
           
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 辅助方法
    private func calculateDateRange(for timeRange: Int, from fromDate: Date) -> (Date, Date) {
        let calendar = Calendar.current
        
        switch timeRange {
        case 0: // Week
            let weekday = calendar.component(.weekday, from: fromDate)
            let daysToSubtract = weekday - 1 // 1=周日, so subtract (1-1)=0 days for sunday
            
            guard let startOfWeek = calendar.date(byAdding: .day, value: -daysToSubtract, to: fromDate),
                  let endOfWeek = calendar.date(byAdding: .day, value: 6, to: startOfWeek) else {
                return (fromDate, fromDate)
            }
            return (startOfWeek, endOfWeek)
            
        case 1: // Month
            guard let monthInterval = calendar.dateInterval(of: .month, for: fromDate),
                  let endOfMonth = calendar.date(byAdding: .day, value: -1, to: monthInterval.end) else {
                return (fromDate, fromDate)
            }
            return (monthInterval.start, endOfMonth)
            
        case 2: // Year
            guard let yearInterval = calendar.dateInterval(of: .year, for: fromDate),
                  let endOfYear = calendar.date(byAdding: .day, value: -1, to: yearInterval.end) else {
                return (fromDate, fromDate)
            }
            return (yearInterval.start, endOfYear)
            
        default:
            return (fromDate, fromDate)
        }
    }
    
    private func formatDateForAPI(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    private func formatDateRangeForDisplay(startDate: Date, endDate: Date, timeRange: Int) -> String {
            return "\(dateFormatter.string(from: startDate))-\(dateFormatter.string(from: endDate))"
    }
    
    /// 下拉刷新功能
    func refreshData() async {
        updateDateRange(from: Date(), for: currentTimeRange)
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
    }
} 
