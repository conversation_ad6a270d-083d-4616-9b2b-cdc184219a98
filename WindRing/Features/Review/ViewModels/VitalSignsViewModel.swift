import Foundation
import Combine

/// 生命体征历史视图模型 - 负责处理和管理生命体征历史数据
class VitalSignsHistoryViewModel: ObservableObject {
    // MARK: - 发布属性
    @Published var heartRateAvg: Int? = nil
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    
    @Published var heartRateHistoryData: HeartRateHistoryData?
    // 心率历史数据
    @Published var heartRateMaxHours: Int = 3
    @Published var heartRateMaxMins: Int = 48
    @Published var heartRateAvgHours: Int = 2
    @Published var heartRateAvgMins: Int = 39
    @Published var heartRateMinHours: Int = 1
    @Published var heartRateMinMins: Int = 30
    @Published var heartRateWeekData: [(min:Double, max:Double)] = []
    @Published var heartRateMonthData: [(min:Double, max:Double)] = []
    @Published var heartRateYearData: [(min:Double, max:Double)] = []
    
    // 血氧历史数据
    @Published var spo2Max: Int = 98
    @Published var spo2Avg: Int = 96
    @Published var spo2Min: Int = 91
    @Published var spo2WeekData: [(min:Double, max:Double)] = []
    @Published var spo2MonthData: [(min:Double, max:Double)] = []
    @Published var spo2YearData: [(min:Double, max:Double)] = []
    
    // HRV历史数据
    @Published var hrvMax: Int = 92
    @Published var hrvAvg: Int = 42
    @Published var hrvMin: Int = 17
    @Published var hrvWeekData: [(min:Double, max:Double)] = []
    @Published var hrvMonthData: [(min:Double, max:Double)] = []
    @Published var hrvYearData: [(min:Double, max:Double)] = []
    
    // MARK: - 私有属性
    private let apiService = APIService.shared
    private var cancellables = Set<AnyCancellable>()
    private var activeTaskCount = 0 // 跟踪活动任务数量
    
    // MARK: - 初始化
    init() {
        print("VitalSignsHistoryViewModel初始化")
    }
    
    // MARK: - 公共方法
    
    /// 加载7天平均心率数据
    func loadHistory7AvgHeartRate() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let token = AuthService.shared.currentToken?.accessToken
                let response = try await apiService.health.getHistory7AvgHeartRate(token: token)
                
                DispatchQueue.main.async {
                    self.heartRateAvg = response
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.errorMessage = "获取心率数据失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
                print("加载7天平均心率失败: \(error)")
            }
        }
    }
    
    /// 获取指定日期范围的心率历史数据
    /// - Parameters:
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    func loadHeartRateHistoryData(startDate: String, endDate: String, type: Int) {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                // 添加重试机制
                let maxRetries = 2
                var currentRetry = 0
                var lastError: Error? = nil
                
                while currentRetry <= maxRetries {
                    do {
                        let token = AuthService.shared.currentToken?.accessToken
                        let response = try await apiService.health.getHistoryVitalSignsHeartRate(
                            startDate: startDate,
                            endDate: endDate,
                            type: type,
                            token: token
                        )
                        
//                        if let data = response {
                            // 转换最大值、最小值和平均值
                            let (maxHours, maxMins) = convertToHoursAndMinutes(response.maxHeartRate)
                            let (avgHours, avgMins) = convertToHoursAndMinutes(response.avgHeartRate)
                            let (minHours, minMins) = convertToHoursAndMinutes(response.minHeartRate)
                            
                            // 准备图表数据
//                            let chartData = response.records//.map { Double($0.heartRate) }
                            let chartData: [(min:Double, max:Double)] = response.records.map { record in
                                let value: (min:Double, max:Double) = (min: record.minHeartRate.double, max: record.maxHeartRate.double)//(min = record.minHeartRate, max: record.maxHeartRate)
                                return value
                            }
                            DispatchQueue.main.async {
                                self.heartRateHistoryData = response
                                // 更新心率时间数据
                                self.heartRateMaxHours = maxHours
                                self.heartRateMaxMins = maxMins
                                self.heartRateAvgHours = avgHours
                                self.heartRateAvgMins = avgMins
                                self.heartRateMinHours = minHours
                                self.heartRateMinMins = minMins
                                
                                // 根据类型更新图表数据
                                switch type {
                                case 1: // 周
                                    self.heartRateWeekData = chartData
                                case 2: // 月
                                    self.heartRateMonthData = chartData
                                case 3: // 年
                                    self.heartRateYearData = chartData
                                default:
                                    break
                                }
                                
                                // 通知视图更新 - 使用任务计数逻辑替代直接设置isLoading
                                self.taskCompleted()
                            }
                            // 请求成功，跳出重试循环
                            break
//                        } else {
//                            throw NetworkError.invalidResponse
//                        }
                    } catch {
                        lastError = error
                        currentRetry += 1
                        
                        // 如果是最后一次重试失败或是致命错误，则不再重试
                        if currentRetry > maxRetries {
                            throw error
                        }
                        
                        // 等待一段时间后重试
                        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                        print("重试获取心率历史数据 (第\(currentRetry)次)")
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    // 提供更友好的错误信息
                    if let networkError = error as? NetworkError {
                        switch networkError {
                        case .networkError:
                            self.errorMessage = "网络连接失败，请检查网络设置"
                        case .unauthorized:
                            self.errorMessage = "认证失败，请重新登录"
                        case .serverError(_, let message):
                            self.errorMessage = "服务器错误: \(message)"
                        default:
                            self.errorMessage = "获取心率历史数据失败: \(networkError.localizedDescription)"
                        }
                    } else {
                        self.errorMessage = "获取心率历史数据失败: \(error.localizedDescription)"
                    }
                    self.isLoading = false
                }
                print("加载心率历史数据失败: \(error)")
            }
        }
    }
    
    /// 获取指定日期范围的血氧历史数据
    /// - Parameters:
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    func loadSpO2HistoryData(startDate: String, endDate: String, type: Int) {
        // 在这里不设置isLoading = true，因为已经在loadDataForTimeRange中设置了
        // errorMessage = nil 也已经在loadDataForTimeRange中设置了
        
        Task {
            do {
                // 添加重试机制
                let maxRetries = 2
                var currentRetry = 0
                var lastError: Error? = nil
                
                while currentRetry <= maxRetries {
                    do {
                        let token = AuthService.shared.currentToken
                        let response = try await apiService.health.getHistoryVitalSignsSpO2(
                            startDate: startDate,
                            endDate: endDate,
                            type: type,
                            token: token?.accessToken
                        )
                        
//                        if let data = response.data {
                            // 准备图表数据
//                            let chartData = data.records.map { Double($0.spO2) }
                        let chartData: [(min:Double, max:Double)] = response.records.map { record in
                            let value: (min:Double, max:Double) = (min: record.minSpO2.double, max: record.maxSpO2.double)//(min = record.minHeartRate, max: record.maxHeartRate)
                            return value
                        }
                        DispatchQueue.main.async {
                            // 更新血氧数据
                            self.spo2Max = response.maxSpO2
                            self.spo2Avg = response.avgSpO2
                            self.spo2Min = response.minSpO2
                            
                            // 根据类型更新图表数据
                            switch type {
                            case 1: // 周
                                self.spo2WeekData = chartData
                            case 2: // 月
                                self.spo2MonthData = chartData
                            case 3: // 年
                                self.spo2YearData = chartData
                            default:
                                break
                            }
                            
                            // 使用任务计数逻辑替代直接设置isLoading
                            self.taskCompleted()
                        }
                        // 请求成功，跳出重试循环
                        break
//                        } else {
//                            throw NetworkError.invalidResponse
//                        }
                    } catch {
                        lastError = error
                        currentRetry += 1
                        
                        // 如果是最后一次重试失败或是致命错误，则不再重试
                        if currentRetry > maxRetries {
                            throw error
                        }
                        
                        // 等待一段时间后重试
                        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                        print("重试获取血氧历史数据 (第\(currentRetry)次)")
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    // 提供更友好的错误信息
                    if let networkError = error as? NetworkError {
                        switch networkError {
                        case .networkError:
                            self.errorMessage = "网络连接失败，请检查网络设置"
                        case .unauthorized:
                            self.errorMessage = "认证失败，请重新登录"
                        case .serverError(_, let message):
                            self.errorMessage = "服务器错误: \(message)"
                        default:
                            self.errorMessage = "获取血氧历史数据失败: \(networkError.localizedDescription)"
                        }
                    } else {
                        self.errorMessage = "获取血氧历史数据失败: \(error.localizedDescription)"
                    }
                    self.isLoading = false
                }
                print("加载血氧历史数据失败: \(error)")
            }
        }
    }
    
    /// 获取指定日期范围的HRV历史数据
    /// - Parameters:
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    ///   - type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    func loadHRVHistoryData(startDate: String, endDate: String, type: Int) {
        // 不设置isLoading = true和errorMessage = nil，因为已经在loadDataForTimeRange中设置了
        
        Task {
            do {
                // 添加重试机制
                let maxRetries = 2
                var currentRetry = 0
                var lastError: Error? = nil
                
                while currentRetry <= maxRetries {
                    do {
                        let token = AuthService.shared.currentToken?.accessToken
                        let response = try await apiService.health.getHistoryVitalSignsHRV(
                            startDate: startDate,
                            endDate: endDate,
                            type: type,
                            token: token
                        )
                        
//                        if let data = response.data {
                            // 准备图表数据
//                            let chartData = data.records.map { Double($0.hrv) }
                            let chartData: [(min:Double, max:Double)] = response.records.map { record in
                                let value: (min:Double, max:Double) = (min: record.minHRV.double, max: record.maxHRV.double)//(min = record.minHeartRate, max: record.maxHeartRate)
                                return value
                            }
                            DispatchQueue.main.async {
                                // 更新HRV数据
                                self.hrvMax = response.maxHRV
                                self.hrvAvg = response.avgHRV
                                self.hrvMin = response.minHRV
                                
                                // 根据类型更新图表数据
                                switch type {
                                case 1: // 周
                                    self.hrvWeekData = chartData
                                case 2: // 月
                                    self.hrvMonthData = chartData
                                case 3: // 年
                                    self.hrvYearData = chartData
                                default:
                                    break
                                }
                                
                                // 任务计数减一，并在所有任务完成时设置isLoading为false
                                self.taskCompleted()
                            }
                            // 请求成功，跳出重试循环
                            break
//                        } else {
//                            throw NetworkError.invalidResponse
//                        }
                    } catch {
                        lastError = error
                        currentRetry += 1
                        
                        // 如果是最后一次重试失败或是致命错误，则不再重试
                        if currentRetry > maxRetries {
                            throw error
                        }
                        
                        // 等待一段时间后重试
                        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                        print("重试获取HRV历史数据 (第\(currentRetry)次)")
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    // 提供更友好的错误信息
                    if let networkError = error as? NetworkError {
                        switch networkError {
                        case .networkError:
                            self.errorMessage = "网络连接失败，请检查网络设置"
                        case .unauthorized:
                            self.errorMessage = "认证失败，请重新登录"
                        case .serverError(_, let message):
                            self.errorMessage = "服务器错误: \(message)"
                        default:
                            self.errorMessage = "获取HRV历史数据失败: \(networkError.localizedDescription)"
                        }
                    } else {
                        self.errorMessage = "获取HRV历史数据失败: \(error.localizedDescription)"
                    }
                    self.isLoading = false
                }
                print("加载HRV历史数据失败: \(error)")
            }
        }
    }
    
    /// 根据选择的时间范围加载数据
    /// - Parameter timeRangeIndex: 时间范围索引 (0: 周, 1: 月, 2: 年)
    func loadDataForTimeRange(_ timeRangeIndex: Int) {
        let (startDate, endDate) = getDateRangeForType(timeRangeIndex + 1) // 转换为API类型 (1: 周, 2: 月, 3: 年)
        isLoading = true
        errorMessage = nil
        
        // 同时加载所有数据，设置活动任务计数为3
        activeTaskCount = 3
        
        // 同时加载心率、血氧和HRV数据
        loadHeartRateHistoryData(startDate: startDate, endDate: endDate, type: timeRangeIndex + 1)
        loadSpO2HistoryData(startDate: startDate, endDate: endDate, type: timeRangeIndex + 1)
        loadHRVHistoryData(startDate: startDate, endDate: endDate, type: timeRangeIndex + 1)
    }
    
    /// 获取指定类型的日期范围
    /// - Parameter type: 时间范围类型 (1: 周, 2: 月, 3: 年)
    /// - Returns: 开始日期和结束日期
    func getDateRangeForType(_ type: Int) -> (String, String) {
        let calendar = Calendar.current
        let now = Date()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        var startDate: Date
        var endDate: Date
        
        switch type {
        case 2: // 月
            // 获取本月第一天
            let components = calendar.dateComponents([.year, .month], from: now)
            startDate = calendar.date(from: components)!
            
            // 获取下月第一天的前一天（本月最后一天）
            var nextMonthComponents = DateComponents()
            nextMonthComponents.month = 1
            nextMonthComponents.day = -1
            endDate = calendar.date(byAdding: nextMonthComponents, to: calendar.date(from: components)!)!
            
        case 3: // 年
            // 获取今年第一天
            let components = calendar.dateComponents([.year], from: now)
            startDate = calendar.date(from: components)!
            
            // 获取今年最后一天
            var lastDayComponents = DateComponents()
            lastDayComponents.year = 1
            lastDayComponents.day = -1
            endDate = calendar.date(byAdding: lastDayComponents, to: startDate)!
            
        default: // 周（默认）
            // 确定本周的开始日期和结束日期
            var weekComponents = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now)
            startDate = calendar.date(from: weekComponents)!
            
            // 结束日期为开始日期后的第6天（一周共7天）
            var endDateComponent = DateComponents()
            endDateComponent.day = 6
            endDate = calendar.date(byAdding: endDateComponent, to: startDate)!
        }
        
        return (dateFormatter.string(from: startDate), dateFormatter.string(from: endDate))
    }
    
    /// 将分钟数转换为小时和分钟
    /// - Parameter minutes: 总分钟数
    /// - Returns: 小时和分钟的元组
    private func convertToHoursAndMinutes(_ minutes: Int) -> (Int, Int) {
        let hours = minutes / 60
        let remainingMinutes = minutes % 60
        return (hours, remainingMinutes)
    }
    
    /// 任务完成，减少活动任务计数
    private func taskCompleted() {
        activeTaskCount -= 1
        if activeTaskCount <= 0 {
            isLoading = false
            activeTaskCount = 0 // 重置计数器，防止负数
        }
    }
    
    /// 异步刷新数据方法，供SwiftUI的refreshable修饰符使用
    /// - Parameter currentTimeRange: 当前选择的时间范围索引 (0: 周, 1: 月, 2: 年)
    /// - Returns: 异步任务
    @MainActor
    func refreshData(currentTimeRange: Int = 0) async {
        // 刷新数据
        loadDataForTimeRange(currentTimeRange)
        
        // 等待加载完成
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
    }
} 
