import Foundation
import Combine

//class SharedDateViewModel: ObservableObject {
//    static let shared = SharedDateViewModel(initialDate: Calendar.current.date(byAdding: .day, value: -1, to: Date())!)
//
//    @Published var selectedDate: Date {
//        didSet {
//            let newDate = Self.startOfDayAndValid(date: selectedDate)
//
//            guard selectedDate != newDate else {
//                let offset = Self.calculateOffset(from: newDate)
//                if selectedDay != offset {
//                    selectedDay = offset
//                }
//                return
//            }
//
//            selectedDate = newDate
//        }
//    }
//
//    @Published var selectedDay: Int {
//        didSet {
//            let validatedOffset = min(0, selectedDay)
//            guard selectedDay != validatedOffset else {
//                let expectedDate = Self.date(fromOffset: validatedOffset)
//                if !Calendar.current.isDate(selectedDate, inSameDayAs: expectedDate) {
//                    selectedDate = expectedDate
//                }
//                return
//            }
//
//            selectedDay = validatedOffset
//        }
//    }
//
//    // MARK: - Init
//    private init(initialDate: Date) {
//        let validDate = Self.startOfDayAndValid(date: initialDate)
//        _selectedDate = Published(initialValue: validDate)
//        _selectedDay = Published(initialValue: Self.calculateOffset(from: validDate))
//    }
//
//    // MARK: - Static Helpers
//    private static func startOfDayAndValid(date: Date) -> Date {
//        let calendar = Calendar.current
//        let start = calendar.startOfDay(for: date)
//        let today = calendar.startOfDay(for: Date())
//        return min(start, today)
//    }
//
//    private static func calculateOffset(from date: Date) -> Int {
//        let calendar = Calendar.current
//        let today = calendar.startOfDay(for: Date())
//        let target = calendar.startOfDay(for: date)
//        return calendar.dateComponents([.day], from: today, to: target).day ?? 0
//    }
//
//    private static func date(fromOffset offset: Int) -> Date {
//        let today = Calendar.current.startOfDay(for: Date())
//        return Calendar.current.date(byAdding: .day, value: offset, to: today) ?? today
//    }
//}

class SharedDateViewModel: ObservableObject {
    static let shared = SharedDateViewModel(initialDate: Calendar.current.date(byAdding: .day, value: 0, to: Date())!)

    @Published var selectedDate: Date {
        didSet {
            // 限制不能选未来日期，强制为当天或更早
            let newDate = Self.startOfDayAndValid(date: selectedDate)
            if selectedDate != newDate {
                selectedDate = newDate
            }
        }
    }

    /// 只读：从 selectedDate 推导出相对于今天的 day 偏移（0 表示今天，-1 昨天，...）
    var selectedDay: Int {
//        let calendar = Calendar.current
//        let today = calendar.startOfDay(for: Date())
//        let target = calendar.startOfDay(for: selectedDate)
        return selectedDate.daysAgoFromToday()//calendar.dateComponents([.day], from: today, to: target).day ?? 0
    }

    // MARK: - Init
    private init(initialDate: Date) {
        self.selectedDate = Self.startOfDayAndValid(date: initialDate)
    }

    // MARK: - Static Helpers
    private static func startOfDayAndValid(date: Date) -> Date {
        let calendar = Calendar.current
        let start = calendar.startOfDay(for: date)
        let today = calendar.startOfDay(for: Date())
        return min(start, today)
    }

    /// 可选：支持通过偏移天数更新日期
    func updateDate(fromDayOffset offset: Int) {
        let today = Calendar.current.startOfDay(for: Date())
        self.selectedDate = Calendar.current.date(byAdding: .day, value: offset, to: today) ?? today
    }
}
