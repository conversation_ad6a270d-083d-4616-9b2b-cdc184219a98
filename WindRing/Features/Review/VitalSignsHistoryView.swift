import SwiftUI
import Charts

/// 生命体征历史详情页面
struct VitalSignsHistoryView: View {
    // MARK: - 属性
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel = VitalSignsHistoryViewModel()
    
    // 时间范围选择
    @State private var selectedTimeRange = 0 // 0: Week, 1: Month, 2: Year
    var timeRanges: [String] {
        ["sleep_history_time_range_week".localized, "sleep_history_time_range_month".localized, "sleep_history_time_range_year".localized]
    }
    
    // 日期范围
    @State private var dateRange = "2025.3.10-2025.3.16"
    @State private var showingDatePicker = false
    @State private var selectedDate = Date()
    
    // 详情弹窗
    @State private var showingDetailSheet = false
    @State private var selectedDetailTitle = ""
    @State private var selectedDetailIcon = ""
    @State private var selectedDetailMaxValue = ""
    @State private var selectedDetailAvgValue = ""
    @State private var selectedDetailMinValue = ""
    @State private var selectedDetailColor = Color.red
    @State private var selectedDetailData: [(min:Double, max:Double)] = []
    @State private var selectedDetailChartType = 0 // 0: 曲线图, 1: 柱状图
    @State private var selectedDetailUnit = ""
    
    // 星期和月份标签
    var weekdays: [String] {
        ["sleep_history_weekday_sun".localized, "sleep_history_weekday_mon".localized, "sleep_history_weekday_tue".localized, "sleep_history_weekday_wed".localized, "sleep_history_weekday_thu".localized, "sleep_history_weekday_fri".localized, "sleep_history_weekday_sat".localized]
    }
    var yearMonths: [String] {
        ["sleep_history_year_jan".localized, "sleep_history_year_feb".localized, "sleep_history_year_mar".localized, "sleep_history_year_apr".localized, "sleep_history_year_may".localized, "sleep_history_year_jun".localized, "sleep_history_year_jul".localized, "sleep_history_year_aug".localized, "sleep_history_year_sep".localized, "sleep_history_year_oct".localized, "sleep_history_year_nov".localized, "sleep_history_year_dec".localized]
    }
    
    // 动态生成当前月份的天数标签
    var monthDayLabels: [String] {
        let calendar = Calendar.current
        guard let range = calendar.range(of: .day, in: .month, for: selectedDate) else {
            return []
        }
        return range.map { String($0) }
    }
    
    // 当前显示的时间标签
    var currentTimeLabels: [String] {
        switch selectedTimeRange {
        case 1: return monthDayLabels
        case 2: return yearMonths
        default: return weekdays
        }
    }
    
    // 月视图下，判断X轴标签是否显示
    private func shouldShowMonthLabel(_ label: String) -> Bool {
        guard let day = Int(label), selectedTimeRange == 1 else { return true }
        let lastDay = monthDayLabels.last.flatMap { Int($0) } ?? 28
        return day == 1 || day % 7 == 0 //|| day == lastDay
    }
    
    // 心率数据的计算属性
    var currentHeartRateData: [(min:Double, max:Double)] {
        switch selectedTimeRange {
        case 1: return viewModel.heartRateMonthData
        case 2: return viewModel.heartRateYearData
        default: return viewModel.heartRateWeekData
        }
    }
    
    // 血氧数据的计算属性
    var currentSpo2Data: [(min:Double, max:Double)] {
        switch selectedTimeRange {
        case 1: return viewModel.spo2MonthData
        case 2: return viewModel.spo2YearData
        default: return viewModel.spo2WeekData
        }
    }
    
    // 心率变异性数据的计算属性
    var currentHRVData: [(min:Double, max:Double)] {
        switch selectedTimeRange {
        case 1: return viewModel.hrvMonthData
        case 2: return viewModel.hrvYearData
        default: return viewModel.hrvWeekData
        }
    }
    
    // MARK: - 视图
    var body: some View {
        VStack(spacing: 0) {
            // 时间范围选择器
            timeRangeSelector
                .background(Color(red: 0.08, green: 0.09, blue: 0.13)) // 使用深色背景
            
            // 内容区域
            ZStack {
                ScrollView {
                    VStack(spacing: 0) {
                        if viewModel.isLoading {
                            ProgressView()
                                .scaleEffect(1.5)
                                .frame(height: 200)
                        } else if let errorMessage = viewModel.errorMessage {
                            errorView(errorMessage: errorMessage)
                        } else {
                            // 心率模块
                            heartRateSection
                            
                            // 血氧模块
                            spo2Section
                            
                            // 心率变异性模块
                            hrvSection
                        }
                        
                        Spacer(minLength: 20)
                    }
//                    .padding(.horizontal)
                }
                .background(Color(red: 0.027, green: 0.027, blue: 0.031).edgesIgnoringSafeArea(.all))
                .refreshable {
                    Task {
                        await viewModel.refreshData(currentTimeRange: selectedTimeRange)
                    }
                }
                
                // 日历弹框直接覆盖在页面上，而不是作为sheet呈现
                if showingDatePicker {
                    datePickerView
                        .zIndex(100) // 确保在最上层
                        .transition(.opacity.animation(.easeInOut(duration: 0.2)))
                }
            }
        }
        .background( // 整体背景
            LinearGradient(
                gradient: Gradient(colors: [Color(red: 0.16, green: 0.19, blue: 0.25), Color(red: 0.08, green: 0.09, blue: 0.13)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)
        )
        .navigationBarBackButtonHidden(true)
        .navigationBarTitleDisplayMode(.inline)
        .toolbarBackground(Color(red: 0.08, green: 0.09, blue: 0.13).opacity(1.0), for: .navigationBar)
        .toolbarBackground(.visible, for: .navigationBar)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image("back")
                            .font(.system(size: 14))
                        Text("vitals_history_nav_title".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                    }
                    .foregroundColor(.white)
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        showingDatePicker = true
                    }
                }) {
                    HStack {
                        Text(dateRange)
                            .font(.custom("DIN-Medium", size: 13))
                            .foregroundColor(.white)
                            .lineLimit(nil)
                        
                        Image("down")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 11, height: 11)
                    }
                    .frame(height: 9.5)
                }
            }
        }
        .onChange(of: selectedTimeRange) { newValue in
            // 更新日期范围
            updateDateRange(for: newValue)
            // 加载相应的数据
            viewModel.loadDataForTimeRange(newValue)
        }
//        .sheet(isPresented: $showingDetailSheet) {
//            detailSheetView
//        }
        .onAppear {
            // 去除导航栏底部分割线
            let appearance = UINavigationBarAppearance()
            appearance.configureWithOpaqueBackground()
            appearance.backgroundColor = UIColor(red: 0.08, green: 0.09, blue: 0.13, alpha: 1.0)
            appearance.shadowColor = .clear // 移除阴影
            appearance.shadowImage = UIImage() // 移除分割线图片
            
            UINavigationBar.appearance().standardAppearance = appearance
            UINavigationBar.appearance().scrollEdgeAppearance = appearance
            
            // 设置当前日期
            selectedDate = Date()
            
            // 页面第一次出现时加载数据
            loadInitialData()
        }
    }
    
    // 错误视图
    private func errorView(errorMessage: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 40))
                .foregroundColor(.orange)
            
            Text(errorMessage)
                .foregroundColor(.red)
                .multilineTextAlignment(.center)
                .padding()
            
            Button(action: {
                // 重试加载数据
                viewModel.loadDataForTimeRange(selectedTimeRange)
            }) {
                Text("vitals_history_button_retry".localized)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 8)
                    .background(Color.blue)
                    .cornerRadius(8)
            }
        }
        .frame(minHeight: 200)
        .padding()
    }
    
    // 心率部分
    private var heartRateSection: some View {
        newVitalDataSection(
            title: "vitals_history_section_title_hr".localized,
            icon: "heart.fill",
            iconColor: .red,
            maxTitle: "activity_history_metric_max".localized,
            maxValue: "\(viewModel.heartRateHistoryData?.maxHeartRate ?? 0)" + "insight_unit_bpm".localized,//"\(viewModel) \(viewModel.heartRateMaxHours) \("hr".localized) \(viewModel.heartRateMaxMins) \("min".localized)",
            avgTitle: "activity_history_metric_ave".localized,
            avgValue: "\(viewModel.heartRateHistoryData?.avgHeartRate ?? 0)" + "insight_unit_bpm".localized,
            minTitle: "activity_history_metric_min".localized,
            minValue: "\(viewModel.heartRateHistoryData?.minHeartRate ?? 0)" + "insight_unit_bpm".localized,
            data: currentHeartRateData,
            yAxisLabels: ["50", "70", "90"],
            chartType: 0, // 曲线图
            color: .red, glossaryKey: .historyVitalHeartRate
        )
    }
    
    // 血氧部分
    private var spo2Section: some View {
        newVitalDataSection(
            title: "vitals_history_section_title_spo2".localized,
            icon: "lungs.fill",
            iconColor: .blue,
            maxTitle: "activity_history_metric_max".localized,
            maxValue: "\(viewModel.spo2Max) %",
            avgTitle: "activity_history_metric_ave".localized,
            avgValue: "\(viewModel.spo2Avg) %",
            minTitle: "activity_history_metric_min".localized,
            minValue: "\(viewModel.spo2Min) %",
            data: currentSpo2Data,
            yAxisLabels: ["90%", "95%", "100%"],
            chartType: 1, // 柱状图
            color: .red, glossaryKey: .historyVitalSpO2
        )
    }
    
    // 心率变异性部分
    private var hrvSection: some View {
        newVitalDataSection(
            title: "vitals_history_section_title_hrv".localized,
            icon: "waveform.path.ecg",
            iconColor: .gray,
            maxTitle: "activity_history_metric_max".localized,
            maxValue: "\(viewModel.hrvMax) \("stress_detail_unit_ms".localized)",
            avgTitle: "activity_history_metric_ave".localized,
            avgValue: "\(viewModel.hrvAvg) \("stress_detail_unit_ms".localized)",
            minTitle: "activity_history_metric_min".localized,
            minValue: "\(viewModel.hrvMin) \("stress_detail_unit_ms".localized)",
            data: currentHRVData,
            yAxisLabels: ["10", "50", "90"],
            chartType: 1, // 柱状图
            color: .red, glossaryKey: .historyVitalHRV
        )
    }
    
    // MARK: - 初始化数据加载
    private func loadInitialData() {
        // 使用当前选择的时间范围加载数据
        viewModel.loadDataForTimeRange(selectedTimeRange)
        
        // 更新日期范围
        let (startDate, endDate) = viewModel.getDateRangeForType(selectedTimeRange + 1)
        
        // 将API日期格式转换为显示格式
        let apiFormatter = DateFormatter()
        apiFormatter.dateFormat = "yyyy-MM-dd"
        
        let displayFormatter = DateFormatter()
        displayFormatter.dateFormat = "yyyy.M.d"
        
        if let startDateObj = apiFormatter.date(from: startDate),
           let endDateObj = apiFormatter.date(from: endDate) {
            dateRange = "\(displayFormatter.string(from: startDateObj))-\(displayFormatter.string(from: endDateObj))"
        }
    }
    
    // MARK: - 时间范围选择器
    private var timeRangeSelector: some View {
        VStack(spacing: 0) {
            HStack(spacing: 0) {
                ForEach(0..<timeRanges.count, id: \.self) { index in
                    Button(action: {
                        withAnimation {
                            selectedTimeRange = index
                        }
                    }) {
                        Text(timeRanges[index])
                            .font(.system(size: 15, weight: .semibold))
                            .foregroundColor(.white)
                            .opacity(selectedTimeRange == index ? 1.0 : 0.6) // 未选中时透明度设置为60%
                            .frame(maxWidth: .infinity)
                            .frame(height: 36)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(selectedTimeRange == index ? Color.blue : Color.clear)
                            )
                            .contentShape(Rectangle())
                    }
                    .padding(.horizontal, 5)
                }
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 8)
            .background(Color(red: 0.08, green: 0.09, blue: 0.13))
        }
    }
    
    // MARK: - 新版生命体征数据区块
    private func newVitalDataSection(
        title: String,
        icon: String,
        iconColor: Color,
        maxTitle: String,
        maxValue: String,
        avgTitle: String,
        avgValue: String,
        minTitle: String,
        minValue: String,
        data: [(min:Double, max:Double)],
        yAxisLabels: [String],
        chartType: Int, // 0: 曲线图, 1: 柱状图
        color: Color,
        glossaryKey: GlossaryKey
    ) -> some View {
        VStack(alignment: .leading, spacing: 0) {
            Divider()
                .background(Color.gray.opacity(0.3))
            // 标题行
            sectionHeaderView(title: title, icon: icon, iconColor: iconColor, glossaryKey: glossaryKey)
            
            Divider()
                .background(Color.gray.opacity(0.3))
            
            // 数据卡片行
            metricCardsRow(maxTitle: maxTitle, maxValue: maxValue, 
                          avgTitle: avgTitle, avgValue: avgValue,
                          minTitle: minTitle, minValue: minValue,
                          icon: icon, iconColor: iconColor)
            // 图表区域
            ChartAreaView(data: data, yAxisLabels: yAxisLabels, chartType: chartType, color: color, currentTimeLabels: currentTimeLabels, selectedTimeRange: selectedTimeRange)
            
            Spacer().frame(height: 10)
        }
//        .background(Color(red: 0.1, green: 0.1, blue: 0.11))
        .cornerRadius(0)
        
        
    }
    
    // 区块标题栏
    private func sectionHeaderView(title: String, icon: String, iconColor: Color,glossaryKey: GlossaryKey) -> some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(iconColor)
                .font(.system(size: 16))
            
            Text(title)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
            
            InfoButtonWithGlossaryPopup(showKey: glossaryKey.rawValue)
            Spacer()
//
//            Button(action: {
//                // 显示详情信息
//                let (maxValue, avgValue, minValue, data) = getDataForDetail(title: title)
//                let chartType = getChartTypeForDetail(title: title)
//                
//                showDetailInfo(
//                    title: title,
//                    icon: icon,
//                    maxValue: maxValue,
//                    avgValue: avgValue,
//                    minValue: minValue,
//                    data: data,
//                    chartType: chartType,
//                    color: iconColor
//                )
//            }) {
//                
////                Image(systemName: "info.circle")
////                    .foregroundColor(.gray)
////                    .font(.system(size: 14))
//            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    // 获取详情页的数据
    private func getDataForDetail(title: String) -> (maxValue: String, avgValue: String, minValue: String, data: [(min:Double, max:Double)]) {
        switch title {
        case "vitals_history_section_title_hr".localized:
            return (
                maxValue: "\(viewModel.heartRateMaxHours) \("hr".localized) \(viewModel.heartRateMaxMins) \("min".localized)",
                avgValue: "\(viewModel.heartRateAvgHours) \("hr".localized) \(viewModel.heartRateAvgMins) \("min".localized)",
                minValue: "\(viewModel.heartRateMinHours) \("hr".localized) \(viewModel.heartRateMinMins) \("min".localized)",
                data: currentHeartRateData
            )
        case "vitals_history_section_title_spo2".localized:
            return (
                maxValue: "\(viewModel.spo2Max) %",
                avgValue: "\(viewModel.spo2Avg) %",
                minValue: "\(viewModel.spo2Min) %",
                data: currentSpo2Data
            )
        case "vitals_history_section_title_hrv".localized:
            return (
                maxValue: "\(viewModel.hrvMax) \("stress_detail_unit_ms".localized)",
                avgValue: "\(viewModel.hrvAvg) \("stress_detail_unit_ms".localized)",
                minValue: "\(viewModel.hrvMin) \("stress_detail_unit_ms".localized)",
                data: currentHRVData
            )
        default:
            return ("", "", "", [])
        }
    }
    
    // 获取详情页的图表类型
    private func getChartTypeForDetail(title: String) -> Int {
        switch title {
        case "vitals_history_section_title_hr".localized:
            return 0 // 曲线图
        case "vitals_history_section_title_spo2".localized, "vitals_history_section_title_hrv".localized:
            return 1 // 柱状图
        default:
            return 0
        }
    }
    
    // 指标卡片行
    private func metricCardsRow(
        maxTitle: String, maxValue: String,
        avgTitle: String, avgValue: String,
        minTitle: String, minValue: String,
        icon: String, iconColor: Color
    ) -> some View {
        HStack(spacing: 0) {
            newMetricCard(title: maxTitle, value: maxValue, icon: icon, iconColor: iconColor)
            
            newMetricCard(title: avgTitle, value: avgValue, icon: nil, iconColor: .clear)
            
            newMetricCard(title: minTitle, value: minValue, icon: icon, iconColor: iconColor)
        }
        .padding(.vertical, 8)
    }
    
    // 新版区间柱状图区域视图
    private func ChartAreaView(data: [(min: Double, max: Double)], yAxisLabels: [String], chartType: Int, color: Color, currentTimeLabels: [String], selectedTimeRange: Int) -> some View {
        @State var selectedIndex: Int? = nil
        @State var lastTimeRange: Int = 0

        let maxValue = data.map { $0.max }.max() ?? 100
        let minValue = data.map { $0.min }.min() ?? 0
        
        return ZStack(alignment: .trailing) {
            Chart {
                ForEach(Array(data.enumerated()), id: \.offset) { index, value in
                    let minY = value.min
                    let maxY = value.max
                    let adjustedMaxY = (minY == maxY) ? (maxY + 1) : maxY
                    RectangleMark(
                        x: .value("Time", currentTimeLabels[index]),
                        yStart: .value("Value", value.min),
                        yEnd: .value("Value", adjustedMaxY),
                        width: .fixed(10)
                    )
                    .cornerRadius(6)
                    .foregroundStyle(selectedIndex == index ? Color.purple.opacity(1.0) : Color.purple.opacity(0.5))
                }
            }
            .chartYScale(domain: 0...(maxValue) * 1.2)
            .chartXAxis {
                AxisMarks(values: .automatic) { value in
                    if let label = value.as(String.self) {
                        if shouldShowMonthLabel(label) {
                            AxisValueLabel() {
                                Text(label)
                                    .font(.system(size: 10))
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                }
            }
            .chartYAxis {
                AxisMarks(position: .trailing) { value in
                    AxisGridLine()
                    AxisValueLabel() {
                        if let val = value.as(Double.self) {
                            Text(val.truncatingRemainder(dividingBy: 1) == 0 ? String(format: "%.0f", val) : String(format: "%.1f", val))
                                .font(.system(size: 10))
                                .foregroundColor(.gray)
                        }
                    }
                }
            }
            .padding(.trailing, 0)
            .chartOverlay { proxy in
                GeometryReader { geometry in
                    Rectangle().fill(Color.clear).contentShape(Rectangle())
                        .gesture(
                            DragGesture(minimumDistance: 0)
                                .onChanged { value in
                                    let origin = geometry[proxy.plotAreaFrame].origin
                                    let location = value.location
                                    var minDistance: CGFloat = .infinity
                                    var closestIndex: Int? = nil
                                    for (i, label) in currentTimeLabels.enumerated() {
                                        let xPos = proxy.position(forX: label) ?? 0
                                        let distance = abs(xPos - (location.x - origin.x))
                                        if distance < minDistance {
                                            minDistance = distance
                                            closestIndex = i
                                        }
                                    }
                                    if let idx = closestIndex, minDistance < 30 {
                                        selectedIndex = idx
                                    } else {
                                        selectedIndex = nil
                                    }
                                }
                                .onEnded { _ in
                                    selectedIndex = nil
                                }
                        )
                }
            }
            .overlay(
                GeometryReader { geo in
                    if let idx = selectedIndex, idx < data.count {
                        let xPos = proxyPositionX(geo: geo, index: idx)
                        VStack(spacing: 4) {
                            Text(String(format: "vitals_history_tooltip_max".localized, String(format: "%.1f", data[idx].max)))
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(4)
                                .background(Color.black.opacity(0.8))
                                .cornerRadius(4)
                            Text(String(format: "vitals_history_tooltip_min".localized, String(format: "%.1f", data[idx].min)))
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(4)
                                .background(Color.black.opacity(0.8))
                                .cornerRadius(4)
                        }
                        .position(x: xPos, y: 0)
                    }
                }
            )
        }
        .frame(height: 250)
        .padding(.top, 10)
        .onChange(of: selectedTimeRange) { newValue in
            // 切换类型时重置手势选中
            selectedIndex = nil
            lastTimeRange = newValue
        }
    }

    private func proxyPositionX(geo: GeometryProxy, index: Int) -> CGFloat {
        let count = currentTimeLabels.count
        if count <= 1 { return geo.size.width / 2 }
        let step = geo.size.width / CGFloat(count)
        return step * CGFloat(index) + step / 2
    }
    
    // 新版指标卡片
    private func newMetricCard(title: String, value: String, icon: String?, iconColor: Color) -> some View {
        VStack(alignment: .center, spacing: 4) {
            Text(title)
                .font(.system(size: 12))
                .foregroundColor(.gray)
            
            HStack(alignment: .center, spacing: 2) {
//                if let icon = icon {
//                    Image(systemName: icon)
//                        .foregroundColor(iconColor)
//                        .font(.system(size: 12))
//                }
                
                Text(value)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(Color(red: 0.12, green: 0.12, blue: 0.14))
        .cornerRadius(8)
        .padding(.horizontal, 4)
    }
    
    // 新版曲线图 - 平滑曲线效果
    private func newLineChartView(data: [Double], color: Color) -> some View {
        GeometryReader { geometry in
            ZStack {
                // 绘制平滑曲线
                SmoothLineView(data: data, color: color)
                    .frame(height: geometry.size.height)
                
                // 添加发光效果
                SmoothLineView(data: data, color: color)
                    .frame(height: geometry.size.height)
                    .blur(radius: 8)
                    .opacity(0.5)
            }
        }
    }
    
    // 平滑曲线视图
    private struct SmoothLineView: View {
        let data: [Double]
        let color: Color
        
        var body: some View {
            GeometryReader { geometry in
                ZStack {
                    // 曲线
                    createLinePath(in: geometry)
                        .stroke(color, style: StrokeStyle(lineWidth: 2, lineCap: .round, lineJoin: .round))
                    
                    // 渐变填充
                    createGradientPath(in: geometry)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    color.opacity(0.3),
                                    color.opacity(0.1),
                                    color.opacity(0.0)
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                }
            }
        }
        
        // 创建曲线路径
        private func createLinePath(in geometry: GeometryProxy) -> Path {
            Path { path in
                let points = calculatePoints(in: geometry)
                
                // 开始绘制
                guard !points.isEmpty else { return }
                path.move(to: points[0])
                
                // 绘制曲线
                drawCurvedLine(path: &path, points: points, stepWidth: calculateStepWidth(in: geometry))
            }
        }
        
        // 创建带渐变的路径
        private func createGradientPath(in geometry: GeometryProxy) -> Path {
            Path { path in
                let points = calculatePoints(in: geometry)
                let width = geometry.size.width
                let height = geometry.size.height
                
                // 开始绘制
                guard !points.isEmpty else { return }
                
                // 绘制路径底边
                path.move(to: CGPoint(x: 0, y: height))
                path.addLine(to: points[0])
                
                // 绘制曲线
                drawCurvedLine(path: &path, points: points, stepWidth: calculateStepWidth(in: geometry))
                
                // 闭合路径
                path.addLine(to: CGPoint(x: width, y: height))
                path.addLine(to: CGPoint(x: 0, y: height))
            }
        }
        
        // 计算所有点的位置
        private func calculatePoints(in geometry: GeometryProxy) -> [CGPoint] {
            let maxValue = data.max() ?? 1
            let minValue = data.min() ?? 0
            let range = maxValue - minValue > 0 ? maxValue - minValue : 1
            
            let width = geometry.size.width
            let height = geometry.size.height
            let stepWidth = width / CGFloat(data.count - 1)
            
            var points: [CGPoint] = []
            
            // 计算所有点
            for i in 0..<data.count {
                let xPosition = CGFloat(i) * stepWidth
                let yPosition = height - CGFloat((data[i] - minValue) / range) * height * 0.8 - height * 0.1
                points.append(CGPoint(x: xPosition, y: yPosition))
            }
            
            return points
        }
        
        // 计算步宽
        private func calculateStepWidth(in geometry: GeometryProxy) -> CGFloat {
            let width = geometry.size.width
            return width / CGFloat(data.count - 1)
        }
        
        // 绘制曲线
        private func drawCurvedLine(path: inout Path, points: [CGPoint], stepWidth: CGFloat) {
            for i in 1..<points.count {
                // 前一个点和当前点的中点
                let mid1 = CGPoint(
                    x: (points[i-1].x + points[i].x) / 2,
                    y: (points[i-1].y + points[i].y) / 2
                )
                
                // 曲线控制点
                let control1 = CGPoint(
                    x: mid1.x - stepWidth * 0.1,
                    y: points[i-1].y
                )
                
                let control2 = CGPoint(
                    x: mid1.x + stepWidth * 0.1,
                    y: points[i].y
                )
                
                path.addCurve(to: points[i], control1: control1, control2: control2)
            }
        }
    }
    
    // 新版柱状图
    private func newBarChartView(data: [Double], color: Color) -> some View {
        GeometryReader { geometry in
            HStack(alignment: .bottom, spacing: 4) {
                ForEach(0..<data.count, id: \.self) { index in
                    let maxValue = 100.0 // 固定最大值为100
                    let percent = data[index] / maxValue
                    let height = percent * geometry.size.height * 0.9
                    
                    VStack(spacing: 0) {
                        Rectangle()
                            .fill(color)
                            .opacity(index > 4 ? 0.3 : 1.0) // 后几天数据透明度降低
                            .frame(width: 4, height: height)
                            .cornerRadius(2)
                    }
                    .frame(maxWidth: .infinity)
                }
            }
            .padding(.horizontal, 8)
        }
    }
    
    // 日期选择器视图
    private var datePickerView: some View {
        CustomCalendarView(
            selectedDate: $selectedDate,
            selectedTimeRange: $selectedTimeRange,
            onClose: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    showingDatePicker = false
                }
            },
            onDateSelected: { date in
                // 更新日期并重新加载数据
                selectedDate = date
                updateDateRangeFromSelection()
                withAnimation(.easeInOut(duration: 0.2)) {
                    showingDatePicker = false
                }
            }
        )
    }
    
    // 自定义日历视图组件
    struct CustomCalendarView: View {
        @Binding var selectedDate: Date
        @Binding var selectedTimeRange: Int // 绑定时间范围选择
        var onClose: () -> Void
        var onDateSelected: (Date) -> Void
        
        @State private var currentMonth: Date
        
        private let calendar = Calendar.current
        private let monthFormatter = DateFormatter()
        private let dayFormatter = DateFormatter()
        private let weekdayFormatter = DateFormatter()
        
        init(selectedDate: Binding<Date>, selectedTimeRange: Binding<Int>, onClose: @escaping () -> Void, onDateSelected: @escaping (Date) -> Void) {
            self._selectedDate = selectedDate
            self._selectedTimeRange = selectedTimeRange
            self.onClose = onClose
            self.onDateSelected = onDateSelected
            self._currentMonth = State(initialValue: selectedDate.wrappedValue)
            
            monthFormatter.dateFormat = "MMMM yyyy"
            dayFormatter.dateFormat = "d"
            weekdayFormatter.dateFormat = "E"
        }
        
        var body: some View {
            ZStack {
                // 磨砂半透明背景
                Color.black.opacity(0.7)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation {
                            onClose()
                        }
                    }
                
                VStack(spacing: 15) {
                    // 月份导航
                    HStack {
                        Button(action: {
                            previousMonth()
                        }) {
                            Image(systemName: "chevron.left")
                                .foregroundColor(.white)
                                .font(.system(size: 18))
                        }
                        
                        Spacer()
                        
                        Text(monthFormatter.string(from: currentMonth))
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Button(action: {
                            nextMonth()
                        }) {
                            Image(systemName: "chevron.right")
                                .foregroundColor(.white)
                                .font(.system(size: 18))
                        }
                    }
                    .padding(.horizontal)
                    .padding(.top, 15)
                    
                    // 星期标题
                    HStack(spacing: 0) {
                        ForEach(getDaysOfWeek(), id: \.self) { day in
                            Text(day)
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .frame(maxWidth: .infinity)
                        }
                    }
                    .padding(.horizontal, 5)
                    .padding(.top, 10)
                    
                    // 日期网格
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 10) {
                        ForEach(extractDates()) { dateValue in
                            CalendarDayView(
                                dateValue: dateValue,
                                selectedDate: $selectedDate,
                                onDateSelected: onDateSelected
                            )
                            .frame(height: 35)
                        }
                    }
                    .padding(.horizontal, 5)
                    
                    // 底部按钮区域
                    HStack {
                        Spacer()
                        
                        // 返回今天按钮
                        Button(action: {
                            selectedDate = Date()
                            onDateSelected(Date())
                        }) {
                            Text("sleep_history_calendar_back_to_today".localized)
                                .font(.system(size: 15))
                                .foregroundColor(.white)
                                .padding(.vertical, 6)
                                .padding(.horizontal, 14)
                                .background(Capsule().stroke(Color.white.opacity(0.4), lineWidth: 1))
                        }
                    }
                    .padding(.top, 5)
                    .padding(.bottom, 15)
                    .padding(.trailing, 15)
                }
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(red: 0.10, green: 0.12, blue: 0.18))
                )
                .frame(width: UIScreen.main.bounds.width * 0.85)
                .padding(.horizontal, 20)
            }
        }
        
        // 获取星期标题
        private func getDaysOfWeek() -> [String] {
            // 直接返回固定顺序的星期标题，以周日开始
            return ["sleep_history_weekday_sun".localized, "sleep_history_weekday_mon".localized, "sleep_history_weekday_tue".localized, "sleep_history_weekday_wed".localized, "sleep_history_weekday_thu".localized, "sleep_history_weekday_fri".localized, "sleep_history_weekday_sat".localized]
        }
        
        // 提取当月日期
        private func extractDates() -> [DateValue] {
            let monthStart = startOfMonth(for: currentMonth)
            let monthEnd = endOfMonth(for: currentMonth)
            
            let calendar = Calendar.current
            let startDayOfWeek = calendar.component(.weekday, from: monthStart)
            let daysInMonth = calendar.dateComponents([.day], from: monthStart, to: monthEnd).day! + 1
            
            var dateValues = [DateValue]()
            
            // 调整星期偏移
            // weekday: 1=周日, 2=周一, ..., 7=周六
            // 所以从周日开始，偏移量就是weekday-1
            let offset = startDayOfWeek - 1
            
            // 填充前置空白
            for _ in 0..<offset {
                dateValues.append(DateValue(day: 0, date: Date(), isCurrentMonth: false))
            }
            
            // 填充当月日期
            for day in 1...daysInMonth {
                if let date = calendar.date(byAdding: .day, value: day - 1, to: monthStart) {
                    dateValues.append(DateValue(day: day, date: date, isCurrentMonth: true))
                }
            }
            
            return dateValues
        }
        
        // 获取月份开始日期
        private func startOfMonth(for date: Date) -> Date {
            let components = calendar.dateComponents([.year, .month], from: date)
            return calendar.date(from: components)!
        }
        
        // 获取月份结束日期
        private func endOfMonth(for date: Date) -> Date {
            var components = DateComponents()
            components.month = 1
            components.day = -1
            return calendar.date(byAdding: components, to: startOfMonth(for: date))!
        }
        
        // 切换到上个月
        private func previousMonth() {
            if let newDate = calendar.date(byAdding: .month, value: -1, to: currentMonth) {
                currentMonth = newDate
            }
        }
        
        // 切换到下个月
        private func nextMonth() {
            if let newDate = calendar.date(byAdding: .month, value: 1, to: currentMonth) {
                currentMonth = newDate
            }
        }
    }
    
    // 日期值模型
    struct DateValue: Identifiable {
        var id = UUID()
        var day: Int
        var date: Date
        var isCurrentMonth: Bool
    }
    
    // 日历天数视图
    struct CalendarDayView: View {
        let dateValue: DateValue
        @Binding var selectedDate: Date
        var onDateSelected: (Date) -> Void
        
        private let calendar = Calendar.current
        
        var isSelected: Bool {
            calendar.isDate(dateValue.date, inSameDayAs: selectedDate)
        }
        
        var isToday: Bool {
            calendar.isDateInToday(dateValue.date)
        }
        
        // 判断是否为未来日期
        var isFutureDate: Bool {
            if dateValue.day == 0 { return false }
            return calendar.compare(dateValue.date, to: Date(), toGranularity: .day) == .orderedDescending
        }
        
        // 判断是否为过去日期（含今天）
        var isPastOrToday: Bool {
            if dateValue.day == 0 { return false }
            return calendar.compare(dateValue.date, to: Date(), toGranularity: .day) != .orderedDescending
        }
        
        var body: some View {
            VStack(spacing: 6) {
                if dateValue.day != 0 {
                    Button(action: {
                        onDateSelected(dateValue.date)
                    }) {
                        Text("\(dateValue.day)")
                            .font(.system(size: 16))
                            .fontWeight(isSelected ? .medium : .regular)
                            .foregroundColor(
                                isSelected ? .white :
                                    dateValue.isCurrentMonth ? .white : .gray.opacity(0.6)
                            )
                    }
                    
                    // 底部指示点
                    if isSelected {
                        // 选中日期显示蓝色点
                        Circle()
                            .fill(Color.blue)
                            .frame(width: 5, height: 5)
                    } else if isPastOrToday {
                        // 过去日期显示灰色点
                        Circle()
                            .fill(Color.gray.opacity(0.5))
                            .frame(width: 5, height: 5)
                    } else {
                        // 占位空间
                        Circle()
                            .fill(Color.clear)
                            .frame(width: 5, height: 5)
                    }
                } else {
                    // 空白占位
                    Text("")
                        .font(.system(size: 16))
                        .frame(maxWidth: .infinity)
                    
                    // 占位空间
                    Circle()
                        .fill(Color.clear)
                        .frame(width: 5, height: 5)
                }
            }
        }
    }
    
    // 详情弹窗视图
    private var detailSheetView: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Button("vitals_history_detail_sheet_close".localized) {
                    showingDetailSheet = false
                }
                .foregroundColor(.blue)
                
                Spacer()
                
                Text(selectedDetailTitle)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button("vitals_history_detail_sheet_more".localized) {
                    // 显示更多信息
                }
                .foregroundColor(.blue)
            }
            .padding()
            .background(Color(red: 0.1, green: 0.1, blue: 0.11))
            
            ScrollView {
                VStack(spacing: 16) {
                    // 数据指标卡片
                    HStack(spacing: 8) {
                        detailMetricCard(title: "vitals_history_detail_sheet_maximum".localized, value: selectedDetailMaxValue)
                            .frame(maxWidth: .infinity)
                        
                        detailMetricCard(title: "vitals_history_detail_sheet_average".localized, value: selectedDetailAvgValue)
                            .frame(maxWidth: .infinity)
                        
                        detailMetricCard(title: "vitals_history_detail_sheet_minimum".localized, value: selectedDetailMinValue)
                            .frame(maxWidth: .infinity)
                    }
                    .padding(.horizontal)
                    .padding(.top)
                    
                    // 大图表
                    VStack(spacing: 16) {
                        if selectedDetailChartType == 0 {
                            // 曲线图
//                            newLineChartView(data: selectedDetailData, color: selectedDetailColor)
//                                .frame(height: 180)
//                                .padding(.horizontal)
                        } else {
                            // 柱状图
//                            newBarChartView(data: selectedDetailData, color: selectedDetailColor)
//                                .frame(height: 180)
//                                .padding(.horizontal)
                        }
                        
                        // 时间标签
                        HStack(spacing: 0) {
                            ForEach(currentTimeLabels, id: \.self) { label in
                                Text(label)
                                    .font(.system(size: 12))
                                    .foregroundColor(.gray)
                                    .frame(maxWidth: .infinity)
                            }
                        }
                        .padding(.horizontal)
                    }
                    .padding(.vertical)
                    .background(Color(red: 0.1, green: 0.1, blue: 0.11))
                    
                    // 指标说明
                    VStack(alignment: .leading, spacing: 12) {
                        Text(String(format: "vitals_history_detail_sheet_about".localized, selectedDetailTitle))
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        Text(getDetailDescription())
                            .font(.body)
                            .foregroundColor(.gray)
                            .lineSpacing(4)
                        
                        Text("vitals_history_detail_sheet_suggestion".localized)
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.top, 8)
                        
                        Text(getDetailSuggestion())
                            .font(.body)
                            .foregroundColor(.gray)
                            .lineSpacing(4)
                    }
                    .padding()
                    
                    // 健康管理按钮
                    Button(action: {
                        // 执行健康管理操作
                        showingDetailSheet = false
                    }) {
                        Text("vitals_history_detail_sheet_start_management".localized)
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                    .padding()
                }
            }
        }
        .background(Color.black)
        .edgesIgnoringSafeArea(.bottom)
    }
    
    // 详情页指标卡片
    private func detailMetricCard(title: String, value: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.system(size: 14))
                .foregroundColor(.gray)
            
            Text(value)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.white)
        }
        .padding(12)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color(red: 0.12, green: 0.12, blue: 0.14))
        .cornerRadius(8)
    }
    
    // 获取详情描述
    private func getDetailDescription() -> String {
        switch selectedDetailTitle {
        case "vitals_history_desc_title_hr".localized:
            return "vitals_history_desc_content_hr".localized
        case "vitals_history_desc_title_spo2".localized:
            return "vitals_history_desc_content_spo2".localized
        case "vitals_history_desc_title_hrv".localized:
            return "vitals_history_desc_content_hrv".localized
        default:
            return "vitals_history_desc_no_info".localized
        }
    }
    
    // 获取详情建议
    private func getDetailSuggestion() -> String {
        switch selectedDetailTitle {
        case "vitals_history_suggestion_title_hr".localized:
            return "vitals_history_suggestion_content_hr".localized
        case "vitals_history_suggestion_title_spo2".localized:
            return "vitals_history_suggestion_content_spo2".localized
        case "vitals_history_suggestion_title_hrv".localized:
            return "vitals_history_suggestion_content_hrv".localized
        default:
            return "vitals_history_suggestion_no_info".localized
        }
    }
    
    // 显示详情信息
    private func showDetailInfo(
        title: String,
        icon: String,
        maxValue: String,
        avgValue: String,
        minValue: String,
        data: [(min:Double, max:Double)],
        chartType: Int,
        color: Color
    ) {
        selectedDetailTitle = title
        selectedDetailIcon = icon
        selectedDetailMaxValue = maxValue
        selectedDetailAvgValue = avgValue
        selectedDetailMinValue = minValue
        selectedDetailData = data
        selectedDetailChartType = chartType
        selectedDetailColor = color
        
        showingDetailSheet = true
    }
    
    // 根据选择的日期更新日期范围
    private func updateDateRangeFromSelection() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy.M.d"
        
        let calendar = Calendar.current
        
        switch selectedTimeRange {
        case 1: // Month
            if let startOfMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: selectedDate)),
               let endOfMonth = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: startOfMonth) {
                dateRange = "\(formatter.string(from: startOfMonth))-\(formatter.string(from: endOfMonth))"
                
                // 加载该月的数据
                viewModel.loadDataForTimeRange(selectedTimeRange)
            }
        case 2: // Year
            if let startOfYear = calendar.date(from: calendar.dateComponents([.year], from: selectedDate)),
               let endOfYear = calendar.date(from: DateComponents(year: calendar.component(.year, from: selectedDate), month: 12, day: 31)) {
                dateRange = "\(formatter.string(from: startOfYear))-\(formatter.string(from: endOfYear))"
                
                // 加载该年的数据
                viewModel.loadDataForTimeRange(selectedTimeRange)
            }
        default: // Week
            // 确定当前周的起始日期和结束日期
            var dayComponent = calendar.dateComponents([.weekday], from: selectedDate)
            let weekday = dayComponent.weekday ?? 1
            
            // weekday: 1=周日, 2=周一, ..., 7=周六
            // 要得到周日，需要向前推 weekday-1 天
            let daysToSubtract = weekday - 1
            
            if let startOfWeek = calendar.date(byAdding: .day, value: -daysToSubtract, to: selectedDate),
               let endOfWeek = calendar.date(byAdding: .day, value: 6 - daysToSubtract, to: selectedDate) {
                dateRange = "\(formatter.string(from: startOfWeek))-\(formatter.string(from: endOfWeek))"
                
                // 加载该周的数据
                viewModel.loadDataForTimeRange(selectedTimeRange)
            }
        }
    }
    
    // 更新日期范围文本
    private func updateDateRange(for timeRangeIndex: Int) {
        let (startDate, endDate) = viewModel.getDateRangeForType(timeRangeIndex + 1)
        
        // 将API日期格式转换为显示格式
        let apiFormatter = DateFormatter()
        apiFormatter.dateFormat = "yyyy-MM-dd"
        
        let displayFormatter = DateFormatter()
        displayFormatter.dateFormat = "yyyy.M.d"
        
        if let startDateObj = apiFormatter.date(from: startDate),
           let endDateObj = apiFormatter.date(from: endDate) {
            dateRange = "\(displayFormatter.string(from: startDateObj))-\(displayFormatter.string(from: endDateObj))"
        }
    }
}

// MARK: - 预览
struct VitalSignsHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            VitalSignsHistoryView()
        }
        .preferredColorScheme(.dark)
    }
} 

