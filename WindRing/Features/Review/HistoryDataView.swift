import SwiftUI
import Combine

/// 历史数据视图
struct HistoryDataView: View {
    // MARK: - 属性
    // 睡眠视图模型
    @StateObject private var sleepViewModel = SleepHistoryViewModel()
    
    // 活动视图模型
    @StateObject private var activityViewModel = ActivityHistoryViewModel()
    
    // 压力视图模型
    @StateObject private var stressViewModel = StressHistoryViewModel()
    
    // 生命体征视图模型
    @StateObject private var vitalSignsViewModel = VitalSignsHistoryViewModel()
    
    @Environment(\.colorScheme) private var colorScheme
    
    // 卡片固定高度
    private let cardHeight: CGFloat = 90
    
    // MARK: - 主视图
    var body: some View {
        ZStack(alignment: .top) {
            // 主背景色
            Color(red: 0.027, green: 0.027, blue: 0.031)
                .ignoresSafeArea()
            
            // 顶部背景图 - 占据整个顶部区域
            VStack {
                Image("Review_bg")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 250)
                    .frame(maxWidth: .infinity)
                    .offset(y: 110)  // 向下偏移30像素
                    .edgesIgnoringSafeArea(.top)
                
                Spacer()
            }
            .ignoresSafeArea()
            
            // 内容
            VStack(spacing: 10) {
                // 标题
                HStack {
                    // 添加小标识（竖线）
                    RoundedRectangle(cornerRadius: 0.8)
                        .fill(Color.white)
                        .frame(width: 4, height: 12)
                        .padding(.leading, 0)
                        .padding(.trailing, 0)
                    
                    Text("history_data".localized)
                        .font(.system(size: 16, weight: .heavy))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                .padding(.horizontal, 0)
                .padding(.top, 20)
                .padding(.bottom, 10)
                
                // 睡眠卡片
                healthDataCard(
                    title: "sleep".localized,
                    icon: "slices_33",
                    iconColor: .purple,
                    value1: "\(sleepViewModel.sleepAvgTime?.hours ?? 0)",
                    value2: "\(sleepViewModel.sleepAvgTime?.minutes ?? 0)",
                    value3: "min".localized,
                    iconBackgroundColor: Color(red: 0.2, green: 0.2, blue: 0.3, opacity: 0.5),
                    isLoading: sleepViewModel.isLoading,
                    useAssetImage: true
                )
                
                // 活动卡片
                healthDataCard(
                    title: "activity".localized,
                    icon: "slices_34",
                    iconColor: .teal,
                    value1: "\(activityViewModel.activityAvgScore.score)",
                    value2: "score".localized,
                    value3: "",
                    iconBackgroundColor: Color(red: 0.2, green: 0.2, blue: 0.3, opacity: 0.5),
                    isLoading: activityViewModel.isLoading,
                    useAssetImage: true
                )
                
                if VersionUpdateService.shared.status == 1{
                    
                    // 压力卡片
                    healthDataCard(
                        title: "stress".localized,
                        icon: "slices_35",
                        iconColor: .blue,
                        value1: "\(stressViewModel.stressAvgScore ?? 0)",
                        value2: "score".localized,
                        value3: "",
                        iconBackgroundColor: Color(red: 0.2, green: 0.2, blue: 0.3, opacity: 0.5),
                        isLoading: stressViewModel.isLoading,
                        useAssetImage: true
                    )
                    
                    // 生命体征卡片 - 使用ViewModel数据
                    healthDataCard(
                        title: "vital_signs".localized,
                        icon: "slices_36",
                        iconColor: .red,
                        value1: "\(vitalSignsViewModel.heartRateAvg ?? 0)",
                        value2: "insight_unit_bpm".localized,
                        value3: "",
                        iconBackgroundColor: Color(red: 0.2, green: 0.2, blue: 0.3, opacity: 0.5),
                        isLoading: vitalSignsViewModel.isLoading,
                        useAssetImage: true
                    )
                }
                
                
                // 底部文字
                Text("history_footer_message".localized)
                    .font(.custom("D-DIN", size: 12))
                    .foregroundColor(Color(red: 0.54, green: 0.55, blue: 0.58))
                    .multilineTextAlignment(.center)
                    .padding(.top, 20)
                    .padding(.horizontal, 10)
                    .frame(maxWidth: .infinity)
                
                Spacer() // 底部留出一些空间
            }
            .padding(.horizontal, 10)
        }
        .onAppear {
            loadData()
        }
        .navigationBarHidden(true)
    }
    
    // 加载数据
    private func loadData() {
        sleepViewModel.loadHistory7AvgSleepTime()
        activityViewModel.getHistory7AvgScore()  // 使用新API加载周数据
        stressViewModel.loadHistory7AvgStressScore()
        vitalSignsViewModel.loadHistory7AvgHeartRate() // 加载心率数据
    }
    
    // MARK: - 健康数据卡片
    private func healthDataCard(title: String, icon: String, iconColor: Color, value1: String, value2: String, value3: String, iconBackgroundColor: Color, isLoading: Bool, useAssetImage: Bool = false) -> some View {
        NavigationLink(destination: healthDetailDestination(for: title)) {
            HStack {
                // 左侧图标
                if useAssetImage {
                    // 使用Assets中的图像
                    Image(icon)
                        .resizable()
                        .scaledToFit()
                        .frame(width: 47, height: 47)
                        .padding(.leading, 20)
                } else {
                    // 使用系统图标
                    Image(systemName: icon)
                        .font(.system(size: 24))
                        .foregroundColor(iconColor)
                        .padding(.leading, 20)
                }
                
                // 标题
                Text(title)
                    .font(.custom("PingFang-SC-Heavy", size: 18))
                    .fontWeight(.black)
                    .foregroundColor(.white)
                
                Spacer()
                
                if isLoading {
                    // 加载指示器
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                        .padding(.trailing, 20)
                } else {
                    // 右侧内容
                    HStack(spacing: 4) {
                        // 数值和说明文本
                        VStack(alignment: .trailing) {
                            // 数值部分
                            if value3.isEmpty {
                                // Activity, Stress, Vital Signs - 数字和单位使用不同字体大小
                                HStack(alignment: .firstTextBaseline, spacing: 2) {
                                    Text(value1) // "84", "72", "86"
                                        .font(.system(size: 18, weight: .bold))
                                        .foregroundColor(.white)
                                    
                                    Text(" \(value2)") // "Score", "bpm"
                                        .font(.system(size: 12, weight: .medium))
                                        .foregroundColor(.white)
                                }
                            } else {
                                // Sleep - 数字和单位使用不同字体大小
                                HStack(alignment: .firstTextBaseline, spacing: 2) {
                                    Text(value1) // "2"
                                        .font(.system(size: 18, weight: .bold))
                                        .foregroundColor(.white)
                                    
                                    Text("hr".localized) // "hr"
                                        .font(.system(size: 12, weight: .medium))
                                        .foregroundColor(.white)
                                    
                                    Text(value2) // "30"
                                        .font(.system(size: 18, weight: .bold))
                                        .foregroundColor(.white)
                                    
                                    Text("min".localized) // "min"
                                        .font(.system(size: 12, weight: .medium))
                                        .foregroundColor(.white)
                                }
                            }
                            
                            // 7日平均说明文本
                            Text("history_7day_average".localized)
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                        }
                        
                        // 箭头
                        Image("right") // 使用自定义图像替代系统图标
                            .resizable()
                            .scaledToFit()
                            .frame(width: 12, height: 12)
                            .foregroundColor(.gray)
                            .padding(.leading, 2)
                            .padding(.trailing, 16)
                    }
                }
            }
            .frame(height: cardHeight)
            .background(
                LinearGradient(
                    gradient: Gradient(
                        colors: [
                            Color(red: 0.16, green: 0.19, blue: 0.25),
                            Color(red: 0.1, green: 0.12, blue: 0.16)
                        ]
                    ),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 健康详情页面目标
    private func healthDetailDestination(for title: String) -> some View {
        Group {
            switch title {
            case "sleep".localized:
                SleepHistoryView()
            case "activity".localized:
                ActivityHistoryView()
            case "stress".localized:
                StressHistoryView()
            case "vital_signs".localized:
                VitalSignsHistoryView()
            default:
                Text("history_no_detail_view".localized)
            }
        }
    }
}

// MARK: - 预览
struct HistoryDataView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            HistoryDataView()
                .preferredColorScheme(.dark)
        }
    }
} 
