//
//  ActivityHistoryContentView.swift
//  WindRing
//
//  Created by zx on 2025/7/16.
//


import SwiftUI
import Charts

struct ActivityHistoryContentView: View {
    // MARK: - 属性
    @ObservedObject var viewModel: ActivityHistoryViewModel
    @Binding var showingDatePicker: Bool
    @Binding var selectedDate: Date
    @Binding var selectedTimeRange: Int
    
    // 星期和月份标签
    var weekdays: [String] {
        ["sleep_history_weekday_sun".localized, "sleep_history_weekday_mon".localized, "sleep_history_weekday_tue".localized, "sleep_history_weekday_wed".localized, "sleep_history_weekday_thu".localized, "sleep_history_weekday_fri".localized, "sleep_history_weekday_sat".localized].map { $0.localized }
    }
    var monthDays: [String] {
        ["sleep_history_month_w1".localized, "sleep_history_month_w2".localized, "sleep_history_month_w3".localized, "sleep_history_month_w4".localized, "sleep_history_month_w5".localized].map { $0.localized }
    }
    var yearMonths: [String] {
        ["sleep_history_year_jan".localized, "sleep_history_year_feb".localized, "sleep_history_year_mar".localized, "sleep_history_year_apr".localized, "sleep_history_year_may".localized, "sleep_history_year_jun".localized, "sleep_history_year_jul".localized, "sleep_history_year_aug".localized, "sleep_history_year_sep".localized, "sleep_history_year_oct".localized, "sleep_history_year_nov".localized, "sleep_history_year_dec".localized].map { $0.localized }
    }
    
    // 当前显示的时间标签
    var currentTimeLabels: [String] {
        switch selectedTimeRange {
        case 1: return monthDays
        case 2: return yearMonths
        default: return weekdays
        }
    }
    
    // 动态X轴标签
    var xAxisLabels: [String] {
        let calendar = Calendar.current
        
        switch selectedTimeRange {
        case 0: // 周
            return ["sleep_history_weekday_sun".localized, "sleep_history_weekday_mon".localized, "sleep_history_weekday_tue".localized, "sleep_history_weekday_wed".localized, "sleep_history_weekday_thu".localized, "sleep_history_weekday_fri".localized, "sleep_history_weekday_sat".localized]

        case 1: // 月
            let components = viewModel.dateRange.split(separator: "-").map { String($0) }
            guard components.count == 2 else { return [] }
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy.M.d"
            guard let startDate = dateFormatter.date(from: components[0]) else { return [] }
            guard let daysInMonth = calendar.range(of: .day, in: .month, for: startDate)?.count else { return [] }
            return (1...daysInMonth).map { String($0) }

        case 2: // 年
            return yearMonths//DateFormatter().shortMonthSymbols
            
        default:
            return []
        }
    }
    
    // 确保数据数组长度与标签数量一致
    private func ensureDataMatchesLabels<T>(data: [T], defaultValue: T) -> [T] {
        let targetCount = xAxisLabels.count
        if data.count >= targetCount {
            return Array(data.prefix(targetCount))
        } else {
            var result = data
            result.append(contentsOf: Array(repeating: defaultValue, count: targetCount - data.count))
            return result
        }
    }
    
    // 活动得分数据 - 使用ViewModel
    var activityScoreMax: Int {
        viewModel.activityScoreMax
    }
    
    var activityScoreAvg: Int {
        viewModel.activityScoreAvg
    }
    
    var activityScoreMin: Int {
        viewModel.activityScoreMin
    }
    
    var currentActivityScoreData: [Int] {
        switch selectedTimeRange {
        case 1: return viewModel.activityScoreMonthData
        case 2: return viewModel.activityScoreYearData
        default: return viewModel.activityScoreWeekData
        }
    }
    
    // 步数数据 - 使用ViewModel
    var stepsMax: Int {
        viewModel.stepsMax
    }
    
    var stepsAvg: Int {
        viewModel.stepsAvg
    }
    
    var stepsMin: Int {
        viewModel.stepsMin
    }
    
    var currentStepsData: [Int] {
        switch selectedTimeRange {
        case 1: return viewModel.stepsMonthData
        case 2: return viewModel.stepsYearData
        default: return viewModel.stepsWeekData
        }
    }
    
    // 距离数据
    @State private var distanceMax = 8.75
    @State private var distanceAvg = 6.54
    @State private var distanceMin = 4.32
    @State private var distanceWeekData = [5.2, 6.8, 8.7, 7.5, 6.3, 5.1, 5.7]
    @State private var distanceMonthData = [6.1, 6.7, 7.5, 6.4, 5.9]
    @State private var distanceYearData = [5.8, 6.2, 6.7, 7.3, 7.8, 8.2, 7.9, 7.4, 6.8, 6.3, 5.9, 6.5]
    
    var currentDistanceData: [Int] {
        // 将浮点数转换为整数以便在图表中使用
        switch selectedTimeRange {
        case 1: return distanceMonthData.map { Int($0 * 10) }
        case 2: return distanceYearData.map { Int($0 * 10) }
        default: return distanceWeekData.map { Int($0 * 10) }
        }
    }
    
    // 活动卡路里数据
    var caloriesMax: Int {
        viewModel.caloriesMax
    }
    
    var caloriesAvg: Int {
        viewModel.caloriesAvg
    }
    
    var caloriesMin: Int {
        viewModel.caloriesMin
    }
    
    var currentCaloriesData: [Int] {
        switch selectedTimeRange {
        case 1: return viewModel.caloriesMonthData
        case 2: return viewModel.caloriesYearData
        default: return viewModel.caloriesWeekData
        }
    }
    
    // 站立时长数据
    var standingDurationMaxHours: Int {
        viewModel.standingDurationMaxHours
    }
    
    var standingDurationMaxMinutes: Int {
        viewModel.standingDurationMaxMinutes
    }
    
    var standingDurationAvgHours: Int {
        viewModel.standingDurationAvgHours
    }
    
    var standingDurationAvgMinutes: Int {
        viewModel.standingDurationAvgMinutes
    }
    
    var standingDurationMinHours: Int {
        viewModel.standingDurationMinHours
    }
    
    var standingDurationMinMinutes: Int {
        viewModel.standingDurationMinMinutes
    }
    
    var currentStandingDurationData: [Double] {
        switch selectedTimeRange {
        case 1: return viewModel.standingDurationMonthData
        case 2: return viewModel.standingDurationYearData
        default: return viewModel.standingDurationWeekData
        }
    }
    
    // MARK: - 视图
    var body: some View {
        ZStack {
            ScrollView {
                VStack(spacing: 20) {
                    // 加载指示器
                    if viewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                            .frame(height: 40)
                            .padding()
                    }
                    
                    // 错误消息
                    if let errorMessage = viewModel.errorMessage {
                        Text(errorMessage)
                            .foregroundColor(.red)
                            .padding()
                    }
                    
                    // 活动得分模块
                    activityScoreSection
                    
                    // 步数模块
                    stepsSection
                    
                    // 卡路里模块
                    caloriesSection
                    
                    // 站立时长模块
                    standingDurationSection
                    
                    Spacer(minLength: 30)
                }
                .padding(.vertical, 12)
                .padding(.horizontal)
            }
            .background(Color(red: 0.027, green: 0.027, blue: 0.031).edgesIgnoringSafeArea(.all))
            .refreshable {
                Task {
                    await viewModel.refreshData()
                }
            }
            
            // 日历弹框直接覆盖在页面上，而不是作为sheet呈现
            if showingDatePicker {
                datePickerView
                    .zIndex(100) // 确保在最上层
                    .transition(.opacity.animation(.easeInOut(duration: 0.2)))
            }
        }
    }
    
    // MARK: - 子视图
    
    // 活动得分部分
    private var activityScoreSection: some View {
        let yLabels = getYAxisLabels(for: Double(activityScoreMax))
        return VStack(alignment: .leading, spacing: 14) {
            // 标题行
            HStack {
                Image(systemName: "figure.walk.circle")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
                
                Text("activity_history_section_title_score".localized)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.historyActivityTotalScore.rawValue)
                
                Spacer()
                
//                Image(systemName: "info.circle")
//                    .font(.system(size: 14))
//                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            
            // 最大、平均、最小值卡片
            HStack(spacing: 10) {
                metricCardNew(title: "activity_history_metric_max".localized, value: "\(activityScoreMax)", unit: "activity_history_unit_score".localized)
                metricCardNew(title: "activity_history_metric_ave".localized, value: "\(activityScoreAvg)", unit: "activity_history_unit_score".localized)
                metricCardNew(title: "activity_history_metric_min".localized, value: "\(activityScoreMin)", unit: "activity_history_unit_score".localized)
            }
            .padding(.horizontal, 16)
            
            // 图表刻度和值
            ZStack(alignment: .trailing) {
                // 图表刻度
//                VStack(alignment: .trailing, spacing: 0) {
//                    Text(yLabels.top)
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//
//                    Spacer()
//
//                    Text(yLabels.mid)
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//
//                    Spacer()
//
//                    Text("0")
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//                }
//                .frame(width: 20)
//                .padding(.trailing, 8)
                
                // 图表
                ActivityBarChartView(
                    chartData: chartData(for: currentActivityScoreData.map { Double($0) }),
                    style: .gradient(LinearGradient(colors: [Color(hex: "#0072FF"), Color(hex: "#00D2FF")], startPoint: .bottom, endPoint: .top)),
                    maxValue: Double(activityScoreMax),
                    selectedTimeRange: selectedTimeRange,
                    valueFormat: "%.0f"
                )
                .padding(.trailing, 8) // 为刻度预留空间
            }
            .frame(height: 100)
            .padding(.top, 10)
        }
        .padding(.vertical, 16)
        .background(Color(red: 0.1, green: 0.1, blue: 0.12))
    }
    
    // 步数部分
    private var stepsSection: some View {
        let yLabels = getYAxisLabels(for: Double(stepsMax))
        return VStack(alignment: .leading, spacing: 14) {
            // 标题行
            HStack {
                Image(systemName: "shoe")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
                
                Text("activity_history_section_title_steps".localized)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.historyActivitySteps.rawValue)
                
                Spacer()
                
//                Image(systemName: "info.circle")
//                    .font(.system(size: 14))
//                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            
            // 最大、平均、最小值卡片
            HStack(spacing: 10) {
                metricCardNew(title: "activity_history_metric_max".localized, value: "\(stepsMax)", unit: "activity_history_unit_steps".localized)
                metricCardNew(title: "activity_history_metric_ave".localized, value: "\(stepsAvg)", unit: "activity_history_unit_steps".localized)
                metricCardNew(title: "activity_history_metric_min".localized, value: "\(stepsMin)", unit: "activity_history_unit_steps".localized)
            }
            .padding(.horizontal, 16)
            
            // 图表刻度和值
            ZStack(alignment: .trailing) {
//                // 图表刻度
//                VStack(alignment: .trailing, spacing: 0) {
//                    Text(yLabels.top)
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//
//                    Spacer()
//
//                    Text(yLabels.mid)
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//
//                    Spacer()
//
//                    Text("0")
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//                }
//                .frame(width: 20)
//                .padding(.trailing, 8)
                
                // 图表
                ActivityBarChartView(
                    chartData: chartData(for: currentStepsData.map { Double($0) }),
                    style: .solid(Color.cyan),
                    maxValue: Double(stepsMax),
                    selectedTimeRange: selectedTimeRange,
                    valueFormat: "%.0f"
                )
                .padding(.trailing, 8) // 为刻度预留空间
            }
            .frame(height: 100)
            .padding(.top, 10)
        }
        .padding(.vertical, 16)
        .background(Color(red: 0.1, green: 0.1, blue: 0.12))
    }
    
    // 卡路里部分
    private var caloriesSection: some View {
        let yLabels = getYAxisLabels(for: Double(caloriesMax))
        return VStack(alignment: .leading, spacing: 14) {
            // 标题行
            HStack {
                Image(systemName: "flame.fill")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
                
                Text("activity_history_section_title_calories".localized)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.historyActivityCalories.rawValue)
                
                Spacer()
                
//                Image(systemName: "info.circle")
//                    .font(.system(size: 14))
//                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            
            // 最大、平均、最小值卡片
            HStack(spacing: 10) {
                metricCardNew(title: "activity_history_metric_max".localized, value: "\(caloriesMax)", unit: "activity_history_unit_kcal".localized)
                metricCardNew(title: "activity_history_metric_ave".localized, value: "\(caloriesAvg)", unit: "activity_history_unit_kcal".localized)
                metricCardNew(title: "activity_history_metric_min".localized, value: "\(caloriesMin)", unit: "activity_history_unit_kcal".localized)
            }
            .padding(.horizontal, 16)
            
            // 图表刻度和值
            ZStack(alignment: .trailing) {
                // 图表刻度
//                VStack(alignment: .trailing, spacing: 0) {
//                    Text(yLabels.top)
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//
//                    Spacer()
//
//                    Text(yLabels.mid)
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//
//                    Spacer()
//
//                    Text("0")
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//                }
//                .frame(width: 20)
//                .padding(.trailing, 8)
                
                // 图表
                ActivityBarChartView(
                    chartData: chartData(for: currentCaloriesData.map { Double($0) }),
                    style: .solid(Color.red),
                    maxValue: Double(caloriesMax),
                    selectedTimeRange: selectedTimeRange,
                    valueFormat: "%.0f"
                )
                .padding(.trailing, 8) // 为刻度预留空间
            }
            .frame(height: 100)
            .padding(.top, 10)
        }
        .padding(.vertical, 16)
        .background(Color(red: 0.1, green: 0.1, blue: 0.12))
    }
    
    // 站立时长部分
    private var standingDurationSection: some View {
        let yLabels = getYAxisLabels(for: Double(standingDurationMaxHours > 0 ? standingDurationMaxHours + 1 : 6), isFloat: true)
        return VStack(alignment: .leading, spacing: 14) {
                // 标题行
                HStack {
                Image(systemName: "figure.stand")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
                
                Text("activity_history_section_title_standing".localized)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                    
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.historyActivityStandingDuration.rawValue)
                Spacer()
                
//                Image(systemName: "info.circle")
//                    .font(.system(size: 14))
//                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            
            // 最大、平均、最小值卡片
            HStack(spacing: 10) {
                metricCardNew(
                    title: "activity_history_metric_max".localized,
                    value: "\(standingDurationMaxHours) \("hr".localized) \(standingDurationMaxMinutes)",
                    unit: "min".localized
                )
                metricCardNew(
                    title: "activity_history_metric_ave".localized,
                    value: "\(standingDurationAvgHours) \("hr".localized) \(standingDurationAvgMinutes)",
                    unit: "min".localized
                )
                metricCardNew(
                    title: "activity_history_metric_min".localized,
                    value: "\(standingDurationMinHours) \("hr".localized) \(standingDurationMinMinutes)",
                    unit: "min".localized
                )
            }
            .padding(.horizontal, 16)
            
            // 图表刻度和值
            ZStack(alignment: .trailing) {
                // 图表刻度
//                VStack(alignment: .trailing, spacing: 0) {
//                    Text(yLabels.top)
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//
//                    Spacer()
//
//                    Text(yLabels.mid)
//                        .font(.system(size: 10))
//                        .foregroundColor(.gray)
//
//                        Spacer()
//
//                    Text("0")
//                            .font(.system(size: 10))
//                            .foregroundColor(.gray)
//                    }
//                .frame(width: 20)
//                .padding(.trailing, 8)
                
                // 图表 - 曲线图
                ActivityBarChartView(
                    chartData: chartData(for: currentStandingDurationData),
                    style: .solid(Color.blue),
                    maxValue: Double(standingDurationMaxHours > 0 ? standingDurationMaxHours + 1 : 6),
                    selectedTimeRange: selectedTimeRange,
                    valueFormat: "%.1f"
                )
                .padding(.trailing, 8) // 为刻度预留空间
            }
            .frame(height: 100)
            .padding(.top, 10)
        }
        .padding(.vertical, 16)
        .background(Color(red: 0.1, green: 0.1, blue: 0.12))
    }
    
    // 日期选择器视图
    private var datePickerView: some View {
        CustomCalendarView(
            selectedDate: $selectedDate,
            selectedTimeRange: $selectedTimeRange,
            onClose: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    showingDatePicker = false
                }
            },
            onDateSelected: { date in
                // 更新日期并重新加载数据
                selectedDate = date
                updateDateRangeFromSelection()
                withAnimation(.easeInOut(duration: 0.2)) {
                    showingDatePicker = false
                }
            }
        )
    }
    
    // 自定义日历视图组件
    struct CustomCalendarView: View {
        @Binding var selectedDate: Date
        @Binding var selectedTimeRange: Int // 绑定时间范围选择
        var onClose: () -> Void
        var onDateSelected: (Date) -> Void
        
        @State private var currentMonth: Date
        
        private let calendar = Calendar.current
        private let monthFormatter = DateFormatter()
        private let dayFormatter = DateFormatter()
        private let weekdayFormatter = DateFormatter()
        
        init(selectedDate: Binding<Date>, selectedTimeRange: Binding<Int>, onClose: @escaping () -> Void, onDateSelected: @escaping (Date) -> Void) {
            self._selectedDate = selectedDate
            self._selectedTimeRange = selectedTimeRange
            self.onClose = onClose
            self.onDateSelected = onDateSelected
            self._currentMonth = State(initialValue: selectedDate.wrappedValue)
            
            monthFormatter.dateFormat = "MMMM yyyy"
            dayFormatter.dateFormat = "d"
            weekdayFormatter.dateFormat = "E"
        }
        
        var body: some View {
            ZStack {
                // 磨砂半透明背景
                Color.black.opacity(0.7)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation {
                            onClose()
                        }
                    }
                
                VStack(spacing: 15) {
                    // 月份导航
                    HStack {
                        Button(action: {
                            previousMonth()
                        }) {
                            Image(systemName: "chevron.left")
                                .foregroundColor(.white)
                                .font(.system(size: 18))
                        }
                        
                        Spacer()
                        
                        Text(monthFormatter.string(from: currentMonth))
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Button(action: {
                            nextMonth()
                        }) {
                            Image(systemName: "chevron.right")
                                .foregroundColor(.white)
                                .font(.system(size: 18))
                        }
                    }
                    .padding(.horizontal)
                    .padding(.top, 15)
                    
                    // 星期标题
                    HStack(spacing: 0) {
                        ForEach(getDaysOfWeek(), id: \.self) { day in
                            Text(day)
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .frame(maxWidth: .infinity)
                        }
                    }
                    .padding(.horizontal, 5)
                    .padding(.top, 10)
                    
                    // 日期网格
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 10) {
                        ForEach(extractDates()) { dateValue in
                            CalendarDayView(
                                dateValue: dateValue,
                                selectedDate: $selectedDate,
                                onDateSelected: onDateSelected
                            )
                            .frame(height: 35)
                        }
                    }
                    .padding(.horizontal, 5)
                    
                    // 底部按钮区域
                    HStack {
                        Spacer()
                        
                        // 返回今天按钮
                        Button(action: {
                            selectedDate = Date()
                            onDateSelected(Date())
                        }) {
                            Text("sleep_history_calendar_back_to_today".localized)
                                .font(.system(size: 15))
                                .foregroundColor(.white)
                                .padding(.vertical, 6)
                                .padding(.horizontal, 14)
                                .background(Capsule().stroke(Color.white.opacity(0.4), lineWidth: 1))
                        }
                    }
                    .padding(.top, 5)
                    .padding(.bottom, 15)
                    .padding(.trailing, 15)
                }
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(red: 0.10, green: 0.12, blue: 0.18))
                )
                .frame(width: UIScreen.main.bounds.width * 0.85)
                .padding(.horizontal, 20)
            }
        }
        
        // 获取星期标题
        private func getDaysOfWeek() -> [String] {
            // 直接返回固定顺序的星期标题，以周日开始
            return ["sleep_history_weekday_sun".localized, "sleep_history_weekday_mon".localized, "sleep_history_weekday_tue".localized, "sleep_history_weekday_wed".localized, "sleep_history_weekday_thu".localized, "sleep_history_weekday_fri".localized, "sleep_history_weekday_sat".localized]
        }
        
        // 提取当月日期
        private func extractDates() -> [DateValue] {
            let monthStart = startOfMonth(for: currentMonth)
            let monthEnd = endOfMonth(for: currentMonth)
            
            let calendar = Calendar.current
            let startDayOfWeek = calendar.component(.weekday, from: monthStart)
            let daysInMonth = calendar.dateComponents([.day], from: monthStart, to: monthEnd).day! + 1
            
            var dateValues = [DateValue]()
            
            // 调整星期偏移
            // weekday: 1=周日, 2=周一, ..., 7=周六
            // 所以从周日开始，偏移量就是weekday-1
            let offset = startDayOfWeek - 1
            
            // 填充前置空白
            for _ in 0..<offset {
                dateValues.append(DateValue(day: 0, date: Date(), isCurrentMonth: false))
            }
            
            // 填充当月日期
            for day in 1...daysInMonth {
                if let date = calendar.date(byAdding: .day, value: day - 1, to: monthStart) {
                    dateValues.append(DateValue(day: day, date: date, isCurrentMonth: true))
                }
            }
            
            return dateValues
        }
        
        // 获取月份开始日期
        private func startOfMonth(for date: Date) -> Date {
            let components = calendar.dateComponents([.year, .month], from: date)
            return calendar.date(from: components)!
        }
        
        // 获取月份结束日期
        private func endOfMonth(for date: Date) -> Date {
            var components = DateComponents()
            components.month = 1
            components.day = -1
            return calendar.date(byAdding: components, to: startOfMonth(for: date))!
        }
        
        // 切换到上个月
        private func previousMonth() {
            if let newDate = calendar.date(byAdding: .month, value: -1, to: currentMonth) {
                currentMonth = newDate
            }
        }
        
        // 切换到下个月
        private func nextMonth() {
            if let newDate = calendar.date(byAdding: .month, value: 1, to: currentMonth) {
                currentMonth = newDate
            }
        }
    }
    
    // 日期值模型
    struct DateValue: Identifiable {
        var id = UUID()
        var day: Int
        var date: Date
        var isCurrentMonth: Bool
    }
    
    // 日历天数视图
    struct CalendarDayView: View {
        let dateValue: DateValue
        @Binding var selectedDate: Date
        var onDateSelected: (Date) -> Void
        
        private let calendar = Calendar.current
        
        var isSelected: Bool {
            calendar.isDate(dateValue.date, inSameDayAs: selectedDate)
        }
        
        var isToday: Bool {
            calendar.isDateInToday(dateValue.date)
        }
        
        // 判断是否为未来日期
        var isFutureDate: Bool {
            if dateValue.day == 0 { return false }
            return calendar.compare(dateValue.date, to: Date(), toGranularity: .day) == .orderedDescending
        }
        
        // 判断是否为过去日期（含今天）
        var isPastOrToday: Bool {
            if dateValue.day == 0 { return false }
            return calendar.compare(dateValue.date, to: Date(), toGranularity: .day) != .orderedDescending
        }
        
        var body: some View {
            VStack(spacing: 6) {
                if dateValue.day != 0 {
                    Button(action: {
                        onDateSelected(dateValue.date)
                    }) {
                        Text("\(dateValue.day)")
                            .font(.system(size: 16))
                            .fontWeight(isSelected ? .medium : .regular)
                            .foregroundColor(
                                isSelected ? .white :
                                    dateValue.isCurrentMonth ? .white : .gray.opacity(0.6)
                            )
                    }
                    
                    // 底部指示点
                    if isSelected {
                        // 选中日期显示蓝色点
                        Circle()
                            .fill(Color.blue)
                            .frame(width: 5, height: 5)
                    } else if isPastOrToday {
                        // 过去日期显示灰色点
                        Circle()
                            .fill(Color.gray.opacity(0.5))
                            .frame(width: 5, height: 5)
                    } else {
                        // 占位空间
                        Circle()
                            .fill(Color.clear)
                            .frame(width: 5, height: 5)
                    }
                } else {
                    // 空白占位
                    Text("")
                        .font(.system(size: 16))
                        .frame(maxWidth: .infinity)
                    
                    // 占位空间
                    Circle()
                        .fill(Color.clear)
                        .frame(width: 5, height: 5)
                }
            }
        }
    }
    
    // MARK: - 辅助视图
    
    // 新版数据卡片 (更大、更独立的卡片)
    private func metricCardNew(title: String, value: String, unit: String) -> some View {
        VStack(alignment: .center, spacing: 4) {
            Text(title)
                .font(.system(size: 14))
                .foregroundColor(.gray)
            
            HStack(alignment: .firstTextBaseline, spacing: 2) {
            Text(value)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.white)
                
                Text(unit)
                    .font(.system(size: 12))
                .foregroundColor(.white)
        }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(Color(red: 0.13, green: 0.13, blue: 0.15))
        .cornerRadius(10)
    }
    
    // 根据选择的日期更新日期范围
    private func updateDateRangeFromSelection() {
        viewModel.updateDateRange(from: selectedDate, for: selectedTimeRange)
    }
    
    private func chartData(for data: [Double]) -> [ActivityBarChartView.ChartDataPoint] {
        let labels = xAxisLabels
        var points: [ActivityBarChartView.ChartDataPoint] = []
        for i in 0..<labels.count {
            let value = i < data.count ? data[i] : 0.0
            points.append(.init(label: labels[i], value: value))
        }
        return points
    }
    
    private func getYAxisLabels(for maxValue: Double, isFloat: Bool = false) -> (top: String, mid: String) {
        let topValue: Double
        let midValue: Double

        if maxValue > 0 {
            topValue = maxValue * 1.2
            midValue = topValue / 2
        } else {
            topValue = 100
            midValue = 50
        }
        
        let format = isFloat ? "%.1f" : "%.0f"
        
        return (String(format: format, topValue), String(format: format, midValue))
    }
}

