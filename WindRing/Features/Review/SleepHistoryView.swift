import SwiftUI
import UIKit
import Charts
import Combine

// MARK: - Shimmer Effect 扩展
extension View {
    func shimmer() -> some View {
        self.modifier(ShimmerEffect())
    }
}

// 闪光效果修饰器
struct ShimmerEffect: ViewModifier {
    @State private var phase: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .overlay(
                GeometryReader { geo in
                    LinearGradient(
                        gradient: Gradient(stops: [
                            .init(color: .clear, location: phase - 0.5),
                            .init(color: Color.white.opacity(0.3), location: phase),
                            .init(color: .clear, location: phase + 0.5)
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    .mask(content)
                    .blur(radius: 2)
                    .onAppear {
                        withAnimation(Animation.linear(duration: 1.5).repeatForever(autoreverses: false)) {
                            self.phase = 1
                        }
                    }
                }
            )
    }
}

// MARK: - Heart Rate Data Model
// This struct is no longer needed for the new index-based chart and will be removed.


// 水平线辅助视图
struct Line: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: 0, y: rect.midY))
        path.addLine(to: CGPoint(x: rect.width, y: rect.midY))
        return path
    }
}

// 自定义不同圆角的矩形形状
struct SleepHistoryRoundedCorners: Shape {
    var topLeft: CGFloat = 0
    var topRight: CGFloat = 0
    var bottomLeft: CGFloat = 0
    var bottomRight: CGFloat = 0
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        let width = rect.size.width
        let height = rect.size.height
        
        // 确保半径不超过宽/高的一半
        let tl = min(min(topLeft, width/2), height/2)
        let tr = min(min(topRight, width/2), height/2)
        let bl = min(min(bottomLeft, width/2), height/2)
        let br = min(min(bottomRight, width/2), height/2)
        
        // 开始绘制路径
        path.move(to: CGPoint(x: width / 2, y: 0))
        path.addLine(to: CGPoint(x: width - tr, y: 0))
        path.addArc(center: CGPoint(x: width - tr, y: tr),
                    radius: tr,
                    startAngle: Angle(degrees: -90),
                    endAngle: Angle(degrees: 0),
                    clockwise: false)
        
        path.addLine(to: CGPoint(x: width, y: height - br))
        path.addArc(center: CGPoint(x: width - br, y: height - br),
                    radius: br,
                    startAngle: Angle(degrees: 0),
                    endAngle: Angle(degrees: 90),
                    clockwise: false)
        
        path.addLine(to: CGPoint(x: bl, y: height))
        path.addArc(center: CGPoint(x: bl, y: height - bl),
                    radius: bl,
                    startAngle: Angle(degrees: 90),
                    endAngle: Angle(degrees: 180),
                    clockwise: false)
        
        path.addLine(to: CGPoint(x: 0, y: tl))
        path.addArc(center: CGPoint(x: tl, y: tl),
                    radius: tl,
                    startAngle: Angle(degrees: 180),
                    endAngle: Angle(degrees: 270),
                    clockwise: false)
        
        path.closeSubpath()
        
        return path
    }
}


/// 睡眠历史详情页面
struct SleepHistoryView: View {
    // MARK: - Properties
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel = SleepHistoryViewModel()
    @ObservedObject private var userSettings = UserSettings.shared
    
    // Detail sheet state
    @State private var showingDetailSheet = false
    @State private var selectedDetailTitle = ""
    @State private var selectedDetailIcon = ""
    @State private var selectedDetailMaxValue = ""
    @State private var selectedDetailAvgValue = ""
    @State private var selectedDetailMinValue = ""
    @State private var selectedDetailColor = Color.blue
    @State private var selectedDetailData: [Int] = []
    @State private var selectedDetailShowPercentages = false
    @State private var selectedDetailChartType = 0 // 0: bar, 1: line
    
    // MARK: - Initialization
    init() {
        // Configure navigation bar appearance
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor(red: 0.08, green: 0.09, blue: 0.13, alpha: 1.0)
        appearance.shadowColor = .clear
        
        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                // Time range selector
                timeRangeSelector
                    .background(Color(red: 0.08, green: 0.09, blue: 0.13))
                
                SleepHistoryContenView(
                    viewModel: viewModel,
                    userSettings: userSettings
                )
            }
            .background(
                Color.appBackground
                    .edgesIgnoringSafeArea(.all)
            )
            .navigationBarBackButtonHidden(true)
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(Color(red: 0.08, green: 0.09, blue: 0.13).opacity(1.0), for: .navigationBar)
            .toolbarBackground(.visible, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack(spacing: 4) {
                            Image("back")
                                .font(.system(size: 14))
                            Text("sleep_history_nav_title".localized)
                                .font(.custom("PingFang-SC-Heavy", size: 19))
                        }
                        .foregroundColor(.white)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            viewModel.showingDatePicker = true
                        }
                    }) {
                        HStack {
                            Text(viewModel.dateRange)
                                .font(.custom("DIN-Medium", size: 13))
                                .foregroundColor(.white)
                                .lineLimit(nil)
                            
                            Image("down")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 11, height: 11)
                        }
                        .frame(height: 9.5)
                    }
                }
            }
            
            if viewModel.showingDatePicker {
                datePickerView
                    .zIndex(100) // 确保在最上层
                    .transition(.opacity.animation(.easeInOut(duration: 0.2)))
            }
        }
        .onChange(of: viewModel.selectedTimeRange) { _ in
            viewModel.updateDisplayRangeAndFetchData()
        }
        .onAppear {
            viewModel.selectedDate = Date()
            viewModel.updateDisplayRangeAndFetchData()
        }
    }
    
    // MARK: - Time Range Selector
    private var timeRangeSelector: some View {
        VStack(spacing: 0) {
            HStack(spacing: 0) {
                ForEach(0..<viewModel.timeRanges.count, id: \.self) { index in
                    Button(action: {
                        withAnimation {
                            viewModel.selectedTimeRange = index
                        }
                    }) {
                        Text(viewModel.timeRanges[index])
                            .font(.system(size: 15, weight: .semibold))
                            .foregroundColor(.white)
                            .opacity(viewModel.selectedTimeRange == index ? 1.0 : 0.6)
                            .frame(maxWidth: .infinity)
                            .frame(height: 36)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(viewModel.selectedTimeRange == index ? Color.blue : Color.clear)
                            )
                            .contentShape(Rectangle())
                    }
                    .padding(.horizontal, 5)
                }
            }
            .padding(.horizontal, 0)
            .padding(.vertical, 10)
            .background(Color(red: 0.08, green: 0.09, blue: 0.13))
        }
    }
    
    private var datePickerView: some View {
        CustomCalendarView(
            selectedDate: $viewModel.selectedDate,
            selectedTimeRange: $viewModel.selectedTimeRange,
            onClose: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    viewModel.showingDatePicker = false
                }
            },
            onDateSelected: { date in
                viewModel.selectedDate = date
                viewModel.updateDisplayRangeAndFetchData()
                withAnimation(.easeInOut(duration: 0.2)) {
                    viewModel.showingDatePicker = false
                }
            }
        )
    }
    
    // 自定义日历视图组件
    struct CustomCalendarView: View {
        @Binding var selectedDate: Date
        @Binding var selectedTimeRange: Int // 绑定时间范围选择
        var onClose: () -> Void
        var onDateSelected: (Date) -> Void
        
        @State private var currentMonth: Date
        
        private let calendar = Calendar.current
        private let monthFormatter = DateFormatter()
        private let dayFormatter = DateFormatter()
        private let weekdayFormatter = DateFormatter()
        
        init(selectedDate: Binding<Date>, selectedTimeRange: Binding<Int>, onClose: @escaping () -> Void, onDateSelected: @escaping (Date) -> Void) {
            self._selectedDate = selectedDate
            self._selectedTimeRange = selectedTimeRange
            self.onClose = onClose
            self.onDateSelected = onDateSelected
            self._currentMonth = State(initialValue: selectedDate.wrappedValue)
            
            monthFormatter.dateFormat = "MMMM yyyy"
            dayFormatter.dateFormat = "d"
            weekdayFormatter.dateFormat = "E"
        }
        
        var body: some View {
            ZStack {
                // 磨砂半透明背景
                Color.black.opacity(0.7)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation {
                            onClose()
                        }
                    }
                
                VStack(spacing: 15) {
                    // 月份导航
                    HStack {
                        Button(action: {
                            previousMonth()
                        }) {
                            Image(systemName: "chevron.left")
                                .foregroundColor(.white)
                                .font(.system(size: 18))
                        }
                        
                        Spacer()
                        
                        Text(monthFormatter.string(from: currentMonth))
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Button(action: {
                            nextMonth()
                        }) {
                            Image(systemName: "chevron.right")
                                .foregroundColor(.white)
                                .font(.system(size: 18))
                        }
                    }
                    .padding(.horizontal)
                    .padding(.top, 15)
                    
                    // 星期标题
                    HStack(spacing: 0) {
                        ForEach(getDaysOfWeek(), id: \.self) { day in
                            Text(day)
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .frame(maxWidth: .infinity)
                        }
                    }
                    .padding(.horizontal, 5)
                    .padding(.top, 10)
                    
                    // 日期网格
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 10) {
                        ForEach(extractDates()) { dateValue in
                            CalendarDayView(
                                dateValue: dateValue,
                                selectedDate: $selectedDate,
                                onDateSelected: onDateSelected
                            )
                            .frame(height: 35)
                        }
                    }
                    .padding(.horizontal, 5)
                    
                    // 底部按钮区域
                    HStack {
                        Spacer()
                        
                        // 返回今天按钮
                        Button(action: {
                            selectedDate = Date()
                            onDateSelected(Date())
                        }) {
                            Text("sleep_history_calendar_back_to_today".localized)
                                .font(.system(size: 15))
                                .foregroundColor(.white)
                                .padding(.vertical, 6)
                                .padding(.horizontal, 14)
                                .background(Capsule().stroke(Color.white.opacity(0.4), lineWidth: 1))
                        }
                    }
                    .padding(.top, 5)
                    .padding(.bottom, 15)
                    .padding(.trailing, 15)
                }
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(red: 0.10, green: 0.12, blue: 0.18))
                )
                .frame(width: UIScreen.main.bounds.width * 0.85)
                .padding(.horizontal, 20)
            }
        }
        
        // 获取星期标题
        private func getDaysOfWeek() -> [String] {
            // 直接返回固定顺序的星期标题，以周日开始
            return ["sleep_history_weekday_sun", "sleep_history_weekday_mon", "sleep_history_weekday_tue", "sleep_history_weekday_wed", "sleep_history_weekday_thu", "sleep_history_weekday_fri", "sleep_history_weekday_sat"].map { $0.localized }
        }
        
        // 提取当月日期
        private func extractDates() -> [DateValue] {
            let monthStart = startOfMonth(for: currentMonth)
            let monthEnd = endOfMonth(for: currentMonth)
            
            let calendar = Calendar.current
            let startDayOfWeek = calendar.component(.weekday, from: monthStart)
            let daysInMonth = calendar.dateComponents([.day], from: monthStart, to: monthEnd).day! + 1
            
            var dateValues = [DateValue]()
            
            // 调整星期偏移
            // weekday: 1=周日, 2=周一, ..., 7=周六
            // 所以从周日开始，偏移量就是weekday-1
            let offset = startDayOfWeek - 1
            
            // 填充前置空白
            for _ in 0..<offset {
                dateValues.append(DateValue(day: 0, date: Date(), isCurrentMonth: false))
            }
            
            // 填充当月日期
            for day in 1...daysInMonth {
                if let date = calendar.date(byAdding: .day, value: day - 1, to: monthStart) {
                    dateValues.append(DateValue(day: day, date: date, isCurrentMonth: true))
                }
            }
            
            return dateValues
        }
        
        // 获取月份开始日期
        private func startOfMonth(for date: Date) -> Date {
            let components = calendar.dateComponents([.year, .month], from: date)
            return calendar.date(from: components)!
        }
        
        // 获取月份结束日期
        private func endOfMonth(for date: Date) -> Date {
            var components = DateComponents()
            components.month = 1
            components.day = -1
            return calendar.date(byAdding: components, to: startOfMonth(for: date))!
        }
        
        // 切换到上个月
        private func previousMonth() {
            if let newDate = calendar.date(byAdding: .month, value: -1, to: currentMonth) {
                currentMonth = newDate
            }
        }
        
        // 切换到下个月
        private func nextMonth() {
            if let newDate = calendar.date(byAdding: .month, value: 1, to: currentMonth) {
                currentMonth = newDate
            }
        }
    }
}

// MARK: - 可交互的柱状图组件
struct InteractiveBarChart: View {
    let data: [Int]
    let yMax: Int
    let timeLabels: [String]
    
    @State private var selectedIndex: Int?
    
    private var barFill: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [Color(hex: "#00D2FF"), Color(hex: "#0072FF")]),
            startPoint: .top,
            endPoint: .bottom
        )
    }
    
    var body: some View {
        VStack(spacing: 8) {
            HStack(spacing: 8) {
                // 图表区域
        GeometryReader { geometry in
                    ZStack(alignment: .bottom) {
                        // Y轴刻度线
                        VStack(spacing: 0) {
                            Rectangle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(height: 1)
                            Spacer()
                            Rectangle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(height: 1)
                            Spacer()
                            Rectangle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(height: 1)
                        }
                        
                        // 数据柱
                        HStack(alignment: .bottom) {
                            ForEach(data.indices, id: \.self) { index in
                                let barHeight = max(0, (CGFloat(data[index]) / CGFloat(yMax)) * geometry.size.height)
                                
                                VStack(spacing: 6) {
                                    if selectedIndex == index {
                                        Text("\(data[index])")
                                            .font(.caption.bold())
                                            .foregroundColor(.white)
                                            .padding(.vertical, 4)
                                            .padding(.horizontal, 8)
                                            .background(Color.black.opacity(0.7))
                                            .clipShape(Capsule())
                                            .transition(.opacity.animation(.easeIn(duration: 0.1)))
                                            .zIndex(1)
                                    }
                                    
                                    // 柱子本身
                                    SleepHistoryRoundedCorners(topLeft: 9, topRight: 9, bottomLeft: 4, bottomRight: 4)
                                        .fill(barFill)
                                        .frame(width: 20, height: barHeight)
                                }
                                .frame(maxWidth: .infinity)
                            }
                        }
                    }
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { value in
                                let barAreaWidth = geometry.size.width / CGFloat(data.count)
                                let index = Int(round((value.location.x - barAreaWidth / 2) / barAreaWidth))
                                
                                if index >= 0 && index < data.count {
                                    self.selectedIndex = index
                                }
                            }
                            .onEnded { _ in
                                self.selectedIndex = nil
                            }
                    )
                }
                
                // Y轴刻度标签
                VStack {
                    Text("\(yMax)")
                    Spacer()
                    Text("\(yMax / 2)")
                    Spacer()
                    Text("0")
                }
                .font(.system(size: 10))
                .foregroundColor(.gray)
                .frame(width: 20)
            }
            .frame(height: 150)
            
            // X轴时间标签
            HStack {
                ForEach(timeLabels, id: \.self) { label in
                    Text(label)
                        .font(.system(size: 10))
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity)
                }
            }
            .padding(.trailing, 28) // Align with chart area
        }
    }
}

// MARK: - 可交互的线性图组件
struct InteractiveLineChart: View {
    let data: [Int] // Data in minutes
    let timeLabels: [String]
    
    @State private var selectedIndex: Int?

    private func formatDuration(minutes: Int) -> String {
        let hours = minutes / 60
        let mins = minutes % 60
        return "\(hours)h \(mins)m"
    }
    
    var body: some View {
        let yMaxHours = Double((data.max() ?? 0) / 60 + 1)
        
        VStack(spacing: 8) {
            HStack(spacing: 8) {
                GeometryReader { geometry in
                    ZStack {
                        // Y-axis grid lines
                        VStack(spacing: 0) {
                            Rectangle().fill(Color.gray.opacity(0.3)).frame(height: 1)
                            Spacer()
                            Rectangle().fill(Color.gray.opacity(0.3)).frame(height: 1)
                            Spacer()
                            Rectangle().fill(Color.gray.opacity(0.3)).frame(height: 1)
                        }

                        // Line Path
                        if !data.isEmpty, data.count > 1 {
                            let stepX = geometry.size.width / CGFloat(data.count - 1)
                            Path { path in
                                for index in 1..<data.count {
                                    if data[index] > 0 && data[index-1] > 0 {
                                        let yPositionPrev = geometry.size.height - (CGFloat(data[index-1]) / 60.0 / CGFloat(yMaxHours)) * geometry.size.height
                                        let prevPoint = CGPoint(x: CGFloat(index - 1) * stepX, y: yPositionPrev)
                                        path.move(to: prevPoint)
                                        
                                        let yPositionCurr = geometry.size.height - (CGFloat(data[index]) / 60.0 / CGFloat(yMaxHours)) * geometry.size.height
                                        let currPoint = CGPoint(x: CGFloat(index) * stepX, y: yPositionCurr)
                                        path.addLine(to: currPoint)
                                    }
                                }
                            }
                            .stroke(Color(red: 0.7, green: 0.3, blue: 0.9), lineWidth: 2)
                        }

                        // Data points (circles)
                        if !data.isEmpty {
                            let stepX = data.count > 1 ? geometry.size.width / CGFloat(data.count - 1) : geometry.size.width / 2
                            ForEach(data.indices, id: \.self) { index in
                                if data[index] > 0 {
                                    let yPosition = geometry.size.height - (CGFloat(data[index]) / 60.0 / CGFloat(yMaxHours)) * geometry.size.height
                                    let xPosition = data.count > 1 ? CGFloat(index) * stepX : stepX
                                    Circle()
                                        .fill(Color(red: 0.7, green: 0.3, blue: 0.9))
                                        .frame(width: 8, height: 8)
                                        .position(x: xPosition, y: yPosition)
                                }
                            }
                        }
                        
                        // Vertical line and value display for gesture
                        if let selectedIndex = selectedIndex, !data.isEmpty, data[selectedIndex] > 0 {
                             let stepX = data.count > 1 ? geometry.size.width / CGFloat(data.count - 1) : geometry.size.width / 2
                             let xPosition = data.count > 1 ? CGFloat(selectedIndex) * stepX : stepX
                             
                             let yPosition = geometry.size.height - (CGFloat(data[selectedIndex]) / 60.0 / CGFloat(yMaxHours)) * geometry.size.height
                             
                             // Vertical line
                                Rectangle()
                                 .fill(Color.white.opacity(0.5))
                                 .frame(width: 1, height: geometry.size.height)
                                 .position(x: xPosition, y: geometry.size.height / 2)

                            // Adjust tooltip position to stay within bounds
                            let tooltipEstimatedHalfWidth: CGFloat = 35
                            var finalXPosition = xPosition
//                            if finalXPosition < tooltipEstimatedHalfWidth {
//                                finalXPosition = tooltipEstimatedHalfWidth
//                            } else if finalXPosition > geometry.size.width - tooltipEstimatedHalfWidth {
//                                finalXPosition = geometry.size.width - tooltipEstimatedHalfWidth
//                            }
                             
                            // Value Text
                            Text(formatDuration(minutes: data[selectedIndex]))
                                .font(.caption.bold())
                                .foregroundColor(.white)
                                .padding(.vertical, 4)
                                .padding(.horizontal, 8)
                                .background(Color.black.opacity(0.7))
                                .clipShape(Capsule())
                                .position(x: finalXPosition, y: yPosition - 20)
                                .transition(.opacity.animation(.easeIn(duration: 0.1)))
                                .zIndex(1)
                        }
                    }
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { value in
                                let index: Int
                                if !data.isEmpty {
                                    if data.count > 1 {
                                        let stepX = geometry.size.width / CGFloat(data.count - 1)
                                        let rawIndex = value.location.x / stepX
                                        index = Int(round(rawIndex))
                                    } else {
                                        index = 0
                                    }
                                    
                                    if index >= 0 && index < data.count {
                                        self.selectedIndex = index
                                    }
                                }
                            }
                            .onEnded { _ in
                                self.selectedIndex = nil
                            }
                    )
                }
                
                // Y-axis labels
                VStack {
                    let yTop = Int(yMaxHours)
                    let yMid = Int(yMaxHours / 2)
                    
                    if yTop > 0 {
                        Text("\(yTop)h")
                        Spacer()
                        if yMid > 0 && yMid < yTop {
                             Text("\(yMid)h")
                             Spacer()
                        }
                        Text("0h")
                    }
                }
                .font(.system(size: 10))
                                    .foregroundColor(.gray)
                .frame(width: 25)
            }
            .frame(height: 150)
            
            // X-axis labels
            HStack {
                ForEach(timeLabels, id: \.self) { label in
                    Text(label)
                        .font(.system(size: 10))
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity)
                }
            }
            .padding(.trailing, 33) // Align with chart area
        }
    }
}



struct SleepHistoryContenView: View {
    @ObservedObject var viewModel: SleepHistoryViewModel
    @ObservedObject var userSettings: UserSettings

    private var hrvRecordsForChart: [SleepHistoryHeartRateRecord] {
        let sortedRecords = viewModel.sleepHRVModel.records.sorted { record1, record2 in
            (TimeInterval(record1.time) ?? 0) < (TimeInterval(record2.time) ?? 0)
        }
        
        return sortedRecords.enumerated().map { index, record in
            return SleepHistoryHeartRateRecord(
                id: index,
                heartRate: record.hrv,
                time: record.time,
                maxHeartRate: record.maxHRV,
                minHeartRate: record.minHRV
            )
        }
    }

    private var spo2RecordsForChart: [SleepHistoryHeartRateRecord] {
        let sortedRecords = viewModel.sleepSpO2Model.records.sorted { record1, record2 in
            (TimeInterval(record1.time) ?? 0) < (TimeInterval(record2.time) ?? 0)
        }
        
        return sortedRecords.enumerated().map { index, record in
            return SleepHistoryHeartRateRecord(
                id: index,
                heartRate: record.spO2,
                time: record.time,
                maxHeartRate: record.maxSpO2,
                minHeartRate: record.minSpO2
            )
        }
    }
    
    // Placeholder for loading view
    private var loadingView: some View {
        ProgressView()
            .progressViewStyle(CircularProgressViewStyle())
            .scaleEffect(1.5)
            .padding()
    }
    
    // Placeholder for skeleton chart view
    private func skeletonBarChartView() -> some View {
        Rectangle()
            .fill(Color.gray.opacity(0.3))
            .frame(height: 150)
            .shimmer()
    }
    
    // Placeholder for temperature section
    private var temperatureSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Title row
            HStack(spacing: 8) {
                Image(systemName: "thermometer")
                    .foregroundColor(.white)
                Text("sleep_history_section_title_skin_temp".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                InfoButtonWithGlossaryPopup(showKey: GlossaryKey.historySleepTemperature.rawValue)
                Image(systemName: "questionmark.circle")
                    .foregroundColor(.gray)
                Spacer()
            }
            
            // Average value
            Text("\(String(format: "%.2f", viewModel.skinTempAvg))°")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.white)

            InteractiveTemperatureChartView(
                records: viewModel.skinTempRecords,
                xAxisCount: viewModel.xAxisCount,
                selectedDate: viewModel.selectedDate,
                selectedTimeRange: viewModel.selectedTimeRange
            )
        }
        .padding()
        .background(Color(red: 0.1, green: 0.12, blue: 0.18))
        .cornerRadius(12)
    }

    var body: some View {
        ZStack {
            ScrollView(.vertical, showsIndicators: false) {
                VStack(spacing: 10) {
                    // 加载指示器
                    if viewModel.isLoading && !viewModel.isRefreshing {
                        loadingView
                            .padding(.vertical, 20)
                    }
                    
                    // 睡眠得分模块（使用新组件）
                    SleepDataSectionView(
                        title: "sleep_history_section_title_score".localized,
                        glossaryKey: .historySleepScore,
                        icon: "Sleep_01",
                        maxValue: "\(viewModel.sleepScoreMax) \("activity_history_unit_score".localized)",
                        avgValue: "\(viewModel.sleepScoreAvg) \("activity_history_unit_score".localized)",
                        minValue: "\(viewModel.sleepScoreMin) \("activity_history_unit_score".localized)",
                        showScaleLabel: true,
                        timeLabels: viewModel.xAxisLabels,
                        useCustomIcon: true,
                        showInfoButton: true,
                        onTap: {
                        },
                        onInfoButtonTap: {
                            print("显示睡眠得分的帮助信息")
                        }
                    ) {
                            if viewModel.isLoading && !viewModel.isRefreshing {
                                // 骨架屏效果
                                skeletonBarChartView()
                            } else {
                                InteractiveBarChart(
                                    data: viewModel.currentSleepScoreData,
                                    yMax: 100,
                                    timeLabels: viewModel.xAxisLabels
                                )
                                    .transition(.opacity)
                                    .animation(.easeInOut(duration: 0.3), value: viewModel.currentSleepScoreData)
                            }
                        }
                    .padding(.horizontal, -15) // 添加负边距以抵消可能的系统内边距
                    .frame(maxWidth: .infinity)
                    
                    // 睡眠效率模块
                    SleepDataSectionView(
                        title: "sleep_history_section_title_efficiency".localized, glossaryKey: .historySleepAvgEfficiency,
                        icon: "Sleep_02",
                        maxValue: "\(viewModel.sleepEfficiencyMax) \("percent_symbol".localized)",
                        avgValue: "\(viewModel.sleepEfficiencyAvg) \("percent_symbol".localized)",
                        minValue: "\(viewModel.sleepEfficiencyMin) \("percent_symbol".localized)",
                        showScaleLabel: true,
                        timeLabels: viewModel.xAxisLabels,
                        onTap: {
                        }
                    ) {
                            if viewModel.isLoading && !viewModel.isRefreshing {
                                // 骨架屏效果
                                skeletonBarChartView()
                            } else {
                                InteractiveBarChart(
                                    data: viewModel.currentSleepEfficiencyData,
                                    yMax: 100,
                                    timeLabels: viewModel.xAxisLabels
                                )
                                    .transition(.opacity)
                                    .animation(.easeInOut(duration: 0.3), value: viewModel.currentSleepEfficiencyData)
                            }
                        }
                    
                    // 睡眠时长模块
                    SleepDataSectionView(
                        title: "sleep_history_section_title_duration".localized, glossaryKey: .historySleepTotalTimeAsleep,
                        icon: "Sleep_03",
                        maxValue: "\(viewModel.sleepTimeMaxHours) \("hr".localized) \(viewModel.sleepTimeMaxMins) \("min".localized)",
                        avgValue: "\(viewModel.sleepTimeAvgHours) \("hr".localized) \(viewModel.sleepTimeAvgMins) \("min".localized)",
                        minValue: "\(viewModel.sleepTimeMinHours) \("hr".localized) \(viewModel.sleepTimeMinMins) \("min".localized)",
                        showScaleLabel: false,
                        timeLabels: viewModel.xAxisLabels,
                        onTap: {
                        }
                    ) {
                            if viewModel.isLoading && !viewModel.isRefreshing {
                                // 骨架屏效果
                                skeletonBarChartView()
                            } else {
                                InteractiveLineChart(data: viewModel.currentSleepTimeData, timeLabels: viewModel.xAxisLabels)
                                    .transition(.opacity)
                                    .animation(.easeInOut(duration: 0.3), value: viewModel.currentSleepTimeData)
                            }
                        }
                    if VersionUpdateService.shared.status == 1{
                        // 睡眠心率模块
                        SleepDataSectionView(
                            title: "sleep_history_section_title_hr".localized, glossaryKey: .historySleepHeartRate,
                            icon: "Sleep_04",
                            maxValue: "\(viewModel.sleepHeartRateModel.maxHeartRate) \("insight_unit_bpm".localized)",
                            avgValue: "\(viewModel.sleepHeartRateModel.avgHeartRate) \("insight_unit_bpm".localized)",
                            minValue: "\(viewModel.sleepHeartRateModel.minHeartRate) \("insight_unit_bpm".localized)",
                            showScaleLabel: false,
                            timeLabels: viewModel.xAxisLabels,
                            onTap: {
                            }
                        ) {
                                if viewModel.isLoading && !viewModel.isRefreshing {
                                    // 骨架屏效果
                                    skeletonBarChartView()
                                } else {
                                    let maxHR = viewModel.sleepHeartRateModel.maxHeartRate
                                    let yAxisLabelsForHR = maxHR > 0 ? ["0", "\(maxHR / 2)", "\(maxHR)"] : ["0", "60", "120"]
                                    InteractiveRangeBarChart(
                                        data: viewModel.sleepHeartRateModel.records,
                                        xAxisLabels: viewModel.xAxisLabels,
                                        yAxisLabels: yAxisLabelsForHR,
                                        selectedTimeRange: viewModel.selectedTimeRange
                                    )
                                    .transition(.opacity)
                                }
                            }
                        
                        // 睡眠HRV模块
                        SleepDataSectionView(
                            title: "sleep_history_section_title_hrv".localized, glossaryKey: .historySleepHRV,
                            icon: "Sleep_05",
                            maxValue: "\(viewModel.sleepHRVModel.maxHRV) \("stress_detail_unit_ms".localized)",
                            avgValue: "\(viewModel.sleepHRVModel.avgHRV) \("stress_detail_unit_ms".localized)",
                            minValue: "\(viewModel.sleepHRVModel.minHRV) \("stress_detail_unit_ms".localized)",
                            showScaleLabel: false,
                            timeLabels: viewModel.xAxisLabels,
                            onTap: {
                            }
                        ) {
                                if viewModel.isLoading && !viewModel.isRefreshing {
                                    // 骨架屏效果
                                    skeletonBarChartView()
                                } else {
                                    let maxHRV = viewModel.sleepHRVModel.maxHRV
                                    let yAxisLabelsForHRV = maxHRV > 0 ? ["0", "\(maxHRV / 2)", "\(maxHRV)"] : ["0", "50", "100"]
                                    InteractiveRangeBarChart(
                                        data: hrvRecordsForChart,
                                        xAxisLabels: viewModel.xAxisLabels,
                                        yAxisLabels: yAxisLabelsForHRV,
                                        selectedTimeRange: viewModel.selectedTimeRange
                                    )
                                    .transition(.opacity)
                                }
                            }
                        
                        // 睡眠SpO2模块
                        SleepDataSectionView(
                            title: "sleep_history_section_title_spo2".localized, glossaryKey: .historySleepSpO2,
                            icon: "questionmark.circle",
                            maxValue: "\(viewModel.spo2Max) \("percent_symbol".localized)",
                            avgValue: "\(viewModel.spo2Avg) \("percent_symbol".localized)",
                            minValue: "\(viewModel.spo2Min) \("percent_symbol".localized)",
                            showScaleLabel: false,
                            timeLabels: viewModel.xAxisLabels,
                            onTap: {
                            }
                        ) {
                                if viewModel.isLoading && !viewModel.isRefreshing {
                                    // 骨架屏效果
                                    skeletonBarChartView()
                                } else {
                                    let maxSpo2 = viewModel.spo2Max
                                    let yAxisLabelsForSpo2 = maxSpo2 > 0 ? ["0", "\(maxSpo2 / 2)", "\(maxSpo2)"] : ["0", "95", "100"]
                                    InteractiveRangeBarChart(
                                        data: spo2RecordsForChart,
                                        xAxisLabels: viewModel.xAxisLabels,
                                        yAxisLabels: yAxisLabelsForSpo2,
                                        selectedTimeRange: viewModel.selectedTimeRange
                                    )
                                    .transition(.opacity)
                                }
                            }
                        // 睡眠皮肤温度模块
                        temperatureSection
                    }

                    Spacer(minLength: 30)
                }
                .padding()
                .background(Color.appBackground)
            }
            .refreshable {
                Task {
                    await viewModel.refreshData()
                }
            }
            .background(Color(red: 0.027, green: 0.027, blue: 0.031)) // 改为直接使用#070708的RGB值
            
            // 顶部刷新指示器 (当下拉刷新时显示)
            if viewModel.isRefreshing {
                HStack {
                    Spacer()
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                        .scaleEffect(1.0)
                    Spacer()
                }
                .padding(.top, 10)
                .background(Color.black.opacity(0.7))
                .zIndex(1)
            }
        }
    }
}


// MARK: - 可交互的区间柱状图组件
struct InteractiveRangeBarChart: View {
    struct RangeDataPoint: Identifiable {
        var id: String { xLabel }
        let min: Int
        let max: Int
        let xLabel: String
    }

    let data: [SleepHistoryHeartRateRecord]
    let xAxisLabels: [String]
    let yAxisLabels: [String]
    let selectedTimeRange: Int

    @State private var selectedLabel: String?

    private var chartData: [RangeDataPoint] {
        xAxisLabels.enumerated().map { (index, label) in
            if index < data.count {
                let record = data[index]
                return RangeDataPoint(min: record.minHeartRate, max: record.maxHeartRate, xLabel: label)
            } else {
                return RangeDataPoint(min: 0, max: 0, xLabel: label)
            }
        }
    }
    
    private var yAxisValues: [Int] {
        yAxisLabels.compactMap { Int($0) }
    }

    private func shouldShowLabel(label: String) -> Bool {
        switch selectedTimeRange {
        case 0, 2: // Week and Year, show all
            return true
        case 1: // Month, show specific labels
            let daysToShow = ["3", "10", "17", "24", xAxisLabels.count.description]
            return daysToShow.contains(label)
        default:
            return false
        }
    }

    private var barFill: Color {
        Color(red: 0.9, green: 0.3, blue: 0.3)
    }
    
    private var barFillHighlight: Color {
        Color(red: 1.0, green: 0.4, blue: 0.4)
    }

    var body: some View {
        let yMaxValue = yAxisValues.max() ?? 100 // Default to 100 if no labels
        let yScaleMax = Double(yMaxValue) * 1.2
        
        Chart {
            ForEach(chartData) { element in
                RectangleMark(
                    x: .value("Label", element.xLabel),
                    yStart: .value("Min", element.min),
                    yEnd: .value("Max", element.max),
                    width: .fixed(10)
                )
                .cornerRadius(6)
                .foregroundStyle(element.xLabel == selectedLabel ? barFillHighlight : barFill)
            }
        }
        .chartYScale(domain: 0...yScaleMax)
        .chartYAxis {
            AxisMarks(position: .trailing, values: yAxisValues) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                AxisValueLabel {
                    if let val = value.as(Int.self) {
                        Text("\(val)")
                            .font(.system(size: 10))
                            .foregroundColor(.gray)
                    }
                }
            }
        }
        .chartXAxis {
            AxisMarks(values: .automatic) { value in
                if let label = value.as(String.self) {
                    if shouldShowLabel(label: label) {
                        AxisValueLabel(label)
                            .font(.system(size: 8))
                            .foregroundStyle(Color.white)
                    }
                }
            }
//            AxisMarks(values: .automatic) { value in
//                if let label = value.as(String.self) {
//                    if shouldShowLabel(label: label) {
//                        AxisValueLabel(label)
//                            .font(.system(size: 10))
////                            .foregroundColor(.gray)
//                    }
//                }
//            }
        }
        .chartOverlay { proxy in
            GeometryReader { geometry in
                Rectangle().fill(.clear).contentShape(Rectangle())
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { value in
                                let xPos = value.location.x - geometry[proxy.plotAreaFrame].origin.x
                                if let label: String = proxy.value(atX: xPos), chartData.contains(where: { $0.xLabel == label }) {
                                    self.selectedLabel = label
                                }
                            }
                            .onEnded { _ in selectedLabel = nil }
                    )
            }
        }
        .frame(height: 150)
    }
}

struct InteractiveTemperatureChartView: View {
    let records: [SleepHistorySkinTempRecord]
    let xAxisCount: Int
    let selectedDate: Date
    let selectedTimeRange: Int
    
    @State private var selectedDataPoint: DataPoint?
    @State private var selectedXPosition: CGFloat?
    @ObservedObject private var userSettings = UserSettings.shared

    struct DataPoint: Identifiable {
        var id: Int { index }
        let index: Int
        let value: Double
    }

    private var dataPoints: [DataPoint] {
        records.map { record in
            DataPoint(index: getXAxisIndex(from: record.time), value: record.skinTemp)
        }.filter { $0.index >= 0 && $0.index < xAxisCount }.sorted(by: { $0.index < $1.index })
    }
    
    var body: some View {
        let displayUnit = userSettings.temperatureUnit
        
        let yDomainCelsius: [Double] = [-2.5, 2.5]
        let yAxisCelsiusValues: [Double] = [-2, -1, 0, 1, 2]
        
        let yDomain = yDomainCelsius.map { displayUnit.convertDeltaFromCelsius($0) }
        let yAxisValues = yAxisCelsiusValues.map { displayUnit.convertDeltaFromCelsius($0) }
        
        let lineColor = Color(hex: "#C4B2FF")
        let areaGradient = LinearGradient(
            gradient: Gradient(colors: [lineColor.opacity(0.4), lineColor.opacity(0.0)]),
            startPoint: .top,
            endPoint: .bottom
        )

        Chart {
            // Baseline
            RuleMark(y: .value("Baseline", 0))
                .lineStyle(StrokeStyle(lineWidth: 1, dash: [3, 3]))
                .foregroundStyle(Color.gray.opacity(0.8))

            if !dataPoints.isEmpty {
                // Area under the line
                if let firstPoint = dataPoints.first {
                    AreaMark(
                        x: .value("Day", firstPoint.index),
                        y: .value("Temp", displayUnit.convertDeltaFromCelsius(firstPoint.value))
                    )
                    .foregroundStyle(areaGradient)
                    .interpolationMethod(.monotone)
                }

                ForEach(dataPoints) { point in
                     AreaMark(
                        x: .value("Day", point.index),
                        y: .value("Temp", displayUnit.convertDeltaFromCelsius(point.value))
                    )
                    .foregroundStyle(areaGradient)
                    .interpolationMethod(.monotone)
                }

                // Dashed Line
                if let firstPoint = dataPoints.first {
                    LineMark(
                        x: .value("Day", firstPoint.index),
                        y: .value("Temp", displayUnit.convertDeltaFromCelsius(firstPoint.value))
                    )
                    .lineStyle(StrokeStyle(lineWidth: 2, dash: [4, 4]))
                    .foregroundStyle(lineColor)
                    .interpolationMethod(.monotone)
                }
                
                ForEach(dataPoints) { point in
                    LineMark(
                        x: .value("Day", point.index),
                        y: .value("Temp", displayUnit.convertDeltaFromCelsius(point.value))
                    )
                    .lineStyle(StrokeStyle(lineWidth: 2, dash: [4, 4]))
                    .foregroundStyle(lineColor)
                    .interpolationMethod(.monotone)
                    
                    PointMark(
                        x: .value("Day", point.index),
                        y: .value("Temp", displayUnit.convertDeltaFromCelsius(point.value))
                    )
                    .symbolSize(60)
                    .foregroundStyle(lineColor)
                }
            }
        }
        .chartYScale(domain: yDomain)
        .chartYAxis {
            AxisMarks(position: .trailing, values: yAxisValues) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 3])).foregroundStyle(Color.gray.opacity(0.5))
                if let tempValue = value.as(Double.self) {
                    AxisValueLabel {
                        if abs(tempValue) < 0.001 {
                            Text("Baseline").font(.system(size: 10)).foregroundColor(.gray)
                } else {
                            Text(String(format: "%+.1f%@", tempValue, displayUnit.rawValue)).font(.system(size: 10)).foregroundColor(.gray)
                        }
                    }
                }
            }
        }
        .chartXAxis {
            switch selectedTimeRange {
            case 0: // Week
                AxisMarks(values: Array(0..<xAxisCount)) { value in
                    if let index = value.as(Int.self) {
                        let weekdays = ["日", "一", "二", "三", "四", "五", "六"]
                        if index < weekdays.count {
                            AxisValueLabel {
                                HStack(spacing: 0) {
                                    Text(weekdays[index])
                                        .font(.system(size: 10))
                                        .foregroundColor(.gray)
                                    Spacer().frame(width: 4) // 向左偏移的视觉效果
                                }
                            }
                        }
                    }
                }

            case 1: // Month
                AxisMarks(values: Array(stride(from: 0, to: xAxisCount, by: 7))) { value in
                    if let index = value.as(Int.self) {
                        AxisValueLabel {
                            HStack(spacing: 0) {
                                Text("\(index + 1)")
                                    .font(.system(size: 10))
                                    .foregroundColor(.gray)
                                Spacer().frame(width: 4)
                            }
                        }
                    }
                }

            case 2: // Year
                AxisMarks(values: Array(0..<xAxisCount)) { value in
                    if let index = value.as(Int.self) {
                        AxisValueLabel {
                            HStack(spacing: 0) {
                                Text("\(index + 1)月")
                                    .font(.system(size: 10))
                                    .foregroundColor(.gray)
                                Spacer().frame(width: 4)
                            }
                        }
                    }
                }

            default:
                AxisMarks()
            }
        }
        .chartOverlay { proxy in
            GeometryReader { geometry in
                Rectangle().fill(.clear).contentShape(Rectangle())
                    .gesture(
                        DragGesture(minimumDistance: 0)
//                            .onChanged { value in
//                                let xPos = value.location.x - geometry[proxy.plotAreaFrame].origin.x
//                                if let index: Double = proxy.value(atX: xPos) {
//                                    let roundedIndex = Int(round(index))
//                                    if roundedIndex >= 0 && roundedIndex < xAxisCount {
////                                        self.selectedIndex = roundedIndex
//                                    }
//                                }
//                            }
//                            .onEnded { _ in selectedIndex = nil }
                    )
            }
        }
        .overlay(alignment: .topLeading) {
            if let selectedDataPoint = selectedDataPoint, let selectedXPosition = selectedXPosition {
                VStack {
                    let convertedValue = displayUnit.convertDeltaFromCelsius(selectedDataPoint.value)
                    Text(String(format: "%.2f %@", convertedValue, displayUnit.rawValue))
                        .font(.caption)
                        .padding(EdgeInsets(top: 4, leading: 8, bottom: 4, trailing: 8))
                        .background(Color.black.opacity(0.8))
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
                .offset(x: selectedXPosition - 25, y: -30)
                .transition(.opacity)
            }
        }
    }
    
    func getXAxisIndex(from timeString: String) -> Int {
        guard let timeInterval = TimeInterval(timeString) else { return -1 }
        let recordDate = Date(timeIntervalSince1970: timeInterval)
        let calendar = Calendar.current
        
        switch selectedTimeRange {
        case 0: // Week
            guard let weekInterval = calendar.dateInterval(of: .weekOfYear, for: selectedDate) else { return -1 }
            let startOfWeek = weekInterval.start
            
            let startOfRecordDate = calendar.startOfDay(for: recordDate)
            let startOfStartOfWeek = calendar.startOfDay(for: startOfWeek)

            let components = calendar.dateComponents([.day], from: startOfStartOfWeek, to: startOfRecordDate)
            return components.day ?? -1
        case 1: // Month
            guard let startOfMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: selectedDate)) else { return -1 }
            let startOfRecordDate = calendar.startOfDay(for: recordDate)
            let components = calendar.dateComponents([.day], from: startOfMonth, to: startOfRecordDate)
            return components.day ?? -1
        case 2: // Year
            let components = calendar.dateComponents([.month], from: recordDate)
            return (components.month ?? 1) - 1
        default:
            return -1
        }
    }
}

// MARK: - 预览
//struct SleepHistoryView_Previews: PreviewProvider {
//    static var previews: some View {
//        NavigationView {
//            SleepHistoryView()
//        }
//        .preferredColorScheme(.dark)
//    }
//}
