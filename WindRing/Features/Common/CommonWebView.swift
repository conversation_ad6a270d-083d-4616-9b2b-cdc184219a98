//
//  WebView.swift
//  WindRing
//
//  Created by zx on 2025/5/16.
//
import SwiftUI
import WebKit
import Combine

enum CopywritingTag: String, CaseIterable, Identifiable {
    case termsOfUse = "terms_of_use"
    case privacyPolicy = "privacy_policy"
    case informationList = "information_list"
    case appUsageGuide = "app_usage_guide"
    case ringUsageGuide = "ring_usage_guide"
    case passwordUsageGuide = "password_usage_guide"

    var id: String { rawValue }

    var displayName: String {
        switch self {
        case .termsOfUse:
            return "about_us_terms_of_use".localized()
        case .privacyPolicy:
            return "about_us_privacy_policy".localized()
        case .informationList:
            return "about_us_information_list".localized()
        case .appUsageGuide:
            return "app_guide_title".localized()
        case .ringUsageGuide:
            return "ring_guide_title".localized()
        case .passwordUsageGuide:
            return "account_guide_title".localized()
        }
    }
}

struct HTMLWebView: UIViewRepresentable {
    let htmlContent: String

    func makeUIView(context: Context) -> WKWebView {
        return WKWebView()
    }

    func updateUIView(_ webView: WKWebView, context: Context) {
//        let styledHTML = """
//        <html>
//            
//            <body>
//                \(htmlContent)
//            </body>
//        </html>
//        """
        let styledHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    color: white;
                    font-size: 14px;
                    background-color: transparent;
                }
                h1, h2, h3, h4, h5, h6 {
                    color: white;
                    font-size: 16px;
                }
            </style>
        </head>
        <body>
              \(htmlContent)
        </body>
        </html>
        """
        webView.isOpaque = false
        webView.backgroundColor = UIColor.clear
        webView.scrollView.backgroundColor = UIColor.clear
        webView.loadHTMLString(styledHTML, baseURL: nil)
    }
//    <head>
//        <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
//        <style>
//            body {
//                background-color: transparent;
//                color: white;
//                font-family: -apple-system, sans-serif;
//            }
//        </style>
//    </head>
}
// 假设的响应结构
public struct CopywritingResponse: Codable {
    let title: String                // 必须字段：标题
    let copywritingTag: String       // 必须字段：标签名称，用于区别不同的文案分类
    let copywritingLang: String      // 必须字段：语言，ZH:中文 EN:英文
    let copywritingContent: String  // 非必须字段：内容
    
}

class WebContentViewModel: ObservableObject {
    @Published var htmlContent: String = ""
    
    private let apiService: APIService = APIService.shared
    private var cancellable: AnyCancellable?
    var cancellables = Set<AnyCancellable>()
    func loadContent(for tag: String) {
        // 调用API获取用户信息
        apiService.user.getCopywriting(tag: tag)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completionStatus in
                guard let self = self else { return }
                if case .failure(_) = completionStatus {
                    self.htmlContent = "<p>加载失败，请稍后重试。</p>"
//                    self.title = "错误"
                }
            } receiveValue: { [weak self] response in
                guard let self = self else { return }
                self.htmlContent = response.copywritingContent
//                self.title = response.title
                
            }
        .store(in: &cancellables)
    }
}
        
struct CommonWebView: View {
    let tag: CopywritingTag
    @State var title: String = ""
    @StateObject private var viewModel = WebContentViewModel()
    @Environment(\.presentationMode) var presentationMode
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色xf
            Color(UIColor(red: 0.027, green: 0.027, blue: 0.031, alpha: 1)).edgesIgnoringSafeArea(.all)
            
            // 添加光晕背景
            VersionGlowBackground()
            
            VStack(spacing: 0) {
                // 自定义导航栏
                HStack(alignment: .center) {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    // 标题居左 - 添加点击返回功能
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text(title)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
                            .padding(.leading, 5)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .frame(height: 44) // 标准iOS导航栏高度
                .padding(.top, 8)
                
                // 主内容
                HTMLWebView(htmlContent: viewModel.htmlContent)
//                    .navigationTitle(viewModel.title)
                    .navigationBarTitleDisplayMode(.inline)
                    .onAppear {
                        viewModel.loadContent(for: tag.rawValue)
                        title = tag.displayName
                    }
                
            }
        }
        .navigationBarHidden(true)
    }
}

