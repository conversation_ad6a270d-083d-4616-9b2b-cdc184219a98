import SwiftUI

// 光晕背景效果组件
struct CornerGlowBackground: View {
    var position: CGPoint? = nil
    var colors: [Color] = [
        Color(hex: "#0048B5").opacity(0.4),
        Color(hex: "#0048B5").opacity(0.2),
        Color(hex: "#0048B5").opacity(0.0)
    ]
    var frameSize: CGFloat = 800
    var startRadius: CGFloat = 1
    var endRadius: CGFloat = 400
    
    var body: some View {
        Circle()
            .fill(
                RadialGradient(
                    gradient: Gradient(colors: colors),
                    center: .center,
                    startRadius: startRadius,
                    endRadius: endRadius
                )
            )
            .frame(width: frameSize, height: frameSize)
            .position(
                position ?? CGPoint(x: UIScreen.main.bounds.width, y: 0)
            )
            .edgesIgnoringSafeArea(.all)
    }
}

// 通用背景视图
struct CommonBackgroundView<Content: View>: View {
    let content: Content
    let title: String
    let showBackButton: Bool
    let backButtonTitle: String
    let backButtonColor: Color
    
    init(
        title: String,
        showBackButton: Bool = true,
        backButtonTitle: String = "Back",
        backButtonColor: Color = .white,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.showBackButton = showBackButton
        self.backButtonTitle = backButtonTitle
        self.backButtonColor = backButtonColor
        self.content = content()
    }
    
    var body: some View {
        ZStack {
            // 背景色
            Color(hex: "#070708").edgesIgnoringSafeArea(.all)
            
            // 添加光晕背景
            CornerGlowBackground(
                colors: [
                    Color(hex: "#0048B5").opacity(0.4),
                    Color(hex: "#0048B5").opacity(0.2),
                    Color(hex: "#0048B5").opacity(0.0)
                ]
            )
            
            content
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarTitleDisplayMode(.inline)
        .toolbarBackground(.hidden, for: .navigationBar)
        .toolbar {
            if showBackButton {
                ToolbarItem(placement: .navigationBarLeading) {
                    BackButton(title: backButtonTitle, color: backButtonColor)
                }
            }
        }
    }
}

// 返回按钮组件
struct BackButton: View {
    @Environment(\.presentationMode) var presentationMode
    let title: String
    let color: Color
    
    var body: some View {
        Button(action: {
            self.presentationMode.wrappedValue.dismiss()
        }) {
            HStack(spacing: 4) {
                Image("back")
                    .foregroundColor(color)
                Text(title)
                    .font(.custom("PingFang-SC-Heavy", size: 19))
                    .foregroundColor(color)
            }
        }
    }
}

// 预览
struct CommonBackgroundView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            CommonBackgroundView(title: "Test Page") {
                VStack {
                    Text("Content")
                        .foregroundColor(.white)
                }
            }
        }
    }
} 
