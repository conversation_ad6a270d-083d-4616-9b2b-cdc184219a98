import SwiftUI

/// 开发者测试中心视图 - 列表视图
struct DeveloperTestCenterView: View {
    @Environment(\.presentationMode) var presentationMode
    
    // 定义测试项目数据，不再使用AnyView
    private let testItems: [(title: String, backgroundColor: Color, id: String)] = [
        ("睡眠", Color(red: 0.1, green: 0.1, blue: 0.2), "sleep"),
        ("活动", Color(red: 0.12, green: 0.12, blue: 0.22), "activity"),
        ("心率", Color(red: 0.14, green: 0.14, blue: 0.24), "heartrate"),
        ("HRV", Color(red: 0.16, green: 0.16, blue: 0.26), "hrv"),
        ("血氧", Color(red: 0.18, green: 0.18, blue: 0.28), "oxygen"),
        ("压力", Color(red: 0.2, green: 0.2, blue: 0.3), "stress"),
        ("体温", Color(red: 0.22, green: 0.22, blue: 0.32), "temperature")
    ]
    
    var body: some View {
        // 移除外层的NavigationView，因为它是由父视图提供的
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Button(action: {
                    // 返回上一级
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(8)
                }
                
                Spacer()
                
                Text("开发者测试中心")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                // 右侧留白，平衡布局
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: 32, height: 32)
            }
            .padding(.horizontal)
            .padding(.top, 8)
            .background(Color.black)
            
            // 列表视图
            ScrollView {
                VStack(spacing: 1) {
                    ForEach(0..<testItems.count, id: \.self) { index in
                        let item = testItems[index]
                        NavigationLink(
                            destination: destinationView(for: item.id),
                            label: {
                                HStack {
                                    Spacer()
                                    Text(item.title)
                                        .font(.system(size: 18, weight: .medium))
                                        .foregroundColor(.red)
                                    Spacer()
                                }
                                .frame(height: 60)
                                .background(item.backgroundColor)
                            }
                        )
                        .buttonStyle(PlainButtonStyle()) // 移除默认的链接样式
                    }
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .background(Color.black.edgesIgnoringSafeArea(.all))
        .navigationBarHidden(true) // 隐藏由外层NavigationView提供的导航栏
    }
    
    // 根据ID返回对应的目标视图
    @ViewBuilder
    func destinationView(for id: String) -> some View {
        switch id {
        case "sleep":
            DevSleepTestView()
        case "activity":
            DevActivityTestView()
        case "heartrate":
            DevHeartRateTestView()
        case "hrv":
            DevHRVTestView()
        case "oxygen":
            DevBloodOxygenTestView()
        case "stress":
            DevStressTestView()
        case "temperature":
            DevTemperatureTestView()
        default:
            DevSleepTestView()
        }
    }
}

// 睡眠测试视图 - 添加Dev前缀避免命名冲突
struct DevSleepTestView: View {
    @Environment(\.presentationMode) var presentationMode
    
    // 定义测试项目数据
    private let testItems = ["获取睡眠数据", "上传到后台服务器", "GoMore睡眠测试", "睡眠数据JSON", "其他睡眠测试"]
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(8)
                }
                
                Spacer()
                
                Text("睡眠测试")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: 32, height: 32)
            }
            .padding(.horizontal)
            .padding(.top, 8)
            .padding(.bottom, 8)
            .background(Color.black)
            
            // 列表视图
            ScrollView {
                VStack(spacing: 1) {
                    ForEach(testItems, id: \.self) { item in
                        // 第一个按钮导航到新页面，其他按钮暂时打印日志
                        if item == "获取睡眠数据" {
                            NavigationLink(destination: GetSleepDataBlankView().navigationBarHidden(true)) {
                                listRow(text: item)
                            }
                            .buttonStyle(PlainButtonStyle())
                        } else if item == "上传到后台服务器" {
                            NavigationLink(destination: UploadSleepDataBlankView().navigationBarHidden(true)) {
                                listRow(text: item)
                            }
                            .buttonStyle(PlainButtonStyle())
                        } else {
                            Button(action: {
                                print("点击了 睡眠测试 - \(item)")
                            }) {
                                listRow(text: item)
                            }
                        }
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.edgesIgnoringSafeArea(.all))
        .navigationBarHidden(true)
    }
    
    // 列表行视图
    @ViewBuilder
    private func listRow(text: String) -> some View {
        HStack {
            Text(text)
                .font(.body)
                .foregroundColor(.white)
                .padding(.leading)
            
            Spacer()
        }
        .frame(height: 50)
        .frame(maxWidth: .infinity)
        .background(Color(red: 0.15, green: 0.15, blue: 0.25)) // 统一列表项背景色
    }
}

// 活动测试视图
struct DevActivityTestView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        TestPageTemplate(title: "活动测试", presentationMode: _presentationMode)
    }
}

// 心率测试视图
struct DevHeartRateTestView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        TestPageTemplate(title: "心率测试", presentationMode: _presentationMode)
    }
}

// HRV测试视图
struct DevHRVTestView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        TestPageTemplate(title: "HRV测试", presentationMode: _presentationMode)
    }
}

// 血氧测试视图
struct DevBloodOxygenTestView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        TestPageTemplate(title: "血氧测试", presentationMode: _presentationMode)
    }
}

// 压力测试视图
struct DevStressTestView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        TestPageTemplate(title: "压力测试", presentationMode: _presentationMode)
    }
}

// 体温测试视图
struct DevTemperatureTestView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        TestPageTemplate(title: "体温测试", presentationMode: _presentationMode)
    }
}

// 测试页面模板
struct TestPageTemplate: View {
    let title: String
    @Environment(\.presentationMode) var presentationMode
    
    // 添加占位列表数据
    private let placeholderItems = ["获取睡眠数据", "测试项 2", "测试项 3", "测试项 4", "测试项 5"]
    
    var body: some View {
        VStack(spacing: 0) { // 设置间距为0
            // 标题栏
            HStack {
                Button(action: {
                    // 返回上一级
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(8)
                }
                
                Spacer()
                
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                // 右侧留白，平衡布局
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: 32, height: 32)
            }
            .padding(.horizontal)
            .padding(.top, 8)
            .padding(.bottom, 8) // 添加底部内边距
            .background(Color.black)
            
            // 列表视图区域
            ScrollView {
                VStack(spacing: 1) {
                    ForEach(placeholderItems, id: \.self) { item in
                        Button(action: {
                            // 点击列表项的操作
                            print("点击了 \(title) - \(item)")
                        }) {
                            HStack {
                                Text(item)
                                    .font(.body)
                                    .foregroundColor(.white) // 列表项使用白色文本
                                    .padding(.leading)
                                
                                Spacer()
                            }
                            .frame(height: 50)
                            .frame(maxWidth: .infinity)
                            .background(Color(red: 0.15, green: 0.15, blue: 0.25)) // 统一列表项背景色
                        }
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.edgesIgnoringSafeArea(.all))
        .navigationBarHidden(true)
    }
}

// 预览
struct DeveloperTestCenterView_Previews: PreviewProvider {
    static var previews: some View {
        DeveloperTestCenterView()
    }
} 