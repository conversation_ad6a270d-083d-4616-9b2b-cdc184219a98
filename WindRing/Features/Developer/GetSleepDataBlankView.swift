import SwiftUI
import CRPSmartRing // 导入SDK框架

/// 获取睡眠数据测试的页面
struct GetSleepDataBlankView: View {
    @Environment(\.presentationMode) var presentationMode
    
    // MARK: - State Variables
    @StateObject private var deviceService = WindRingDeviceService.shared // 获取设备服务单例
    private let healthDataManager = HealthDataManager.shared // 添加数据库管理器引用
    @State private var selectedDay = 1 // 默认获取昨天的数据
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var sleepData: CRPSleepRecordModel? = nil
    @State private var sleepDetails: [SleepDetailItem] = [] // 使用与SleepTestView类似的结构
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(8)
                }
                
                Spacer()
                
                Text("获取睡眠数据") // 页面标题
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: 32, height: 32)
            }
            .padding(.horizontal)
            .padding(.top, 8)
            .padding(.bottom, 8)
            .background(Color.black)
            
            // 内容区域
            ScrollView {
                VStack(spacing: 20) {
                    // 连接状态
                    connectionStatusView
                    
                    // 日期选择
                    daySelectionView
                    
                    // 获取数据按钮
                    fetchDataButton
                    
                    // 结果显示区域
                    if isLoading {
                        ProgressView("获取数据中...")
                            .padding()
                            .foregroundColor(.white)
                    } else if let errorMsg = errorMessage {
                        Text(errorMsg)
                            .foregroundColor(.red)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(Color.red.opacity(0.2))
                            .cornerRadius(8)
                    } else if let data = sleepData {
                        sleepSummaryView(data: data)
                        if !sleepDetails.isEmpty {
                            sleepDetailsView
                        }
                    } else {
                        Text("请连接设备并选择日期后点击按钮")
                            .foregroundColor(.gray)
                            .padding()
                    }
                }
                .padding()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.edgesIgnoringSafeArea(.all))
        .navigationBarHidden(true)
    }
    
    // MARK: - Subviews
    
    /// 连接状态视图
    private var connectionStatusView: some View {
        HStack {
            Circle()
                .fill(deviceService.connectionState.isConnected ? Color.green : Color.red)
                .frame(width: 10, height: 10)
            Text(deviceService.connectionState.isConnected ? "设备connected".localized : "设备未连接")
                .font(.caption)
                .foregroundColor(deviceService.connectionState.isConnected ? .green : .red)
            Spacer()
        }
        .padding(8)
        .background(Color.white.opacity(0.1))
        .cornerRadius(5)
    }
    
    /// 日期选择视图
    private var daySelectionView: some View {
        VStack(alignment: .leading) {
            Text("选择日期：")
                .font(.subheadline)
                .foregroundColor(.gray)
            Picker("选择日期", selection: $selectedDay) {
                Text("今天").tag(0)
                Text("昨天").tag(1)
                Text("前天").tag(2)
                Text("3天前").tag(3)
                Text("4天前").tag(4)
                Text("5天前").tag(5)
                Text("6天前").tag(6)
            }
            .pickerStyle(SegmentedPickerStyle())
            .colorScheme(.dark) // 适应暗色背景
            .padding(.bottom)
        }
    }
    
    /// 获取数据按钮
    private var fetchDataButton: some View {
        Button(action: fetchData) {
            HStack {
                Image(systemName: "arrow.down.circle.fill")
                Text("获取睡眠数据")
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(.borderedProminent)
        .tint(.blue)
        .padding(.bottom)
        .disabled(!deviceService.connectionState.isConnected || isLoading)
    }
    
    /// 睡眠数据总结视图
    private func sleepSummaryView(data: CRPSleepRecordModel) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("睡眠总结")
                .font(.headline)
                .foregroundColor(.white)
            
            let totalMinutes = data.deep + data.light + data.rem
            let hours = totalMinutes / 60
            let minutes = totalMinutes % 60
            
            HStack {
                summaryCard(title: "总睡眠", value: "\(hours)h \(minutes)m", color: .blue)
            }
            
            HStack {
                summaryCard(title: "深睡", value: "\(data.deep)m", color: .indigo)
                summaryCard(title: "浅睡", value: "\(data.light)m", color: .purple)
                summaryCard(title: "REM", value: "\(data.rem)m", color: .pink)
            }
        }
        .padding()
        .background(Color.white.opacity(0.1))
        .cornerRadius(12)
    }
    
    /// 睡眠阶段详情视图
    private var sleepDetailsView: some View {
        VStack(alignment: .leading) {
            Text("睡眠阶段详情")
                .font(.headline)
                .foregroundColor(.white)
                .padding(.top)
            
            if sleepDetails.isEmpty {
                Text("没有详细的睡眠阶段数据")
                    .foregroundColor(.gray)
                    .padding()
            } else {
                // 使用简单的列表显示文本，避免引入过多复杂性
                VStack(alignment: .leading, spacing: 6) {
                    ForEach(sleepDetails) { detail in
                        HStack {
                            Image(systemName: detail.icon)
                                .foregroundColor(detail.color)
                                .frame(width: 20)
                            Text("\(detail.startTimeText)-\(detail.endTimeText) \(detail.typeText) (\(detail.durationText))")
                                .font(.caption)
                                .foregroundColor(.white)
                            Spacer()
                        }
                    }
                }
                .padding()
                .background(Color.white.opacity(0.1))
                .cornerRadius(12)
            }
        }
    }
    
    // 辅助卡片视图
    private func summaryCard(title: String, value: String, color: Color) -> some View {
        VStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
            Text(value)
                .font(.headline)
                .foregroundColor(color)
                .padding(.top, 1)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
    }
    
    // MARK: - Fetch Data Logic
    private func fetchData() {
        guard deviceService.connectionState.isConnected else {
            errorMessage = "错误：设备未连接"
            return
        }
        
        isLoading = true
        errorMessage = nil
        sleepData = nil
        sleepDetails = []
        
        print("DEBUG: [fetchData] 准备获取第 \(selectedDay) 天的睡眠数据...")
        
        CRPSmartRingSDK.sharedInstance.getSleepData(selectedDay) { model, error in
            DispatchQueue.main.async {
                isLoading = false
                if error == .none {
                    print("DEBUG: [fetchData] 成功获取基础睡眠数据: 深睡=\(model.deep), 浅睡=\(model.light), REM=\(model.rem)")
                    // ---> 添加调试: 打印原始 detail 数组
                    print("DEBUG: [fetchData] 原始 model.detail 内容: \(model.detail)")
                    print("DEBUG: [fetchData] 原始 model.detail 数量: \(model.detail.count)")
                    // <--- 调试结束
                    sleepData = model
                    parseSleepDetails(from: model.detail)
                    
                    // 获取必要的保存数据
                    var calculatedAwake: Int = 0
                    var overallStartDate: Date? = nil
                    var overallEndDate: Date? = nil
                    
                    if !self.sleepDetails.isEmpty {
                        overallStartDate = self.sleepDetails.first?.startTime
                        overallEndDate = self.sleepDetails.last?.endTime
                        
                        // 计算清醒时间
                        for detail in self.sleepDetails {
                            if detail.type == 0 { // 清醒状态
                                calculatedAwake += detail.durationMinutes
                            }
                        }
                    }
                    
                    // --- 添加明确的JSON格式打印代码 ---
                    print("准备打印睡眠数据JSON...")
                    
                    // 1. 准备日历
                    let calendar = Calendar.current
                    
                    // 2. 准备记录数组
                    var recordsArray: [[String: Any]] = []
                    for detail in self.sleepDetails {
                        let record: [String: Any] = [
                            "type": detail.type,
                            "total": detail.durationMinutes,
                            "startTime": Int(detail.startTime.timeIntervalSince1970 * 1000),
                            "endTime": Int(detail.endTime.timeIntervalSince1970 * 1000)
                        ]
                        recordsArray.append(record)
                    }
                    
                    // 3. 准备完整的数据字典
                    let sleepTimeTotal = model.deep + model.light + model.rem
                    let jsonDict: [String: Any] = [
                        "date": overallStartDate != nil ? Int(calendar.startOfDay(for: overallStartDate!).timeIntervalSince1970 * 1000) : 0,
                        "sleepTime": sleepTimeTotal,
                        "light": model.light,
                        "rem": model.rem,
                        "records": recordsArray,
                        "deep": model.deep,
                        "endDate": overallEndDate != nil ? Int(overallEndDate!.timeIntervalSince1970 * 1000) : 0,
                        "startDate": overallStartDate != nil ? Int(overallStartDate!.timeIntervalSince1970 * 1000) : 0,
                        "awake": calculatedAwake
                    ]
                    
                    // 4. 转换为JSON并打印
                    do {
                        let jsonData = try JSONSerialization.data(withJSONObject: jsonDict, options: [.prettyPrinted])
                        if let jsonString = String(data: jsonData, encoding: .utf8) {
                            print("\n--- 格式化睡眠数据 JSON 开始 ---")
                            print(jsonString)
                            print("--- 格式化睡眠数据 JSON 结束 ---\n")
                        } else {
                            print("ERROR: 无法将JSON数据转换为字符串")
                        }
                    } catch {
                        print("ERROR: JSON序列化失败: \(error)")
                    }
                    // --- JSON打印代码结束 ---
                    
                    // 保存数据到本地数据库
                    self.saveToDatabase(
                        model: model,
                        sleepDetails: self.sleepDetails,
                        calculatedAwake: calculatedAwake,
                        startDate: overallStartDate,
                        endDate: overallEndDate
                    )
                    
                    errorMessage = nil
                } else {
                    print("ERROR: [fetchData] 获取睡眠数据失败: \(error)")
                    errorMessage = "获取睡眠数据失败: \(error)"
                    sleepData = nil
                }
            }
        }
        
        // 添加超时处理
        DispatchQueue.main.asyncAfter(deadline: .now() + 15.0) {
            if self.isLoading {
                self.isLoading = false
                self.errorMessage = "获取数据超时（15秒），请重试"
                print("ERROR: [fetchData] 获取睡眠数据超时")
            }
        }
    }
    
    /// 解析睡眠阶段详情
    private func parseSleepDetails(from details: [Dictionary<String, String>]) {
        var parsedDetails: [SleepDetailItem] = []
        print("DEBUG: [parseSleepDetails] 开始解析睡眠详情，共 \(details.count) 条")
        
        // 获取当前日历和目标日期
        let calendar = Calendar.current
        // 计算获取数据的那一天的日期。注意：如果用户选择的是"今天"(selectedDay=0)，但睡眠发生在昨天晚上到今天凌晨，
        // 这里的 baseDate 应该是昨天，以便正确处理跨天。更稳妥的方式是，从睡眠总结数据（如果SDK提供）
        // 或第一个睡眠阶段的开始时间来推断基准日期。这里暂时简化处理，假设睡眠都发生在所选日期的前一晚到当天。
        // 一个更健壮的方法是分析所有时间戳，判断整体时间范围。
        
        // 假设睡眠数据对应的是 selectedDay 天前的晚上到 selectedDay-1 天前的早上
        // 例如 selectedDay = 1 (昨天)，基准日期是前天晚上
        guard let baseDate = calendar.date(byAdding: .day, value: -selectedDay, to: Date()) else {
            print("ERROR: [parseSleepDetails] 无法计算基准日期")
            return
        }
        
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm"
        
        var previousEndTime: Date? = nil // 用于处理可能的跨天逻辑
        
        for (index, detail) in details.enumerated() {
            print("DEBUG: [parseSleepDetails] 正在解析原始详情[\(index)]: \(detail)")
            
            if let typeString = detail["type"],
               let startTimeString = detail["start"],
               let endTimeString = detail["end"],
               let totalString = detail["total"],
               let type = Int(typeString),
               let totalMinutes = Int(totalString),
               // 解析 HH:mm 格式的时间
               let startTimeComponent = timeFormatter.date(from: startTimeString),
               let endTimeComponent = timeFormatter.date(from: endTimeString) {
                
                // 从时间组件中提取小时和分钟
                let startHour = calendar.component(.hour, from: startTimeComponent)
                let startMinute = calendar.component(.minute, from: startTimeComponent)
                let endHour = calendar.component(.hour, from: endTimeComponent)
                let endMinute = calendar.component(.minute, from: endTimeComponent)
                
                // 构建完整的开始和结束日期
                // 初始假设开始和结束都在 baseDate 这一天或之后一天
                var startDateComponents = calendar.dateComponents([.year, .month, .day], from: baseDate)
                startDateComponents.hour = startHour
                startDateComponents.minute = startMinute
                
                var endDateComponents = calendar.dateComponents([.year, .month, .day], from: baseDate)
                endDateComponents.hour = endHour
                endDateComponents.minute = endMinute
                
                guard var startDate = calendar.date(from: startDateComponents),
                      var endDate = calendar.date(from: endDateComponents) else {
                    print("ERROR: [parseSleepDetails] 无法从组件创建日期[\(index)]: \(detail)")
                    continue
                }
                
                // --- 处理跨天逻辑 --- 
                // 规则 1: 如果当前阶段的开始时间比结束时间晚（例如 start=23:00, end=01:00），则结束时间加一天
                if endDate <= startDate {
                    endDate = calendar.date(byAdding: .day, value: 1, to: endDate) ?? endDate
                    print("DEBUG: [parseSleepDetails] 检测到跨天(规则1)，结束日期调整为: \(endDate)")
                }
                
                // 规则 2: 如果存在上一个阶段，并且当前阶段的开始时间早于上一个阶段的结束时间，
                // 说明当前阶段及后续阶段都应该属于第二天。
                // （这种基于顺序的假设可能不总是准确，但可以处理常见情况）
                if let prevEnd = previousEndTime, startDate < prevEnd {
                     // 检查是否已经是第二天了（通过与 baseDate + 1 天比较）
                    if !calendar.isDate(startDate, inSameDayAs: calendar.date(byAdding: .day, value: 1, to: baseDate)! ) {
                         startDate = calendar.date(byAdding: .day, value: 1, to: startDate) ?? startDate
                         endDate = calendar.date(byAdding: .day, value: 1, to: endDate) ?? endDate
                         print("DEBUG: [parseSleepDetails] 检测到跨天(规则2)，开始和结束日期调整为: \(startDate), \(endDate)")
                    }
                }
                // --- 跨天逻辑结束 --- 
                
                // 更新上一个阶段的结束时间
                previousEndTime = endDate

                // 验证持续时间 (可选，如果totalMinutes可靠，可以信任它；否则用计算的)
                let calculatedDuration = Int(endDate.timeIntervalSince(startDate) / 60)
                if abs(calculatedDuration - totalMinutes) > 5 { // 允许几分钟误差
                    print("WARN: [parseSleepDetails] 计算时长 (\(calculatedDuration) min) 与提供时长 (\(totalMinutes) min) 不符，详情: \(detail)")
                    // 可以选择使用哪个时长，这里暂时信任 totalMinutes
                }
                
                let item = SleepDetailItem(
                    id: index,
                    type: type,
                    startTime: startDate,
                    endTime: endDate,
                    // 优先使用SDK提供的 totalMinutes，因为它可能更准确地反映了设备的记录
                    durationMinutes: totalMinutes 
                )
                parsedDetails.append(item)
                print("DEBUG: [parseSleepDetails] 成功解析详情[\(index)]: \(item.typeText) - \(item.durationMinutes)分钟 - [\(item.startTimeText) - \(item.endTimeText)]")
                
            } else {
                print("ERROR: [parseSleepDetails] 解析失败[\(index)]：无法完全解析字典或时间格式: \(detail)")
                if detail["type"] == nil { print(" - 缺少或无法解析 'type'") }
                if detail["start"] == nil { print(" - 缺少或无法解析 'start'") }
                if detail["end"] == nil { print(" - 缺少或无法解析 'end'") }
                if detail["total"] == nil { print(" - 缺少或无法解析 'total'") }
                if Int(detail["type"] ?? "x") == nil { print(" - 'type' 无法转换为 Int") }
                if timeFormatter.date(from: detail["start"] ?? "") == nil { print(" - 'start' 不是有效的 HH:mm 格式")}
                if timeFormatter.date(from: detail["end"] ?? "") == nil { print(" - 'end' 不是有效的 HH:mm 格式")}
                if Int(detail["total"] ?? "x") == nil { print(" - 'total' 无法转换为 Int") }
            }
        }
        
        // 按时间排序
        sleepDetails = parsedDetails.sorted { $0.startTime < $1.startTime }
        print("DEBUG: [parseSleepDetails] 解析完成，共解析出 \(sleepDetails.count) 条有效详情")
    }
    
    /// 将睡眠数据保存到本地数据库
    private func saveToDatabase(
        model: CRPSleepRecordModel,
        sleepDetails: [SleepDetailItem],
        calculatedAwake: Int,
        startDate: Date?,
        endDate: Date?
    ) {
        guard !sleepDetails.isEmpty, let startDate = startDate, let endDate = endDate else {
            print("⚠️ 无法保存到数据库: 睡眠详情数据为空或缺少开始/结束时间")
            return
        }
        
        // 从UserDefaults获取当前登录用户的ID，而不是使用硬编码的测试用户ID
        let userId = UserDefaults.standard.string(forKey: "userId")
        
        // 如果无法获取userId，打印警告并使用测试用户ID
        if userId == nil || userId?.isEmpty == true {
            print("⚠️ 无法从UserDefaults获取用户ID，使用默认测试用户")
        }
        
        // 使用获取到的userId或默认的测试用户ID
        let currentUserID = userId ?? "current_user"
        print("ℹ️ 使用用户ID: \(currentUserID) 保存睡眠数据")
        
        // 确保用户存在
//        if healthDataManager.getUser(id: currentUserID) == nil {
//            print("⚠️ 用户不存在，创建测试用户...")
//            healthDataManager.createUser(id: currentUserID, name: "用户\(currentUserID)", email: "user\(currentUserID)@example.com", completion: {created in 
//                if created {
//                    print("✓ 用户创建成功")
//                } else {
//                    print("⚠️ 创建用户失败，尝试直接添加数据")
//                }
//            })
//            
//        }
        
        // --- 添加查重逻辑 ---
        // 1. 首先检查是否存在重复数据
        // 使用宽松的时间范围进行查询：开始时间前后1小时，确保能找到可能重叠的记录
        let searchStartDate = Calendar.current.date(byAdding: .hour, value: -1, to: startDate) ?? startDate
        let searchEndDate = Calendar.current.date(byAdding: .hour, value: 1, to: endDate) ?? endDate
        
        let existingRecords = healthDataManager.getSleep(userId: currentUserID, startDate: searchStartDate, endDate: searchEndDate)
        
        if !existingRecords.isEmpty {
            print("⚠️ 检测到可能的重复睡眠数据，正在进行详细比对...")
            
            // 2. 检查是否有时间范围重叠的记录
            var hasDuplicate = false
            var duplicateInfo = ""
            
            for record in existingRecords {
                guard let recordStartTime = record.startTime, let recordEndTime = record.endTime else {
                    continue
                }
                
                // 计算时间重叠度
                // 两组时间段存在重叠的条件：一组的开始时间早于另一组的结束时间，且一组的结束时间晚于另一组的开始时间
                let hasOverlap = startDate < recordEndTime && endDate > recordStartTime
                
                // 计算基本信息的相似度
                let deepSimilar = abs(Int(record.deepMinutes) - model.deep) <= 5 // 允许5分钟的误差
                let lightSimilar = abs(Int(record.lightMinutes) - model.light) <= 5
                let remSimilar = abs(Int(record.remMinutes) - model.rem) <= 5
                
                // 如果时间有重叠且基本指标相似，认为是重复数据
                if hasOverlap && (deepSimilar && lightSimilar && remSimilar) {
                    hasDuplicate = true
                    duplicateInfo = "ID=\(record.id ?? "未知"), 开始=\(record.startTime?.description ?? "未知"), 结束=\(record.endTime?.description ?? "未知")"
                    break
                }
            }
            
            if hasDuplicate {
                print("🚫 发现重复的睡眠数据，跳过保存。重复记录: \(duplicateInfo)")
                return
            } else {
                print("✅ 未发现重复数据，继续保存...")
            }
        } else {
            print("✅ 未找到时间范围内的现有记录，继续保存...")
        }
        // --- 查重逻辑结束 ---
        
        // 准备睡眠阶段数据，转换为HealthDataManager需要的格式
        var sleepStages: [(type: String, startTime: Date, duration: Int16)] = []
        
        for detail in sleepDetails {
            // 转换类型从数字到字符串
            var stageType = "unknown"
            switch detail.type {
            case 0:
                stageType = "awake" // 清醒
            case 1:
                stageType = "light" // 浅睡
            case 2:
                stageType = "deep"  // 深睡
            case 3:
                stageType = "rem"   // REM睡眠
            default:
                stageType = "unknown"
            }
            
            sleepStages.append((
                type: stageType,
                startTime: detail.startTime,
                duration: Int16(detail.durationMinutes)
            ))
        }
        
        // 存储到数据库
        print("🔵 开始将睡眠数据保存到数据库...")
        
        let success = healthDataManager.addSleep(
            userId: currentUserID,
            startTime: startDate,
            endTime: endDate,
            totalMinutes: Int16(model.deep + model.light + model.rem),
            deepMinutes: Int16(model.deep),
            lightMinutes: Int16(model.light),
            remMinutes: Int16(model.rem),
            awakeMinutes: Int16(calculatedAwake),
            score: nil, // SDK未提供睡眠评分
            efficiency: nil, // SDK未提供睡眠效率
            deviceId: "smart_ring", // 设备标识符
            sleepStages: sleepStages
        )
        
        if success {
            print("✅ 睡眠数据已成功保存到本地数据库")
            
            // 验证是否成功保存
            if let latestSleep = healthDataManager.getLatestSleep(userId: currentUserID) {
                print("✓ 验证：成功从数据库检索到睡眠数据")
                print("  开始时间: \(latestSleep.startTime ?? Date())")
                print("  结束时间: \(latestSleep.endTime ?? Date())")
                print("  总时长: \(latestSleep.totalMinutes) 分钟")
                print("  深睡: \(latestSleep.deepMinutes) 分钟")
                print("  浅睡: \(latestSleep.lightMinutes) 分钟")
                print("  REM: \(latestSleep.remMinutes) 分钟")
                
                if let stages = latestSleep.sleepStages?.allObjects as? [SleepStageEntity], !stages.isEmpty {
                    print("  包含 \(stages.count) 个睡眠阶段")
                    
                    // 可选：打印睡眠阶段详情
                    for (index, stage) in stages.enumerated() {
                        print("    阶段 #\(index+1): 类型=\(stage.type ?? "未知"), 时长=\(stage.duration)分钟")
                    }
                } else {
                    print("  没有睡眠阶段数据")
                }
            } else {
                print("⚠️ 警告：无法从数据库检索刚保存的睡眠数据")
            }
        } else {
            print("❌ 保存睡眠数据到数据库失败")
        }
    }
}

struct GetSleepDataBlankView_Previews: PreviewProvider {
    static var previews: some View {
        GetSleepDataBlankView()
    }
} 
