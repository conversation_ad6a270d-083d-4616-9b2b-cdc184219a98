import SwiftUI

/// 上传睡眠数据到后台的测试空白页面
struct UploadSleepDataBlankView: View {
    @Environment(\.presentationMode) var presentationMode
    
    // 添加状态变量
    @State private var isLoading: Bool = false
    @State private var uploadPreview: String = "尚未获取待上传数据"
    @State private var serverResponse: String = ""
    
    // 获取服务实例
    private let sleepUploadService = SleepUploadService.shared
    private let healthDataManager = HealthDataManager.shared
    private let authService = AuthService.shared
    
    var body: some View {
        VStack(spacing: 0) { // 使用 spacing: 0 移除 VStack 默认间距
            // 标题栏
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(8)
                }
                
                Spacer()
                
                Text("上传到后台服务器") // 页面标题
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: 32, height: 32)
                    .padding(8) // 与左侧按钮对齐
            }
            .padding(.horizontal)
            .padding(.top, 8) // 调整顶部内边距
            .padding(.bottom, 8)
            .background(Color.black) // 背景色确保覆盖安全区域

            // 内容区域
            ScrollView {
                VStack(alignment: .leading, spacing: 15) {
                    Text("待上传数据预览:")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(.bottom, 5)
                    
                    Text(uploadPreview)
                        .font(.system(.body, design: .monospaced)) // 等宽字体适合显示数据
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                        .background(Color.white.opacity(0.1))
                        .cornerRadius(8)
                    
                    Button(action: uploadData) {
                        HStack {
                            Spacer()
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            } else {
                                Text("获取预览并上传数据")
                                    .fontWeight(.semibold)
                            }
                            Spacer()
                        }
                        .padding()
                        .background(isLoading ? Color.gray : Color.blue) // 加载时按钮变灰
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isLoading) // 加载时禁用按钮
                    .padding(.vertical)

                    Text("服务器响应:")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(.bottom, 5)
                    
                    Text(serverResponse.isEmpty ? "尚未上传" : serverResponse)
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(serverResponse.contains("成功") ? .green : (serverResponse.contains("失败") ? .red : .gray)) // 根据结果显示不同颜色
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                        .background(Color.white.opacity(0.1))
                        .cornerRadius(8)

                    Spacer() // 将内容推到顶部
                }
                .padding() // 给 ScrollView 内容添加内边距
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.edgesIgnoringSafeArea(.all)) // 背景延伸至安全区域
        .navigationBarHidden(true)
        .onAppear(perform: fetchInitialPreview) // 页面出现时获取一次预览
    }
    
    /// 获取初始预览数据
    private func fetchInitialPreview() {
        guard let userId = authService.currentUser?.id else {
            uploadPreview = "错误：用户未登录"
            return
        }
        
        // 获取所有待上传的睡眠数据
        let sleepData = healthDataManager.getSleep(userId: userId) // 假设 getSleep 获取所有相关数据
        
        if sleepData.isEmpty {
            uploadPreview = "数据库中没有找到当前用户的睡眠数据。"
        } else {
            // 格式化为 JSON 预览字符串
            var previewText = "找到 \(sleepData.count) 条睡眠记录 (最多显示3条):\n\n"
            let calendar = Calendar.current
            
            for (index, sleep) in sleepData.prefix(3).enumerated() {
                previewText += "记录 #\(index + 1) JSON:\n"
                
                // 准备睡眠阶段记录数组
                var recordsArray: [[String: Any]] = []
                if let stages = sleep.sleepStages?.allObjects as? [SleepStageEntity] {
                    for stage in stages.sorted(by: { $0.startTime ?? Date() < $1.startTime ?? Date() }) {
                        let record: [String: Any] = [
                            "type": sleepStageTypeToInt(stage.type),
                            "total": Int(stage.duration),
                            "startTime": Int((stage.startTime ?? Date()).timeIntervalSince1970 * 1000),
                            "endTime": Int((stage.startTime ?? Date()).addingTimeInterval(TimeInterval(stage.duration * 60)).timeIntervalSince1970 * 1000)
                        ]
                        recordsArray.append(record)
                    }
                }
                
                // 准备完整的数据字典
                let jsonDict: [String: Any?] = [
                    "date": sleep.startTime != nil ? Int(calendar.startOfDay(for: sleep.startTime!).timeIntervalSince1970 * 1000) : nil,
                    "sleepTime": Int(sleep.totalMinutes),
                    "light": Int(sleep.lightMinutes),
                    "rem": Int(sleep.remMinutes),
                    "records": recordsArray,
                    "deep": Int(sleep.deepMinutes),
                    "endDate": sleep.endTime != nil ? Int(sleep.endTime!.timeIntervalSince1970 * 1000) : nil,
                    "startDate": sleep.startTime != nil ? Int(sleep.startTime!.timeIntervalSince1970 * 1000) : nil,
                    "awake": Int(sleep.awakeMinutes),
                    // 添加数据库中的评分和效率字段（如果需要）
                    // "score": Int(sleep.score),
                    // "efficiency": Int(sleep.efficiency)
                ]
                
                // 过滤掉 nil 值
                let nonNilDict = jsonDict.compactMapValues { $0 }
                
                // 转换为JSON字符串并添加到预览文本
                do {
                    let jsonData = try JSONSerialization.data(withJSONObject: nonNilDict, options: [.prettyPrinted])
                    if let jsonString = String(data: jsonData, encoding: .utf8) {
                        previewText += jsonString + "\n\n"
                    } else {
                        previewText += "  错误：无法将记录 #\(index + 1) 转换为 JSON 字符串\n\n"
                    }
                } catch {
                    previewText += "  错误：记录 #\(index + 1) JSON 序列化失败: \(error)\n\n"
                }
            }
            
            if sleepData.count > 3 {
                previewText += "...等 \(sleepData.count - 3) 条记录"
            }
            uploadPreview = previewText
        }
    }

    // 辅助函数：将 SleepStageEntity 的 type (String) 转换为 Int
    private func sleepStageTypeToInt(_ typeString: String?) -> Int {
        switch typeString?.lowercased() {
        case "awake": return 0
        case "light": return 1
        case "deep": return 2
        case "rem": return 3
        default: return -1 // 或者其他表示未知的值
        }
    }

    /// 上传数据函数
    private func uploadData() {
        isLoading = true
        serverResponse = "正在上传..."
        
        // 先刷新一次预览，确保显示的是将要上传的数据
        fetchInitialPreview() // 重新获取预览

        // 调用上传服务
        sleepUploadService.uploadPendingSleepData { count, error in
            isLoading = false
            if let error = error {
                serverResponse = "上传失败: \(error.localizedDescription)"
            } else {
                serverResponse = "上传成功: \(count) 天的数据已处理。"
                 // 可以在成功后再次刷新预览，展示可能已更新的上传状态（如果需要）
                // fetchInitialPreview()
            }
        }
    }
}

struct UploadSleepDataBlankView_Previews: PreviewProvider {
    static var previews: some View {
        // 预览时可能需要注入一些模拟数据或服务
        UploadSleepDataBlankView()
            .preferredColorScheme(.dark) // 预览时使用暗色模式
    }
} 