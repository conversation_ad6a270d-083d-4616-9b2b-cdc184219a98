import SwiftUI

struct DatabaseDebugView: View {
    private let healthDataManager = HealthDataManager.shared
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("数据库调试工具")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top, 30)
                
                // 用户数据部分
                VStack(alignment: .leading, spacing: 10) {
                    Text("用户数据")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    <PERSON><PERSON>(action: {
//                        healthDataManager.printAllUsersAsJSON()
                    }) {
                        HStack {
                            Image(systemName: "person.fill")
                            Text("打印所有用户数据")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical)
                .background(Color(.systemGray6))
                .cornerRadius(15)
                .padding(.horizontal)
                
                // 睡眠数据部分
                VStack(alignment: .leading, spacing: 10) {
                    Text("睡眠数据")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    But<PERSON>(action: {
                        healthDataManager.printAllSleepDataAsJSON()
                    }) {
                        HStack {
                            Image(systemName: "moon.fill")
                            Text("打印所有睡眠数据")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.purple)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .padding(.horizontal)
                    
                    Button(action: {
                        createTestSleepData()
                    }) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                            Text("创建测试睡眠数据")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .padding(.horizontal)
                    
                    Button(action: {
                        cleanupDuplicateSleepData()
                    }) {
                        HStack {
                            Image(systemName: "trash.circle.fill")
                            Text("清理重复睡眠数据")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .padding(.horizontal)
                    
                    // 添加新测试按钮
                    Button(action: {
                        testDataRetrieval()
                    }) {
                        HStack {
                            Image(systemName: "doc.text.magnifyingglass")
                            Text("测试数据检索功能")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical)
                .background(Color(.systemGray6))
                .cornerRadius(15)
                .padding(.horizontal)
                
                Section(header: Text("API测试工具").font(.headline)) {
                    NavigationLink(destination: HeartRateAPITestView()) {
                        HStack {
                            Image(systemName: "heart.fill")
                                .foregroundColor(.red)
                            Text("心率API测试")
                        }
                    }
                    
                    NavigationLink(destination: CalorieAPITestView()) {
                        HStack {
                            Image(systemName: "flame.fill")
                                .foregroundColor(.orange)
                            Text("卡路里API测试")
                        }
                    }
                    
                    NavigationLink(destination: ActivityAPITestView()) {
                        HStack {
                            Image(systemName: "figure.walk")
                                .foregroundColor(.green)
                            Text("活动数据API测试")
                        }
                    }
                    
                    NavigationLink(destination: StepDetailTestView()) {
                        HStack {
                            Image(systemName: "footprints.fill")
                                .foregroundColor(.blue)
                            Text("步数明细数据测试")
                        }
                    }
                }
                
                Spacer()
            }
        }
        .background(Color(.systemBackground))
        .onAppear {
            print("数据库调试视图已加载")
        }
    }
    
    // 清理重复睡眠数据
    private func cleanupDuplicateSleepData() {
        let userId = UserDefaults.standard.string(forKey: "userId") ?? "current_user"
        print("开始为用户 \(userId) 清理重复睡眠数据...")
        let deletedCount = healthDataManager.cleanupDuplicateSleepData(userId: userId)
        print("清理完成，共删除了 \(deletedCount) 条重复记录。")
        // 这里可以添加一个 @State 变量来在 UI 上显示结果
    }
    
    // 创建测试睡眠数据
    private func createTestSleepData() {
        print("===== 开始创建测试睡眠数据 =====")
        let userId = "current_user"
        
        // 首先删除现有的用户，确保重新创建
//        if healthDataManager.getUser(id: userId) != nil {
//            print("找到现有用户，尝试删除...")
//            let deleted = healthDataManager.deleteUser(id: userId)
//            print("删除用户结果: \(deleted ? "成功" : "失败")")
//        }
//        
//        // 创建新用户，确保ID格式正确
//        healthDataManager.createUser(id: userId, name: "测试用户", email: "<EMAIL>", completion: {bool in
//            print("创建测试用户: \(bool ? "成功" : "失败")")
//            if !bool {
//                print("创建用户失败，尝试直接添加睡眠数据...")
//            }
//        })
        
        
        
        
        // 创建睡眠数据
        let now = Date()
        let calendar = Calendar.current
        
        // 昨晚的睡眠数据（使用当前时间往前推）
        let yesterdayEnd = calendar.date(byAdding: .hour, value: -1, to: now)!
        let yesterdayStart = calendar.date(byAdding: .hour, value: -8, to: yesterdayEnd)!
        
        print("创建睡眠数据：开始时间=\(yesterdayStart)，结束时间=\(yesterdayEnd)")
        
        let success = healthDataManager.addSleep(
            userId: userId,
            startTime: yesterdayStart,
            endTime: yesterdayEnd,
            totalMinutes: 420, // 7小时
            deepMinutes: 90,   // 1.5小时
            lightMinutes: 240, // 4小时
            remMinutes: 60,    // 1小时
            awakeMinutes: 30,  // 0.5小时
            score: 84,         // 睡眠评分
            efficiency: 85,    // 睡眠效率
            deviceId: "test_device_001",
            sleepStages: [
                ("deep", yesterdayStart, 90),
                ("light", calendar.date(byAdding: .minute, value: 90, to: yesterdayStart)!, 120),
                ("rem", calendar.date(byAdding: .minute, value: 210, to: yesterdayStart)!, 60),
                ("light", calendar.date(byAdding: .minute, value: 270, to: yesterdayStart)!, 120),
                ("awake", calendar.date(byAdding: .minute, value: 390, to: yesterdayStart)!, 30)
            ]
        )
        
        print("添加睡眠数据结果: \(success ? "成功" : "失败")")
        
        // 验证数据是否确实添加成功
        if let sleepData = healthDataManager.getLatestSleep(userId: userId) {
            print("✅ 成功验证: 能够检索到刚添加的睡眠数据")
            print("睡眠评分: \(sleepData.score)")
            print("睡眠总时长: \(sleepData.totalMinutes)分钟")
        } else {
            print("❌ 验证失败: 无法检索到刚添加的睡眠数据")
        }
        
        // 打印所有用户及其睡眠数据
        print("所有用户及ID:")
//        if let users = healthDataManager.getAllUsers() {
//            for user in users {
//                print("用户ID: \(user.id ?? "未知"), 名称: \(user.name ?? "未知")")
//                if let sleepData = healthDataManager.getLatestSleep(userId: user.id ?? "") {
//                    print(" - 有睡眠数据，评分: \(sleepData.score)")
//                } else {
//                    print(" - 没有睡眠数据")
//                }
//            }
//        } else {
//            print("无法获取用户列表")
//        }
        
        print("===== 测试睡眠数据创建结束 =====")
    }
    
    // 新增的测试数据检索功能
    private func testDataRetrieval() {
        print("===== 开始测试数据检索功能 =====")
        
        // 检查当前用户情况
        let userId = "current_user"
//        if let user = healthDataManager.getUser(id: userId) {
//            print("✅ 找到current_user: \(user.name ?? "未知名称")")
//        } else {
//            print("❌ 未找到current_user，尝试创建...")
//            healthDataManager.createUser(id: userId, name: "测试用户", email: "<EMAIL>", completion: {created in
//                print("创建用户结果: \(created ? "成功" : "失败")")
//            })
//            
//        }
//        
//        // 检查所有用户
//        print("\n所有用户列表:")
//        if let users = healthDataManager.getAllUsers() {
//            for (index, user) in users.enumerated() {
//                print("[\(index+1)] ID: \(user.id ?? "未知"), 名称: \(user.name ?? "未知")")
//            }
//        } else {
//            print("无法获取用户列表")
//        }
        
        // 为当前用户添加一条简单的睡眠记录
        print("\n尝试添加简单睡眠记录...")
        let now = Date()
        let fiveMinutesAgo = Calendar.current.date(byAdding: .minute, value: -5, to: now)!
        let oneHourAgo = Calendar.current.date(byAdding: .hour, value: -1, to: now)!
        
        let simpleSuccess = healthDataManager.addSleep(
            userId: userId,
            startTime: oneHourAgo,
            endTime: fiveMinutesAgo,
            totalMinutes: 55,
            deepMinutes: 20,
            lightMinutes: 30,
            remMinutes: 5,
            awakeMinutes: 0,
            score: 84,
            efficiency: 100,
            deviceId: "test_simple",
            sleepStages: []
        )
        print("简单睡眠记录添加结果: \(simpleSuccess ? "成功" : "失败")")
        
        // 尝试检索当前用户的睡眠数据
        print("\n尝试检索current_user的睡眠数据...")
        if let sleepData = healthDataManager.getLatestSleep(userId: userId) {
            print("✅ 成功检索到睡眠数据:")
            print(" - 睡眠评分: \(sleepData.score)")
            print(" - 总时长: \(sleepData.totalMinutes)分钟")
            print(" - 深睡时长: \(sleepData.deepMinutes)分钟")
            print(" - 设备ID: \(sleepData.deviceId ?? "未知")")
        } else {
            print("❌ 无法检索到睡眠数据")
        }
        
        // 尝试使用服务计算评分
        print("\n尝试使用SleepScoreService...")
        let sleepScoreService = SleepScoreService.shared
        let testScore = sleepScoreService.calculateSleepScore(
            sleepDuration: 420,
            bedDuration: 450,
            deepSleepDuration: 90,
            wakeCount: 2
        )
        print("通过服务计算的测试评分: \(testScore)")
        
        print("===== 数据检索测试结束 =====")
    }
}

struct DatabaseDebugView_Previews: PreviewProvider {
    static var previews: some View {
        DatabaseDebugView()
    }
} 
