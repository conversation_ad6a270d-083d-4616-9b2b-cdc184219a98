//
//  MainInsightModel.swift
//  WindRing
//
//  Created by zx on 2025/5/20.
//

// 添加蓝牙连接状态枚举
enum BluetoothConnectionState {
    case disconnected // 断开连接
    case connecting   // 自动重连中
    case connected    // 连接状态
}

// 添加睡眠数据模型
struct SleepScoreResponse: Codable {
    let code: Int
    let data: SleepScoreData?
    let msg: String
}

struct SleepScoreData: Codable {
    let score: Int
    let scoreName: String?  // 修改为可选类型
    let deep: Int
    let light: Int
    let rem: Int
    let efficiency: Int?    // 修改为可选类型
    let totalSleep: Int?    // 修改为可选类型
}

// 添加体征评分数据模型
struct VitalScoreResponse: Codable {
    let code: Int
    let data: VitalScoreData?
    let msg: String
}

struct VitalScoreData: Codable {
    let score: Int
    let avgHr: Int
    let avgO2: Int
    let time: Int64? // 设为可选，因为API可能不返回此字段
}
