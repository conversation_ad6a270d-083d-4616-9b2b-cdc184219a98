//
//  GlossaryInfoPopup.swift
//  WindRing
//
//  Created by zx on 2025/7/3.
//

import SwiftUI

enum GlossaryKey: String, CaseIterable, Codable {
    case sleepScore = "sleep_score"
    case historyActivityCalories = "history_activity_calories"
    case activityScore = "activity_score"
    case vitalHRV = "vital_HRV"
    case historySleepAvgEfficiency = "history_sleep_avg_sleep_efficiency"
    case historySleepTotalTimeAsleep = "history_sleep_total_time_asleep"
    case vitalHeartRate = "vital_heart_rate"
    case sleepHeartRate = "sleep_heart_rate"
    case historyVitalSpO2 = "history_vital_spO2"
    case historyActivityStandingDuration = "history_activity_standing_dutation"
    case healthStatus = "health_status"
    case vitalSpO2 = "vital_spO2"
    case historyActivityTotalScore = "history_activity_total_activity_score"
    case stress = "stress"
    case historySleepScore = "history_sleep_score"
    case historyStressEvening = "history_stress_evening_stress"
    case historySleepSpO2 = "history_sleep_sleeping_spO2"
    case sleepHRV = "sleep_HRV"
    case historyVitalHRV = "history_vital_HRV"
    case historySleepTemperature = "history_sleep_sleeping_skin_temperature"
    case historyActivitySteps = "history_activity_steps"
    case vitalsStatus = "vitals_status"
    case historyVitalHeartRate = "history_vital_heart_rate"
    case historyStressValue = "history_stress_value"
    case historySleepHeartRate = "history_sleep_heart_rate"
    case historySleepHRV = "history_sleep_sleeping_HRV"
}

struct InfoButtonWithGlossaryPopup: View {
    @StateObject private var glossaryManager = UserSettings.shared
    var showKey:String = GlossaryKey.healthStatus.rawValue
    var body: some View {
        Button(action: {
            if let item = UserSettings.shared.glossaryItem(forKey: showKey) {
                BottomSheetManager.shared.present(key: showKey) {
                    GlossaryInfoPopup(glossaryItem: item,showKey: showKey, onDismiss: {
                        BottomSheetManager.shared.dismiss(key: showKey)
                    })
                }
            } else {
                print("Glossary item with key \(showKey) not found.")
            }
        }) {
            Image("疑问")
                .resizable()
                .frame(width: 14, height: 14)
                .foregroundColor(.white.opacity(0.7))
        }
        .padding(.leading, 4)
    }
}

struct GlossaryInfoPopup: View {
    let glossaryItem: GlossaryItem
    var showKey:String = GlossaryKey.healthStatus.rawValue
    let onDismiss: () -> Void
//    @Binding var isPresented: Bool
    

    private var title: String {
        return ""
//        if VersionUpdateService.shared.status == 1 {
//            return glossaryItem.title
//        } else {
//            if showKey == GlossaryKey.historySleepScore.rawValue {
//                return NSLocalizedString("glossary_title_history_sleep_score", comment: "Sleep Score glossary title")
//            } else if showKey == GlossaryKey.healthStatus.rawValue {
//                return NSLocalizedString("glossary_title_health_status", comment: "Health Status glossary title")
//            } else {
//                return glossaryItem.title
//            }
//        }
    }

    private var content: String {
        if VersionUpdateService.shared.status == 1{
            return LanguageManager.shared.selectedLanguage == "zh-Hans" ? glossaryItem.zhCn : glossaryItem.enUs
        }else{
            if showKey == GlossaryKey.historySleepScore.rawValue{
                return NSLocalizedString("glossary_content_history_sleep_score", comment: "Sleep score glossary content")
            }else if showKey == GlossaryKey.healthStatus.rawValue{
                return NSLocalizedString("glossary_content_health_status", comment: "Health status glossary content")
            }else{
                return LanguageManager.shared.selectedLanguage == "zh-Hans" ? glossaryItem.zhCn : glossaryItem.enUs
            }
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text(title)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
                Spacer()
                Button(action: { onDismiss() }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.white.opacity(0.7))
                        .padding()
                }
            }
            .padding(.top, 20)
            .padding(.horizontal)

            ScrollView {
                Text(content)
                    .font(.system(size: 16))
                    .foregroundColor(.white.opacity(0.85))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 40)
            }
        }
        .background(Color(hex: "#242733"))
        .cornerRadius(20, corners: [.topLeft, .topRight])
        .frame(height: UIScreen.main.bounds.height * 0.6)
        .shadow(radius: 20)
    }
}

//struct GlossaryInfoPopup: View {
//    @Binding var isPresented: Bool
//    let glossaryItem: GlossaryItem
//
//    private var content: String {
//        LanguageManager.shared.selectedLanguage == "zh-Hans" ? glossaryItem.zhCn : glossaryItem.enUs
//    }
//
//    private var title: String {
//        glossaryItem.title
//    }
//
//    var body: some View {
//        VStack(alignment: .leading, spacing: 0) {
//            HStack {
//                Text(title)
//                    .font(.system(size: 20, weight: .semibold))
//                    .foregroundColor(.white)
//                Spacer()
//                Button(action: { isPresented = false }) {
//                    Image(systemName: "xmark")
//                        .font(.system(size: 16, weight: .bold))
//                        .foregroundColor(.white.opacity(0.7))
//                }
//            }
//            .padding(20)
//
//            ScrollView {
//                Text(content)
//                    .font(.system(size: 16))
//                    .foregroundColor(.white.opacity(0.85))
//                    .frame(maxWidth: .infinity, alignment: .leading)
//                    .padding(.horizontal, 20)
//                    .padding(.bottom, 40)
//            }
//        }
//        .background(Color(hex: "#242733"))
//        .cornerRadius(20, corners: [.topLeft, .topRight])
//        .frame(height: UIScreen.main.bounds.height * 0.6)
//        .shadow(radius: 20)
//    }
//}
