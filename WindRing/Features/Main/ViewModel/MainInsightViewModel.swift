//
//  MainInsightViewModel.swift
//  WindRing
//
//  Created by zx on 2025/5/20.
//
// 添加MainInsightViewModel类
import SwiftUI
import Combine
// 确保导入必要的模块
// import WindRingDeviceService // 假设 WindRingDeviceService 在此模块
// import CRPSmartRing // 假设 CRPSmartRingManage 在此模块
// import AuthService // 假设 AuthService 在此模块
// import AppGlobals // 假设 AppGlobals 在此模块

// MARK: - Data Models (确保这些模型在项目中已定义或在此处定义)
// 这些模型定义需要与您项目中其他地方的定义一致
// 如果它们在其他地方定义，请确保导入相应的模块

//struct SleepScoreResponse: Codable {
//    let code: Int
//    let data: SleepScoreData?
//    let msg: String
//}
//
//struct SleepScoreData: Codable, Identifiable {
//    var id = UUID() // 添加以符合 Identifiable
//    let score: Int?
//    let totalSleep: Int? // 总睡眠时间，单位分钟
//    let deep: Int // 深睡时长，单位分钟
//    let light: Int // 浅睡时长，单位分钟
//    let rem: Int // REM时长，单位分钟
//    let awake: Int // 清醒时长，单位分钟
//    let fallAsleepTime: String? // 入睡时间 "HH:mm"
//    let wakeTime: String? // 醒来时间 "HH:mm"
//
//    // 如果API返回的字段名不一致，使用CodingKeys
//    enum CodingKeys: String, CodingKey {
//        case score, totalSleep, deep, light, rem, awake
//        case fallAsleepTime = "fallsleep" // 假设API返回的是fallsleep
//        case wakeTime = "wakeup"       // 假设API返回的是wakeup
//    }
//}
//
//struct StressScoreResponse: Codable {
//    let code: Int
//    let data: StressScoreData?
//    let msg: String
//}
//
//struct StressScoreData: Codable, Identifiable {
//    var id = UUID() // 添加以符合 Identifiable
//    let score: Int?
//    let stress: Int // 压力值
//    let stressState: Int // 压力状态: 1-放松, 2-正常, 3-有压力, 4-高压
//
//    enum CodingKeys: String, CodingKey {
//        case score, stress, stressState
//    }
//}
//
//struct VitalScoreResponse: Codable {
//    let code: Int
//    let data: VitalScoreData?
//    let msg: String
//}
//
//struct VitalScoreData: Codable, Identifiable {
//    var id = UUID() // 添加以符合 Identifiable
//    let score: Int?
//    let avgHr: Int // 平均心率
//    let avgO2: Int // 平均血氧
//
//    enum CodingKeys: String, CodingKey {
//        case score, avgHr, avgO2
//    }
//}
//
//struct ActivityScoreResponse: Codable {
//    let code: Int
//    let data: ActivityScoreData?
//    let msg: String
//}
//
//struct ActivityScoreData: Codable, Identifiable {
//    var id = UUID() // 添加以符合 Identifiable
//    let score: Int?
//    let steps: Int
//    let calories: Int // 卡路里，单位 kcal
//    let time: Int     // 活动时长，单位秒
//
//    enum CodingKeys: String, CodingKey {
//        case score, steps, calories, time
//    }
//}
//
struct CalendarDataResponse: Codable {
    let code: Int
    let data: [CalendarDataItem]?
    let msg: String
}
//
struct CalendarDataItem: Codable {
    let flag: Bool
    let time: String // "yyyy-MM-dd'T'HH:mm:ss"
}

class MainInsightViewModel: ObservableObject {
    @Published var sharedDateViewModel = SharedDateViewModel.shared // <--- 确保 SharedDateViewModel 可访问
    // 添加步数相关的发布属性
    @Published var realTimeSteps: Int = 0 {
        didSet {
            if realTimeSteps != oldValue {
                // 步数发生变化，通知外部可能需要更新UI
                self.objectWillChange.send()
                
                // 打印步数变化
                print("步数从 \(oldValue) 更新为 \(realTimeSteps)")
                
                // 根据步数更新实时卡路里
                updateRealTimeCaloriesFromSteps()
            }
        }
    }
    
    // 添加卡路里相关的发布属性
    @Published var realTimeCalories: Int = 0 {
        didSet {
            if realTimeCalories != oldValue {
                // 卡路里发生变化，通知外部可能需要更新UI
                self.objectWillChange.send()
                
                // 打印卡路里变化
                print("卡路里从 \(oldValue) 更新为 \(realTimeCalories)")
            }
        }
    }

    // MARK: - Published Properties for Network Data
    @Published var sleepData: SleepScoreData?
    @Published var isLoadingSleepData = false
    @Published var sleepDataError: String?
    
    @Published var stressData: StressScoreData?
    @Published var isLoadingStressData = false
    @Published var stressDataError: String?
    
    @Published var vitalData: VitalScoreData?
    @Published var isLoadingVitalData = false
    @Published var vitalDataError: String?
    
    @Published var activityData: ActivityScoreData?
    @Published var isLoadingActivityData = false
    @Published var activityDataError: String?
    
    @Published var calendarDatesWithData: [Date] = []
    @Published var isLoadingCalendarData = false
    
    private var lastActivityScoreRequestTime: Date? = nil
    private var cancellables = Set<AnyCancellable>()
    
    // 检查指定日期是否有数据
    func checkDataExistsForDate(_ dateString: String) -> Bool {
        // 构建API基础URL
        let apiBaseURL = AppGlobals.apiBaseURL // 使用 AppGlobals
        
        // 创建信号量用于同步
        let semaphore = DispatchSemaphore(value: 0)
        var hasAnyData = false
        
        // 创建URL请求
        let endpoints = [
            "/app-api/iot/sleep/getScore",
            "/app-api/iot/stress/getScore",
            "/app-api/iot/vital/getScore",
            "/app-api/iot/activity/getScore"
        ]
        
        for endpoint in endpoints {
            guard let url = URL(string: "\(apiBaseURL)\(endpoint)?date=\(dateString)") else { continue }
            
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            request.addValue("1", forHTTPHeaderField: "tenant-id")
            
            // 添加认证令牌
            if let token = AuthService.shared.currentToken?.accessToken, !token.isEmpty { //确保AuthService可访问
                request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            } else {
                request.addValue("Bearer test1", forHTTPHeaderField: "Authorization") // Fallback test token
            }
            
            URLSession.shared.dataTask(with: request) { data, response, error in
                defer { semaphore.signal() }
                
                guard let data = data,
                      let json = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                      let responseData = json["data"] as? [String: Any],
                      !responseData.isEmpty else { return }
                
                hasAnyData = true
            }.resume()
            
            _ = semaphore.wait(timeout: .now() + 2) // 设置2秒超时
        }
        
        return hasAnyData
    }
    
    // 获取实时步数的方法
    @objc func fetchRealTimeSteps() {
        // 检查设备是否已连接
        let deviceService = WindRingDeviceService.shared // 确保 WindRingDeviceService 可访问
        if deviceService.connectionState == .connected { // 确保 .connected 可访问
            // 使用getTrainingData获取今天(day=0)的步数数据
            CRPSmartRingManage.shared.getHistoricalExerciseStats(sharedDateViewModel.selectedDate) { result in //确保CRPSmartRingManage可访问
                switch result {
                case .success(let record):
                    // 在主线程更新步数
                    DispatchQueue.main.async {
                        // 获取并更新MainInsightView中的lastDataUpdateTime变量
                        let notificationName = Notification.Name("UpdateLastDataUpdateTime")
                        NotificationCenter.default.post(name: notificationName, object: nil)
                        
                        // 先检查变化是否足够大
                        let stepsChanged = abs(self.realTimeSteps - record.steps) > 100
                        let caloriesChanged = abs(self.realTimeCalories - record.calory) > 80
                        
                        // 如果有数据变化且足够大，才进行动画更新
                        if stepsChanged || caloriesChanged {
                            // 记录旧值用于日志
                            let oldSteps = self.realTimeSteps
                            let oldCalories = self.realTimeCalories
                            
                            // 添加更平滑的动画
                            withAnimation(.easeInOut(duration: 0.5)) {
                                if stepsChanged {
                                    self.realTimeSteps = record.steps
                                    print("定时器获取步数成功：当前\(oldSteps)步 更新为\(record.steps)步")
                                }
                                
                                if caloriesChanged {
                                    self.realTimeCalories = record.calory
                                    print("定时器获取卡路里成功：当前\(oldCalories)卡 更新为\(record.calory)卡")
                                }
                            }
                        } else {
                            print("步数和卡路里变化不大，保持UI不变")
                        }
                    }
                default:
                    print("定时获取实时数据失败")
                }
            }
        }
    }
    
    // 根据步数更新卡路里的方法
    private func updateRealTimeCaloriesFromSteps() {
        // 简单估算：根据步数计算卡路里消耗
        // 如果已经有从设备获取的卡路里数据，则不需要再次计算
        if realTimeCalories == 0 {
            // 每25步大约消耗1卡路里
            let estimatedCalories = realTimeSteps / 25
            realTimeCalories = estimatedCalories
            print("根据步数 \(realTimeSteps) 估算卡路里: \(estimatedCalories)")
        }
    }
    
    // 处理步数更新通知
    @objc private func handleStepsUpdate(_ notification: Notification) {
        if let object = notification.object {
            print("收到步数更新通知，对象类型: \(type(of: object))")
            
            // 使用反射获取步数值和卡路里值
            let mirror = Mirror(reflecting: object)
            
            // 提取步数
            var stepValue: Int? = nil
            var caloryValue: Int? = nil
            
            for child in mirror.children {
                if child.label == "steps" || child.label == "step" {
                    if let value = child.value as? Int {
                        stepValue = value
                    }
                }
                // 提取卡路里值
                if child.label == "calory" || child.label == "calories" || child.label == "cal" {
                    if let value = child.value as? Int {
                        caloryValue = value
                    }
                }
            }
            
            // 更新步数和卡路里 - 始终使用最新值
            DispatchQueue.main.async {
                if let steps = stepValue {
                    // 添加动画效果
                    withAnimation(.easeInOut(duration: 0.3)) {
                        self.realTimeSteps = steps
                    }
                    print("通过反射获取步数成功: \(steps) 步")
                }
                
                if let calories = caloryValue {
                    // 添加动画效果
                    withAnimation(.easeInOut(duration: 0.3)) {
                        self.realTimeCalories = calories
                    }
                    print("通过反射获取卡路里成功: \(calories) 千卡")
                } else if let steps = stepValue {
                    // 如果有步数但没有卡路里，则根据步数更新卡路里
                    self.updateRealTimeCaloriesFromSteps()
                }
            }
            
            if stepValue == nil && caloryValue == nil {
                print("通过反射无法获取步数和卡路里值，通知对象缺少相关属性")
            }
        } else {
            print("收到步数更新通知，但没有附带对象")
        }
    }
    
    // MARK: - 初始化和生命周期方法
    
    init() {
        // 监听步数更新通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleStepsUpdate(_:)),
            name: .stepsUpdated, // 确保 .stepsUpdated 有定义
            object: nil
        )
    }
    
    deinit {
        cancellables.forEach { $0.cancel() }
    }
    
    // MARK: - 私有属性
    
    // 添加上次数据更新时间，用于节流UI更新
    private var lastDataUpdateTime: Date?
    
    // 检查指定日期是否有数据
    private func hasDataForDate(_ date: Date) -> Bool {
        // 实现检查逻辑
        return true
    }

    // MARK: - Network Request Functions
    
    private func commonNetworkRequest<T: Decodable>(urlString: String, method: String = "GET", completion: @escaping (Result<T, Error>) -> Void) {
        guard let url = URL(string: urlString) else {
            completion(.failure(NSError(domain: "InvalidURL", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = method
        request.addValue("1", forHTTPHeaderField: "tenant-id")
        
        if let token = AuthService.shared.currentToken?.accessToken, !token.isEmpty { //确保AuthService可访问
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        } else {
            print("警告: ViewModel 中没有可用的访问令牌，使用测试令牌")
            request.addValue("Bearer test1", forHTTPHeaderField: "Authorization") // Fallback test token
        }
        request.addValue("application/json", forHTTPHeaderField: "Accept")

        print("发送请求 (ViewModel): \(url)")

        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    completion(.failure(error))
                    return
                }

                if let httpResponse = response as? HTTPURLResponse {
                    print("HTTP状态码 (ViewModel): \(httpResponse.statusCode) for \(url)")
                    if !(200...299).contains(httpResponse.statusCode) {
                        let httpError = NSError(domain: "HTTPError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "HTTP错误: \(httpResponse.statusCode)"])
                        completion(.failure(httpError))
                        return
                    }
                }

                guard let data = data else {
                    completion(.failure(NSError(domain: "NoData", code: -2, userInfo: [NSLocalizedDescriptionKey: "没有返回数据"])))
                    return
                }
                
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("原始响应 (ViewModel) for \(url): \(jsonString)")
                }

                do {
                    let decodedResponse = try CleanJSONDecoder().decode(T.self, from: data)
                    completion(.success(decodedResponse))
                } catch let decodingError {
                    print("解析错误 (ViewModel) for \(url): \(decodingError)")
                    completion(.failure(decodingError))
                }
            }
        }.resume()
    }

    func fetchSleepScore() {
        isLoadingSleepData = true
        sleepDataError = nil
        
        let calendar = Calendar.current
        let adjustedDate = calendar.date(byAdding: .day, value: -1, to: sharedDateViewModel.selectedDate) ?? sharedDateViewModel.selectedDate
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = sharedDateViewModel.selectedDate.string()//dateFormatter.string(from: adjustedDate)
        
        let urlString = "\(AppGlobals.apiBaseURL)/app-api/iot/sleep/getScore?date=\(dateString)"
        
        commonNetworkRequest(urlString: urlString) { (result: Result<SleepScoreResponse, Error>) in
            self.isLoadingSleepData = false
            switch result {
            case .success(let response):
                if response.code == 200 || response.code == 0 {
                    self.sleepData = response.data
                    if response.data == nil {
                        self.sleepDataError = "该日期没有睡眠数据"
                    } else if response.data?.totalSleep == nil {
                         print("警告: 睡眠数据中有关键字段为空 (ViewModel)")
                    }
                } else {
                    self.sleepDataError = "API错误: \(response.msg)"
                }
            case .failure(let error):
                self.sleepDataError = "网络或解析错误: \(error.localizedDescription)"
            }
        }
    }

    func fetchStressScore() {
        isLoadingStressData = true
        stressDataError = nil
        
        let calendar = Calendar.current
        let adjustedDate = calendar.date(byAdding: .day, value: -1, to: sharedDateViewModel.selectedDate) ?? sharedDateViewModel.selectedDate
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = sharedDateViewModel.selectedDate.string()//dateFormatter.string(from: adjustedDate)

        let urlString = "\(AppGlobals.apiBaseURL)/app-api/iot/stress/getScore?date=\(dateString)"
        
        commonNetworkRequest(urlString: urlString) { (result: Result<StressScoreResponse, Error>) in
            self.isLoadingStressData = false
            switch result {
            case .success(let response):
                if response.code == 200 || response.code == 0 {
                    self.stressData = response.data
                    if response.data == nil {
                        self.stressDataError = "该日期没有压力数据"
                    }
                } else {
                    self.stressDataError = "API错误: \(response.msg)"
                }
            case .failure(let error):
                self.stressDataError = "网络或解析错误: \(error.localizedDescription)"
            }
        }
    }

    func fetchVitalScore() {
        isLoadingVitalData = true
        vitalDataError = nil

        let calendar = Calendar.current
//        let adjustedDate = calendar.date(byAdding: .day, value: -1, to: sharedDateViewModel.selectedDate) ?? sharedDateViewModel.selectedDate
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = sharedDateViewModel.selectedDate.string()//dateFormatter.string(from: adjustedDate)

        let urlString = "\(AppGlobals.apiBaseURL)/app-api/iot/vital/getScore?date=\(dateString)"

        commonNetworkRequest(urlString: urlString) { (result: Result<VitalScoreResponse, Error>) in
            self.isLoadingVitalData = false
            switch result {
            case .success(let response):
                if response.code == 200 || response.code == 0 {
                    self.vitalData = response.data
                    if response.data == nil {
                        self.vitalDataError = "该日期没有体征数据"
                    }
                } else {
                    self.vitalDataError = "API错误: \(response.msg)"
                }
            case .failure(let error):
                self.vitalDataError = "网络或解析错误: \(error.localizedDescription)"
            }
        }
    }

    func fetchActivityScore() {
        let currentTime = Date()
//        if let lastRequestTime = lastActivityScoreRequestTime, currentTime.timeIntervalSince(lastRequestTime) < 300 { // 5 分钟冷却
//            print("活动评分请求过于频繁 (ViewModel)，跳过此次请求")
//            // 即使跳过，也确保 isLoadingActivityData 为 false，以防UI卡在加载状态
//            // 但如果当前正在加载，则不应将其设置为 false
//            if !isLoadingActivityData {
//                 //如果当前没有在加载，可以什么都不做，或者确保它确实是false
//            } else {
//                // 如果正在加载，让它完成
//            }
//            return
//        }
        lastActivityScoreRequestTime = currentTime
        
        isLoadingActivityData = true
        activityDataError = nil

        let calendar = Calendar.current
        let adjustedDate = calendar.date(byAdding: .day, value: -1, to: sharedDateViewModel.selectedDate) ?? sharedDateViewModel.selectedDate
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = sharedDateViewModel.selectedDate.string()//dateFormatter.string(from: adjustedDate)

        let urlString = "\(AppGlobals.apiBaseURL)/app-api/iot/activity/getScore?date=\(dateString)"

        commonNetworkRequest(urlString: urlString) { (result: Result<ActivityScoreResponse, Error>) in
            self.isLoadingActivityData = false
            switch result {
            case .success(let response):
                if response.code == 200 || response.code == 0 {
                    // 确保UI变化使用动画以避免闪烁
                    withAnimation(.easeInOut(duration: 0.3)) {
                        self.activityData = response.data
                    }
                    if response.data == nil {
                        self.activityDataError = "该日期没有活动数据"
                    }
                } else {
                    self.activityDataError = "API错误: \(response.msg)"
                }
            case .failure(let error):
                self.activityDataError = "网络或解析错误: \(error.localizedDescription)"
            }
        }
    }
    
    func fetchCalendarData() {
        isLoadingCalendarData = true
        // calendarDatesWithData.removeAll() // 清空旧数据，或者根据需求决定是否清空

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd" // API 可能需要 yyyy-MM 格式，请确认
        let dateString = dateFormatter.string(from: sharedDateViewModel.selectedDate) // 或者 currentMonth

        let urlString = "\(AppGlobals.apiBaseURL)/app-api/iot/common/getCalenderData?date=\(dateString)"

        commonNetworkRequest(urlString: urlString) { (result: Result<CalendarDataResponse, Error>) in
            self.isLoadingCalendarData = false
            switch result {
            case .success(let response):
                if response.code == 200 || response.code == 0 {
                    var newDatesWithData: [Date] = []
                    if let items = response.data {
                        let itemDateFormatter = DateFormatter()
                        itemDateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss" // API返回的日期格式
                        itemDateFormatter.locale = Locale(identifier: "en_US_POSIX")
                        itemDateFormatter.timeZone = TimeZone(secondsFromGMT: 0) // 假设是UTC

                        for item in items {
                            if item.flag, let date = itemDateFormatter.date(from: item.time) {
                                newDatesWithData.append(date)
                            }
                        }
                    }
                    self.calendarDatesWithData = newDatesWithData
                    print("成功获取日历数据 (ViewModel)，共\(self.calendarDatesWithData.count)个日期有数据")
                } else {
                    print("日历数据API错误 (ViewModel): \(response.msg)")
                    self.calendarDatesWithData = [] // 出错时清空
                }
            case .failure(let error):
                print("日历数据网络或解析错误 (ViewModel): \(error.localizedDescription)")
                self.calendarDatesWithData = [] // 出错时清空
            }
        }
    }
    
    // 调用所有数据获取方法
    func fetchAllScores() {
        fetchSleepScore()
        fetchStressScore()
        fetchVitalScore()
        fetchActivityScore()
    }
}

// 添加环境键
private struct MainInsightViewModelKey: EnvironmentKey {
    static let defaultValue = MainInsightViewModel()
}

extension EnvironmentValues {
    var mainInsightViewModel: MainInsightViewModel {
        get { self[MainInsightViewModelKey.self] }
        set { self[MainInsightViewModelKey.self] = newValue }
    }
}

// 需要确保 CleanJSONDecoder 在项目中可用
// 如果没有，可以暂时使用 JSONDecoder，或者添加 CleanJSONDecoder 的实现
// class CleanJSONDecoder: JSONDecoder { ... }
// 假设 CleanJSONDecoder 已经存在
