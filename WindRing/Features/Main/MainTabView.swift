import SwiftUI
import Combine
import HealthKit // 如果需要的话
import CoreData   // 添加CoreData导入
import StoreKit
import CRPSmartRing  // 添加CRPSmartRing导入

// 使用普通导入而不是特定结构体导入，避免歧义
// 这些视图在Features目录下，它们应该可以直接访问
// import struct WindRing.Features.Profile.UserProfileView
// import struct WindRing.Features.Debug.DatabaseDebugView

// MARK: - MainTabViewModel
/// 主标签视图的视图模型
class MainTabViewModel: ObservableObject {
    @Published var showDebugView = false
    @Published var showHeartRateFlowTest = false
    @Published var showStepDetailTest = false
    @Published var showHeartRateDetailTest = false
    @Published var showHRVDetailTest = false
    
    @Published var glossaryItems: [GlossaryItem] = []
    @Published var errorMessage: String?

    private var cancellables = Set<AnyCancellable>()
    // 打开调试视图
    func openDebugView() {
        showDebugView = true
    }
    
    // 打开心率数据流程测试
    func openHeartRateFlowTest() {
        showHeartRateFlowTest = true
    }
    
    // 打开步数明细测试
    func openStepDetailTest() {
        showStepDetailTest = true
    }
    
    // 打开心率明细测试
    func openHeartRateDetailTest() {
        showHeartRateDetailTest = true
    }
    
    // 打开HRV明细测试
    func openHRVDetailTest() {
        showHRVDetailTest = true
    }
    
    func loadGlossary() {
        APIService.shared.getGlossaryList()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.errorMessage = error.localizedDescription
                }
            } receiveValue: { [weak self] dict in
                var items : [GlossaryItem] = []

                for value in dict.values {
                    do {
                        let data = try JSONSerialization.data(withJSONObject: value)
                        let item = try CleanJSONDecoder().decode(GlossaryItem.self, from: data)
                        items.append(item)
                    } catch {
                        print("❌ 解析失败: \(error)")
                    }
                }
                print(dict)
                UserSettings.shared.loadGlossaryDict(dict)
//                UserSettings.shared.updateGlossaryItems(items: items)
            }
            .store(in: &cancellables)
    }
}

/// 主标签视图
struct MainTabView: View {
    // MARK: - 属性
    @StateObject private var viewModel = MainTabViewModel()
    var cancellables = Set<AnyCancellable>()
    @State private var selectedTab = 0
    @Environment(\.colorScheme) private var colorScheme
//    @EnvironmentObject var mqttService: MQTTService
//    @EnvironmentObject var mqttSyncService: MQTTSyncService
//    @EnvironmentObject var deviceService: WindRingDeviceService
//    @EnvironmentObject var localizationService: LocalizationService
    
    @State private var mqttSyncService: MQTTSyncService?
    @State private var mqttService: MQTTService?
    @State private var deviceService: WindRingDeviceService?
    @State private var rawDataUploadService: RawDataUploadService?
    
    // 健康服务引用
    private let sleepScoreService = SleepScoreService.shared
    private let stressScoreService = StressScoreService.shared
    private let activityScoreService = ActivityScoreService.shared
    
    
    @StateObject private var glossaryPopup = UserSettings.shared
    
    @EnvironmentObject var deepLinkManager: DeepLinkManager
    @State private var navigateToShar = false
    
    // MARK: - 主视图
    var body: some View {
        NavigationStack {
            TabView(selection: $selectedTab) {
                // 第一个标签页 - Insight（健康洞察）
                MainInsightView()
//                    .environmentObject(mqttService)
//                    .environmentObject(mqttSyncService)
                    .tabItem {
                        Label {
                            Text("insight".localized)
                                .foregroundColor(selectedTab == 0 ? .white : Color(hex: "#596480"))
                        } icon: {
                            Image(selectedTab == 0 ? "Insight_selected" : "Insight_unselected")
                                .resizable()
                                .scaledToFit()
                        }
                    }
                    .tag(0)
                
                // 第二个标签页 - Review（显示健康历史数据）
                HistoryDataView()
//                    .environmentObject(mqttService)
//                    .environmentObject(mqttSyncService)
                    .tabItem {
                        Label {
                            Text("review".localized)
                        } icon: {
                            Image(selectedTab == 1 ? "Review_selected" : "Review_unselected")
                                .resizable()
                                .scaledToFit()
                        }
                    }
                    .tag(1)
                
                // 第三个标签页 - 用户档案
                UserProfileView()
//                    .environmentObject(mqttService)
//                    .environmentObject(mqttSyncService)
                    .environmentObject(viewModel)  // 传递视图模型
                    .tabItem {
                        Label {
                            Text("mine".localized)
                        } icon: {
                            Image(selectedTab == 2 ? "Mine_selected" : "Mine_unselected")
                                .resizable()
                                .scaledToFit()
                        }
                    }
                    .tag(2)
            }
            // Sheet 层
            
//            NavigationLink(
//                destination: FamilySharingView(),
//                isActive: $navigateToShar,
//                label: { EmptyView() }
//            )
//            .hidden()
            
//            if let path = deepLinkManager.deepLinkPath {
//                Text("收到 DeepLink 路径: \(path)")
//                    .foregroundColor(.red)
//            }
//
//            NavigationLink("去详情", destination: HeartRateDataFlowTest())
            // 调试视图导航链接
//            NavigationLink(destination: DatabaseDebugView(), isActive: $viewModel.showDebugView) {
//                EmptyView()
//            }
//            
//            // 心率数据流程测试视图导航链接
//            NavigationLink(destination: HeartRateDataFlowTest(), isActive: $viewModel.showHeartRateFlowTest) {
//                EmptyView()
//            }
//            
//            // 步数明细测试视图导航链接
//            NavigationLink(destination: StepDetailTestView(), isActive: $viewModel.showStepDetailTest) {
//                EmptyView()
//            }
//            
//            // 心率明细测试视图导航链接
//            NavigationLink(destination: HeartRateDetailTestView(), isActive: $viewModel.showHeartRateDetailTest) {
//                EmptyView()
//            }
//            
//            // HRV明细测试视图导航链接
//            NavigationLink(destination: HRVDetailTestView(), isActive: $viewModel.showHRVDetailTest) {
//                EmptyView()
//            }
            
            .environmentObject(BottomSheetManager.shared)
            .accentColor(Color(hex: "#0A7B83"))
            .onAppear {
                configureTabBarAppearance()
                setupNotificationObservers()
            }
//            .bottomSheet(isPresented: $glossaryPopup.isShowing, backgroundOpacity: 0.4, onDismiss: {
//                print("🚀 底部弹窗被关闭")
//            }) {
//                if let item = glossaryPopup.currentItem {
//                    GlossaryInfoPopup(isPresented: $glossaryPopup.isShowing, glossaryItem: item)
//                }
////                SheetContentView(isPresented: $showSheet)
//            }
//            .sheet(isPresented: $glossaryPopup.isShowing) {
//                if let item = glossaryPopup.currentItem {
//                    GlossaryInfoPopup(isPresented: $glossaryPopup.isShowing, glossaryItem: item)
//                }
//            }
//            .toast(isPresented: $glossaryPopup.isShowing) {
//                if let item = glossaryPopup.currentItem {
//                    GlossaryInfoPopup(isPresented: $glossaryPopup.isShowing, glossaryItem: item)
//                }
//            }
//            .bottomSheet(isPresented: $glossaryPopup.isShowing) {
//                
//            }
            .toast(isPresented: $deepLinkManager.presentingAlert) {
              if let deepLink = deepLinkManager.activeDeepLink {
                  // 居中的弹窗
                  HealthDataShareAlert(
                      type: deepLink.type, nickname: deepLink.nickname,
                      onConfirm: {
                          print("✅ 接受")

                          let dict: [String: Any] = [
                              "uuid": deepLink.uuid,
                              "type": deepLink.type.rawValue,
                              "sleep": 0,
                              "activity": 0,
                              "stress": 0,
                              "healthStatus": 0
                          ]
                          
                          Task {
                              do {
                                  let response = try await APIService.shared.confirmShareInfo(requestBody: dict)
                                  await MainActor.run {
//                                      if response.code == 0 && response.data == true {
                                          print("✅ Share confirmation successful")
                                          if deepLink.type.hashValue == 0{
                                              navigateToShar = true
                                          }
                                           // Trigger navigation
//                                      } else {
//                                          print("❌ Share confirmation failed: \(response.msg)")
//                                          // Optionally show an error to the user here.
//                                      }
                                      deepLinkManager.dismiss()
                                  }
                              } catch {
//                                  print("❌ API call to confirm share failed: \(error)")
                                  
                                  await MainActor.run {
                                      // Optionally show an error to the user here.
                                      deepLinkManager.dismiss()
                                      if let netError = error as? NetworkError{
                                          netError.localizedDescription.showToast()
                                      }
                                      
                                  }
                              }
                          }
                      },
                      onCancel: {
                          print("❌ 拒绝")
                          deepLinkManager.dismiss()
                      }
                  )
              }
            }
            
            
        }
        
        
        .onAppear {
            // 初始化服务
            initializeServices()
           
        }
        .onDisappear {
            // 移除通知监听
            NotificationCenter.default.removeObserver(
                self,
                name: Notification.Name("OpenDatabaseDebugView"),
                object: nil
            )
            NotificationCenter.default.removeObserver(
                self,
                name: Notification.Name("OpenHeartRateFlowTest"),
                object: nil
            )
            NotificationCenter.default.removeObserver(
                self,
                name: Notification.Name("OpenHeartRateDetailTest"),
                object: nil
            )
            NotificationCenter.default.removeObserver(
                self,
                name: Notification.Name("OpenHRVDetailTest"),
                object: nil
            )
        }
    }
    
    // MARK: - TabBar 配置
    private func configureTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        
        // 设置背景颜色
        appearance.backgroundColor = UIColor(red: 0.11, green: 0.125, blue: 0.165, alpha: 1.0) // #1C202A
        
        // 配置标签项样式
        let itemAppearance = UITabBarItemAppearance()
        
        // 选中状态
        itemAppearance.selected.iconColor = UIColor.white
        itemAppearance.selected.titleTextAttributes = [.foregroundColor: UIColor.white]
        
        // 未选中状态
        itemAppearance.normal.iconColor = UIColor(red: 0.349, green: 0.392, blue: 0.502, alpha: 1.0) // #596480
        itemAppearance.normal.titleTextAttributes = [.foregroundColor: UIColor(red: 0.349, green: 0.392, blue: 0.502, alpha: 1.0)]
        
        // 应用样式到各个状态
        appearance.stackedLayoutAppearance = itemAppearance
        appearance.inlineLayoutAppearance = itemAppearance
        appearance.compactInlineLayoutAppearance = itemAppearance
        
        // 应用到 UITabBar
        UITabBar.appearance().standardAppearance = appearance
        if #available(iOS 15.0, *) {
            UITabBar.appearance().scrollEdgeAppearance = appearance
        }
        
        // 设置圆角
        DispatchQueue.main.async {
            if #available(iOS 15.0, *) {
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let tabBarController = windowScene.windows.first?.rootViewController as? UITabBarController {
                    configureTabBarCorners(tabBarController.tabBar)
                }
            } else {
                if let tabBarController = UIApplication.shared.windows.first?.rootViewController as? UITabBarController {
                    configureTabBarCorners(tabBarController.tabBar)
                }
            }
        }
    }
    
    private func configureTabBarCorners(_ tabBar: UITabBar) {
        tabBar.layer.masksToBounds = true
        tabBar.layer.cornerRadius = 10
        tabBar.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        
        // 添加阴影效果
        tabBar.layer.shadowColor = UIColor.black.cgColor
        tabBar.layer.shadowOffset = CGSize(width: 0, height: -2)
        tabBar.layer.shadowOpacity = 0.1
        tabBar.layer.shadowRadius = 4
    }
    
    private func setupNotificationObservers() {
        // 添加调试视图通知监听
        NotificationCenter.default.addObserver(
            forName: Notification.Name("OpenDatabaseDebugView"),
            object: nil,
            queue: .main
        ) { _ in
            viewModel.openDebugView()
            print("MainTabView收到打开调试视图通知")
        }
        
        // 添加心率数据流程测试视图通知监听
        NotificationCenter.default.addObserver(
            forName: Notification.Name("OpenHeartRateFlowTest"),
            object: nil,
            queue: .main
        ) { _ in
            viewModel.openHeartRateFlowTest()
            print("MainTabView收到打开心率数据流程测试视图通知")
        }
        
        // 添加心率明细测试视图通知监听
        NotificationCenter.default.addObserver(
            forName: Notification.Name("OpenHeartRateDetailTest"),
            object: nil,
            queue: .main
        ) { _ in
            viewModel.openHeartRateDetailTest()
            print("MainTabView收到打开心率明细测试视图通知")
        }
        
        // 添加HRV明细测试视图通知监听
        NotificationCenter.default.addObserver(
            forName: Notification.Name("OpenHRVDetailTest"),
            object: nil,
            queue: .main
        ) { _ in
            viewModel.openHRVDetailTest()
            print("MainTabView收到打开HRV明细测试视图通知")
        }
    }
    
    // 初始化所有服务
    private func initializeServices() {
        // 如果服务已经初始化，则不再重复初始化
        if mqttSyncService == nil {
            mqttSyncService = MQTTSyncService.shared
        }
        if mqttService == nil {
            mqttService = MQTTService.shared
        }
        if deviceService == nil {
            deviceService = WindRingDeviceService.shared
        }
        if rawDataUploadService == nil {
            rawDataUploadService = RawDataUploadService.shared
        }
        
        // 设置MQTT服务
        setupMQTTService()
        // 设置数据自动上传服务
        setupAutoUploadService()
        // 获取词汇表
        viewModel.loadGlossary()
    }
    
    /// 设置MQTT服务
    private func setupMQTTService() {
        guard let mqttService = mqttService else { return }
        
        // 如果用户已登录，设置认证信息
        if let userId = UserDefaults.standard.string(forKey: "userId"),
           let token = UserDefaults.standard.string(forKey: "userToken") {
            mqttService.setCredentials(username: userId, password: token)
        } else {
            // 使用zhq作为默认用户名和密码
            mqttService.setCredentials(username: "zhq", password: "123456")
        }
        
        // 确保自动连接设置为true（如果是首次安装）
        if !UserDefaults.standard.bool(forKey: "mqtt_autoConnect_initialized") {
            UserDefaults.standard.set(true, forKey: "mqtt_autoConnect")
            UserDefaults.standard.set(true, forKey: "mqtt_autoConnect_initialized")
        }
        
        // 启动MQTT同步服务
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.mqttSyncService?.start()
        }
    }
    
    /// 设置数据自动上传服务
    private func setupAutoUploadService() {
        guard let rawDataUploadService = rawDataUploadService else { return }
        
        // 始终确保自动上传设置为true
        UserDefaults.standard.set(true, forKey: "auto_upload_enabled")
        UserDefaults.standard.set(true, forKey: "auto_upload_initialized")
        
        // 在应用启动后延迟2秒启动自动上传服务
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            print("🔄 正在启动数据自动上传服务...")
            // 启动自动上传，每5分钟自动上传一次数据
            _ = rawDataUploadService.startAutoUpload(interval: 300)
            
            // 5秒后再次确认自动上传是否启动
            DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                if !rawDataUploadService.isAutoUploadEnabled {
                    print("🔄 再次尝试启动数据自动上传服务...")
                    _ = rawDataUploadService.startAutoUpload(interval: 300)
                }
            }
        }
        
        // 监听设备连接状态变化
        NotificationCenter.default.addObserver(forName: NSNotification.Name("DeviceConnectedNotification"), object: nil, queue: .main) { _ in
            print("📲 设备已连接，启动数据自动上传服务...")
            _ = rawDataUploadService.startAutoUpload(interval: 300)
        }
    }

}

// MARK: - Previews
//struct MainTabView_Previews: PreviewProvider {
//    static var previews: some View {
//        MainTabView()
//    }
//}

