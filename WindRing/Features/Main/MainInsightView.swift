import SwiftUI
import Combine
import WebKit
//import UIKit // 如果 UIImage, UIScreen 等报错，取消此注释

// GIF动画播放器组件
struct GIFView: UIViewRepresentable {
    private let name: String
    private let fallbackSystemImage: String?
    
    init(_ name: String, fallbackSystemImage: String? = nil) {
        self.name = name
        self.fallbackSystemImage = fallbackSystemImage
    }
    
    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        // 属性设置需要检查 WKWebView API
        // webView.backgroundColor = .clear
        // webView.isOpaque = false
        // webView.scrollView.isScrollEnabled = false
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        // 首先尝试从Bundle加载GIF
        if let gifURL = Bundle.main.url(forResource: name, withExtension: "gif") {
            let data = try? Data(contentsOf: gifURL)
            webView.load(data ?? Data(),
                         mimeType: "image/gif",
                         characterEncodingName: "UTF-8",
                         baseURL: gifURL.deletingLastPathComponent())
            return
        }
        
        // 如果从Bundle加载失败，尝试从Assets.xcassets加载
        if let gifData = loadGIFFromAssets(named: name) {
            webView.load(gifData,
                        mimeType: "image/gif",
                        characterEncodingName: "UTF-8",
                        baseURL: Bundle.main.bundleURL)
            return
        }
        
        print("找不到GIF资源: \(name).gif")
        
        // 当GIF无法加载时，优先使用替代图像
        if let fallbackImage = fallbackSystemImage, let image = UIImage(systemName: fallbackImage) {
            let imageData = image.withTintColor(.blue).pngData()
            loadImageData(webView: webView, imageData: imageData)
        }
        // 否则尝试加载同名静态图像
        else if let image = UIImage(named: name) {
            let imageData = image.pngData()
            loadImageData(webView: webView, imageData: imageData)
        }
    }
    
    // 从Assets.xcassets加载GIF数据的辅助方法
    private func loadGIFFromAssets(named: String) -> Data? {
        guard let asset = NSDataAsset(name: named) else { return nil }
        return asset.data
    }
    
    // 通过HTML加载图像数据的辅助方法
    private func loadImageData(webView: WKWebView, imageData: Data?) {
        let base64String = imageData?.base64EncodedString() ?? ""
        let html = """
        <html>
        <body style="margin: 0; padding: 0; background-color: transparent;">
        <img src="data:image/png;base64,\(base64String)" style="width: 100%; height: 100%; object-fit: contain;">
        </body>
        </html>
        """
        webView.loadHTMLString(html, baseURL: nil)
    }
}

// 也可以添加一个备用的静态GIF视图，当GIF无法加载时使用
struct StaticImageView: View {
    let imageName: String
    
    var body: some View {
        if let uiImage = UIImage(named: imageName) {
            Image(uiImage: uiImage)
                .resizable()
                .scaledToFit()
        } else {
            // 如果图像也无法加载，则显示一个占位符
            Image(systemName: "antenna.radiowaves.left.and.right")
                .resizable()
                .scaledToFit()
                .foregroundColor(.blue)
        }
    }
}

// ActivityScoreResponse和ActivityScoreData已经在项目其他地方定义，不需要在这里重复定义
struct MainInsightView: View {
    // 添加 ViewModel
    @StateObject private var viewModel = MainInsightViewModel()
    @State private var showHealthStatusInfo = false
    @State private var healthStatusGlossaryItem: GlossaryItem?
    
    // 添加日期状态管理
    @State private var showDatePicker = false
    @State private var showActivityDetail = false
    @State private var showSleepDetail = false
    @State private var showStressDetail = false
    @State private var showVitalSignsDetail = false
    
    // 添加设备服务实例
    @StateObject private var deviceService = WindRingDeviceService.shared
    
    // 蓝牙连接状态
    @State private var bluetoothState: BluetoothConnectionState = .disconnected
    @State private var isShowingBluetoothAlert = false // 控制蓝牙操作提示框
    @State private var rotationDegrees: CGFloat = 0 // 添加旋转角度状态
    @State private var shouldCancelConnection = false // 取消连接标志
    @State private var connectionCheckTimer: AnyCancellable? // 连接状态检查计时器
    
//    @EnvironmentObject var versionService: VersionUpdateService
    // 日期格式化器
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM yyyy"  // 更改为英文简洁格式：Mar 2025
//        formatter.locale = Locale(identifier: "en_US") // 设置为英文
        return formatter
    }()
    
    // 健康状态数据 - 修改为计算属性，根据selectedDate变化
    private var healthData: [HealthMetric] {
        // 使用日期作为随机种子，确保同一天显示相同数据
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day], from: viewModel.sharedDateViewModel.selectedDate)
        let dateValue = (components.day ?? 0) + (components.month ?? 0) * 31 + (components.year ?? 0) * 12 * 31
        
        // 为不同日期生成不同的随机值，但保证相同日期看到相同数据
        srand48(dateValue)
        
        // 分别计算各个健康指标的值
        let sleepValue = viewModel.sleepData?.score ?? 0
        let vitalValue = viewModel.vitalData?.score ?? 0
        let activityValue = viewModel.activityData?.score ?? 0
        let stressValue = viewModel.stressData?.score ?? 0
        let vitalTitle = VersionUpdateService.shared.status == 1 ? "health_metric_vital_signs".localized:"health_metric_overall_situation".localized
        
        return [
            HealthMetric(name: "health_metric_sleep".localized, value: sleepValue, position: .top),
            HealthMetric(name: vitalTitle, value: vitalValue, position: .right),
            HealthMetric(name: "health_metric_activity".localized, value: activityValue, position: .bottom),
            HealthMetric(name: "health_metric_stress".localized, value: stressValue, position: .left)
        ]
    }
    
    // 添加连接成功提示的状态变量
    @State private var showConnectedToast = false
    @State private var connectedDeviceName = ""
    
    // 添加上次活动评分请求时间属性
    @State private var lastActivityScoreRequestTime: Date? = nil
    
    // 添加上次数据更新时间属性
    @State private var lastDataUpdateTime: Date? = nil
    
    // 添加日历数据状态
    @State private var calendarDatesWithData: [Date] = []
    @State private var isLoadingCalendarData = false
    
    @State private var hasLoaded = false
    var body: some View {
        NavigationView {  // 添加内部NavigationView
            ZStack(alignment: .top) {
                // 背景色
                Color.appBackground
                    .ignoresSafeArea()
                
//                 顶部背景图
                VStack(spacing: 0) {
                    Image("Insight_bg")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: UIScreen.main.bounds.width)
                        .edgesIgnoringSafeArea(.top)
                    
                    Spacer()
                }
                
                // 页面内容
                VStack(spacing: 0) {
                    // 顶部安全区域占位 - 改为10pt
                    Color.clear.frame(height: 10)
                    
                    // 顶部区域：日历选择器和蓝牙按钮
                    HStack {
                        // 左侧日历选择器
                        Button(action: {
                            withAnimation {
                                showDatePicker.toggle()
                                
                                // 当打开日历时获取日历数据
                                if showDatePicker {
                                    viewModel.fetchCalendarData()
                                }
                            }
                        }) {
                            HStack(spacing: 6) {
                                // 使用项目中的日历图标
                                Image("日历")
                                    .resizable()
                                    .frame(width: 16, height: 16)
                                
                                // 删除日期文本，只保留日历图标
                            }
                            .padding(.vertical, 5)
                            .padding(.horizontal, 8)
                        }
                        .padding(.leading, 10)
                        
                        Spacer()
                        
                        // 右侧蓝牙按钮
                        Button(action: {
                            // 蓝牙按钮点击逻辑
                            handleBluetoothButtonTap()
                        }) {
                            ZStack {
                                // 根据连接状态显示不同图标
                                if bluetoothState == .disconnected {
                                    // 断开连接时显示断连图标
                                    Image("断连")
                                        .resizable()
                                        .frame(width: 16, height: 16)
                                } else if bluetoothState == .connected {
                                    // 已连接状态显示连接状态图标 - 使用蓝色圆环
                                    Image("连联")
                                        .resizable()
                                        .frame(width: 16, height: 16)
                                        // 移除颜色修改器，使用图标原始颜色
                                } else if bluetoothState == .connecting {
                                    // 连接中状态的子视图
                                    ConnectingStateView()
                                        .frame(width: 16, height: 16)
                                }
                            }
                        }
                        .padding(.trailing, 16)
                    }
                    .padding(.top, 10)
                    
                    // 星期日期选择器
                    DateSelectionView(
//                        selectedDay: $viewModel.sharedDateViewModel.selectedDay,
                        selectedDate: $viewModel.sharedDateViewModel.selectedDate,
                        onDateSelected: { dayOffset, date in
                            // 此回调中的特定逻辑现在可以简化，因为主要的数据刷新由onChange驱动
                            print("MainInsightView - DateSelectionView selected: \(date), offset: \(dayOffset)")
                        }
                    )
                    .frame(height: 60)
                    .padding(.top, 8)
                    
                    // 内容滚动区域
                    ScrollView(.vertical, showsIndicators: false) {
                        VStack(spacing: 16) {
                            // Health Status 雷达图
                            VStack(spacing: 12) {
                                // 标题
                                HStack(spacing: 5) {
                                    // 指示线
                                    Rectangle()
                                        .frame(width: 4, height: 14)
                                        .foregroundColor(Color(red: 0.4, green: 0.4, blue: 0.4, opacity: 1))
                                        .cornerRadius(0.75)
                                    
                                    // 标题
                                    if VersionUpdateService.shared.status == 1 {
                                        Text("insight_health_status".localized)
                                            .font(.custom("PingFang-SC-Medium", size: 16))
                                            .foregroundColor(Color(hex: "#FFFFFF"))
    
                                    }else{
                                        Text("insight_health_overallRating".localized)
                                            .font(.custom("PingFang-SC-Medium", size: 16))
                                            .foregroundColor(Color(hex: "#FFFFFF"))
                                        
                                    }
                                    
                                    InfoButtonWithGlossaryPopup(showKey: GlossaryKey.healthStatus.rawValue)
//                                    // 信息按钮 - 直接放在标题后面
//                                    Button(action: {
//                                        self.healthStatusGlossaryItem = UserSettings.shared.glossaryItems.filter{ $0.id == 26 }.first
//                                        if healthStatusGlossaryItem != nil {
//                                            showHealthStatusInfo = true
//                                        } else {
//                                            print("Debug: Glossary item 'insight_health_status' not found.")
//                                        }
//                                    }) {
//                                        Image("疑问")
//                                            .resizable()
//                                            .frame(width: 14, height: 14)
//                                            .foregroundColor(.white.opacity(0.7))
//                                    }
//                                    .padding(.leading, 4) // 添加一点左边距，与标题保持适当间隔
                                    
                                    Spacer()
                                }
                                .padding(.horizontal, 16)
                                .padding(.bottom, 20) // 增加雷达图与下方健康卡片的间距
                                
                                // 外部容器 - 保持固定大小，确保指标位置不变
                                HealthRadarView(
                                    healthData: healthData,
                                    isLoadingSleepData: viewModel.isLoadingSleepData,
                                    isLoadingStressData: viewModel.isLoadingStressData,
                                    isLoadingVitalData: viewModel.isLoadingVitalData,
                                    getSleepScoreColor: getSleepScoreColor,
                                    getStressScoreColor: { self.getStressScoreColor(score: viewModel.stressData?.score ?? 0) },
                                    getVitalScoreColor: getVitalScoreColor,
                                    getActivityScoreColor: { score in self.getActivityScoreColor(score: viewModel.activityData?.score ?? 0) }
                                )
                                .frame(height: 300) // 确保整个容器有足够的高度
                                .padding(.top, 8)
                            }
                            .padding(.top, 15)
                            .padding(.bottom, 20) // 增加雷达图与下方健康卡片的间距
                            
                            // Activity Card - 活动卡片
                            HealthCardView(
                                title: "insight_activity".localized,
                                content: {
                                    HStack(spacing: 0) {
                                        // 步数指标
                                        MetricItemView(
                                            icon: CircularProgressView(
                                                progress: viewModel.isLoadingActivityData ? 1.00 :
                                                    (viewModel.activityData != nil ? min(CGFloat(viewModel.activityData!.steps) / AppGlobals.defaultSteps, 1.0) : 1.00),
                                                color: Color.teal,
                                                iconName: "跑步",
                                                useAssetImage: true
                                            ),
                                            value: viewModel.isLoadingActivityData ? "--" :
                                                (viewModel.activityData != nil ? "\(viewModel.activityData!.steps)" : "--"),
                                            unit: "insight_unit_steps".localized,
                                            isRealData: Calendar.current.isDateInToday(viewModel.sharedDateViewModel.selectedDate) ?
                                                viewModel.realTimeSteps > 0 :
                                                viewModel.activityData != nil
                                        )
                                        
                                        // 分隔线
                                        Rectangle()
                                            .fill(Color.white.opacity(0.1))
                                            .frame(width: 1, height: 60)
                                            .padding(.vertical, 15)
                                        
                                        // 卡路里指标
                                        MetricItemView(
                                            icon: CircularProgressView(
                                                progress: viewModel.isLoadingActivityData ? 0.24 : (viewModel.activityData != nil ? min(CGFloat(viewModel.activityData!.calories) / AppGlobals.defaultKcal, 1.0) : 0.24),
                                                color: Color.purple,
                                                iconName: "热量",
                                                useAssetImage: true
                                            ),
                                            value: viewModel.isLoadingActivityData ? "--" :
                                                (viewModel.activityData != nil ? "\(Int(Double(viewModel.activityData!.calories)))" : "--"),
                                                // 判断是否为今天并且有实时数据
//                                                (Calendar.current.isDateInToday(viewModel.sharedDateViewModel.selectedDate) && viewModel.realTimeCalories > 0) ?
//                                                    "\(Int(Double(viewModel.realTimeCalories) / 10000.0))" :
//                                                    (viewModel.activityData != nil ? "\(Int(Double(viewModel.activityData!.calories) / 10000.0))" : "--"),
                                            unit: "insight_unit_kcal".localized,
                                            isRealData: Calendar.current.isDateInToday(viewModel.sharedDateViewModel.selectedDate) ?
                                                viewModel.realTimeCalories > 0 :
                                                viewModel.activityData != nil
                                        )
                                        
                                        // 分隔线
                                        Rectangle()
                                            .fill(Color.white.opacity(0.1))
                                            .frame(width: 1, height: 60)
                                            .padding(.vertical, 15)
                                        
                                        // 时间指标
                                        MetricItemView(
                                            icon: CircularProgressView(
                                                progress: viewModel.isLoadingActivityData ? 0.28 : (viewModel.activityData != nil ? min(CGFloat(viewModel.activityData!.time) / AppGlobals.defaultActivityStepTime, 1.0) : 0.28),
                                                color: Color.blue,
                                                iconName: "时钟",
                                                useAssetImage: true
                                            ),
                                            value: viewModel.isLoadingActivityData || viewModel.activityData == nil ? "--" : (viewModel.activityData != nil ? (viewModel.activityData!.time).description : "--"),//self.formatActivityTime(activityData: viewModel.activityData)
                                            unit: "insight_unit_min".localized,
                                            unitOnNewLine: true,
                                            isRealData: viewModel.activityData != nil
                                        )
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 0)  // 移除垂直内边距
                                },
                                onTap: {
                                    showActivityDetail = true
                                }
                            )
                            .padding(.horizontal, 16)
                            .background(
                                NavigationLink(destination: ActivityDetailView(), isActive: $showActivityDetail) {
                                    EmptyView()
                                }
                            )
                            
                             //Sleep & Stress Cards - 睡眠和压力卡片
                            HStack(spacing: 12) {
                                // Sleep Card - 睡眠卡片
                                HealthCardView(
                                    title: "insight_sleep".localized,
                                    isHalfWidth: true,
                                    content: {
                                        HStack(spacing: 10) {
                                            // 无圆环的图标
                                            ZStack {
                                                IconView(color: Color.purple, iconName: "睡眠")
                                                    .frame(width: 24, height: 24)
                                                    .offset(y: 2)
                                            }
                                            .frame(width: 44, height: 44)
                                            .background(
                                                RoundedRectangle(cornerRadius: 6)
                                                    .fill(Color.clear)
                                            )
                                            
                                            if viewModel.isLoadingSleepData {
                                                // 加载中状态
                                                ProgressView()
                                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                            } else if let error = viewModel.sleepDataError {
                                                // 错误状态 - 显示占位符
                                                HStack(alignment: .bottom, spacing: 1) {
                                                    Text("--")
                                                        .font(.system(size: 16, weight: .bold))
                                                        .foregroundColor(.white)
                                                }
                                            } else if let sleepData = viewModel.sleepData {
                                                // 有睡眠数据时，显示真实数据
                                                VStack(alignment: .leading, spacing: 4) {
                                                    // 睡眠时间格式化显示 - 始终使用deep + light + rem的总和作为实际的总睡眠时长
                                                    let calculatedTotal = sleepData.deep + sleepData.light + sleepData.rem
                                                    let hours = calculatedTotal / 60
                                                    let minutes = calculatedTotal % 60
                                                    let isRealData = true // 根据需求，将其视为真实数据
                                                    
                                                    HStack(alignment: .bottom, spacing: 1) {
                                                        Text("\(hours)")
                                                            .font(.system(size: 16, weight: .bold))
                                                            .foregroundColor(getTimeDisplayColor(isRealData: isRealData))
                                                        
                                                        Text("insight_unit_hr".localized)
                                                            .font(.system(size: 12, weight: .regular))
                                                            .foregroundColor(.white)
                                                            .offset(y: -1)
                                                        
                                                        Text("\(minutes)")
                                                            .font(.system(size: 16, weight: .bold))
                                                            .foregroundColor(getTimeDisplayColor(isRealData: isRealData))
                                                        
                                                        Text("insight_unit_min".localized)
                                                            .font(.system(size: 12, weight: .regular))
                                                            .foregroundColor(.white)
                                                            .offset(y: -1)
                                                    }
                                                }
                                            } else {
                                                // 无数据状态 - 显示占位符
                                                HStack(alignment: .bottom, spacing: 1) {
                                                    Text("--")
                                                        .font(.system(size: 16, weight: .bold))
                                                        .foregroundColor(.white)
                                                }
                                            }
                                            Spacer()
                                        }
                                        .padding(.horizontal, 6)
                                    },
                                    onTap: {
                                        showSleepDetail = true
                                    }
                                )
                                .background(
                                    NavigationLink(destination: SleepDetailView(), isActive: $showSleepDetail) {
                                        EmptyView()
                                    }
                                )
                                if VersionUpdateService.shared.status == 1{
                                    // Stress Card - 压力卡片
                                    HealthCardView(
                                        title: "insight_stress".localized,
                                        isHalfWidth: true,
                                        content: {
                                            HStack(spacing: 10) {
                                                // 无圆环的图标
                                                ZStack {
                                                    IconView(color: Color.teal, iconName: "压力")
                                                        .frame(width: 24, height: 24)
                                                        .offset(y: 2)
                                                }
                                                .frame(width: 44, height: 44)
                                                .background(
                                                    RoundedRectangle(cornerRadius: 6)
                                                        .fill(Color.clear)
                                                )
                                                
                                                if viewModel.isLoadingStressData {
                                                    // 加载中状态
                                                    ProgressView()
                                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                                } else if let error = viewModel.stressDataError {
                                                    // 错误状态 - 显示占位符
                                                    HStack(alignment: .bottom, spacing: 1) {
                                                        Text("--")
                                                            .font(.system(size: 16, weight: .bold))
                                                            .foregroundColor(.white)
                                                    }
                                                } else if let stressData = viewModel.stressData {
                                                    // 有压力数据时，显示真实数据
                                                    HStack(alignment: .bottom, spacing: 4) {
                                                        Text("\(stressData.stress)")
                                                            .font(.system(size: 16, weight: .bold))
                                                            .foregroundColor(getTimeDisplayColor(isRealData: true)) // 使用一致的方法获取颜色
                                                        
                                                        // 根据stressState显示不同文本
                                                        let stressText = getStressStateText(stressData.stressState)
                                                        Text(stressText)
                                                            .font(.system(size: 12))
                                                            .foregroundColor(.white.opacity(0.6))
                                                            .offset(y: -1)
                                                    }
                                                } else {
                                                    // 无数据状态 - 显示占位符
                                                    HStack(alignment: .bottom, spacing: 1) {
                                                        Text("--")
                                                            .font(.system(size: 16, weight: .bold))
                                                            .foregroundColor(.white)
                                                    }
                                                }
                                                Spacer()
                                            }
                                            .padding(.horizontal, 6)
                                        },
                                        onTap: {
                                            showStressDetail = true
                                        }
                                    )
                                    .background(
                                        NavigationLink(destination: StressDetailView(), isActive: $showStressDetail) {
                                            EmptyView()
                                        }
                                    )
                                }
                            }
                            .padding(.horizontal, 16)
                            ///是否隐藏
                            if VersionUpdateService.shared.status == 1{
                                //Heart Rate Card - 心率卡片
                                 HealthCardView(
                                     title: "vital_signs".localized,
                                     content: {
                                         HStack(spacing: 0) {
                                             // 心率数值部分
                                             HStack(spacing: 10) {
                                                 // 无圆环的图标
                                                 ZStack {
                                                     IconView(color: Color.red, iconName: "心率")
                                                         .frame(width: 24, height: 24)
                                                         .offset(y: 2)
                                                 }
                                                 .frame(width: 44, height: 44)
                                                 .background(
                                                     RoundedRectangle(cornerRadius: 6)
                                                         .fill(Color.clear)
                                                 )
                                                 
                                                 // 显示心率数据或加载状态
                                                 if viewModel.isLoadingVitalData {
                                                     // 加载中状态
                                                     ProgressView()
                                                         .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                                 } else if let error = viewModel.vitalDataError {
                                                     // 错误状态 - 显示占位符
                                                     HStack(alignment: .bottom, spacing: 1) {
                                                         Text("--")
                                                             .font(.system(size: 16, weight: .bold))
                                                             .foregroundColor(.white)
                                                     }
                                                 } else if let vitalData = viewModel.vitalData {
                                                     // 有数据时显示真实数据
                                                     HStack(alignment: .bottom, spacing: 2) {
                                                         Text("\(vitalData.avgHr)")
                                                             .font(.system(size: 16, weight: .bold))
                                                             .foregroundColor(getTimeDisplayColor(isRealData: true)) // 使用一致的方法获取颜色
                                                         Text("insight_unit_bpm".localized)
                                                             .font(.system(size: 12))
                                                             .foregroundColor(.white.opacity(0.6))
                                                             .offset(y: -1)
                                                     }
                                                     .padding(.vertical, 4)
                                                     .padding(.horizontal, 6)
                                                 } else {
                                                     // 无数据状态 - 显示占位符
                                                     HStack(alignment: .bottom, spacing: 1) {
                                                         Text("--")
                                                             .font(.system(size: 16, weight: .bold))
                                                             .foregroundColor(.white)
                                                     }
                                                 }
                                             }
                                             .padding(.leading, 6)
                                             
                                             // 使用固定宽度替代弹性Spacer
                                             Color.clear.frame(width: 65)
                                             
                                             // 血氧饱和度
                                             HStack(spacing: 10) {
                                                 // 无圆环的图标
                                                 ZStack {
                                                     IconView(color: Color.teal, iconName: "血氧")
                                                         .frame(width: 24, height: 24)
                                                         .offset(y: 2)
                                                 }
                                                 .frame(width: 44, height: 44)
                                                 .background(
                                                     RoundedRectangle(cornerRadius: 6)
                                                         .fill(Color.clear)
                                                 )
                                                 
                                                 // 显示血氧数据或加载状态
                                                 if viewModel.isLoadingVitalData {
                                                     // 加载中状态
                                                     ProgressView()
                                                         .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                                 } else if viewModel.vitalDataError != nil {
                                                     // 错误状态 - 显示占位符
                                                     HStack(alignment: .bottom, spacing: 1) {
                                                         Text("--")
                                                             .font(.system(size: 16, weight: .bold))
                                                             .foregroundColor(.white)
                                                     }
                                                 } else if let vitalData = viewModel.vitalData {
                                                     // 有数据时显示真实数据
                                                     HStack(alignment: .bottom, spacing: 2) {
                                                         Text("\(vitalData.avgO2)")
                                                             .font(.system(size: 16, weight: .bold))
                                                             .foregroundColor(getTimeDisplayColor(isRealData: true)) // 使用一致的方法获取颜色
                                                         Text("percent_symbol".localized)
                                                             .font(.system(size: 12))
                                                             .foregroundColor(.white.opacity(0.6))
                                                             .offset(y: -1)
                                                     }
                                                 } else {
                                                     // 无数据状态 - 显示占位符
                                                     HStack(alignment: .bottom, spacing: 1) {
                                                         Text("--")
                                                             .font(.system(size: 16, weight: .bold))
                                                             .foregroundColor(.white)
                                                     }
                                                 }
                                             }
                                             .padding(10)
                                             .background(
                                                 RoundedRectangle(cornerRadius: 8)
                                                     .fill(Color.clear)
                                             )
                                             
                                             Spacer()
                                         }
                                         .frame(maxWidth: .infinity)
                                     },
                                     onTap: {
                                         showVitalSignsDetail = true
                                     }
                                 )
                                 .padding(.horizontal, 16)
                                 .background(
                                     NavigationLink(destination: VitalSignsDetailView(), isActive: $showVitalSignsDetail) {
                                         EmptyView()
                                     }
                                 )
                            }
                            // 添加底部安全区域占位，确保滚动时有足够的空间
                            Color.clear.frame(height: 60)
                        }
                    }
                    .padding(.top, 5)
                    
                    Spacer(minLength: 0)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                
                // 弹出式日历视图覆盖层
                if showDatePicker {
                    ZStack {
                        // 磨砂半透明背景
                        Color.black.opacity(0.8)
                            .ignoresSafeArea()
                            .blur(radius: 5)
                            .onTapGesture {
                                withAnimation {
                                    showDatePicker = false
                                }
                            }
                        
                        // 自定义日历视图
                        CustomCalendarView(
                            selectedDate: $viewModel.sharedDateViewModel.selectedDate,
//                            selectedDay: $viewModel.sharedDateViewModel.selectedDay,
                            onClose: {
                                withAnimation {
                                    showDatePicker = false
                                }
                            },
                            datesWithData: calendarDatesWithData // 传递日历数据
                        )
                        .frame(width: UIScreen.main.bounds.width * 0.85)
                        .transition(.scale.combined(with: .opacity))
                        .offset(y: 0)
                    }
                }
                
                // 添加连接成功提示
                if showConnectedToast {
                    VStack {
                        Spacer().frame(height: 80)
                        
                        HStack(spacing: 10) {
                            Image("连联")
                                .resizable()
                                .frame(width: 18, height: 18)
                                // 移除颜色修改器，使用图标原始颜色
                            
                            Text(String(format: "insight_connected_to_device".localized, connectedDeviceName))
                                .font(.system(size: 14))
                                .foregroundColor(.white)
                        }
                        .padding(.vertical, 8)
                        .padding(.horizontal, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color.black.opacity(0.7))
                        )
                        .transition(.move(edge: .top).combined(with: .opacity))
                        
                        Spacer()
                    }
                }
            }
            .navigationBarTitle("", displayMode: .inline)
            .navigationBarHidden(true)
        }
//        .bottomSheet(isPresented: $showHealthStatusInfo) {
//            if let item = healthStatusGlossaryItem {
//                GlossaryInfoPopup(isPresented: $showHealthStatusInfo, glossaryItem: item)
//            }
//        }
        .navigationViewStyle(StackNavigationViewStyle())  // 使用StackNavigationViewStyle避免分屏
        .alert(isPresented: $isShowingBluetoothAlert) {
            Alert(
                title: Text("insight_disconnect_alert_title".localized),
                message: Text("insight_disconnect_alert_message".localized),
                primaryButton: .destructive(Text("insight_disconnect_button_title".localized)) {
                    disconnectBluetooth()
                },
                secondaryButton: .cancel(Text("cancel".localized))
            )
        }
        .onAppear {
            viewModel.sharedDateViewModel.selectedDate = viewModel.sharedDateViewModel.selectedDate
//            if !hasLoaded {
//                hasLoaded = true
                // 将selectedDate设置
//                let calendar = Calendar.current
//                if let today = calendar.date(byAdding: .day, value: 0, to: Date()) {
//                    viewModel.sharedDateViewModel.selectedDate = today
//                }
//                viewModel.sharedDateViewModel.selectedDate = 0
                // 同步蓝牙状态
                syncBluetoothState()
                startConnectionStateChecking()
                
                // 检查资源可用性
                checkResources()
                
                // 额外检查：如果设备已连接但UI未更新，立即更新状态
                if deviceService.connectionState == .connected && bluetoothState != .connected {
                    DispatchQueue.main.async {
                        bluetoothState = .connected
                        print("页面加载时检测到设备已连接，立即更新UI")
                        
                        // 更新设备名称但不显示提示
                        if let deviceName = deviceService.currentDiscovery?.localName, !deviceName.isEmpty {
                            connectedDeviceName = deviceName
                        }
                    }
                }
                
                // 添加通知观察者，用于更新lastDataUpdateTime
                NotificationCenter.default.addObserver(
                    forName: Notification.Name("UpdateLastDataUpdateTime"),
                    object: nil,
                    queue: .main
                ) { [self] _ in
                    let now = Date()
                    if let lastUpdateTime = self.lastDataUpdateTime,
                       now.timeIntervalSince(lastUpdateTime) < 10 { // 最少10秒钟更新一次UI
                        // 时间间隔太短，暂不更新时间戳
                        print("数据更新间隔太短，保持上次更新时间")
                    } else {
                        // 记录本次更新时间
                        self.lastDataUpdateTime = now
                        print("更新数据时间戳: \(now)")
                    }
                }
                NotificationCenter.default.addObserver(
                    forName:.deviceConnected,
                    object: nil,
                    queue: .main
                ) { [self] _ in
                    
                }
//            }
            

            
            
        }
        .onDisappear {
            // 取消计时器
            connectionCheckTimer?.cancel()
            
            // 移除通知观察者
            NotificationCenter.default.removeObserver(self, name: Notification.Name("UpdateLastDataUpdateTime"), object: nil)
        }
        .onChange(of: viewModel.sharedDateViewModel.selectedDate) { newDate in
            // 无论何种情况，切换日期都需要加载所有评分数据
            viewModel.fetchAllScores()
        
        }
//        .onChange(of: viewModel.sharedDateViewModel.selectedDay) { newDay in
//            // 根据selectedDay更新selectedDate
//            let calendar = Calendar.current
//            if let newDate = calendar.date(byAdding: .day, value: newDay, to: calendar.startOfDay(for: Date())) {
//                // 防止无限循环：只有当日期确实不同时才更新
//                if !calendar.isDate(newDate, inSameDayAs: viewModel.sharedDateViewModel.selectedDate) {
//                    viewModel.sharedDateViewModel.selectedDate = newDate
//                }
//            }
//        }

        .environment(\.mainInsightViewModel, viewModel)  // 添加环境值
        .refreshable {
            viewModel.fetchAllScores()
        }
        .onAppear {
            viewModel.fetchAllScores()
        }
        // Add it here:
        .onReceive(NotificationCenter.default.publisher(for: .didSyncTodayData)) { _ in
            print("收到今日数据同步完成通知，正在刷新MainInsightView...")
            viewModel.fetchAllScores()
        }
    }
    
    // 添加蓝牙按钮点击逻辑
    private func handleBluetoothButtonTap() {
        switch bluetoothState {
        case .disconnected:
            // 断开状态下点击，尝试连接之前的设备
            connectLastDevice()
        case .connecting:
            // 连接中状态下点击，取消连接
            cancelConnecting()
        case .connected:
            break
            // 已连接状态下点击，重新尝试连接
//            reconnectDevice()
        }
    }
    
    // 尝试连接上次的设备
    private func connectLastDevice() {
        bluetoothState = .connecting
        shouldCancelConnection = false
        
        print("尝试连接设备...")
        
        // 首先检查设备服务是否有上次连接的设备
        if let lastMac = deviceService.lastConnectedDeviceMAC, !lastMac.isEmpty {
            print("尝试连接上次设备: \(lastMac)")
            
            // 先断开当前连接（如果有）
            if deviceService.connectionState.isConnected {
                deviceService.disconnectDevice()
            }
            
            // 开始扫描
            deviceService.startScan(duration: 10)
            
            // 设置10秒超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [self] in
                guard bluetoothState == .connecting else { return }
                
                // 检查是否已连接
                if !deviceService.connectionState.isConnected && !shouldCancelConnection {
                    bluetoothState = .disconnected
                    print("连接超时")
                }
            }
            
            // 尝试连接
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [self] in
                guard !shouldCancelConnection else { return }
                
                if let discovery = deviceService.discoveredDevices.first(where: {
                    $0.mac?.uppercased() == lastMac.uppercased()
                }) {
                    print("找到之前设备，开始连接")
                    deviceService.connectDevice(discovery: discovery)
                    
                    // 连接成功后记录设备名称，用于显示提示
                    if let deviceName = discovery.localName, !deviceName.isEmpty {
                        connectedDeviceName = deviceName
                    } else {
                        connectedDeviceName = "default_device_name".localized
                    }
                }
            }
        } else {
            print("无之前连接的设备记录")
            
            // 开始扫描，连接任何发现的设备
            deviceService.startScan(duration: 5)
            
            // 设置扫描超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 6) { [self] in
                guard bluetoothState == .connecting else { return }
                
                if !deviceService.discoveredDevices.isEmpty && !shouldCancelConnection {
                    let firstDevice = deviceService.discoveredDevices.first!
                    print("尝试连接发现的设备: \(firstDevice.localName ?? "unknown_device".localized)")
                    deviceService.connectDevice(discovery: firstDevice)
                    
                    // 连接超时
                    DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [self] in
                        guard bluetoothState == .connecting else { return }
                        
                        if !deviceService.connectionState.isConnected && !shouldCancelConnection {
                            bluetoothState = .disconnected
                            print("连接超时")
                        }
                    }
                } else {
                    // 没有发现设备
                    bluetoothState = .disconnected
                    print("未发现设备")
                }
            }
        }
    }
    
    // 取消连接过程
    private func cancelConnecting() {
        shouldCancelConnection = true
        bluetoothState = .disconnected
        
        // 取消设备服务的连接操作
        if deviceService.connectionState == .connecting {
            deviceService.disconnectDevice()
        }
        
        if deviceService.isScanning {
            deviceService.stopScan()
        }
    }
    
    // 重新连接设备
    private func reconnectDevice() {
        // 先断开当前连接
        if deviceService.connectionState.isConnected {
            deviceService.disconnectDevice()
        }
        
        // 延迟后开始新的连接
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [self] in
            connectLastDevice()
        }
    }
    
    // 开始定期检查连接状态
    private func startConnectionStateChecking() {
        // 取消可能存在的计时器
        connectionCheckTimer?.cancel()
        
        // 创建新计时器，每2秒检查一次
        connectionCheckTimer = Timer.publish(every: 1, on: .main, in: .common) // 从2秒改为1秒，更频繁检查
            .autoconnect()
            .sink { [self] _ in
                syncBluetoothState()
                
                // 如果设备已连接但UI未显示连接状态，立即更新
                if deviceService.connectionState == .connected && bluetoothState != .connected {
                    bluetoothState = .connected
                    print("发现设备已连接，更新UI状态")
                }
                
                // 如果设备已断开但UI仍显示连接状态，立即更新
                if deviceService.connectionState != .connected && bluetoothState == .connected {
                    bluetoothState = .disconnected
                    print("发现设备已断开，更新UI状态")
                }
            }
    }
    
    // 同步蓝牙状态
    private func syncBluetoothState() {
        let previousState = bluetoothState
        
        // 先检查设备服务的连接状态
        if deviceService.connectionState == .connected {
            // 如果设备已连接，直接更新UI状态
            if bluetoothState != .connected {
                bluetoothState = .connected
                
                // 如果状态从非连接变为连接，更新设备名称但不显示提示
                if previousState != .connected {
                    connectedDeviceName = deviceService.currentDiscovery?.localName ?? "default_device_name".localized
                }
            }
            return
        }
        
        // 连接中状态的特殊处理
        if bluetoothState == .connecting {
            if deviceService.connectionState == .connected {
                bluetoothState = .connected
            } else if deviceService.connectionState == .disconnected {
                if !shouldCancelConnection {
                    bluetoothState = .disconnected
                }
            } else {
                // 检查是否为失败状态
                switch deviceService.connectionState {
                case .failed:
                    if !shouldCancelConnection {
                        bluetoothState = .disconnected
                    }
                default:
                    break
                }
            }
        } else {
            // 常规状态同步
            if deviceService.connectionState == .connected && bluetoothState != .connected {
                bluetoothState = .connected
            } else if deviceService.connectionState != .connected && bluetoothState == .connected {
                bluetoothState = .disconnected
            }
        }
    }
    
    // 断开蓝牙连接
    private func disconnectBluetooth() {
        // 执行断开连接逻辑
        bluetoothState = .disconnected // 确保 bluetoothState 的修改是合适的，或者调用 viewModel 的方法
        deviceService.disconnectDevice()
    }
    
    
    
    // 处理睡眠数据获取错误的辅助方法
    private func handleSleepFetchError(_ message: String) {
        viewModel.isLoadingSleepData = false
        viewModel.sleepDataError = message
        viewModel.sleepData = nil
        print("睡眠评分错误: \(message)")
    }
    
    // 添加获取睡眠评分颜色的辅助方法 - 根据是否为模拟数据或真实数据显示不同颜色
    private func getSleepScoreColor(score: Int) -> Color {
        // 所有数值统一显示为白色
        return Color.white
    }
    
    // 判断日期是否在未来
    private func isFutureDate(_ date: Date) -> Bool {
        let calendar = Calendar.current
        return calendar.compare(date, to: Date(), toGranularity: .day) == .orderedDescending
    }
    
    // 格式化日期为易读格式
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        return formatter.string(from: date)
    }
    
    // 处理压力数据获取错误的辅助方法
    private func handleStressFetchError(_ message: String) {
        viewModel.isLoadingStressData = false
        viewModel.stressDataError = message
        viewModel.stressData = nil
        print("压力评分错误: \(message)")
    }
    
    // 添加获取压力评分颜色的辅助方法 - 根据是否为模拟数据或真实数据显示不同颜色
    private func getStressScoreColor(score: Int) -> Color {
        // 所有数值统一显示为白色
        return Color.white
    }
    
    // 添加获取压力状态文本的辅助方法
    private func getStressStateText(_ stressState: Int) -> String {
        switch stressState {
        case 1:
            return "stress_state_relax".localized // 放松
        case 2:
            return "stress_state_normal".localized // 正常
        case 3:
            return "stress_state_stress".localized // 压力
        case 4:
            return "stress_state_high_stress".localized // 高压力
        default:
            return "stress_state_unknown".localized // 未知状态
        }
    }
    
    
    
    // 处理体征数据获取错误
    private func handleVitalFetchError(_ message: String) {
        viewModel.isLoadingVitalData = false
        viewModel.vitalDataError = message
        viewModel.vitalData = nil
        print("体征评分错误: \(message)")
    }
    
    // 处理体征数据获取错误
    private func handleVitalFetchError() -> String {
        if viewModel.isLoadingVitalData {
            return "loading_text".localized
        }
        
        if let error = viewModel.vitalDataError {
            return error
        }
        
        if viewModel.vitalData == nil {
            return "no_data_available".localized
        }
        
        return ""
    }
    
    // 获取体征评分对应的颜色 - 根据是否为模拟数据或真实数据显示不同颜色
    private func getVitalScoreColor() -> Color {
        // 所有数值统一显示为白色
        return Color.white
    }
    
    // 可视化健康状态等级评分 - 根据健康指标平均分计算
    private func calculateOverallHealthStatus() -> String {
        let averageScore = calculateAverageHealthScore()
        
        // 返回健康状态评级
        if averageScore >= 80 {
            return "health_status_excellent".localized
        } else if averageScore >= 70 {
            return "health_status_good".localized
        } else if averageScore >= 60 {
            return "health_status_normal".localized
        } else if averageScore >= 40 {
            return "health_status_fair".localized
        } else {
            return "health_status_poor".localized
        }
    }
    
    // 计算平均健康分数
    private func calculateAverageHealthScore() -> Int {
        let (totalScore, validCount) = calculateTotalHealthScore()
        return validCount > 0 ? totalScore / validCount : 0
    }
    
    // 计算总健康分数
    private func calculateTotalHealthScore() -> (totalScore: Int, validCount: Int) {
        let sleepValue = viewModel.sleepData?.score ?? 0
        let stressValue = viewModel.stressData?.score ?? 0
        let vitalValue = viewModel.vitalData?.score ?? 0
        
        // 计算有效的指标数量和总评分
        var validCount = 0
        var totalScore = 0
        
        if sleepValue > 0 {
            totalScore += sleepValue
            validCount += 1
        }
        
        if stressValue > 0 {
            totalScore += stressValue
            validCount += 1
        }
        
        if vitalValue > 0 {
            totalScore += vitalValue
            validCount += 1
        }
        
        // 加上固定的活动评分（未接入真实数据）
        totalScore += Int(20 + drand48() * 60)
        validCount += 1
        
        return (totalScore, validCount)
    }
    
    // 获取健康状态显示的颜色
    private func getHealthStatusColor() -> Color {
        // 检查是否有足够的真实数据
        let realDataCount = [viewModel.sleepData, viewModel.stressData, viewModel.vitalData, viewModel.activityData].compactMap { $0 }.count
        
        if realDataCount == 0 {
            // 完全没有真实数据，使用黄色表示全部是模拟数据
            return Color.yellow
        } else if realDataCount == 4 {
            // 全部是真实数据，使用绿色
            return Color.green
        } else {
            // 部分是真实数据，使用橙色表示混合数据
            return Color.orange
        }
    }
    
    // 获取活动评分颜色 - 始终返回白色
    private func getActivityScoreColor(score: Int) -> Color {
        return Color.white // 所有活动评分统一显示为白色
    }
    
    
    
    // 处理活动数据获取错误
    private func handleActivityFetchError(_ message: String) {
        viewModel.isLoadingActivityData = false
        viewModel.activityDataError = message
        viewModel.activityData = nil
        print("活动评分错误: \(message)")
    }
    
    // 格式化活动时间显示 - 注意ActivityScoreData.time是秒级单位
    private func formatActivityTime() -> String {
        guard let activityData = viewModel.activityData else {
            return "0" // 简单返回0，单位由UI层固定为"min"
        }
        
        // time是秒为单位，需要转换为分钟
        let time = activityData.time
        let totalMinutes = time / 60 // 直接转换为总分钟数
        
        // 当时间为0但有步数时，根据步数估算活动时长
        if time == 0 && activityData.steps > 0 {
            // 根据步速和步数估算活动时长
            // 普通人每分钟约走80-120步，跑步每分钟约150-200步
            // 使用平均每分钟120步作为保守估计
            let estimatedMinutes = max(1, Int(Double(activityData.steps) / 120.0))
            return "\(estimatedMinutes)" // 返回估算的分钟数
        }
        
        return "\(totalMinutes)" // 直接返回分钟数
    }
    
    // 添加一个辅助方法，用于获取睡眠时间显示的颜色
    private func getTimeDisplayColor(isRealData: Bool) -> Color {
        return Color.white // 所有数据统一显示为白色
    }
    
    // 添加资源检查方法
    private func checkResources() {
        // 检查蓝牙图标资源
        let disconnectedIconExists = UIImage(named: "断连") != nil
        let connectedIconExists = UIImage(named: "连接状态") != nil
        let connectingGifExists = NSDataAsset(name: "lianjie") != nil || Bundle.main.url(forResource: "lianjie", withExtension: "gif") != nil
        
        print("资源检查结果 - 断开连接图标: \(disconnectedIconExists ? "可用" : "不可用"), " +
              "已连接图标: \(connectedIconExists ? "可用" : "不可用"), " +
              "连接中动画: \(connectingGifExists ? "可用" : "不可用")")
        
        // 检查GIF资源是否是正确的格式
        if let asset = NSDataAsset(name: "lianjie") {
            print("lianjie资源大小: \(asset.data.count) 字节")
            
            // 检查前几个字节以确认是否为GIF文件格式
            let gifSignature: [UInt8] = [0x47, 0x49, 0x46] // "GIF" in ASCII
            if asset.data.count >= 3 {
                let bytes = [UInt8](asset.data.prefix(3))
                let isGif = bytes == gifSignature
                print("lianjie资源是否为GIF格式: \(isGif ? "是" : "否")")
            }
        }
    }
    
    // 处理实时步数更新
    private func handleRealTimeStepsUpdate(_ newSteps: Int) {
        // 实现...
    }
    

}

// MARK: - 健康卡片组件
struct HealthCardView<Content: View>: View {
    let title: String
    var isHalfWidth: Bool = false
    let content: () -> Content
    var onTap: (() -> Void)? = nil // 添加可选的点击回调闭包
    
    var body: some View {
        ZStack(alignment: .topLeading) {
            // 背景 - 使用渐变效果，增加透明度
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(
                            colors: [
                                Color(red: 0.16, green: 0.19, blue: 0.25, opacity: 0.7),  // 原来是不透明的，现在加入0.7的透明度
                                Color(red: 0.08, green: 0.09, blue: 0.13, opacity: 0.7)   // 原来是不透明的，现在加入0.7的透明度
                            ]
                        ),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4)
            
            VStack(alignment: .leading, spacing: 8) {
                // 标题栏
                HStack(spacing: 5) {
                    // 指示线 - 紧贴左边
                    Rectangle()
                        .frame(width: 2, height: 10)
                        .foregroundColor(Color(red: 0.4, green: 0.4, blue: 0.4, opacity: 1))
                        .cornerRadius(0.75)
                    
                    // 标题
                    Text(title)
                        .font(.custom("PingFang-SC-Medium", size: 14))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // 箭头
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                }
                .padding(.top, 12)
                .padding(.trailing, 12) // 只保留右侧内边距
                .padding(.leading, 0) // 左侧不设置内边距，确保指示线紧贴边缘
                
                // 内容区域
                content()
                    .padding(.bottom, 8)
            }
        }
        .frame(maxWidth: isHalfWidth ? .infinity : nil)
        .onTapGesture {
            if let onTap = onTap {
                onTap()
            }
        }
    }
}

// MARK: - 指标项组件
struct MetricItemView: View {
    let icon: CircularProgressView
    let value: String
    let unit: String
    var unitOnNewLine: Bool = false
    var isRealData: Bool = false // 添加是否为真实数据的标识
    
    var body: some View {
        VStack(spacing: 4) {  // 减小间距
            icon
                .frame(width: 56, height: 56)  // 增大环形图标
            
            if unitOnNewLine {
                Text(value)
                    .font(.system(size: 22, weight: .bold))  // 增大数值字体
                    .foregroundColor(value == "--" ? .white : .white)  // 统一显示为白色
                
                Text(unit)
                    .font(.system(size: 13))  // 调整单位字体
                    .foregroundColor(Color(red: 0.6, green: 0.6, blue: 0.6, opacity: 1))
            } else {
                Text(value)
                    .font(.system(size: 22, weight: .bold))  // 增大数值字体
                    .foregroundColor(value == "--" ? .white : .white)  // 统一显示为白色
                
                Text(unit)
                    .font(.system(size: 13))  // 调整单位字体
                    .foregroundColor(Color(red: 0.6, green: 0.6, blue: 0.6, opacity: 1))
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 15)
    }
}

// MARK: - 环形进度组件
struct CircularProgressView: View {
    let progress: CGFloat
    let color: Color
    let iconName: String
    var useAssetImage: Bool = false
    
    var body: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(Color.white.opacity(0.1), lineWidth: 6)
            
            // 进度圆环
            Circle()
                .trim(from: 0, to: progress)
                .stroke(color, style: StrokeStyle(lineWidth: 6, lineCap: .round))
                .rotationEffect(.degrees(-90))
            
            // 中心图标 - 可选择使用资源图片
            if useAssetImage {
                Image(iconName)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 20, height: 20)
                    .foregroundColor(color)
            } else {
                Image(systemName: iconName)
                    .font(.system(size: 16))
                    .foregroundColor(color)
            }
        }
    }
}

// 健康指标位置枚举
enum MetricPosition {
    case top, right, bottom, left
}

// 健康指标数据模型
struct HealthMetric: Identifiable {
    let id = UUID()
    let name: String
    let value: Int
    let position: MetricPosition
}

// 菱形形状
struct Diamond: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let width = rect.width / 2
        let height = rect.height / 2
        
        path.move(to: CGPoint(x: center.x, y: center.y - height))
        path.addLine(to: CGPoint(x: center.x + width, y: center.y))
        path.addLine(to: CGPoint(x: center.x, y: center.y + height))
        path.addLine(to: CGPoint(x: center.x - width, y: center.y))
        path.closeSubpath()
        
        return path
    }
}

// MARK: - 仅图标组件(无圆环)
struct IconView: View {
    let color: Color
    let iconName: String
    
    var body: some View {
        Image(iconName)
            .resizable()
            .scaledToFit()
            .frame(width: 24, height: 24)
            .foregroundColor(color)
    }
}

#Preview {
    MainInsightView()
}

// 在文件底部添加自定义日历视图组件
struct CustomCalendarView: View {
    @Binding var selectedDate: Date
//    @Binding var selectedDay: Int // 添加selectedDay绑定
    var onClose: () -> Void
    var datesWithData: [Date] = [] // 修改为[Date]类型
    
    @State private var currentMonth: Date
    @State private var showFutureAlert = false // 添加提示框状态控制
    @State private var futureAlertDate = "" // 提示框中显示的日期文本
    
    private let calendar = Calendar.current
    private let monthFormatter = DateFormatter()
    private let dayFormatter = DateFormatter()
    private let weekdayFormatter = DateFormatter()
//    , selectedDay: Binding<Int>
    init(selectedDate: Binding<Date>, onClose: @escaping () -> Void, datesWithData: [Date] = []) {
        self._selectedDate = selectedDate
//        self._selectedDay = selectedDay
        self.onClose = onClose
        self.datesWithData = datesWithData // 初始化datesWithData
        self._currentMonth = State(initialValue: selectedDate.wrappedValue)
        
        monthFormatter.dateFormat = "MMMM yyyy"
        dayFormatter.dateFormat = "d"
        weekdayFormatter.dateFormat = "E"
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // 月份导航
            HStack {
                Button(action: {
                    previousMonth()
                }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                Text(monthFormatter.string(from: currentMonth))
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    nextMonth()
                }) {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal)
            
            // 星期标题
            HStack(spacing: 0) {
                ForEach(getDaysOfWeek(), id: \.self) { day in
                    Text(day)
                        .font(.caption)
                        .frame(maxWidth: .infinity)
                        .foregroundColor(.gray)
                }
            }
            
            // 日期网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 15) {
                ForEach(extractDates()) { dateValue in
                    CalendarDayView(
                        dateValue: dateValue,
                        selectedDate: $selectedDate,
                        onDateSelected: { date in
                            handleDateSelection(date)
                        },
                        datesWithData: datesWithData // 传递有数据的日期列表
                    )
                    .frame(height: 40)
                }
            }
            
            // 底部按钮
            HStack {
                Spacer() // 添加Spacer使按钮靠右对齐
                
                Button(action: {
                    // 返回今天
                    selectedDate = Date()
//                    selectedDay = 0 // 设置selectedDay为0表示今天
                    onClose()
                }) {
                    Text("calendar_button_back_to_today".localized)
                        .foregroundColor(.white)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 16)
                        .background(Capsule().fill(Color.gray.opacity(0.3)))
                        .overlay(
                            Capsule()
                                .stroke(Color.white, lineWidth: 1)
                        )
                }
            }
            .padding(.top, 10)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(red: 0.16, green: 0.19, blue: 0.25))
                .shadow(color: Color.black.opacity(0.3), radius: 10, x: 0, y: 5)
        )
        .alert(isPresented: $showFutureAlert) {
            Alert(
                title: Text("calendar_alert_future_date_title".localized),
                message: Text(String(format: "calendar_alert_future_date_message".localized, futureAlertDate)),
                dismissButton: .default(Text("ok".localized))
            )
        }
    }
    
    // 处理日期选择
    private func handleDateSelection(_ date: Date) {
        // 检查日期是否在未来
        if calendar.compare(date, to: Date(), toGranularity: .day) == .orderedDescending {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .none
            futureAlertDate = formatter.string(from: date)
            showFutureAlert = true
            return
        }

        selectedDate = date
        
        // 计算选中日期与今天的天数差，更新selectedDay
        if let dayDifference = Calendar.current.dateComponents([.day], from: calendar.startOfDay(for: Date()), to: date).day {
//            selectedDay = dayDifference // selectedDay表示相对于今天的偏移
        }
        
        onClose()
    }
    
    // 获取星期标题
    private func getDaysOfWeek() -> [String] {
        // 直接返回固定顺序的星期标题，以周日开始
        return ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
    }
    
    // 提取当月日期
    private func extractDates() -> [DateValue] {
        let monthStart = startOfMonth(for: currentMonth)
        let monthEnd = endOfMonth(for: currentMonth)
        
        let calendar = Calendar.current
        let startDayOfWeek = calendar.component(.weekday, from: monthStart)
        let daysInMonth = calendar.dateComponents([.day], from: monthStart, to: monthEnd).day! + 1
        
        var dateValues = [DateValue]()
        
        // 调整星期偏移
        // weekday: 1=周日, 2=周一, ..., 7=周六
        // 所以从周日开始，偏移量就是weekday-1
        let offset = startDayOfWeek - 1
        
        // 填充前置空白
        for _ in 0..<offset {
            dateValues.append(DateValue(day: 0, date: Date(), isCurrentMonth: false))
        }
        
        // 填充当月日期
        for day in 1...daysInMonth {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: monthStart) {
                dateValues.append(DateValue(day: day, date: date, isCurrentMonth: true))
            }
        }
        
        return dateValues
    }
    
    // 获取月份开始日期
    private func startOfMonth(for date: Date) -> Date {
        let components = calendar.dateComponents([.year, .month], from: date)
        return calendar.date(from: components)!
    }
    
    // 获取月份结束日期
    private func endOfMonth(for date: Date) -> Date {
        var components = DateComponents()
        components.month = 1
        components.day = -1
        return calendar.date(byAdding: components, to: startOfMonth(for: date))!
    }
    
    // 切换到上个月
    private func previousMonth() {
        if let newDate = calendar.date(byAdding: .month, value: -1, to: currentMonth) {
            currentMonth = newDate
        }
    }
    
    // 切换到下个月
    private func nextMonth() {
        if let newDate = calendar.date(byAdding: .month, value: 1, to: currentMonth) {
            currentMonth = newDate
        }
    }
}

// 日期值模型
struct DateValue: Identifiable {
    var id = UUID()
    var day: Int
    var date: Date
    var isCurrentMonth: Bool
}

// 日历天数视图
struct CalendarDayView: View {
    let dateValue: DateValue
    @Binding var selectedDate: Date
    var onDateSelected: (Date) -> Void
    var datesWithData: [Date] = [] // 添加这行，接收有数据的日期列表
    
    private let calendar = Calendar.current
    
    var isSelected: Bool {
        calendar.isDate(dateValue.date, inSameDayAs: selectedDate)
    }
    
    var isToday: Bool {
        calendar.isDateInToday(dateValue.date)
    }
    
    // 判断是否为未来日期
    var isFutureDate: Bool {
        if dateValue.day == 0 { return false }
        return calendar.compare(dateValue.date, to: Date(), toGranularity: .day) == .orderedDescending
    }
    
    // 判断是否为过去日期（含今天）
    var isPastOrToday: Bool {
        if dateValue.day == 0 { return false }
        return calendar.compare(dateValue.date, to: Date(), toGranularity: .day) != .orderedDescending
    }
    
    // 检查是否有真实数据
    var hasData: Bool {
        if dateValue.day == 0 { return false }
        return datesWithData.contains { calendar.isDate($0, inSameDayAs: dateValue.date) }
    }
    
    var body: some View {
        VStack {
            if dateValue.day != 0 {
                Button(action: {
                    onDateSelected(dateValue.date)
                }) {
                    Text("\(dateValue.day)")
                        .font(.system(size: 16))
                        .frame(maxWidth: .infinity)
                        .foregroundColor(
                            isSelected ? .white :
                                isFutureDate ? Color.white.opacity(0.4) :
                                (dateValue.isCurrentMonth ? .white : .gray)
                        )
                }
                .disabled(isFutureDate)
                
                // 底部指示点
                if isSelected {
                    // 选中日期显示亮蓝色点
                    Circle()
                        .fill(Color(hex: "#1B71FF"))
                        .frame(width: 6, height: 6)
                        .padding(.top, 2)
                } else if isPastOrToday {
                    // 过去日期根据是否有数据显示不同颜色的点
                    Circle()
                        .fill(hasData ? Color(hex: "#1B71FF") : Color(hex: "#1B71FF").opacity(0.3))
                        .frame(width: 6, height: 6)
                        .padding(.top, 2)
                } else {
                    // 其他情况（未来日期）显示透明点（保持布局一致）
                    Circle()
                        .fill(Color.clear)
                        .frame(width: 6, height: 6)
                        .padding(.top, 2)
                }
            } else {
                Text("")
                    .font(.system(size: 16))
                    .frame(maxWidth: .infinity)
            }
        }
    }
}

// MARK: - 健康状态雷达图组件
struct HealthRadarView: View {
    let healthData: [HealthMetric]
    let isLoadingSleepData: Bool
    let isLoadingStressData: Bool
    let isLoadingVitalData: Bool
    let getSleepScoreColor: (Int) -> Color
    let getStressScoreColor: () -> Color
    let getVitalScoreColor: () -> Color
    let getActivityScoreColor: (Int) -> Color
    
    var body: some View {
        ZStack {
	
            // 雷达图核心部分
            ZStack {
                // 蓝色背景图
                Image("组 3_slices")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 220, height: 220)
                
                // 动态评分菱形
                GeometryReader { geometry in
                    let size = min(geometry.size.width, geometry.size.height)
                    let center = CGPoint(x: size/2, y: size/2)
                    let maxRadius = size * 0.35  // 最大半径为容器的35%
                    let minRadius = size * 0.1   // 最小半径为容器的10%
                    
                    // 获取四个方向的评分值（0-100）
                    let sleepScore = CGFloat(healthData[0].value)
                    let vitalScore = CGFloat(healthData[1].value)
                    let activityScore = CGFloat(healthData[2].value)
                    let stressScore = CGFloat(healthData[3].value)
                    
                    // 计算每个方向的实际半径
                    let sleepRadius = minRadius + (maxRadius - minRadius) * (sleepScore / 100.0)
                    let vitalRadius = minRadius + (maxRadius - minRadius) * (vitalScore / 100.0)
                    let activityRadius = minRadius + (maxRadius - minRadius) * (activityScore / 100.0)
                    let stressRadius = minRadius + (maxRadius - minRadius) * (stressScore / 100.0)
                    
                    // 创建动态菱形
                    Path { path in
                        // 计算四个顶点
                        let topPoint = CGPoint(x: center.x, y: center.y - sleepRadius)     // Sleep
                        let rightPoint = CGPoint(x: center.x + vitalRadius, y: center.y)   // Vital signs
                        let bottomPoint = CGPoint(x: center.x, y: center.y + activityRadius) // Activity
                        let leftPoint = CGPoint(x: center.x - stressRadius, y: center.y)   // Stress
                        
                        // 绘制路径
                        path.move(to: topPoint)
                        path.addLine(to: rightPoint)
                        path.addLine(to: bottomPoint)
                        path.addLine(to: leftPoint)
                        path.closeSubpath()
                    }
                    .fill(Color.blue.opacity(0.3))
                    .overlay(
                        Path { path in
                            let topPoint = CGPoint(x: center.x, y: center.y - sleepRadius)
                            let rightPoint = CGPoint(x: center.x + vitalRadius, y: center.y)
                            let bottomPoint = CGPoint(x: center.x, y: center.y + activityRadius)
                            let leftPoint = CGPoint(x: center.x - stressRadius, y: center.y)
                            
                            path.move(to: topPoint)
                            path.addLine(to: rightPoint)
                            path.addLine(to: bottomPoint)
                            path.addLine(to: leftPoint)
                            path.closeSubpath()
                        }
                        .stroke(Color.blue.opacity(0.8), lineWidth: 2)
                    )
                }
                .frame(width: 220, height: 220)
                .animation(.easeInOut(duration: 0.3), value: [
                    healthData[0].value,
                    healthData[1].value,
                    healthData[2].value,
                    healthData[3].value
                ])
            }
            
            // 顶部和底部指标
            VStack(spacing: 0) {
                // 顶部指标 - Sleep（仅 status != 1 时展示在顶部）
                if VersionUpdateService.shared.status == 1 {
                    VStack(spacing: 2) {
                        Text("health_metric_sleep".localized)
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.8))
                        
                        if isLoadingSleepData {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.7)
                        } else {
                            Text(healthData[0].value > 0 ? "\(healthData[0].value)" : "--")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(getSleepScoreColor(healthData[0].value))
                        }
                    }
                    .offset(y: -8)
                }

                Spacer()
                
                // 底部指标 - Activity（仅 status != 1 时展示在底部）
                if VersionUpdateService.shared.status == 1 {
                    VStack(spacing: 2) {
                        Text(healthData[2].value > 0 ? "\(healthData[2].value)" : "--")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(getActivityScoreColor(healthData[2].value))
                        Text("health_metric_activity".localized)
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .offset(y: 8)
                }
            }
            .frame(height: 280)

            // 左右指标：根据 status 切换内容
            HStack(spacing: 0) {
                if VersionUpdateService.shared.status == 1 {
                    VStack(spacing: 2) {
                        Text("health_metric_stress".localized)
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.8))
                        if isLoadingStressData {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.7)
                        } else {
                            Text(healthData[3].value > 0 ? "\(healthData[3].value)" : "--")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(getStressScoreColor())
                        }
                    }
                    .offset(x: -8)
                    
                    Spacer()
                    
                    VStack(spacing: 2) {
                        let vitalTitle = VersionUpdateService.shared.status == 1 ? "health_metric_vital_signs".localized : "health_metric_overall_situation".localized
                        Text(vitalTitle)
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.8))
                        if isLoadingVitalData {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.7)
                        } else {
                            Text(healthData[1].value > 0 ? "\(healthData[1].value)" : "--")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(getVitalScoreColor())
                        }
                    }
                    .offset(x: 16)
                } else {
                    // 左侧：Activity
                    VStack(spacing: 2) {
                        Text("health_metric_activity".localized)
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.8))
                        Text(healthData[2].value > 0 ? "\(healthData[2].value)" : "--")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(getActivityScoreColor(healthData[2].value))
                    }
                    .offset(x: -8)

                    Spacer()

                    // 右侧：Sleep
                    VStack(spacing: 2) {
                        Text("health_metric_sleep".localized)
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.8))
                        if isLoadingSleepData {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.7)
                        } else {
                            Text(healthData[0].value > 0 ? "\(healthData[0].value)" : "--")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(getSleepScoreColor(healthData[0].value))
                        }
                    }
                    .offset(x: 16)
                    // status != 1 时显示 Stress 和 Vital
                    
                }
            }
            .frame(width: 300)

        }
    }
}

// 在文件末尾添加ConnectingStateView实现
struct ConnectingStateView: View {
    @State private var isAnimating = false
    
    var body: some View {
        ZStack {
            // 使用带有回退图像的GIFView
            GIFView("lianjie", fallbackSystemImage: "bluetoothwaves")
                .frame(width: 16, height: 16)
                .rotationEffect(.degrees(isAnimating ? 360 : 0))
                .animation(
                    Animation.linear(duration: 2)
                        .repeatForever(autoreverses: false),
                    value: isAnimating
                )
                .onAppear {
                    isAnimating = true
                }
        }
    }
}

extension View {
    func bottomSheet<Content: View>(
        isPresented: Binding<Bool>,
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        ZStack {
            self
            if isPresented.wrappedValue {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation {
                            isPresented.wrappedValue = false
                        }
                    }
                    .zIndex(1)
                
                VStack {
                    Spacer()
                    content()
                }
                .transition(.move(edge: .bottom))
                .zIndex(2)
                .onDisappear {
                    isPresented.wrappedValue = false
                }
            }
        }
        .animation(.easeInOut, value: isPresented.wrappedValue)
    }
}



