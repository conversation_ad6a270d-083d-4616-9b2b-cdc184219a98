//
//  AppUpdateService.swift
//  WindRing
//
//  Created by zx on 2025/5/15.
//

import SwiftUI
import Combine
import Foundation
import Network

// MARK: - 版本更新服务
class VersionUpdateService: ObservableObject {
    // MARK: - 单例
    public static let shared = VersionUpdateService()
    @Published var updateAlertItem: UpdateCheckResult? = nil
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    ///审核状态
    @Published var status: Int? = nil {
        didSet {
            if let newStatus = status {
                UserDefaults.standard.set(newStatus, forKey: userDefaultsKeyStatus)
            }
        }
    }
    var cancellables = Set<AnyCancellable>()

    private var networkMonitor = NWPathMonitor()
    private let userDefaultsKeyLastAlert = "lastNonMandatoryUpdateAlertDate"
    private let userDefaultsKeyStatus = "appUpdateStatus"

    init() {
        // Load persisted status on init
        self.status = UserDefaults.standard.object(forKey: userDefaultsKeyStatus) as? Int

        // Start network monitoring
        setupNetworkMonitoring()
        
        // Initial check for updates
        requestCheckForUpdates()
    }

    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                if path.status == .satisfied {
                    print("网络已连接，触发版本检查。")
                    self?.requestCheckForUpdates()
                } else {
                    print("网络已断开。")
                }
            }
        }
        let queue = DispatchQueue(label: "NetworkMonitor")
        networkMonitor.start(queue: queue)
    }

    public func requestCheckForUpdates() {
        isLoading = true
        errorMessage = nil
        updateAlertItem = nil

        guard let currentVersionString = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String else {
            self.errorMessage = VersionCheckError.currentVersionNotFound.localizedDescription
            self.isLoading = false
            return
        }

        let networkManager = NetworkManager.shared
        networkManager.request(
            endpoint: "/app-api/member/version/record/1",
            method: .get,
            parameters: [:],
            responseType: AppVersionData.self
        )
        .receive(on: DispatchQueue.main)
        .sink(
            receiveCompletion: { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.errorMessage = error.localizedDescription
                    print("版本检查失败: \(error.localizedDescription)")
                }
            },
            receiveValue: { [weak self] response in
                guard let self = self else { return }
                
//                if let apiBusinessCode = response.code, !(apiBusinessCode == 200 || apiBusinessCode == 0) {
//                    self.errorMessage = response.msg ?? "接口返回业务错误码: \(apiBusinessCode)"
//                    return
//                }

//                guard let versionData = response.data else {
//                    self.errorMessage = "服务器未返回版本信息"
//                    return
//                }

                self.status = response.status
                let serverVersion = response.versionName ?? ""
                
                let currentVersionInt = versionToInt(currentVersionString)
                let serverVersionInt = versionToInt(serverVersion)

                let needsUpdate = currentVersionInt < serverVersionInt
                 
                if currentVersionInt > serverVersionInt {
                    self.status = 0
                } else {
                    self.status = 1
                }
                
//                let isMandatory = response.operationType == 0 // 假设 operationType = 0 表示强制更新

                if needsUpdate {
//                    if isMandatory {
//                        self.setUpdateAlert(response: response, serverVersion: serverVersion)
//                    } else {
                        if self.shouldShowNonMandatoryUpdate() {
                            self.setUpdateAlert(response: response, serverVersion: serverVersion)
                            UserDefaults.standard.set(Date(), forKey: self.userDefaultsKeyLastAlert)
                        } else {
                            print("非强制更新今天已经提醒过，不再提醒。")
                        }
//                    }
                }
                
                print("版本检查完成 - 当前版本: \(currentVersionString), 服务器版本: \(serverVersion), 需要更新: \(needsUpdate)")
            }
        )
        .store(in: &cancellables)
    }

    private func setUpdateAlert(response: AppVersionData, serverVersion: String) {
        self.updateAlertItem = UpdateCheckResult(
            needsUpdate: true,
            isMandatory: response.operationType == 0, // 假设 operationType = 0 表示强制更新
            serverVersion: serverVersion,
            updateTitle:  "发现新版本",
            updateMessage: "发现新版本 \(serverVersion)，建议您立即更新以获得更好的体验。",
            updateURL: URL(string: response.url ?? "")
        )
    }

    private func shouldShowNonMandatoryUpdate() -> Bool {
        guard let lastAlertDate = UserDefaults.standard.object(forKey: userDefaultsKeyLastAlert) as? Date else {
            return true
        }
        // Check if more than 24 hours have passed.
        let twentyFourHoursInSeconds: TimeInterval = 24 * 60 * 60
        return Date().timeIntervalSince(lastAlertDate) > twentyFourHoursInSeconds
    }
    
    func versionToInt(_ version: String) -> Int {
        let components = version.split(separator: ".")
        let padded = components.map { String(format: "%02d", Int($0) ?? 0) } // 每段补齐2位
        return Int(padded.joined()) ?? 0
    }
}
