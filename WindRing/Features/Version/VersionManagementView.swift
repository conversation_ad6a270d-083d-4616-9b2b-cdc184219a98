import SwiftUI

// 为VersionManagementView提供特定的光晕背景效果组件
// 避免与GoalSettingScreen中的CornerGlowBackground冲突
struct VersionGlowBackground: View {
    var position: CGPoint? = nil
    var colors: [Color] = [
        Color(UIColor(red: 0, green: 0.28, blue: 0.71, alpha: 0.4)),
        Color(UIColor(red: 0, green: 0.28, blue: 0.71, alpha: 0.2)),
        Color(UIColor(red: 0, green: 0.28, blue: 0.71, alpha: 0.0))
    ]
    var frameSize: CGFloat = 800
    var startRadius: CGFloat = 1
    var endRadius: CGFloat = 400
    
    var body: some View {
        Circle()
            .fill(
                RadialGradient(
                    gradient: Gradient(colors: colors),
                    center: .center,
                    startRadius: startRadius,
                    endRadius: endRadius
                )
            )
            .frame(width: frameSize, height: frameSize)
            .position(
                position ?? CGPoint(x: UIScreen.main.bounds.width, y: 0)
            )
            .edgesIgnoringSafeArea(.all)
    }
}

// 版本管理主页面
struct VersionManagementView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var showVersioningDetail = true
    
    var body: some View {
        CommonBackgroundView(title: "versioning".localized) {
            VStack {
                Spacer()
                
                Text("versioning".localized)
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.black)
                    .onTapGesture {
                        showVersioningDetail = true
                    }
                
                Spacer()
            }
        }
        .sheet(isPresented: $showVersioningDetail) {
            VersioningView()
        }
    }
}

// 详细版本信息页面
struct VersioningView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var versionService: VersionUpdateService
    @Environment(\.openURL) var openURL
    @State private var showAppVersionDetail = false
    @State private var isCheckingUpdate = false
    @State private var showUpdateAlert = false
    @State private var errorMessage: String?
    // 获取当前App版本号
    private var currentAppVersion: String {
        let version = Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "--"
        return String(format: "versioning_app_version_format".localized, version)
    }
    // 硬编码的版本信息 - 实际应用中应从设备或API获取
//    private let appVersion = "APP version 3.31"
    @StateObject private var deviceService = WindRingDeviceService.shared
    
    private let firmwareVersion = "Firmware version FR01.048"
    private let equipmentName = "WindRing"
    private let deviceFirmwareVersion = "MOY-R193-2.1"
    private let macAddress = "--"
    private let serialNumber = "--"
    
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色 - 使用深色背景
            Color(UIColor(red: 0.027, green: 0.027, blue: 0.031, alpha: 1)).edgesIgnoringSafeArea(.all)
            
            // 使用封装的光晕背景组件
            VersionGlowBackground()
            
            VStack(spacing: 0) {
                // 自定义导航栏
                HStack(alignment: .center) {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    // 标题居左 - 添加点击返回功能
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("versioning".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
                            .padding(.leading, 5)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .frame(height: 44) // 标准iOS导航栏高度
                .padding(.top, 8)
                
                // 主内容
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 20) {
                        // 软件版本部分
                        versionSection(title: "versioning_software_version".localized, content: currentAppVersion, isFirst: true)
                        
                        // 固件版本部分
//                        versionSection(title: "Firmware version", content: firmwareVersion)
                        
                        // 设备信息部分
                        VStack(spacing: 0) {
                            HStack {
                                Rectangle()
                                    .fill(Color(UIColor(red: 0, green: 0.28, blue: 0.71, alpha: 1)))
                                    .frame(width: 2, height: 7.5)
                                    .cornerRadius(1)
                                
                                Text("versioning_firmware_version".localized)
                                    .font(.custom("PingFang-SC-Medium", size: 14))
                                    .foregroundColor(Color(UIColor(red: 0.62, green: 0.63, blue: 0.64, alpha: 1)))
                                    .padding(.leading, 2)
                                
                                Spacer()
                            }
                            .padding(.leading, 16)
                            .padding(.vertical, 16)
                            
                            deviceInfoRow(title: "versioning_equipment_name".localized, value: deviceService.deviceInfo?.localName ?? "-")
                            Divider()
                                .background(Color.gray.opacity(0.2))
                                .padding(.leading, 16)
                            deviceInfoRow(title: "firmware_version".localized, value: deviceService.deviceInfo?.firmwareVersion ?? "-")
                            Divider()
                                .background(Color.gray.opacity(0.2))
                                .padding(.leading, 16)
                            deviceInfoRow(title: "versioning_mac_address".localized, value: deviceService.deviceInfo?.mac ?? "-")
                            Divider()
                                .background(Color.gray.opacity(0.2))
                                .padding(.leading, 16)
                            let deviceIdSuffix: String = {
                                guard let id = deviceService.deviceInfo?.mac else {
                                    return "-"
                                }
                                return String(id.suffix(8))
                            }()
                            
                            deviceInfoRow(title: "serial_number".localized, value: deviceService.deviceInfo?.serial ?? "-")
                        }
                        .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
                        .cornerRadius(25)
                        .padding(.horizontal, 10)
                    }
                    .padding(.top, 20)
                    .padding(.bottom, 30)
                }
            }
            if isCheckingUpdate {
                Color.black.opacity(0.3)
                    .edgesIgnoringSafeArea(.all)
                
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)
            }
        }
        
//        .alert("versioning_update_check_failed".localized, isPresented: .constant(versionService.errorMessage != nil)) {
//            Button("ok".localized, role: .cancel) {
//                versionService.errorMessage = nil
//            }
//        } message: {
//            Text(versionService.errorMessage ?? "versioning_unknown_error".localized)
//        }
        .navigationBarHidden(true)
    }
    
    // 版本部分
    private func versionSection(title: String, content: String, isFirst: Bool = false) -> some View {
//        NavigationLink(destination: AppVersionDetailView()) {
            VStack(spacing: 0) {
                if isFirst {
                    // 顶部图标
                    Image("slices_26")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 60, height: 60)
                        .padding(.top, 20)
                        .padding(.bottom, 10)
                }
                
                HStack {
                    Rectangle()
                        .fill(Color(UIColor(red: 0, green: 0.28, blue: 0.71, alpha: 1)))
                        .frame(width: 2, height: 7.5)
                        .cornerRadius(1)
                    
                    Text(title)
                        .font(.custom("PingFang-SC-Medium", size: 14))
                        .foregroundColor(Color(UIColor(red: 0.62, green: 0.63, blue: 0.64, alpha: 1)))
                        .padding(.leading, 2)
                    
                    Spacer()
                }
                .padding(.leading, 16)
                .padding(.vertical, 16)
                
                HStack {
                    Text(content)
                        .font(.custom("PingFang-SC-Medium", size: 13))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Text("versioning_last_version".localized)
                        .font(.system(size: 13))
                        .foregroundColor(.gray)
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
            }
            .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
            .cornerRadius(25)
            .padding(.horizontal, 10)
            .onTapGesture {
                if isFirst {
                    isCheckingUpdate = true
                    versionService.requestCheckForUpdates()
                    
                    // 监听加载状态变化
                    versionService.$isLoading
                        .sink {  isLoading in
                            self.isCheckingUpdate = isLoading
                        }
                        .store(in: &versionService.cancellables)
                }
            }
//        }
//        .buttonStyle(PlainButtonStyle())
    }
    
    // 设备信息行
    private func deviceInfoRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.system(size: 15))
                .foregroundColor(.white)
            
            Spacer()
            
            Text(value)
                .font(.system(size: 15))
                .foregroundColor(.gray)
        }
        .padding(.vertical, 14)
        .padding(.horizontal, 16)
    }
}

// 添加新的详情页面
//struct AppVersionDetailView: View {
//    @Environment(\.presentationMode) var presentationMode
//    
//    var body: some View {
//        ZStack(alignment: .top) {
//            // 背景色xf
//            Color(UIColor(red: 0.027, green: 0.027, blue: 0.031, alpha: 1)).edgesIgnoringSafeArea(.all)
//            
//            // 添加光晕背景
//            VersionGlowBackground()
//            
//            VStack(spacing: 0) {
//                // 自定义导航栏
//                HStack(alignment: .center) {
//                    // 返回按钮
//                    Button(action: {
//                        presentationMode.wrappedValue.dismiss()
//                    }) {
//                        Image(systemName: "chevron.left")
//                            .font(.system(size: 17, weight: .semibold))
//                            .foregroundColor(.white)
//                    }
//                    
//                    // 标题居左 - 添加点击返回功能
//                    Button(action: {
//                        presentationMode.wrappedValue.dismiss()
//                    }) {
//                        Text("About WindRing")
//                            .font(.custom("PingFang-SC-Heavy", size: 19))
//                            .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
//                            .padding(.leading, 5)
//                    }
//                    
//                    Spacer()
//                }
//                .padding(.horizontal, 16)
//                .frame(height: 44) // 标准iOS导航栏高度
//                .padding(.top, 8)
//                
//                // 主内容
//                ScrollView(showsIndicators: false) {
//                    VStack(spacing: 20) {
//                        // 顶部卡片
//                        VStack(spacing: 16) {
//                            // 使用ZStack将logo叠放在圆圈上
//                            ZStack {
//                                // 背景圆圈
//                                Image("slices_32")
//                                    .resizable()
//                                    .scaledToFit()
//                                    .frame(width: 95, height: 95)
//                                
//                                // 叠放在上面的logo
//                                Image("logo")
//                                    .resizable()
//                                    .scaledToFit()
//                                    .frame(width: 60, height: 60)
//                                    .offset(y: -5) // 微调位置，使logo在圆圈中间偏上位置
//                            }
//                            .padding(.top, 30)
//                            
//                            // 应用名称
//                            Text("WindRing")
//                                .font(.custom("PingFang-SC-Medium", size: 20))
//                                .foregroundColor(.white)
//                            
//                            // 版本号
//                            Text("Current version 3.31")
//                                .font(.custom("PingFang-SC-Regular", size: 15))
//                                .foregroundColor(Color(UIColor(red: 0.62, green: 0.63, blue: 0.64, alpha: 1)))
//                                .padding(.bottom, 30)
//                        }
//                        .frame(maxWidth: .infinity)
//                        .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
//                        .cornerRadius(25)
//                        .padding(.horizontal, 10)
//                        
//                        // 底部卡片
//                        VStack(spacing: 0) {
//                            // Terms Of Use
//                            NavigationLink(destination: VersionTermsOfUseView()) {
//                                HStack {
//                                    Image("slices_28")
//                                        .resizable()
//                                        .scaledToFit()
//                                        .frame(width: 16, height: 16)
//                                    
//                                    Text("Terms Of Use")
//                                        .font(.custom("PingFang-SC-Medium", size: 15))
//                                        .foregroundColor(.white)
//                                    
//                                    Spacer()
//                                    
//                                    Image(systemName: "chevron.right")
//                                        .foregroundColor(Color(UIColor(red: 0.62, green: 0.63, blue: 0.64, alpha: 1)))
//                                }
//                                .padding(.vertical, 16)
//                                .padding(.horizontal, 16)
//                            }
//                            
//                            Divider()
//                                .background(Color.gray.opacity(0.2))
//                                .padding(.leading, 16)
//                            
//                            // Privacy Policy
//                            NavigationLink(destination: VersionPrivacyPolicyView()) {
//                                HStack {
//                                    Image("slices_29")
//                                        .resizable()
//                                        .scaledToFit()
//                                        .frame(width: 16, height: 16)
//                                    
//                                    Text("Privacy Policy")
//                                        .font(.custom("PingFang-SC-Medium", size: 15))
//                                        .foregroundColor(.white)
//                                    
//                                    Spacer()
//                                    
//                                    Image(systemName: "chevron.right")
//                                        .foregroundColor(Color(UIColor(red: 0.62, green: 0.63, blue: 0.64, alpha: 1)))
//                                }
//                                .padding(.vertical, 16)
//                                .padding(.horizontal, 16)
//                            }
//                            
//                            Divider()
//                                .background(Color.gray.opacity(0.2))
//                                .padding(.leading, 16)
//                            
//                            // Information list
//                            NavigationLink(destination: VersionInformationListView()) {
//                                HStack {
//                                    Image("slices_30")
//                                        .resizable()
//                                        .scaledToFit()
//                                        .frame(width: 16, height: 16)
//                                    
//                                    Text("Information list")
//                                        .font(.custom("PingFang-SC-Medium", size: 15))
//                                        .foregroundColor(.white)
//                                    
//                                    Spacer()
//                                    
//                                    Image(systemName: "chevron.right")
//                                        .foregroundColor(Color(UIColor(red: 0.62, green: 0.63, blue: 0.64, alpha: 1)))
//                                }
//                                .padding(.vertical, 16)
//                                .padding(.horizontal, 16)
//                            }
//                        }
//                        .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
//                        .cornerRadius(25)
//                        .padding(.horizontal, 10)
//                    }
//                    .padding(.top, 20)
//                    .padding(.bottom, 30)
//                }
//            }
//        }
//        .navigationBarHidden(true)
//    }
//}

// 使用条款视图
struct VersionTermsOfUseView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色
            Color(UIColor(red: 0.027, green: 0.027, blue: 0.031, alpha: 1)).edgesIgnoringSafeArea(.all)
            
            // 添加光晕背景
            VersionGlowBackground()
            
            VStack(spacing: 0) {
                // 自定义导航栏
                HStack(alignment: .center) {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    // 标题居左 - 添加点击返回功能
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("about_us_terms_of_use".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
                            .padding(.leading, 5)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .frame(height: 44) // 标准iOS导航栏高度
                .padding(.top, 8)
                
                // 主内容
                ScrollView {
                    VStack(spacing: 0) {
                        VStack(spacing: 0) {
                            // 标题部分
                            VStack(spacing: 8) {
                                Text("terms_of_use_title".localized)
                                    .font(.custom("PingFang-SC-Bold", size: 15))
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity, alignment: .center)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 54)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(UIColor(red: 0.16, green: 0.19, blue: 0.25, alpha: 1)),
                                        Color(UIColor(red: 0.08, green: 0.09, blue: 0.13, alpha: 1))
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            
                            // 内容部分
                            VStack(alignment: .leading, spacing: 24) {
                                // 更新日期
                                Text("terms_of_use_last_updated_date_format".localized)
                                    .font(.custom("PingFang-SC-Regular", size: 12))
                                    .foregroundColor(Color(UIColor(red: 0.34, green: 0.35, blue: 0.37, alpha: 1)))
                                    .frame(maxWidth: .infinity, alignment: .center)
                                    .padding(.bottom, 8)
                                
                                // 介绍部分
                                sectionView(
                                    number: "01",
                                    title: "terms_of_use_intro_title".localized,
                                    content: "terms_of_use_intro_content".localized
                                )
                                
                                // 数据隐私部分
                                sectionView(
                                    number: "02",
                                    title: "terms_of_use_privacy_title".localized,
                                    content: "terms_of_use_privacy_content".localized
                                )
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 20)
                        }
                    }
                    .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
                    .cornerRadius(25)
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
            }
        }
        .navigationBarHidden(true)
    }
    
    // 章节视图
    private func sectionView(number: String, title: String, content: String) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题行带背景
            ZStack(alignment: .leading) {
                // 背景
                Rectangle()
                    .fill(Color(UIColor(red: 0.08, green: 0.1, blue: 0.14, alpha: 1)))
                    .frame(height: 18)
                
                // 内容
                HStack(spacing: 12) {
                    // 序号背景
                    ZStack {
                        Image("slices_31")
                            .resizable()
                            .frame(width: 32, height: 32)
                        
                        Text(number)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    }
                    
                    // 标题
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
            }
            .padding(.leading, -16)  // 抵消父视图的内边距，使背景延伸到边缘
            .padding(.trailing, -16)
            
            // 内容
            Text(content)
                .font(.system(size: 14))
                .foregroundColor(Color(white: 1.0, opacity: 0.7))
                .lineSpacing(4)
        }
    }
}

// 隐私政策视图
struct VersionPrivacyPolicyView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色
            Color(UIColor(red: 0.027, green: 0.027, blue: 0.031, alpha: 1)).edgesIgnoringSafeArea(.all)
            
            // 添加光晕背景
            VersionGlowBackground()
            
            VStack(spacing: 0) {
                // 自定义导航栏
                HStack(alignment: .center) {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    // 标题居左 - 添加点击返回功能
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("about_us_privacy_policy".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
                            .padding(.leading, 5)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .frame(height: 44) // 标准iOS导航栏高度
                .padding(.top, 8)
                
                // 主内容
                ScrollView {
                    VStack(spacing: 0) {
                        VStack(spacing: 0) {
                            // 标题部分
                            VStack(spacing: 8) {
                                Text("privacy_policy_view_title".localized)
                                    .font(.custom("PingFang-SC-Bold", size: 15))
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity, alignment: .center)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 54)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(UIColor(red: 0.16, green: 0.19, blue: 0.25, alpha: 1)),
                                        Color(UIColor(red: 0.08, green: 0.09, blue: 0.13, alpha: 1))
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            
                            // 内容部分
                            VStack(alignment: .leading, spacing: 24) {
                                // 更新日期
                                Text("privacy_policy_last_updated_date_format".localized)
                                    .font(.custom("PingFang-SC-Regular", size: 12))
                                    .foregroundColor(Color(UIColor(red: 0.34, green: 0.35, blue: 0.37, alpha: 1)))
                                    .frame(maxWidth: .infinity, alignment: .center)
                                    .padding(.bottom, 8)
                                
                                // 信息收集部分
                                sectionView(
                                    number: "01",
                                    title: "privacy_policy_info_collection_title".localized,
                                    content: "privacy_policy_info_collection_content".localized
                                )
                                
                                // 数据存储和安全部分
                                sectionView(
                                    number: "02",
                                    title: "privacy_policy_data_security_title".localized,
                                    content: "privacy_policy_data_security_content".localized
                                )
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 20)
                        }
                    }
                    .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
                    .cornerRadius(25)
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
            }
        }
        .navigationBarHidden(true)
    }
    
    // 章节视图
    private func sectionView(number: String, title: String, content: String) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题行带背景
            ZStack(alignment: .leading) {
                // 背景
                Rectangle()
                    .fill(Color(UIColor(red: 0.08, green: 0.1, blue: 0.14, alpha: 1)))
                    .frame(height: 18)
                
                // 内容
                HStack(spacing: 12) {
                    // 序号背景
                    ZStack {
                        Image("slices_31")
                            .resizable()
                            .frame(width: 32, height: 32)
                        
                        Text(number)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    }
                    
                    // 标题
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
            }
            .padding(.leading, -16)  // 抵消父视图的内边距，使背景延伸到边缘
            .padding(.trailing, -16)
            
            // 内容
            Text(content)
                .font(.system(size: 14))
                .foregroundColor(Color(white: 1.0, opacity: 0.7))
                .lineSpacing(4)
        }
    }
}

// 信息列表视图
struct VersionInformationListView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色
            Color(UIColor(red: 0.027, green: 0.027, blue: 0.031, alpha: 1)).edgesIgnoringSafeArea(.all)
            
            // 添加光晕背景
            VersionGlowBackground()
            
            VStack(spacing: 0) {
                // 自定义导航栏
                HStack(alignment: .center) {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    // 标题居左 - 添加点击返回功能
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("about_us_information_list".localized)
                            .font(.custom("PingFang-SC-Heavy", size: 19))
                            .foregroundColor(Color(UIColor(red: 1, green: 1, blue: 1, alpha: 1)))
                            .padding(.leading, 5)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .frame(height: 44) // 标准iOS导航栏高度
                .padding(.top, 8)
                
                // 主内容
                ScrollView {
                    VStack(spacing: 0) {
                        // 标题部分
                        VStack(spacing: 10) {
                            Text("info_list_health_title".localized)
                                .font(.custom("PingFang-SC-Bold", size: 17))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity, alignment: .center)
                                .padding(.top, 20)
                                .padding(.bottom, 10)
                            
                            // 说明文字
                            Text("info_list_health_description".localized)
                                .font(.system(size: 14))
                                .foregroundColor(Color(white: 0.8, opacity: 1.0))
                                .multilineTextAlignment(.leading)
                                .lineSpacing(4)
                                .padding(.horizontal, 16)
                                .padding(.bottom, 20)
                        }
                        .frame(maxWidth: .infinity)
                        .background(Color(UIColor(red: 0.08, green: 0.1, blue: 0.14, alpha: 1)))
                        .cornerRadius(15, corners: [.topLeft, .topRight])
                        
                        // 注册/登录部分
                        infoSectionView(
                            title: "info_list_register_login_title".localized,
                            personalInfo: "info_list_collect_personal_info".localized,
                            personalInfoContent: "info_list_register_login_info_content".localized,
                            businessScene: "info_list_business_scene".localized,
                            businessSceneContent: "info_list_register_login_scene_content".localized
                        )
                        
                        // 绑定设备部分
                        infoSectionView(
                            title: "info_list_bind_device_title".localized,
                            personalInfo: "info_list_collect_personal_info".localized,
                            personalInfoContent: "info_list_bind_device_info_content".localized,
                            businessScene: "info_list_business_scene".localized,
                            businessSceneContent: "info_list_bind_device_scene_content".localized
                        )
                        
                        // 监测部分
                        infoSectionView(
                            title: "info_list_monitoring_title".localized,
                            personalInfo: "info_list_collect_personal_info".localized,
                            personalInfoContent: "info_list_monitoring_info_content".localized,
                            businessScene: "info_list_business_scene".localized,
                            businessSceneContent: "info_list_monitoring_scene_content".localized
                        )
                        
                        // 监测提醒功能
                        infoSectionView(
                            title: "info_list_monitoring_reminder_title".localized,
                            personalInfo: "info_list_collect_personal_info".localized,
                            personalInfoContent: "info_list_monitoring_reminder_info_content".localized,
                            businessScene: "info_list_business_scene".localized,
                            businessSceneContent: "info_list_monitoring_reminder_scene_content".localized
                        )
                    }
                    .background(Color(UIColor(red: 0.06, green: 0.07, blue: 0.1, alpha: 1)))
                    .cornerRadius(25)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                }
            }
        }
        .navigationBarHidden(true)
    }
    
    // 信息部分视图
    private func infoSectionView(title: String, personalInfo: String, personalInfoContent: String, businessScene: String, businessSceneContent: String) -> some View {
        VStack(spacing: 0) {
            // 标题
            HStack {
                Rectangle()
                    .fill(Color.blue)
                    .frame(width: 3, height: 16)
                
                Text(title)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(Color.blue)
                    .padding(.leading, 5)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            
            Divider()
                .background(Color.gray.opacity(0.3))
            
            // 内容表格
            HStack(alignment: .top, spacing: 0) {
                // 左列 - 个人信息
                VStack(alignment: .leading, spacing: 8) {
                    Text(personalInfo)
                        .font(.system(size: 14))
                        .foregroundColor(Color.gray)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                }
                .frame(width: UIScreen.main.bounds.width * 0.3)
                
                // 分割线
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 1)
                
                // 右列 - 个人信息内容
                VStack(alignment: .leading, spacing: 8) {
                    Text(personalInfoContent)
                        .font(.system(size: 14))
                        .foregroundColor(Color.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                }
            }
            
            Divider()
                .background(Color.gray.opacity(0.3))
            
            // 业务场景表格
            HStack(alignment: .top, spacing: 0) {
                // 左列 - 业务场景
                VStack(alignment: .leading, spacing: 8) {
                    Text(businessScene)
                        .font(.system(size: 14))
                        .foregroundColor(Color.gray)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                }
                .frame(width: UIScreen.main.bounds.width * 0.3)
                
                // 分割线
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 1)
                
                // 右列 - 业务场景内容
                VStack(alignment: .leading, spacing: 8) {
                    Text(businessSceneContent)
                        .font(.system(size: 14))
                        .foregroundColor(Color.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                }
            }
            
            Divider()
                .background(Color.gray.opacity(0.3))
        }
    }
}

struct VersionManagementView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            VersionManagementView()
        }
    }
} 
