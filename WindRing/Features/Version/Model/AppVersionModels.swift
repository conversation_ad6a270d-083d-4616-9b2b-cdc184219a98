//
//  AppVersionModels.swift
//  WindRing
//
//  Created by zx on 2025/5/15.
//

import SwiftUI // For AlertItem if you choose that route

// MARK: - API 响应数据模型
struct AppVersionRecordResponse: Codable {
    let code: Int?
    let data: AppVersionData?
    let msg: String?
}

struct AppVersionData: Codable {
    let id: Int
    let operationType: Int? // 0 安卓, 1 iOS
    let title: String?
    let versionName: String? // e.g., "1.2.0"
    let url: String? // App Store link
    var status:Int = 0 //0 待审核 1 已审核
}

// MARK: - 版本检查结果
struct UpdateCheckResult: Identifiable {
    let id = UUID()
    let needsUpdate: Bool
    let isMandatory: Bool
    let serverVersion: String?
    let updateTitle: String?
    let updateMessage: String?
    let updateURL: URL?
    
    func toSwiftUIAlert(openURLAction: @escaping (URL) -> Void) -> Alert {
        let titleText = Text(self.updateTitle ?? "发现新版本")
        let messageText = Text(self.updateMessage ?? "建议您更新到最新版本以获得更好的体验。")

//        if self.isMandatory {
//            if let validUpdateURL = self.updateURL {
//                return Alert(
//                    title: titleText,
//                    message: messageText,
//                    dismissButton: .default(Text("立即更新")) {
//                        openURLAction(validUpdateURL)
//                    }
//                )
//            } else {
//                return Alert(
//                    title: titleText,
//                    message: Text("\(self.updateMessage ?? "")\n\n重要：更新链接缺失，请稍后重试或联系支持。"),
//                    dismissButton: .default(Text("知道了"))
//                )
//            }
//        } else {
            if let validUpdateURL = self.updateURL {
                return Alert(
                    title: titleText,
                    message: messageText,
                    primaryButton: .default(Text("立即更新")) {
                        openURLAction(validUpdateURL)
                    },
                    secondaryButton: .cancel(Text("稍后提醒"))
                )
            } else {
                return Alert(
                    title: titleText,
                    message: messageText,
                    dismissButton: .default(Text("知道了"))
                )
            }
//        }
    }
}

// MARK: - 版本检查错误
enum VersionCheckError: LocalizedError {
    case invalidURL
    case apiError(String)
    case noDataFromServer
    case decodingError(Error)
    case noUpdateInfoForThisApp
    case currentVersionNotFound
    case serverVersionFormatError
    case appStoreURLInvalid
    case missingMandatoryFlag

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无法构造有效的请求URL"
        case .apiError(let message):
            return "接口错误: \(message)"
        case .noDataFromServer:
            return "服务器未返回有效数据"
        case .decodingError(let underlyingError):
            return "解析服务器响应失败: \(underlyingError.localizedDescription)"
        case .noUpdateInfoForThisApp:
            return "未找到适用于本应用的更新信息"
        case .currentVersionNotFound:
            return "无法获取当前应用版本号"
        case .serverVersionFormatError:
            return "服务器返回的版本号格式不正确"
        case .appStoreURLInvalid:
            return "更新链接无效"
        case .missingMandatoryFlag:
            return "API响应中缺少关于是否强制更新的关键信息"
        }
    }
}
