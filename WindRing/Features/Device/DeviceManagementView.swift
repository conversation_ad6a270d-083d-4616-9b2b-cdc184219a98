import SwiftUI
import CRPSmartRing
import Combine

/// 设备管理视图
struct DeviceManagementView: View {
    // MARK: - 属性
    @StateObject private var deviceService = WindRingDeviceService.shared
    @State private var showingPairingSheet = false
    @State private var isUpdatingFirmware = false
    @State private var showResetConfirmation = false
    @State private var showRemoveConfirmation = false
    @State private var showMeasurementSheet = false
    @State private var measurementType = ""
    @State private var measurementValue = ""
    @State private var isMeasuring = false
    
    // 用于存储通知发布者
    private let heartRatePublisher = NotificationCenter.default.publisher(for: .heartRateMeasured)
    private let bloodOxygenPublisher = NotificationCenter.default.publisher(for: .bloodOxygenMeasured)
    private let temperaturePublisher = NotificationCenter.default.publisher(for: .temperatureMeasured)
    private let bloodPressurePublisher = NotificationCenter.default.publisher(for: .bloodPressureMeasured)
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 设备状态卡片
                    deviceStatusCard
                    
                    // 设备信息卡片
                    deviceInfoCard
                    
                    // 设备功能卡片
                    deviceFunctionsCard
                    
                    // 设备管理卡片
                    deviceManagementCard
                    
                    // 底部说明
                    Text("设备问题？请联系客服 400-123-4567")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.top, 8)
                        .padding(.bottom, 32)
                }
                .padding(.vertical)
            }
            .background(Color.moduleBackground.edgesIgnoringSafeArea(.all))
            .navigationTitle("设备管理")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingPairingSheet = true
                    }) {
                        Image(systemName: "plus")
                            .foregroundColor(.blue)
                    }
                }
            }
            .sheet(isPresented: $showingPairingSheet) {
                DevicePairingView()
            }
            .sheet(isPresented: $showMeasurementSheet) {
                // 停止测量
                stopMeasurement()
            } content: {
                measurementView
            }
            .onAppear {
                if deviceService.connectionState.isConnected {
                    deviceService.getBatteryLevel()
                    deviceService.getWearingState()
                    deviceService.getFirmwareVersion()
                }
            }
            // 使用onReceive来处理通知
            .onReceive(heartRatePublisher) { notification in
                if let value = notification.userInfo?["value"] as? Int {
                    self.measurementValue = "\(value) BPM"
                    if self.measurementType == "心率" {
                        self.isMeasuring = false
                    }
                }
            }
            .onReceive(bloodOxygenPublisher) { notification in
                if let value = notification.userInfo?["value"] as? Int {
                    self.measurementValue = "\(value)%"
                    if self.measurementType == "血氧" {
                        self.isMeasuring = false
                    }
                }
            }
            .onReceive(temperaturePublisher) { notification in
                if let value = notification.userInfo?["value"] as? Double {
                    self.measurementValue = String(format: "%.1f°C", value)
                    if self.measurementType == "体温" {
                        self.isMeasuring = false
                    }
                }
            }
            .onReceive(bloodPressurePublisher) { notification in
                if let systolic = notification.userInfo?["systolic"] as? Int,
                   let diastolic = notification.userInfo?["diastolic"] as? Int {
                    self.measurementValue = "\(systolic)/\(diastolic) mmHg"
                    if self.measurementType == "血压" {
                        self.isMeasuring = false
                    }
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
    
    // MARK: - 设备状态卡片
    private var deviceStatusCard: some View {
        VStack(spacing: 20) {
            HStack(spacing: 20) {
                // 设备图像
                DeviceImageView(isActive: deviceService.connectionState.isConnected)
                    .frame(width: 120, height: 120)
                
                VStack(alignment: .leading, spacing: 12) {
                    // 设备名称
                    Text(deviceService.deviceInfo?.localName ?? "未连接设备")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    // 连接状态
                    HStack {
                        Circle()
                            .fill(deviceService.connectionState.isConnected ? Color.green : Color.red)
                            .frame(width: 10, height: 10)
                        
                        Text(deviceService.connectionState.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    // 电池状态
                    if deviceService.connectionState.isConnected {
                        HStack {
                            batteryIcon
                            
                            Text("\(deviceService.batteryLevel)%")
                                .font(.subheadline)
                                .foregroundColor(batteryColor)
                        }
                    }
                }
                
                Spacer()
            }
            
            // 连接按钮
            Button(action: {
                if deviceService.connectionState.isConnected {
                    deviceService.disconnectDevice()
                } else {
                    showingPairingSheet = true
                }
            }) {
                Text(deviceService.connectionState.isConnected ? "断开连接" : "连接设备")
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(deviceService.connectionState.isConnected ? Color.red.opacity(0.8) : Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        .padding(.horizontal)
    }
    
    // MARK: - 设备信息卡片
    private var deviceInfoCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text("设备信息")
                .font(.headline)
                .fontWeight(.semibold)
            
            if deviceService.connectionState.isConnected {
                // 信息列表
                VStack(spacing: 12) {
                    infoRow(title: "设备型号", value: deviceService.deviceInfo?.localName ?? "未知")
                    infoRow(title: "固件版本", value: deviceService.deviceInfo?.firmwareVersion ?? "未知")
                    infoRow(title: "MAC地址", value: deviceService.deviceInfo?.mac ?? "未知")
//                    if let ringInfo = deviceService.deviceInfo?.ringInfo {
//                        infoRow(title: "戒指颜色", value: "\(ringInfo.color)")
//                        infoRow(title: "戒指尺寸", value: "\(ringInfo.size)")
//                    }
                    infoRow(title: "佩戴状态", value: deviceService.wearingState == 1 ? "已佩戴" : "未佩戴")
                }
            } else {
                Text("连接设备后显示详细信息")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .center)
            }
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        .padding(.horizontal)
    }
    
    // MARK: - 设备功能卡片
    private var deviceFunctionsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text("设备功能")
                .font(.headline)
                .fontWeight(.semibold)
            
            // 功能列表
            VStack(spacing: 0) {
                functionRow(icon: "arrow.clockwise", title: "同步数据", action: {
                    // 同步数据
                    deviceService.syncTime()
                    deviceService.getBatteryLevel()
                    deviceService.getWearingState()
                }, disabled: !deviceService.connectionState.isConnected)
                
                Divider().padding(.leading, 50)
                
                functionRow(icon: "heart.fill", title: "心率测量", action: {
                    startMeasurement(type: "心率")
                }, disabled: !deviceService.connectionState.isConnected)
                
                Divider().padding(.leading, 50)
                
                functionRow(icon: "drop.fill", title: "血氧测量", action: {
                    startMeasurement(type: "血氧")
                }, disabled: !deviceService.connectionState.isConnected)
                
                Divider().padding(.leading, 50)
                
                functionRow(icon: "thermometer", title: "体温测量", action: {
                    startMeasurement(type: "体温")
                }, disabled: !deviceService.connectionState.isConnected)
                
                Divider().padding(.leading, 50)
                
                functionRow(icon: "waveform.path.ecg", title: "血压测量", action: {
                    startMeasurement(type: "血压")
                }, disabled: !deviceService.connectionState.isConnected)
            }
            .background(Color.moduleBackground)
            .cornerRadius(10)
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        .padding(.horizontal)
    }
    
    // MARK: - 设备管理卡片
    private var deviceManagementCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text("设备管理")
                .font(.headline)
                .fontWeight(.semibold)
            
            // 管理选项
            VStack(spacing: 0) {
                functionRow(icon: "arrow.up.bin", title: "更新固件", action: {
                    isUpdatingFirmware = true
                }, disabled: !deviceService.connectionState.isConnected)
                
                Divider().padding(.leading, 50)
                
                functionRow(icon: "exclamationmark.triangle", title: "重置设备", action: {
                    showResetConfirmation = true
                }, disabled: !deviceService.connectionState.isConnected)
                
                Divider().padding(.leading, 50)
                
                functionRow(icon: "power", title: "关闭设备", action: {
                    if deviceService.connectionState.isConnected {
                        CRPSmartRingSDK.sharedInstance.shutDown({ (success, error) in
                            if success {
                                deviceService.disconnectDevice()
                            }
                        })
                    }
                }, disabled: !deviceService.connectionState.isConnected)
                
                Divider().padding(.leading, 50)
                
                functionRow(icon: "arrow.counterclockwise.circle", title: "重启设备", action: {
                    if deviceService.connectionState.isConnected {
                        CRPSmartRingSDK.sharedInstance.reboot()
                    }
                }, disabled: !deviceService.connectionState.isConnected)
                
                Divider().padding(.leading, 50)
                
                functionRow(icon: "trash", title: "解除配对", action: {
                    showRemoveConfirmation = true
                }, textColor: .red, disabled: !deviceService.connectionState.isConnected)
            }
            .background(Color.moduleBackground)
            .cornerRadius(10)
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        .padding(.horizontal)
        .alert(isPresented: $isUpdatingFirmware) {
            Alert(
                title: Text("固件更新"),
                message: Text("正在检查固件更新，请确保设备电量充足并保持连接。"),
                dismissButton: .default(Text("确定"))
            )
        }
        .alert("重置设备", isPresented: $showResetConfirmation) {
            Button("取消", role: .cancel) { }
            Button("确定", role: .destructive) {
                if deviceService.connectionState.isConnected {
                    CRPSmartRingSDK.sharedInstance.reset({ (success, error) in
                        // 重置后可能需要重新连接设备
                    })
                }
            }
        } message: {
            Text("确定要将设备恢复出厂设置吗？所有用户数据将被清除。")
        }
        .alert("解除配对", isPresented: $showRemoveConfirmation) {
            Button("取消", role: .cancel) { }
            Button("确定", role: .destructive) {
                deviceService.disconnectDevice()
            }
        } message: {
            Text("确定要解除与此设备的配对吗？")
        }
    }
    
    // MARK: - 测量视图
    private var measurementView: some View {
        VStack(spacing: 20) {
            Text("\(measurementType)测量")
                .font(.title)
                .fontWeight(.bold)
                .padding(.top, 30)
            
            if isMeasuring {
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.2), lineWidth: 15)
                        .frame(width: 200, height: 200)
                    
                    Circle()
                        .trim(from: 0, to: 0.75)
                        .stroke(Color.blue, lineWidth: 15)
                        .frame(width: 200, height: 200)
                        .rotationEffect(Angle(degrees: -90))
                        .animation(Animation.linear(duration: 1).repeatForever(autoreverses: false), value: UUID())
                    
                    VStack {
                        Text("测量中...")
                            .font(.headline)
                        Text("请保持手指静止")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.vertical, 30)
            } else if !measurementValue.isEmpty {
                ZStack {
                    Circle()
                        .fill(Color.green.opacity(0.1))
                        .frame(width: 200, height: 200)
                    
                    VStack {
                        Text(measurementValue)
                            .font(.system(size: 40, weight: .bold))
                        
                        Text(measurementUnit)
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.vertical, 30)
            }
            
            Button(action: {
                if isMeasuring {
                    stopMeasurement()
                } else {
                    showMeasurementSheet = false
                }
            }) {
                Text(isMeasuring ? "停止测量" : "完成")
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(isMeasuring ? Color.red : Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
            }
            .padding(.horizontal)
            
            Spacer()
        }
        .padding()
        .background(Color.moduleBackground)
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - 辅助视图
    
    // 电池图标
    private var batteryIcon: some View {
        Image(systemName: batteryIconName)
            .foregroundColor(batteryColor)
    }
    
    private var batteryIconName: String {
        let level = deviceService.batteryLevel
        if level <= 20 {
            return "battery.0"
        } else if level <= 40 {
            return "battery.25"
        } else if level <= 60 {
            return "battery.50"
        } else if level <= 80 {
            return "battery.75"
        } else {
            return "battery.100"
        }
    }
    
    private var batteryColor: Color {
        let level = deviceService.batteryLevel
        if level <= 20 {
            return .red
        } else if level <= 40 {
            return .orange
        } else {
            return .green
        }
    }
    
    private var measurementUnit: String {
        switch measurementType {
        case "心率":
            return "BPM"
        case "血氧":
            return "%"
        case "体温":
            return "°C"
        case "血压":
            return "mmHg"
        default:
            return ""
        }
    }
    
    // 信息行
    private func infoRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .foregroundColor(.primary)
        }
    }
    
    // 功能行
    private func functionRow(icon: String, title: String, action: @escaping () -> Void, textColor: Color = .primary, disabled: Bool = false) -> some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(.blue)
                    .frame(width: 30, height: 30)
                    .padding(.trailing, 20)
                
                Text(title)
                    .foregroundColor(disabled ? .gray : textColor)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.gray)
            }
            .padding()
        }
        .disabled(disabled)
    }
    
    // MARK: - 辅助方法
    
    // 开始测量
    private func startMeasurement(type: String) {
        measurementType = type
        measurementValue = ""
        isMeasuring = true
        showMeasurementSheet = true
        
        switch type {
        case "心率":
            deviceService.startHeartRateMeasurement()
        case "血氧":
            deviceService.startSpO2Measurement()
        case "体温":
            deviceService.startTemperatureMeasurement()
        case "血压":
            deviceService.startBloodPressureMeasurement()
        default:
            break
        }
    }
    
    // 停止测量
    private func stopMeasurement() {
        isMeasuring = false
        
        switch measurementType {
        case "心率":
            deviceService.stopHeartRateMeasurement()
        case "血氧":
            deviceService.stopSpO2Measurement()
        case "体温":
            deviceService.stopTemperatureMeasurement()
        case "血压":
            deviceService.stopBloodPressureMeasurement()
        default:
            break
        }
    }
}

// MARK: - 预览
struct DeviceManagementView_Previews: PreviewProvider {
    static var previews: some View {
        DeviceManagementView()
    }
} 
