import SwiftUI

/// 设备图像视图 - 在配对过程中显示设备动画
struct DeviceImageView: View {
    // MARK: - 属性
    var isActive: Bool // 指示是否处于活动扫描状态
    @State private var scale: CGFloat = 1.0
    @State private var opacity: CGFloat = 0.3
    
    // MARK: - 主视图
    var body: some View {
        ZStack {
            // 当活动时，显示动画的波纹效果
            if isActive {
                // 创建三个不同大小的波纹圆圈
                ForEach(0..<3) { i in
                    Circle()
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                        .scaleEffect(scale * (1 + CGFloat(i) * 0.2))
                        .opacity(opacity / (1 + CGFloat(i) * 0.5))
                }
                .animation(
                    Animation.easeInOut(duration: 2)
                        .repeatForever(autoreverses: true),
                    value: scale
                )
                .onAppear {
                    scale = 1.2
                    opacity = 0.7
                }
            }
            
            // 中央设备图像
            Image(systemName: "waveform.circle.fill")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 100, height: 100)
                .foregroundColor(isActive ? .blue : .gray)
                .shadow(color: isActive ? .blue.opacity(0.5) : .clear, radius: 5)
                .animation(.easeInOut, value: isActive)
        }
    }
}

struct DeviceImageView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 40) {
            DeviceImageView(isActive: true)
                .frame(width: 200, height: 200)
                .previewDisplayName("激活状态")
            
            DeviceImageView(isActive: false)
                .frame(width: 200, height: 200)
                .previewDisplayName("非激活状态")
        }
        .padding()
        .background(Color(.systemBackground))
        .previewLayout(.sizeThatFits)
    }
} 