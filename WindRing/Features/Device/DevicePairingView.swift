import CRPSmartRing
import SwiftUI
import CoreBluetooth
// 确保可以访问DeviceImageView

/// Device Pairing View - 重新设计为Add Ring页面
struct DevicePairingView: View {
    // MARK: - 状态属性
    @StateObject private var deviceService = WindRingDeviceService.shared
    @StateObject private var bluetoothHelper = BluetoothPermissionHelper.shared
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedDevice: CRPDiscovery?
    @State private var showConnectingAlert = false
    @State private var showConnectionError = false
    @State private var errorMessage = ""
    @State private var scanRetryCount = 0
    @State private var isAutoScanning = false
    
    // MARK: - Main View
    var body: some View {
        ZStack(alignment: .top) {
            // 背景颜色 - 使用纯黑色背景
            Color.black.edgesIgnoringSafeArea(.all)
            
            // 恢复光晕背景效果
            CornerGlowBackground()
            
            VStack(spacing: 0) {
                // 导航栏
                HStack(alignment: .center) {
                    // 返回按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    // 标题居左
                    Text(NSLocalizedString("device_add_ring_title", comment: "Add Ring page title"))
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.white)
                        .padding(.leading, 5)
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .frame(height: 44)
                .padding(.top, 8)
                
                // 主内容
                VStack(alignment: .leading, spacing: 20) {
                    // 提示文字 - 使用PingFang-SC-Heavy字体
                    Text(NSLocalizedString("device_select_prompt", comment: "Prompt to select a ring"))
                        .font(.custom("PingFang-SC-Heavy", size: 13))
                        .foregroundColor(.white)
                        .padding(.top, 20)
                        .padding(.horizontal, 16)
                        .frame(height: 13, alignment: .leading)
                        .padding(.leading, 3.5) // 调整左边距对应UIKit中的x位置
                    
                    // 当设备列表为空且没有在扫描时，显示提示信息
                    if deviceService.discoveredDevices.isEmpty && !deviceService.isScanning {
                        Text(NSLocalizedString("device_not_found_prompt", comment: "Prompt when no ring is found"))
                            .font(.custom("PingFang-SC-Regular", size: 12))
                            .foregroundColor(.gray)
                            .padding(.horizontal, 16)
                            .padding(.top, 5)
                    }
                    
                    // 设备列表 - 始终显示ScrollView以支持下拉刷新
                    deviceListView
                        .transition(.opacity)
                    
                    Spacer()
                }
                .padding(.top, 20)
            }
        }
        .navigationBarHidden(true)
//        .alert(isPresented: $showConnectingAlert) {
//            Alert(
//                title: Text("正在连接"),
//                message: Text("正在连接设备: \(selectedDevice?.localName ?? "")"),
//                dismissButton: .cancel(Text("取消")) {
//                    deviceService.disconnectDevice()
//                }
//            )
//        }
//        .alert(isPresented: $showConnectionError) {
//            Alert(
//                title: Text("连接失败"),
//                message: Text(errorMessage),
//                dismissButton: .default(Text("确定"))
//            )
//        }
        .onAppear {
            // 检查是否有已配对且已连接的设备，如果有则直接退出扫描页面
            if deviceService.connectionState.isConnected {
                print("检测到已连接的设备，自动退出扫描页面")
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    presentationMode.wrappedValue.dismiss()
                }
                return
            }
            
            // 如果没有已连接设备，则检查是否有已配对但未连接的设备
//            let hasAnyPairedDevice = deviceService.hasPairedDevices()
            
//            if hasAnyPairedDevice {
//                // 尝试自动连接已配对设备
//                print("检测到有已配对设备，先尝试自动连接")
//                // 启动扫描以便检测附近的已配对设备
//                isAutoScanning = true
//                startScanning()
//                
//                // 给一定时间扫描已配对设备，如果能连接则会自动退出页面
//                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
//                    // 如果3秒后已连接，connectToDevice方法中会处理退出
//                    // 如果未连接，继续留在扫描页面让用户手动选择
//                }
//            } else {
                // 无已配对设备，正常启动扫描
                isAutoScanning = true
                startScanning()
//            }
        }
        .onDisappear {
            if deviceService.isScanning {
                deviceService.stopScan()
            }
        }
        // 添加设备连接状态变化监听
//        .onReceive(deviceService.$connectionState) { state in
//            // 如果检测到设备已连接，自动退出扫描页面
//            if state.isConnected && !deviceService.isScanning {
//                print("设备已连接，自动退出扫描页面")
//                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
//                    presentationMode.wrappedValue.dismiss()
//                }
//            }
//        }
        .onReceive(deviceService.$isCharging) { _ in
            // 充电状态变化时刷新UI
        }
        // 添加额外的定时检查，确保能捕获设备连接状态
//        .onReceive(Timer.publish(every: 2, on: .main, in: .common).autoconnect()) { _ in
//            if deviceService.connectionState.isConnected {
//                print("定时检查：设备已连接，自动退出扫描页面")
//                presentationMode.wrappedValue.dismiss()
//            }
//        }
    }
    
    // MARK: - Device List View
    private var deviceListView: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 如果正在扫描，显示一个加载指示器
                if deviceService.isScanning {
                    ProgressView()
                        .padding(.vertical, 20)
                }
                
                // 对设备列表进行排序，已配对设备优先显示
                let sortedDevices = deviceService.discoveredDevices.sorted { device1, device2 in
                    // 如果第一个设备已配对而第二个未配对，则第一个设备优先
                    let isPaired1 = WindRingDeviceService.shared.isDevicePaired(device1)
                    let isPaired2 = WindRingDeviceService.shared.isDevicePaired(device2)
                    
                    if isPaired1 && !isPaired2 {
                        return true
                    } else if !isPaired1 && isPaired2 {
                        return false
                    }
                    
                    let hide1 = device1.hide == 1
                    let hide2 = device2.hide == 1
                    if hide1 && !hide2 {
                        return true
                    } else if !hide1 && hide2 {
                        return false
                    }
                    
                    // 如果配对状态相同，优先按设备类型排序（保持相同型号设备在一起）
                    let name1 = device1.localName ?? ""
                    let name2 = device2.localName ?? ""
                    
                    if name1.contains("Da Ring") && !name2.contains("Da Ring") {
                        return true // Da Ring 优先
                    } else if !name1.contains("Da Ring") && name2.contains("Da Ring") {
                        return false
                    }
                    
                    // Herz P1 Ring 设备排序（在 Da Ring 之后，VRing 之前）
                    if name1.contains("Herz P1 Ring") && !name2.contains("Herz P1 Ring") && !name2.contains("Da Ring") {
                        return true
                    } else if !name1.contains("Herz P1 Ring") && name2.contains("Herz P1 Ring") && !name1.contains("Da Ring") {
                        return false
                    }
                    
                    // 如果设备类型相同，则按信号强度排序（RSSI越大信号越强）
                    let rssi1 = device1.RSSI
                    let rssi2 = device2.RSSI
                    return rssi1 > rssi2
                }
                
                ForEach(sortedDevices, id: \.remotePeripheral.identifier) { device in
                    RingDeviceRow(device: device) {
                        
                        deviceService.initDevice(device)
                        selectedDevice = device
                        connectToDevice(device)
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            presentationMode.wrappedValue.dismiss()
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 16)
                }
            }
            .padding(.top, 16)
        }
        .refreshable {
            await refreshDevices()
        }
    }
    
    // MARK: - 重新设计的设备行视图
    struct RingDeviceRow: View {
        let device: CRPDiscovery
        let onConnect: () -> Void
//        @State private var macAddress: String = NSLocalizedString("loading_mac_address", comment: "Loading state for MAC address")
        @State private var isLoadingMac: Bool = false
        @State private var isTouched: Bool = false  // 跟踪轻触状态
        
        var body: some View {
            ZStack {
                // 底层背景与视觉效果
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(
                                colors: [
                                    isTouched ? Color(red: 0.20, green: 0.24, blue: 0.30) : Color(red: 0.16, green: 0.19, blue: 0.25),
                                    isTouched ? Color(red: 0.12, green: 0.13, blue: 0.18) : Color(red: 0.08, green: 0.09, blue: 0.13)
                                ]
                            ),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isTouched ? Color.blue.opacity(0.4) : Color.clear, lineWidth: 1.5)
                    )
                
                // 内容层
                HStack(alignment: .center) {
                    HStack(spacing: 10) {
                        // 设备图片
                        Image("ring_device")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 70, height: 70)

                        // ✅ 添加充电按钮
                        if device.hide == 1{
                            ChargingStatusView(batteryLevel: 100, isCharging: true)
                        }
                    }
                    .padding(.leading, 15)
                    
                    Spacer()
                    
                    // 右侧信息区域 - 右侧对齐
                    VStack(alignment: .trailing, spacing: 6) {
                        // 设备名称
                        Text(device.localName?.isEmpty ?? true ? NSLocalizedString("default_ring_name", comment: "Default ring name") : device.localName ?? NSLocalizedString("default_ring_name", comment: "Default ring name"))
                            .font(.system(size: 22, weight: .medium))
                            .foregroundColor(.white)
                        
                        // MAC地址信息 - 显示真实MAC地址
                        HStack(spacing: 4) {
                            if isLoadingMac {
                                ProgressView()
                                    .scaleEffect(0.7)
                                    .frame(width: 10, height: 10)
                            }
                            
                            
                            Text(device.mac ?? "--")
                                .font(.system(size: 15))
                                .foregroundColor(.gray)
                        }
                        
                        // 显示已配对状态 - 放在MAC地址下方并右对齐
                        if WindRingDeviceService.shared.isDevicePaired(device) {
                            Text("系统蓝牙已配对")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.green)
                                .padding(.vertical, 2)
                        }
                    }
                    .padding(.trailing, 18)
                }
                .padding(.vertical, 15)
            }
            .scaleEffect(isTouched ? 0.99 : 1.0)
            .contentShape(Rectangle()) // 确保整个区域都可触摸
            // 使用手势识别而不是Button，以获得更灵敏的反应
            .onTapGesture {
                // 轻触反馈
                let generator = UIImpactFeedbackGenerator(style: .light)
                generator.impactOccurred()
                
                // 短暂的视觉反馈
                withAnimation(.easeInOut(duration: 0.08)) {
                    isTouched = true
                }
                
                // 轻微延迟执行操作，确保用户能看到视觉反馈
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(.easeInOut(duration: 0.08)) {
                        isTouched = false
                    }
                    onConnect()
                }
            }
            .animation(.easeInOut(duration: 0.08), value: isTouched)
            .onAppear {
                // 调试输出
                print("设备扫描信息 - 名称: \(device.localName ?? "未知")")
                print("设备扫描信息 - MAC属性: \(device.mac ?? "为空")")
                print("设备扫描信息 - UUID: \(device.remotePeripheral.identifier)")
                
                // 获取设备MAC地址
//                loadMacAddress()
            }
        }
        
        // 获取并加载MAC地址
        private func loadMacAddress() {
            // 获取设备的UUID
            let uuid = device.remotePeripheral.identifier.uuidString
            
            // 检查设备是否已与系统连接
            let isPaired = WindRingDeviceService.shared.isDevicePaired(device)
            let isSystemConnected = device.remotePeripheral.state == .connected
            
            // 1. 首先检查这个特定UUID对应的MAC地址缓存
//            if let uuidMac = WindRingDeviceService.shared.getDeviceMacByUUID(uuid: uuid) {
//                self.macAddress = uuidMac
//                return
//            }
            
            // 2. 然后从device.mac获取（如果可用且有效）
//            if let mac = device.mac, !mac.isEmpty, mac != "auto_connected_device" {
//                self.macAddress = mac
//                
//                // 同时保存到UUID-MAC映射，确保下次能直接通过UUID找到
//                WindRingDeviceService.shared.saveDeviceMacByUUID(uuid: uuid, mac: mac)
//                return
//            }
            
            // 3. 如果是已配对设备或已与系统连接，尝试主动获取MAC地址
            if isPaired || isSystemConnected {
                isLoadingMac = true
                // 使用SDK方法获取MAC地址
                WindRingDeviceService.shared.getMacAddressForSystemConnectedDevice(peripheral: device.remotePeripheral) { mac in
                    DispatchQueue.main.async {
                        self.isLoadingMac = false
                        if !mac.isEmpty {
//                            self.macAddress = mac
                        } else {
//                            self.macAddress = NSLocalizedString("mac_address_not_found", comment: "MAC address not found")
                        }
                    }
                }
            } else {
                // 对于未连接设备，使用一个基于UUID的唯一MAC地址
                let generatedMac = WindRingDeviceService.shared.generateMACFromUUID(uuid: uuid)
//                self.macAddress = generatedMac
            }
        }
        
        // 获取显示的MAC地址
//        private func getMacAddressDisplay() -> String {
//            return macAddress
//        }
    }
    
    // MARK: - Scanning State View
    private var scanningStateView: some View {
        VStack(spacing: 12) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.5)
                .padding()
            
            Text(NSLocalizedString("device_searching_prompt", comment: "Searching for devices"))
                .font(.custom("PingFang-SC-Regular", size: 14))
                .foregroundColor(.gray)
        }
        .frame(height: 150)
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - Bluetooth Permission View
    private var bluetoothPermissionView: some View {
        VStack(spacing: 16) {
            Text(NSLocalizedString("bluetooth_permission_required_title", comment: "Bluetooth permission required title"))
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
            
            Text(NSLocalizedString("bluetooth_permission_required_message", comment: "Bluetooth permission required message"))
                .font(.system(size: 16))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button(action: {
                if let url = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(url)
                }
            }) {
                Text(NSLocalizedString("go_to_settings", comment: "Go to settings button"))
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.blue)
                    .cornerRadius(12)
            }
            .padding(.horizontal, 16)
        }
        .padding()
    }
    
    // MARK: - Helper Methods
    private func startScanning() {
        scanRetryCount += 1
        deviceService.startScan()
    }
    
    private func refreshDevices() async {
        print("开始下拉刷新...")
        await withCheckedContinuation { continuation in
            deviceService.startScan(duration: 10) {
                print("下拉刷新完成")
                continuation.resume()
            }
        }
    }
    
    private func connectToDevice(_ device: CRPDiscovery) {
        // 检查是否为已配对设备
        let isPaired = WindRingDeviceService.shared.isDevicePaired(device)
        
        // 如果是已配对设备，可能已自动连接，直接返回上一页
        if isPaired && deviceService.connectionState.isConnected {
            presentationMode.wrappedValue.dismiss()
            return
        }
        
        showConnectingAlert = true
        deviceService.initializeDeviceInfo(discovery: device)
        deviceService.connectDevice(discovery: device)
        
        // 为已配对设备提供更长的连接时间
        let connectionTimeout = isPaired ? 4.0 : 3.0
        
        DispatchQueue.main.asyncAfter(deadline: .now() + connectionTimeout) {
            showConnectingAlert = false
            
            // 增加额外验证，防止假性的连接失败提示
            // 即使上面的超时检查触发，也再次确认连接状态是否已经变为已连接
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // 最终确认连接状态
                if deviceService.connectionState.isConnected {
                    // 连接成功，返回上一页
                    presentationMode.wrappedValue.dismiss()
                    return
                }
                
                // 如果是已配对设备但未连接，再次检查系统蓝牙连接状态
                if isPaired {
                    // 强制检查系统蓝牙连接状态
                    deviceService.checkSystemBluetoothConnection()
                    
                    // 给一点时间让系统检查连接状态
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                        // 再次确认连接状态
                        if deviceService.connectionState.isConnected {
                            presentationMode.wrappedValue.dismiss()
                        } else {
                            self.errorMessage = isPaired ?
                                NSLocalizedString("error_connect_paired_device", comment: "Error connecting to paired device") :
                                NSLocalizedString("error_connect_failed", comment: "Error connecting to device")
                            self.showConnectionError = true
                        }
                    }
                }
                // 未配对设备直接显示连接失败
                else {
                    errorMessage = NSLocalizedString("error_connect_failed_with_suggestion", comment: "Error connecting to device with suggestion")
                    showConnectionError = true
                }
            }
        }
    }
}

struct ChargingStatusView: View {
    let batteryLevel: Int       // 电量 0~100
    let isCharging: Bool        // 是否正在充电

    var backgroundImageName: String {
        switch batteryLevel {
        case 0..<20: return "battery_level_warning_red"
        case 20..<80: return "battery_level_warning_orange"
        default: return "battery_level_warning_green"
        }
    }

    var fillColor: Color {
        switch batteryLevel {
        case 0..<20: return .red
        case 20..<80: return .orange
        default: return .green
        }
    }

    var body: some View {
        if let uiImage = UIImage(named: backgroundImageName) {
            let size = uiImage.size
            let aspectRatio = size.width / size.height
            let displayHeight: CGFloat = 15
            let displayWidth = displayHeight * aspectRatio

            contentView
                .frame(width: displayWidth, height: displayHeight)
        } else {
            // fallback，如果图片加载失败
            contentView
                .frame(width: 40, height: 15)
        }
    }

    private var contentView: some View {
        GeometryReader { geo in
            let inset: CGFloat = 2
            let fillHeight = geo.size.height - inset * 2
            let fillWidth = max(0, (geo.size.width - inset * 2) * CGFloat(batteryLevel) / 100)

            ZStack {
                // 背景图居中铺满
                Image(backgroundImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: geo.size.width, height: geo.size.height)

                // 电量填充层（从左往右填充）
                HStack(spacing: 0) {
                    Rectangle()
                        .fill(fillColor.opacity(0.7))
                        .frame(width: fillWidth-2, height: fillHeight)
                        .cornerRadius(2)
                    Spacer(minLength: 0)
                }.padding(inset)

                

                // ⚡️ 正在充电图标（居中放置）
                if isCharging {
                    Image("icon_charging")
                        .resizable()
                        .frame(width: 12, height: 12)
                        .padding(4)
                }else{
                    // 百分比文字
                    Text("\(batteryLevel)%")
                        .font(.system(size: 8, weight: .bold))
                        .foregroundColor(.white)
                        .shadow(radius: 1)
                }
            }
        }
    }
}

struct DevicePairingView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            DevicePairingView()
        }
    }
}

// 增加String扩展，用于分割字符串
extension String {
    func chunks(ofCount count: Int) -> [String] {
        var result = [String]()
        var currentIndex = self.startIndex
        
        while currentIndex < self.endIndex {
            let endIndex = self.index(currentIndex, offsetBy: count, limitedBy: self.endIndex) ?? self.endIndex
            result.append(String(self[currentIndex..<endIndex]))
            currentIndex = endIndex
        }
        
        return result
    }
}

