import SwiftUI
import CRPSmartRing

struct DeviceDetailView: View {
    @StateObject private var deviceService = WindRingDeviceService.shared
    @State private var isEditingNickname = false
    @State private var nickname = ""
    
    var body: some View {
        Form {
            // 基本信息
            Section {
                if let info = deviceService.deviceInfo {
                    infoRow(title: "设备名称", value: info.localName ?? "")
                    
                    // MAC地址行，添加刷新按钮
                    HStack {
                        Text("MAC地址")
                        Spacer()
                        Text(info.mac ?? "")
                            .foregroundColor(.secondary)
                        Button(action: {
                            // 刷新MAC地址
                            deviceService.getMacAddress { _ in
                                // MAC地址已在deviceService中更新，视图将自动刷新
                            }
                        }) {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 14))
                                .foregroundColor(.blue)
                        }
                    }
                    
                    infoRow(title: "固件版本", value: info.firmwareVersion ?? "")
                    infoRow(title: "固件类型", value: deviceService.firmwareType)
                    infoRow(title: "睡眠算法", value: getSleepAlgorithmName(for: deviceService.firmwareType))
                }
                
                // 昵称
                if isEditingNickname {
                    TextField("输入设备昵称", text: $nickname)
                        .onSubmit {
                            saveNickname()
                        }
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    Button("保存") {
                        saveNickname()
                    }
                } else {
                    HStack {
                        Text("我的昵称")
                        Spacer()
                        Text(deviceService.userDeviceNickname ?? "")
                            .foregroundColor(.secondary)
                        Button(action: {
                            nickname = deviceService.userDeviceNickname ?? ""
                            isEditingNickname = true
                        }) {
                            Image(systemName: "pencil")
                                .foregroundColor(.blue)
                        }
                    }
                }
                
                infoRow(title: "电池电量", value: "\(deviceService.batteryLevel)%")
                
                if let ringInfo = deviceService.deviceInfo?.info {
                    infoRow(title: "戒指尺寸", value: "\(ringInfo.size)")
                    infoRow(title: "戒指颜色", value: colorName(for: ringInfo.color))
                    infoRow(title: "戒指类型", value: typeName(for: ringInfo.type))
                }
                
                infoRow(title: "佩戴状态", value: deviceService.wearingState == 1 ? "已佩戴" : "未佩戴")
            } header: {
                Text("基本信息")
            }
            
            // 连接设置
            Section {
                Toggle(isOn: Binding<Bool>(
                    get: { deviceService.autoReconnect },
                    set: { deviceService.setAutoReconnect($0) }
                )) {
                    HStack {
                        Image(systemName: "arrow.triangle.2.circlepath")
                            .foregroundColor(.blue)
                        Text("自动重连")
                    }
                }
                
                Text("开启后，应用会在蓝牙恢复可用时自动尝试连接上次使用的设备")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } header: {
                Text("连接设置")
            }
            
            // 设备操作
            Section {
                Button(action: { deviceService.syncTime() }) {
                    HStack {
                        Image(systemName: "clock")
                        Text("同步时间")
                    }
                }
                
                Button(action: { deviceService.getBatteryLevel() }) {
                    HStack {
                        Image(systemName: "battery.100")
                        Text("刷新电量")
                    }
                }
                
                NavigationLink(destination: GoMoreSupportView()) {
                    HStack {
                        Image(systemName: "checkmark.shield")
                        Text("GoMore算法支持")
                    }
                }
                
                Button(action: { deviceService.disconnectDevice() }) {
                    HStack {
                        Image(systemName: "xmark.circle")
                        Text("断开连接")
                    }
                    .foregroundColor(.red)
                }
            } header: {
                Text("设备操作")
            }
            
            // 说明部分
            Section {
                Text("您最近连接过的设备会被标记为\"\(deviceService.userDeviceNickname)\"，并且在扫描列表中优先显示。")
                    .font(.footnote)
                    .foregroundColor(.secondary)
            } footer: {
                Text("设置昵称可以帮助您在多个设备中识别出自己的设备")
            }
        }
        .navigationTitle("设备详情")
        .onAppear {
            // 刷新设备信息
            if deviceService.connectionState.isConnected {
                deviceService.getBatteryLevel()
                deviceService.getWearingState()
            }
        }
    }
    
    private func saveNickname() {
        if !nickname.isEmpty {
            deviceService.setDeviceNickname(nickname)
        }
        isEditingNickname = false
    }
    
    private func infoRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
            Spacer()
            Text(value)
                .foregroundColor(.secondary)
        }
    }
    
    private func colorName(for colorCode: Int) -> String {
        switch colorCode {
        case 0: return "黑色"
        case 1: return "银色"
        case 2: return "金色"
        default: return "未知"
        }
    }
    
    private func typeName(for typeCode: Int) -> String {
        switch typeCode {
        case 0: return "标准版"
        case 1: return "专业版"
        case 2: return "运动版"
        default: return "未知"
        }
    }
    
    private func getSleepAlgorithmName(for firmwareType: String) -> String {
        if firmwareType == "ION" {
            return "GoMore高级睡眠算法"
        } else if firmwareType == "R033" {
            return "基础睡眠算法"
        } else {
            return "基础睡眠算法(默认)"
        }
    }
}

struct DeviceDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            DeviceDetailView()
        }
    }
} 
