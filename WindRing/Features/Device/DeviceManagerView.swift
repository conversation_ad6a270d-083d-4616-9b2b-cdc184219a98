import SwiftUI
#if os(iOS)
import CRPSmartRing
#endif

/// 设备管理视图 - 显示连接状态和设备信息
struct DeviceManagerView: View {
    // MARK: - 状态和环境
    @StateObject private var deviceService = WindRingDeviceService.shared
    @State private var showPairingView = false
    @State private var showDeviceDetailView = false
    @AppStorage("isLoggedIn") private var isLoggedIn: Bool = false
    
    // 心率数据同步状态
    @State private var isSyncingHeartRate = false
    @State private var lastSyncTime: Date? = nil
    @State private var syncResult: SyncResult? = nil
    @State private var showSyncResult = false
    
    // 心率数据上传状态
    @State private var isUploadingHeartRate = false
    @State private var lastUploadTime: Date? = nil
    
    // 添加初始化方法，确保不检查登录状态
    init() {
        // 预先检查设备连接状态
        let isConnected = WindRingDeviceService.shared.connectionState.isConnected
        if !isConnected {
            print("警告：设备未连接")
        } else {
            print("设备已连接，准备显示设备管理页面")
        }
    }
    
    // MARK: - 主视图
    var body: some View {
        List {
            // 提示消息 - 仅在未登录但设备已连接时显示
            if !isLoggedIn && deviceService.connectionState.isConnected {
                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("设备connected".localized)
                            .font(.headline)
                            .foregroundColor(.green)
                        
                        Text("您可以在不登录的情况下管理已连接的设备")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 8)
                }
            }
            
            // 设备状态
            Section {
                HStack(spacing: 16) {
                    // 设备图片
                    Image(systemName: "ring")
                        .font(.largeTitle)
                        .foregroundColor(deviceService.connectionState.isConnected ? .green : .gray)
                        .frame(width: 60, height: 60)
                        .background(Color.moduleBackground)
                        .cornerRadius(12)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        if deviceService.connectionState.isConnected {
                            Text(deviceService.deviceInfo?.localName ?? "未知设备")
                                .font(.headline)
                        } else {
                            Text(deviceService.deviceInfo?.localName ?? "未连接设备")
                                .font(.headline)
                        }
                        
                        HStack {
                            // 连接状态指示器
                            Circle()
                                .fill(deviceService.connectionState.isConnected ? Color.green : Color.gray)
                                .frame(width: 8, height: 8)
                            
                            // 连接状态文字描述
                            Text(deviceService.connectionState.description)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        if deviceService.connectionState.isConnected {
                            // 电池状态
                            HStack {
                                Image(systemName: getBatteryIcon())
                                    .foregroundColor(getBatteryColor())
                                Text("电量: \(deviceService.batteryLevel)%")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    
                    Spacer()
                    
                    // 连接或配对按钮
                    if deviceService.connectionState.isConnected {
                        NavigationLink(destination: DeviceDetailView()) {
                            Text("详情")
                                .font(.subheadline)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.blue.opacity(0.2))
                                .foregroundColor(.blue)
                                .cornerRadius(8)
                        }
                    } else {
                        Button(action: {
                            showPairingView = true
                        }) {
                            Text("配对")
                                .font(.subheadline)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.blue.opacity(0.2))
                                .foregroundColor(.blue)
                                .cornerRadius(8)
                        }
                    }
                }
                .padding(.vertical, 8)
            } header: {
                Text("设备状态")
            }
            
            // 设备信息
            if deviceService.connectionState.isConnected, let info = deviceService.deviceInfo {
                Section {
                    InfoRow(title: "MAC地址", value: info.mac ?? "")
                    InfoRow(title: "固件版本", value: info.firmwareVersion ?? "")
                    
                    if let ringInfo = info.info {
                        InfoRow(title: "戒指尺寸", value: "\(ringInfo.size)")
                        InfoRow(title: "戒指颜色", value: getRingColorName(ringInfo.color))
                        InfoRow(title: "戒指类型", value: getRingTypeName(ringInfo.type))
                    }
                    
                    InfoRow(title: "佩戴状态", value: deviceService.wearingState == 1 ? "已佩戴" : "未佩戴")
                    
                    // 添加显示当前睡眠算法类型
                    InfoRow(title: "睡眠算法", value: deviceService.sleepAlgorithmType.description)
                } header: {
                    Text("设备信息")
                }
                
                // 设备功能
                Section {
                    Button(action: {
                        deviceService.syncTime()
                    }) {
                        FeatureRow(icon: "arrow.triangle.2.circlepath", title: "同步时间", color: .blue)
                    }
                    
                    Button(action: {
                        deviceService.getBatteryLevel()
                    }) {
                        FeatureRow(icon: "battery.100", title: "刷新电量", color: .green)
                    }
                    
                    // 添加同步心率数据按钮
                    Button(action: {
                        syncHeartRateData()
                    }) {
                        HStack {
                            FeatureRow(icon: "heart.fill", title: "同步心率数据", color: .red)
                            
                            if isSyncingHeartRate {
                                Spacer()
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .scaleEffect(0.8)
                            }
                        }
                    }
                    .disabled(isSyncingHeartRate)
                    
                    // 添加上传心率数据按钮
                    Button(action: {
                        uploadHeartRateData()
                    }) {
                        HStack {
                            FeatureRow(icon: "arrow.up.heart.fill", title: "上传心率数据", color: .purple)
                            
                            if isUploadingHeartRate {
                                Spacer()
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .scaleEffect(0.8)
                            }
                        }
                    }
                    .disabled(isUploadingHeartRate || !isLoggedIn)
                    
                    // 显示上次上传时间
                    if let lastUpload = lastUploadTime {
                        HStack {
                            Image(systemName: "arrow.up.doc")
                                .foregroundColor(.secondary)
                                .frame(width: 24)
                            Text("上次上传")
                                .foregroundColor(.primary)
                            Spacer()
                            Text(timeAgoString(from: lastUpload))
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // 显示上次同步时间
                    if let lastSync = lastSyncTime {
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(.secondary)
                                .frame(width: 24)
                            Text("上次同步")
                                .foregroundColor(.primary)
                            Spacer()
                            Text(timeAgoString(from: lastSync))
                                .foregroundColor(.secondary)
                        }
                    }
                } header: {
                    Text("设备功能")
                }
                
                // 设备设置
                Section {
                    Toggle(isOn: Binding<Bool>(
                        get: { deviceService.autoReconnect },
                        set: { deviceService.setAutoReconnect($0) }
                    )) {
                        HStack {
                            Image(systemName: "arrow.triangle.2.circlepath")
                                .font(.system(size: 16))
                                .foregroundColor(.blue)
                                .frame(width: 22, height: 22)
                            Text("自动重连")
                        }
                    }
                } header: {
                    Text("设备设置")
                } footer: {
                    Text("开启后，应用会在蓝牙恢复可用时自动尝试连接上次使用的设备")
                }
            }
        }
        .navigationTitle("设备管理")
        .sheet(isPresented: $showPairingView) {
            NavigationView {
                DevicePairingView()
                    .navigationBarTitleDisplayMode(.inline)
            }
        }
        .onAppear {
            if deviceService.connectionState.isConnected {
                deviceService.getBatteryLevel()
                deviceService.getWearingState()
            }
            // 检查上次同步时间
            if let timestamp = UserDefaults.standard.object(forKey: "lastHRSyncTime") as? Date {
                lastSyncTime = timestamp
            }
            
            // 检查上次上传时间
            if let timestamp = UserDefaults.standard.object(forKey: "lastHRUploadTime") as? Date {
                lastUploadTime = timestamp
            }
        }
        .refreshable {
            if deviceService.connectionState.isConnected {
                deviceService.getBatteryLevel()
                deviceService.getWearingState()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("DeviceConnected"))) { _ in
            // 设备连接成功后打开设备详情页面
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                showDeviceDetailView = true
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .sleepAlgorithmChanged)) { _ in
            // 睡眠算法改变时刷新界面
            print("收到睡眠算法变化通知，当前算法：\(deviceService.sleepAlgorithmType.description)")
        }
        .onReceive(NotificationCenter.default.publisher(for: .heartRateDataUploaded)) { notification in
            // 心率数据上传完成后更新UI状态
            DispatchQueue.main.async {
                if isSyncingHeartRate {
                    isSyncingHeartRate = false
                    lastSyncTime = Date()
                    
                    // 保存同步时间
                    UserDefaults.standard.set(lastSyncTime, forKey: "lastHRSyncTime")
                    
                    // 如果是自动上传完成，更新结果
                    let count = notification.userInfo?["count"] as? Int ?? 0
                    let success = notification.userInfo?["success"] as? Bool ?? false
                    
                    syncResult = SyncResult(success: success, count: count)
                    showSyncResult = true
                    
                    // 3秒后自动隐藏结果
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        showSyncResult = false
                    }
                }
            }
        }
        .alert(isPresented: $showSyncResult, content: {
            if let result = syncResult {
                return Alert(
                    title: Text(result.success ? "同步成功" : "同步失败"),
                    message: Text(result.message ?? (result.success ? "成功上传\(result.count)条心率数据" : "数据上传失败，请稍后重试")),
                    dismissButton: .default(Text("确定"))
                )
            } else {
                return Alert(title: Text("同步完成"))
            }
        })
        .background(
            NavigationLink(destination: DeviceDetailView(), isActive: $showDeviceDetailView) {
                EmptyView()
            }
        )
    }
    
    // MARK: - 辅助方法
    /// 同步心率数据
    private func syncHeartRateData() {
        guard !isSyncingHeartRate else { return }
        
        isSyncingHeartRate = true
        
        HeartRateUploadService.shared.fetchAndSaveHeartRateHistory { count, error in
            DispatchQueue.main.async {
                if error != nil {
                    // 如果获取数据失败，直接结束同步状态
                    self.isSyncingHeartRate = false
                    self.syncResult = SyncResult(success: false, count: 0)
                    self.showSyncResult = true
                    
                    // 3秒后自动隐藏结果
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        self.showSyncResult = false
                    }
                } else if count == 0 {
                    // 如果没有新数据，也直接结束同步状态
                    self.isSyncingHeartRate = false
                    self.lastSyncTime = Date()
                    self.syncResult = SyncResult(success: true, count: 0)
                    self.showSyncResult = true
                    
                    // 3秒后自动隐藏结果
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        self.showSyncResult = false
                    }
                }
                // 如果有数据获取成功，保持同步状态，等待上传完成通知
            }
        }
    }
    
    /// 手动上传心率数据
    private func uploadHeartRateData() {
        guard !isUploadingHeartRate && isLoggedIn else { 
            if !isLoggedIn {
                // 显示未登录提示
                syncResult = SyncResult(success: false, count: 0, message: "请先登录才能上传数据")
                showSyncResult = true
                
                // 3秒后自动隐藏结果
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    self.showSyncResult = false
                }
            }
            return 
        }
        
        isUploadingHeartRate = true
        
        HeartRateUploadService.shared.uploadPendingHeartRateData { count, error in
            DispatchQueue.main.async {
                self.isUploadingHeartRate = false
                self.lastUploadTime = Date()
                
                // 保存上传时间
                UserDefaults.standard.set(self.lastUploadTime, forKey: "lastHRUploadTime")
                
                if let error = error {
                    print("上传心率数据失败: \(error.localizedDescription)")
                    self.syncResult = SyncResult(success: false, count: 0, message: error.localizedDescription)
                } else {
                    print("成功上传\(count)条心率数据")
                    self.syncResult = SyncResult(success: true, count: count)
                }
                
                self.showSyncResult = true
                
                // 3秒后自动隐藏结果
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    self.showSyncResult = false
                }
            }
        }
    }
    
    /// 将日期转换为"多久前"的文本
    private func timeAgoString(from date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    /// 根据电量获取电池图标
    private func getBatteryIcon() -> String {
        let level = deviceService.batteryLevel
        if level <= 10 {
            return "battery.0"
        } else if level <= 25 {
            return "battery.25"
        } else if level <= 50 {
            return "battery.50"
        } else if level <= 75 {
            return "battery.75"
        } else {
            return "battery.100"
        }
    }
    
    /// 根据电量获取电池颜色
    private func getBatteryColor() -> Color {
        let level = deviceService.batteryLevel
        if level <= 20 {
            return .red
        } else if level <= 40 {
            return .orange
        } else {
            return .green
        }
    }
    
    /// 获取戒指颜色名称
    private func getRingColorName(_ colorCode: Int) -> String {
        switch colorCode {
        case 0: return "黑色"
        case 1: return "银色"
        case 2: return "金色"
        default: return "未知"
        }
    }
    
    /// 获取戒指类型名称
    private func getRingTypeName(_ typeCode: Int) -> String {
        switch typeCode {
        case 0: return "标准版"
        case 1: return "专业版"
        case 2: return "运动版"
        default: return "未知"
        }
    }
}

// MARK: - 辅助视图
/// 信息行视图
struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .foregroundColor(.primary)
        }
    }
}

/// 功能行视图
struct FeatureRow: View {
    let icon: String
    let title: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 24)
            Text(title)
                .foregroundColor(.primary)
            Spacer()
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - 辅助类型
/// 同步结果
struct SyncResult {
    let success: Bool
    let count: Int
    let message: String?
    
    init(success: Bool, count: Int, message: String? = nil) {
        self.success = success
        self.count = count
        self.message = message
    }
}

struct DeviceManagerView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            DeviceManagerView()
        }
    }
} 
