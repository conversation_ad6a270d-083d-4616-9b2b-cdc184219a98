import SwiftUI
import CRPSmartRing

/// GoMore算法支持检查和设置视图
struct GoMoreSupportView: View {
    // MARK: - 状态和环境变量
    @StateObject private var deviceService = WindRingDeviceService.shared
    @StateObject private var goMoreService = GoMoreService.shared
    @State private var goMoreKey = ""
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // 当前设备状态
                GroupBox(label: Text("设备状态")) {
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Label(
                                deviceService.connectionState.isConnected ? "connected".localized : "disconnected".localized,
                                systemImage: deviceService.connectionState.isConnected ? "checkmark.circle.fill" : "xmark.circle.fill"
                            )
                            .foregroundColor(deviceService.connectionState.isConnected ? .green : .red)
                            
                            Spacer()
                            
                            if !deviceService.connectionState.isConnected {
                                Button("连接设备") {
                                    // 导航到设备连接界面的逻辑
                                }
                                .buttonStyle(.bordered)
                            }
                        }
                        
                        if deviceService.connectionState.isConnected,
                           let deviceInfo = deviceService.deviceInfo {
                            Divider()
                            
                            HStack {
                                Text("设备名称")
                                Spacer()
                                Text(deviceInfo.localName ?? "")
                                    .foregroundColor(.secondary)
                            }
                            
                            HStack {
                                Text("固件版本")
                                Spacer()
                                Text(deviceInfo.firmwareVersion ?? "")
                                    .foregroundColor(.secondary)
                            }
                            
                            HStack {
                                Text("电池电量")
                                Spacer()
                                Text("\(deviceService.batteryLevel)%")
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(8)
                }
                
                // GoMore支持状态
                GroupBox(label: Text("GoMore算法支持")) {
                    VStack(alignment: .leading, spacing: 12) {
                        if goMoreService.isLoading {
                            HStack {
                                ProgressView()
                                    .padding(.trailing, 8)
                                Text("正在检查GoMore算法支持...")
                            }
                            .padding(8)
                        } else {
                            HStack {
                                Label(
                                    goMoreService.isGoMoreSupported ? "支持GoMore算法" : "不支持GoMore算法",
                                    systemImage: goMoreService.isGoMoreSupported ? "checkmark.circle.fill" : "xmark.circle.fill"
                                )
                                .foregroundColor(goMoreService.isGoMoreSupported ? .green : .red)
                                
                                Spacer()
                                
                                if deviceService.connectionState.isConnected {
                                    Button("检查") {
                                        goMoreService.checkGoMoreSupport()
                                    }
                                    .buttonStyle(.bordered)
                                }
                            }
                            
                            Divider()
                            
                            // 显示算法类型（无论是否支持）
                            HStack {
                                Text("算法类型")
                                Spacer()
                                Text("\(goMoreService.algorithmType)")
                                    .foregroundColor(.secondary)
                            }
                            
                            // 设备信息额外显示
                            if deviceService.connectionState.isConnected,
                               let deviceInfo = deviceService.deviceInfo {
                                
                                Divider()
                                Text("设备详细信息")
                                    .font(.headline)
                                    .padding(.top, 4)
                                
                                HStack {
                                    Text("产品名称")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(deviceInfo.localName ?? "")
                                }
                                
                                HStack {
                                    Text("固件版本")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(deviceInfo.firmwareVersion ?? "")
                                }
                                
                                HStack {
                                    Text("固件类型")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(deviceService.firmwareType)
                                        .foregroundColor(.secondary)
                                }
                                
                                HStack {
                                    Text("硬件版本")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text("未知") //deviceInfo.hardwareVersion ??
                                }
                            }
                            
                            if !goMoreService.errorMessage.isEmpty {
                                Divider()
                                Text(goMoreService.errorMessage)
                                    .foregroundColor(.red)
                            }
                        }
                    }
                    .padding(8)
                }
                
                // 调试信息区域
                GroupBox(label: Text("调试信息")) {
                    VStack(alignment: .leading, spacing: 12) {
                        if deviceService.connectionState.isConnected,
                           let discovery = deviceService.currentDiscovery {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("原始设备信息")
                                    .font(.headline)
                                    .padding(.bottom, 4)
                                
                                HStack {
                                    Text("本地名称")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(discovery.localName ?? "未知")
                                }
                                
                                HStack {
                                    Text("原始MAC")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(discovery.mac ?? "未知")
                                }
                                
                                HStack {
                                    Text("广播RSSI")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text("\(discovery.RSSI)")
                                }
                                
                                if let advData = discovery.advertisementData as? [String: Any] {
                                    Divider()
                                    Text("广播数据")
                                        .font(.headline)
                                        .padding(.top, 8)
                                        .padding(.bottom, 4)
                                    
                                    ForEach(Array(advData.keys.sorted()), id: \.self) { key in
                                        HStack {
                                            Text(key)
                                                .font(.footnote)
                                                .foregroundColor(.secondary)
                                            Spacer()
                                            Text(String(describing: advData[key] ?? ""))
                                                .font(.footnote)
                                                .foregroundColor(.primary)
                                                .multilineTextAlignment(.trailing)
                                        }
                                    }
                                }
                            }
                            .padding(.vertical, 4)
                        } else {
                            Text("未连接设备，无法显示调试信息")
                                .foregroundColor(.secondary)
                                .padding(8)
                        }
                    }
                    .padding(8)
                }
                
                // GoMore工具区域(如果支持GoMore算法)
                if goMoreService.isGoMoreSupported {
                    // GoMore密钥区域
                    GroupBox(label: Text("GoMore密钥")) {
                        VStack(alignment: .leading, spacing: 12) {
                            if goMoreService.isLoading {
                                HStack {
                                    ProgressView()
                                        .padding(.trailing, 8)
                                    Text("检查密钥匹配状态...")
                                }
                            } else {
                                HStack {
                                    Label(
                                        goMoreService.isKeyMatched ? "密钥已匹配" : "密钥未匹配",
                                        systemImage: goMoreService.isKeyMatched ? "checkmark.circle.fill" : "xmark.circle.fill"
                                    )
                                    .foregroundColor(goMoreService.isKeyMatched ? .green : .red)
                                    
                                    Spacer()
                                    
                                    Button("检查") {
                                        goMoreService.checkGoMoreKeySupport()
                                    }
                                    .buttonStyle(.bordered)
                                }
                                
                                if !goMoreService.isKeyMatched {
                                    Divider()
                                    TextField("输入GoMore密钥", text: $goMoreKey)
                                        .textFieldStyle(.roundedBorder)
                                        .autocapitalization(.none)
                                        .disableAutocorrection(true)
                                    
                                    Button("设置密钥") {
                                        goMoreService.setGoMoreKey(goMoreKey) { success, _ in
                                            if success {
                                                goMoreKey = ""
                                            }
                                        }
                                    }
                                    .disabled(goMoreKey.isEmpty)
                                    .buttonStyle(.borderedProminent)
                                    .frame(maxWidth: .infinity)
                                }
                                
                                if !goMoreService.errorMessage.isEmpty {
                                    Divider()
                                    Text(goMoreService.errorMessage)
                                        .foregroundColor(.red)
                                }
                            }
                        }
                        .padding(8)
                    }
                    
                    // GoMore ChipID区域
                    GroupBox(label: Text("GoMore ChipID")) {
                        VStack(alignment: .leading, spacing: 12) {
                            if goMoreService.isLoading {
                                HStack {
                                    ProgressView()
                                        .padding(.trailing, 8)
                                    Text("获取ChipID...")
                                }
                            } else {
                                HStack {
                                    Text("ChipID")
                                    Spacer()
                                    if goMoreService.chipID.isEmpty {
                                        Text("未获取")
                                            .foregroundColor(.secondary)
                                    } else {
                                        Text(goMoreService.chipID)
                                            .foregroundColor(.secondary)
                                    }
                                }
                                
                                Button("获取ChipID") {
                                    goMoreService.getGoMoreChipID()
                                }
                                .buttonStyle(.bordered)
                                .frame(maxWidth: .infinity)
                                
                                if !goMoreService.errorMessage.isEmpty {
                                    Divider()
                                    Text(goMoreService.errorMessage)
                                        .foregroundColor(.red)
                                }
                            }
                        }
                        .padding(8)
                    }
                }
            }
            .padding()
        }
        .navigationTitle("GoMore算法支持")
        .onAppear {
            // 当视图出现时检查GoMore算法支持状态
            if deviceService.connectionState.isConnected {
                goMoreService.checkGoMoreSupport()
            }
        }
    }
}

struct GoMoreSupportView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            GoMoreSupportView()
        }
    }
} 
