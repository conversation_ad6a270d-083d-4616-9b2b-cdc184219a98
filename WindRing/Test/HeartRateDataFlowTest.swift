import SwiftUI
import Combine
#if os(iOS)
import CRPSmartRing
#endif

/// 心率数据流程测试视图
/// 用于测试和演示心率数据的完整流程：获取->本地存储->上传->下载处理数据->存储处理数据->显示
struct HeartRateDataFlowTest: View {
    // 服务依赖
    private let deviceService = WindRingDeviceService.shared
    private let heartRateService = HeartRateUploadService.shared
    private let authService = AuthService.shared
    private let healthDataManager = HealthDataManager.shared
    private let syncService = SyncService.shared
    
    // 状态变量
    @State private var isLoggedIn = false
    @State private var isDeviceConnected = false
    @State private var isFetchingData = false
    @State private var isUploadingData = false
    @State private var isDownloadingData = false
    
    // 数据计数
    @State private var originalDataCount = 0
    @State private var uploadedDataCount = 0
    @State private var processedDataCount = 0
    
    // 实时心率数据
    @State private var currentHeartRate: Int = 0
    @State private var lastHeartRateTime: Date? = nil
    @State private var recentHeartRates: [(value: Int, timestamp: Date)] = []
    
    // 日志
    @State private var logs: [String] = []
    
    // 存储订阅
    @State private var cancellables = Set<AnyCancellable>()
    
    var body: some View {
        NavigationView {
            List {
                // 状态信息
                Section(header: Text("状态信息")) {
                    HStack {
                        Text("设备连接状态:")
                        Spacer()
                        Text(isDeviceConnected ? "connected".localized : "disconnected".localized)
                            .foregroundColor(isDeviceConnected ? .green : .red)
                    }
                    
                    HStack {
                        Text("用户登录状态:")
                        Spacer()
                        Text(isLoggedIn ? "已登录" : "未登录")
                            .foregroundColor(isLoggedIn ? .green : .red)
                    }
                    
                    // 实时心率显示
                    HStack {
                        Text("当前心率:")
                        Spacer()
                        if currentHeartRate > 0 {
                            Text("\(currentHeartRate) bpm")
                                .foregroundColor(.red)
                                .fontWeight(.bold)
                                .font(.title3)
                            
                            if let time = lastHeartRateTime {
                                Text("(\(formatTime(time)))")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        } else {
                            Text("等待数据...")
                                .foregroundColor(.gray)
                        }
                    }
                }
                
                // 自动心率历史记录
                if !recentHeartRates.isEmpty {
                    Section(header: Text("最近自动测量心率")) {
                        ForEach(recentHeartRates.prefix(10), id: \.timestamp) { data in
                            HStack {
                                Text("\(formatTime(data.timestamp))")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Spacer()
                                Text("\(data.value) bpm")
                                    .foregroundColor(.red)
                            }
                        }
                    }
                }
                
                // 数据计数
                Section(header: Text("数据统计")) {
                    HStack {
                        Text("原始数据量:")
                        Spacer()
                        Text("\(originalDataCount)条")
                    }
                    
                    HStack {
                        Text("已上传数据量:")
                        Spacer()
                        Text("\(uploadedDataCount)条")
                    }
                    
                    HStack {
                        Text("处理后数据量:")
                        Spacer()
                        Text("\(processedDataCount)条")
                    }
                }
                
                // 操作按钮
                Section(header: Text("数据流程操作")) {
                    Button(action: fetchHeartRateData) {
                        HStack {
                            Text("步骤1: 获取心率数据")
                            Spacer()
                            if isFetchingData {
                                ProgressView()
                            }
                        }
                    }
                    .disabled(isFetchingData || !isDeviceConnected)
                    
                    Button(action: uploadHeartRateData) {
                        HStack {
                            Text("步骤2: 上传心率数据")
                            Spacer()
                            if isUploadingData {
                                ProgressView()
                            }
                        }
                    }
                    .disabled(isUploadingData || !isLoggedIn || originalDataCount == 0)
                    
                    Button(action: downloadProcessedData) {
                        HStack {
                            Text("步骤3: 下载处理后数据")
                            Spacer()
                            if isDownloadingData {
                                ProgressView()
                            }
                        }
                    }
                    .disabled(isDownloadingData || !isLoggedIn)
                    
                    Button(action: viewHeartRateData) {
                        Text("步骤4: 查看心率数据")
                    }
                    
                    Button(action: runFullDataFlow) {
                        Text("一键完成全流程")
                    }
                    .disabled(isFetchingData || isUploadingData || isDownloadingData || !isDeviceConnected || !isLoggedIn)
                }
                
                // 自动测量配置
                Section(header: Text("自动测量配置")) {
                    Button(action: {
                        deviceService.setTimingHeartRateMeasurement(interval: 1) // 设置为5分钟
                        addLog("✅ 已设置自动心率测量: 每5分钟")
                    }) {
                        Text("设置5分钟自动测量")
                    }
                    
                    Button(action: {
                        deviceService.getTimingHeartRateMeasurementStatus { interval, error in
                            if let error = error {
                                addLog("❌ 获取定时心率状态失败: \(error.localizedDescription)")
                            } else if interval == 0 {
                                addLog("ℹ️ 当前定时心率测量已关闭")
                            } else {
                                addLog("ℹ️ 当前定时心率测量间隔: 每\(interval * 5)分钟")
                            }
                        }
                    }) {
                        Text("获取自动测量状态")
                    }
                    
                    Button(action: {
                        fetchAndPrintYesterdayHeartRateData()
                    }) {
                        Text("获取昨天心率数据(JSON)")
                            .foregroundColor(.blue)
                    }
                }
                
                // 日志记录
                Section(header: Text("操作日志")) {
                    ForEach(logs, id: \.self) { log in
                        Text(log)
                            .font(.footnote)
                    }
                }
                
                // 批量数据获取
                Section(header: Text("批量数据获取")) {
                    Button(action: {
                        fetchMultipleDaysHeartRateData(days: 14)
                    }) {
                        Text("获取最近14天心率数据")
                            .foregroundColor(.blue)
                    }
                    
                    Button(action: {
                        fetchDatabaseHeartRateWithJsonPrint(days: 15) { count in
                            addLog("🔍 数据库查询完成，共找到\(count)条记录")
                        }
                    }) {
                        Text("查询数据库中的心率数据")
                            .foregroundColor(.green)
                    }
                    
                    Button(action: {
                        clearDatabaseAndRefetch()
                    }) {
                        Text("清空数据库并重新获取数据")
                            .foregroundColor(.red)
                            .fontWeight(.bold)
                    }
                }
            }
            .navigationTitle("心率数据流程测试")
            .toolbar {
                Button("刷新状态") {
                    updateStatus()
                }
            }
            .onAppear {
                setupSubscriptions()
                updateStatus()
                
                // 设置通知监听
                NotificationCenter.default.addObserver(
                    forName: .autoHeartRateMeasured,
                    object: nil,
                    queue: .main
                ) { notification in
                    handleAutoHeartRateNotification(notification)
                }
            }
            .onDisappear {
                // 移除通知监听
                NotificationCenter.default.removeObserver(self, name: .autoHeartRateMeasured, object: nil)
            }
        }
    }
    
    // MARK: - 私有方法
    
    /// 处理自动心率通知
    private func handleAutoHeartRateNotification(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let value = userInfo["value"] as? Int,
              let timestamp = userInfo["timestamp"] as? Date else {
            return
        }
        
        // 更新当前心率
        currentHeartRate = value
        lastHeartRateTime = timestamp
        
        // 添加到最近心率记录
        recentHeartRates.insert((value: value, timestamp: timestamp), at: 0)
        
        // 限制记录数量
        if recentHeartRates.count > 50 {
            recentHeartRates = Array(recentHeartRates.prefix(50))
        }
        
        // 添加日志
        addLog("♥️ 收到自动心率测量: \(value) bpm")
        
        // 更新数据统计
        updateStatus()
    }
    
    /// 格式化时间
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter.string(from: date)
    }
    
    /// 设置订阅
    private func setupSubscriptions() {
        // 监听设备连接状态
        deviceService.$connectionState
            .sink { state in
                isDeviceConnected = state.isConnected
            }
            .store(in: &cancellables)
        
        // 监听登录状态
        // 注意：AuthService 可能没有发布的 isLoggedIn 属性，改为普通属性监听
        DispatchQueue.main.async {
//            self.isLoggedIn = self.(authService.currentToken == nil)
        }
        
        // 如果 AuthService 确实有 isLoggedIn 的发布者，可以取消注释下面的代码
        // authService.$isLoggedIn
        //    .assign(to: \.isLoggedIn, on: self)
        //    .store(in: &cancellables)
    }
    
    /// 更新状态信息
    private func updateStatus() {
        isDeviceConnected = deviceService.connectionState.isConnected
//        isLoggedIn = (authService.currentToken == nil)
        
        // 获取数据计数
        if let userId = authService.currentUser?.id {
            // 获取原始数据计数
            let heartRates = healthDataManager.getHeartRates(userId: userId)
            originalDataCount = heartRates.count
            
            // 获取已上传数据计数
            uploadedDataCount = heartRates.filter { $0.isUploaded }.count
            
            // TODO: 获取处理后数据计数
            // 此处需要实现获取处理后数据的计数逻辑
            processedDataCount = 0
            
            addLog("状态已更新: 原始数据\(originalDataCount)条，已上传\(uploadedDataCount)条，处理后\(processedDataCount)条")
        } else {
            originalDataCount = 0
            uploadedDataCount = 0
            processedDataCount = 0
            addLog("无法获取数据计数: 用户未登录")
        }
    }
    
    /// 步骤1: 获取心率数据
    private func fetchHeartRateData() {
        guard isDeviceConnected else {
            addLog("❌ 错误: 设备未连接")
            return
        }
        
        isFetchingData = true
        addLog("🔄 开始获取心率数据...")
        
        // 使用定时心率数据
        heartRateService.syncTimingHeartRateData(days: 1) { count, error in
            isFetchingData = false
            
            if let error = error {
                addLog("❌ 获取心率数据失败: \(error.localizedDescription)")
            } else {
                addLog("✅ 成功获取\(count)条心率数据")
                updateStatus()
            }
        }
    }
    
    /// 步骤2: 上传心率数据
    private func uploadHeartRateData() {
        guard isLoggedIn else {
            addLog("❌ 错误: 用户未登录")
            return
        }
        
        isUploadingData = true
        addLog("🔄 开始上传心率数据...")
        
        heartRateService.uploadPendingHeartRateData { count, error in
            isUploadingData = false
            
            if let error = error {
                addLog("❌ 上传心率数据失败: \(error.localizedDescription)")
            } else {
                addLog("✅ 成功上传\(count)条心率数据")
                updateStatus()
            }
        }
    }
    
    /// 步骤3: 下载处理后数据
    private func downloadProcessedData() {
        guard isLoggedIn, let userId = authService.currentUser?.id else {
            addLog("❌ 错误: 用户未登录")
            return
        }
        
        isDownloadingData = true
        addLog("🔄 开始下载处理后的心率数据...")
        
        // 根据 SyncService 的实际接口修改
        // 假设 SyncService 有一个同步心率数据的具体方法
        syncService.syncHeartRateData(userId: userId) { success in
            isDownloadingData = false
            
            if success {
                addLog("✅ 成功下载处理后的心率数据")
                updateStatus()
            } else {
                addLog("❌ 下载处理后的心率数据失败")
            }
        }
    }
    
    /// 步骤4: 查看心率数据
    private func viewHeartRateData() {
        guard let userId = authService.currentUser?.id else {
            addLog("❌ 错误: 用户未登录")
            return
        }
        
        let heartRates = healthDataManager.getHeartRates(userId: userId)
        
        if heartRates.isEmpty {
            addLog("⚠️ 没有心率数据可以显示")
            return
        }
        
        addLog("📊 显示最近\(min(5, heartRates.count))条心率数据:")
        
        // 显示最近5条数据
        let recentHeartRates = Array(heartRates.suffix(5))
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        for heartRate in recentHeartRates {
            if let timestamp = heartRate.timestamp {
                let timeString = dateFormatter.string(from: timestamp)
                addLog("♥️ \(timeString): \(heartRate.value)次/分钟")
            }
        }
    }
    
    /// 一键完成全流程
    private func runFullDataFlow() {
        addLog("🚀 开始执行完整心率数据流程...")
        
        // 步骤1: 获取数据
        fetchHeartRateData()
        
        // 等待获取完成后执行上传
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            // 步骤2: 上传数据
            self.uploadHeartRateData()
            
            // 等待上传完成后执行下载
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                // 步骤3: 下载处理后数据
                self.downloadProcessedData()
                
                // 等待下载完成后显示数据
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    // 步骤4: 查看数据
                    self.viewHeartRateData()
                    self.addLog("🎉 完整心率数据流程已执行完毕!")
                }
            }
        }
    }
    
    /// 获取并打印昨天的心率数据(JSON格式)
    private func fetchAndPrintYesterdayHeartRateData() {
        addLog("🔍 开始获取昨天的心率数据...")
        
        // 创建日历并计算昨天的日期范围
        let calendar = Calendar.current
        let now = Date()
        let yesterday = calendar.date(byAdding: .day, value: -1, to: now)!
        let startOfYesterday = calendar.startOfDay(for: yesterday)
        let endOfYesterday = calendar.date(byAdding: .day, value: 1, to: startOfYesterday)!.addingTimeInterval(-1)
        
        addLog("📅 查询时间范围: \(formatFullDate(startOfYesterday)) - \(formatFullDate(endOfYesterday))")
        
        guard let userId = authService.currentUser?.id else {
            addLog("❌ 错误: 用户未登录，无法获取数据")
            return
        }
        
        // 从本地数据库获取昨天的心率数据
        let heartRates = healthDataManager.getHeartRates(
            userId: userId,
            startDate: startOfYesterday,
            endDate: endOfYesterday
        )
        
        if heartRates.isEmpty {
            addLog("⚠️ 没有找到昨天的心率数据")
            
            // 尝试通过设备获取昨天的心率数据
            addLog("🔄 尝试从设备获取昨天的心率数据...")
            fetchTimingHeartRateWithJsonPrint(day: 1) { count, error in
                if let error = error {
                    addLog("❌ 从设备获取昨天心率数据失败: \(error.localizedDescription)")
                } else if count > 0 {
                    addLog("✅ 成功从设备获取\(count)条昨天的心率数据")
                } else {
                    addLog("⚠️ 设备中没有昨天的心率数据")
                }
            }
            return
        }
        
        // 转换为JSON格式并打印
        var jsonArray: [[String: Any]] = []
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        
        for heartRate in heartRates {
            if let timestamp = heartRate.timestamp {
                let jsonObject: [String: Any] = [
                    "value": heartRate.value,
                    "timestamp": dateFormatter.string(from: timestamp),
                    "deviceId": heartRate.deviceId ?? "unknown",
                    "confidence": heartRate.confidence,
                    "isUploaded": heartRate.isUploaded
                ]
                jsonArray.append(jsonObject)
            }
        }
        
        // 转换为JSON字符串
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: jsonArray, options: .prettyPrinted)
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                // 打印到控制台
                print("\n===== 昨天的心率数据 JSON =====\n\(jsonString)\n================================\n")
                addLog("✅ 成功生成包含\(jsonArray.count)条记录的JSON并打印到控制台")
            }
        } catch {
            addLog("❌ 转换JSON失败: \(error.localizedDescription)")
        }
    }
    
    /// 格式化完整日期
    private func formatFullDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }
    
    /// 从设备获取指定日期的定时心率数据并打印JSON
    private func fetchTimingHeartRateWithJsonPrint(day: Int, completion: @escaping (Int, Error?) -> Void) {
        guard deviceService.connectionState.isConnected else {
            completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "设备未连接"]))
            return
        }
        
        addLog("🔄 从设备获取第\(day)天的定时心率数据...")
        
        #if os(iOS)
        CRPSmartRingSDK.sharedInstance.getTimingHeartRate(day) { model, error in
            if error == .none {
                // 获取日期
                let calendar = Calendar.current
                let today = Date()
                let targetDate = calendar.date(byAdding: .day, value: -day, to: today) ?? today
                
                // 计算当天0点的时间
                let startOfDay = calendar.startOfDay(for: targetDate)
                
                addLog("✅ 成功获取定时心率数据，共\(model.hearts.count)条记录")
                
                // 过滤有效的心率值（大于0且小于255的值）
                let validHeartRates = model.hearts.enumerated().filter { $0.element > 0 && $0.element < 255 }
                
                if validHeartRates.isEmpty {
                    addLog("⚠️ 没有有效的定时心率数据")
                    completion(0, nil)
                    return
                }
                
                addLog("📊 有效定时心率数据: \(validHeartRates.count)条")
                
                // 创建JSON数组
                var jsonArray: [[String: Any]] = []
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                
                // 定时心率每5分钟一条，总共288条
                for (index, value) in validHeartRates {
                    // 计算时间戳：当天0点 + index * 5分钟
                    let timestamp = calendar.date(byAdding: .minute, value: index * 5, to: startOfDay) ?? startOfDay
                    
                    let jsonObject: [String: Any] = [
                        "value": value,
                        "timestamp": dateFormatter.string(from: timestamp),
                        "index": index
                    ]
                    jsonArray.append(jsonObject)
                }
                
                // 转换为JSON字符串并打印
                do {
                    let jsonData = try JSONSerialization.data(withJSONObject: jsonArray, options: .prettyPrinted)
                    if let jsonString = String(data: jsonData, encoding: .utf8) {
                        // 打印到控制台
                        print("\n===== 设备中第\(day)天的定时心率数据 JSON =====\n\(jsonString)\n=======================================\n")
                        addLog("✅ 成功生成包含\(jsonArray.count)条记录的JSON并打印到控制台")
                    }
                } catch {
                    addLog("❌ 转换JSON失败: \(error.localizedDescription)")
                }
                
                completion(validHeartRates.count, nil)
            } else {
                addLog("❌ 获取定时心率数据失败: \(error)")
                completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取定时心率数据失败: \(error)"]))
            }
        }
        #else
        addLog("❌ 当前平台不支持获取心率数据")
        completion(0, NSError(domain: "com.windring.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "当前平台不支持SDK"]))
        #endif
    }
    
    /// 获取最近多天的心率数据并打印JSON
    private func fetchMultipleDaysHeartRateData(days: Int) {
        guard deviceService.connectionState.isConnected else {
            addLog("❌ 错误: 设备未连接，无法获取数据")
            return
        }
        
        addLog("🔄 开始获取最近\(days)天的心率数据...")
        
        // 创建进度计数器
        let totalDays = days
        var completedDays = 0
        
        // 递归函数，逐天获取数据
        func fetchNextDay(currentDay: Int) {
            guard currentDay <= totalDays else {
                addLog("✅ 全部\(totalDays)天的心率数据获取完成")
                return
            }
            
            addLog("📅 正在获取第\(currentDay)天的心率数据 (还剩\(totalDays - currentDay)天)...")
            
            fetchTimingHeartRateWithJsonPrint(day: currentDay) { count, error in
                if let error = error {
                    addLog("❌ 获取第\(currentDay)天心率数据失败: \(error.localizedDescription)")
                } else if count > 0 {
                    completedDays += 1
                    addLog("✅ 成功获取第\(currentDay)天的心率数据 (\(count)条记录)")
                } else {
                    addLog("⚠️ 第\(currentDay)天没有心率数据")
                }
                
                // 短暂延迟后获取下一天数据，避免设备请求过于频繁
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    fetchNextDay(currentDay: currentDay + 1)
                }
            }
        }
        
        // 开始获取第1天的数据
        fetchNextDay(currentDay: 1)
    }
    
    /// 添加日志
    private func addLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        let logEntry = "[\(timestamp)] \(message)"
        
        DispatchQueue.main.async {
            withAnimation {
                self.logs.append(logEntry)
                
                // 如果日志条目超过100条，移除最早的日志
                if self.logs.count > 100 {
                    self.logs.removeFirst()
                }
            }
        }
    }
    
    /// 从本地数据库获取心率数据并打印JSON
    private func fetchDatabaseHeartRateWithJsonPrint(days: Int = 15, completion: @escaping (Int) -> Void) {
        guard let userId = authService.currentUser?.id else {
            addLog("❌ 错误: 用户未登录，无法查询数据")
            completion(0)
            return
        }
        
        addLog("🔍 开始查询数据库中最近\(days)天的心率数据...")
        
        // 创建日历
        let calendar = Calendar.current
        let today = Date()
        let startOfToday = calendar.startOfDay(for: today)
        
        // 统计总记录数
        var totalCount = 0
        var dayStats: [Int: Int] = [:] // 天数: 记录数
        
        // 查询每一天的数据
        for day in 0..<days {
            // 计算日期范围
            let targetDate = calendar.date(byAdding: .day, value: -day, to: today)!
            let startOfDay = calendar.startOfDay(for: targetDate)
            let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!.addingTimeInterval(-1)
            
            // 日期描述
            let dayDesc = day == 0 ? "今天" : "第\(day)天"
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: startOfDay)
            
            // 从数据库获取该天的心率数据
            let heartRates = healthDataManager.getHeartRates(
                userId: userId,
                startDate: startOfDay,
                endDate: endOfDay
            )
            
            // 记录统计信息
            let count = heartRates.count
            totalCount += count
            dayStats[day] = count
            
            if count == 0 {
                addLog("⚠️ 数据库中没有\(dayDesc)(\(dateString))的心率数据")
                continue
            }
            
            addLog("📊 数据库中有\(dayDesc)(\(dateString))的\(count)条心率记录")
            
            // 转换为JSON格式并打印
            var jsonArray: [[String: Any]] = []
            let timeFormatter = DateFormatter()
            timeFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            
            for heartRate in heartRates {
                if let timestamp = heartRate.timestamp {
                    let jsonObject: [String: Any] = [
                        "value": heartRate.value,
                        "timestamp": timeFormatter.string(from: timestamp),
                        "deviceId": heartRate.deviceId ?? "unknown",
                        "confidence": heartRate.confidence,
                        "isUploaded": heartRate.isUploaded
                    ]
                    jsonArray.append(jsonObject)
                }
            }
            
            // 转换为JSON字符串
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: jsonArray, options: .prettyPrinted)
                if let jsonString = String(data: jsonData, encoding: .utf8) {
                    // 打印到控制台
                    print("\n===== 数据库中\(dayDesc)(\(dateString))的心率数据 JSON =====\n\(jsonString)\n===========================================\n")
                    addLog("✅ 成功生成\(dayDesc)的\(jsonArray.count)条记录的JSON并打印到控制台")
                }
            } catch {
                addLog("❌ 转换JSON失败: \(error.localizedDescription)")
            }
        }
        
        // 输出汇总信息
        addLog("📋 数据库中共有最近\(days)天的\(totalCount)条心率记录")
        
        // 打印每天的统计信息
        var summaryInfo = "\n===== 数据库心率记录统计 =====\n"
        for day in 0..<days {
            let targetDate = calendar.date(byAdding: .day, value: -day, to: today)!
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: targetDate)
            
            let count = dayStats[day] ?? 0
            let dayDesc = day == 0 ? "今天" : "第\(day)天"
            summaryInfo += "📆 \(dayDesc)(\(dateString)): \(count)条记录\n"
        }
        summaryInfo += "================================\n"
        print(summaryInfo)
        
        completion(totalCount)
    }
    
    /// 清空数据库并重新获取心率数据
    private func clearDatabaseAndRefetch() {
        guard let userId = authService.currentUser?.id else {
            addLog("❌ 错误: 用户未登录，无法操作数据")
            return
        }
        
        // 显示确认对话框
        let alert = UIAlertController(
            title: "确认操作",
            message: "这将清空数据库中所有心率数据并重新获取。此操作不可撤销，是否继续？",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        alert.addAction(UIAlertAction(title: "确认", style: .destructive) { _ in
            self.executeDataClearAndRefetch(userId: userId)
        })
        
        // 获取当前视图控制器并显示弹窗
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            var currentController = rootViewController
            while let presentedController = currentController.presentedViewController {
                currentController = presentedController
            }
            currentController.present(alert, animated: true)
        }
    }
    
    /// 执行数据清空和重新获取
    private func executeDataClearAndRefetch(userId: String) {
        addLog("🗑️ 开始清空本地数据库中的心率数据...")
        
        // 清空数据库中的心率数据
        let success = healthDataManager.clearHeartRates(userId: userId)
        
        if !success {
            addLog("❌ 清空数据库失败")
            return
        }
        
        addLog("✅ 数据库已清空")
        updateStatus() // 更新UI状态
        
        // 检查设备连接状态
        guard deviceService.connectionState.isConnected else {
            addLog("❌ 设备未连接，无法获取数据")
            return
        }
        
        addLog("🔄 开始重新获取最近15天的心率数据...")
        
        // 递归函数，逐天获取数据
        func fetchNextDay(currentDay: Int, totalDays: Int, completion: @escaping (Int) -> Void) {
            guard currentDay <= totalDays else {
                addLog("✅ 已完成所有\(totalDays)天的心率数据同步")
                completion(currentDay - 1)
                return
            }
            
            guard deviceService.connectionState.isConnected else {
                addLog("⚠️ 设备已断开连接，停止心率数据同步")
                completion(currentDay - 1)
                return
            }
            
            let dayDesc = currentDay == 0 ? "今天" : "第\(currentDay)天"
            addLog("📅 正在同步\(dayDesc)的心率数据 (还剩\(totalDays - currentDay)天)...")
            
            // 使用SDK获取定时心率数据
            CRPSmartRingSDK.sharedInstance.getTimingHeartRate(currentDay) { model, error in
                if error == .none {
                    // 获取日期并计算当天0点
                    let calendar = Calendar.current
                    let today = Date()
                    let targetDate = calendar.date(byAdding: .day, value: -currentDay, to: today) ?? today
                    let startOfDay = calendar.startOfDay(for: targetDate)
                    
                    // 过滤有效的心率值
                    let validHeartRates = model.hearts.enumerated().filter { $0.element > 0 && $0.element < 255 }
                    
                    if validHeartRates.isEmpty {
                        self.addLog("⚠️ \(dayDesc)没有有效的心率数据")
                    } else {
                        self.addLog("✅ 获取到\(dayDesc)的\(validHeartRates.count)条有效心率数据")
                        
                        // 转换为批量添加格式
                        var heartRateData: [(value: Int16, timestamp: Date, deviceId: String?, confidence: Int16?)] = []
                        let deviceId = self.deviceService.deviceInfo?.mac ?? "unknown"
                        
                        for (index, value) in validHeartRates {
                            // 计算时间戳：当天0点 + index * 5分钟
                            let timestamp = calendar.date(byAdding: .minute, value: index * 5, to: startOfDay) ?? startOfDay
                            
                            heartRateData.append((
                                value: Int16(value),
                                timestamp: timestamp,
                                deviceId: deviceId,
                                confidence: 100
                            ))
                        }
                        
                        // 保存到本地数据库
                        let success = self.healthDataManager.addHeartRates(userId: userId, data: heartRateData)
                        
                        if success {
                            self.addLog("✅ 成功保存\(dayDesc)的\(heartRateData.count)条心率数据到本地")
                            self.updateStatus() // 更新UI状态
                        } else {
                            self.addLog("❌ 保存\(dayDesc)的心率数据失败")
                        }
                    }
                } else {
                    self.addLog("❌ 获取\(dayDesc)的心率数据失败: \(error)")
                }
                
                // 短暂延迟后获取下一天数据，避免设备请求过于频繁
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    fetchNextDay(currentDay: currentDay + 1, totalDays: totalDays, completion: completion)
                }
            }
        }
        
        // 开始获取数据，从第0天(今天)开始，共15天
        fetchNextDay(currentDay: 0, totalDays: 14) { completedDays in
            // 获取完成后，尝试上传数据到服务器
            DispatchQueue.main.async {
                self.addLog("🔄 已获取\(completedDays + 1)天的心率数据，开始上传到服务器...")
                
                self.heartRateService.uploadPendingHeartRateData { count, error in
                    if let error = error {
                        self.addLog("❌ 上传心率数据失败: \(error.localizedDescription)")
                    } else {
                        self.addLog("✅ 成功上传\(count)条心率数据到服务器")
                    }
                    
                    // 完成后查询数据库状态
                    self.fetchDatabaseHeartRateWithJsonPrint(days: 15) { totalCount in
                        self.addLog("📊 数据清空并重新获取完成，数据库现有\(totalCount)条记录")
                    }
                }
            }
        }
    }
}

#Preview {
    HeartRateDataFlowTest()
} 
