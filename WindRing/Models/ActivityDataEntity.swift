import Foundation
import CoreData

@objc(ActivityDataEntity)
public class ActivityDataEntity: NSManagedObject {
    
}

extension ActivityDataEntity {
    @nonobjc public class func fetchRequest() -> NSFetchRequest<ActivityDataEntity> {
        return NSFetchRequest<ActivityDataEntity>(entityName: "ActivityEntity")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var userId: String?
    @NSManaged public var deviceId: String?
    @NSManaged public var date: Date?
    @NSManaged public var steps: Int32
    @NSManaged public var distance: Int32
    @NSManaged public var calories: Int32
    @NSManaged public var activeMinutes: Int32
    @NSManaged public var isUploaded: Bool
    @NSManaged public var createdAt: Date?
}

extension ActivityDataEntity: Identifiable {
    
} 