//
//  FeedbackHistoryModels.swift
//  WindRing
//
//  Created by zx on 2025/6/19.
//

import Foundation

public struct FeedbackHistoryPage: Codable {
    let list: [FeedbackHistoryItemDTO]
    let total: Int
}

public struct FeedbackHistoryItemDTO: Codable {
    let id: Int
    let feedbackType: String
    let feedbackChannel: String
    let feedbackContent: String
    let attachUrls: String
    let contactEmail: String
    let status: Int // 0-待回复 1-已回复
    let createTime: String
    let replyContent: String?
} 
