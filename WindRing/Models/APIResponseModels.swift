import Foundation

// MARK: - 压力评分数据模型
public struct StressScoreResponse: Codable {
    public let code: Int
    public let data: StressScoreData?
    public let msg: String
    
    // 添加初始化方法使其可以在不同地方创建实例
    public init(code: Int, data: StressScoreData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct StressScoreData: Codable {
    public let score: Int
    public let stress: Int
    public let stressState: Int
    public let avgHrv: Int?
    public let lastAvg30Hrv: Int?
    public let time: Int64?  // 设为可选，以满足不同文件的需求
    public let quality: Int?
    public let lastAvgHRV: Int?  // 这个字段与API示例中匹配
    
    // 添加初始化方法，使其可以在不同地方创建实例
    public init(
        score: Int,
        stress: Int,
        stressState: Int,
        avgHrv: Int? = nil,
        lastAvg30Hrv: Int? = nil,
        time: Int64? = nil,
        quality: Int? = nil,
        lastAvgHRV: Int? = nil
    ) {
        self.score = score
        self.stress = stress
        self.stressState = stressState
        self.avgHrv = avgHrv
        self.lastAvg30Hrv = lastAvg30Hrv
        self.time = time
        self.quality = quality
        self.lastAvgHRV = lastAvgHRV
    }
    
    // 添加编码和解码方法，以处理不同文件中的字段差异
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        score = try container.decode(Int.self, forKey: .score)
        stress = try container.decode(Int.self, forKey: .stress)
        stressState = try container.decode(Int.self, forKey: .stressState)
        
        // 对可选字段使用decodeIfPresent
        avgHrv = try container.decodeIfPresent(Int.self, forKey: .avgHrv)
        lastAvg30Hrv = try container.decodeIfPresent(Int.self, forKey: .lastAvg30Hrv)
        time = try container.decodeIfPresent(Int64.self, forKey: .time)
        quality = try container.decodeIfPresent(Int.self, forKey: .quality)
        lastAvgHRV = try container.decodeIfPresent(Int.self, forKey: .lastAvgHRV)
    }
}

// MARK: - 生命体征状态数据模型
public struct VitalsStatusResponse: Codable {
    public let code: Int
    public let data: VitalsStatusData?
    public let msg: String
    
    public init(code: Int, data: VitalsStatusData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct VitalsStatusData: Codable {
    public let significantTimes: Int
    public let minorTimes: Int
    public let records: [VitalRecord]
    
    public init(significantTimes: Int, minorTimes: Int, records: [VitalRecord]) {
        self.significantTimes = significantTimes
        self.minorTimes = minorTimes
        self.records = records
    }
}

public struct VitalRecord: Codable {
    public let type: Int
    public let normalTimes: Int?
    public let significanttimes: Int?
    public let minortimes: Int?
    public let high: Int?
    public let normal: Int?
    public let low: Int?
    
    public init(type: Int, normalTimes: Int? = nil, significanttimes: Int? = nil, minortimes: Int? = nil, high: Int? = nil, normal: Int? = nil, low: Int? = nil) {
        self.type = type
        self.normalTimes = normalTimes
        self.significanttimes = significanttimes
        self.minortimes = minortimes
        self.high = high
        self.normal = normal
        self.low = low
    }
}

// MARK: - 心率详情数据模型
public struct HrVitalDetailsResponse: Codable {
    public let code: Int
    public let data: HrVitalDetailsData?
    public let msg: String
    
    public init(code: Int, data: HrVitalDetailsData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct HrVitalDetailsData: Codable {
    public let id: String?
    public let records: [HrVitalRecord]
    public let avg: Int?
    public let latestHearts: Int?
    public let latestTime: Int64?
    
    public init(id: String?, records: [HrVitalRecord], avg: Int?, latestHearts: Int?, latestTime: Int64?) {
        self.id = id
        self.records = records
        self.avg = avg
        self.latestHearts = latestHearts
        self.latestTime = latestTime
    }
}

public struct HrVitalRecord: Codable,Identifiable {
    public var id: Int { Int(time) }
    public let hearts: Int
    public let time: Int64  // 改为Int64类型，接收毫秒时间戳
    public let minHearts: Int
    public let maxHearts: Int
    
    public init(hearts: Int, time: Int64, minHearts: Int, maxHearts: Int) {
        self.hearts = hearts
        self.time = time
        self.minHearts = minHearts
        self.maxHearts = maxHearts
    }
}

// MARK: - 血氧详情数据模型
public struct SpO2DetailsResponse: Codable {
    public let code: Int
    public let data: SpO2DetailsData?
    public let msg: String
    
    public init(code: Int, data: SpO2DetailsData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct SpO2DetailsData: Codable {
    public let records: [SpO2Record]
    public let avg: Int?
    public let latestSpO2: Int?
    public let latestTime: Int64?
    
    public init(records: [SpO2Record], avg: Int?, latestSpO2: Int?, latestTime: Int64?) {
        self.records = records
        self.avg = avg
        self.latestSpO2 = latestSpO2
        self.latestTime = latestTime
    }
    
    // 自定义解码，处理字段名称大小写问题
    enum CodingKeys: String, CodingKey {
        case records
        case avg
        case latestSpO2 = "latestSpO2"  // 确保大小写匹配API
        case latestTime
    }
}

public struct SpO2Record: Codable {
    public let spO2: Int
    public let time: Int64  // 毫秒时间戳
    public let minSpO2: Int?
    public let maxSpO2: Int?
    
    public init(spO2: Int, time: Int64, minSpO2: Int?, maxSpO2: Int?) {
        self.spO2 = spO2
        self.time = time
        self.minSpO2 = minSpO2
        self.maxSpO2 = maxSpO2
    }
    
    // 自定义解码，处理字段名称大小写问题
    enum CodingKeys: String, CodingKey {
        case spO2 = "spO2"  // 确保大小写匹配API
        case time
        case minSpO2 = "minSpO2"  // 确保大小写匹配API
        case maxSpO2 = "maxSpO2"  // 确保大小写匹配API
    }
}

// MARK: - 活动分数历史数据模型
public struct ActivityScoreHistoryResponse: Codable {
    public let code: Int
    public let data: ActivityScoreHistoryData?
    public let msg: String
    
    public init(code: Int, data: ActivityScoreHistoryData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct ActivityScoreHistoryData: Codable {
    public let maxScore: Int
    public let avgScore: Int
    public let minScore: Int
    public let records: [ActivityScoreHistoryRecord]
    
    public enum CodingKeys: String, CodingKey {
        case maxScore
        case avgScore
        case minScore
        case records
    }
    
    public init(maxScore: Int, avgScore: Int, minScore: Int, records: [ActivityScoreHistoryRecord]) {
        self.maxScore = maxScore
        self.avgScore = avgScore
        self.minScore = minScore
        self.records = records
    }
}

public struct ActivityScoreHistoryRecord: Codable {
    public let score: Int
    public let time: String
    
    public enum CodingKeys: String, CodingKey {
        case score
        case time
    }
    
    public init(score: Int, time: String) {
        self.score = score
        self.time = time
    }
}

// MARK: - 步数历史数据模型
public struct ActivityStepsHistoryResponse: Codable {
    public let code: Int
    public let data: ActivityStepsHistoryData?
    public let msg: String
    
    public enum CodingKeys: String, CodingKey {
        case code
        case data
        case msg
    }
    
    public init(code: Int, data: ActivityStepsHistoryData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct ActivityStepsHistoryData: Codable {
    public let maxSteps: Int
    public let avgSteps: Int
    public let minSteps: Int
    public let records: [ActivityStepsRecord]
    
    public enum CodingKeys: String, CodingKey {
        case maxSteps
        case avgSteps
        case minSteps
        case records
    }
    
    public init(maxSteps: Int, avgSteps: Int, minSteps: Int, records: [ActivityStepsRecord]) {
        self.maxSteps = maxSteps
        self.avgSteps = avgSteps
        self.minSteps = minSteps
        self.records = records
    }
}

public struct ActivityStepsRecord: Codable {
    public let steps: Int
    public let time: String
    
    public enum CodingKeys: String, CodingKey {
        case steps
        case time
    }
    
    public init(steps: Int, time: String) {
        self.steps = steps
        self.time = time
    }
}

// MARK: - 卡路里历史数据模型
public struct ActivityCaloriesHistoryResponse: Codable {
    public let code: Int
    public let data: ActivityCaloriesHistoryData?
    public let msg: String
    
    public enum CodingKeys: String, CodingKey {
        case code
        case data
        case msg
    }
    
    public init(code: Int, data: ActivityCaloriesHistoryData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct ActivityCaloriesHistoryData: Codable {
    public let maxCalories: Int
    public let avgCalories: Int
    public let minCalories: Int
    public let records: [ActivityCaloriesRecord]
    
    public enum CodingKeys: String, CodingKey {
        case maxCalories
        case avgCalories
        case minCalories
        case records
    }
    
    public init(maxCalories: Int, avgCalories: Int, minCalories: Int, records: [ActivityCaloriesRecord]) {
        self.maxCalories = maxCalories
        self.avgCalories = avgCalories
        self.minCalories = minCalories
        self.records = records
    }
}

public struct ActivityCaloriesRecord: Codable {
    public let calories: Int
    public let time: String
    
    public enum CodingKeys: String, CodingKey {
        case calories
        case time
    }
    
    public init(calories: Int, time: String) {
        self.calories = calories
        self.time = time
    }
}

// MARK: - 站立时长历史数据模型
public struct ActivityStandingDurationHistoryResponse: Codable {
    public let code: Int
    public let data: ActivityStandingDurationHistoryData?
    public let msg: String
    
    public enum CodingKeys: String, CodingKey {
        case code
        case data
        case msg
    }
    
    public init(code: Int, data: ActivityStandingDurationHistoryData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct ActivityStandingDurationHistoryData: Codable {
    public let maxDuration: Int
    public let avgDuration: Int
    public let minDuration: Int
    public let records: [ActivityStandingDurationRecord]
    
    public enum CodingKeys: String, CodingKey {
        case maxDuration
        case avgDuration
        case minDuration
        case records
    }
    
    public init(maxDuration: Int, avgDuration: Int, minDuration: Int, records: [ActivityStandingDurationRecord]) {
        self.maxDuration = maxDuration
        self.avgDuration = avgDuration
        self.minDuration = minDuration
        self.records = records
    }
}

public struct ActivityStandingDurationRecord: Codable {
    public let duration: Int
    public let time: String
    
    public enum CodingKeys: String, CodingKey {
        case duration
        case time
    }
    
    public init(duration: Int, time: String) {
        self.duration = duration
        self.time = time
    }
}

// MARK: - 压力历史接口响应模型
/// 压力历史得分响应
public struct StressHistoryScoreResponse: Codable {
    public let code: Int
    public let data: StressHistoryScoreData?
    public let msg: String?
    
    enum CodingKeys: String, CodingKey {
        case code, data, msg
    }
    
    public init(code: Int, data: StressHistoryScoreData?, msg: String?) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

/// 压力历史得分数据
public struct StressHistoryScoreData: Codable {
    public let maxStress: Int
    public let avgStress: Int
    public let minStress: Int
    public let records: [StressHistoryRecord]
    
//    enum CodingKeys: String, CodingKey {
//        case maxScore, avgScore, minScore, records
//    }
    
//    public init(maxScore: Int, avgScore: Int, minScore: Int, records: [StressHistoryRecord]) {
//        self.maxStress = maxScore
//        self.avgStress = avgScore
//        self.minStress = minScore
//        self.records = records
//    }
}

/// 压力历史记录
public struct StressHistoryRecord: Codable, Identifiable {
    public let stress: Int
    public let time: String
    
    public var id: String { time }
    
//    enum CodingKeys: String, CodingKey {
//        case score, time
//    }
//    
//    public init(stress: Int, time: String) {
//        self.stress = stress
//        self.time = time
//    }
}

// MARK: - 低压力时段历史接口响应模型
/// 低压力时段历史响应
public struct StressHistoryLowStressPeriodResponse: Codable {
    public let code: Int
    public let data: StressHistoryLowStressPeriodData?
    public let msg: String?
    
    enum CodingKeys: String, CodingKey {
        case code, data, msg
    }
    
    public init(code: Int, data: StressHistoryLowStressPeriodData?, msg: String?) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

/// 低压力时段历史数据
public struct StressHistoryLowStressPeriodData: Codable {
    public let maxLowStressPeriod: Int // 最大低压力时段(分钟)
    public let avgLowStressPeriod: Int // 平均低压力时段(分钟)
    public let minLowStressPeriod: Int // 最小低压力时段(分钟)
    public let records: [LowStressPeriodRecord] // 记录列表
    
    enum CodingKeys: String, CodingKey {
        case maxLowStressPeriod, avgLowStressPeriod, minLowStressPeriod, records
    }
    
    public init(maxLowStressPeriod: Int, avgLowStressPeriod: Int, minLowStressPeriod: Int, records: [LowStressPeriodRecord]) {
        self.maxLowStressPeriod = maxLowStressPeriod
        self.avgLowStressPeriod = avgLowStressPeriod
        self.minLowStressPeriod = minLowStressPeriod
        self.records = records
    }
}

/// 低压力时段记录
public struct LowStressPeriodRecord: Codable, Identifiable {
    public let lowStressPeriod: Int // 低压力时段值(分钟)
    public let time: String // 时间
    
    public var id: String { time }
    
    enum CodingKeys: String, CodingKey {
        case lowStressPeriod, time
    }
    
    public init(lowStressPeriod: Int, time: String) {
        self.lowStressPeriod = lowStressPeriod
        self.time = time
    }
}

// MARK: - 过度压力时段历史接口响应模型
/// 过度压力时段历史响应
public struct StressHistoryExcessiveResponse: Codable {
    public let code: Int
    public let data: StressHistoryExcessiveData?
    public let msg: String?
    
    enum CodingKeys: String, CodingKey {
        case code, data, msg
    }
    
    public init(code: Int, data: StressHistoryExcessiveData?, msg: String?) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

/// 过度压力时段历史数据
public struct StressHistoryExcessiveData: Codable {
    public let maxExcessive: Int // 最大过度压力时段(分钟)
    public let avgExcessive: Int // 平均过度压力时段(分钟)
    public let minExcessive: Int // 最小过度压力时段(分钟)
    public let records: [ExcessiveRecord] // 记录列表
    
    enum CodingKeys: String, CodingKey {
        case maxExcessive, avgExcessive, minExcessive, records
    }
    
    public init(maxExcessive: Int, avgExcessive: Int, minExcessive: Int, records: [ExcessiveRecord]) {
        self.maxExcessive = maxExcessive
        self.avgExcessive = avgExcessive
        self.minExcessive = minExcessive
        self.records = records
    }
}

/// 过度压力时段记录
public struct ExcessiveRecord: Codable, Identifiable {
    public let excessive: Int // 过度压力时段值(分钟)
    public let time: String // 时间
    
    public var id: String { time }
    
    enum CodingKeys: String, CodingKey {
        case excessive, time
    }
    
    public init(excessive: Int, time: String) {
        self.excessive = excessive
        self.time = time
    }
}

/// 夜间压力分数历史接口响应模型
public struct StressHistoryEveningResponse: Codable {
    public let code: Int
    public let data: StressHistoryEveningData?
    public let msg: String?
    
    enum CodingKeys: String, CodingKey {
        case code, data, msg
    }
    
    public init(code: Int, data: StressHistoryEveningData?, msg: String?) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

/// 夜间压力历史数据
public struct StressHistoryEveningData: Codable {
    public let maxEvening: Int // 最大夜间压力分数
    public let avgEvening: Int // 平均夜间压力分数
    public let minEvening: Int // 最小夜间压力分数
    public let records: [EveningRecord] // 记录列表
    
    enum CodingKeys: String, CodingKey {
        case maxEvening, avgEvening, minEvening, records
    }
    
    public init(maxEvening: Int, avgEvening: Int, minEvening: Int, records: [EveningRecord]) {
        self.maxEvening = maxEvening
        self.avgEvening = avgEvening
        self.minEvening = minEvening
        self.records = records
    }
}

/// 夜间压力记录
public struct EveningRecord: Codable, Identifiable {
    public let evening: Int // 夜间压力分数值
    public let time: String // 时间
    
    public var id: String { time }
    
    enum CodingKeys: String, CodingKey {
        case evening, time
    }
    
    public init(evening: Int, time: String) {
        self.evening = evening
        self.time = time
    }
}

// MARK: - 心率历史数据模型
public struct HeartRateHistoryResponse: Codable {
    public let code: Int
    public let data: HeartRateHistoryData?
    public let msg: String
    
    public init(code: Int, data: HeartRateHistoryData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct HeartRateHistoryData: Codable {
    public let maxHeartRate: Int
    public let avgHeartRate: Int
    public let minHeartRate: Int
    public let records: [HeartRateHistoryRecord]
    
    public init(maxHeartRate: Int, avgHeartRate: Int, minHeartRate: Int, records: [HeartRateHistoryRecord]) {
        self.maxHeartRate = maxHeartRate
        self.avgHeartRate = avgHeartRate
        self.minHeartRate = minHeartRate
        self.records = records
    }
}

public struct HeartRateHistoryRecord: Codable {
    public let heartRate: Int
    public let time: String
    public let maxHeartRate: Int
    public let minHeartRate: Int
    
    public init(heartRate: Int, time: String, maxHeartRate: Int, minHeartRate: Int) {
        self.heartRate = heartRate
        self.time = time
        self.maxHeartRate = maxHeartRate
        self.minHeartRate = minHeartRate
    }
}

// MARK: - 血氧历史数据模型
public struct SpO2HistoryResponse: Codable {
    public let code: Int
    public let data: SpO2HistoryData?
    public let msg: String
    
    public init(code: Int, data: SpO2HistoryData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct SpO2HistoryData: Codable {
    public let maxSpO2: Int
    public let avgSpO2: Int
    public let minSpO2: Int
    public let records: [SpO2HistoryRecord]
    
    public init(maxSpO2: Int, avgSpO2: Int, minSpO2: Int, records: [SpO2HistoryRecord]) {
        self.maxSpO2 = maxSpO2
        self.avgSpO2 = avgSpO2
        self.minSpO2 = minSpO2
        self.records = records
    }
}

public struct SpO2HistoryRecord: Codable {
    public let spO2: Int
    public let time: String
    public let maxSpO2: Int
    public let minSpO2: Int
    
    public init(spO2: Int, time: String, maxSpO2: Int, minSpO2: Int) {
        self.spO2 = spO2
        self.time = time
        self.maxSpO2 = maxSpO2
        self.minSpO2 = minSpO2
    }
}

// MARK: - HRV历史数据模型
public struct HRVHistoryResponse: Codable {
    public let code: Int
    public let data: HRVHistoryData?
    public let msg: String
    
    public init(code: Int, data: HRVHistoryData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
}

public struct HRVHistoryData: Codable {
    public let maxHRV: Int
    public let avgHRV: Int
    public let minHRV: Int
    public let records: [HRVHistoryRecord]
    
    public init(maxHRV: Int, avgHRV: Int, minHRV: Int, records: [HRVHistoryRecord]) {
        self.maxHRV = maxHRV
        self.avgHRV = avgHRV
        self.minHRV = minHRV
        self.records = records
    }
}

public struct HRVHistoryRecord: Codable {
    public let hrv: Int
    public let time: String
    public let maxHRV: Int
    public let minHRV: Int
    
    public init(hrv: Int, time: String, maxHRV: Int, minHRV: Int) {
        self.hrv = hrv
        self.time = time
        self.maxHRV = maxHRV
        self.minHRV = minHRV
    }
}

// 如果需要添加其他共享的API响应模型，可以添加在下面 
