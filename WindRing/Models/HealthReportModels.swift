import Foundation
import SwiftUI

// MARK: - 健康报告模型命名空间
public enum HealthReportModels {
    // MARK: - 健康报告类型
    public enum ReportType: String, Codable, CaseIterable {
        case daily
        case weekly
        case monthly
        case custom
        case all
        
        public var description: String {
            switch self {
            case .daily:
                return "日报"
            case .weekly:
                return "周报"
            case .monthly:
                return "月报"
            case .custom:
                return "自定义"
            case .all:
                return "全部"
            }
        }
        
        public var iconName: String {
            switch self {
            case .daily:
                return "sun.max"
            case .weekly:
                return "calendar"
            case .monthly:
                return "calendar.badge.clock"
            case .custom:
                return "slider.horizontal.3"
            case .all:
                return "list.bullet"
            }
        }
        
        public var color: Color {
            switch self {
            case .daily:
                return .blue
            case .weekly:
                return .green
            case .monthly:
                return .purple
            case .custom:
                return .orange
            case .all:
                return .gray
            }
        }
    }
    
    // MARK: - 健康报告
    public struct Report: Identifiable, Codable {
        public var id: String
        public var userId: String
        public var title: String
        public var type: ReportType
        public var createdAt: Date
        public var startDate: Date
        public var endDate: Date
        public var summary: String?
        public var healthScore: Int?
        public var sections: [Section]
        public var isFavorite: Bool
        
        public init(
            userId: String,
            type: ReportType,
            title: String = "",
            summary: String? = nil,
            healthScore: Int? = nil,
            sections: [Section] = [],
            isFavorite: Bool = false
        ) {
            self.id = UUID().uuidString
            self.userId = userId
            self.type = type
            
            // 设置日期范围
            let now = Date()
            switch type {
            case .daily:
                self.startDate = Calendar.current.startOfDay(for: now)
                self.endDate = now
                self.title = title.isEmpty ? "今日健康报告" : title
            case .weekly:
                if let weekStart = Calendar.current.date(byAdding: .day, value: -6, to: now) {
                    self.startDate = Calendar.current.startOfDay(for: weekStart)
                    self.endDate = now
                    self.title = title.isEmpty ? "每周健康报告" : title
                } else {
                    self.startDate = Calendar.current.startOfDay(for: now)
                    self.endDate = now
                    self.title = title.isEmpty ? "健康报告" : title
                }
            case .monthly:
                if let monthStart = Calendar.current.date(byAdding: .day, value: -29, to: now) {
                    self.startDate = Calendar.current.startOfDay(for: monthStart)
                    self.endDate = now
                    self.title = title.isEmpty ? "每月健康报告" : title
                } else {
                    self.startDate = Calendar.current.startOfDay(for: now)
                    self.endDate = now
                    self.title = title.isEmpty ? "健康报告" : title
                }
            case .custom:
                self.startDate = Calendar.current.date(byAdding: .day, value: -7, to: now) ?? now
                self.endDate = now
                self.title = title.isEmpty ? "自定义健康报告" : title
            case .all:
                self.startDate = now
                self.endDate = now
                self.title = title.isEmpty ? "健康报告" : title
            }
            
            self.createdAt = now
            self.summary = summary
            self.healthScore = healthScore
            self.sections = sections
            self.isFavorite = isFavorite
        }
    }
    
    // MARK: - 健康报告元数据
    public struct ReportMetadata: Identifiable, Codable {
        public var id: String
        public var userId: String
        public var title: String
        public var type: ReportType
        public var createdAt: Date
        public var startDate: Date
        public var endDate: Date
        public var summary: String?
        public var healthScore: Int?
        public var isFavorite: Bool
        
        public init(from report: Report) {
            self.id = report.id
            self.userId = report.userId
            self.title = report.title
            self.type = report.type
            self.createdAt = report.createdAt
            self.startDate = report.startDate
            self.endDate = report.endDate
            self.summary = report.summary
            self.healthScore = report.healthScore
            self.isFavorite = report.isFavorite
        }
        
        // 允许自定义初始化
        public init(
            id: String,
            userId: String,
            title: String,
            type: ReportType,
            createdAt: Date,
            startDate: Date,
            endDate: Date,
            summary: String? = nil,
            healthScore: Int? = nil,
            isFavorite: Bool = false
        ) {
            self.id = id
            self.userId = userId
            self.title = title
            self.type = type
            self.createdAt = createdAt
            self.startDate = startDate
            self.endDate = endDate
            self.summary = summary
            self.healthScore = healthScore
            self.isFavorite = isFavorite
        }
    }
    
    // MARK: - 报告部分
    public struct Section: Identifiable, Codable {
        public var id: String
        public var title: String
        public var content: String
        public var dataPoints: [DataPoint]?
        public var charts: [ChartData]?
        
        public init(
            title: String,
            content: String,
            dataPoints: [DataPoint]? = nil,
            charts: [ChartData]? = nil
        ) {
            self.id = UUID().uuidString
            self.title = title
            self.content = content
            self.dataPoints = dataPoints
            self.charts = charts
        }
    }
    
    // MARK: - 数据点
    public struct DataPoint: Identifiable, Codable {
        public var id: String
        public var label: String
        public var value: String
        
        public init(label: String, value: String) {
            self.id = UUID().uuidString
            self.label = label
            self.value = value
        }
    }
    
    // MARK: - 图表类型
    public enum ChartType: String, Codable {
        case line
        case bar
        case pie
        case area
    }
    
    // MARK: - 图表数据
    public struct ChartData: Identifiable, Codable {
        public var id: String
        public var title: String
        public var type: ChartType
        public var labels: [String]
        public var values: [Double]
        public var unit: String
        
        public init(
            title: String,
            type: ChartType,
            labels: [String],
            values: [Double],
            unit: String
        ) {
            self.id = UUID().uuidString
            self.title = title
            self.type = type
            self.labels = labels
            self.values = values
            self.unit = unit
        }
    }
}

// 便于使用的类型别名
public typealias HRMReport = HealthReportModels.Report
public typealias HRMReportType = HealthReportModels.ReportType
public typealias HRMReportMetadata = HealthReportModels.ReportMetadata
public typealias HRMSection = HealthReportModels.Section
public typealias HRMDataPoint = HealthReportModels.DataPoint
public typealias HRMChartType = HealthReportModels.ChartType
public typealias HRMChartData = HealthReportModels.ChartData

// 报告部分模型
public struct ReportSection: Identifiable, Codable {
    public var id: String
    public var title: String
    public var content: String
    public var dataPoints: [HRMDataPoint]
    public var chartData: HRMChartData?
    
    public init(
        title: String,
        content: String,
        dataPoints: [HRMDataPoint],
        chartData: HRMChartData? = nil
    ) {
        self.id = UUID().uuidString
        self.title = title
        self.content = content
        self.dataPoints = dataPoints
        self.chartData = chartData
    }
} 