import Foundation

public struct HRVDataModel: Codable,UploadedMarkable {
    var uploaded: Bool = false
    
    public let heartRate: [HRVData]
    
}

/// HRV数据模型，用于与服务器交互
public struct HRVData: Codable, Identifiable {
    public var id: String { "\(time)" } // 使用时间戳作为唯一标识符
    
    public let hrv: Int      // HRV值
    public let time: Int64   // 时间戳（毫秒）
    
    public enum CodingKeys: String, CodingKey {
        case hrv
        case time
    }
    
    public init(hrv: Int, time: Int64) {
        self.hrv = hrv
        self.time = time
    }
    
    /// 解析时间，转换为Date对象
    public var date: Date? {
        // API返回的是毫秒时间戳，直接转换
        return Date(timeIntervalSince1970: Double(time) / 1000.0)
    }
    
    /// 获取格式化的时间字符串（HH:mm格式）
    public func formattedTime() -> String {
        guard let date = self.date else {
            return "无效时间"
        }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    /// 获取ISO8601格式的时间字符串，用于API请求
    public func isoTimeString() -> String {
        guard let date = self.date else {
            return ""
        }
        
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime]
        return formatter.string(from: date)
    }
    
    /// 计算该时间点对应的半小时索引(0-47)
    public var halfHourIndex: Int? {
        guard let date = self.date else {
            return nil
        }
        
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        return hour * 2 + (minute >= 30 ? 1 : 0)
    }
}

/// HRV上传数据模型
public struct HRVUploadData: Codable {
    public let date: String            // 日期，格式：yyyy-MM-dd
    public let records: [HRVRecord]    // HRV记录数组
    
    public init(date: String, records: [HRVRecord]) {
        self.date = date
        self.records = records
    }
}

/// HRV记录模型
public struct HRVRecord: Codable {
    public let hrv: Int       // HRV值
    public let time: String   // 时间，ISO8601格式
    
    public init(hrv: Int, time: String) {
        self.hrv = hrv
        self.time = time
    }
} 
