import Foundation

/// 活动明细响应
public struct ActivityDetailResponse: Codable {
    public let code: Int
    public let data: [ActivityDetailData]?
    public let msg: String
    
    public enum CodingKeys: String, CodingKey {
        case code
        case data
        case msg
    }
    
    public init(code: Int, data: [ActivityDetailData]?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
    
    /// 将响应转换为格式化的JSON字符串
    public func toJSONString() -> String? {
        let encoder = JSONEncoder()
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        
        guard let data = try? encoder.encode(self),
              let jsonString = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return jsonString
    }
}

/// 活动明细数据
public struct ActivityDetailData: Codable, Identifiable {
    public var id: String { "\(time)" }
    
    public let steps: Int       // 步数
    public let time: Int64      // 时间戳（毫秒）
    
    public enum CodingKeys: String, CodingKey {
        case steps
        case time
    }
    
    public init(steps: Int, time: Int64) {
        self.steps = steps
        self.time = time
    }
    
    /// 解析毫秒时间戳，转换为Date对象
    public var date: Date? {
        return Date(timeIntervalSince1970: Double(time) / 1000.0)
    }
    
    /// 获取格式化的时间字符串（HH:mm格式）
    public func formattedTime() -> String {
        guard let date = self.date else {
            return "无效时间"
        }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    /// 计算该时间点对应的半小时索引(0-47)
    public var halfHourIndex: Int? {
        guard let date = self.date else {
            return nil
        }
        
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        return hour * 2 + (minute >= 30 ? 1 : 0)
    }
} 