import Foundation

/// 活动卡路里响应
public struct ActivityCalorieResponse: Codable {
    public let code: Int
    public let data: [ActivityCalorieData]?
    public let msg: String
    
    public enum CodingKeys: String, CodingKey {
        case code
        case data
        case msg
    }
    
    public init(code: Int, data: [ActivityCalorieData]?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
    
    /// 将响应转换为格式化的JSON字符串
    public func toJSONString() -> String? {
        let encoder = JSONEncoder()
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        
        guard let data = try? encoder.encode(self),
              let jsonString = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return jsonString
    }
}

/// 活动卡路里数据
public struct ActivityCalorieData: Codable, Identifiable {
    public var id: String { "\(time)" } // 使用时间戳作为唯一标识符
    
    public let calories: Double     // 卡路里值（浮点数）
    public let time: Int64      // 时间戳（毫秒）
    
    public enum CodingKeys: String, CodingKey {
        case calories
        case time
    }
    
    public init(calories: Double, time: Int64) {
        self.calories = calories
        self.time = time
    }
    
    /// 解析时间，转换为Date对象
    public var date: Date? {
        // API返回的是毫秒时间戳，直接转换
        return Date(timeIntervalSince1970: Double(time) / 1000.0)
    }
    
    /// 获取格式化的时间字符串（HH:mm格式）
    public func formattedTime() -> String {
        guard let date = self.date else {
            return "无效时间"
        }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    /// 计算该时间点对应的半小时索引(0-47)
    public var halfHourIndex: Int? {
        guard let date = self.date else {
            return nil
        }
        
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        return hour * 2 + (minute >= 30 ? 1 : 0)
    }
} 