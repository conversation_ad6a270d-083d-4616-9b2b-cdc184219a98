import Foundation

/// 活动状态响应
public struct ActivityStateResponse: Codable {
    public let code: Int
    public let data: [ActivityStateData]?
    public let msg: String
    
    public enum CodingKeys: String, CodingKey {
        case code
        case data
        case msg
    }
    
    public init(code: Int, data: [ActivityStateData]?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
    
    /// 将响应转换为格式化的JSON字符串
    public func toJSONString() -> String? {
        let encoder = JSONEncoder()
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        
        guard let data = try? encoder.encode(self),
              let jsonString = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return jsonString
    }
}

/// 平均活动状态响应模型
public struct ActivityAvgStateData: Codable {
    let state: Int
    let title: String
    let time: String
}

/// 活动状态数据
public struct ActivityStateData: Codable, Identifiable {
    public var id: String { String(time) } // 使用时间戳作为唯一标识符
    
    public let state: Int        // 活动状态值
    public let title: String     // 活动级别(Inactive/Low/Moderate/Vigorous)
    public let time: Int64       // 时间戳（毫秒）
    
    public enum CodingKeys: String, CodingKey {
        case state
        case title
        case time
    }
    
    public init(state: Int, title: String, time: Int64) {
        self.state = state
        self.title = title
        self.time = time
    }
    
    /// 获取活动级别
    public var level: String {
        return title
    }
    
    /// 获取Date对象
    public var date: Date {
        return Date(timeIntervalSince1970: Double(time) / 1000.0)
    }
    
    /// 转换毫秒时间戳为半小时索引(0-47，每半小时一个索引)
    public var halfHourIndex: Int {
        // 将毫秒时间戳转换为Date对象
        let date = self.date
        return calculateHalfHourIndex(from: date)
    }
    
    /// 计算半小时索引
    private func calculateHalfHourIndex(from date: Date) -> Int {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        return hour * 2 + (minute >= 30 ? 1 : 0)
    }
    
    /// 格式化时间
    public func formattedTime() -> String {
        let date = self.date
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm"
        return timeFormatter.string(from: date)
    }
    
    /// 获取ISO8601格式的时间字符串（用于调试）
    public func isoTimeString() -> String {
        let date = self.date
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime]
        return formatter.string(from: date)
    }
} 
