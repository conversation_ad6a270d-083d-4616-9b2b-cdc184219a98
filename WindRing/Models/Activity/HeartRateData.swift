import Foundation

public struct HeartRateModel: Codable,UploadedMarkable {
    var uploaded: Bool = false
    
    public let heartRate: [HeartRateData]
    
}



/// 心率数据模型，用于与服务器交互
public struct HeartRateData: Codable, Identifiable {
    public var id: String { "\(time)" } // 使用时间戳作为唯一标识符
    
    public let hearts: Int      // 心率值
    public let time: Int64      // 时间戳（毫秒）
    
    public enum CodingKeys: String, CodingKey {
        case hearts
        case time
    }
    
    public init(hearts: Int, time: Int64) {
        self.hearts = hearts
        self.time = time
    }
    
    /// 解析时间，转换为Date对象
    public var date: Date? {
        // API返回的是毫秒时间戳，直接转换
        return Date(timeIntervalSince1970: Double(time) / 1000.0)
    }
    
    /// 获取格式化的时间字符串（HH:mm格式）
    public func formattedTime() -> String {
        guard let date = self.date else {
            return "无效时间"
        }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    /// 计算该时间点对应的半小时索引(0-47)
    public var halfHourIndex: Int? {
        guard let date = self.date else {
            return nil
        }
        
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        return hour * 2 + (minute >= 30 ? 1 : 0)
    }
} 
