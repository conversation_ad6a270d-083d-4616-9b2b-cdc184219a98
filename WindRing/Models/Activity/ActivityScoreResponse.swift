import Foundation

/// 活动评分响应
public struct ActivityScoreResponse: Codable {
    public let code: Int
    public let data: ActivityScoreData?
    public let msg: String
    
    public enum CodingKeys: String, CodingKey {
        case code
        case data
        case msg
    }
    
    public init(code: Int, data: ActivityScoreData?, msg: String) {
        self.code = code
        self.data = data
        self.msg = msg
    }
    
    /// 将响应转换为格式化的JSON字符串
    public func toJSONString() -> String? {
        let encoder = JSONEncoder()
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        
        guard let data = try? encoder.encode(self),
              let jsonString = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return jsonString
    }
}

/// 活动评分数据
public struct ActivityScoreData: Codable {
    public var score: Int       // 评分值
    public var steps: Int       // 步数
    public var calories: Int    // 卡路里消耗 (单位: 千卡)
    public var time: Int        // 活动时间 (单位: 分)
    public var distance: Double    // 距离 (单位: 米)
    
    public enum CodingKeys: String, CodingKey {
        case score
        case steps
        case calories
        case time
        case distance
    }
    
    public init(score: Int, steps: Int, calories: Int, time: Int, distance: Double = 0) {
        self.score = score
        self.steps = steps
        self.calories = calories
        self.time = time
        self.distance = distance
    }
    
    /// 将活动数据转换为格式化的JSON字符串
    public func toJSONString() -> String? {
        let encoder = JSONEncoder()
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        
        guard let data = try? encoder.encode(self),
              let jsonString = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return jsonString
    }
    
    /// 获取格式化后的人类可读活动数据摘要
    public func getSummary() -> String {
        return """
        活动数据摘要:
        - 得分: \(score)分
        - 步数: \(steps)步
        - 卡路里: \(calories)千卡
        - 活动时间: \(formatTime(seconds: time))
        - 距离: \(formatDistance(meters: distance))
        """
    }
    
    /// 格式化时间（从秒转为小时和分钟）
    private func formatTime(seconds: Int) -> String {
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        
        if hours > 0 {
            return "\(hours)小时\(minutes > 0 ? " \(minutes)分钟" : "")"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    /// 格式化距离（从米转为千米）
    private func formatDistance(meters: Double) -> String {
        let km = Double(meters) / 1000.0
        return String(format: "%.2f公里", km)
    }
} 
