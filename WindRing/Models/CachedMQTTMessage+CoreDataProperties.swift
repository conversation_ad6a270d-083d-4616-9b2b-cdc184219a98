import Foundation
import CoreData

extension CachedMQTTMessage {
    @nonobjc public class func fetchRequest() -> NSFetchRequest<CachedMQTTMessage> {
        return NSFetchRequest<CachedMQTTMessage>(entityName: "CachedMQTTMessage")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var topic: String?
    @NSManaged public var message: Data?
    @NSManaged public var timestamp: Date?
    @NSManaged public var isSynced: Bool
    
    /// 将消息转换为字典
    func toDictionary() -> [String: Any]? {
        guard let messageData = self.message else { return nil }
        
        do {
            return try JSONSerialization.jsonObject(with: messageData) as? [String: Any]
        } catch {
            print("消息解析失败: \(error)")
            return nil
        }
    }
} 