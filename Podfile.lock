PODS:
  - CocoaMQTT (2.1.6):
    - CocoaMQTT/Core (= 2.1.6)
  - CocoaMQTT/Core (2.1.6):
    - MqttCocoaAsyncSocket (~> 1.0.8)
  - FLEX (5.22.10)
  - IQKeyboardCore (1.0.5)
  - IQKeyboardManagerSwift (8.0.1):
    - IQKeyboardManagerSwift/Appearance (= 8.0.1)
    - IQKeyboardManagerSwift/Core (= 8.0.1)
    - IQKeyboardManagerSwift/IQKeyboardReturnManager (= 8.0.1)
    - IQKeyboardManagerSwift/IQKeyboardToolbarManager (= 8.0.1)
    - IQKeyboardManagerSwift/IQTextView (= 8.0.1)
    - IQKeyboardManagerSwift/Resign (= 8.0.1)
  - IQKeyboardManagerSwift/Appearance (8.0.1):
    - IQKeyboardManagerSwift/Core
  - IQKeyboardManagerSwift/Core (8.0.1):
    - IQKeyboardNotification
    - IQTextInputViewNotification
  - IQKeyboardManagerSwift/IQKeyboardReturnManager (8.0.1):
    - IQKeyboardReturnManager
  - IQKeyboardManagerSwift/IQKeyboardToolbarManager (8.0.1):
    - IQKeyboardManagerSwift/Core
    - IQKeyboardToolbarManager
  - IQKeyboardManagerSwift/IQTextView (8.0.1):
    - IQTextView
  - IQKeyboardManagerSwift/Resign (8.0.1):
    - IQKeyboardManagerSwift/Core
  - IQKeyboardNotification (1.0.3)
  - IQKeyboardReturnManager (1.0.4):
    - IQKeyboardCore (= 1.0.5)
  - IQKeyboardToolbar (1.1.1):
    - IQKeyboardCore
    - IQKeyboardToolbar/Core (= 1.1.1)
  - IQKeyboardToolbar/Core (1.1.1):
    - IQKeyboardCore
    - IQKeyboardToolbar/Placeholderable
  - IQKeyboardToolbar/Placeholderable (1.1.1):
    - IQKeyboardCore
  - IQKeyboardToolbarManager (1.1.3):
    - IQKeyboardToolbar
    - IQTextInputViewNotification
  - IQTextInputViewNotification (1.0.8):
    - IQKeyboardCore
  - IQTextView (1.0.5):
    - IQKeyboardToolbar/Placeholderable
  - MqttCocoaAsyncSocket (1.0.8)
  - Sentry (8.52.1):
    - Sentry/Core (= 8.52.1)
  - Sentry/Core (8.52.1)

DEPENDENCIES:
  - CocoaMQTT (~> 2.1.0)
  - FLEX
  - IQKeyboardManagerSwift
  - Sentry (from `https://github.com/getsentry/sentry-cocoa.git`, tag `8.52.1`)

SPEC REPOS:
  trunk:
    - CocoaMQTT
    - FLEX
    - IQKeyboardCore
    - IQKeyboardManagerSwift
    - IQKeyboardNotification
    - IQKeyboardReturnManager
    - IQKeyboardToolbar
    - IQKeyboardToolbarManager
    - IQTextInputViewNotification
    - IQTextView
    - MqttCocoaAsyncSocket

EXTERNAL SOURCES:
  Sentry:
    :git: https://github.com/getsentry/sentry-cocoa.git
    :tag: 8.52.1

CHECKOUT OPTIONS:
  Sentry:
    :git: https://github.com/getsentry/sentry-cocoa.git
    :tag: 8.52.1

SPEC CHECKSUMS:
  CocoaMQTT: 1f206228b29318eabdacad0c2e4e88575922c27a
  FLEX: f21ee4f498eed3f8a1eded66b21939fd3b7a22ce
  IQKeyboardCore: 28c8bf3bcd8ba5aa1570b318cbc4da94b861711e
  IQKeyboardManagerSwift: 835fc9c6e4732398113406d84900ad2e8f141218
  IQKeyboardNotification: d7382c4466c5a5adef92c7452ebf861b36050088
  IQKeyboardReturnManager: 972be48528ce9e7508ab3ab15ac7efac803f17f5
  IQKeyboardToolbar: d4bdccfb78324aec2f3920659c77bb89acd33312
  IQKeyboardToolbarManager: 6c693c8478d6327a7ef2107528d29698b3514dbb
  IQTextInputViewNotification: f5e954d8881fd9808b744e49e024cc0d4bcfe572
  IQTextView: ae13b4922f22e6f027f62c557d9f4f236b19d5c7
  MqttCocoaAsyncSocket: 77d3b74f76228dd5a05d1f9526eab101d415b30c
  Sentry: 2cbbe3592f30050c60e916c63c7f5a2fa584005e

PODFILE CHECKSUM: 2066506840ec7610f468240692877eea0347003b

COCOAPODS: 1.16.2
