# WindRing技术文档交接

## 项目架构

WindRing采用模块化架构设计，遵循MVVM (Model-View-ViewModel) 设计模式，主要分为以下几个层次：

### 架构层次

1. **视图层 (View)**
   - 使用SwiftUI构建用户界面
   - 复杂界面结合UIKit实现
   - 视图仅负责展示和用户交互

2. **视图模型层 (ViewModel)**
   - 处理视图的业务逻辑
   - 管理视图状态
   - 通过服务层获取数据
   - 使用Combine框架实现响应式编程

3. **服务层 (Service)**
   - 提供业务功能接口
   - 封装网络请求和设备通信
   - 管理数据缓存和持久化
   - 处理后台任务

4. **数据层 (Data)**
   - 定义数据模型
   - 实现数据存储和检索
   - 管理数据同步逻辑

5. **设备层 (Device)**
   - 管理智能戒指连接
   - 处理设备数据通信
   - 实现固件更新功能

## 目录结构说明

```
WindRing/
├── Core/                    # 核心功能和服务
│   ├── Models/              # 核心数据模型
│   ├── Services/            # 核心服务
│   ├── Notifications/       # 通知管理
│   ├── Network/             # 网络层
│   ├── Device/              # 设备管理
│   ├── Storage/             # 数据存储
│   ├── Data/                # 数据处理
│   ├── API/                 # API接口
│   └── Utilities/           # 工具类
│
├── Features/                # 功能模块
│   ├── Health/              # 健康功能
│   │   ├── Models/          # 健康数据模型
│   │   ├── ViewModels/      # 健康视图模型
│   │   ├── Services/        # 健康相关服务
│   │   └── Reports/         # 健康报告
│   ├── Profile/             # 用户资料
│   ├── Settings/            # 应用设置
│   ├── Auth/                # 用户认证
│   └── Device/              # 设备管理
│
├── UI/                      # UI组件
├── Components/              # 可复用组件
├── Extensions/              # 扩展
└── Resources/               # 资源文件
```

## 关键模块说明

### 1. 健康数据模块

健康数据模块是应用的核心，负责处理和展示所有健康相关数据。

#### 主要文件
- `Features/Health/HealthTabView.swift`：健康模块主Tab视图
- `Features/Health/SleepView.swift`：睡眠数据展示
- `Features/Health/ActivityHistoryView.swift`：活动数据历史
- `Features/Health/VitalSignsHistoryView.swift`：生命体征历史

#### 数据模型
- `Features/Health/Models/SleepModel.swift`：睡眠数据模型
- `Features/Health/Models/ActivityModel.swift`：活动数据模型
- `Features/Health/Models/VitalSignsModel.swift`：生命体征模型

#### 视图模型
- `Features/Health/ViewModels/SleepViewModel.swift`：处理睡眠数据逻辑
- `Features/Health/ViewModels/ActivityViewModel.swift`：处理活动数据逻辑
- `Features/Health/ViewModels/HRVViewModel.swift`：处理HRV数据逻辑

### 2. 设备连接模块

负责智能戒指的连接、配对、数据同步和固件更新。

#### 主要文件
- `Core/Device/RingDeviceManager.swift`：戒指设备管理器
- `Core/Device/RingConnectionService.swift`：戒指连接服务
- `Core/Device/FirmwareUpdateService.swift`：固件更新服务
- `Features/Device/DeviceSetupView.swift`：设备设置界面

#### 通信协议
- 使用MQTT协议通过CocoaMQTT库实现设备通信
- 数据通过protobuf进行序列化和反序列化

### 3. 数据同步与存储

管理应用数据的本地存储、云端同步和离线功能。

#### 主要文件
- `Core/Data/DataSyncManager.swift`：数据同步管理器
- `Core/Storage/HealthDataStore.swift`：健康数据存储
- `RawDataUploadService.swift`：原始数据上传服务

#### 存储机制
- 使用Core Data进行复杂结构化数据存储
- 使用GRDB进行高性能SQL存储
- 使用UserDefaults存储简单键值对

#### 同步策略
- 增量同步：只同步变化的数据
- 冲突解决：基于时间戳和优先级规则
- 离线处理：无网络时缓存操作，联网后自动同步

### 4. 用户认证与账户管理

处理用户认证、注册、个人资料管理和账户设置。

#### 主要文件
- `Features/Auth/AuthManager.swift`：认证管理器
- `Features/Auth/LoginView.swift`：登录界面
- `Features/Profile/UserProfileView.swift`：用户资料界面

#### 认证方式
- 手机号验证码登录
- 邮箱密码登录
- 第三方认证（微信、Apple ID）

### 5. 数据分析与健康洞察

基于用户健康数据提供分析和个性化建议。

#### 主要文件
- `Features/Health/InsightView.swift`：健康洞察视图
- `Core/Services/HealthAnalyticsService.swift`：健康分析服务
- `Features/Health/Reports/HealthReportGenerator.swift`：健康报告生成器

#### AI分析功能
- 使用CoreML进行本地数据分析
- 复杂分析通过云端API进行

## 数据流

### 健康数据流
1. 设备采集原始数据
2. 通过MQTT协议传输至手机
3. `RingDeviceManager`接收并解析数据
4. `DataSyncManager`处理数据并存储至本地数据库
5. 相关ViewModel订阅数据变化
6. 视图更新展示最新数据
7. `DataSyncManager`在合适时机将数据同步至云端

### 用户操作流
1. 用户在UI上进行操作
2. 视图将操作传递给ViewModel
3. ViewModel调用相应Service处理业务逻辑
4. Service完成操作并更新数据
5. 数据变化通过Combine发布者通知ViewModel
6. ViewModel更新视图状态
7. 视图响应状态变化并更新UI

## API接口

### 后端API
- 基本URL：`https://api.windring.com/v1`
- 认证方式：Bearer Token
- 请求格式：JSON
- 响应格式：JSON

### 主要API端点
- `/auth/*`：认证相关API
- `/user/*`：用户信息API
- `/device/*`：设备管理API
- `/health/*`：健康数据API
- `/sync/*`：数据同步API

## 关键技术点

### 1. MQTT通信
- 使用CocoaMQTT库实现设备通信
- 采用QoS 1确保消息传递
- 实现自动重连和会话恢复

### 2. 数据存储策略
- 短期数据：内存缓存
- 中期数据：SQLite（GRDB）
- 长期数据：Core Data
- 二进制数据：文件系统

### 3. 性能优化
- 使用后台队列处理数据解析
- 采用分页加载展示大量历史数据
- 实现数据预加载提升用户体验

### 4. 隐私保护
- 敏感数据本地加密存储
- 网络传输采用TLS加密
- 实现最小数据收集原则

## 待完成工作

1. **睡眠数据分析优化**
   - 文件：`Features/Health/ViewModels/SleepViewModel.swift`
   - 状态：开发中，完成度80%
   - 优先级：高

2. **MQTT连接稳定性改进**
   - 文件：`Core/Device/RingConnectionService.swift`
   - 状态：问题排查中
   - 优先级：高

3. **离线数据同步机制增强**
   - 文件：`Core/Data/DataSyncManager.swift`
   - 状态：开发中，完成度60%
   - 优先级：中

4. **用户界面本地化**
   - 文件：多个视图文件
   - 状态：计划中
   - 优先级：中

5. **健康报告生成功能**
   - 文件：`Features/Health/Reports/`目录
   - 状态：初步实现，需要完善
   - 优先级：低

## 已知问题

1. **设备断连问题**
   - 描述：某些型号的手机在后台运行时容易断开设备连接
   - 文件：`Core/Device/RingConnectionService.swift`
   - 临时解决方案：增加重连逻辑和提醒用户保持应用活跃

2. **数据同步偶发冲突**
   - 描述：多设备同时编辑数据时可能出现冲突
   - 文件：`Core/Data/DataSyncManager.swift`
   - 计划解决方案：实现基于版本的冲突解决策略

3. **内存占用较大**
   - 描述：长时间运行后内存占用增加
   - 文件：多个服务类
   - 计划解决方案：优化缓存策略，减少内存泄漏

## 调试与测试

### 测试账号
- 开发测试账号：<EMAIL> / Test@123
- 管理员账号：<EMAIL> / Admin@456

### 调试工具
- `Features/Developer/`目录包含多个开发者调试工具
- `Features/Health/APIViews/`目录包含API测试视图

### 测试数据生成
- 使用`DataSyncTestView.swift`可生成测试数据
- 使用`SleepDataTestView.swift`可模拟睡眠数据 