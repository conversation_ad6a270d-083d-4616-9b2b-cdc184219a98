<entity name="CachedMQTTMessage" representedClassName="CachedMQTTMessage" syncable="YES">
    <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
    <attribute name="isSynced" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
    <attribute name="message" attributeType="Binary"/>
    <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
    <attribute name="topic" attributeType="String"/>
    <uniquenessConstraints>
        <uniquenessConstraint>
            <constraint value="id"/>
        </uniquenessConstraint>
    </uniquenessConstraints>
</entity> 