import SwiftUI

struct GoalSettingView: View {
    @Environment(\.presentationMode) var presentationMode
    
    // 目标设置状态变量
    @State private var sleepHours: Int = 8
    @State private var sleepMinutes: Int = 0
    @State private var stepGoal: Int = 2345
    @State private var calorieGoal: Int = 355
    @State private var activityHours: Int = 8
    @State private var activityMinutes: Int = 0
    
    // 控制底部表单显示
    @State private var showSleepGoalSheet = false
    @State private var showStepGoalSheet = false
    @State private var showCalorieGoalSheet = false
    @State private var showDurationGoalSheet = false
    
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色 - 使用深色背景
            Color.black.edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 0) {
                // 导航栏
                HStack {
                    Button(action: {
                        // 返回操作
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                            
                            Text("goal_setting_title".localized)
                                .font(.system(size: 24, weight: .bold))
                                .foregroundColor(.white)
                        }
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.top, 5)
                .padding(.bottom, 10)
                
                // 网格布局
                VStack(spacing: 16) {
                    HStack(spacing: 16) {
                        // 睡眠目标卡片
                        goalCard(
                            icon: "bed.double.fill",
                            iconColor: .indigo,
                            title: "goal_setting_sleep_goal".localized,
                            value: "\(sleepHours) \("hr".localized) \(sleepMinutes) \("min".localized)",
                            action: { showSleepGoalSheet = true }
                        )
                        
                        // 步数目标卡片
                        goalCard(
                            icon: "figure.walk",
                            iconColor: .white,
                            title: "goal_setting_steps_goal".localized,
                            value: "\(stepGoal)",
                            action: { showStepGoalSheet = true }
                        )
                    }
                    
                    HStack(spacing: 16) {
                        // 活动卡路里目标卡片
                        goalCard(
                            icon: "flame.fill",
                            iconColor: .orange,
                            title: "goal_setting_calories_goal".localized,
                            value: "\(calorieGoal)\("insight_unit_kcal".localized)",
                            action: { showCalorieGoalSheet = true }
                        )
                        
                        // 活动时长目标卡片
                        goalCard(
                            icon: "clock.fill",
                            iconColor: .gray,
                            title: "goal_setting_duration_goal".localized,
                            value: "\(activityHours) \("hr".localized) \(activityMinutes) \("min".localized)",
                            action: { showDurationGoalSheet = true }
                        )
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 16)
                
                Spacer()
            }
        }
        .navigationBarHidden(true)
    }
    
    // 目标卡片组件 - 修改为深色风格
    private func goalCard(
        icon: String,
        iconColor: Color,
        title: String,
        value: String,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                HStack(spacing: 8) {
                    // 图标
                    ZStack {
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 36, height: 36)
                        
                        Image(systemName: icon)
                            .font(.system(size: 16))
                            .foregroundColor(iconColor)
                    }
                    
                    Text(title)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                }
                
                HStack {
                    Text(value)
                        .font(.system(size: 18))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                        .font(.system(size: 14))
                }
            }
            .padding(16)
            .frame(maxWidth: .infinity)
            .background(Color(red: 0.15, green: 0.15, blue: 0.2))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 预览
struct GoalSettingView_Previews: PreviewProvider {
    static var previews: some View {
        GoalSettingView()
            .preferredColorScheme(.dark)
} 
} 