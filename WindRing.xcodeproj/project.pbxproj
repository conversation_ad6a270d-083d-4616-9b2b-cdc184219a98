// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		14AB0B4E7EB3BDD7E5D8EAA6 /* Pods_WindRingTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 28EA1EDFA66F1A71AF5F1A2F /* Pods_WindRingTests.framework */; };
		9BBD22DBD32B5B7C26B73A3C /* Pods_WindRing.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0E92A2DE6D2884051F4AF946 /* Pods_WindRing.framework */; };
		EF7FE8D1510CFC1C1276D1D9 /* Pods_WindRing_WindRingUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D80CB2336736FAC26823B6F0 /* Pods_WindRing_WindRingUITests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7C83C0A82D76346400302395 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7C83C0882D76346200302395 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7C83C08F2D76346200302395;
			remoteInfo = WindRing;
		};
		7C83C0B22D76346400302395 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7C83C0882D76346200302395 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7C83C08F2D76346200302395;
			remoteInfo = WindRing;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		7CC5713E2D7A2805000B9E2C /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		06D0519DC60ED7B697D2F2F3 /* Pods-WindRing.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-WindRing.release.xcconfig"; path = "Target Support Files/Pods-WindRing/Pods-WindRing.release.xcconfig"; sourceTree = "<group>"; };
		0E92A2DE6D2884051F4AF946 /* Pods_WindRing.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_WindRing.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1401D6E69C50A6240C5986C9 /* Pods-WindRing-WindRingUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-WindRing-WindRingUITests.release.xcconfig"; path = "Target Support Files/Pods-WindRing-WindRingUITests/Pods-WindRing-WindRingUITests.release.xcconfig"; sourceTree = "<group>"; };
		28EA1EDFA66F1A71AF5F1A2F /* Pods_WindRingTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_WindRingTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5B636C7D6CA6D6A02A81293A /* Pods-WindRing.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-WindRing.debug.xcconfig"; path = "Target Support Files/Pods-WindRing/Pods-WindRing.debug.xcconfig"; sourceTree = "<group>"; };
		7C83C0902D76346200302395 /* WindRing.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = WindRing.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7C83C0A72D76346400302395 /* WindRingTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = WindRingTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7C83C0B12D76346400302395 /* WindRingUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = WindRingUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		9FDC58CF73BEB908BAE022A4 /* Pods-WindRingTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-WindRingTests.release.xcconfig"; path = "Target Support Files/Pods-WindRingTests/Pods-WindRingTests.release.xcconfig"; sourceTree = "<group>"; };
		A9CB3D058A3128D466C14D4A /* Pods-WindRing-WindRingUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-WindRing-WindRingUITests.debug.xcconfig"; path = "Target Support Files/Pods-WindRing-WindRingUITests/Pods-WindRing-WindRingUITests.debug.xcconfig"; sourceTree = "<group>"; };
		D80CB2336736FAC26823B6F0 /* Pods_WindRing_WindRingUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_WindRing_WindRingUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E7B58F171B7FDC3C58BFD0B0 /* Pods-WindRingTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-WindRingTests.debug.xcconfig"; path = "Target Support Files/Pods-WindRingTests/Pods-WindRingTests.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		7C7313482D840CFD0015AAF7 /* Exceptions for "WindRing" folder in "WindRing" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 7C83C08F2D76346200302395 /* WindRing */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */
		7C7314D02D8413440015AAF7 /* Exceptions for "WindRing" folder in "Embed Frameworks" phase from "WindRing" target */ = {
			isa = PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet;
			attributesByRelativePath = {
				Frameworks/CRPSmartRing.framework = (CodeSignOnCopy, RemoveHeadersOnCopy, );
			};
			buildPhase = 7CC5713E2D7A2805000B9E2C /* Embed Frameworks */;
			membershipExceptions = (
				Frameworks/CRPSmartRing.framework,
			);
		};
/* End PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7C83C0922D76346200302395 /* WindRing */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				7C7313482D840CFD0015AAF7 /* Exceptions for "WindRing" folder in "WindRing" target */,
				7C7314D02D8413440015AAF7 /* Exceptions for "WindRing" folder in "Embed Frameworks" phase from "WindRing" target */,
			);
			path = WindRing;
			sourceTree = "<group>";
		};
		7C83C0AA2D76346400302395 /* WindRingTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = WindRingTests;
			sourceTree = "<group>";
		};
		7C83C0B42D76346400302395 /* WindRingUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = WindRingUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7C83C08D2D76346200302395 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9BBD22DBD32B5B7C26B73A3C /* Pods_WindRing.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C83C0A42D76346400302395 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				14AB0B4E7EB3BDD7E5D8EAA6 /* Pods_WindRingTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C83C0AE2D76346400302395 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EF7FE8D1510CFC1C1276D1D9 /* Pods_WindRing_WindRingUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		334494FB25A6EB416D8BBEE0 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0E92A2DE6D2884051F4AF946 /* Pods_WindRing.framework */,
				D80CB2336736FAC26823B6F0 /* Pods_WindRing_WindRingUITests.framework */,
				28EA1EDFA66F1A71AF5F1A2F /* Pods_WindRingTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7C83C0872D76346200302395 = {
			isa = PBXGroup;
			children = (
				7C83C0922D76346200302395 /* WindRing */,
				7C83C0AA2D76346400302395 /* WindRingTests */,
				7C83C0B42D76346400302395 /* WindRingUITests */,
				7C83C0912D76346200302395 /* Products */,
				A04015FA4E0CDEC1B51E46F9 /* Pods */,
				334494FB25A6EB416D8BBEE0 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		7C83C0912D76346200302395 /* Products */ = {
			isa = PBXGroup;
			children = (
				7C83C0902D76346200302395 /* WindRing.app */,
				7C83C0A72D76346400302395 /* WindRingTests.xctest */,
				7C83C0B12D76346400302395 /* WindRingUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A04015FA4E0CDEC1B51E46F9 /* Pods */ = {
			isa = PBXGroup;
			children = (
				5B636C7D6CA6D6A02A81293A /* Pods-WindRing.debug.xcconfig */,
				06D0519DC60ED7B697D2F2F3 /* Pods-WindRing.release.xcconfig */,
				A9CB3D058A3128D466C14D4A /* Pods-WindRing-WindRingUITests.debug.xcconfig */,
				1401D6E69C50A6240C5986C9 /* Pods-WindRing-WindRingUITests.release.xcconfig */,
				E7B58F171B7FDC3C58BFD0B0 /* Pods-WindRingTests.debug.xcconfig */,
				9FDC58CF73BEB908BAE022A4 /* Pods-WindRingTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7C83C08F2D76346200302395 /* WindRing */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7C83C0BA2D76346400302395 /* Build configuration list for PBXNativeTarget "WindRing" */;
			buildPhases = (
				C410A9D3BCF9DD1B94B646E0 /* [CP] Check Pods Manifest.lock */,
				7C83C08C2D76346200302395 /* Sources */,
				7C83C08D2D76346200302395 /* Frameworks */,
				7C83C08E2D76346200302395 /* Resources */,
				7CC5713E2D7A2805000B9E2C /* Embed Frameworks */,
				898810E46763B8DB9B272FEB /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7C83C0922D76346200302395 /* WindRing */,
			);
			name = WindRing;
			productName = WindRing;
			productReference = 7C83C0902D76346200302395 /* WindRing.app */;
			productType = "com.apple.product-type.application";
		};
		7C83C0A62D76346400302395 /* WindRingTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7C83C0BF2D76346400302395 /* Build configuration list for PBXNativeTarget "WindRingTests" */;
			buildPhases = (
				420009DAC3787A164F3F6AB3 /* [CP] Check Pods Manifest.lock */,
				7C83C0A32D76346400302395 /* Sources */,
				7C83C0A42D76346400302395 /* Frameworks */,
				7C83C0A52D76346400302395 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7C83C0A92D76346400302395 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7C83C0AA2D76346400302395 /* WindRingTests */,
			);
			name = WindRingTests;
			productName = WindRingTests;
			productReference = 7C83C0A72D76346400302395 /* WindRingTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7C83C0B02D76346400302395 /* WindRingUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7C83C0C22D76346400302395 /* Build configuration list for PBXNativeTarget "WindRingUITests" */;
			buildPhases = (
				C0D8FEB92CAC4E12E74192B8 /* [CP] Check Pods Manifest.lock */,
				7C83C0AD2D76346400302395 /* Sources */,
				7C83C0AE2D76346400302395 /* Frameworks */,
				7C83C0AF2D76346400302395 /* Resources */,
				131EB34DC7749CBAAC258802 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7C83C0B32D76346400302395 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7C83C0B42D76346400302395 /* WindRingUITests */,
			);
			name = WindRingUITests;
			productName = WindRingUITests;
			productReference = 7C83C0B12D76346400302395 /* WindRingUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7C83C0882D76346200302395 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					7C83C08F2D76346200302395 = {
						CreatedOnToolsVersion = 16.2;
					};
					7C83C0A62D76346400302395 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7C83C08F2D76346200302395;
					};
					7C83C0B02D76346400302395 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7C83C08F2D76346200302395;
					};
				};
			};
			buildConfigurationList = 7C83C08B2D76346200302395 /* Build configuration list for PBXProject "WindRing" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 7C83C0872D76346200302395;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 7C83C0912D76346200302395 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7C83C08F2D76346200302395 /* WindRing */,
				7C83C0A62D76346400302395 /* WindRingTests */,
				7C83C0B02D76346400302395 /* WindRingUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7C83C08E2D76346200302395 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C83C0A52D76346400302395 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C83C0AF2D76346400302395 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		131EB34DC7749CBAAC258802 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-WindRing-WindRingUITests/Pods-WindRing-WindRingUITests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-WindRing-WindRingUITests/Pods-WindRing-WindRingUITests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-WindRing-WindRingUITests/Pods-WindRing-WindRingUITests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		420009DAC3787A164F3F6AB3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-WindRingTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		898810E46763B8DB9B272FEB /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-WindRing/Pods-WindRing-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-WindRing/Pods-WindRing-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-WindRing/Pods-WindRing-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C0D8FEB92CAC4E12E74192B8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-WindRing-WindRingUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C410A9D3BCF9DD1B94B646E0 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-WindRing-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7C83C08C2D76346200302395 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C83C0A32D76346400302395 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C83C0AD2D76346400302395 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7C83C0A92D76346400302395 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7C83C08F2D76346200302395 /* WindRing */;
			targetProxy = 7C83C0A82D76346400302395 /* PBXContainerItemProxy */;
		};
		7C83C0B32D76346400302395 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7C83C08F2D76346200302395 /* WindRing */;
			targetProxy = 7C83C0B22D76346400302395 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		7C83C0BB2D76346400302395 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5B636C7D6CA6D6A02A81293A /* Pods-WindRing.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = WindRing/WindRing.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.9.2;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_ASSET_PATHS = "\"WindRing/Preview Content\"";
				DEVELOPMENT_TEAM = AWE4V7AFMV;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/WindRing/Frameworks",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = WindRing/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SiRings;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "需要蓝牙权限以连接和管理WindRing智能戒指，获取健康数据并同步设置";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "需要蓝牙权限以连接和管理WindRing智能戒指，获取健康数据并同步设置";
				INFOPLIST_KEY_NSCameraUsageDescription = "This app requires access to your camera to take profile pictures.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "当蓝牙连接不可用时，我们可能需要位置权限来扫描周围的设备";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "This app requires access to your photo library to select profile pictures.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.SiRing;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		7C83C0BC2D76346400302395 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 06D0519DC60ED7B697D2F2F3 /* Pods-WindRing.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = WindRing/WindRing.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.9.2;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_ASSET_PATHS = "\"WindRing/Preview Content\"";
				DEVELOPMENT_TEAM = AWE4V7AFMV;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/WindRing/Frameworks",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = WindRing/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SiRings;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "需要蓝牙权限以连接和管理WindRing智能戒指，获取健康数据并同步设置";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "需要蓝牙权限以连接和管理WindRing智能戒指，获取健康数据并同步设置";
				INFOPLIST_KEY_NSCameraUsageDescription = "This app requires access to your camera to take profile pictures.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "当蓝牙连接不可用时，我们可能需要位置权限来扫描周围的设备";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "This app requires access to your photo library to select profile pictures.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.SiRing;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		7C83C0BD2D76346400302395 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "DWARF with dSYM File";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7C83C0BE2D76346400302395 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		7C83C0C02D76346400302395 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E7B58F171B7FDC3C58BFD0B0 /* Pods-WindRingTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 73XM69R3R6;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.zhq.WindRing.WindRingTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/WindRing.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/WindRing";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		7C83C0C12D76346400302395 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9FDC58CF73BEB908BAE022A4 /* Pods-WindRingTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 73XM69R3R6;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.zhq.WindRing.WindRingTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/WindRing.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/WindRing";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		7C83C0C32D76346400302395 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A9CB3D058A3128D466C14D4A /* Pods-WindRing-WindRingUITests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 73XM69R3R6;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.zhq.WindRing.WindRingUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = WindRing;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		7C83C0C42D76346400302395 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1401D6E69C50A6240C5986C9 /* Pods-WindRing-WindRingUITests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 73XM69R3R6;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.zhq.WindRing.WindRingUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = WindRing;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7C83C08B2D76346200302395 /* Build configuration list for PBXProject "WindRing" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C83C0BD2D76346400302395 /* Debug */,
				7C83C0BE2D76346400302395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7C83C0BA2D76346400302395 /* Build configuration list for PBXNativeTarget "WindRing" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C83C0BB2D76346400302395 /* Debug */,
				7C83C0BC2D76346400302395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7C83C0BF2D76346400302395 /* Build configuration list for PBXNativeTarget "WindRingTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C83C0C02D76346400302395 /* Debug */,
				7C83C0C12D76346400302395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7C83C0C22D76346400302395 /* Build configuration list for PBXNativeTarget "WindRingUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C83C0C32D76346400302395 /* Debug */,
				7C83C0C42D76346400302395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7C83C0882D76346200302395 /* Project object */;
}
